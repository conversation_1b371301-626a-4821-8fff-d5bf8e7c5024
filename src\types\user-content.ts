// Tipos para conteúdo gerado pelo usuário nas páginas de perfil

export interface UserReview {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  igdb_cover_url?: string;
  game_id?: string;
  title: string;
  slug: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  is_public: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
  pros?: string[];
  cons?: string[];
  recommendation?: 'recommended' | 'mixed' | 'not_recommended';
}

export interface UserSurvey {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  game_id?: string;
  performance_score: number;
  fps_average: number;
  fps_min?: number;
  fps_max?: number;
  resolution: string;
  graphics_settings: string;
  created_at: string;
  updated_at?: string;
  hardware_used: string;
  cpu?: string;
  gpu?: string;
  ram?: string;
  storage_type?: 'HDD' | 'SSD' | 'NVMe';
  notes?: string;
  is_public: boolean;
  is_verified: boolean;
  benchmark_type?: 'gameplay' | 'synthetic' | 'stress_test';
}

export interface UserActivity {
  id: string;
  user_id: string;
  type: 'review' | 'survey' | 'achievement' | 'milestone' | 'comment' | 'like' | 'follow';
  title: string;
  description: string;
  created_at: string;
  related_id?: string; // ID do item relacionado (review, survey, etc.)
  metadata?: Record<string, any>;
  is_public: boolean;
  points_earned?: number;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  name: string;
  description: string;
  icon?: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  category: 'reviews' | 'surveys' | 'social' | 'gaming' | 'special';
  unlocked_at: string;
  points_value: number;
  progress?: number;
  max_progress?: number;
  is_hidden: boolean;
  requirements?: string[];
}

export interface UserMedia {
  id: string;
  user_id: string;
  type: 'image' | 'video' | 'gif';
  url: string;
  thumbnail_url?: string;
  title: string;
  description?: string;
  game_name?: string;
  game_id?: string;
  created_at: string;
  updated_at?: string;
  file_size?: number;
  duration?: number; // Para vídeos, em segundos
  width?: number;
  height?: number;
  is_public: boolean;
  is_featured: boolean;
  likes_count: number;
  views_count: number;
  tags?: string[];
  upload_source?: 'direct' | 'steam' | 'console' | 'mobile';
}

export interface UserStats {
  user_id: string;
  reviews_count: number;
  surveys_count: number;
  achievements_count: number;
  media_count: number;
  total_likes_received: number;
  total_views_received: number;
  total_comments_received: number;
  total_points: number;
  level: number;
  rank: string;
  join_date: string;
  last_activity: string;
  featured_content_count: number;
  verified_content_count: number;
  monthly_stats?: {
    month: string;
    reviews: number;
    surveys: number;
    likes_received: number;
    views_received: number;
  }[];
}

export interface ContentModule {
  id: string;
  type: 'reviews' | 'surveys' | 'achievements' | 'media' | 'activity' | 'stats';
  title: string;
  description?: string;
  is_enabled: boolean;
  display_order: number;
  visibility: 'public' | 'friends' | 'private';
  settings?: {
    max_items?: number;
    show_stats?: boolean;
    allow_interactions?: boolean;
    auto_update?: boolean;
    [key: string]: any;
  };
}

export interface UserContentPreferences {
  user_id: string;
  enabled_modules: ContentModule[];
  theme_preferences?: {
    color_scheme?: 'auto' | 'light' | 'dark';
    accent_color?: string;
    animation_level?: 'none' | 'reduced' | 'full';
  };
  privacy_settings?: {
    show_real_name?: boolean;
    show_online_status?: boolean;
    allow_content_embedding?: boolean;
    require_approval_for_features?: boolean;
  };
  notification_settings?: {
    new_likes?: boolean;
    new_comments?: boolean;
    achievement_unlocks?: boolean;
    featured_content?: boolean;
  };
  created_at: string;
  updated_at: string;
  youtubeModule: {
    enabled: boolean;
    visibility: 'public' | 'friends' | 'private';
    maxVideos: number;
    showStats: boolean;
    showDescription: boolean;
    autoplay: boolean;
  };
}

// Tipos para respostas da API
export interface ContentResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
}

export interface UserContentData {
  reviews: UserReview[];
  surveys: UserSurvey[];
  activities: UserActivity[];
  achievements: UserAchievement[];
  media: UserMedia[];
  stats: UserStats;
  preferences: UserContentPreferences;
}

// Filtros para busca de conteúdo
export interface ContentFilters {
  type?: 'reviews' | 'surveys' | 'media' | 'achievements';
  game_name?: string;
  platform?: string;
  rating_min?: number;
  rating_max?: number;
  date_from?: string;
  date_to?: string;
  is_featured?: boolean;
  is_public?: boolean;
  tags?: string[];
  sort_by?: 'created_at' | 'updated_at' | 'likes_count' | 'views_count' | 'rating';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Tipos para operações CRUD
export type CreateUserReview = Omit<UserReview, 'id' | 'created_at' | 'updated_at' | 'likes_count' | 'views_count' | 'comments_count'>;
export type UpdateUserReview = Partial<Omit<UserReview, 'id' | 'user_id' | 'created_at'>>;

export type CreateUserSurvey = Omit<UserSurvey, 'id' | 'created_at' | 'updated_at' | 'is_verified'>;
export type UpdateUserSurvey = Partial<Omit<UserSurvey, 'id' | 'user_id' | 'created_at'>>;

export type CreateUserMedia = Omit<UserMedia, 'id' | 'created_at' | 'updated_at' | 'likes_count' | 'views_count'>;
export type UpdateUserMedia = Partial<Omit<UserMedia, 'id' | 'user_id' | 'created_at' | 'url'>>;

// Tipos para eventos em tempo real
export interface ContentEvent {
  type: 'new_like' | 'new_comment' | 'new_view' | 'achievement_unlock' | 'content_featured';
  user_id: string;
  content_id: string;
  content_type: 'review' | 'survey' | 'media';
  timestamp: string;
  data?: Record<string, any>;
}

export interface UserYouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  duration: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  videoId: string;
  url: string;
}

export interface UserYouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  subscriberCount: number;
  videoCount: number;
  viewCount: number;
  customUrl?: string;
  publishedAt: string;
}

export interface UserYouTubeData {
  userId: string;
  channelUrl: string;
  channelId: string;
  channel: UserYouTubeChannel;
  videos: UserYouTubeVideo[];
  lastFetched: string;
  isValid: boolean;
  error?: string;
}

export interface GetUserYouTubeDataResponse {
  success: boolean;
  data?: UserYouTubeData;
  error?: string;
  cached?: boolean;
}

export interface UpdateYouTubeChannelResponse {
  success: boolean;
  data?: UserYouTubeData;
  error?: string;
} 