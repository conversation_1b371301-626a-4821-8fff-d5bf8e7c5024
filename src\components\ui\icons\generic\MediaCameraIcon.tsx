import React from 'react';

const MediaCameraIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <rect x="3" y="6" width="18" height="12" rx="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="5" y="8" width="14" height="8" rx="1"/>
    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <circle cx="12" cy="12" r="1"/>
    <rect x="7" y="4" width="10" height="2" rx="1"/>
    <circle cx="17" cy="9" r="0.5"/>
    <rect x="2" y="10" width="1" height="4"/>
    <rect x="21" y="10" width="1" height="4"/>
    <rect x="9" y="2" width="6" height="1"/>
  </svg>
);

export default MediaCameraIcon;