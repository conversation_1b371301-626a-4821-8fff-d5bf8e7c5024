/**
 * Line Counter Service
 * Provides functionality to track and retrieve total lines written across all reviews
 */

import { createClient } from '@/lib/supabase/client';

export interface LineCountStats {
  totalLines: number;
  lastUpdated: string;
}

export interface SiteStats {
  totalUsers: number;
  totalReviews: number;
  totalSurveys: number;
  totalLines: number;
  lastUpdated: string;
}

export interface ReviewLineStats {
  reviewId: string;
  lineCount: number;
  title: string;
}

/**
 * Get the total number of lines written across all reviews in the site's history
 */
export async function getTotalReviewLines(): Promise<LineCountStats | null> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('site_statistics')
      .select('metric_value, last_updated')
      .eq('metric_name', 'total_review_lines')
      .single();

    if (error) {
      console.error('Error fetching total review lines:', error);
      return null;
    }

    return {
      totalLines: data.metric_value || 0,
      lastUpdated: data.last_updated
    };
  } catch (error) {
    console.error('Error in getTotalReviewLines:', error);
    return null;
  }
}

/**
 * Get line count statistics for individual reviews
 */
export async function getReviewLineStats(limit: number = 10): Promise<ReviewLineStats[]> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('reviews')
      .select('id, title, line_count')
      .not('line_count', 'is', null)
      .gt('line_count', 0)
      .order('line_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching review line stats:', error);
      return [];
    }

    return data.map(review => ({
      reviewId: review.id,
      lineCount: review.line_count,
      title: review.title
    }));
  } catch (error) {
    console.error('Error in getReviewLineStats:', error);
    return [];
  }
}

/**
 * Get total lines written by a specific user
 */
export async function getUserTotalLines(userId: string): Promise<number> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('reviews')
      .select('line_count')
      .eq('author_id', userId)
      .not('line_count', 'is', null);

    if (error) {
      console.error('Error fetching user total lines:', error);
      return 0;
    }

    return data.reduce((total, review) => total + (review.line_count || 0), 0);
  } catch (error) {
    console.error('Error in getUserTotalLines:', error);
    return 0;
  }
}

/**
 * Format line count number for display
 */
export function formatLineCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toLocaleString();
}

/**
 * Get comprehensive site statistics
 */
export async function getSiteStats(): Promise<SiteStats | null> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('site_statistics')
      .select('metric_name, metric_value, last_updated')
      .in('metric_name', ['total_users', 'total_reviews', 'total_surveys', 'total_review_lines']);

    if (error) {
      console.error('Error fetching site stats:', error);
      return null;
    }

    const stats = data.reduce((acc, stat) => {
      acc[stat.metric_name] = stat.metric_value;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalUsers: stats.total_users || 0,
      totalReviews: stats.total_reviews || 0,
      totalSurveys: stats.total_surveys || 0,
      totalLines: stats.total_review_lines || 0,
      lastUpdated: data[0]?.last_updated || new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in getSiteStats:', error);
    return null;
  }
}

/**
 * Get site-wide content statistics including line counts (legacy function)
 */
export async function getSiteContentStats(): Promise<{
  totalLines: number;
  totalReviews: number;
  averageLinesPerReview: number;
} | null> {
  const stats = await getSiteStats();
  if (!stats) return null;

  return {
    totalLines: stats.totalLines,
    totalReviews: stats.totalReviews,
    averageLinesPerReview: stats.totalReviews > 0 ? Math.round(stats.totalLines / stats.totalReviews) : 0
  };
}
