"use client";

import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarBody,
  SidebarLink,
  Logo,
  LogoIcon,
  AnimatedBackground,
  QuickStats,
  type ModernDashboardLayoutProps,
  type NavLink
} from "./ModernDashboardLayout";
import {
  LayoutDashboard,
  FileText,
  Gauge,
  Settings,
  User,
  Bell,
  Search,
  Plus,
  ExternalLink,
  TrendingUp,
  Activity,
  Calendar,
  Clock
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface ModernDashboardProps extends ModernDashboardLayoutProps {
  onCreateReview?: () => void;
  onViewProfile?: () => void;
  notifications?: number;
}

export function ModernDashboard({
  children,
  user,
  activeTab,
  onTabChange,
  stats,
  onCreateReview,
  onViewProfile,
  notifications = 0
}: ModernDashboardProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Navigation links
  const navLinks: NavLink[] = useMemo(() => [
    {
      label: "Overview",
      href: "#overview",
      icon: <LayoutDashboard className="h-5 w-5 flex-shrink-0" />,
      isActive: activeTab === "overview"
    },
    {
      label: "Reviews",
      href: "#reviews", 
      icon: <FileText className="h-5 w-5 flex-shrink-0" />,
      isActive: activeTab === "reviews"
    },
    {
      label: "Performance",
      href: "#performance",
      icon: <Gauge className="h-5 w-5 flex-shrink-0" />,
      isActive: activeTab === "performance"
    },
    {
      label: "Settings",
      href: "#settings",
      icon: <Settings className="h-5 w-5 flex-shrink-0" />,
      isActive: activeTab === "settings"
    }
  ], [activeTab]);

  // Tab configuration
  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "reviews", label: "Reviews" },
    { id: "performance", label: "Performance" },
    { id: "settings", label: "Settings" }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-950 text-slate-200 overflow-hidden">
      <Sidebar open={sidebarOpen} setOpen={setSidebarOpen}>
        <SidebarBody className="justify-between gap-10">
          <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
            {sidebarOpen ? <Logo /> : <LogoIcon />}
            
            {/* Quick Stats in Sidebar */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: sidebarOpen ? 1 : 0 }}
              transition={{ delay: 0.2 }}
              className="mt-6"
            >
              {sidebarOpen && <QuickStats stats={stats} />}
            </motion.div>

            {/* Navigation Links */}
            <div className="mt-4 flex flex-col gap-2">
              {navLinks.map((link, idx) => (
                <SidebarLink 
                  key={idx} 
                  link={{
                    ...link,
                    href: "#"
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    onTabChange(link.href.replace("#", ""));
                  }}
                />
              ))}
            </div>
          </div>

          {/* User Profile in Sidebar */}
          <div className="border-t border-slate-800/50 pt-4">
            <SidebarLink
              link={{
                label: user?.displayName || user?.userName || "User",
                href: "#profile",
                icon: user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt="Profile"
                    className="h-7 w-7 rounded-full object-cover border border-slate-600"
                  />
                ) : (
                  <div className="h-7 w-7 rounded-full bg-purple-600/20 border border-purple-500/30 flex items-center justify-center text-xs font-medium text-purple-300">
                    {(user?.displayName || user?.userName || "U").charAt(0).toUpperCase()}
                  </div>
                ),
              }}
              onClick={(e) => {
                e.preventDefault();
                onViewProfile?.();
              }}
            />
          </div>
        </SidebarBody>
      </Sidebar>

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header */}
        <header className="h-16 border-b border-slate-800/50 flex items-center justify-between px-6 bg-slate-950/80 backdrop-blur-sm">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-semibold text-slate-200">
              {tabs.find(tab => tab.id === activeTab)?.label || "Dashboard"}
            </h1>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative hidden sm:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-9 w-64 rounded-lg border border-slate-700/50 bg-slate-900/50 pl-9 pr-3 text-sm text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all"
              />
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={onCreateReview}
                className="bg-purple-600 hover:bg-purple-700 text-white border-0"
              >
                <Plus className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Review</span>
              </Button>

              {/* Notifications */}
              <button className="relative h-9 w-9 rounded-lg flex items-center justify-center hover:bg-slate-800/50 transition-colors">
                <Bell className="h-5 w-5 text-slate-400" />
                {notifications > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs font-medium text-white flex items-center justify-center">
                    {notifications > 9 ? "9+" : notifications}
                  </span>
                )}
              </button>
            </div>
          </div>
        </header>

        {/* Tab Navigation */}
        <div className="border-b border-slate-800/50 px-6 py-3 bg-slate-950/50">
          <div className="inline-flex rounded-lg bg-slate-900/50 p-1 backdrop-blur-sm">
            <AnimatedBackground
              defaultValue={activeTab}
              className="rounded-md bg-slate-800/80 shadow-sm"
              transition={{
                type: "spring",
                bounce: 0.2,
                duration: 0.3,
              }}
              onValueChange={(value) => value && onTabChange(value)}
            >
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  data-id={tab.id}
                  type="button"
                  className="px-4 py-2 text-sm font-medium text-slate-400 transition-colors duration-200 data-[checked=true]:text-slate-200 hover:text-slate-300"
                >
                  {tab.label}
                </button>
              ))}
            </AnimatedBackground>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="h-full"
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>
    </div>
  );
}

export default ModernDashboard;
