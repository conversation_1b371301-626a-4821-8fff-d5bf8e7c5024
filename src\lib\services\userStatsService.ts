import { createClient } from '@/lib/supabase/client';

export interface UserStats {
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalReviews: number;
  publishedReviews: number;
  draftReviews: number;
  averageScore: number;
}

/**
 * Get comprehensive user statistics including total views from all reviews
 * @param userId - The user ID to get stats for
 * @returns Promise<UserStats>
 */
export async function getUserStats(userId: string): Promise<UserStats> {
  try {
    const supabase = createClient();

    // Get all reviews for the user with their view counts
    const { data: reviews, error: reviewsError } = await supabase
      .from('reviews')
      .select(`
        id,
        view_count,
        like_count,
        comment_count,
        overall_score,
        status
      `)
      .eq('author_id', userId);

    if (reviewsError) {
      console.error('Error fetching user reviews for stats:', reviewsError);
      throw reviewsError;
    }

    // Calculate basic statistics
    const totalViews = reviews?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;
    const totalLikes = reviews?.reduce((sum, review) => sum + (review.like_count || 0), 0) || 0;
    const totalReviews = reviews?.length || 0;

    // Get total comments RECEIVED on the user's reviews
    let totalComments = 0;
    if (reviews && reviews.length > 0) {
      const reviewIds = reviews.map(review => review.id);
      const { data: comments, error: commentsError } = await supabase
        .from('comments')
        .select('id')
        .in('review_id', reviewIds);

      if (!commentsError && comments) {
        totalComments = comments.length;
      }
    }
    
    const publishedReviews = reviews?.filter(review => 
      review.status === 'published' || !review.status
    ).length || 0;
    
    const draftReviews = reviews?.filter(review => 
      review.status === 'draft'
    ).length || 0;

    const averageScore = totalReviews > 0
      ? reviews.reduce((sum, review) => sum + (review.overall_score || 0), 0) / totalReviews
      : 0;

    const stats: UserStats = {
      totalViews,
      totalLikes,
      totalComments,
      totalReviews,
      publishedReviews,
      draftReviews,
      averageScore: Math.round(averageScore * 10) / 10
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('📊 User Stats Calculated:', {
        userId,
        stats,
        reviewsCount: reviews?.length || 0
      });
    }

    return stats;

  } catch (error) {
    console.error('Error calculating user stats:', error);
    
    // Return default stats on error
    return {
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalReviews: 0,
      publishedReviews: 0,
      draftReviews: 0,
      averageScore: 0
    };
  }
}

/**
 * Get total views for a specific user (lightweight version)
 * @param userId - The user ID to get total views for
 * @returns Promise<number>
 */
export async function getUserTotalViews(userId: string): Promise<number> {
  try {
    const supabase = createClient();

    // Get sum of all view counts for user's reviews
    const { data, error } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('author_id', userId);

    if (error) {
      console.error('Error fetching user total views:', error);
      return 0;
    }

    const totalViews = data?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;

    if (process.env.NODE_ENV === 'development') {
      console.log('👁️ User Total Views:', {
        userId,
        totalViews,
        reviewsCount: data?.length || 0
      });
    }

    return totalViews;

  } catch (error) {
    console.error('Error calculating user total views:', error);
    return 0;
  }
}




