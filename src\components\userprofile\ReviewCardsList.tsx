'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gamepad2, Star, Search, ChevronLeft, ChevronRight, Calendar, Eye, MessageSquare, Heart, Clock } from 'lucide-react';
import { FastAverageColor } from 'fast-average-color';
import { useUserContent } from '@/hooks/useUserContent';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from '@/lib/utils';

interface ReviewCardsListProps {
  profileData: any;
  currentUserId: string | null;
  isOwnProfile: boolean;
  theme: any;
  searchTerm: string;
  filterPlatform: string;
  itemsPerPage?: number;
  defaultSort?: 'date' | 'rating' | 'title';
}

// Reviews List Component - List view with horizontal layout
const ReviewsList = React.memo(({ reviews, theme }: { reviews: any[]; theme: any }) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [imageColors, setImageColors] = useState<Record<string, { isDark: boolean; rgb: string }>>({});
  const fac = useMemo(() => new FastAverageColor(), []);

  // Format date for display
  const formatDate = (date: string | Date) => {
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };

  // Format rating for display
  const formatRating = (rating: number) => {
    if (rating <= 1) {
      return Math.round(rating * 100);
    } else if (rating <= 10) {
      return Math.round(rating * 10);
    } else {
      return Math.round(Math.max(0, Math.min(100, rating)));
    }
  };

  // Handle image load and color analysis
  const handleImageLoad = useCallback((reviewId: string, imgElement: HTMLImageElement) => {
    // Skip color analysis for external images that likely won't work with CORS
    const imageUrl = imgElement.src;
    const isExternalImage = imageUrl && !imageUrl.startsWith(window.location.origin) && !imageUrl.startsWith('/');
    
    if (isExternalImage) {
      // Use default dark theme for external images to avoid CORS issues
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: true,
          rgb: 'rgb(64, 64, 64)'
        }
      }));
      return;
    }

    try {
      // Only analyze local/same-origin images
      const result = fac.getColor(imgElement, { 
        algorithm: 'dominant',
        defaultColor: [128, 128, 128, 255]
      });
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: result.isDark,
          rgb: result.rgb
        }
      }));
    } catch (error) {
      // Fallback for any remaining errors
      console.warn(`Color analysis failed for image ${reviewId}:`, error instanceof Error ? error.message : String(error));
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: true,
          rgb: 'rgb(64, 64, 64)'
        }
      }));
    }
  }, [fac]);

  return (
    <>
      {/* CSS Styles for banner layout */}
      <style jsx>{`
        .banner-image-smart {
          object-position: center 25%;
          filter: brightness(1.1) contrast(1.05) saturate(1.1);
        }

        .banner-image-hq {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: -moz-crisp-edges;
          image-rendering: crisp-edges;
          image-rendering: high-quality;
          backface-visibility: hidden;
          transform: translateZ(0);
          will-change: transform;
        }

        .review-banner-overlay {
          background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.3) 50%, transparent 100%);
          border-radius: 0.75rem;
          transition: background 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          overflow: hidden;
        }

        .review-banner-overlay:hover {
          background: linear-gradient(to top, rgba(0,0,0,0.95) 0%, rgba(0,0,0,0.6) 50%, rgba(0,0,0,0.2) 100%);
        }
      `}</style>

      <div className="space-y-8 w-full">
        {reviews.map((review, index) => {

          
          const reviewUrl = `/reviews/view/${review.slug}`;
          const displayScore = formatRating(review.rating || 0);
          const imageColor = imageColors[review.id];
          
          // Dynamic text color based on image brightness
          const textColor = imageColor?.isDark === false ? 'text-gray-900' : 'text-white';
          const badgeClass = imageColor?.isDark === false ? 'bg-white/90 text-gray-900 border-gray-300' : 'bg-black/70 text-white border-white/20';
          


          return (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group"
              onMouseEnter={() => setHoveredCard(review.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <Link href={reviewUrl} className="block h-full">
                <Card className="bg-gray-900/40 backdrop-blur-sm overflow-hidden h-full hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 ease-out rounded-xl shadow-lg" style={{border: 'none', boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'}}>
                  <CardContent className="p-0 h-full">
                    <div className="relative h-64 w-full">
                      {/* Banner Image */}
                      {review.game_image || review.main_image_url || review.igdb_cover_url ? (
                        <img
                          src={review.game_image || review.main_image_url || review.igdb_cover_url}
                          alt={review.game_name}
                          className="w-full h-full object-cover banner-image-smart banner-image-hq transition-all duration-500 ease-out rounded-xl"
                          onLoad={(e) => handleImageLoad(review.id, e.target as HTMLImageElement)}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                          loading="lazy"
                        />
                      ) : null}
                      
                      {/* Fallback element */}
                      <div
                        className={`image-fallback w-full h-full flex items-center justify-center rounded-xl ${
                          (review.game_image || review.main_image_url || review.igdb_cover_url) ? 'hidden' : 'flex'
                        }`}
                        style={{ 
                          backgroundColor: `${theme?.colors?.primary}30`
                        }}
                      >
                        <div className="text-center">
                          <Gamepad2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                          <p className="text-xs text-gray-500">No image</p>
                        </div>
                      </div>

                      {/* Overlay */}
                      <div className="absolute inset-0 review-banner-overlay p-4">
                        {/* Score - Top Left */}
                        <div className="absolute top-4 left-4">
                          <div className={`flex items-center gap-2 px-3 py-2 rounded backdrop-blur-sm h-8 shadow-lg ${badgeClass}`}>
                            <span className={`font-mono text-xs ${textColor} opacity-80`}>score:</span>
                            <span className={`font-mono font-bold text-sm ${textColor} tabular-nums`}>
                              {displayScore}
                            </span>
                          </div>
                        </div>





                        {/* Content - Bottom */}
                        <div className="absolute bottom-4 left-4 right-4">
                          <h3 className={`font-mono font-bold text-lg ${textColor} drop-shadow-lg mb-2`}>
                            <span className={`${textColor} opacity-60 text-sm mr-2`}>//</span>
                            {review.game_name}
                          </h3>
                          <div className={`flex items-center justify-between ${textColor} text-xs opacity-90`}>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>
                                {formatDate(review.created_at)}
                                {review.played_on && (
                                  <>
                                    <span className="mx-3">•</span>
                                    <span>Played on {review.played_on}</span>
                                  </>
                                )}
                              </span>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>{review.view_count || 0}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Heart className="h-3 w-3" />
                                <span>{review.like_count || 0}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageSquare className="h-3 w-3" />
                                <span>{review.comment_count || 0}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </motion.div>
          );
        })}
      </div>
    </>
  );
});

ReviewsList.displayName = 'ReviewsList';

// Filter Controls Component (reused from grid version)
const FilterControls = React.memo(({
  searchTerm,
  setSearchTerm,
  filterPlatform,
  setFilterPlatform,
  availablePlatforms,
  isVisible
}: {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filterPlatform: string;
  setFilterPlatform: (value: string) => void;
  availablePlatforms: string[];
  isVisible: boolean;
}) => (
  <AnimatePresence>
    {isVisible && (
      <motion.div
        initial={{ opacity: 0, height: 0, y: -20 }}
        animate={{ opacity: 1, height: "auto", y: 0 }}
        exit={{ opacity: 0, height: 0, y: -20 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        className="overflow-hidden"
      >
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4">
          {/* Neural Search and Filters Header */}
          <div className="flex items-center mb-4">
            <div className="h-px bg-gray-600 flex-1" />
            <span className="mx-3 text-xs font-mono uppercase tracking-widest text-gray-400">
              <span className="text-gray-400">//</span> Search & Filter
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            {/* Neural Search Input */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3" />
              <Input
                placeholder="search reviews..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 bg-gray-800 border border-gray-700 text-white placeholder-gray-400 w-full text-xs h-8 font-mono hover:bg-gray-700 focus:bg-gray-700 transition-all duration-300 rounded"
              />
            </div>

            {/* Platform Filter */}
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono text-gray-400">platform:</span>
              <Select value={filterPlatform} onValueChange={setFilterPlatform}>
                <SelectTrigger className="w-28 bg-gray-800 border border-gray-700 text-white text-xs h-8 font-mono rounded">
                  <SelectValue placeholder="all" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border border-gray-700">
                  <SelectItem value="all" className="text-xs font-mono">all</SelectItem>
                  {availablePlatforms.map((platform) => (
                    <SelectItem key={platform} value={platform} className="text-xs font-mono">
                      {platform.toLowerCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </motion.div>
    )}
  </AnimatePresence>
));

FilterControls.displayName = 'FilterControls';

// Reviews Section Component for List View
const ReviewsSectionList = React.memo(({
  profileData,
  currentUserId,
  isOwnProfile,
  theme,
  searchTerm,
  filterPlatform,
  itemsPerPage = 8,
  defaultSort = 'date'
}: ReviewCardsListProps) => {
  const [displayedCount, setDisplayedCount] = useState(itemsPerPage);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Background brightness detection for text adaptation
  const isDarkBackground = useBackgroundBrightness();

  const {
    data,
    isLoading,
    error
  } = useUserContent(profileData.id, currentUserId || undefined);

  const filteredReviews = useMemo(() => {
    if (!data?.reviews) return [];

    let filtered = data.reviews.filter(review => {
      // Search filter
      const matchesSearch = !searchTerm ||
        review.game_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.review_text.toLowerCase().includes(searchTerm.toLowerCase());

      // Platform filter - using played_on field (where user actually played)
      const matchesPlatform = filterPlatform === 'all' ||
        review.played_on?.toLowerCase() === filterPlatform.toLowerCase();

      return matchesSearch && matchesPlatform;
    });

    // Sort based on defaultSort setting
    return filtered.sort((a, b) => {
      switch (defaultSort) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'title':
          return a.game_name.localeCompare(b.game_name);
        case 'date':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [data?.reviews, searchTerm, filterPlatform, defaultSort]);

  // Get unique platforms for filter options
  const availablePlatforms = useMemo(() => {
    if (!data?.reviews) return [];
    const platforms = new Set<string>();
    data.reviews.forEach(review => {
      if (review.platform) platforms.add(review.platform);
    });
    return Array.from(platforms).sort();
  }, [data?.reviews]);

  // Infinite scroll logic
  const currentReviews = filteredReviews.slice(0, displayedCount);
  const hasMoreReviews = displayedCount < filteredReviews.length;

  // Reset displayed count when search or filters change
  useEffect(() => {
    setDisplayedCount(itemsPerPage);
  }, [searchTerm, filterPlatform, itemsPerPage]);

  // Load more reviews function
  const loadMoreReviews = useCallback(async () => {
    if (isLoadingMore || !hasMoreReviews) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    setDisplayedCount(prev => prev + itemsPerPage);
    setIsLoadingMore(false);
  }, [isLoadingMore, hasMoreReviews, itemsPerPage]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return null; // Silently fail for reviews
  }

  if (!filteredReviews.length && !searchTerm) {
    return null; // Don't show empty module if no reviews exist
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={`reviews-list-${searchTerm}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentReviews.length > 0 ? (
            <>
              <ReviewsList reviews={currentReviews} theme={theme} />
              {hasMoreReviews && (
                <div className="flex justify-center mt-8">
                  <Button
                    onClick={loadMoreReviews}
                    disabled={isLoadingMore}
                    className={cn(
                      "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
                      isDarkBackground
                        ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70'
                        : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
                    )}
                    style={{
                      backdropFilter: 'blur(8px)',
                      boxShadow: isDarkBackground
                        ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)'
                        : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
                    }}
                  >
                    {isLoadingMore ? (
                      <motion.div
                        className="flex items-center gap-2"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Clock className="w-4 h-4" />
                        </motion.div>
                        Loading more reviews...
                      </motion.div>
                    ) : (
                      <span className="flex items-center gap-2">
                        Load More Reviews
                        <Badge
                          variant="secondary"
                          className="bg-slate-700 text-slate-300 text-xs"
                        >
                          {filteredReviews.length - displayedCount} remaining
                        </Badge>
                      </span>
                    )}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12 text-gray-400">
              <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No reviews found</p>
              <p className="text-sm">Try adjusting your search filters</p>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
});

ReviewsSectionList.displayName = 'ReviewsSectionList';

// Main export - combine all components for list view
export { ReviewsList, FilterControls, ReviewsSectionList };
export default ReviewsSectionList;