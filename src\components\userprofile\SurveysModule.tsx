'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import {
  Target,
  Search,
  Monitor,
  Cpu,
  Zap,
  Calendar,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  Heart,
  Loader2
} from 'lucide-react';
import { useUserContent } from '@/hooks/useUserContent';

interface UserSurvey {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  performance_score: number;
  fps_average: number;
  resolution: string;
  graphics_settings: string;
  created_at: string;
  hardware_used: string;
  notes?: string;
  is_public: boolean;
  is_verified: boolean;
}

interface SurveysModuleProps {
  userId: string;
  currentUserId?: string;
  isOwnProfile?: boolean;
  theme?: any;
}


// Componente principal do módulo de surveys
export default function SurveysModule({
  userId,
  currentUserId,
  isOwnProfile = false,
  theme
}: SurveysModuleProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredSurvey, setHoveredSurvey] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const surveysPerPage = 8;

  // Use the real useUserContent hook
  const {
    data,
    isLoading,
    error
  } = useUserContent(userId, currentUserId);

  // Filter surveys based on search criteria
  const filteredSurveys = useMemo(() => {
    if (!data?.surveys) return [];

    let filtered = data.surveys.filter(survey =>
      !searchTerm ||
      survey.game_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      survey.hardware_used.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort by date (most recent first)
    return filtered.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [data?.surveys, searchTerm]);

  // Pagination logic
  const totalPages = Math.ceil(filteredSurveys.length / surveysPerPage);
  const startIndex = (currentPage - 1) * surveysPerPage;
  const endIndex = startIndex + surveysPerPage;
  const currentSurveys = filteredSurveys.slice(startIndex, endIndex);

  // Reset page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
          {/* Header */}
          <div className="flex items-center mb-4">
            <div className="h-px bg-gray-600 flex-1" />
            <span 
              className="mx-3 text-xs font-mono uppercase tracking-widest"
              style={{ color: theme?.colors?.primary || '#00D4FF' }}
            >
              <span className="text-gray-400">//</span> Performance
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>
          
          <div className="flex items-center justify-center py-8">
            <Loader2 size={20} className="animate-spin text-gray-400" />
            <span className="ml-2 text-xs text-gray-400 font-mono">Loading surveys...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
          {/* Header */}
          <div className="flex items-center mb-4">
            <div className="h-px bg-gray-600 flex-1" />
            <span 
              className="mx-3 text-xs font-mono uppercase tracking-widest"
              style={{ color: theme?.colors?.primary || '#00D4FF' }}
            >
              <span className="text-gray-400">//</span> Performance
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>
          
          <div className="py-8 text-center">
            <div className="text-xs text-red-400 mb-2 font-mono">Failed to load</div>
            <div className="text-xs text-gray-400 font-mono">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full overflow-hidden">
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="h-px bg-gray-600 flex-1" />
          <span 
            className="mx-3 text-xs font-mono uppercase tracking-widest"
            style={{ color: theme?.colors?.primary || '#00D4FF' }}
          >
            <span className="text-gray-400">//</span> Performance
          </span>
          <div className="h-px bg-gray-600 flex-1" />
        </div>
        
        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
          <Input
            placeholder="search surveys..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 bg-gray-800 border border-gray-700 text-white placeholder-gray-400 w-full text-xs h-8 font-mono hover:bg-gray-700 focus:bg-gray-700 transition-all duration-300"
            style={{
              borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
            }}
          />
        </div>

        {/* Content */}
        {currentSurveys.length > 0 ? (
          <>
            <div className="space-y-2">
              {currentSurveys.map((survey, idx) => {
                const animDelay = idx * 50;
                // Convert performance score to 0-144 range for gauge
                const gaugeValue = Math.round((survey.performance_score / 100) * 144);
                // Mock like value (will be real data later)
                const mockLikes = Math.floor(Math.random() * 50) + 5;
                
                return (
                  <motion.div
                    key={survey.id}
                    className="flex items-center gap-3 p-3 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-300 group"
                    style={{
                      borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                    }}
                    whileHover={{ 
                      x: 2,
                      borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.3)`
                    }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: animDelay / 1000 }}
                  >
                    {/* Game Icon/Image */}
                    <div 
                      className="w-6 h-6 rounded flex items-center justify-center flex-shrink-0 transition-colors"
                      style={{ 
                        backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                        color: theme?.colors?.primary || '#00D4FF'
                      }}
                    >
                      {survey.game_image ? (
                        <img 
                          src={survey.game_image} 
                          alt={survey.game_name}
                          className="w-6 h-6 rounded object-cover"
                        />
                      ) : (
                        <Target size={12} />
                      )}
                    </div>
                    
                    {/* Game Name */}
                    <div className="min-w-0 flex-1">
                      <div className="text-xs font-mono font-bold text-white truncate">{survey.game_name}</div>
                      <div className="flex items-center gap-2 mt-1">
                        {/* FPS Score */}
                        <div 
                          className="text-xs font-mono tabular-nums"
                          style={{ color: theme?.colors?.primary || '#00D4FF' }}
                        >
                          {survey.fps_average} FPS
                        </div>
                        
                        {/* Like Count */}
                        <div className="flex items-center gap-1 text-red-400">
                          <Heart className="w-2 h-2" />
                          <span className="text-xs tabular-nums">{mockLikes}</span>
                        </div>
                        
                        {/* Verification Badge */}
                        {survey.is_verified && (
                          <div className="flex items-center">
                            <Zap className="w-2 h-2 text-cyan-400" />
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center mt-4">
                <div className="flex items-center bg-gray-800 rounded-lg border border-gray-700 p-1">
                  <motion.button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`flex items-center justify-center w-6 h-6 rounded transition-all font-mono text-xs ${
                      currentPage === 1
                        ? 'text-gray-500 cursor-not-allowed'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    style={currentPage !== 1 ? {
                      color: theme?.colors?.primary || '#00D4FF'
                    } : {}}
                    whileHover={currentPage !== 1 ? { scale: 1.1 } : {}}
                  >
                    <ChevronLeft className="w-3 h-3" />
                  </motion.button>

                  <div className="px-3 py-1 text-xs text-gray-400 font-mono">
                    {currentPage}/{totalPages}
                  </div>

                  <motion.button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className={`flex items-center justify-center w-6 h-6 rounded transition-all font-mono text-xs ${
                      currentPage === totalPages
                        ? 'text-gray-500 cursor-not-allowed'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    style={currentPage !== totalPages ? {
                      color: theme?.colors?.primary || '#00D4FF'
                    } : {}}
                    whileHover={currentPage !== totalPages ? { scale: 1.1 } : {}}
                  >
                    <ChevronRight className="w-3 h-3" />
                  </motion.button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-4"
            >
              <div 
                className="w-12 h-12 mx-auto mb-3 rounded-xl border flex items-center justify-center"
                style={{
                  backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.1)`,
                  borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                  color: theme?.colors?.primary || '#00D4FF'
                }}
              >
                <Target className="h-6 w-6" />
              </div>
            </motion.div>
            
            <div className="text-xs font-mono font-bold text-white mb-2">
              surveys.empty() <span className="text-primary-accent animate-pulse">|</span>
            </div>
            
            <p className="text-gray-400 font-mono text-xs">
              <span className="text-gray-500">// </span>{searchTerm ? 'No matching performance data' : 'No survey data available'}
            </p>
            
            {searchTerm && (
              <motion.button
                type="button"
                onClick={() => setSearchTerm('')}
                className="mt-3 px-3 py-1 rounded-lg bg-gray-800 border border-gray-700 text-xs font-mono transition-all duration-300 hover:bg-gray-700"
                style={{
                  borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                  color: theme?.colors?.primary || '#00D4FF'
                }}
                whileHover={{ x: 2 }}
              >
                clear()
              </motion.button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
