'use client';

import { useState, useEffect } from 'react';
import { useAuthContext } from './use-auth-context';

interface MFAStatus {
  isRequired: boolean;
  isVerified: boolean;
  isConfigured: boolean;
  loading: boolean;
  error: string | null;
}

interface UseMFAGuardResult {
  mfaStatus: MFAStatus;
  showMFAChallenge: boolean;
  handleMFASuccess: () => void;
  checkMFAStatus: () => Promise<void>;
  resetMFAState: () => void;
}

export function useMFAGuard(): UseMFAGuardResult {
  const { user } = useAuthContext();
  const [mfaStatus, setMfaStatus] = useState<MFAStatus>({
    isRequired: false,
    isVerified: false,
    isConfigured: false,
    loading: true,
    error: null,
  });
  const [showMFAChallenge, setShowMFAChallenge] = useState(false);

  const checkMFAStatus = async () => {
    if (!user) {
      setMfaStatus(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setMfaStatus(prev => ({ ...prev, loading: true, error: null }));

      // DEBUG: Log the API call
      console.log('🔍 MFA Guard: Checking admin verification status for user:', user.id);

      // Check admin verification status (includes MFA status)
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error('🚨 MFA Guard: Admin verify API failed:', response.status, response.statusText);
        throw new Error('Failed to verify admin status');
      }

      const result = await response.json();
      console.log('📊 MFA Guard: Admin verify result:', result);

      if (!result.isAdmin) {
        throw new Error('Admin access required');
      }

      const newMfaStatus = {
        isRequired: result.mfaRequired || false,
        isVerified: result.mfaVerified || false,
        isConfigured: result.mfaConfigured || false,
        loading: false,
        error: null
      };

      console.log('🔐 MFA Guard: New MFA status:', newMfaStatus);
      setMfaStatus(newMfaStatus);

      // Show MFA challenge if MFA is required but not verified
      if (newMfaStatus.isRequired && !newMfaStatus.isVerified) {
        console.log('🚨 MFA Guard: MFA required but not verified - showing challenge');
        if (newMfaStatus.isConfigured) {
          console.log('✅ MFA Guard: MFA configured - showing challenge modal');
          setShowMFAChallenge(true);
        } else {
          console.log('⚠️ MFA Guard: MFA not configured - redirecting to setup');
          // MFA is required but not configured - redirect to setup
          window.location.href = '/admin/security/mfa';
        }
      } else {
        console.log('✅ MFA Guard: MFA not required or already verified - hiding challenge');
        setShowMFAChallenge(false);
      }

    } catch (error: any) {
      console.error('🚨 MFA Guard: Error checking MFA status:', error);
      setMfaStatus({
        isRequired: false,
        isVerified: false,
        isConfigured: false,
        loading: false,
        error: error.message || 'Failed to check MFA status',
      });
    }
  };

  const handleMFASuccess = () => {
    setMfaStatus(prev => ({
      ...prev,
      isVerified: true,
    }));
    setShowMFAChallenge(false);
  };

  const resetMFAState = () => {
    setMfaStatus({
      isRequired: false,
      isVerified: false,
      isConfigured: false,
      loading: true,
      error: null,
    });
    setShowMFAChallenge(false);
  };

  // Check MFA status when user changes
  useEffect(() => {
    if (user) {
      checkMFAStatus();
    } else {
      resetMFAState();
    }
  }, [user]);

  // Periodic check for MFA session expiration (every 5 minutes)
  useEffect(() => {
    if (!user || !mfaStatus.isRequired) return;

    const interval = setInterval(() => {
      checkMFAStatus();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [user, mfaStatus.isRequired]);

  return {
    mfaStatus,
    showMFAChallenge,
    handleMFASuccess,
    checkMFAStatus,
    resetMFAState,
  };
}
