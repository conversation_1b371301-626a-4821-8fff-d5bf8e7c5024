'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Image as ImageIcon,
  Trash2,
  Plus,
  Settings,
  LayoutTemplate,
  MessageSquare,
  FileText,
  X,
  ExternalLink,
  Info,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';

// Import monetization-specific styles
import './style/monetizationConfig.css';

// Import standardized types
import type { MonetizationBlock, MonetizationBlockData } from '@/lib/types';

// Component props
interface MonetizationConfiguratorProps {
  monetizationBlocks: MonetizationBlock[];
  onMonetizationBlocksChange: (blocks: MonetizationBlock[]) => void;
}

const MonetizationConfigurator: React.FC<MonetizationConfiguratorProps> = ({
  monetizationBlocks,
  onMonetizationBlocksChange
}) => {
  // State management
  const [activeConfig, setActiveConfig] = useState<string | null>(null);
  const [configData, setConfigData] = useState<MonetizationBlockData>({});

  // Placement configurations
  const placements = [
    {
      id: 'after-banner',
      label: 'Top Banner',
      description: 'Display below main banner image',
      icon: LayoutTemplate,
      limit: 1,
      available: true
    },
    {
      id: 'above-comments', 
      label: 'Above Comments',
      description: 'Display above comment section',
      icon: MessageSquare,
      limit: 1,
      available: true
    },
    {
      id: 'in-editor',
      label: 'In Text Banner',
      description: 'Insert within article content',
      icon: FileText,
      limit: 3,
      available: false // Will be implemented later
    }
  ];

  // Helper functions
  const getBannersForPlacement = (placementId: string) => {
    return monetizationBlocks.filter(block => block.placement === placementId);
  };

  const canAddToPlacement = (placementId: string) => {
    const placement = placements.find(p => p.id === placementId);
    if (!placement) return false;
    return getBannersForPlacement(placementId).length < placement.limit;
  };

  // Handle configuration start
  const handleStartConfig = (placementId: string) => {
    setActiveConfig(placementId);
    setConfigData({});
  };

  // Handle configuration cancel
  const handleCancelConfig = () => {
    setActiveConfig(null);
    setConfigData({});
  };

  // Handle input changes
  const handleInputChange = (field: keyof MonetizationBlockData, value: string) => {
    setConfigData(prev => ({ ...prev, [field]: value }));
  };

  // Handle banner creation
  const handleCreateBanner = () => {
    if (!activeConfig || !configData.imageUrl || !configData.linkUrl) return;

    const newBlock: MonetizationBlock = {
      id: Date.now().toString(),
      type: 'imageLink',
      placement: activeConfig,
      data: configData
    };

    onMonetizationBlocksChange([...monetizationBlocks, newBlock]);
    handleCancelConfig();
  };

  // Handle banner removal
  const handleRemoveBanner = (bannerId: string) => {
    const updatedBlocks = monetizationBlocks.filter(block => block.id !== bannerId);
    onMonetizationBlocksChange(updatedBlocks);
  };

  return (
    <div className="space-y-6">
      {/* Default Banner Info */}
      <div className="bg-slate-800/50 border border-white/10 rounded-xl p-6 transition-all duration-300">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <span className="font-mono text-sm text-slate-300">// Default Banner Policy</span>
          </div>
          <div className="font-mono text-xs text-slate-500">
            <span className="text-violet-400">{monetizationBlocks.length}</span>
            <span className="text-slate-600 mx-1">/</span>
            <span className="text-slate-500">5</span>
            <span className="ml-2">slots used</span>
          </div>
        </div>
        <div className="flex items-start gap-3">
          <Info className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
          <div className="space-y-1">
            <p className="text-slate-400 text-xs leading-relaxed font-lato">
              Empty banner slots will display Critical Pixel promotional content. 
              You can customize this behavior in your{' '}
              <span className="text-cyan-400 font-mono">Dashboard &gt; Settings</span>.
            </p>
          </div>
        </div>
      </div>

      {/* Placement Cards */}
      <div className="space-y-4">
        {placements.map((placement) => {
          const banners = getBannersForPlacement(placement.id);
          const canAdd = canAddToPlacement(placement.id);
          const isConfiguring = activeConfig === placement.id;
          const IconComponent = placement.icon;

          return (
            <div key={placement.id} className="space-y-3">
              {/* Placement Header */}
              <div className="bg-slate-800/50 border border-white/10 rounded-xl transition-all duration-300 hover:bg-slate-800/60">
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-md bg-slate-800/60">
                        <IconComponent className="h-4 w-4 text-slate-400" />
                      </div>
                      <div>
                        <div className="font-mono text-sm">
                          <span className="text-slate-300">// {placement.label}</span>
                        </div>
                        <p className="text-slate-500 text-xs mt-1 font-lato">
                          {placement.description}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="font-mono text-xs">
                        <span className="text-slate-500">{banners.length}/{placement.limit}</span>
                      </div>
                      
                      {placement.available ? (
                        canAdd && !isConfiguring ? (
                          <Button
                            onClick={() => handleStartConfig(placement.id)}
                            size="sm"
                            className="bg-violet-600/20 hover:bg-violet-600/30 text-violet-300 border border-violet-500/30 font-mono transition-all duration-200"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                        ) : isConfiguring ? (
                          <Button
                            onClick={handleCancelConfig}
                            size="sm"
                            variant="ghost"
                            className="text-slate-500 hover:text-slate-300 transition-colors duration-200"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        ) : (
                          <div className="font-mono text-xs text-slate-500 px-2 py-1 bg-slate-800/40 rounded border border-slate-700/40">
                            FULL
                          </div>
                        )
                      ) : (
                        <div className="font-mono text-xs text-orange-400 px-2 py-1 bg-orange-400/10 rounded border border-orange-400/20">
                          SOON
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Configuration Form */}
                  <div 
                    className="overflow-hidden transition-all duration-500 ease-in-out"
                    style={{
                      maxHeight: isConfiguring ? '800px' : '0px',
                      opacity: isConfiguring ? 1 : 0
                    }}
                  >
                    <div className="mt-6 pt-6 border-t border-white/10 space-y-6">
                      {/* Banner Size Recommendations */}
                      <div className="bg-slate-800/30 border border-white/10 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="space-y-1">
                            <div className="font-mono text-xs text-slate-300">
                              // Recommended Banner Dimensions
                            </div>
                            <div className="text-xs text-slate-400 space-y-1 font-lato">
                              <div>
                                <span className="text-cyan-400 font-mono">1456x180px</span>
                                <span className="text-slate-500 mx-2">•</span>
                                <span>High quality, crisp on all displays</span>
                              </div>
                              <div>
                                <span className="text-orange-400 font-mono">728x90px</span>
                                <span className="text-slate-500 mx-2">•</span>
                                <span>Acceptable, may appear blurry on high-DPI screens</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <div className="font-mono text-xs text-slate-400 mb-3">// IMAGE_URL</div>
                          <Input
                            placeholder="https://your-banner-image.jpg"
                            value={configData.imageUrl || ''}
                            onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                            className="bg-slate-800/60 border-white/10 text-slate-200 placeholder:text-slate-500 
                                     focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-lato text-sm
                                     transition-all duration-200"
                          />
                        </div>
                        
                        <div>
                          <div className="font-mono text-xs text-slate-400 mb-3">// AFFILIATE_LINK</div>
                          <Input
                            placeholder="https://your-affiliate-link.com"
                            value={configData.linkUrl || ''}
                            onChange={(e) => handleInputChange('linkUrl', e.target.value)}
                            className="bg-slate-800/60 border-white/10 text-slate-200 placeholder:text-slate-500 
                                     focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-lato text-sm
                                     transition-all duration-200"
                          />
                        </div>
                      </div>

                      {/* Preview */}
                      {configData.imageUrl && (
                        <div className="animate-in fade-in duration-300">
                          <div className="font-mono text-xs text-slate-400 mb-3">// PREVIEW</div>
                          <div className="bg-slate-800/40 border border-white/10 rounded-lg overflow-hidden max-w-md transition-all duration-200">
                            <img 
                              src={configData.imageUrl} 
                              alt="Banner preview"
                              className="w-full h-20 object-cover transition-opacity duration-200"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                            <div className="hidden p-4 flex items-center justify-center">
                              <div className="text-center">
                                <ImageIcon className="h-6 w-6 text-slate-500 mx-auto mb-1" />
                                <p className="text-xs text-slate-500 font-lato">Invalid image URL</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex justify-end gap-3 pt-2">
                        <Button
                          onClick={handleCreateBanner}
                          disabled={!configData.imageUrl || !configData.linkUrl}
                          className="bg-violet-600 hover:bg-violet-700 text-white disabled:opacity-50 font-mono
                                   transition-all duration-200 disabled:cursor-not-allowed"
                        >
                          Insert Banner
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Active Banners */}
                {banners.length > 0 && (
                  <div className="border-t border-white/10 bg-slate-800/20 animate-in slide-in-from-bottom-2 duration-300">
                    <div className="p-4 space-y-3">
                      <div className="font-mono text-sm text-slate-400">
                        // Active Banners
                      </div>
                      {banners.map((banner) => (
                        <div 
                          key={banner.id}
                          className="flex items-center gap-3 p-3 bg-slate-800/40 border border-white/10 rounded-lg
                                   hover:border-violet-400/30 transition-all duration-200 group hover:bg-slate-800/60"
                        >
                          {/* Banner Thumbnail */}
                          <div className="relative w-16 h-10 rounded bg-slate-700/50 flex-shrink-0 overflow-hidden">
                            <img 
                              src={banner.data.imageUrl} 
                              alt="Banner"
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                            <div className="hidden absolute inset-0 flex items-center justify-center">
                              <ImageIcon className="h-4 w-4 text-slate-500" />
                            </div>
                          </div>
                          
                          {/* Banner Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <ExternalLink className="h-3 w-3 text-slate-500" />
                              <p className="text-sm text-slate-300 truncate font-lato">
                                {banner.data.linkUrl}
                              </p>
                            </div>
                          </div>
                          
                          {/* Remove Button */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveBanner(banner.id)}
                            className="opacity-0 group-hover:opacity-100 transition-all duration-200 text-slate-500 hover:text-red-400 hover:bg-red-400/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Navigation Buttons */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4">
        <div className="flex items-center space-x-2 min-w-0">
          <div className="w-2 h-2 bg-amber-400/60 rounded-full animate-pulse flex-shrink-0" />
          <span className="text-sm text-slate-400/80 font-mono break-words">
            {monetizationBlocks.length > 0
              ? '// Monetization configured'
              : '// Optional: Configure monetization'
            }
          </span>
        </div>

        {/* Continue buttons removed - no step progression needed */}
      </div>
    </div>
  );
};

export default MonetizationConfigurator;