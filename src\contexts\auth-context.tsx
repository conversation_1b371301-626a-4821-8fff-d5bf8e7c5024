// src/contexts/auth-context.tsx
// PHASE 2 IMPLEMENTATION: Real Supabase Authentication + RLS Integration
// Updated: January 2025 - Supabase authentication with Row Level Security
'use client';

import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState, useMemo, Dispatch, SetStateAction } from 'react';
import type { User } from '@supabase/supabase-js';
import type { UserProfile } from '@/lib/types/profile';
import type { ExtendedUserProfile } from '@/lib/types';
import { convertToExtendedProfile, convertFromExtendedProfile } from '@/utils/profile-conversion';
import { createClient } from '@/lib/supabase/client';

// Real Supabase function to update user profile with RLS enforcement
const updateUserProfileFields = async (userId: string, fields: Partial<UserProfile>) => {
  try {
    const supabase = createClient();

    // RLS automatically enforces that users can only update their own profile
    const { data, error } = await supabase
      .from('profiles')
      .update(fields)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Profile update error:', error);
      throw new Error(`Profile update failed: ${error.message}`);
    }

    return data;
  } catch (error) {
    console.error('updateUserProfileFields error:', error);
    throw error;
  }
};

interface AuthContextType {
  user: ExtendedUserProfile | null;
  supabaseUser: User | null;
  loading: boolean;
  isAdmin: boolean;
  isSuspended: boolean; // NEW: Suspension status
  suspensionReason?: string; // NEW: Suspension reason
  suspendedAt?: string; // NEW: Suspension timestamp
  authVersion: number; // Add version to force re-renders
  setUser: Dispatch<SetStateAction<ExtendedUserProfile | null>>;
  updateProfile: (fields: Partial<ExtendedUserProfile>) => Promise<ExtendedUserProfile>;
  signOut: () => Promise<void>;
  checkSuspensionStatus: () => Promise<void>; // NEW: Force suspension check
}

const defaultContext: AuthContextType = {
  user: null,
  supabaseUser: null,
  loading: true,
  isAdmin: false,
  isSuspended: false,
  suspensionReason: undefined,
  suspendedAt: undefined,
  authVersion: 0,
  setUser: () => {},
  updateProfile: async () => { throw new Error('Auth context not initialized'); },
  signOut: async () => { throw new Error('Auth context not initialized'); },
  checkSuspensionStatus: async () => { throw new Error('Auth context not initialized'); },
};

export const AuthContext = createContext<AuthContextType>(defaultContext);

export const useAuthContext = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<ExtendedUserProfile | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [authVersion, setAuthVersion] = useState(0);
  const [isSuspended, setIsSuspended] = useState(false);
  const [suspensionReason, setSuspensionReason] = useState<string | undefined>(undefined);
  const [suspendedAt, setSuspendedAt] = useState<string | undefined>(undefined);

  // Initialize Supabase client
  const supabase = createClient();

  // Fetch user profile from database with RLS enforcement
  const fetchUserProfile = async (userId: string): Promise<ExtendedUserProfile | null> => {
    try {
      // First, verify we have a valid session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (!session) {
        return null;
      }

      // Add timeout to prevent hanging
      const profilePromise = supabase
        .from('profiles')
        .select(`
          *,
          suspended,
          suspension_reason,
          suspended_at,
          suspended_by
        `)
        .eq('id', userId)
        .single();

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile fetch timeout')), 5000)
      );

      const { data, error } = await Promise.race([profilePromise, timeoutPromise]) as any;

      if (error) {
        return null;
      }

      // Convert database profile to UserProfile format first
      const supabaseProfile: UserProfile = {
        id: data.id,
        username: data.username,
        display_name: data.display_name || '',
        slug: data.slug,
        slug_lower: data.slug_lower,
        email: supabaseUser?.email || null,
        avatar_url: data.avatar_url || null,
        banner_url: data.banner_url || null,
        bio: data.bio || null,
        website: data.website || null,
        location: data.location || null,
        preferred_genres: data.preferred_genres || null,
        favorite_consoles: data.favorite_consoles || null,
        theme: data.theme || 'muted-dark',
        custom_colors: data.custom_colors || null,
        is_admin: data.is_admin || false,
        is_online: data.is_online || false,
        last_seen: data.last_seen || null,
        level: data.level || 1,
        experience: data.experience || 0,
        review_count: data.review_count || 0,
        privacy_settings: data.privacy_settings || {
          profile_visibility: 'public',
          show_online_status: true,
          show_gaming_profiles: true,
          show_achievements: true,
          allow_contact: true,
          allow_friend_requests: true
        },
        created_at: data.created_at,
        updated_at: data.updated_at,
        suspended: data.suspended || false,
        suspension_reason: data.suspension_reason || null,
        suspended_at: data.suspended_at || null,
        suspended_by: data.suspended_by || null
      };

      // Update suspension state
      setIsSuspended(data.suspended || false);
      setSuspensionReason(data.suspension_reason || undefined);
      setSuspendedAt(data.suspended_at || undefined);

      // Convert to ExtendedUserProfile for legacy compatibility
      return convertToExtendedProfile(supabaseProfile);
    } catch (error) {
      console.error('fetchUserProfile error:', error);
      return null;
    }
  };

  // Update user profile function
  const updateProfile = async (fields: Partial<ExtendedUserProfile>): Promise<ExtendedUserProfile> => {
    if (!user?.uid) {
      throw new Error('No authenticated user');
    }

    // Convert ExtendedUserProfile fields to UserProfile format for database update
    const supabaseFields = convertFromExtendedProfile(fields);
    const updatedProfile = await updateUserProfileFields(user.uid, supabaseFields);
    const newProfile = await fetchUserProfile(user.uid);

    if (newProfile) {
      setUser(newProfile);
      return newProfile;
    }

    throw new Error('Failed to fetch updated profile');
  };

  // Check suspension status function
  const checkSuspensionStatus = async (): Promise<void> => {
    if (!supabaseUser?.id) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('suspended, suspension_reason, suspended_at')
        .eq('id', supabaseUser.id)
        .single();

      if (error) {
        console.error('Error checking suspension status:', error);
        return;
      }

      setIsSuspended(data?.suspended || false);
      setSuspensionReason(data?.suspension_reason || undefined);
      setSuspendedAt(data?.suspended_at || undefined);

    } catch (error) {
      console.error('Suspension check failed:', error);
    }
  };

  // Sign out function
  const signOut = async (): Promise<void> => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
        throw error;
      }

      // Clear local state
      setUser(null);
      setSupabaseUser(null);
      setIsSuspended(false);
      setSuspensionReason(undefined);
      setSuspendedAt(undefined);
    } catch (error) {
      console.error('signOut error:', error);
      throw error;
    }
  };

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session error:', error);
          setLoading(false);
          return;
        }

        if (session?.user) {
          setSupabaseUser(session.user);
          const profile = await fetchUserProfile(session.user.id);
          setUser(profile);
        }

        setLoading(false);
      } catch (error) {
        console.error('getInitialSession error:', error);
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setSupabaseUser(session.user);

          // For auth state changes, use a small delay to ensure RLS is ready
          const delay = event === 'SIGNED_IN' ? 100 : 0; // Only delay for new sign-ins

          setTimeout(async () => {
            try {
              const profile = await fetchUserProfile(session.user.id);
              if (profile) {
                setUser(profile);
              } else {
                setUser(null);
              }
            } catch (error) {
              setUser(null);
            } finally {
              setLoading(false);
            }
          }, delay);
        } else {
          setSupabaseUser(null);
          setUser(null);
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Increment auth version when user state changes to force re-renders
  useEffect(() => {
    setAuthVersion(prev => prev + 1);
  }, [user, loading]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    user,
    supabaseUser,
    loading,
    isAdmin: user?.isAdmin || false,
    isSuspended,
    suspensionReason,
    suspendedAt,
    authVersion,
    setUser,
    updateProfile,
    signOut,
    checkSuspensionStatus,
  }), [user, supabaseUser, loading, authVersion, isSuspended, suspensionReason, suspendedAt, checkSuspensionStatus]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};