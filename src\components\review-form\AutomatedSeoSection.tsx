'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  ArrowRight,
  Target,
  FileText,
  CheckCircle,
  AlertTriangle,
  Eye,
  Search,
  Sparkles,
  RefreshCw,
  Zap,
  TrendingUp,
  Brain,
  Wand2
} from 'lucide-react';

// Import the CSS to ensure button styles are loaded
import '@/components/review-form/style/NewReview.css';

// Import our metadata generation services
import { metadataGenerator, type ReviewData, type GeneratedMetadata } from '@/lib/metadata/generator';
import { contentAnalyzer, type ContentAnalysis } from '@/lib/content/analyzer';
import { socialMetaGenerator, type SocialPreview } from '@/lib/social/meta-generator';
import SocialMediaPreview from '@/components/review-form/SocialMediaPreview';

interface AutomatedSeoSectionProps {
  // Current SEO state
  metaTitle: string;
  onMetaTitleChange: (value: string) => void;
  metaDescription: string;
  onMetaDescriptionChange: (value: string) => void;
  focusKeyword: string;
  onFocusKeywordChange: (value: string) => void;
  
  // Review data for automation
  reviewData: ReviewData;
  
  // Navigation
  handlePrevStep: () => void;
  handleNextStep: () => void;
}

// Character counter component
const CharacterCounter = ({ current, max }: { current: number; max: number }) => {
  const percentage = (current / max) * 100;
  const isNearLimit = percentage > 80;
  const isOverLimit = percentage > 95;
  
  return (
    <div className="flex items-center space-x-2 mt-2">
      <div className="flex-1 h-1 bg-slate-800 rounded-full overflow-hidden">
        <div
          className={`h-full transition-colors duration-300 ${
            isOverLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-gradient-to-r from-violet-500 to-cyan-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      <span className={`text-xs font-mono transition-colors duration-300 ${
        isOverLimit ? 'text-red-400' : isNearLimit ? 'text-yellow-400' : 'text-slate-400'
      }`}>
        {current}/{max}
      </span>
    </div>
  );
};

// Confidence indicator component
const ConfidenceIndicator = ({ score }: { score: number }) => {
  const getColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  const getLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Work';
  };

  return (
    <div className="flex items-center space-x-2">
      <TrendingUp className={`h-4 w-4 ${getColor(score)}`} />
      <span className={`text-xs font-mono ${getColor(score)}`}>
        {getLabel(score)} ({score}%)
      </span>
    </div>
  );
};

const AutomatedSeoSection: React.FC<AutomatedSeoSectionProps> = ({
  metaTitle,
  onMetaTitleChange,
  metaDescription,
  onMetaDescriptionChange,
  focusKeyword,
  onFocusKeywordChange,
  reviewData,
  handlePrevStep,
  handleNextStep,
}) => {
  // State for automation features
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedMetadata, setGeneratedMetadata] = useState<GeneratedMetadata | null>(null);
  const [contentAnalysis, setContentAnalysis] = useState<ContentAnalysis | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [autoMode, setAutoMode] = useState(false);
  const [lastApplied, setLastApplied] = useState<string[]>([]);
  const [socialPreviews, setSocialPreviews] = useState<SocialPreview[]>([]);

  // Check if any content is provided
  const hasAnyContent = metaTitle.length > 0 || metaDescription.length > 0 || focusKeyword.length > 0;
  const hasReviewContent = reviewData.reviewContentLexical && reviewData.reviewContentLexical.length > 0;

  // Auto-populate meta title from content title if empty
  const handleMetaTitleFocus = () => {
    if (!metaTitle && reviewData.reviewTitle) {
      onMetaTitleChange(reviewData.reviewTitle.substring(0, 60));
    }
  };

  // Generate automated metadata
  const generateMetadata = useCallback(async () => {
    if (!reviewData.gameName) return;

    setIsGenerating(true);
    try {
      const metadata = await metadataGenerator.generateMetadata(reviewData);
      setGeneratedMetadata(metadata);

      // Generate social media previews
      const authorName = reviewData.authorName || 'CriticalPixel User';
      const previews = socialMetaGenerator.generateSocialPreviews(reviewData, metadata, authorName);
      setSocialPreviews(previews);

      // Track what was applied for user feedback
      const applied = [];

      // Auto-apply if in auto mode or if fields are empty
      if (autoMode || !metaTitle) {
        onMetaTitleChange(metadata.title);
        applied.push('title');
      }
      if (autoMode || !metaDescription) {
        onMetaDescriptionChange(metadata.description);
        applied.push('description');
      }
      if (autoMode || !focusKeyword) {
        onFocusKeywordChange(metadata.focusKeyword);
        applied.push('focus keyword');
      }

      // Log what was applied for debugging and update state
      if (applied.length > 0) {
        console.log(`[AutoSEO] Applied generated: ${applied.join(', ')}`);
        setLastApplied(applied);
        // Clear the indicator after 3 seconds
        setTimeout(() => setLastApplied([]), 3000);
      }
    } catch (error) {
      console.error('Error generating metadata:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [reviewData, autoMode, metaTitle, metaDescription, focusKeyword, onMetaTitleChange, onMetaDescriptionChange, onFocusKeywordChange]);

  // Analyze content when review content changes
  useEffect(() => {
    if (hasReviewContent) {
      try {
        const analysis = contentAnalyzer.analyzeContent(reviewData.reviewContentLexical!);
        setContentAnalysis(analysis);
      } catch (error) {
        console.error('Error analyzing content:', error);
      }
    }
  }, [reviewData.reviewContentLexical, hasReviewContent]);

  // Auto-generate metadata when review data changes (if auto mode is enabled)
  useEffect(() => {
    if (autoMode && reviewData.gameName && hasReviewContent) {
      generateMetadata();
    }
  }, [autoMode, reviewData.gameName, hasReviewContent, generateMetadata]);

  // Generate social previews when metadata changes
  useEffect(() => {
    if (reviewData.gameName && (metaTitle || metaDescription)) {
      try {
        const metadata = {
          title: metaTitle || reviewData.reviewTitle || `${reviewData.gameName} Review`,
          description: metaDescription || `Read our detailed review of ${reviewData.gameName}`,
          focusKeyword: focusKeyword || reviewData.gameName,
          socialTitle: metaTitle || reviewData.reviewTitle || `${reviewData.gameName} Review`,
          socialDescription: metaDescription || `Read our detailed review of ${reviewData.gameName}`,
          confidence: 85
        };

        const authorName = reviewData.authorName || 'CriticalPixel User';
        const previews = socialMetaGenerator.generateSocialPreviews(reviewData, metadata, authorName);
        setSocialPreviews(previews);
      } catch (error) {
        console.error('Error generating social previews:', error);
      }
    }
  }, [reviewData, metaTitle, metaDescription, focusKeyword]);

  // Apply generated suggestions
  const applySuggestion = (field: 'title' | 'description' | 'keyword', value: string) => {
    switch (field) {
      case 'title':
        onMetaTitleChange(value);
        break;
      case 'description':
        onMetaDescriptionChange(value);
        break;
      case 'keyword':
        onFocusKeywordChange(value);
        break;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with automation controls */}
      <div className="flex items-center justify-between">
        <div className="font-mono text-xs text-slate-500">
          <span className="text-violet-400">Enhanced</span>
          <span className="text-slate-600 mx-1">•</span>
          <span className="text-slate-500">SEO Optimization</span>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Auto mode toggle */}
          <Button
            onClick={() => setAutoMode(!autoMode)}
            variant="outline"
            size="sm"
            className={`border-slate-600 transition-all duration-200 ${
              autoMode
                ? 'bg-violet-500/20 border-violet-500/60 text-violet-300'
                : 'bg-slate-800/50 hover:bg-slate-700/50 text-slate-300'
            }`}
          >
            <Zap className="h-3 w-3 mr-1" />
            Auto
          </Button>

          {/* Generate button */}
          <Button
            onClick={generateMetadata}
            disabled={isGenerating || !reviewData.gameName}
            variant="outline"
            size="sm"
            className="border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white transition-all duration-200"
          >
            {isGenerating ? (
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <Wand2 className="h-3 w-3 mr-1" />
            )}
            Generate
          </Button>
        </div>
      </div>

      {/* Content Analysis Summary */}
      {contentAnalysis && (
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 rounded-md bg-slate-800/60">
              <Brain className="h-4 w-4 text-cyan-400" />
            </div>
            <div>
              <div className="font-mono text-sm text-slate-300">Content Analysis</div>
              <p className="text-slate-500 text-xs">
                {contentAnalysis.wordCount} words • {contentAnalysis.readingTime} min read
              </p>
            </div>
            <div className="ml-auto">
              <ConfidenceIndicator score={contentAnalysis.seoScore} />
            </div>
          </div>
          
          {contentAnalysis.gameAspects.length > 0 && (
            <div className="text-xs text-slate-400">
              <span className="text-slate-300">Covers:</span> {contentAnalysis.gameAspects.slice(0, 4).join(', ')}
              {contentAnalysis.gameAspects.length > 4 && ` +${contentAnalysis.gameAspects.length - 4} more`}
            </div>
          )}
        </div>
      )}

      {/* Auto-Applied Indicator */}
      {lastApplied.length > 0 && (
        <div className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-500/30 rounded-lg p-3 animate-in slide-in-from-top-2 duration-300">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-400" />
            <div className="font-mono text-sm text-green-300">
              Auto-applied: {lastApplied.join(', ')}
            </div>
          </div>
        </div>
      )}

      {/* Generated Metadata Preview */}
      {generatedMetadata && (
        <div className="bg-gradient-to-r from-violet-900/20 to-cyan-900/20 border border-violet-500/30 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-3">
            <Sparkles className="h-4 w-4 text-violet-400" />
            <div className="font-mono text-sm text-violet-300">AI Generated Suggestions</div>
            <div className="ml-auto">
              <ConfidenceIndicator score={generatedMetadata.confidence} />
            </div>
          </div>
          
          <div className="space-y-2 text-xs">
            <div>
              <span className="text-slate-400">Title:</span>
              <span className="text-slate-200 ml-2">{generatedMetadata.title}</span>
              <Button
                onClick={() => applySuggestion('title', generatedMetadata.title)}
                size="sm"
                variant="ghost"
                className="ml-2 h-5 px-2 text-xs text-violet-400 hover:text-violet-300"
              >
                Apply
              </Button>
            </div>
            <div>
              <span className="text-slate-400">Description:</span>
              <span className="text-slate-200 ml-2">{generatedMetadata.description}</span>
              <Button
                onClick={() => applySuggestion('description', generatedMetadata.description)}
                size="sm"
                variant="ghost"
                className="ml-2 h-5 px-2 text-xs text-violet-400 hover:text-violet-300"
              >
                Apply
              </Button>
            </div>
            <div>
              <span className="text-slate-400">Focus Keyword:</span>
              <span className="text-slate-200 ml-2">{generatedMetadata.focusKeyword}</span>
              <Button
                onClick={() => applySuggestion('keyword', generatedMetadata.focusKeyword)}
                size="sm"
                variant="ghost"
                className="ml-2 h-5 px-2 text-xs text-violet-400 hover:text-violet-300"
              >
                Apply
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Social Media Previews */}
      {socialPreviews.length > 0 && (
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4">
          <SocialMediaPreview previews={socialPreviews} />
        </div>
      )}

      {/* Content Cards */}
      <div className="space-y-4">

        {/* Meta Title Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <Target className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Meta Title</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The clickable title that appears in search results
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">SEARCH_TITLE</div>
                <Input
                  placeholder="Craft the perfect search-engine title (auto-fills from content title)"
                  value={metaTitle}
                  onChange={(e) => onMetaTitleChange(e.target.value)}
                  onFocus={handleMetaTitleFocus}
                  maxLength={60}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                />
                {metaTitle && (
                  <div className="absolute right-3 top-9 transform -translate-y-1/2">
                    {metaTitle.length <= 60 ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                )}
              </div>

              {metaTitle && (
                <CharacterCounter current={metaTitle.length} max={60} />
              )}

              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Eye className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      Search Result Preview
                    </div>
                    <p className="text-xs text-slate-400">
                      This appears as your clickable link in search results
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Meta Description Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <FileText className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Meta Description</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The preview text that appears below your title
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">DESCRIPTION_TEXT</div>
                <Textarea
                  placeholder="Write a compelling preview that makes people want to click and read your content..."
                  value={metaDescription}
                  onChange={(e) => onMetaDescriptionChange(e.target.value)}
                  maxLength={160}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm
                           min-h-[80px] resize-none pr-10"
                />
                {metaDescription && (
                  <div className="absolute right-3 top-9">
                    {metaDescription.length <= 160 ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                )}
              </div>

              {metaDescription && (
                <CharacterCounter current={metaDescription.length} max={160} />
              )}

              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Eye className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      Search Snippet Preview
                    </div>
                    <p className="text-xs text-slate-400">
                      This preview text appears below your title in search results
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Focus Keyword Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <Search className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Primary Keyword</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The main search term people will use to find your content
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">SEARCH_KEYWORD</div>
                <Input
                  placeholder="e.g., 'product review', 'tutorial guide', 'how to'"
                  value={focusKeyword}
                  onChange={(e) => onFocusKeywordChange(e.target.value)}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                />
                {focusKeyword && (
                  <div className="absolute right-3 top-9 transform -translate-y-1/2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                  </div>
                )}
              </div>

              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Search className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      SEO Optimization Target
                    </div>
                    <p className="text-xs text-slate-400">
                      The main term people will search for to find your content
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4">
        <div className="flex items-center space-x-2 min-w-0">
          <div className="w-2 h-2 bg-yellow-400/60 rounded-full animate-pulse flex-shrink-0" />
          <span className="text-sm text-slate-400/80 font-mono break-words">
            {hasAnyContent
              ? '<SEO optimization configured />'
              : '<Optional: Configure SEO settings />'
            }
          </span>
        </div>

        <div className="flex items-center space-x-3 flex-shrink-0">
          <Button
            onClick={handlePrevStep}
            className="review-continue-button review-continue-disabled"
          >
            <div className="review-button-content">
              <ArrowLeft className="review-button-arrow" />
              <span className="review-code-brackets">&lt;</span>
              Previous
              <span className="review-code-brackets">/&gt;</span>
            </div>
          </Button>
          <Button
            onClick={handleNextStep}
            className="review-continue-button review-continue-ready"
          >
            <div className="review-button-content">
              <span className="review-code-brackets">&lt;</span>
              Continue
              <span className="review-code-brackets">/&gt;</span>
              <ArrowRight className="review-button-arrow" />
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AutomatedSeoSection;
