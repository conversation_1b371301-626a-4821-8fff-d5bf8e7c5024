@import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@600;700;800&family=Lato+2:ital,wght@0,400;0,700;1,400&family=Lato:ital,wght@0,400;0,700;1,400&family=Press+Start+2P&family=Pixelify+Sans:wght@700&family=VT323&display=swap');

:root {
  --font-exo2: 'Exo 2', sans-serif;
  --font-lato: 'Lato', sans-serif;
  --font-lato2: 'Lato 2', sans-serif;
  --font-press-start-2p: 'Press Start 2P', cursive;
  --font-pixelify-sans: 'Pixelify Sans', sans-serif;
}

/* font-display: swap ensures text remains visible during font loading */
@font-face {
  font-family: 'Exo 2';
  font-display: swap;
}
@font-face {
  font-family: 'Lato';
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --popover: 0 0% 100%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 238 24% 12%;
    --foreground: 0 0% 98%; /* White for general text */

    --card-foreground: 0 0% 98%; /* White text on cards */
    --popover: 238 24% 15%; /* Darker popover */
    --popover-foreground: 0 0% 98%;

    --primary: 269 100% 71%; /* Neon Purple - for primary buttons, highlights */
    --primary-foreground: 0 0% 10%; /* Very dark text for good contrast on purple */

    --secondary: 238 24% 25%; /* Lighter charcoal for secondary buttons */
    --secondary-foreground: 183 100% 62%; /* Neon Cyan text on secondary */

    --card: 238 24% 18%; /* Deep Charcoal for cards */
    --accent: 183 100% 62%; /* Neon Cyan - for specific accents, links, glows */
    --accent-foreground: 0 0% 10%; /* Dark text on cyan accents */

    --muted: 238 24% 22%; /* Muted charcoal */
    --muted-foreground: 0 0% 60%; /* Grey muted text */

    --destructive: 0 70% 50%; /* More vibrant red for dark theme */
    --destructive-foreground: 0 0% 98%;

    --border: 238 24% 30%; /* Charcoal border */
    --input: 238 24% 15%; /* Dark input background */
    --ring: 183 100% 71%; /* Brighter Neon Cyan for focus rings */
    
    /* Chart colors reflecting palette */
    --chart-1: 269 100% 71%; /* Neon Purple */
    --chart-2: 183 100% 62%;  /* Neon Cyan */
    --chart-3: 269 100% 80%; /* Lighter Purple */
    --chart-4: 183 100% 75%; /* Lighter Cyan */
    --chart-5: 0 0% 70%; /* Grey */
  }
}

@layer base {
  :root {
    --bg-dimmer-opacity: 1;
  }

  * {
    @apply border-border;
  }
  body {
    /* Applied gradient directly, text-foreground for default text color */
    @apply text-foreground;
    background: linear-gradient(to bottom right, #1a202c, #35363A);
    background-attachment: fixed;
    font-family: var(--font-lato);
    position: relative;
  }

  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, var(--bg-dimmer-dark-opacity, 0));
    pointer-events: none;
    z-index: 1;
  }

  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, var(--bg-dimmer-light-opacity, 0));
    pointer-events: none;
    z-index: 1;
  }

  /* Ensure main content is above the dimmer overlay */
  main, nav, footer {
    position: relative;
    z-index: 2;
  }

  /* Tailwind CSS Prose styles for rendered review content */
  .prose {
    @apply text-foreground;
  }
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply text-primary;
  }
  .prose a {
    @apply text-accent hover:underline;
  }
  .prose strong {
    @apply text-foreground;
  }
  .prose blockquote {
    @apply border-l-4 border-accent pl-4 italic text-muted-foreground;
  }
  .prose code {
     @apply bg-muted text-sm font-mono px-1 py-0.5 rounded;
  }
  .prose pre {
    @apply bg-muted p-3 rounded-md font-mono text-sm my-4 block whitespace-pre-wrap overflow-x-auto;
  }
  .prose ul > li::marker {
    @apply text-accent;
  }
  .prose ol > li::marker {
    @apply text-accent;
  }
  .prose img {
    @apply rounded-md shadow-md my-4;
  }

  .dark .prose {
    @apply text-foreground;
  }
  .dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
    @apply text-primary; /* Or adjust for dark theme if needed */
  }
  .dark .prose a {
    @apply text-accent;
  }
  .dark .prose strong {
    @apply text-foreground;
  }
  .dark .prose blockquote {
    @apply border-accent text-muted-foreground;
  }
  .dark .prose code {
     @apply bg-muted text-sm;
  }
   .dark .prose pre {
    @apply bg-muted;
  }
}

/* Glow effect utility - toned down */
.glow-purple {
  box-shadow: 0 0 3px 0.5px rgba(179, 108, 255, 0.08), 0 0 5px 1px rgba(179, 108, 255, 0.06);
}
.glow-purple-sm {
  box-shadow: 0 0 2px 0.3px rgba(179, 108, 255, 0.06), 0 0 3px 0.8px rgba(179, 108, 255, 0.04);
}

.glow-cyan {
  box-shadow: 0 0 3px 0.5px rgba(61, 242, 255, 0.08), 0 0 5px 1px rgba(61, 242, 255, 0.06);
}

.text-glow-cyan {
  text-shadow: 0 0 1px rgba(61, 242, 255, 0.5), 0 0 3px rgba(61, 242, 255, 0.3);
}

/* Admin Layout Overrides */
.admin-layout ~ main,
.admin-layout + main,
body:has(.admin-layout) main {
  padding-top: 0 !important;
  margin-top: 0 !important;
}


/* Lexical Editor Specific Styles */
.editor-placeholder {
  @apply text-muted-foreground;
}

.editor-paragraph {
  @apply mb-2;
}

.editor-heading-h1 {
  @apply font-bold mb-4 mt-6 text-primary heading-1;
}

.editor-heading-h2 {
  @apply font-semibold mb-3 mt-5 text-primary heading-2;
}

.editor-heading-h3 {
  @apply font-semibold mb-2 mt-4 text-primary heading-3;
}

/* Typography CSS */
.heading-1 {
  font-family: var(--font-exo2);
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: -1px;
}
.heading-2 {
  font-family: var(--font-exo2);
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}
.heading-3 {
  font-family: var(--font-exo2);
  font-size: 1.5rem;
  font-weight: 600;
}
.heading-4 {
  font-family: var(--font-exo2);
  font-size: 1.25rem;
  font-weight: 600;
}

.editor-list-ol {
  @apply list-decimal list-inside my-2 pl-4;
}
.editor-list-ul {
  @apply list-disc list-inside my-2 pl-4;
}
.editor-listitem {
  @apply mb-1;
}
.editor-quote {
  @apply border-l-4 border-accent pl-4 italic my-4 text-muted-foreground;
}
.editor-link {
  @apply text-accent hover:underline cursor-pointer;
}
.editor-text-bold {
  @apply font-bold;
}
.editor-text-italic {
  @apply italic;
}
.editor-text-underline {
  @apply underline;
}
.editor-text-strikethrough {
  @apply line-through;
}
.editor-text-code {
  @apply bg-muted text-sm font-mono px-1 py-0.5 rounded;
}
.editor-code {
  @apply bg-muted p-3 rounded-md font-mono text-sm my-4 block whitespace-pre-wrap overflow-x-auto;
}

/* Add more specific styles for code highlighting if needed */
.editor-tokenComment { @apply text-muted-foreground; }
.editor-tokenPunctuation { @apply text-muted-foreground; }
.editor-tokenProperty { @apply text-pink-500; } /* Example color */
.editor-tokenSelector { @apply text-purple-400; } /* Example color */
.editor-tokenOperator { @apply text-accent; }
.editor-tokenAttr { @apply text-yellow-500; } /* Example color */
.editor-tokenVariable { @apply text-orange-400; } /* Example color */
.editor-tokenFunction { @apply text-blue-400; } /* Example color */

.editor-listitem-checked > svg,
.editor-listitem-unchecked > svg {
  @apply inline-block mr-2 align-middle;
}
.editor-listitem-checked .editor-text-strikethrough {
  @apply text-muted-foreground;
}

.ltr {
  text-align: left;
}
.rtl {
  text-align: right;
}

.editor-image {
  @apply max-w-full h-auto my-4 rounded-md shadow-md;
}

/* General Animation Keyframes */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: translateY(0); 
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

@keyframes glitch-effect {
  0%, 100% {
    text-shadow: none;
    transform: none;
  }
  20%, 60% {
    text-shadow: 1px 0 0 red, -1px 0 0 blue;
    transform: translateX(1px);
  }
  40%, 80% {
    text-shadow: -1px 0 0 red, 1px 0 0 blue;
    transform: translateX(-1px);
  }
}

@keyframes glitch1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 1px); }
  40% { transform: translate(2px, -1px); }
  60% { transform: translate(-1px, -1px); }
  80% { transform: translate(1px, 1px); }
}

@keyframes glitch2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(2px, 1px); }
  40% { transform: translate(-2px, -1px); }
  60% { transform: translate(1px, -1px); }
  80% { transform: translate(-1px, 1px); }
}

@keyframes dropdown-appear {
  0% {
    opacity: 0;
    transform: translateY(-10px) scaleY(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scaleY(1);
  }
}

@keyframes shimmer {
  0% { background-position: -300px 0; }
  100% { background-position: 300px 0; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Pixel art drop shadow utility */
.text-shadow-pixel-primary {
  text-shadow: 3px 3px 0px hsl(var(--primary));
}
.text-shadow-pixel-light {
   text-shadow: 2px 2px 0px rgba(241, 245, 249, 0.1);
}
.text-shadow-username-effect {
  text-shadow: 5px 4px 0px rgba(169,169,169,1);
}

/* Custom utility class for very small text */
.text-xxs {
  font-size: 0.65rem;
  line-height: 0.85rem;
}

.small-text {
  font-family: var(--font-lato2);
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.profile-username {
  font-family: var(--font-press-start-2p);
  font-weight: 300;
}

.profile-username:hover {
  animation: glitch-effect 0.5s linear infinite;
  cursor: pointer;
}

.button-label {
  font-family: var(--font-lato2);
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.caption {
  font-family: var(--font-lato2);
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.2px;
}

/* Modern Search Bar Styles */
.modern-search-bar input[type="text"],
.searchbar-input {
  border-radius: 0.375rem !important;
}
.searchbar-chip,
.user-search-result-item {
  border-radius: 0.375rem !important;
}

/* Utility classes */
@layer utilities {
  .filter-red {
    filter: sepia(100%) hue-rotate(-50deg) saturate(6);
  }
  
  .filter-blue {
    filter: sepia(100%) hue-rotate(90deg) saturate(6);
  }
}

@layer components {
  .animate-glitch-1 {
    animation: glitch1 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite both;
  }
  
  .animate-glitch-2 {
    animation: glitch2 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s infinite both;
  }
}

/* Hide scrollbar utility */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Custom focus styles for accessibility */
:focus-visible {
  outline: 2px solid rgba(139, 92, 246, 0.6);
  outline-offset: 2px;
}

/* Gradient text utility */
.gradient-text {
  background: linear-gradient(to right, #8e5cf6, #4cc4e7);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: bold;
}

/* Add these CSS variables to your main CSS file (globals.css or app.css) */

:root {
  /* Existing shadcn variables... keep all your current ones */
  
  /* 🚨 ADD THESE: Lexical editor specific variables */
  --review-text-primary: #f1f5f9;
  --review-text-secondary: #94a3b8;
  --review-bg-primary: rgba(15, 23, 42, 0.9);
  --review-bg-secondary: rgba(30, 41, 59, 0.8);
  --review-accent: rgba(139, 92, 246, 0.4);
  --review-border: rgba(139, 92, 246, 0.2);
}

/* Dark mode overrides if needed */
[data-theme="dark"] {
  --review-text-primary: #f1f5f9;
  --review-text-secondary: #94a3b8;
  --review-bg-primary: rgba(15, 23, 42, 0.9);
  --review-bg-secondary: rgba(30, 41, 59, 0.8);
  --review-accent: rgba(139, 92, 246, 0.4);
  --review-border: rgba(139, 92, 246, 0.2);
}

/* Admin-specific styles */
.admin-layout {
  /* Admin-specific global styles */
  --admin-primary: hsl(var(--primary));
  --admin-accent: hsl(var(--accent));
}

/* Admin-specific scrollbar styling */
.admin-layout ::-webkit-scrollbar {
  width: 6px;
}

.admin-layout ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.admin-layout ::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 3px;
}

.admin-layout ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Background Dimmer Slider Styles */
.dimmer-slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.dimmer-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #e2e8f0;
  cursor: pointer;
  border: 2px solid #475569;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.dimmer-slider::-webkit-slider-thumb:hover {
  background: #f1f5f9;
  border-color: #64748b;
  transform: scale(1.1);
}

.dimmer-slider::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #e2e8f0;
  cursor: pointer;
  border: 2px solid #475569;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.dimmer-slider::-moz-range-thumb:hover {
  background: #f1f5f9;
  border-color: #64748b;
  transform: scale(1.1);
}