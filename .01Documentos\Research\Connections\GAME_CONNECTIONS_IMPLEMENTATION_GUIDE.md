# Architecting and Implementing a Multi-Platform Game Ownership Verification System in Next.js

## Section 1: Foundational Architecture: A Backend-for-Frontend (BFF) Approach

To successfully integrate multiple, disparate third-party gaming APIs into a single, cohesive application, a robust and scalable architecture is paramount. The foundational architectural pattern selected for this project is the Backend-for-Frontend (BFF). This approach involves leveraging the server-side capabilities of Next.js to create a dedicated API layer that serves as an intermediary between the client-facing React application and the external gaming platform services. This section will justify the selection of the BFF pattern, detail its implementation using Next.js, propose a unified data model for consistency, and outline advanced strategies for caching and performance optimization. The extreme diversity in the target APIs—ranging from official and well-documented to unofficial and reverse-engineered—makes the BFF pattern not merely a best practice but a mission-critical necessity for security, maintainability, and performance.¹

### 1.1 The Case for a BFF: Security, Abstraction, and Performance

The BFF pattern positions the Next.js application to act as both the frontend rendering engine and a dedicated backend for that specific frontend.⁴ This architecture is exceptionally well-suited for aggregating data from numerous downstream services and tailoring it for a particular client experience, which is the core requirement of this project.⁷ The benefits can be categorized into three primary areas: security, abstraction, and performance.

**Security:** The most significant advantage of the BFF architecture is the enhancement of security. All sensitive credentials, including the Steam Web API Key, the Xbox Client Secret, and especially the user-provided PlayStation Network (PSN) npsso tokens, are stored and utilized exclusively on the server side within the BFF layer. These credentials are never exposed to the client's browser, mitigating the risk of theft through client-side attacks like Cross-Site Scripting (XSS).⁸ The BFF acts as a secure vault, handling all interactions with external APIs and shielding the frontend from the complexities and risks of direct authentication.

**Abstraction & Normalization:** Each gaming platform provides its data in a unique and often incompatible format. For instance, Steam, Xbox, and PSN have entirely different structures for representing a user's game library and playtime.¹⁰ A direct-to-frontend approach would force the client application to contain complex logic to handle each of these variations, leading to bloated, brittle, and difficult-to-maintain code. The BFF layer assumes the responsibility of consuming these heterogeneous responses and normalizing them into a single, consistent domain model.⁷ This abstraction means the frontend components only ever need to interact with one predictable data structure, drastically simplifying client-side development and making the application more resilient to changes in the external APIs. The existence of commercial "Unified API" platforms for gaming and other industries validates this approach; in essence, this project involves building a private, purpose-built Unified API tailored to the application's specific needs.¹¹

**Performance:** By centralizing data fetching, the BFF enables powerful server-side optimization strategies. A caching layer, using a technology like Redis, can be implemented to store the results of frequent or expensive API calls, such as fetching a user's entire game library.¹⁴ This not only provides a significantly faster experience for the user on subsequent visits but also helps in managing and respecting the rate limits imposed by the external platforms. For example, the Steam Web API has a default limit of 100,000 requests per day, which a caching strategy can help to avoid exceeding.¹⁵

### 1.2 Structuring the BFF: API Route Handlers and Service Layers

The implementation of the BFF will be built upon the modern, recommended features of the Next.js App Router. Specifically, we will use Route Handlers, which are files named `route.ts` (or `.js`) placed within the `app/api/` directory structure.⁴ This file-based routing convention allows for the creation of distinct API endpoints that can handle various HTTP methods (GET, POST, etc.) from within a single file.

To ensure the architecture is modular and maintainable, a feature-based project structure is recommended.¹⁶ A dedicated directory, such as `/lib/services/`, will be created to house the logic for each external platform. This directory will contain distinct modules like `steam.service.ts`, `xbox.service.ts`, and `psn.service.ts`. This approach adheres to the principle of Separation of Concerns, where each module is responsible for a single, well-defined part of the system's functionality.¹⁰

Each service module will encapsulate all the logic required to interact with its corresponding platform:
- **Authentication:** Handling the specific auth flow, including token exchanges and refreshes.
- **Data Fetching:** Making the actual API calls to retrieve user data (e.g., game libraries, playtime).
- **Data Transformation:** Normalizing the raw API response into the application's unified data schema.

The Next.js Route Handlers in `app/api/` will then act as thin controllers. Their role is to handle incoming requests, perform necessary validation (e.g., checking for a user session), call the appropriate function from the service layer, and return the final, normalized response to the client. This layered architecture makes the system easier to test, debug, and extend in the future.

### 1.3 A Unified Data Schema for Cross-Platform Game Data

The cornerstone of the BFF's abstraction capability is the definition of a unified data schema. This schema serves as the "contract" between the BFF and the frontend, ensuring that no matter which platform the data originates from, the React components will receive it in a consistent and predictable format.

A TypeScript interface, `VerifiedGame`, will be defined to represent a single game entry in a user's library. This interface will standardize the disparate fields from each platform's API.

```typescript
// /interfaces/VerifiedGame.ts
export interface VerifiedGame {
  platform: 'steam' | 'xbox' | 'psn' | 'gog';
  gameId: string; // Platform-specific game identifier (e.g., Steam AppID)
  gameName: string;
  gameImageUrl: string;
  playtimeMinutes: number | null; // Standardized to minutes
  lastPlayed: Date | null;
  isOwned: boolean;
  dlc: {
    id: string;
    name: string;
    isOwned: boolean;
  }[];
}
```

This unified schema forces the normalization logic to reside within each platform's service module in the BFF. For example, the Steam service will be responsible for converting `playtime_forever` (which is in minutes) into the `playtimeMinutes` field.¹⁸ The PSN service will need to parse the ISO 8601 duration string from its `playDuration` field (e.g., "PT228H56M33S") into total minutes.¹⁹ This ensures the frontend logic remains simple and agnostic of the data source.

To provide a clear overview of the capabilities and challenges associated with each platform, the following matrix summarizes the findings from the initial research phase. This at-a-glance summary is crucial for project planning and setting realistic expectations for development effort and feature parity across platforms.

| Feature         | Steam                      | Xbox                      | PlayStation (PSN)        | GOG                   | Epic Games          |
|-----------------|----------------------------|---------------------------|--------------------------|-----------------------|---------------------|
| **Official API**    | Yes (Web API)              | Yes (REST API)            | No (Unofficial)          | Yes (Galaxy API)      | Yes (EOS)           |
| **Auth Method**     | OpenID 2.0                 | OAuth 2.0 (XSTS)          | npsso Token              | OAuth 2.0             | OAuth 2.0           |
| **Game Library**    | Yes (GetOwnedGames)        | Yes (Inventory Service)   | Yes (getUserPlayedGames) | Yes (get_owned_games) | No                  |
| **Playtime Data**   | Yes                        | Yes (via Title History)   | Yes (playDuration)       | Yes                   | N/A                 |
| **DLC Ownership**   | No (Workaround needed)     | Yes (Inventory Service)   | Yes (Included in games list) | Yes                 | N/A                 |
| **Key Challenge**   | DLC data requires scraping | Complex XSTS token auth   | Unofficial, manual token | N/A                   | No library access   |
_Sources: 1_

### 1.4 Advanced Caching and Rate-Limiting Strategies

Fetching a user's complete game library, especially from multiple platforms, can be a slow and network-intensive operation. To mitigate this and improve user experience, a robust caching strategy is essential. A server-side cache, such as Redis, is highly recommended for this purpose.¹⁴

The flow would be as follows:
1.  A user initiates a request to sync their library from a platform (e.g., Steam).
2.  The BFF's API route receives the request and calls the corresponding service module (e.g., `steam.service.ts`).
3.  The service module first checks the Redis cache for an entry corresponding to that user and platform (e.g., `cache.get('steam:user_id')`).
4.  If a valid, non-expired cache entry exists, it is returned immediately, providing a near-instant response.
5.  If no cache entry is found (a "cache miss"), the service proceeds to call the external Steam API.
6.  Upon receiving the data from Steam, the service normalizes it into the `VerifiedGame` schema and stores this result in the Redis cache with a reasonable Time-To-Live (TTL), such as 24 hours.
7.  The fresh data is then returned to the user.

This caching mechanism significantly reduces the number of calls to external APIs, which not only improves performance but also prevents the application from hitting API rate limits, such as Steam's daily request cap.¹⁵

In addition to caching external responses, it is wise to implement rate-limiting on the BFF's own API endpoints. This protects the application from abuse or denial-of-service attacks, ensuring fair usage for all users. Libraries like `@upstash/ratelimit` can be easily integrated with Next.js Route Handlers to enforce limits (e.g., 10 requests per 10 seconds per user).

To support this architecture, a well-defined database schema is required to persist user information, their platform connections, and the verified game data.

| Table Name      | Column              | Data Type    | Description                               |
|-----------------|---------------------|--------------|-------------------------------------------|
| **Users**         | `id`                  | `VARCHAR(255)` | Primary Key, from NextAuth.js             |
|                 | `name`                | `VARCHAR(255)` | User's display name                       |
|                 | `email`               | `VARCHAR(255)` | User's email, unique                      |
|                 | `image`               | `VARCHAR(255)` | URL for user's profile image              |
| **Accounts**      | `userId`              | `VARCHAR(255)` | Foreign Key to Users.id                   |
|                 | `type`                | `VARCHAR(255)` | e.g., 'oauth', 'credentials'              |
|                 | `provider`            | `VARCHAR(255)` | e.g., 'steam', 'xbox', 'psn'              |
|                 | `providerAccountId`   | `VARCHAR(255)` | User's ID on the provider's platform      |
|                 | `refresh_token`       | `TEXT`         | Must be encrypted at rest                 |
|                 | `access_token`        | `TEXT`         | Must be encrypted at rest                 |
|                 | `expires_at`          | `INT`          | Expiration timestamp for the access token |
| **Connections**   | `id`                  | `VARCHAR(255)` | Primary Key, UUID                         |
|                 | `userId`              | `VARCHAR(255)` | Foreign Key to Users.id                   |
|                 | `platform`            | `VARCHAR(255)` | 'steam', 'xbox', 'psn', etc.              |
|                 | `platformUsername`    | `VARCHAR(255)` | User's name on the platform               |
|                 | `lastSyncAt`          | `DATETIME`     | Timestamp of the last successful sync     |
|                 | `syncStatus`          | `VARCHAR(255)` | e.g., 'SUCCESS', 'FAILED', 'PENDING'      |
| **VerifiedGames** | `id`                  | `VARCHAR(255)` | Primary Key, UUID                         |
|                 | `connectionId`        | `VARCHAR(255)` | Foreign Key to Connections.id             |
|                 | `gameIdentifier`      | `VARCHAR(255)` | Platform-specific ID for the game         |
|                 | `gameName`            | `VARCHAR(255)` | Normalized name of the game               |
|                 | `playtimeMinutes`     | `INT`          | Total playtime in minutes                 |
|                 | `lastPlayed`          | `DATETIME`     | Timestamp of the last play session        |
|                 | `isOwned`             | `BOOLEAN`      | Ownership status                          |

## Section 2: Centralized Authentication with NextAuth.js

A robust and flexible authentication system is the linchpin of the connections feature. NextAuth.js (now Auth.js) is the ideal library for this task, providing a comprehensive solution for handling complex authentication flows from multiple providers within a Next.js application.²² This section details the configuration of NextAuth.js, the critical process of extending its default behavior to store platform-specific data, and the paramount security practices for handling sensitive user tokens. The `callbacks` object within NextAuth.js is the central nervous system of this entire integration, transforming a generic authentication library into a specialized "Gaming Identity" management platform.

### 2.1 Configuring NextAuth.js for a Multi-Provider Environment

The initial setup involves integrating NextAuth.js into the project's structure. This begins with installing the necessary package and establishing the core authentication endpoint.

- **Installation:** The first step is to add NextAuth.js to the project's dependencies:
  ```bash
  npm install next-auth
  ```

- **API Route Handler:** A dynamic route handler must be created at `app/api/auth/[...nextauth]/route.ts`. This file will export the NextAuth.js handler and contain all global configurations for the authentication system.²²

- **Provider Configuration:** The core of the setup lies within the `AuthOptions` object inside the route handler. The `providers` array will be populated with the configurations for each gaming platform. This will be a mix of pre-built, community-maintained, and custom-built providers to accommodate the unique authentication schemes of Steam, Xbox, and PSN.²³

- **Session Provider:** To make session data accessible to client-side React components, the application's root layout must be wrapped with the `<SessionProvider>`. As this is a client-side context provider, it must be placed within a "use client" boundary component, not directly in the root `layout.tsx` file.²²

A high-level configuration summary illustrates how these providers coexist within the `AuthOptions` object. This centralized view is the most direct way to translate the section's theory into a single, actionable code artifact.

**Table 3: NextAuth.js Provider Configuration Summary**
```typescript
// /app/api/auth/[...nextauth]/route.ts
import NextAuth, { AuthOptions } from "next-auth";
import { steamProvider } from "@/lib/auth/providers/steamProvider";
import { xboxProvider } from "@/lib/auth/providers/xboxProvider";
import { psnProvider } from "@/lib/auth/providers/psnProvider";

export const authOptions: AuthOptions = {
  providers: [
    steamProvider,
    xboxProvider,
    psnProvider,
  ],
  callbacks: {
    // Detailed implementation in section 2.2
    async jwt({ token, account, profile }) {
      // Logic to persist platform tokens and IDs to the JWT
    },
    async session({ session, token }) {
      // Logic to expose necessary data from JWT to client session
    },
  },
  pages: {
    signIn: '/auth/signin', // Custom sign-in page
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
```
_Sources: 23_

### 2.2 Extending the Session and JWT: Storing Platform-Specific Data

The default NextAuth.js session contains minimal user information (name, email, image). To power the game verification feature, this session must be augmented with platform-specific identifiers and tokens. This is achieved using the `callbacks` object in `AuthOptions`, specifically the `jwt` and `session` callbacks.²² These callbacks are the critical junction where a generic authentication flow is transformed into a specialized gaming identity management system.

**The `jwt` Callback:**
This callback is executed whenever a JSON Web Token (JWT) is created or updated, which happens upon sign-in or session access. It's the ideal place to persist sensitive, server-side information.
- When a user successfully authenticates with a provider, the `account` object passed to this callback contains the crucial tokens and identifiers from that provider.
- The logic will inspect `account.provider` to determine which platform the user connected with.
- Based on the provider, it will add the relevant data to the `token` object. For example:
  - For Steam, it will extract the 64-bit SteamID from `account.providerAccountId` and store it as `token.steamId`.
  - For Xbox, it will store the XSTS token and the user's XUID.
  - For PSN, it will store the access and refresh tokens obtained from the credential flow.
- This augmented JWT is then encrypted and stored in a secure, HTTP-Only cookie.

```typescript
// Inside authOptions callbacks
async jwt({ token, account, profile }) {
  if (account) {
    switch (account.provider) {
      case 'steam':
        // The 'sub' property of the token usually holds the provider's user ID
        token.steamId = token.sub; 
        break;
      case 'xbox':
        token.xstsToken = account.access_token;
        token.xuid = profile.xuid; // Assuming profile contains xuid
        break;
      case 'psn':
        token.psnAccessToken = account.access_token;
        token.psnRefreshToken = account.refresh_token;
        token.psnAccountId = profile.accountId; // Assuming profile contains accountId
        break;
    }
  }
  return token;
}
```
_Source: 26_

**The `session` Callback:**
This callback is executed whenever a client-side session is accessed (e.g., via the `useSession` hook). Its purpose is to control which information from the server-side JWT is exposed to the client.
- This is a crucial security boundary. Sensitive tokens like the PSN access token or the Xbox XSTS token should **never** be passed to the client.
- The logic will transfer safe, necessary identifiers from the `token` object to the `session.user` object.
- For example, it can safely expose `token.steamId` as `session.user.steamId`, allowing the frontend to know the user's Steam identity without having access to any secrets.

```typescript
// Inside authOptions callbacks
async session({ session, token }) {
  // Expose non-sensitive identifiers to the client
  if (token.steamId) {
    session.user.steamId = token.steamId;
  }
  if (token.xuid) {
    session.user.gamertag = token.gamertag; // Assuming gamertag is also in the token
  }
  if (token.psnAccountId) {
    session.user.psnOnlineId = token.psnOnlineId; // Assuming onlineId is in the token
  }
  
  // IMPORTANT: Do NOT expose sensitive tokens like psnAccessToken or xstsToken here.
  return session;
}
```
_Source: 22_

### 2.3 A Deep Dive on Secure Token Handling and Storage

The security model for this application is complex, particularly due to the PSN connection. While Steam and Xbox use standard OAuth flows where the user trusts our application with limited-scope tokens, the PSN flow requires the user to trust our application with a password-equivalent secret (npsso).²⁷ This inverted trust model places a significant security burden on the application and necessitates stringent security practices.

- **Environment Variables:** All application-level secrets, such as the `NEXTAUTH_SECRET`, Steam API Key, and Xbox Client Secret, must be stored as environment variables. The `.env.local` file should be used for local development and must be included in `.gitignore` to prevent accidental commits.²⁹ In production, these variables must be configured securely in the hosting provider's environment.
- **Treat Tokens as Passwords:** This principle applies universally but is especially critical for the user-provided PSN npsso token.²⁹ This token must be handled with the utmost care:
    - It should only ever be transmitted from the client to the server over an encrypted HTTPS connection.
    - It should be used immediately on the server to exchange for short-lived access and refresh tokens.
    - The npsso token itself should **not** be stored in the database after the initial exchange. Only the resulting `access_token` and `refresh_token` should be persisted.
- **Encryption at Rest:** All sensitive tokens stored in the database (specifically in the `Accounts` table), including `access_token` and `refresh_token` for all platforms, must be encrypted at rest. This means that even if an attacker gains access to a database backup, they cannot use the tokens without the encryption key. A strong, symmetric encryption algorithm like AES-256-GCM should be used. The encryption key itself must be stored securely as a high-priority environment variable.
- **HTTP-Only Cookies for Session Management:** By default, NextAuth.js uses secure, HTTP-Only cookies to manage user sessions. This is a critical security feature that must be maintained. Storing session tokens (JWTs) in client-side storage like `localStorage` is highly discouraged, as it makes them vulnerable to being stolen by malicious scripts via XSS attacks. HTTP-Only cookies are inaccessible to JavaScript running in the browser, providing a strong layer of protection.³⁰
- **Token Expiration and Rotation:** The architecture must respect and leverage token lifecycles. Access tokens are designed to be short-lived (often expiring in an hour). The application's service layer must include logic to handle expired tokens. For platforms that provide refresh tokens (like PSN), the service should use the refresh token to silently obtain a new access token without requiring user re-authentication.³¹ This provides a seamless user experience while maintaining a high level of security.

## Section 3: Implementation Guide: Steam Connector

This section provides a comprehensive, end-to-end guide for integrating Steam game ownership verification. It covers Steam's unique OpenID 2.0 authentication flow, details the retrieval of game and playtime data using the official Web API, and presents a secure, server-side workaround for the critical challenge of verifying DLC ownership. The primary technical challenge with Steam is that its official API deliberately omits DLC ownership data, necessitating the use of a non-public endpoint that our BFF architecture is uniquely positioned to handle securely.³³

### 3.1 Authentication: Integrating the @hyperplay/next-auth-steam Provider

Steam's authentication system does not use the modern OAuth 2.0 or OpenID Connect (OIDC) standards. Instead, it relies on the legacy OpenID 2.0 protocol.³⁵ Implementing this from scratch is complex and error-prone. Therefore, the recommended approach is to use a specialized, community-maintained NextAuth.js provider, `@hyperplay/next-auth-steam`, which abstracts these complexities.²⁴

**Step 1: Obtain a Steam Web API Key**
Before any integration can begin, a Steam Web API key is required. This key authenticates the application's server-side requests to Steam's APIs.
1. Navigate to the Steam Community Developer page: https://steamcommunity.com/dev/apikey.³⁷
2. Log in with a valid Steam account.
3. Fill out the domain name for the application and agree to the API Terms of Use.
4. Steam will generate a 32-character alphanumeric API key. This key is a secret and must be stored securely in the application's environment variables (e.g., `STEAM_API_KEY`).

**Step 2: Install and Configure the Provider**
Install the package using npm:
```bash
npm install @hyperplay/next-auth-steam
```
Next, create a provider configuration file (e.g., `/lib/auth/providers/steamProvider.ts`) and add it to the `providers` array in the main `authOptions`.

```typescript
// /lib/auth/providers/steamProvider.ts
import SteamProvider from "@hyperplay/next-auth-steam";
import { NextApiRequest } from "next";

export const steamProvider = (req: NextApiRequest) => {
  return SteamProvider(req, {
    clientSecret: process.env.STEAM_API_KEY!,
    callbackUrl: `${process.env.NEXTAUTH_URL}/api/auth/callback/steam`,
  });
};

// /app/api/auth/[...nextauth]/route.ts
//... imports
import { steamProvider } from "@/lib/auth/providers/steamProvider";

export const authOptions: AuthOptions = {
  providers: [
    // ... other providers
  ],
  //...
};
```
_Source: 24_

**Step 3: Extracting the SteamID into the Session**
To make server-side API calls on behalf of the user, their unique 64-bit SteamID must be available. This is accomplished by using the `jwt` and `session` callbacks in `authOptions`. The Steam provider conveniently places the user's profile information, including the SteamID, into the `profile` object during the sign-in process.

```typescript
// Inside authOptions callbacks
callbacks: {
  async jwt({ token, account, profile }) {
    if (account?.provider === "steam" && profile) {
      // The profile object from the steam provider contains the user's steam data
      token.steam = profile;
      // The user's 64-bit SteamID is in profile.steamid
      token.steamId = profile.steamid;
    }
    return token;
  },
  async session({ session, token }) {
    // Expose the steam profile data and steamId to the client-side session
    if (token.steam) {
      session.user.steam = token.steam;
      session.user.steamId = token.steamId;
    }
    return session;
  },
}
```
_Sources: 24_

### 3.2 Data Retrieval: Mastering the IPlayerService/GetOwnedGames Endpoint

The primary method for fetching a user's game library is the `GetOwnedGames` method within the `IPlayerService` interface of the Steam Web API.
- **Endpoint:** `GET http://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/`.¹⁸
- **Key Parameters:**
    - `key`: The 32-character Steam Web API key.
    - `steamid`: The 64-bit SteamID of the user whose library is being requested.
    - `include_appinfo`: A boolean (`true`) to include the game's name and image URLs in the response. This is highly recommended to avoid making subsequent API calls for metadata.
    - `include_played_free_games`: A boolean (`true`) to include free-to-play games that the user has played.
- **Response Structure:** The API returns a JSON object. The relevant data is in `response.games`, which is an array of game objects. Each game object contains:
    - `appid`: The unique numeric identifier for the game.
    - `name`: The human-readable name of the game.
    - `playtime_forever`: The total number of minutes the user has played the game on record.
    - `img_icon_url` and `img_logo_url`: URLs for game images.

**Handling Private Profiles:** A significant limitation of this endpoint is its dependency on user privacy settings. If a user's profile or game details are set to "Private" in their Steam settings, this API call will fail or return an empty list.³⁸ The application's UI must handle this case gracefully. It should detect the empty response and display a message prompting the user to adjust their Steam privacy settings to "Public" for their "Game details" to allow the sync to work.

### 3.3 The DLC Ownership Challenge: A Secure Workaround

The most critical limitation of the official `IPlayerService/GetOwnedGames` API is that it does not return any information about owned Downloadable Content (DLC).³³ For a feature centered on accurate game ownership validation, this is a major deficiency.

The only known and widely used workaround is to access an endpoint used by the Steam store itself: `https://store.steampowered.com/dynamicstore/userdata/`. This is **not** an official, public API and is subject to change without notice.³⁴ It returns a large JSON object containing detailed user data, including arrays named `rgOwnedApps` and `rgOwnedPackages`. These arrays contain the appids for all owned items, including base games and DLCs.³⁹

Implementing this workaround securely is where the BFF architecture becomes indispensable. This endpoint requires the user to be actively logged into the Steam store. A direct client-side call is not feasible or secure. The correct implementation is as follows:
1. The user authenticates with the application via the Steam NextAuth.js provider.
2. The application's frontend triggers a sync via a call to a BFF endpoint (e.g., `POST /api/sync/steam`).
3. The BFF endpoint, running on the server, receives this request. It uses the user's session to retrieve their credentials.
4. The server-side Steam service then makes a proxied request to the `dynamicstore/userdata/` endpoint, forwarding the user's authentication cookies.
5. The BFF receives the JSON response, parses it on the server to extract the list of owned DLC appids, and merges this information with the data from the official `GetOwnedGames` API.

This server-side proxying approach ensures that the user's session cookies are never exposed to the client and abstracts the fragile nature of this unofficial endpoint away from the frontend code.

### 3.4 Full Implementation: A Code Walkthrough for the Steam Service Module

The following provides a conceptual structure for the `steam.service.ts` module, which encapsulates the logic for fetching and normalizing a user's full Steam library.

```typescript
// /lib/services/steam.service.ts
import { VerifiedGame } from '@/interfaces/VerifiedGame';

const STEAM_API_KEY = process.env.STEAM_API_KEY;

interface SteamGame {
  appid: number;
  name: string;
  playtime_forever: number;
  img_icon_url: string;
  //... other fields
}

export async function getSteamLibrary(steamId: string): Promise<VerifiedGame[]> {
  try {
    // Step 1: Fetch base games from the official API
    const baseGamesUrl = `http://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/?key=${STEAM_API_KEY}&steamid=${steamId}&format=json&include_appinfo=true`;
    const baseGamesResponse = await fetch(baseGamesUrl);
    if (!baseGamesResponse.ok) {
      // Handle private profile or other API errors
      if (baseGamesResponse.status === 403 || baseGamesResponse.status === 401) {
        throw new Error("Steam profile is private or API key is invalid.");
      }
      throw new Error("Failed to fetch Steam games.");
    }
    const baseGamesData = await baseGamesResponse.json();
    const ownedGames: SteamGame[] = baseGamesData.response.games || [];

    // Step 2: Fetch DLC data via the unofficial endpoint
    // This requires a secure, server-side proxy request using the user's session cookies.
    // The implementation of this proxy is complex and depends on how session cookies are managed.
    // For this example, we assume `fetchDlcData` is a function that handles this.
    // const ownedDlcAppIds: number[] = await fetchDlcData(userSession);
    const ownedDlcAppIds: number[] = []; // Placeholder

    // Step 3: Combine and normalize the data
    const allOwnedAppIds = new Set([...ownedGames.map(g => g.appid), ...ownedDlcAppIds]);

    const verifiedGames: VerifiedGame[] = ownedGames.map((game) => ({
      platform: 'steam',
      gameId: game.appid.toString(),
      gameName: game.name,
      gameImageUrl: `https://media.steampowered.com/steamcommunity/public/images/apps/${game.appid}/${game.img_icon_url}.jpg`,
      playtimeMinutes: game.playtime_forever,
      lastPlayed: null, // Steam API does not provide this per game in this call
      isOwned: true,
      dlc: [], // This would be populated by cross-referencing allOwnedAppIds with a master list of DLCs for each game
    }));

    // Further logic to identify which appIds are DLCs and attach them to their base game object
    //...

    return verifiedGames;

  } catch (error) {
    console.error("Error in getSteamLibrary:", error);
    // Return empty array or re-throw a custom error for the API route to handle
    return [];
  }
}
```

## Section 4: Implementation Guide: Xbox Live Connector

Integrating with Xbox Live presents a significant technical challenge due to the complexity and fragmented documentation of its authentication systems. Unlike Steam's straightforward (if dated) approach, Xbox requires developers to navigate the Microsoft Entra ID ecosystem and orchestrate a multi-step token exchange to generate a valid Xbox Security Token Service (XSTS) token. This section provides a clear, step-by-step guide to demystify this process, from initial application registration to successfully fetching a user's game ownership data from the correct service endpoint.

### 4.1 Authentication: Navigating Microsoft Entra ID and the XSTS Token Flow

The journey to authenticating an Xbox Live user begins within Microsoft's identity platform, formerly Azure Active Directory.

**Step 1: Application Registration in Microsoft Entra ID**
A new application registration is required to establish a trust relationship between the application and the Microsoft identity platform.⁴⁰
1.  Navigate to the Microsoft Entra admin center.
2.  Go to `Identity > Applications > App registrations` and select `New registration`.⁴⁰
3.  **Name:** Provide a descriptive name for the application.
4.  **Supported account types:** This is a critical setting. Select "Accounts in any organizational directory (Any Microsoft Entra ID tenant - Multitenant) and personal Microsoft accounts (e.g. Skype, Xbox, Live)". This ensures that any user with a personal Microsoft account, which all Xbox Live accounts are, can use the application.⁴⁰
5.  **Redirect URI:** Under `Platform configurations`, select "Web" and enter the NextAuth.js callback URL. This will be `https://<your-domain>/api/auth/callback/xbox`.⁴⁰
6.  **Credentials:** Navigate to `Certificates & secrets`, select `Client secrets`, and create a `New client secret`. Copy this secret value immediately; it will not be shown again. This value must be stored securely as an environment variable (e.g., `XBOX_CLIENT_SECRET`).⁴⁰
7.  The **Application (client) ID** from the app's overview page should also be stored as an environment variable (e.g., `XBOX_CLIENT_ID`).

**Step 2: The Multi-Step XSTS Token Exchange**
Obtaining a usable XSTS token is not a single request but a chain of token exchanges. The entire flow must be orchestrated by the server-side BFF.⁴²
1.  **Authorization Code:** The user is redirected to the Microsoft login page. After successful authentication and consent, Microsoft redirects back to the application's callback URL with an `authorization_code`.
2.  **RPS Token:** The BFF exchanges this `authorization_code` (along with the client ID and secret) at the `https://login.live.com/oauth20_token.srf` endpoint to get a Microsoft Account access token, often called an RPS token.
3.  **Xbox User Token:** The RPS token is then sent to `https://user.auth.xboxlive.com/user/authenticate`. This request authenticates the user with the Xbox Live service and returns an Xbox User Token.
4.  **XSTS Token:** Finally, the User Token is sent to `https://xsts.auth.xboxlive.com/xsts/authorize`. This endpoint returns the final XSTS token, which is required to call protected Xbox Live APIs.

This entire sequence is what makes a custom OAuth provider necessary.

### 4.2 Building a Custom NextAuth.js OAuth Provider for Xbox Live

Due to the non-standard, multi-step token exchange, a pre-built NextAuth.js provider for Xbox Live does not exist. The solution is to create a custom provider by defining a configuration object with custom handlers for the token exchange process.²⁵

The custom provider configuration will live in a file like `/lib/auth/providers/xboxProvider.ts`. It will specify the authorization endpoint but will require a custom token handler to perform the complex exchange flow described in 4.1. The `profile` callback will be responsible for decoding the final XSTS token (which is a JWT) to extract user information like their XUID (Xbox User ID) and Gamertag.⁴⁶

```typescript
// /lib/auth/providers/xboxProvider.ts (Conceptual)
import { OAuthConfig, OAuthUserConfig } from "next-auth/providers";

export function xboxProvider(options: OAuthUserConfig<any>): OAuthConfig<any> {
  return {
    id: "xbox",
    name: "Xbox",
    type: "oauth",
    clientId: options.clientId,
    clientSecret: options.clientSecret,
    authorization: {
      url: "https://login.live.com/oauth20_authorize.srf",
      params: {
        scope: "XboxLive.signin XboxLive.offline_access",
        response_type: "code",
      },
    },
    token: {
      url: "https://login.live.com/oauth20_token.srf",
      // A custom request handler is needed here to perform the full XSTS exchange
      async request(context) {
        // 1. Standard OAuth2 token request to get RPS token
        const rpsResponse = await fetch(/*... */);
        const rpsTokens = await rpsResponse.json();

        // 2. Exchange RPS token for Xbox User Token
        const userTokenResponse = await fetch("https://user.auth.xboxlive.com/user/authenticate", /*... */);
        const userTokenData = await userTokenResponse.json();

        // 3. Exchange User Token for XSTS Token
        const xstsTokenResponse = await fetch("https://xsts.auth.xboxlive.com/xsts/authorize", /*... */);
        const xstsTokenData = await xstsTokenResponse.json();
        
        // Return a TokenSet compatible object for NextAuth.js
        return { tokens: { access_token: xstsTokenData.Token, ...xstsTokenData } };
      },
    },
    userinfo: {
      // Custom userinfo handler to extract data from the XSTS token
      async request(context) {
        const xstsToken = context.tokens.access_token as string;
        // Decode the JWT to get claims
        const claims = JSON.parse(Buffer.from(xstsToken.split('.')[1], 'base64').toString());
        const xui = claims.xui[0];
        return {
          sub: xui.xid, // XUID
          xuid: xui.xid,
          gamertag: xui.gtg,
          //... other claims
        };
      }
    },
    profile(profile) {
      return {
        id: profile.xuid,
        name: profile.gamertag,
        image: null, // Profile image requires another API call
        ...profile
      };
    },
   ...options,
  };
}
```

### 4.3 Data Retrieval: Interfacing with the Xbox Inventory Service

A common mistake when trying to fetch a user's game library is using the achievements endpoint. The endpoint at `https://achievements.xboxlive.com/users/xuid({XUID})/titlehistory` is insufficient because it only returns games for which the user has unlocked at least one achievement, leading to an incomplete library list.⁴⁷

The correct and definitive method for checking game ownership is the **Xbox Inventory service**.⁴⁸ This service provides a complete list of all digital entitlements associated with a user's account, including base games and DLCs.

A critical and poorly documented requirement for using this service is the concept of a "relying party". Most Xbox Live API calls use an XSTS token generated for the default relying party `http://xboxlive.com`. However, the Inventory service will reject such a token. It requires an XSTS token that has been specifically generated for the audience `https://licensing.xboxlive.com`.⁴⁸ This detail is often discovered not in primary documentation but in developer forums and GitHub issue discussions, highlighting the fractured nature of the available information.

The custom token exchange logic within the BFF must be designed to request a token with this specific relying party when it intends to call the inventory service. The endpoint for the inventory service is `GET https://inventory.xboxlive.com/users/me/inventory`.⁴⁹ The request must include the specialized XSTS token in the `Authorization` header.

### 4.4 Full Implementation: A Code Walkthrough for the Xbox Service Module

This section provides the conceptual code for the `xbox.service.ts` module. This service will be responsible for taking a valid XSTS token (for the correct relying party) and fetching the user's library.

```typescript
// /lib/services/xbox.service.ts
import { VerifiedGame } from '@/interfaces/VerifiedGame';

// This function assumes a valid XSTS token for the licensing.xboxlive.com relying party has been obtained.
export async function getXboxLibrary(xstsToken: string): Promise<VerifiedGame[]> {
  const inventoryUrl = 'https://inventory.xboxlive.com/users/me/inventory';
  const headers = {
    'Authorization': xstsToken,
    'x-xbl-contract-version': '2', // Required contract version for this service
    'Content-Type': 'application/json',
  };

  try {
    const response = await fetch(inventoryUrl, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch Xbox inventory: ${response.statusText}`);
    }

    const data = await response.json();
    const items = data.items || [];

    const verifiedGames: VerifiedGame[] = items.map((item: any) => ({
      platform: 'xbox',
      gameId: item.productId, // The unique product identifier
      gameName: item.title,
      gameImageUrl: item.images.find((img: any) => img.purpose === 'Tile')?.uri || '',
      playtimeMinutes: null, // Inventory service does not provide playtime. This would require a separate call to another service.
      lastPlayed: new Date(item.lastModified), // The inventory item has a last modified date
      isOwned: true,
      dlc: [], // DLCs are included as separate items and would need to be identified and grouped
    }));

    return verifiedGames;

  } catch (error) {
    console.error("Error in getXboxLibrary:", error);
    return [];
  }
}
```
The implementation of the Xbox connector is the most complex of the three, but by centralizing this complexity within the BFF's custom provider and service module, the rest of the application remains insulated from these challenges.

## Section 5: Implementation Guide: PlayStation Network Connector

The integration with the PlayStation Network (PSN) presents a unique set of challenges and risks. Unlike Steam and Xbox, Sony does not provide an official, public-facing Web API for accessing user game data. Consequently, this integration relies on well-supported but unofficial, reverse-engineered libraries. The primary challenge is the authentication mechanism, which requires the user to manually provide a highly sensitive, password-equivalent token. This section provides a guide to implementing this connection securely and with a clear, user-friendly experience, leveraging the `psn-api` library and a custom NextAuth.js Credentials provider.

### 5.1 Authentication: The npsso Token Flow and its Security Implications

The authentication flow for the unofficial PSN API revolves around a 64-character token known as an `npsso`.

**The User-Facing Flow:**
The process for a user to obtain their `npsso` is entirely manual and must be performed in their web browser.²⁸
1.  The user must first log in to the official PlayStation website (https://my.playstation.com/) with their standard credentials. This action sets necessary authentication cookies in their browser.
2.  In the same browser session, the user must navigate to a specific Sony API endpoint: https://ca.account.sony.com/api/v1/ssocookie.
3.  This page will return a simple JSON response: `{ "npsso": "<64_character_token>" }`.
4.  The user must then copy this 64-character token and paste it into the application's interface.

**Security Implications:**
- It is critical to understand and communicate that the `npsso` token is **not** a limited-scope OAuth token; it is a long-lived credential that is functionally equivalent to the user's password. If this token is compromised, an attacker could potentially gain significant access to the user's PSN account. This places a profound security responsibility on the application. The entire architecture must be designed to mitigate this inherent risk. The token must only ever be handled on the server, transmitted over HTTPS, and immediately exchanged for shorter-lived tokens. The original `npsso` should **not** be stored persistently.

**User Experience Design:**
- Given the non-intuitive and high-risk nature of this process, the application's UI must provide an exceptional level of guidance. This should include:
    - A step-by-step wizard with clear instructions and screenshots.
    - Direct links to the PlayStation login page and the `ssocookie` URL.
    - Prominent warnings about the sensitivity of the `npsso` token and strong assurances about how the application secures it (e.g., "This token will be used once to generate a secure session and will not be stored.").

### 5.2 Building a Custom NextAuth.js Credentials Provider for PSN

Because the `npsso` flow is not based on OAuth, the correct tool in NextAuth.js is a custom `Credentials` provider.²³ This type of provider allows for authentication based on arbitrary credentials submitted by the user—in this case, the `npsso` token.

The provider will be configured in `authOptions` to render a form with a single input field for the token. The core logic will reside in the `authorize` function, which runs exclusively on the server.

```typescript
// /lib/auth/providers/psnProvider.ts (Conceptual)
import CredentialsProvider from "next-auth/providers/credentials";
import { exchangeNpssoForAccessCode, exchangeAccessCodeForAuthTokens, getProfileFromUserName } from "psn-api";

export const psnProvider = CredentialsProvider({
  id: "psn",
  name: "PlayStation Network",
  credentials: {
    npsso: { label: "NPSSO Token", type: "password", placeholder: "Paste your 64-character token" },
  },
  async authorize(credentials) {
    if (!credentials?.npsso) {
      return null;
    }

    try {
      // Server-side exchange using the psn-api library
      const accessCode = await exchangeNpssoForAccessCode(credentials.npsso);
      const authorization = await exchangeAccessCodeForAuthTokens(accessCode);

      // At this point, auth is successful. We need to fetch basic user profile info.
      const userProfile = await getProfileFromUserName(authorization, "me");

      // The real implementation would return actual profile data.
      return {
        id: userProfile.profile.accountId, // This should be the user's unique PSN accountId
        name: userProfile.profile.onlineId, // The user's onlineId
        // We also need to attach the tokens for the JWT callback to use.
        ...authorization 
      };

    } catch (error) {
      console.error("PSN Authorization Failed:", error);
      // Return null to indicate failed authentication
      return null;
    }
  },
});
```

When the `authorize` function returns a user object, NextAuth.js considers the sign-in successful. The `jwt` callback will then receive this object in the `user` parameter and can persist the `access_token` and `refresh_token` to the JWT for future server-side use.

### 5.3 Data Retrieval: Utilizing the psn-api Library

The recommended library for all PSN interactions is `psn-api` on npm. It is well-tested, supports TypeScript, and is actively maintained.³

Surprisingly, despite being unofficial, the data provided by this library is the most feature-rich of the three platforms. The key function for this project is `getUserPlayedGames()`. This function is superior to other available methods because it returns a comprehensive list of a user's games (if their privacy settings permit) and, crucially, includes detailed playtime information for each title in the `playDuration` field.¹⁹

The function is called by passing the `authorization` object (which contains the `accessToken` obtained during the sign-in flow) and the user's `accountId`. The `accountId` can be retrieved during the authorization step or by using `"me"` to refer to the currently authenticated user.¹⁹

Like the other platforms, this API call is subject to the user's privacy settings. If a user has restricted access to their game library, the call will fail. The application must handle this error and guide the user on how to adjust their PSN privacy settings if they wish to sync their library.

### 5.4 Full Implementation: A Code Walkthrough for the PSN Service Module

The following code provides the conceptual structure for the `psn.service.ts` module. It demonstrates how to use the `psn-api` library to fetch and normalize a user's game library.

```typescript
// /lib/services/psn.service.ts
import { getUserPlayedGames, exchangeRefreshTokenForAuthTokens } from "psn-api";
import type { Authorization, UserGameTitle } from "psn-api";
import { VerifiedGame } from '@/interfaces/VerifiedGame';

// Function to parse ISO 8601 duration string (e.g., "PT10H30M5S") to minutes
function parsePlayDuration(duration: string): number {
  const matches = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!matches) return 0;
  const hours = parseInt(matches[1] || '0', 10);
  const minutes = parseInt(matches[2] || '0', 10);
  const seconds = parseInt(matches[3] || '0', 10);
  return (hours * 60) + minutes + (seconds / 60);
}

export async function getPsnLibrary(auth: Authorization): Promise<VerifiedGame[]> {
  try {
    // The psn-api library automatically handles token refreshing if a refresh token is present.
    // However, explicit refresh logic can be added for more control.
    // if (isTokenExpired(auth.accessToken)) {
    //   auth = await exchangeRefreshTokenForAuthTokens(auth.refreshToken);
    // }

    const response = await getUserPlayedGames(auth, "me");
    const playedGames: UserGameTitle[] = response.titles || [];

    const verifiedGames: VerifiedGame[] = playedGames.map((game) => ({
      platform: 'psn',
      gameId: game.titleId,
      gameName: game.name,
      gameImageUrl: game.image.url,
      playtimeMinutes: game.playDuration ? parsePlayDuration(game.playDuration) : null,
      lastPlayed: game.lastPlayedDateTime ? new Date(game.lastPlayedDateTime) : null,
      isOwned: true, // The API returns played games, which implies ownership or access.
      dlc: [], // This API includes different versions/DLCs as separate titles. Grouping them would require additional logic.
    }));

    return verifiedGames;

  } catch (error) {
    console.error("Error in getPsnLibrary:", error);
    // Handle privacy errors or other API failures
    return [];
  }
}
```

## Section 6: Frontend Implementation and User Experience

With a robust Backend-for-Frontend (BFF) in place to handle the complexities of authentication and data aggregation, the frontend implementation becomes significantly simpler and more secure. The client-side React components will interact only with the application's own well-defined API endpoints, remaining completely unaware of the intricate logic required to communicate with Steam, Xbox, or PSN. This section outlines the practical steps for building the user-facing "Connections" dashboard, displaying verified ownership data on reviews, and engineering the review filtering system.

### 6.1 Building the Connections Dashboard in React and TypeScript

The central hub for this feature will be a "Connections" page within the user's dashboard. This page will be a client component, leveraging React hooks to manage state and interact with the authentication system.

- **Component Structure:** Create a `ConnectionsPage` component (e.g., `app/dashboard/connections/page.tsx`) marked with the `"use client"`; directive.
- **Session Management:** The component will use the `useSession` hook from `next-auth/react` to access the current user's session state. This hook provides the `session` object, which contains user details, and a `status` field ("authenticated", "unauthenticated", "loading").⁵³
- **Connecting Accounts:** For each platform, the UI will display its logo and a "Connect" button. The `onClick` handler for this button will call the `signIn` function from `next-auth/react`, passing the provider's ID (e.g., `signIn('steam')`, `signIn('xbox')`). NextAuth.js will then handle the entire redirection and authentication flow automatically.²²
- **Displaying Connected Accounts:** The component will check the `session` object for evidence of connected accounts (e.g., by checking for the existence of `session.user.steamId`). If an account is connected, the UI will display the user's platform-specific username (e.g., Gamertag, Steam persona name) and change the button to "Sync" or "Disconnect". The "Disconnect" button would call the `signOut` function, though care must be taken to only sign the user out of that specific provider link if possible, or manage the disconnection state within the application's own database.
- **Specialized PSN UI:** The UI for the PSN connection will be different. Instead of a simple "Connect" button that triggers `signIn('psn')`, it will present the instructional guide and the secure input form for the `npsso` token, as designed in Section 5.1. Submitting this form will trigger the `signIn('psn', { npsso: tokenValue, redirect: false })` function, which sends the credential to the custom PSN provider's `authorize` function on the backend.

### 6.2 Displaying Verification Status and Playtime on Reviews

Once a user's game library has been synced, this data can be used to add a layer of authenticity to their reviews.

- **BFF Verification Endpoint:** A new BFF API endpoint will be created, for example, at `GET /api/verification/check`. This endpoint will accept a game identifier as a query parameter (e.g., `/api/verification/check?gameId=STEAM_APP_ID_12345`).
- **Backend Logic:** This server-side route handler will:
    1.  Get the current user's session using `getServerSession`.
    2.  If the user is not authenticated, return a `401 Unauthorized` error.
    3.  Using the `userId` from the session, query the application's database (specifically the `VerifiedGames` table) to see if a record exists for that user and the specified `gameId`.
    4.  Return a simple JSON response, such as `{ isVerified: true, playtimeMinutes: 7200 }` or `{ isVerified: false, playtimeMinutes: null }`.
- **Frontend Component Logic:** The React component responsible for rendering a single review will use a data-fetching hook (like SWR or React Query) to call this `/api/verification/check` endpoint.

```tsx
// Inside a ReviewCard component
const { data, error } = useSWR(`/api/verification/check?gameId=${game.id}`, fetcher);

return (
  <div>
    {/*... other review content... */}
    {data?.isVerified && (
      <div className="verified-badge">
        <CheckmarkIcon />
        <span>Verified Owner</span>
        {data.playtimeMinutes > 0 && (
          <span className="playtime">
            ({Math.round(data.playtimeMinutes / 60)} hours played)
          </span>
        )}
      </div>
    )}
  </div>
);
```
This approach cleanly separates concerns. The frontend component knows nothing about how verification is performed; it only knows how to ask the BFF "Is this review verified?" and render the result. This simplification is a direct benefit of the BFF architecture.⁶

### 6.3 Engineering the Review Filtering System

To enhance user trust, the application should allow users to filter reviews to see only those from verified game owners.

- **UI Control:** The page that lists all reviews for a game will include a UI element, such as a checkbox or toggle switch, labeled "Verified Owners Only".
- **Client-Side State Management:** The state of this filter will be managed using React's `useState`. When the filter is toggled, the component will trigger a re-fetch of the review data.
- **API Query Parameter:** The client-side data fetching logic will append a query parameter to its API request for the list of reviews. For example, the request might change from `/api/reviews?gameId=123` to `/api/reviews?gameId=123&verifiedOnly=true`.
- **Backend Filtering Logic:** The API route handler on the backend that serves the list of reviews (`/api/reviews`) will read this `verifiedOnly` query parameter.
    - If `verifiedOnly` is `true`, it will modify its database query.
    - Instead of simply fetching reviews for the given `gameId`, it will perform a `JOIN` between the `Reviews` table and the `VerifiedGames` table (via the `Users` and `Connections` tables).
    - The `WHERE` clause of the query will be updated to include a condition that a matching, `isOwned=true` record must exist in the `VerifiedGames` table for the review's author.
- This ensures that only reviews from verified owners are returned in the API response, efficiently filtering the data at the source.

This system provides a powerful and trustworthy feature for users, built upon the clean data foundation established by the BFF and the database schema.

## Section 7: Project Plan and Strategic Recommendations

This final section provides a high-level project roadmap, addresses critical long-term maintenance considerations, and offers strategic recommendations to ensure the system's success, security, and future-readiness. The architectural patterns chosen are not just for the initial build; they are a crucial strategy for future-proofing and maintainability, allowing the system to adapt and grow over time.

### 7.1 Phased Implementation Roadmap

To manage complexity and mitigate risk, a phased implementation is strongly recommended. This approach allows the team to deliver value incrementally and tackle the most challenging integrations after establishing a stable foundation.

- **Phase 1 (MVP): Steam Integration.**
    - **Rationale:** Start with Steam. Its API is public, the authentication flow is abstracted by a community provider (`@hyperplay/next-auth-steam`), and it provides a clear path to a functional proof-of-concept.²⁴ This phase will validate the core BFF architecture, the database schema, and the frontend display logic.
    - **Goal:** Users can connect their Steam account, sync their game library (excluding DLCs for the MVP), and have their reviews marked as "Verified Owner."
- **Phase 2: PlayStation Network (PSN) Integration.**
    - **Rationale:** Tackle PSN next. Although its API is unofficial, the `psn-api` library is mature and provides rich data, including playtime.¹⁹ This phase will force the team to confront the most significant security and UX challenges related to the manual `npsso` token flow.
    - **Goal:** Users can connect their PSN account via the custom Credentials provider. The system securely handles the `npsso` token, fetches the game library with playtime, and correctly displays verification status.
- **Phase 3: Xbox Integration.**
    - **Rationale:** Implement the Xbox connector last. Its authentication process is by far the most complex, requiring deep integration with Microsoft Entra ID and a multi-step token exchange.⁴² The experience gained in building the custom providers and service modules in the previous phases will be invaluable here.
    - **Goal:** Users can connect their Xbox account. The custom OAuth provider successfully orchestrates the XSTS token flow, and the service module correctly queries the Inventory service for ownership data.
- **Phase 4: Expansion and Refinement.**
    - **Rationale:** With the core platforms integrated, focus can shift to expansion and feature enhancement.
    - **Goals:**
        - Implement the Steam DLC ownership workaround (Section 3.3).
        - Consider adding other platforms with developer-friendly APIs, such as GOG, which has a well-documented API and would be a relatively straightforward addition to the existing modular architecture.²⁰
        - Refine the data normalization to handle edge cases and improve playtime accuracy.

### 7.2 Long-Term Maintenance and API Change Management

The project's primary long-term risk is not the initial technical implementation but API instability. The reliance on an unofficial API for PSN and a non-public endpoint for Steam DLCs means the system is built on potentially shifting foundations. Sony or Valve could alter or remove these endpoints at any time without warning, which would break core functionality. A proactive maintenance strategy is therefore non-negotiable.

- **Robust Monitoring and Alerting:** Implement a comprehensive error monitoring solution like Sentry or Datadog.¹⁴ Configure dashboards to track the health of each external API integration. Set up alerts to immediately notify the development team of any spike in API errors (e.g., HTTP 4xx or 5xx status codes) from the platform-specific service modules. This provides an early warning system for when an external API has changed or is experiencing an outage.
- **Graceful Degradation:** The system must be designed to fail gracefully. If the PSN API becomes unavailable, it should not crash the entire application or prevent users from connecting their Steam accounts. Each service module should be isolated. If a sync fails, the UI should clearly communicate this to the user (e.g., "Could not sync with PlayStation Network at this time. Please try again later.") without affecting the functionality of other connected platforms.
- **Proactive Dependency Management:** Regularly update key npm packages, especially `next-auth`, `psn-api`, and `@hyperplay/next-auth-steam`. The maintainers of these libraries are often the first to respond to upstream API changes, and updating them can be the quickest way to fix a broken integration.

### 7.3 Final Recommendations and Future-Proofing the System

- **Prioritize Security Above All:** The handling of user credentials, especially the PSN `npsso` token, is the highest-risk aspect of this project. Re-emphasize the security practices outlined in Section 2.3: use environment variables for all secrets, encrypt all tokens at rest in the database, and never expose sensitive data to the client. Consider periodic, informal security reviews of the BFF and its token handling logic.
- **Decouple Core Business Logic:** The logic that determines if a review should be marked "verified" should be abstracted from the data-fetching services. For example, the rule might be "user must own the game." Later, it might evolve to "user must own the game and have at least 2 hours of playtime." By keeping this validation logic in a separate module, these rules can be changed without altering the platform-specific service modules.
- **Plan for Future Scale with an API Gateway:** The Next.js BFF is the perfect starting point and is sufficient for the initial scope. However, if the application grows to include many more services (e.g., Twitch, GOG, Discord) or if the API needs to be consumed by other clients (e.g., a native mobile app), the BFF logic could become a bottleneck. The modular service-layer architecture designed in this report makes a future migration to a dedicated API Gateway (such as Amazon API Gateway, Kong, or Apigee) a straightforward process.⁵⁵ The existing service modules (`steam.service.ts`, etc.) could be easily redeployed as serverless functions (e.g., AWS Lambda) behind the gateway, with minimal code changes. This architectural foresight ensures the system is prepared for future growth and complexity.

---

## Referências citadas
1. Web API Overview - Sign in to Steamworks, acessado em junho 19, 2025, https://partner.steamgames.com/doc/webapi_overview
2. Xbox services API overview - Microsoft Game Development Kit, acessado em junho 19, 2025, https://learn.microsoft.com/en-us/gaming/gdk/docs/services/fundamentals/xbox-services-api/live-introduction-to-xbox-live-apis
3. psn-api - NPM, acessado em junho 19, 2025, https://www.npmjs.com/package/psn-api
4. Guides: Backend for Frontend | Next.js, acessado em junho 19, 2025, https://nextjs.org/docs/app/guides/backend-for-frontend
5. Backend-for-Frontend (BFF) Architecture with Server Actions - ProNextJS, acessado em junho 19, 2025, https://www.pronextjs.dev/workshops/next-js-react-server-component-rsc-architecture-jbvxk/backend-for-frontend-bff-architecture-with-server-actions-fcoc0
6. Do you guys use Next js only for frontend or for both backend and frontend? - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/nextjs/comments/1bffsq1/do_you_guys_use_next_js_only_for_frontend_or_for/
7. BFF - Backend for Frontend Design Pattern with Next.js - DEV Community, acessado em junho 19, 2025, https://dev.to/adelhamad/bff-backend-for-frontend-design-pattern-with-nextjs-3od0
8. Next.js API Routes with AWS Amplify | Front-End Web & Mobile, acessado em junho 19, 2025, https://aws.amazon.com/blogs/mobile/next-js-api-routes-with-aws-amplify/
9. Building APIs with Next.js, acessado em junho 19, 2025, https://nextjs.org/blog/building-apis-with-nextjs
10. Best practises for consuming multiple third party APIs in existing API - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/questions/69713852/best-practises-for-consuming-multiple-third-party-apis-in-existing-api
11. Unified API: Seamless data integration with modern platforms | Enterspeed, acessado em junho 19, 2025, https://www.enterspeed.com/insights/what-is-unified-api
12. Unified Casino API - Upgaming, acessado em junho 19, 2025, https://upgaming.com/unified-casino-api/
13. The Realtime Unified API, acessado em junho 19, 2025, https://www.apideck.com/
14. Integrating Third-Party APIs into Next.js Server-Side Rendering Workflows - MoldStud, acessado em junho 19, 2025, https://moldstud.com/articles/p-integrating-third-party-apis-into-nextjs-server-side-rendering-workflows
15. The Ultimate Steam Web API Guide | Zuplo Blog, acessado em junho 19, 2025, https://zuplo.com/blog/2024/10/04/what-is-the-steam-web-api
16. Best architecture setup for a highly scalable, complex app using Next.js 15 (App Router + React)? : r/nextjs - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/nextjs/comments/1k0hssv/best_architecture_setup_for_a_highly_scalable/
17. Design patterns in Next.js and React.js. : r/nextjs - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/nextjs/comments/1gxeowu/design_patterns_in_nextjs_and_reactjs/
18. Steam Web API - Valve Developer Community, acessado em junho 19, 2025, https://developer.valvesoftware.com/wiki/Steam_Web_API
19. Users API | psn-api, acessado em junho 19, 2025, https://psn-api.achievements.app/api-docs/users
20. galaxy.api — GOG Galaxy Integrations API 0.69 documentation, acessado em junho 19, 2025, https://galaxy-integrations-python-api.readthedocs.io/en/latest/galaxy.api.html
21. Is there a web API endpoint to retrieve a user's library or games? - Epic Games EOS Help, acessado em junho 19, 2025, https://eoshelp.epicgames.com/s/article/Is-there-a-web-API-endpoint-to-retrieve-a-user-s-library-or-games?language=en_US
22. Getting Started - NextAuth.js, acessado em junho 19, 2025, https://next-auth.js.org/getting-started/example
23. Overview - NextAuth.js, acessado em junho 19, 2025, https://next-auth.js.org/providers/
24. @hyperplay/next-auth-steam - npm, acessado em junho 19, 2025, https://www.npmjs.com/package/%40hyperplay%2Fnext-auth-steam
25. OAuth | NextAuth.js, acessado em junho 19, 2025, https://next-auth.js.org/configuration/providers/oauth
26. Type "NextApiRequest" is not a valid type for the function's first argument - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/questions/79194744/type-nextapirequest-is-not-a-valid-type-for-the-functions-first-argument
27. Which OAuth 2.0 Flow Should I Use? - Auth0, acessado em junho 19, 2025, https://auth0.com/docs/get-started/authentication-and-authorization-flow/which-oauth-2-0-flow-should-i-use
28. Authenticating Manually - psn-api, acessado em junho 19, 2025, https://psn-api.achievements.app/authentication/authenticating-manually
29. API Security Best Practices for API keys and tokens - 42Crunch, acessado em junho 19, 2025, https://42crunch.com/token-management-best-practices/
30. How to keep user's (not my) API tokens securely? - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/questions/71201521/how-to-keep-users-not-my-api-tokens-securely
31. OAuth 2 Access Token Usage Strategies for Multiple Resources (APIs): Part 1 | Ping Identity, acessado em junho 19, 2025, https://www.pingidentity.com/en/resources/blog/post/oauth2-access-token-multiple-resources-usage-strategies.html
32. Token-based authentication - Securing the token - Information Security Stack Exchange, acessado em junho 19, 2025, https://security.stackexchange.com/questions/19676/token-based-authentication-securing-the-token
33. Steam Web API GetOwnedGames for DLC content :: Suggestions / Ideas, acessado em junho 19, 2025, https://steamcommunity.com/discussions/forum/10/1693785669868326876/
34. How can I fetch a list of games and DLC for a user via Steam's API?, acessado em junho 19, 2025, https://gamedev.stackexchange.com/questions/69058/how-can-i-fetch-a-list-of-games-and-dlc-for-a-user-via-steams-api
35. How to use with Steam openid · nextauthjs next-auth · Discussion ..., acessado em junho 19, 2025, https://github.com/nextauthjs/next-auth/discussions/697
36. Login with Steam OpenId(oidc-client-js) - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/a/49801702
37. Better Steam Web API Documentation, acessado em junho 19, 2025, https://steamwebapi.azurewebsites.net/
38. How to check the user owns our game in steam by web API? - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/questions/********/how-to-check-the-user-owns-our-game-in-steam-by-web-api
39. Steam Web API : get owned dlc info - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/Steam/comments/1cdsdx2/steam_web_api_get_owned_dlc_info/
40. Register an application with the Microsoft identity platform - Microsoft Graph, acessado em junho 19, 2025, https://learn.microsoft.com/en-us/graph/auth-register-app-v2
41. Identity and account types for single- and multitenant apps - Learn Microsoft, acessado em junho 19, 2025, https://learn.microsoft.com/en-us/security/zero-trust/develop/identity-supported-account-types
42. Requesting a User Store ID from your service with XSTS tokens or OAuth 2.0, acessado em junho 19, 2025, https://learn.microsoft.com/en-us/gaming/gdk/docs/store/commerce/service-to-service/xstore-requesting-a-userstoreid-from-services
43. Get data of User's XBOX account - Stack Overflow, acessado em junho 19, 2025, https://stackoverflow.com/questions/********/get-data-of-users-xbox-account
44. Configuring Oauth Providers - Auth.js, acessado em junho 19, 2025, https://authjs.dev/guides/configuring-oauth-providers
45. NextAuth.js: create a custom OAuth provider - DEV Community, acessado em junho 19, 2025, https://dev.to/blackarm/nextauthjs-create-a-custom-oauth-provider-1aj3
46. Xbox XSTS Login Flow - GitHub Gist, acessado em junho 19, 2025, https://gist.github.com/johngagefaulkner/7af56c87e29c85641474d2eb43eee441
47. [XboxLive REST API] How to get all games I played on my Xbox account? - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/XboxSupport/comments/19157i0/xboxlive_rest_api_how_to_get_all_games_i_played/
48. Fetching game list for specific user/profile · Issue #513 · microsoft/xbox-live-api - GitHub, acessado em junho 19, 2025, https://github.com/microsoft/xbox-live-api/issues/513
49. uri-inventory.md - MicrosoftDocs/xbox-live-docs - GitHub, acessado em junho 19, 2025, https://github.com/MicrosoftDocs/xbox-live-docs/blob/docs/xbox-live-docs-pr/api-ref/xbox-live-rest/uri/marketplace/uri-inventory.md
50. Authenticating your service with the Microsoft Store APIs - Microsoft ..., acessado em junho 19, 2025, https://learn.microsoft.com/en-us/gaming/gdk/docs/store/commerce/service-to-service/xstore-authenticating-your-service
51. Get Started - psn-api, acessado em junho 19, 2025, https://psn-api.achievements.app/get-started
52. psn-api: About, acessado em junho 19, 2025, https://psn-api.achievements.app/
53. Client API - NextAuth.js, acessado em junho 19, 2025, https://next-auth.js.org/getting-started/client
54. caprica-Six/react-bff: BFF Pattern - Back-end for Front-end Design Pattern, Micro-service Architecture in SPA, TypeScript, React - GitHub, acessado em junho 19, 2025, https://github.com/caprica-Six/react-bff
55. do we need API gateway like kong or flex gateway in nextjs App - softpost, acessado em junho 19, 2025, https://www.softpost.org/tech/do-we-need-api-gateway-like-kong-or-flex-gateway-in-nextjs-app
56. 5 Best API tools for Next.js apps - DEV Community, acessado em junho 19, 2025, https://dev.to/ethanleetech/top-5-api-management-solutions-for-nextjs-applications-33ba
57. Architecture - API Gateway between Next.js and Contentful/SAP Commerce,etc - Reddit, acessado em junho 19, 2025, https://www.reddit.com/r/nextjs/comments/1ffowde/architecture_api_gateway_between_nextjs_and/
