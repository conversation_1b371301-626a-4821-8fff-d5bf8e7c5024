// src/components/review-form/EnhancedTagInput.tsx
// Enhanced tag input component with autocomplete, suggestions, and validation

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { X, Plus, Tag, Hash, TrendingUp, Search, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import tagService, { type Tag as TagType } from '@/lib/services/tagService';

interface EnhancedTagInputProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  placeholder?: string;
  disabled?: boolean;
}

interface TagSuggestion extends TagType {
  type: 'existing' | 'trending' | 'category';
}

export const EnhancedTagInput: React.FC<EnhancedTagInputProps> = ({
  tags,
  onTagsChange,
  maxTags = 20,
  placeholder = "Add tags to help people discover your review...",
  disabled = false
}) => {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<TagSuggestion[]>([]);
  const [trendingTags, setTrendingTags] = useState<TagType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Validation function
  const validateTag = useCallback((tag: string): string | null => {
    if (!tag.trim()) return 'Tag cannot be empty';
    if (tag.length > 50) return 'Tag must be less than 50 characters';
    if (tags.includes(tag.toLowerCase())) return 'Tag already added';
    return null;
  }, [tags]);

  // Normalize tag format
  const normalizeTag = useCallback((input: string): string => {
    return input
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }, []);

  // Fetch tag suggestions
  const fetchSuggestions = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/tags/search?q=${encodeURIComponent(query)}&limit=8`);
      const data = await response.json();

      if (data.success) {
        const existingSuggestions = data.suggestions.map((tag: TagType) => ({
          ...tag,
          type: 'existing' as const
        }));
        setSuggestions(existingSuggestions);
      }
    } catch (error) {
      console.error('Error fetching tag suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Debounced suggestion fetching
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (inputValue.trim()) {
        fetchSuggestions(inputValue.trim());
      } else {
        setSuggestions([]);
      }
    }, 300);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [inputValue, fetchSuggestions]);

  // Load trending tags on mount
  useEffect(() => {
    const loadTrendingTags = async () => {
      try {
        const response = await fetch('/api/tags?endpoint=trending&limit=10');
        const data = await response.json();

        if (data.success) {
          setTrendingTags(data.tags || []);
        }
      } catch (error) {
        console.error('Error loading trending tags:', error);
      }
    };

    loadTrendingTags();
  }, []);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setValidationError(null);
    setShowSuggestions(true);
    setSelectedSuggestionIndex(-1);
  }, []);

  // Handle tag addition
  const addTag = useCallback((tagName: string) => {
    const normalizedTag = normalizeTag(tagName);
    const error = validateTag(normalizedTag);

    if (error) {
      setValidationError(error);
      return;
    }

    if (tags.length >= maxTags) {
      setValidationError(`Maximum ${maxTags} tags allowed`);
      return;
    }

    onTagsChange([...tags, normalizedTag]);
    setInputValue('');
    setValidationError(null);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    
    // Focus back to input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [tags, onTagsChange, maxTags, normalizeTag, validateTag]);

  // Handle tag removal
  const removeTag = useCallback((tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  }, [tags, onTagsChange]);

  // Handle Enter key press
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (selectedSuggestionIndex >= 0 && suggestions[selectedSuggestionIndex]) {
        addTag(suggestions[selectedSuggestionIndex].name);
      } else if (inputValue.trim()) {
        addTag(inputValue.trim());
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => 
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      // Remove last tag if input is empty
      removeTag(tags[tags.length - 1]);
    }
  }, [inputValue, selectedSuggestionIndex, suggestions, addTag, removeTag, tags]);

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion: TagSuggestion) => {
    addTag(suggestion.name);
  }, [addTag]);

  // Handle trending tag click
  const handleTrendingTagClick = useCallback((tag: TagType) => {
    addTag(tag.name);
  }, [addTag]);

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    };

    if (showSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showSuggestions]);

  return (
    <div className="space-y-4">
      {/* Main Input Area */}
      <div className="relative">
        {/* Input Container */}
        <div className="relative border border-slate-600/50 rounded-lg bg-slate-800/30 backdrop-blur-sm focus-within:border-violet-500/50 transition-colors">
          {/* Tags Display */}
          <div className="p-3">
            <div className="flex flex-wrap gap-2 mb-3">
              <AnimatePresence>
                {tags.map((tag, index) => (
                  <motion.div
                    key={tag}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Badge 
                      variant="secondary"
                      className="bg-violet-500/20 text-violet-300 border-violet-500/30 hover:bg-violet-500/30 transition-colors"
                    >
                      <Hash size={12} className="mr-1" />
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        disabled={disabled}
                        className="ml-2 hover:text-violet-100 transition-colors"
                        aria-label={`Remove ${tag} tag`}
                      >
                        <X size={12} />
                      </button>
                    </Badge>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>

            {/* Input Field */}
            <div className="flex items-center gap-2">
              <Tag size={16} className="text-slate-400 flex-shrink-0" />
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onFocus={() => setShowSuggestions(true)}
                placeholder={tags.length === 0 ? placeholder : "Add another tag..."}
                disabled={disabled || tags.length >= maxTags}
                className="flex-1 bg-transparent text-slate-300 placeholder-slate-500 outline-none"
              />
              {isLoading && <Loader2 size={16} className="text-slate-400 animate-spin" />}
              {inputValue && (
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  onClick={() => addTag(inputValue)}
                  disabled={!!validateTag(normalizeTag(inputValue))}
                  className="h-6 px-2 text-xs"
                >
                  Add
                </Button>
              )}
            </div>
          </div>

          {/* Tag Counter */}
          <div className="px-3 pb-3 flex justify-between items-center text-xs text-slate-500">
            <span>{tags.length}/{maxTags} tags</span>
            {validationError && (
              <span className="text-red-400">{validationError}</span>
            )}
          </div>
        </div>

        {/* Suggestions Dropdown */}
        <AnimatePresence>
          {showSuggestions && (suggestions.length > 0 || inputValue.length >= 2) && (
            <motion.div
              ref={suggestionsRef}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-2 bg-slate-800/95 backdrop-blur-xl border border-slate-600/50 rounded-lg shadow-xl z-50 max-h-60 overflow-y-auto"
            >
              {suggestions.length > 0 ? (
                <div className="p-2">
                  <div className="text-xs text-slate-400 mb-2 px-2">Suggestions</div>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={suggestion.id}
                      type="button"
                      onClick={() => handleSuggestionClick(suggestion)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        index === selectedSuggestionIndex
                          ? 'bg-violet-500/20 text-violet-300'
                          : 'hover:bg-slate-700/50 text-slate-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Hash size={12} />
                          <span>{suggestion.name}</span>
                          {suggestion.category && (
                            <Badge variant="outline" className="text-xs">
                              {suggestion.category}
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-slate-500">
                          {suggestion.usage_count} uses
                        </span>
                      </div>
                    </button>
                  ))}
                </div>
              ) : inputValue.length >= 2 ? (
                <div className="p-4 text-center">
                  <div className="text-slate-400 text-sm mb-2">No existing tags found</div>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => addTag(inputValue)}
                    disabled={!!validateTag(normalizeTag(inputValue))}
                    className="text-xs"
                  >
                    <Plus size={12} className="mr-1" />
                    Create "{normalizeTag(inputValue)}"
                  </Button>
                </div>
              ) : null}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Trending Tags */}
      {trendingTags.length > 0 && tags.length === 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm text-slate-400">
            <TrendingUp size={14} />
            <span>Trending Tags</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {trendingTags
              .filter(tag => !tags.includes(tag.name))
              .slice(0, 8)
              .map(tag => (
                <Button
                  key={tag.id}
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleTrendingTagClick(tag)}
                  disabled={disabled || tags.length >= maxTags}
                  className="h-7 px-3 text-xs border-slate-600/50 hover:border-violet-500/50 hover:bg-violet-500/10"
                >
                  <Hash size={10} className="mr-1" />
                  {tag.name}
                </Button>
              ))
            }
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-slate-500">
        <p>Use tags to help people discover your review. Press Enter or comma to add a tag.</p>
        <p>Tags are automatically normalized (lowercase, hyphens for spaces).</p>
      </div>
    </div>
  );
};

export default EnhancedTagInput;