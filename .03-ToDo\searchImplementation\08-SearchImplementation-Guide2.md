# CriticalPixel Search System Implementation - Guide 2
## PostgreSQL FTS Schema and Backend Implementation

### 🎯 **Overview & Objectives**

This guide provides step-by-step instructions for implementing PostgreSQL Full-Text Search (FTS) schema changes and backend functionality for the CriticalPixel search system. The focus is on database-level implementations that will enable efficient search across reviews and users.

---

### 📋 **Implementation Checklist**

- [ ] **Add search vectors and indexes to database**
  - [ ] Add `search_vector` column to `reviews` table
  - [ ] Add `search_vector` column to `profiles` table
  - [ ] Create GIN indexes for fast full-text search
  - [ ] Create additional indexes for filtering

- [ ] **Create search update functions**
  - [ ] Implement `update_review_search_vector()` function
  - [ ] Implement `update_profile_search_vector()` function
  - [ ] Test vector generation with sample data

- [ ] **Create and activate search triggers**
  - [ ] Create trigger for reviews search vector updates
  - [ ] Create trigger for profiles search vector updates
  - [ ] Verify trigger activation on data changes

- [ ] **Implement advanced search RPC functions**
  - [ ] Create `search_reviews()` function with filtering
  - [ ] Create `search_profiles()` function
  - [ ] Test functions with various search parameters

- [ ] **Update existing data**
  - [ ] Populate search vectors for all existing reviews
  - [ ] Populate search vectors for all existing profiles
  - [ ] Verify successful vector generation

---

### 🧠 **Efficiency Guidelines for AI**

1. **Batch operations for performance:**
   - When updating existing data, use batched operations to prevent timeouts
   - Consider using temporary tables for complex operations

2. **Test with real data:**
   - Verify index performance with realistic data volumes
   - Check query execution plans using `EXPLAIN ANALYZE`
   - Monitor query times for different search patterns

3. **Optimize tsvector generation:**
   - Use appropriate weights (A, B, C, D) for different text fields
   - Consider custom dictionaries for gaming terminology
   - Balance index size vs search performance

4. **Error handling and robustness:**
   - Add NULL checks to prevent function failures
   - Implement transaction handling for data updates
   - Add proper error logging in PostgreSQL functions

---

### 💾 **SQL Implementation Scripts**

#### Step 1: Add Search Vectors to Tables

```sql
-- IMPORTANT: Execute all commands in Supabase SQL Editor
-- Add comment explaining purpose of these schema modifications
-- Step 2.1: Add search vectors to reviews table
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Step 2.2: Add search vectors to profiles table  
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Step 2.3: Create GIN indexes for fast full-text search
-- GIN indexes are recommended for full-text search as they allow fast lookups
CREATE INDEX IF NOT EXISTS reviews_search_idx ON reviews USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS profiles_search_idx ON profiles USING GIN(search_vector);

-- Step 2.4: Create additional indexes for filtering
-- These indexes support the advanced filtering capabilities
CREATE INDEX IF NOT EXISTS reviews_genre_idx ON reviews USING GIN(genres);
CREATE INDEX IF NOT EXISTS reviews_platform_idx ON reviews USING GIN(platforms);
CREATE INDEX IF NOT EXISTS reviews_tags_idx ON reviews USING GIN(tags);
CREATE INDEX IF NOT EXISTS reviews_score_idx ON reviews (overall_score);
CREATE INDEX IF NOT EXISTS reviews_date_idx ON reviews (created_at);
CREATE INDEX IF NOT EXISTS reviews_author_idx ON reviews (author_id);

-- Step 2.5: Create composite indexes for complex filtering
-- Composite indexes support the most common filter combinations
CREATE INDEX IF NOT EXISTS reviews_status_score_date_idx ON reviews (status, overall_score, created_at);
CREATE INDEX IF NOT EXISTS reviews_author_status_idx ON reviews (author_id, status);
```

#### Step 2: Create Search Update Functions

```sql
-- Step 2.6: Function to update review search vectors
-- This function generates a weighted tsvector from multiple text fields
CREATE OR REPLACE FUNCTION update_review_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  -- Use setweight to assign different priorities to different fields
  -- A: Highest priority (title, game name)
  -- B: High priority (metadata, keywords)
  -- C: Medium priority (genres, platforms, tags)
  -- D: Lower priority (summary text)
  NEW.search_vector = 
    setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.game_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.meta_description, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(NEW.focus_keyword, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.genres, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.platforms, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.tags, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(NEW.summary, '')), 'D');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 2.7: Function to update profile search vectors
-- Similar approach to review vectors but with profile-specific fields
CREATE OR REPLACE FUNCTION update_profile_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector = 
    setweight(to_tsvector('english', COALESCE(NEW.username, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.display_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.bio, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.preferred_genres, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.favorite_consoles, ' '), '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

#### Step 3: Create Search Triggers

```sql
-- Step 2.8: Create triggers for automatic search vector updates
-- First remove any existing triggers to avoid conflicts
DROP TRIGGER IF EXISTS reviews_search_update ON reviews;
CREATE TRIGGER reviews_search_update
  BEFORE INSERT OR UPDATE ON reviews
  FOR EACH ROW EXECUTE FUNCTION update_review_search_vector();

DROP TRIGGER IF EXISTS profiles_search_update ON profiles;
CREATE TRIGGER profiles_search_update
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_profile_search_vector();
```

#### Step 4: Create Search RPC Functions

```sql
-- Step 2.9: Advanced review search function with filtering
-- This function supports complex filtering and sorting of search results
CREATE OR REPLACE FUNCTION search_reviews(
  query_text text DEFAULT '',
  filter_genres text[] DEFAULT '{}',
  filter_platforms text[] DEFAULT '{}',
  filter_tags text[] DEFAULT '{}',
  min_score numeric DEFAULT 0,
  max_score numeric DEFAULT 10,
  author_id_filter text DEFAULT '',
  limit_count integer DEFAULT 20,
  offset_count integer DEFAULT 0
)
RETURNS TABLE(
  id text,
  title text,
  game_name text,
  slug text,
  overall_score numeric,
  genres text[],
  platforms text[],
  tags text[],
  author_name text,
  author_slug text,
  created_at timestamptz,
  main_image_url text,
  igdb_cover_url text,
  search_rank real
) AS $$
BEGIN
  -- Return a filtered and ranked set of reviews
  -- Note: websearch_to_tsquery supports natural language queries
  RETURN QUERY
  SELECT 
    r.id,
    r.title,
    r.game_name,
    r.slug,
    r.overall_score,
    r.genres,
    r.platforms,
    r.tags,
    r.author_name,
    r.author_slug,
    r.created_at,
    r.main_image_url,
    r.igdb_cover_url,
    CASE 
      WHEN query_text = '' THEN 1.0
      ELSE ts_rank(r.search_vector, websearch_to_tsquery('english', query_text))
    END as search_rank
  FROM reviews r
  WHERE 
    r.status = 'published'
    AND (query_text = '' OR r.search_vector @@ websearch_to_tsquery('english', query_text))
    AND (cardinality(filter_genres) = 0 OR r.genres && filter_genres)
    AND (cardinality(filter_platforms) = 0 OR r.platforms && filter_platforms)
    AND (cardinality(filter_tags) = 0 OR r.tags && filter_tags)
    AND r.overall_score >= min_score
    AND r.overall_score <= max_score
    AND (author_id_filter = '' OR r.author_id = author_id_filter)
  ORDER BY 
    CASE WHEN query_text = '' THEN r.created_at ELSE search_rank END DESC,
    r.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Step 2.10: User search function with optimization for common patterns
CREATE OR REPLACE FUNCTION search_profiles(
  query_text text DEFAULT '',
  limit_count integer DEFAULT 20,
  offset_count integer DEFAULT 0
)
RETURNS TABLE(
  id text,
  username text,
  display_name text,
  slug text,
  avatar_url text,
  bio text,
  level integer,
  review_count integer,
  preferred_genres text[],
  search_rank real
) AS $$
BEGIN
  -- Return a filtered and ranked set of user profiles
  RETURN QUERY
  SELECT 
    p.id,
    p.username,
    p.display_name,
    p.slug,
    p.avatar_url,
    p.bio,
    p.level,
    p.review_count,
    p.preferred_genres,
    CASE 
      WHEN query_text = '' THEN 1.0
      ELSE ts_rank(p.search_vector, websearch_to_tsquery('english', query_text))
    END as search_rank
  FROM profiles p
  WHERE 
    (query_text = '' OR p.search_vector @@ websearch_to_tsquery('english', query_text))
  ORDER BY 
    CASE WHEN query_text = '' THEN p.level ELSE search_rank END DESC,
    p.review_count DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;
```

#### Step 5: Update Existing Data

```sql
-- Step 2.11: Update search vectors for existing reviews
-- This populates the search_vector column for all existing data
UPDATE reviews SET search_vector = 
  setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(game_name, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(meta_description, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(focus_keyword, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(genres, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(platforms, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(tags, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(summary, '')), 'D')
WHERE search_vector IS NULL;

-- Step 2.12: Update search vectors for existing profiles
UPDATE profiles SET search_vector = 
  setweight(to_tsvector('english', COALESCE(username, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(display_name, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(bio, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(preferred_genres, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(favorite_consoles, ' '), '')), 'C')
WHERE search_vector IS NULL;
```

---

### 🔍 **Testing and Verification**

After implementing the above SQL scripts, perform these tests to verify functionality:

1. **Verify schema changes:**
   ```sql
   -- Check if columns were added
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'reviews' AND column_name = 'search_vector';
   
   -- Check if indexes were created
   SELECT indexname, indexdef FROM pg_indexes 
   WHERE tablename = 'reviews' AND indexname LIKE '%search%';
   ```

2. **Test search vectors:**
   ```sql
   -- Test a review search vector
   SELECT id, title, ts_rank(search_vector, websearch_to_tsquery('english', 'test query')) as rank
   FROM reviews
   WHERE search_vector @@ websearch_to_tsquery('english', 'test query')
   LIMIT 5;
   ```

3. **Verify triggers:**
   ```sql
   -- Insert test data and verify trigger execution
   INSERT INTO reviews(id, title, status) 
   VALUES ('test-trigger', 'Test Trigger Execution', 'draft')
   RETURNING id, title, search_vector;
   
   -- Clean up test data
   DELETE FROM reviews WHERE id = 'test-trigger';
   ```

4. **Test search functions:**
   ```sql
   -- Test review search function
   SELECT * FROM search_reviews('action adventure', '{}', '{}', '{}', 7, 10, '', 5, 0);
   
   -- Test profile search function
   SELECT * FROM search_profiles('gaming', 5, 0);
   ```

---

### 🚀 **Performance Considerations**

1. **Monitor GIN index size:**
   - GIN indexes can become large for text-heavy tables
   - Use `SELECT pg_size_pretty(pg_relation_size('reviews_search_idx'))` to monitor

2. **Query performance analysis:**
   - Use `EXPLAIN ANALYZE` to check query execution plans
   - Example: `EXPLAIN ANALYZE SELECT * FROM search_reviews('test', '{}', '{}', '{}', 0, 10, '', 10, 0);`

3. **Batch processing for large datasets:**
   - For tables with >100k rows, consider updating in batches
   - Example approach: `UPDATE reviews SET search_vector = ... WHERE id IN (SELECT id FROM reviews WHERE search_vector IS NULL LIMIT 1000);`

---

### 🔄 **Next Steps**

After implementing the database changes, proceed to Guide 3 which focuses on creating the Unified Search API Routes for the CriticalPixel search system. The database foundation established here will enable efficient search operations through these API routes.

**Note:** Keep a record of any performance issues or edge cases encountered during implementation to inform future optimizations.
