'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Gamepad2 } from 'lucide-react';
import { FastAverageColor } from 'fast-average-color';

interface ReviewCardProps {
  review: any;
  theme: any;
  index: number;
  totalReviews: number;
  variant?: 'grid' | 'list';
  onImageColorChange?: (reviewId: string, colorData: { isDark: boolean; rgb: string }) => void;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  theme,
  index,
  totalReviews,
  variant = 'grid',
  onImageColorChange
}) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [imageColor, setImageColor] = useState<{ isDark: boolean; rgb: string } | null>(null);
  const fac = useMemo(() => new FastAverageColor(), []);

  // Handle image load and color analysis with robust CORS handling
  const handleImageLoad = useCallback((reviewId: string, imgElement: HTMLImageElement) => {
    // Skip color analysis for external images that likely won't work with CORS
    const imageUrl = imgElement.src;
    const isExternalImage = imageUrl && !imageUrl.startsWith(window.location.origin) && !imageUrl.startsWith('/');
    
    if (isExternalImage) {
      // Use default dark theme for external images to avoid CORS issues
      const colorData = {
        isDark: true,
        rgb: 'rgb(64, 64, 64)'
      };
      setImageColor(colorData);
      onImageColorChange?.(reviewId, colorData);
      return;
    }

    try {
      // Only analyze local/same-origin images
      const result = fac.getColor(imgElement, { 
        algorithm: 'dominant',
        defaultColor: [128, 128, 128, 255]
      });
      const colorData = {
        isDark: result.isDark,
        rgb: result.rgb
      };
      setImageColor(colorData);
      onImageColorChange?.(reviewId, colorData);
    } catch (error) {
      // Fallback for any remaining errors
      console.warn(`Color analysis failed for image ${reviewId}:`, error instanceof Error ? error.message : String(error));
      const colorData = {
        isDark: true,
        rgb: 'rgb(64, 64, 64)'
      };
      setImageColor(colorData);
      onImageColorChange?.(reviewId, colorData);
    }
  }, [fac, onImageColorChange]);

  // Check if review should span full width (2x3 grid for odd numbers)
  const shouldSpanFullWidth = (index: number, totalReviews: number) => {
    if (variant !== 'grid') return false;
    // For 3 reviews: show as 2 top + 1 full width bottom (2x3 layout)
    if (totalReviews === 3) return index === 2;
    // For 5 reviews: show as 2x2 + 1 full width bottom (2x3 layout)
    if (totalReviews === 5) return index === 4;
    return false;
  };

  const reviewUrl = `/reviews/view/${review.slug}`;
  
  // Format date function
  const formatDate = (date: string | Date) => {
    if (!date) return 'Unknown';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };
  
  // Convert score to 0-100 range and no decimals
  // Handle different possible score formats from database
  const rawScore = review.rating || 0;
  let displayScore = 0;
  
  if (rawScore <= 1) {
    // If score is 0-1 (decimal percentage), convert to 0-100
    displayScore = Math.round(rawScore * 100);
  } else if (rawScore <= 10) {
    // If score is 0-10, convert to 0-100
    displayScore = Math.round(rawScore * 10);
  } else {
    // Assume it's already 0-100, just clamp and round
    displayScore = Math.round(Math.max(0, Math.min(100, rawScore)));
  }
  
  // Debug logging for review cards
  if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 ReviewCard ${review.game_name} score calculation:`, {
      originalScore: review.rating,
      displayScore,
      type: typeof review.rating,
      conversionUsed: rawScore <= 1 ? 'decimal-to-percent' : rawScore <= 10 ? '10-scale-to-100' : 'clamped-100'
    });
  }

  const isFullWidth = shouldSpanFullWidth(index, totalReviews);
  
  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log(`Review ${index + 1}: ${review.game_name}`, {
      isFullWidth,
      totalReviews: totalReviews,
      image: review.game_image || review.main_image_url,
      hasImage: !!(review.game_image || review.main_image_url)
    });
  }

  // Dynamic text color based on image brightness
  const textColor = imageColor?.isDark === false ? 'text-gray-900' : 'text-white';
  const scoreColor = imageColor?.isDark === false ? 'text-yellow-600' : 'text-yellow-400';



  // List variant styling
  if (variant === 'list') {
    return (
      <motion.div
        key={review.id}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        whileHover={{ x: 5 }}
        className="group"
        onMouseEnter={() => setHoveredCard(review.id)}
        onMouseLeave={() => setHoveredCard(null)}
      >
        <Link href={reviewUrl} className="block h-full">
          <Card className="bg-gray-900/40 backdrop-blur-sm overflow-hidden h-24 hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 ease-out rounded-lg shadow-lg" style={{border: 'none', boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'}}>
            <CardContent className="p-0 h-full">
              <div className="relative h-full flex">
                {/* Image Section */}
                <div className="relative w-32 flex-shrink-0 overflow-hidden rounded-l-lg">
                  {review.game_image || review.main_image_url ? (
                    <>
                      <img
                        src={review.game_image || review.main_image_url}
                        alt={review.game_name}
                        className="w-full h-full object-cover transition-all duration-500 ease-out"
                        style={{
                          objectPosition: review.main_image_position || 'center'
                        }}
                        onLoad={(e) => handleImageLoad(review.id, e.target as HTMLImageElement)}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                        loading="lazy"
                      />
                      <div
                        className="image-fallback absolute inset-0 w-full h-full flex items-center justify-center"
                        style={{ 
                          backgroundColor: `${theme?.colors?.primary}30`,
                          display: 'none'
                        }}
                      >
                        <Gamepad2 className="h-6 w-6 text-gray-400" />
                      </div>
                    </>
                  ) : (
                    <div
                      className="w-full h-full flex items-center justify-center"
                      style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                    >
                      <Gamepad2 className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="flex-1 p-3 flex flex-col justify-between">
                  <div>
                    <h3 className="font-mono font-bold text-sm text-white truncate">
                      <span className="text-white/40 text-xs mr-1">//</span>
                      {review.game_name}
                    </h3>
                    <div className="flex items-center gap-2 text-xs text-gray-400 mt-1">
                      <span>{formatDate(review.created_at)}</span>
                      {review.played_on && (
                        <>
                          <span className="mx-3">•</span>
                          <span>Played on {review.played_on}</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center gap-1">
                      <span className="text-white/60 font-mono text-xs">score:</span>
                      <span className="font-mono font-bold text-sm text-white tabular-nums">
                        {displayScore}
                      </span>
                    </div>
                    <div className="flex items-center gap-3 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <span>👁{review.view_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>♥{review.like_count || 0}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>💬{review.comment_count || 0}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </motion.div>
    );
  }

  // Grid variant styling (default)
  return (
    <>
      {/* CSS Styles for banner images and responsive grid */}
      <style jsx>{`
        .banner-image-smart {
          object-position: center 25%;
          filter: brightness(1.1) contrast(1.05) saturate(1.1);
        }

        .banner-image-hq {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: -moz-crisp-edges;
          image-rendering: crisp-edges;
          image-rendering: high-quality;
          backface-visibility: hidden;
          transform: translateZ(0);
          will-change: transform;
        }

        .review-card-overlay {
          background: linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.3) 50%, transparent 100%);
          border-radius: 0.75rem; /* Match the rounded-xl */
          transition: background 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          overflow: hidden; /* Ensure content doesn't bleed outside */
        }

        .review-card-overlay:hover {
          background: linear-gradient(to top, rgba(0,0,0,0.95) 0%, rgba(0,0,0,0.6) 50%, rgba(0,0,0,0.2) 100%);
        }

        /* Fix for full-width cards to prevent color bleeding */
        .full-width-card .review-card-overlay {
          border-radius: 0.75rem !important;
          overflow: hidden !important;
        }

        .full-width-card .banner-image-smart,
        .full-width-card .image-fallback {
          border-radius: 0.75rem !important;
        }

        .full-width-card {
          grid-column: 1 / -1 !important;
          width: 100% !important;
          max-width: none !important;
        }

        .full-width-card .review-card-content {
          height: 400px; /* Further increased height for full-width cards */
        }

        /* Force grid to respect full-width cards */
        .grid > .full-width-card {
          grid-column: 1 / -1 !important;
          width: 100% !important;
        }

        @media (max-width: 640px) {
          .banner-image-smart {
            object-position: center 30%;
          }
        }

        @media (min-width: 1024px) {
          .banner-image-smart {
            object-position: center 20%;
          }
        }
      `}</style>

      <motion.div
        key={review.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5 }}
        className={`group ${isFullWidth ? 'full-width-card' : ''}`}
        style={isFullWidth ? { 
          gridColumn: '1 / -1', 
          width: '100%',
          backgroundColor: process.env.NODE_ENV === 'development' ? 'rgba(255, 0, 0, 0.1)' : undefined 
        } : undefined}
        onMouseEnter={() => setHoveredCard(review.id)}
        onMouseLeave={() => setHoveredCard(null)}
      >
        <Link href={reviewUrl} className="block h-full">
          <Card className="bg-gray-900/40 backdrop-blur-sm overflow-hidden h-full hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 ease-out rounded-xl shadow-lg" style={{border: 'none', boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'}}>
            <CardContent className="p-0 h-full">
              <div className={`relative h-full ${isFullWidth ? 'review-card-content' : ''}`}>
                {/* Banner Image with proper aspect ratio and positioning */}
                {review.game_image || review.main_image_url ? (
                  <div className={`relative w-full overflow-hidden rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}>
                    <img
                      src={review.game_image || review.main_image_url}
                      alt={review.game_name}
                      className="w-full h-full object-cover banner-image-smart banner-image-hq transition-all duration-500 ease-out rounded-xl"
                      style={{
                        objectPosition: review.main_image_position || undefined
                      }}
                      onLoad={(e) => handleImageLoad(review.id, e.target as HTMLImageElement)}
                      onError={(e) => {
                        // Hide broken image and show fallback
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                      loading="lazy"
                    />
                    {/* Fallback element - hidden by default */}
                    <div
                      className={`image-fallback absolute inset-0 w-full flex items-center justify-center rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}
                      style={{ 
                        backgroundColor: `${theme?.colors?.primary}30`,
                        display: 'none'
                      }}
                    >
                      <div className="text-center">
                        <Gamepad2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-xs text-gray-500">Image not available</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div
                    className={`w-full flex items-center justify-center rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}
                    style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                  >
                    <div className="text-center">
                      <Gamepad2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-xs text-gray-500">No image</p>
                    </div>
                  </div>
                )}

                {/* Neural Code Overlay */}
                <div className="absolute inset-0 review-card-overlay p-4">
                  {/* Score - Top Left */}
                  <div className="absolute top-4 left-4">
                    <div className="flex items-center gap-2 px-3 py-2 bg-gray-800/30 border border-gray-700/30 rounded backdrop-blur-sm h-8">
                      <span className="text-white/60 font-mono text-xs">score:</span>
                      <span className="font-mono font-bold text-sm text-white tabular-nums">
                        {displayScore}
                      </span>
                    </div>
                  </div>

                  {/* Played on - Top Right */}
                  {review.played_on && (
                    <div className="absolute top-4 right-4">
                      <div className="flex flex-col items-end px-3 py-2 bg-gray-800/30 border border-gray-700/30 rounded backdrop-blur-sm">
                        <span className="text-white/60 font-mono text-xs mb-1">
                          Played on
                        </span>
                        <span className="text-white/90 font-mono text-xs font-bold">
                          {review.played_on}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Title and Stats - Bottom */}
                  <div className="absolute bottom-4 left-4 right-4 pr-2">
                    <h3 className={`font-mono font-bold text-lg truncate transition-colors duration-300 ${textColor} drop-shadow-lg max-w-full mb-2`}>
                      <span className="text-white/40 text-sm mr-2">//</span>
                      {review.game_name}
                    </h3>
                    <div className="flex items-center justify-between text-xs text-white/80 mb-2">
                      <div className="flex items-center gap-2">
                        <span>{formatDate(review.created_at)}</span>
                        {review.played_on && (
                          <>
                            <span>•</span>
                            <span className="font-medium">{review.played_on}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-white/80">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <span>👁</span>
                          <span>{review.view_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span>♥</span>
                          <span>{review.like_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span>💬</span>
                          <span>{review.comment_count || 0}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </motion.div>
    </>
  );
};

export default ReviewCard;