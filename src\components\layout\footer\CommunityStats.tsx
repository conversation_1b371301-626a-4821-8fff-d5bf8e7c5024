'use client';

import React, { useState, useEffect } from 'react';
import { getSiteStats, formatLineCount, type SiteStats } from '@/lib/services/lineCounterService';

interface CommunityStatsProps {
  className?: string;
}

const CommunityStats: React.FC<CommunityStatsProps> = ({ className = "" }) => {
  const [stats, setStats] = useState<SiteStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const siteStats = await getSiteStats();
        setStats(siteStats);
      } catch (error) {
        console.error('Error fetching community stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-6 h-6 bg-slate-700 rounded animate-pulse"></div>
        <div className="w-48 h-4 bg-slate-700 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  // Generate fun community text variations with styled numbers
  const communityTexts = [
    {
      text: "Our {users} pixel warriors have crafted {lines} lines across {reviews} epic reviews!",
      users: stats.totalUsers,
      lines: formatLineCount(stats.totalLines),
      reviews: stats.totalReviews
    },
    {
      text: "{users} gamers strong, writing {lines} lines of gaming wisdom in {reviews} reviews!",
      users: stats.totalUsers,
      lines: formatLineCount(stats.totalLines),
      reviews: stats.totalReviews
    },
    {
      text: "Community power: {users} members × {lines} lines = {reviews} legendary reviews!",
      users: stats.totalUsers,
      lines: formatLineCount(stats.totalLines),
      reviews: stats.totalReviews
    },
    {
      text: "{users} critical thinkers have penned {lines} lines of pure gaming insight!",
      users: stats.totalUsers,
      lines: formatLineCount(stats.totalLines),
      reviews: stats.totalReviews
    },
    {
      text: "Level {users} community unlocked! Achievement: {lines} lines written across {reviews} reviews!",
      users: stats.totalUsers,
      lines: formatLineCount(stats.totalLines),
      reviews: stats.totalReviews
    }
  ];

  // Select text based on current stats (deterministic but varies with growth)
  const textIndex = (stats.totalUsers + stats.totalReviews) % communityTexts.length;
  const selectedTextData = communityTexts[textIndex];

  // Function to render text with styled numbers
  const renderStyledText = (textData: typeof selectedTextData) => {
    const parts = textData.text.split(/(\{[^}]+\})/);
    return parts.map((part, index) => {
      if (part === '{users}') {
        return <span key={index} className="font-bold text-violet-400">{textData.users}</span>;
      } else if (part === '{lines}') {
        return <span key={index} className="font-bold text-emerald-400">{textData.lines}</span>;
      } else if (part === '{reviews}') {
        return <span key={index} className="font-bold text-cyan-400">{textData.reviews}</span>;
      }
      return part;
    });
  };

  return (
    <div 
      className={`group relative transition-all duration-300 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main community text */}
      <div className="flex flex-wrap gap-2 text-[0.6rem] leading-tight text-slate-400 font-mono">
        <span className="bg-slate-800/50 px-1.5 py-0.5 rounded">
          <span className="text-violet-400">//</span> {renderStyledText(selectedTextData)}
        </span>

        {/* Additional survey info */}
        {stats.totalSurveys > 0 && (
          <span className="bg-slate-800/50 px-1.5 py-0.5 rounded">
            <span className="text-violet-400">+</span> {stats.totalSurveys} performance surveys completed
          </span>
        )}
      </div>


    </div>
  );
};

export default CommunityStats;
