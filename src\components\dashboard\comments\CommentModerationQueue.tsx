'use client';

// Comment Moderation Queue Component
// Date: 21/06/2025
// Task: Comment Moderation Dashboard Implementation

import React, { useState } from 'react';
import { CommentModerationData } from '@/types/commentModeration';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { useAuthContext } from '@/contexts/auth-context';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Check, 
  X, 
  Pin, 
  Flag, 
  ExternalLink,
  MessageSquare,
  Calendar,
  User
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

interface CommentModerationQueueProps {
  comments: CommentModerationData[];
  isLoading: boolean;
}

export function CommentModerationQueue({ 
  comments, 
  isLoading 
}: CommentModerationQueueProps) {
  const { user } = useAuthContext();
  const [selectedComment, setSelectedComment] = useState<string | null>(null);
  const [moderationNotes, setModerationNotes] = useState('');
  const { moderateComment } = useCommentModeration(user?.id || '');

  const handleModeration = async (
    commentId: string, 
    action: 'approve' | 'reject' | 'delete' | 'pin'
  ) => {
    await moderateComment.mutateAsync({
      commentId,
      action,
      notes: moderationNotes,
    });
    
    setSelectedComment(null);
    setModerationNotes('');
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                <div className="h-4 bg-slate-700 rounded w-1/2"></div>
                <div className="h-20 bg-slate-700 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2">
            No pending comments
          </h3>
          <p className="text-slate-400">
            All comments have been reviewed. Great job!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map((comment) => (
        <Card key={comment.id} className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600/50 transition-colors">
          <CardContent className="p-6">
            {/* Comment Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-slate-400" />
                  <span className="font-medium text-slate-200">
                    {comment.author?.display_name || comment.author_name}
                  </span>
                  <span className="text-slate-400">@{comment.author_username}</span>
                </div>
                {comment.flag_count > 0 && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <Flag className="h-3 w-3" />
                    {comment.flag_count} reports
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2 text-sm text-slate-400">
                <Calendar className="h-4 w-4" />
                {formatDistanceToNow(new Date(comment.created_at))} ago
              </div>
            </div>

            {/* Review Context */}
            <div className="mb-4 p-3 bg-slate-900/50 rounded-lg border border-slate-700/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Comment on:</p>
                  <p className="font-medium text-slate-200">{comment.review_title}</p>
                </div>
                <Link 
                  href={`/reviews/view/${comment.review_slug}`}
                  className="text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Comment Content */}
            <div className="mb-4 p-4 bg-slate-900/30 rounded-lg border border-slate-700/30">
              <p className="text-slate-300 whitespace-pre-wrap leading-relaxed">
                {comment.content}
              </p>
            </div>

            {/* Moderation Actions */}
            <div className="space-y-4">
              {selectedComment === comment.id && (
                <div className="space-y-3">
                  <Textarea
                    value={moderationNotes}
                    onChange={(e) => setModerationNotes(e.target.value)}
                    placeholder="Add moderation notes (optional)..."
                    className="bg-slate-800/50 border-slate-600 text-slate-200"
                  />
                </div>
              )}
              
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => handleModeration(comment.id, 'approve')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  disabled={moderateComment.isPending}
                >
                  <Check className="h-4 w-4 mr-2" />
                  Approve
                </Button>
                
                <Button
                  onClick={() => handleModeration(comment.id, 'reject')}
                  variant="destructive"
                  disabled={moderateComment.isPending}
                >
                  <X className="h-4 w-4 mr-2" />
                  Reject
                </Button>
                
                <Button
                  onClick={() => handleModeration(comment.id, 'pin')}
                  variant="outline"
                  className="border-yellow-600 text-yellow-400 hover:bg-yellow-600/20"
                  disabled={moderateComment.isPending}
                >
                  <Pin className="h-4 w-4 mr-2" />
                  Pin
                </Button>
                
                <Button
                  variant="ghost"
                  onClick={() => setSelectedComment(
                    selectedComment === comment.id ? null : comment.id
                  )}
                  className="text-slate-400 hover:text-slate-200"
                >
                  Add Notes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
