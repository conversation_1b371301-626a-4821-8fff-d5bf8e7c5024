import type { NextConfig } from 'next';

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
  openAnalyzer: true,
});

const nextConfig: NextConfig = {
  reactStrictMode: true,
  // Only use standalone output in production
  ...(process.env.NODE_ENV === 'production' && { output: 'standalone' }),
  
  // Enable server actions with proper typing
  experimental: {
    serverActions: {
      bodySizeLimit: '2mb',
    },
    webVitalsAttribution: ['CLS', 'LCP'],
    esmExternals: true,
  },
  
  // CORS headers for development
  headers: async () => {
    if (process.env.NODE_ENV !== 'development') return [];
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ];
  },

  // TypeScript and ESLint configurations
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Webpack configuration
  webpack: (config, { isServer, buildId, dev, defaultLoaders, webpack }) => {
    // Handle Node.js modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        net: false,
        tls: false,
        http2: false,
        dns: false,
        child_process: false,
        dgram: false,
        zlib: false,
        http: false,
        https: false,
        stream: false,
        os: false,
      };
    }

    // Handle Lexical packages
    config.module.rules.push({
      test: /\.m?js$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // Optimize chunks for Lexical
    config.optimization.splitChunks = {
      ...config.optimization.splitChunks,
      cacheGroups: {
        ...config.optimization.splitChunks?.cacheGroups,
        lexical: {
          test: /[\\/]node_modules[\\/](@lexical|lexical)[\\/]/,
          name: 'lexical',
          chunks: 'all',
          priority: 10,
        },
      },
    };



    return config;
  },

  // Transpile necessary packages
  transpilePackages: [
    'lexical',
    '@lexical/react',
    '@lexical/utils',
    '@lexical/rich-text',
    '@lexical/list',
    '@lexical/code',
    '@lexical/link',
    '@lexical/markdown',
    '@lexical/hashtag',
    '@lexical/overflow',
    '@lexical/table',
  ],

  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },


  
  // Image optimization
  images: {
    // Allow images from any HTTPS domain
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
      },
    ],
    // Specific patterns for known domains (optional, for better performance)
    domains: [],
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365,
    // Enable dangerous allow for external images (use with caution)
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
};

export default withBundleAnalyzer(nextConfig);