import { z } from 'zod';
import {
  ProfileSchema,
  UsernameSchema,
  ImageUploadSchema,
  PrivacySettingsSchema,
  ProfileUpdateSchema,
  type ProfileInput,
  type ProfileValidationResult,
  type ImageUploadInput,
  type PrivacySettings
} from '@/lib/validations/profile';
import type { UserProfile } from '@/lib/types/profile';

/**
 * Comprehensive profile data validation
 * Validates all profile fields and returns detailed error information
 */
export function validateProfileData(data: Partial<ProfileInput>): ProfileValidationResult {
  try {
    // Use partial schema for updates
    const result = ProfileUpdateSchema.safeParse(data);
    
    if (result.success) {
      return {
        valid: true,
        errors: []
      };
    }

    // Map Zod errors to our format
    const errors = result.error.errors.map(error => ({
      field: error.path.join('.'),
      message: error.message,
      code: error.code
    }));

    return {
      valid: false,
      errors
    };
  } catch (error) {
    return {
      valid: false,
      errors: [{
        field: 'general',
        message: 'Erro interno de validação',
        code: 'VALIDATION_ERROR'
      }]
    };
  }
}

/**
 * Username-specific validation with detailed feedback
 * Includes checks for reserved words, format, and length
 */
export function validateUsername(username: string): ProfileValidationResult {
  try {
    const result = UsernameSchema.safeParse(username);
    
    if (result.success) {
      return {
        valid: true,
        errors: []
      };
    }

    const errors = result.error.errors.map(error => ({
      field: 'username',
      message: error.message,
      code: error.code
    }));

    return {
      valid: false,
      errors
    };
  } catch (error) {
    return {
      valid: false,
      errors: [{
        field: 'username',
        message: 'Erro na validação do username',
        code: 'USERNAME_VALIDATION_ERROR'
      }]
    };
  }
}

/**
 * Image upload validation for avatars and banners
 * Checks file size, type, and other constraints
 */
export function validateImageUpload(
  file: File, 
  type: 'avatar' | 'banner' = 'avatar'
): ProfileValidationResult {
  try {
    // Different size limits for different image types
    const maxSize = type === 'avatar' ? 2 * 1024 * 1024 : 10 * 1024 * 1024; // 2MB vs 10MB
    
    const uploadData: ImageUploadInput = {
      file,
      maxSize,
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
    };

    const result = ImageUploadSchema.safeParse(uploadData);
    
    if (result.success) {
      return {
        valid: true,
        errors: []
      };
    }

    const errors = result.error.errors.map(error => ({
      field: 'file',
      message: error.message,
      code: error.code
    }));

    return {
      valid: false,
      errors
    };
  } catch (error) {
    return {
      valid: false,
      errors: [{
        field: 'file',
        message: 'Erro na validação do arquivo',
        code: 'FILE_VALIDATION_ERROR'
      }]
    };
  }
}

/**
 * Privacy settings validation
 * Ensures all privacy settings are valid and consistent
 */
export function validatePrivacySettings(settings: Partial<PrivacySettings>): ProfileValidationResult {
  try {
    const result = PrivacySettingsSchema.partial().safeParse(settings);
    
    if (result.success) {
      return {
        valid: true,
        errors: []
      };
    }

    const errors = result.error.errors.map(error => ({
      field: error.path.join('.'),
      message: error.message,
      code: error.code
    }));

    return {
      valid: false,
      errors
    };
  } catch (error) {
    return {
      valid: false,
      errors: [{
        field: 'privacy_settings',
        message: 'Erro na validação das configurações de privacidade',
        code: 'PRIVACY_VALIDATION_ERROR'
      }]
    };
  }
}

/**
 * Helper function to validate URL format
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Helper function to validate image URL by checking if it loads
 */
export async function validateImageUrl(url: string): Promise<boolean> {
  if (!validateUrl(url)) {
    return false;
  }

  try {
    const response = await fetch(url, { method: 'HEAD' });
    const contentType = response.headers.get('content-type');
    return response.ok && contentType?.startsWith('image/') === true;
  } catch {
    return false;
  }
}

/**
 * Helper function to check if username contains only allowed characters
 */
export function isValidUsernameFormat(username: string): boolean {
  const usernameRegex = /^[a-zA-Z0-9_-]+$/;
  return usernameRegex.test(username);
}

/**
 * Helper function to check if username is reserved
 */
export function isReservedUsername(username: string): boolean {
  const reservedUsernames = [
    'admin', 'api', 'www', 'root', 'support', 'help', 'about', 'contact',
    'terms', 'privacy', 'dashboard', 'settings', 'profile', 'user', 'users',
    'game', 'games', 'review', 'reviews', 'moderator', 'mod', 'staff'
  ];
  
  return reservedUsernames.includes(username.toLowerCase());
}

/**
 * Helper function to validate bio content
 */
export function validateBio(bio: string): ProfileValidationResult {
  if (bio.length > 500) {
    return {
      valid: false,
      errors: [{
        field: 'bio',
        message: 'Bio deve ter no máximo 500 caracteres',
        code: 'BIO_TOO_LONG'
      }]
    };
  }

  return {
    valid: true,
    errors: []
  };
}

/**
 * Helper function to validate display name
 */
export function validateDisplayName(displayName: string): ProfileValidationResult {
  if (!displayName || displayName.trim().length === 0) {
    return {
      valid: false,
      errors: [{
        field: 'display_name',
        message: 'Nome de exibição é obrigatório',
        code: 'DISPLAY_NAME_REQUIRED'
      }]
    };
  }

  if (displayName.length > 100) {
    return {
      valid: false,
      errors: [{
        field: 'display_name',
        message: 'Nome de exibição deve ter no máximo 100 caracteres',
        code: 'DISPLAY_NAME_TOO_LONG'
      }]
    };
  }

  return {
    valid: true,
    errors: []
  };
}

/**
 * Comprehensive profile validation that checks all fields
 */
export function validateCompleteProfile(profile: UserProfile): ProfileValidationResult {
  const errors: Array<{ field: string; message: string; code: string }> = [];

  // Validate username
  const usernameValidation = validateUsername(profile.userName || '');
  if (!usernameValidation.valid) {
    errors.push(...usernameValidation.errors);
  }

  // Validate display name
  const displayNameValidation = validateDisplayName(profile.displayName || '');
  if (!displayNameValidation.valid) {
    errors.push(...displayNameValidation.errors);
  }

  // Validate bio if present
  if (profile.bio) {
    const bioValidation = validateBio(profile.bio);
    if (!bioValidation.valid) {
      errors.push(...bioValidation.errors);
    }
  }

  // Validate URLs if present
  if (profile.website && !validateUrl(profile.website)) {
    errors.push({
      field: 'website',
      message: 'URL do website é inválida',
      code: 'INVALID_WEBSITE_URL'
    });
  }

  if (profile.avatarUrl && !validateUrl(profile.avatarUrl)) {
    errors.push({
      field: 'avatarUrl',
      message: 'URL do avatar é inválida',
      code: 'INVALID_AVATAR_URL'
    });
  }

  if (profile.bannerUrl && !validateUrl(profile.bannerUrl)) {
    errors.push({
      field: 'bannerUrl',
      message: 'URL do banner é inválida',
      code: 'INVALID_BANNER_URL'
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
