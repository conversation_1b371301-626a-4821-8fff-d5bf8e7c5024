'use client';

// Admin Breadcrumb Component
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.2

import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface AdminBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function AdminBreadcrumb({ items, className }: AdminBreadcrumbProps) {
  // Always include Admin Dashboard as the first item
  const allItems: BreadcrumbItem[] = [
    { label: 'Admin Dashboard', href: '/admin' },
    ...items
  ];

  return (
    <nav 
      className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}
      aria-label="Breadcrumb"
    >
      <Home className="h-4 w-4" />
      
      {allItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          )}
          
          {item.href && index < allItems.length - 1 ? (
            <Link
              href={item.href}
              className="hover:text-primary transition-colors font-medium"
            >
              {item.label}
            </Link>
          ) : (
            <span 
              className={cn(
                "font-medium",
                index === allItems.length - 1 
                  ? "text-primary" 
                  : "text-muted-foreground"
              )}
            >
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

export default AdminBreadcrumb;
