'use client';
// Complete Supabase Review Hooks Implementation
// Real-time data fetching with React Query integration

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getUserReviews, getReviewBySlug, searchReviews } from '@/lib/review-service';
import { useAuthContext } from '@/contexts/auth-context';
import type { Review } from '@/lib/types';
import type { ReviewFilters } from '@/lib/review-service';

export interface UseUserReviewsOptions {
  realTime?: boolean;
  sortBy?: 'publishDate' | 'createdAt' | 'title' | 'overallScore';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  status?: string;
}

export interface UseUserReviewsReturn {
  reviews: Review[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  totalCount: number;
}

export function useUserReviews(
  userId: string | null,
  options: UseUserReviewsOptions = {}
): UseUserReviewsReturn {
  const { status } = options;

  const {
    data: reviews = [],
    isLoading: loading,
    error,
    refetch
  } = useQuery<Review[], Error>({
    queryKey: ['userReviews', userId, status],
    queryFn: async () => {
      if (!userId) return [];
      return getUserReviews(userId, status);
    },
    enabled: !!userId,
    staleTime: 30000, // Cache for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });

  // Sort reviews based on options
  const sortedReviews = [...(reviews || [])].sort((a, b) => {
    const { sortBy = 'createdAt', sortOrder = 'desc' } = options;

    let aValue: any, bValue: any;

    switch (sortBy) {
      case 'publishDate':
        aValue = a.publishDate ? new Date(a.publishDate).getTime() : 0;
        bValue = b.publishDate ? new Date(b.publishDate).getTime() : 0;
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case 'overallScore':
        aValue = a.overallScore;
        bValue = b.overallScore;
        break;
      default:
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Apply limit if specified
  const limitedReviews = options.limit ? sortedReviews.slice(0, options.limit) : sortedReviews;

  const refetchReviews = useCallback(async () => {
    await refetch();
  }, [refetch]);

  return {
    reviews: limitedReviews,
    loading,
    error: error?.message || null,
    refetch: refetchReviews,
    totalCount: (reviews || []).length
  };
}

// Hook for fetching a single review by slug
export function useReview(slug: string, trackView: boolean = true) {
  return useQuery<Review | null, Error>({
    queryKey: ['review', slug],
    queryFn: async () => {
      if (!slug) return null;
      return getReviewBySlug(slug, trackView);
    },
    enabled: !!slug && trackView, // Only enable when slug exists and trackView is true
    staleTime: 1000, // Cache for 1 second (for testing view count updates)
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}

// Hook for review search with filters
export function useReviewSearch(query: string, filters?: ReviewFilters) {
  return useQuery<Review[], Error>({
    queryKey: ['reviewSearch', query, filters],
    queryFn: async () => {
      if (!query.trim() && !filters) return [];
      return searchReviews(query, filters);
    },
    enabled: !!(query.trim() || filters),
    staleTime: 30000, // Cache for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });
}

// Hook for managing review mutations (create, update, delete)
export function useReviewMutations() {
  const queryClient = useQueryClient();
  const { user } = useAuthContext();

  const invalidateReviewQueries = useCallback(() => {
    // Invalidate all review-related queries to refresh data
    queryClient.invalidateQueries({ queryKey: ['userReviews'] });
    queryClient.invalidateQueries({ queryKey: ['review'] });
    queryClient.invalidateQueries({ queryKey: ['reviewSearch'] });
  }, [queryClient]);

  const createReview = useCallback(async (formData: any) => {
    if (!user?.uid) {
      throw new Error('User must be authenticated to create reviews');
    }

    const { createReview: createReviewService } = await import('@/lib/review-service');
    const result = await createReviewService(formData, user.uid);

    if (result.success) {
      invalidateReviewQueries();
    }

    return result;
  }, [user?.uid, invalidateReviewQueries]);

  const updateReview = useCallback(async (reviewId: string, formData: any) => {
    if (!user?.uid) {
      throw new Error('User must be authenticated to update reviews');
    }

    const { updateReview: updateReviewService } = await import('@/lib/review-service');
    const result = await updateReviewService(reviewId, formData, user.uid);

    if (result.success) {
      invalidateReviewQueries();
    }

    return result;
  }, [user?.uid, invalidateReviewQueries]);

  const deleteReview = useCallback(async (reviewId: string) => {
    if (!user?.uid) {
      throw new Error('User must be authenticated to delete reviews');
    }

    const { deleteReview: deleteReviewService } = await import('@/lib/review-service');
    const result = await deleteReviewService(reviewId, user.uid);

    if (result.success) {
      invalidateReviewQueries();
    }

    return result;
  }, [user?.uid, invalidateReviewQueries]);

  return {
    createReview,
    updateReview,
    deleteReview,
    invalidateQueries: invalidateReviewQueries
  };
}

// Hook for review analytics
export function useReviewAnalytics(reviewId: string, dateRange?: { start: Date; end: Date }) {
  return useQuery({
    queryKey: ['reviewAnalytics', reviewId, dateRange],
    queryFn: async () => {
      const { getReviewAnalytics } = await import('@/lib/review-service');
      return getReviewAnalytics(reviewId, dateRange);
    },
    enabled: !!reviewId,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 15 * 60 * 1000, // Keep in cache for 15 minutes
  });
}

// Hook for real-time review updates (using Supabase subscriptions)
export function useReviewRealtime(reviewId: string) {
  const [review, setReview] = useState<Review | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!reviewId) return;

    let mounted = true;

    // Initial fetch
    getReviewBySlug(reviewId, false)
      .then(data => {
        if (mounted) {
          setReview(data);
          setLoading(false);
        }
      })
      .catch(err => {
        if (mounted) {
          setError(err.message);
          setLoading(false);
        }
      });

    // TODO: Implement Supabase real-time subscription
    // const subscription = supabase
    //   .channel('review-changes')
    //   .on('postgres_changes', {
    //     event: '*',
    //     schema: 'public',
    //     table: 'reviews',
    //     filter: `id=eq.${reviewId}`
    //   }, (payload) => {
    //     // Handle real-time updates
    //   })
    //   .subscribe();

    return () => {
      mounted = false;
      // subscription?.unsubscribe();
    };
  }, [reviewId]);

  return { review, loading, error };
}
