# Guia de Desenvolvimento IA - Sistema Administrativo
**Documento:** Guia Completo para Desenvolvimento IA  
**Objetivo:** Implementar Sistema Admin Supabase  
**Versão:** 1.0  
**Data:** 10/12/2025  

## 🎯 **Instruções para IA Desenvolvedora**

Você é uma IA especializada em desenvolvimento de sistemas administrativos seguros. Este guia contém todos os prompts, checklists e especificações técnicas necessárias para implementar o sistema administrativo Supabase.

### **⚠️ REGRAS CRÍTICAS DE SEGURANÇA**
1. **SEMPRE** verificar permissões admin antes de qualquer operação
2. **SEMPRE** logar ações administrativas para auditoria
3. **NUNCA** expor dados sensíveis em logs ou interfaces
4. **SEMPRE** validar inputs antes de processar
5. **SEMPRE** usar transações para operações críticas

---

## 📋 **SEÇÃO A: SETUP & PREPARAÇÃO**

### **A.1: Verificação de Pré-requisitos**

**AI PROMPT:**
```
Verifique se as seguintes dependências estão funcionando:

1. Database Schema (Fase 1):
   - Tabela 'profiles' existe e está populada
   - Tabela 'reviews' existe com dados
   - Tabela 'comments' existe
   - Funções de database estão criadas

2. RLS Security (Fase 3):
   - Políticas RLS estão ativas
   - Admin users têm permissões corretas
   - Security functions estão operacionais

3. User Profile Services (Fase 4):
   - AuthContext está funcionando
   - useAuthContext hook está disponível
   - isAdmin function está implementada

Execute: Verificar cada item e reportar status
```

**CHECKLIST DE VERIFICAÇÃO:**
- [ ] `SELECT * FROM profiles LIMIT 1` retorna dados
- [ ] `SELECT * FROM reviews LIMIT 1` retorna dados  
- [ ] RLS está habilitado: `SELECT * FROM pg_policies`
- [ ] Admin user existe: `SELECT * FROM profiles WHERE is_admin = true`
- [ ] AuthContext está em `/src/contexts/auth-context.tsx`
- [ ] Function `isAdmin` retorna boolean corretamente

---

## 🚀 **SEÇÃO B: PROMPTS DE IMPLEMENTAÇÃO**

### **🏗️ SPRINT 1: FUNDAÇÃO ADMINISTRATIVA**

#### **B.1.1: Admin Authentication Implementation**

**AI PROMPT:**
```
Implemente autenticação administrativa segura:

TASK: Remover "Access Denied" de /src/app/admin/page.tsx e implementar verificação real de admin

REQUIREMENTS:
- Verificar se user está logado via AuthContext
- Verificar se user.is_admin === true
- Mostrar loading enquanto verifica
- Redirect não-admins para homepage
- Mostrar AdminDashboard se autorizado

SECURITY: 
- Double-check permissions server-side
- Log todas as tentativas de acesso admin
- Rate limit tentativas de acesso

Execute: Implementar admin authentication com security logging
```

**VALIDATION CHECKLIST:**
- [ ] Admin user pode acessar `/admin` sem erro
- [ ] Non-admin é redirecionado para homepage
- [ ] Loading state é mostrado durante verificação
- [ ] Logs de acesso são criados
- [ ] Error handling está funcionando

#### **B.1.2: User Management System**

**AI PROMPT:**
```
Implemente sistema completo de gestão de usuários:

TASK: Criar /src/lib/admin/userService.ts e interfaces admin

FUNCTIONS TO IMPLEMENT:
1. getUserList(page, limit, filters) - Lista paginada
2. searchUsers(query) - Busca por texto
3. updateUserAsAdmin(id, data) - Update com privilégios admin
4. toggleUserSuspension(id, suspended, reason) - Suspender/reativar
5. toggleAdminPrivileges(id, isAdmin) - Grant/revoke admin

SECURITY REQUIREMENTS:
- Verificar permissões admin em cada função
- Usar RLS policies do Supabase
- Log todas as operações
- Validate inputs rigorosamente

Execute: Implementar user management functions with audit logging
```

#### **B.1.3: Content Moderation System**

**AI PROMPT:**
```
Desenvolva sistema de moderação de conteúdo:

TASK: Criar sistema de moderação de reviews e comments

FEATURES:
- Queue de reviews para moderação
- Interface de edição admin de reviews
- Sistema de flagging de conteúdo
- Moderação batch de múltiplos itens
- Histórico de moderação

FUNCTIONS:
- getReviewsForModeration(status, priority)
- moderateReview(id, action, reason)
- getFlaggedComments()
- moderateComment(id, action, reason)

Execute: Implementar content moderation engine
```

#### **B.1.4: Analytics Dashboard**

**AI PROMPT:**
```
Crie dashboard completo de analytics:

TASK: Implementar analytics administrativo

METRICS:
- User Growth (daily, weekly, monthly)
- Content Creation Rate
- User Engagement (reviews, comments, likes)
- Popular Content (most viewed/liked reviews)
- System Performance (load times, errors)

COMPONENTS:
- AnalyticsDashboard.tsx - Dashboard principal
- MetricsCard.tsx - Cards de métricas
- AnalyticsChart.tsx - Charts interativos

Execute: Implementar analytics system with real-time updates
```

---

## ✅ **SEÇÃO C: CHECKLISTS DE VALIDAÇÃO**

### **C.1: Checklist de Segurança**

**AUTHENTICATION:**
- [ ] Middleware bloqueia usuários não-admin
- [ ] Admin permissions são re-verificadas
- [ ] Session timeout é tratado corretamente
- [ ] Logs de acesso são completos

**AUTHORIZATION:**
- [ ] RLS policies funcionam corretamente
- [ ] Admin users só modificam dados permitidos
- [ ] Privilege escalation é prevenida
- [ ] Cross-user data access é bloqueado

**DATA PROTECTION:**
- [ ] Inputs são validados e sanitizados
- [ ] SQL injection é prevenida
- [ ] XSS é prevenido
- [ ] Sensitive data não vaza em logs

### **C.2: Checklist de Performance**

**LOAD TIMES:**
- [ ] Admin dashboard < 2 segundos
- [ ] User search < 500ms
- [ ] Analytics refresh < 3 segundos
- [ ] Content moderation < 1 segundo

**OPTIMIZATION:**
- [ ] Database queries são otimizadas
- [ ] Indexes são usados efetivamente
- [ ] Caching reduz database load
- [ ] API responses são otimizadas

### **C.3: Checklist de Funcionalidade**

**USER MANAGEMENT:**
- [ ] CRUD operations funcionam
- [ ] Search e filtering funcionam
- [ ] Bulk operations funcionam
- [ ] Suspension/activation funciona
- [ ] Admin privilege management funciona

**CONTENT MODERATION:**
- [ ] Content flagging funciona
- [ ] Moderation actions são aplicadas
- [ ] Batch moderation funciona
- [ ] Content history é mantido

**ANALYTICS:**
- [ ] Metrics são calculadas corretamente
- [ ] Charts mostram dados precisos
- [ ] Date filtering funciona
- [ ] Export funciona

---

## 🛠️ **SEÇÃO D: TEMPLATES DE CÓDIGO**

### **D.1: Security-First Function Template**

```typescript
// TEMPLATE for admin functions
async function adminFunction(userId: string, data: any) {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(userId);
  if (!isAuthorized) {
    throw new Error('Unauthorized admin access');
  }

  // 2. Validate input data
  const validatedData = validateInput(data, schema);
  
  // 3. Execute operation in transaction
  const result = await supabase.rpc('admin_operation', {
    user_id: userId,
    data: validatedData
  });

  // 4. Log admin action
  await logAdminAction(userId, 'function_name', data, result);

  // 5. Return result
  return result;
}
```

### **D.2: Admin Component Template**

```tsx
// TEMPLATE for admin components
'use client';

import { useState, useEffect } from 'react';
import { useAuthContext } from '@/contexts/auth-context';

interface ComponentProps {
  // Define props
}

export default function AdminComponent({ }: ComponentProps) {
  const { user, isAdmin } = useAuthContext();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  // Security check
  if (!isAdmin) {
    return <div>Access Denied</div>;
  }

  // Implementation
  return (
    <div>
      {/* Component content */}
    </div>
  );
}
```

---

## 🚨 **SEÇÃO E: TROUBLESHOOTING**

### **E.1: Problemas Comuns**

#### **"Access Denied" persiste**
**Diagnóstico:**
- Verificar se `isAdmin` retorna true
- Verificar se user tem `is_admin = true` no database
- Verificar RLS policies

**Solução:**
```typescript
console.log('User:', user);
console.log('isAdmin:', isAdmin);
console.log('Database check:', await checkAdminInDB(user.id));
```

#### **Performance degradada**
**Diagnóstico:**
- Verificar execution plans das queries
- Monitorar query times
- Verificar se indexes estão sendo usados

**Solução:**
- Adicionar paginação
- Implementar indexes
- Otimizar queries

### **E.2: Rollback Procedures**

#### **Emergency Rollback:**
1. **Disable admin access:**
   ```typescript
   if (EMERGENCY_DISABLE_ADMIN) {
     return NextResponse.redirect('/maintenance');
   }
   ```

2. **Revert database changes:**
   ```sql
   SELECT * FROM admin_audit_log 
   WHERE created_at > '2025-12-10' 
   ORDER BY created_at DESC;
   ```

---

## 📊 **SEÇÃO F: CRITÉRIOS DE SUCESSO**

### **F.1: Critérios de Conclusão**

**IMPLEMENTAÇÃO:**
- [ ] 100% dos admin endpoints funcionais
- [ ] 0 security vulnerabilities
- [ ] Performance targets atingidos
- [ ] 100% test coverage
- [ ] 90+ accessibility score

**QUALIDADE:**
- [ ] Código segue padrões
- [ ] Documentation completa
- [ ] Error handling robusto
- [ ] Security audit passou
- [ ] User experience otimizada

---

## 📝 **PRÓXIMOS PASSOS PARA IA**

### **Sequência de Implementação:**

1. **VERIFICAÇÃO (30 min):**
   - Execute checklist A.1
   - Confirme dependências
   - Setup ambiente

2. **SPRINT 1 (8 horas):**
   - Implemente B.1.1 (Authentication)
   - Valide com checklist C.1
   - Teste segurança

3. **SPRINT 2 (8 horas):**
   - Implemente B.1.2 (User Management)  
   - Valide com checklist C.2
   - Teste performance

4. **SPRINT 3 (8 horas):**
   - Implemente B.1.3 (Content Moderation)
   - Implemente B.1.4 (Analytics)
   - Valide funcionalidade completa

### **Para cada task:**
1. Leia o prompt específico
2. Implemente usando templates
3. Valide com checklists
4. Execute testes de segurança
5. Documente no log

**Status Atual:**
- [ ] Sprint 1: Fundação (0%)
- [ ] Sprint 2: User Management (0%)
- [ ] Sprint 3: Content & Analytics (0%)
- [ ] Sprint 4: System Tools (0%)

**Próximo Passo:** Iniciar verificação de dependências (Seção A.1)

---
**Documento Criado por:** Claude (Senior Software Developer)  
**Para Uso por:** IA Desenvolvedora  
**Objetivo:** Implementação completa e segura do sistema administrativo
