import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    
    if (!query) {
      return NextResponse.json({ games: [] });
    }

    const supabase = await createServerClient();
    
    // Search for games with similar names using ILIKE for case-insensitive partial matching
    const { data: games, error } = await supabase
      .from('games')
      .select('id, name, slug')
      .or(`name.ilike.%${query}%,name.ilike.${query}%`)
      .limit(5);

    if (error) {
      console.error('Error searching games:', error);
      return NextResponse.json({ games: [] });
    }

    return NextResponse.json({ games: games || [] });
  } catch (error) {
    console.error('Error in game search:', error);
    return NextResponse.json({ games: [] });
  }
}