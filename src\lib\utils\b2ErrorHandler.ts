// src/lib/utils/b2ErrorHandler.ts
export interface B2Error {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
}

export class B2ServiceError extends Error {
  public readonly code: string;
  public readonly retryable: boolean;
  public readonly details?: any;

  constructor(code: string, message: string, retryable: boolean = false, details?: any) {
    super(message);
    this.name = 'B2ServiceError';
    this.code = code;
    this.retryable = retryable;
    this.details = details;
  }
}

export function handleB2Error(error: any): B2Error {
  console.error('B2 Service Error:', error);

  // AWS SDK specific errors
  if (error.name === 'NoSuchBucket') {
    return {
      code: 'BUCKET_NOT_FOUND',
      message: 'Storage bucket not found',
      retryable: false,
    };
  }

  if (error.name === 'AccessDenied') {
    return {
      code: 'ACCESS_DENIED',
      message: 'Access denied to storage service',
      retryable: false,
    };
  }

  if (error.name === 'NetworkingError' || error.code === 'ECONNRESET') {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network connection error',
      retryable: true,
    };
  }

  // Generic errors
  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unknown error occurred',
    retryable: false,
    details: error,
  };
}

export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const b2Error = handleB2Error(error);

      if (!b2Error.retryable || i === maxRetries) {
        throw new B2ServiceError(b2Error.code, b2Error.message, b2Error.retryable, b2Error.details);
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  throw lastError;
}
