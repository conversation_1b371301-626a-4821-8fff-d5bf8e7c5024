// src/components/review-form/lexical/hooks/useEditorState.ts
'use client';

import { useState, useCallback, useRef } from 'react';
import { EditorState } from 'lexical';

interface UseEditorStateProps {
  initialState?: string | null;
  onChange?: (editorState: EditorState) => void;
}

export function useEditorState({ initialState, onChange }: UseEditorStateProps) {
  const [currentState, setCurrentState] = useState<string | null>(initialState || null);
  const editorStateRef = useRef<EditorState | null>(null);
  const isUpdatingRef = useRef(false);

  const handleEditorChange = useCallback((editorState: EditorState) => {
    // Prevent infinite loops
    if (isUpdatingRef.current) return;
    
    try {
      editorState.read(() => {
        const jsonState = JSON.stringify(editorState.toJSON());
        
        // Only update if the state actually changed
        if (jsonState !== currentState) {
          setCurrentState(jsonState);
          editorStateRef.current = editorState;
          
          // Call the external onChange handler
          if (onChange) {
            onChange(editorState);
          }
        }
      });
    } catch (error) {
      console.error("Error processing editor state:", error);
    }
  }, [currentState, onChange]);

  const updateState = useCallback((newState: string | null) => {
    isUpdatingRef.current = true;
    setCurrentState(newState);
    setTimeout(() => {
      isUpdatingRef.current = false;
    }, 0);
  }, []);

  return {
    currentState,
    handleEditorChange,
    updateState,
    editorStateRef: editorStateRef.current
  };
}
