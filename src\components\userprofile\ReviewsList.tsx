'use client';

import React, { useState } from 'react';
import ReviewCard from './ReviewCard';

interface ReviewsListProps {
  reviews: any[];
  theme: any;
  maxItems?: number; // Optional limit for list view
}

const ReviewsList: React.FC<ReviewsListProps> = ({ reviews, theme, maxItems }) => {
  const [imageColors, setImageColors] = useState<Record<string, { isDark: boolean; rgb: string }>>({});

  // Handle image color updates from individual cards
  const handleImageColorChange = (reviewId: string, colorData: { isDark: boolean; rgb: string }) => {
    setImageColors(prev => ({
      ...prev,
      [reviewId]: colorData
    }));
  };

  // Limit reviews if maxItems is specified
  const displayReviews = maxItems ? reviews.slice(0, maxItems) : reviews;

  return (
    <div className="space-y-8 w-full">
      {displayReviews.map((review, index) => (
        <ReviewCard
          key={review.id}
          review={review}
          theme={theme}
          index={index}
          totalReviews={displayReviews.length}
          variant="list"
          onImageColorChange={handleImageColorChange}
        />
      ))}
    </div>
  );
};

ReviewsList.displayName = 'ReviewsList';

export default ReviewsList;