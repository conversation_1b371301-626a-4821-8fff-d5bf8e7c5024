import React from 'react';

const GamingMonitorIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <rect x="2" y="4" width="20" height="12" rx="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="4" y="6" width="16" height="8" rx="1"/>
    <path d="M7 9h2v2H7zm3 0h2v2h-2zm3 0h2v2h-2z"/>
    <circle cx="18" cy="8" r="0.5"/>
    <circle cx="18" cy="10" r="0.5"/>
    <circle cx="18" cy="12" r="0.5"/>
    <path d="M8 18h8"/>
    <path d="M10 16v4"/>
    <path d="M14 16v4"/>
    <rect x="9" y="20" width="6" height="1"/>
  </svg>
);

export default GamingMonitorIcon;