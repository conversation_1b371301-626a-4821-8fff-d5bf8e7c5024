import React from 'react';

const DigitalClockIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <rect x="2" y="6" width="20" height="12" rx="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="4" y="8" width="16" height="8" rx="1"/>
    <path d="M6 10h2v4H6zm3 0h1v1H9zm0 2h1v1H9zm0 2h1v1H9z"/>
    <path d="M11 10h2v4h-2zm3 0h1v1h-1zm0 2h1v1h-1zm0 2h1v1h-1z"/>
    <path d="M16 10h2v4h-2z"/>
    <circle cx="10" cy="11" r="0.3"/>
    <circle cx="10" cy="13" r="0.3"/>
    <circle cx="15" cy="11" r="0.3"/>
    <circle cx="15" cy="13" r="0.3"/>
    <rect x="8" y="4" width="1" height="2"/>
    <rect x="15" y="4" width="1" height="2"/>
  </svg>
);

export default DigitalClockIcon;