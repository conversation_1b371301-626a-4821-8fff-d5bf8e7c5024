#### Search Suggestions Component

```typescript
// File: /src/components/search/SearchSuggestions.tsx
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, TrendingUp, ArrowRight } from "lucide-react";

interface SearchSuggestionsProps {
  recentSearches: string[];
  onSelectQuery: (query: string) => void;
}

/**
 * Component to display search suggestions and recent searches
 */
export function SearchSuggestions({ recentSearches, onSelectQuery }: SearchSuggestionsProps) {
  // State for trending searches (would be fetched from API)
  const [trendingSearches, setTrendingSearches] = useState<string[]>([]);
  
  /**
   * Fetch trending searches
   */
  useEffect(() => {
    const fetchTrendingSearches = async () => {
      try {
        // In a real implementation, you would fetch from your API
        const response = await fetch('/api/trending-searches');
        if (!response.ok) {
          throw new Error('Failed to fetch trending searches');
        }
        
        const data = await response.json();
        setTrendingSearches(data.trending);
      } catch (error) {
        console.error('Error fetching trending searches:', error);
        // Fallback to default trending searches
        setTrendingSearches([
          "Cyberpunk 2077",
          "The Legend of Zelda",
          "PlayStation 5",
          "Xbox Series X",
          "Best gaming laptops",
        ]);
      }
    };
    
    // For demonstration, we'll use mock data
    setTrendingSearches([
      "Cyberpunk 2077",
      "The Legend of Zelda",
      "PlayStation 5",
      "Xbox Series X",
      "Best gaming laptops",
    ]);
    
    // In production, uncomment this to fetch real data
    // fetchTrendingSearches();
  }, []);
  
  // If no recent or trending searches, don't render component
  if (recentSearches.length === 0 && trendingSearches.length === 0) {
    return null;
  }
  
  return (
    <Card className="w-full">
      <CardContent className="py-4">
        <div className="space-y-4">
          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div>
              <h3 className="font-medium flex items-center mb-2">
                <Clock className="h-4 w-4 mr-2" />
                Recent Searches
              </h3>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((query, index) => (
                  <Button
                    key={`recent-${index}`}
                    variant="outline"
                    size="sm"
                    onClick={() => onSelectQuery(query)}
                    className="flex items-center"
                  >
                    {query}
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                ))}
              </div>
            </div>
          )}
          
          {/* Trending Searches */}
          {trendingSearches.length > 0 && (
            <div>
              <h3 className="font-medium flex items-center mb-2">
                <TrendingUp className="h-4 w-4 mr-2" />
                Trending
              </h3>
              <div className="flex flex-wrap gap-2">
                {trendingSearches.map((query, index) => (
                  <Button
                    key={`trending-${index}`}
                    variant="outline"
                    size="sm"
                    onClick={() => onSelectQuery(query)}
                    className="flex items-center"
                  >
                    {query}
                    <ArrowRight className="ml-1 h-3 w-3" />
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```
