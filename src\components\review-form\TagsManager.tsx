// src/components/review-form/TagsManager.tsx
// Wrapper component to manage tags with the new enhanced system

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Hash } from 'lucide-react';
import EnhancedTagInput from './EnhancedTagInput';

interface TagsManagerProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  disabled?: boolean;
}

export const TagsManager: React.FC<TagsManagerProps> = ({
  tags,
  onTagsChange,
  disabled = false
}) => {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
      className="bg-slate-900/60 border border-slate-700/50 rounded-lg"
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-md bg-slate-800/60">
            <Hash className="h-4 w-4 text-slate-400" />
          </div>
          <div>
            <div className="font-mono text-sm">
              <span className="text-violet-400">&lt;</span>
              <span className="text-slate-300">Content Tags</span>
              <span className="text-violet-400">/&gt;</span>
            </div>
            <p className="text-slate-500 text-xs mt-1">
              Add tags to help others discover your review content
            </p>
          </div>
        </div>

        {/* Enhanced Tag Input Component */}
        <EnhancedTagInput
          tags={tags}
          onTagsChange={onTagsChange}
          maxTags={20}
          placeholder="Add tags to help people discover your review..."
          disabled={disabled}
        />
      </div>
    </motion.div>
  );
};

export default TagsManager;