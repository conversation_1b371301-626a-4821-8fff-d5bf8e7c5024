'use client';

import Link from 'next/link';
import Logo from '@/components/layout/Logo';
import { useAuthContext } from '@/contexts/auth-context';
import { useState, useRef } from 'react';
import GameStyleUserMenu from './GameStyleUserMenu';
import BackgroundDimmerSlider from '@/components/ui/BackgroundDimmerSlider';
import '@/components/style/navColors.css';
import { motion } from 'framer-motion';

const Navbar = () => {
  const { user } = useAuthContext();
  const [isGameMenuOpen, setIsGameMenuOpen] = useState(false);

  // Refs
  const userIconRef = useRef<HTMLButtonElement>(null);
  const gameMenuRef = useRef<HTMLDivElement>(null);

  return (
    <>
      <motion.nav
        className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 backdrop-blur-md ${
          isGameMenuOpen ? 'nav-container-active' : 'nav-container'
        }`}
      >
        <div className="w-full px-4 flex items-center justify-between h-14">
          {/* Logo */}
          <motion.div
            className="flex-shrink-0 transition-all duration-300"
            initial={{ opacity: 0 }}
            animate={{
              opacity: isGameMenuOpen ? 0 : 1,
              scale: isGameMenuOpen ? 0.95 : 1,
              x: isGameMenuOpen ? 10 : 0,
              visibility: isGameMenuOpen ? "hidden" : "visible"
            }}
            transition={{
              duration: 0.3,
              ease: [0.16, 1, 0.3, 1]
            }}
          >
            <Link href="/" className="gaming-nav-link">
              <Logo size="sm" noLink />
            </Link>
          </motion.div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/reviews" className="gaming-nav-link terminal-text">
              Reviews
            </Link>
            <Link href="/games" className="gaming-nav-link terminal-text">
              Games
            </Link>
            <Link href="/community" className="gaming-nav-link terminal-text">
              Community
            </Link>
            <Link href="/steamgriddb-test" className="gaming-nav-link terminal-text">
              SteamGridDB
            </Link>
          </div>

          {/* User Authentication */}
          <div className="flex items-center space-x-4">
            {/* Background Dimmer Slider */}
            <BackgroundDimmerSlider className="hidden md:flex" />

            <motion.button
              type="button"
              ref={userIconRef}
              onClick={() => setIsGameMenuOpen(!isGameMenuOpen)}
              className={`themed-hamburger-menu ${isGameMenuOpen ? 'active' : ''}`}
              aria-label="Toggle menu"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="hamburger-container">
                <motion.div
                  className="hamburger-line hamburger-line-1"
                  animate={{
                    rotate: isGameMenuOpen ? 45 : 0,
                    y: isGameMenuOpen ? 8 : 0
                  }}
                  transition={{ duration: 0.3 }}
                />
                <motion.div
                  className="hamburger-line hamburger-line-2"
                  animate={{
                    opacity: isGameMenuOpen ? 0 : 1,
                    x: isGameMenuOpen ? -10 : 0
                  }}
                  transition={{ duration: 0.3 }}
                />
                <motion.div
                  className="hamburger-line hamburger-line-3"
                  animate={{
                    rotate: isGameMenuOpen ? -45 : 0,
                    y: isGameMenuOpen ? -8 : 0
                  }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </motion.button>
          </div>
        </div>
      </motion.nav>

      {/* User Menu */}
      <GameStyleUserMenu
        ref={gameMenuRef}
        open={isGameMenuOpen}
        onClose={() => setIsGameMenuOpen(false)}
      />
    </>
  );
};

export default Navbar;
