'use client';

// Link Moderation Settings Component
// Date: 28/06/2025
// Purpose: Admin interface for managing link auto-moderation settings

import React, { useState, useEffect } from 'react';
import { ContentFilter } from '@/lib/security/contentFilter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Shield,
  Plus,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  HelpCircle,
  ExternalLink,
  Globe
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

export function LinkModerationSettings() {
  const [contentFilter] = useState(() => new ContentFilter());
  const [domainLists, setDomainLists] = useState({ allowed: [], blocked: [] });
  const [newDomain, setNewDomain] = useState('');
  const [testUrl, setTestUrl] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [bulkDomains, setBulkDomains] = useState('');

  useEffect(() => {
    // Load current domain lists
    const lists = contentFilter.getDomainLists();
    setDomainLists(lists);
  }, [contentFilter]);

  const handleAddDomain = (type: 'allowed' | 'blocked') => {
    if (!newDomain.trim()) return;

    const domain = newDomain.trim().toLowerCase();
    
    if (type === 'allowed') {
      contentFilter.addAllowedDomain(domain);
    } else {
      contentFilter.addBlockedDomain(domain);
    }

    // Refresh lists
    const lists = contentFilter.getDomainLists();
    setDomainLists(lists);
    setNewDomain('');

    toast({
      title: "Domain added",
      description: `${domain} has been added to the ${type} list.`,
    });
  };

  const handleRemoveDomain = (domain: string, type: 'allowed' | 'blocked') => {
    if (type === 'allowed') {
      contentFilter.removeAllowedDomain(domain);
    }
    // Note: No remove method for blocked domains in current implementation
    
    // Refresh lists
    const lists = contentFilter.getDomainLists();
    setDomainLists(lists);

    toast({
      title: "Domain removed",
      description: `${domain} has been removed from the ${type} list.`,
    });
  };

  const handleTestUrl = () => {
    if (!testUrl.trim()) return;

    const result = contentFilter.checkUrl(testUrl.trim());
    setTestResult(result);
  };

  const handleBulkAdd = (type: 'allowed' | 'blocked') => {
    const domains = bulkDomains
      .split('\n')
      .map(d => d.trim().toLowerCase())
      .filter(d => d.length > 0);

    domains.forEach(domain => {
      if (type === 'allowed') {
        contentFilter.addAllowedDomain(domain);
      } else {
        contentFilter.addBlockedDomain(domain);
      }
    });

    // Refresh lists
    const lists = contentFilter.getDomainLists();
    setDomainLists(lists);
    setBulkDomains('');

    toast({
      title: "Bulk domains added",
      description: `${domains.length} domains added to ${type} list.`,
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'allowed':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'blocked':
        return <XCircle className="h-4 w-4 text-red-400" />;
      case 'suspicious':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />;
      case 'unknown':
        return <HelpCircle className="h-4 w-4 text-slate-400" />;
      default:
        return <Globe className="h-4 w-4 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'allowed':
        return 'bg-green-500/10 text-green-300 border-green-500/30';
      case 'blocked':
        return 'bg-red-500/10 text-red-300 border-red-500/30';
      case 'suspicious':
        return 'bg-yellow-500/10 text-yellow-300 border-yellow-500/30';
      case 'unknown':
        return 'bg-slate-500/10 text-slate-300 border-slate-500/30';
      default:
        return 'bg-slate-500/10 text-slate-300 border-slate-500/30';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="h-6 w-6 text-purple-400" />
        <h2 className="text-xl font-mono text-slate-200">
          <span className="text-purple-400">//</span> Link Auto-Moderation Settings
        </h2>
      </div>

      <Tabs defaultValue="domains" className="space-y-4">
        <TabsList className="bg-slate-800/50 border-slate-700/50">
          <TabsTrigger value="domains">Domain Management</TabsTrigger>
          <TabsTrigger value="test">URL Testing</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Operations</TabsTrigger>
        </TabsList>

        <TabsContent value="domains" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Allowed Domains */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-green-400 font-mono text-sm flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Allowed Domains ({domainLists.allowed.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="example.com"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    className="bg-slate-900/50 border-slate-600/50"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddDomain('allowed')}
                  />
                  <Button
                    onClick={() => handleAddDomain('allowed')}
                    size="sm"
                    className="bg-green-600/20 hover:bg-green-600/30 text-green-300 border border-green-600/30"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {domainLists.allowed.map((domain) => (
                    <div key={domain} className="flex items-center justify-between p-2 bg-slate-900/30 rounded border border-slate-700/30">
                      <span className="text-slate-300 text-sm font-mono">{domain}</span>
                      <Button
                        onClick={() => handleRemoveDomain(domain, 'allowed')}
                        size="sm"
                        variant="ghost"
                        className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Blocked Domains */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-red-400 font-mono text-sm flex items-center gap-2">
                  <XCircle className="h-4 w-4" />
                  Blocked Domains ({domainLists.blocked.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="spam-site.com"
                    value={newDomain}
                    onChange={(e) => setNewDomain(e.target.value)}
                    className="bg-slate-900/50 border-slate-600/50"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddDomain('blocked')}
                  />
                  <Button
                    onClick={() => handleAddDomain('blocked')}
                    size="sm"
                    className="bg-red-600/20 hover:bg-red-600/30 text-red-300 border border-red-600/30"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {domainLists.blocked.map((domain) => (
                    <div key={domain} className="flex items-center justify-between p-2 bg-slate-900/30 rounded border border-slate-700/30">
                      <span className="text-slate-300 text-sm font-mono">{domain}</span>
                      <Badge variant="outline" className="text-xs bg-red-500/10 text-red-300 border-red-500/30">
                        Blocked
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-purple-400 font-mono text-sm flex items-center gap-2">
                <ExternalLink className="h-4 w-4" />
                URL Testing Tool
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="https://example.com/path"
                  value={testUrl}
                  onChange={(e) => setTestUrl(e.target.value)}
                  className="bg-slate-900/50 border-slate-600/50"
                  onKeyPress={(e) => e.key === 'Enter' && handleTestUrl()}
                />
                <Button onClick={handleTestUrl} size="sm">
                  Test URL
                </Button>
              </div>

              {testResult && (
                <div className="p-4 bg-slate-900/30 rounded border border-slate-700/30">
                  <div className="flex items-center gap-3 mb-2">
                    {getStatusIcon(testResult.status)}
                    <Badge variant="outline" className={getStatusColor(testResult.status)}>
                      {testResult.status.toUpperCase()}
                    </Badge>
                  </div>
                  {testResult.reason && (
                    <p className="text-slate-400 text-sm">{testResult.reason}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-blue-400 font-mono text-sm">
                Bulk Domain Operations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="Enter domains, one per line:&#10;example.com&#10;another-site.org&#10;gaming-site.net"
                value={bulkDomains}
                onChange={(e) => setBulkDomains(e.target.value)}
                className="bg-slate-900/50 border-slate-600/50 min-h-32"
              />
              
              <div className="flex gap-2">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button size="sm" className="bg-green-600/20 hover:bg-green-600/30 text-green-300 border border-green-600/30">
                      Add to Allowed
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-slate-900 border-slate-700">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Bulk Add to Allowed List</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will add all entered domains to the allowed list. Are you sure?
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleBulkAdd('allowed')}>
                        Add Domains
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button size="sm" className="bg-red-600/20 hover:bg-red-600/30 text-red-300 border border-red-600/30">
                      Add to Blocked
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="bg-slate-900 border-slate-700">
                    <AlertDialogHeader>
                      <AlertDialogTitle>Bulk Add to Blocked List</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will add all entered domains to the blocked list. Are you sure?
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleBulkAdd('blocked')}>
                        Block Domains
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
