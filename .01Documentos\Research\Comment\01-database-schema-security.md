# Step 1: Database Schema & Security Foundation
## Comment System Implementation Guide - Phase 1

**Date:** 2025-01-20  
**Task:** Comment System Database Foundation  
**Priority:** CRITICAL  
**Estimated Time:** 4-6 hours  

---

## 🎯 Overview & Objectives

This step establishes the foundational database schema and security infrastructure for CriticalPixel's comment system. We're building a custom Supabase-based solution instead of using Remark42 for better integration, cost-effectiveness, and security control.

### Key Objectives:
- [ ] Create comprehensive comment database schema
- [ ] Implement Row Level Security (RLS) policies
- [ ] Set up performance indexes
- [ ] Create database functions for common operations
- [ ] Establish audit logging system
- [ ] Configure rate limiting infrastructure

---

## 📋 Prerequisites

- [ ] Supabase project access with admin privileges
- [ ] Existing CriticalPixel database with reviews table
- [ ] Understanding of current RLS policies
- [ ] Access to Supabase SQL editor

---

## 🗄️ Database Schema Implementation

### Core Tables to Create

#### 1. Comments Table
```sql
-- Main comments table with comprehensive fields
CREATE TABLE IF NOT EXISTS comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
  author_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
  content TEXT NOT NULL CHECK (length(content) >= 1 AND length(content) <= 5000),
  is_deleted BOOLEAN DEFAULT false,
  is_pinned BOOLEAN DEFAULT false,
  is_approved BOOLEAN DEFAULT false,
  upvotes INTEGER DEFAULT 0 CHECK (upvotes >= 0),
  downvotes INTEGER DEFAULT 0 CHECK (downvotes >= 0),
  flag_count INTEGER DEFAULT 0 CHECK (flag_count >= 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  moderated_by UUID REFERENCES auth.users(id),
  moderated_at TIMESTAMP WITH TIME ZONE,
  moderation_notes TEXT,
  author_ip INET,
  user_agent TEXT,
  
  -- Constraints
  CONSTRAINT valid_content_length CHECK (length(trim(content)) > 0),
  CONSTRAINT no_self_reply CHECK (id != parent_id)
);
```

#### 2. Comment Votes Table
```sql
-- Track individual user votes on comments
CREATE TABLE IF NOT EXISTS comment_votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id UUID NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  vote_type TEXT NOT NULL CHECK (vote_type IN ('upvote', 'downvote')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure one vote per user per comment
  UNIQUE(comment_id, user_id)
);
```

#### 3. Comment Moderation Settings Table
```sql
-- Per-review moderation settings
CREATE TABLE IF NOT EXISTS comment_moderation_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  review_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  auto_approve BOOLEAN DEFAULT true,
  require_approval BOOLEAN DEFAULT false,
  allow_anonymous BOOLEAN DEFAULT true,
  max_comment_length INTEGER DEFAULT 1000 CHECK (max_comment_length > 0),
  rate_limit_minutes INTEGER DEFAULT 1 CHECK (rate_limit_minutes > 0),
  blocked_users UUID[] DEFAULT '{}',
  blocked_words TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure one setting per review
  UNIQUE(review_id)
);
```

#### 4. Comment Audit Log Table
```sql
-- Comprehensive audit logging for all comment actions
CREATE TABLE IF NOT EXISTS comment_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id UUID REFERENCES comments(id) ON DELETE SET NULL,
  action_type TEXT NOT NULL CHECK (action_type IN (
    'create', 'update', 'delete', 'approve', 'reject', 
    'pin', 'unpin', 'flag', 'vote', 'moderate'
  )),
  performed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  target_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  old_values JSONB,
  new_values JSONB,
  reason TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

---

## 🔒 Row Level Security (RLS) Policies

### Comments Table Policies
```sql
-- Enable RLS
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

-- Public read access for approved comments
CREATE POLICY "comments_public_read" ON comments
  FOR SELECT USING (
    is_approved = true AND is_deleted = false
  );

-- Authors can read their own comments
CREATE POLICY "comments_author_read" ON comments
  FOR SELECT USING (
    auth.uid() = author_id
  );

-- Review owners can read all comments on their reviews
CREATE POLICY "comments_owner_read" ON comments
  FOR SELECT USING (
    auth.uid() IN (
      SELECT author_id FROM reviews WHERE id = comments.review_id
    )
  );

-- Authenticated users can create comments
CREATE POLICY "comments_authenticated_create" ON comments
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    auth.uid() = author_id AND
    EXISTS (SELECT 1 FROM reviews WHERE id = review_id AND status = 'published')
  );

-- Authors can update their own comments (within time limit)
CREATE POLICY "comments_author_update" ON comments
  FOR UPDATE USING (
    auth.uid() = author_id AND
    created_at > now() - interval '15 minutes'
  );

-- Review owners can moderate comments on their reviews
CREATE POLICY "comments_owner_moderate" ON comments
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT author_id FROM reviews WHERE id = comments.review_id
    )
  );
```

### Comment Votes Table Policies
```sql
-- Enable RLS
ALTER TABLE comment_votes ENABLE ROW LEVEL SECURITY;

-- Users can read all votes (for vote counts)
CREATE POLICY "comment_votes_public_read" ON comment_votes
  FOR SELECT USING (true);

-- Users can create votes
CREATE POLICY "comment_votes_authenticated_create" ON comment_votes
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    auth.uid() = user_id
  );

-- Users can update their own votes
CREATE POLICY "comment_votes_user_update" ON comment_votes
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own votes
CREATE POLICY "comment_votes_user_delete" ON comment_votes
  FOR DELETE USING (auth.uid() = user_id);
```

---

## 📊 Performance Indexes

```sql
-- Essential indexes for query performance
CREATE INDEX IF NOT EXISTS idx_comments_review_id ON comments(review_id);
CREATE INDEX IF NOT EXISTS idx_comments_author_id ON comments(author_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_approved_deleted ON comments(is_approved, is_deleted);
CREATE INDEX IF NOT EXISTS idx_comments_review_approved ON comments(review_id, is_approved, is_deleted);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_comments_review_thread ON comments(review_id, parent_id, created_at);
CREATE INDEX IF NOT EXISTS idx_comments_moderation ON comments(is_approved, flag_count, created_at);

-- Vote indexes
CREATE INDEX IF NOT EXISTS idx_comment_votes_comment_id ON comment_votes(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_votes_user_id ON comment_votes(user_id);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_comment_audit_comment_id ON comment_audit_log(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_audit_performed_by ON comment_audit_log(performed_by);
CREATE INDEX IF NOT EXISTS idx_comment_audit_created_at ON comment_audit_log(created_at DESC);
```

---

## ⚡ Database Functions

### 1. Update Comment Vote Counts
```sql
-- Function to update vote counts when votes change
CREATE OR REPLACE FUNCTION update_comment_vote_counts()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.vote_type = 'upvote' THEN
      UPDATE comments SET upvotes = upvotes + 1 WHERE id = NEW.comment_id;
    ELSE
      UPDATE comments SET downvotes = downvotes + 1 WHERE id = NEW.comment_id;
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.vote_type = 'upvote' THEN
      UPDATE comments SET upvotes = upvotes - 1 WHERE id = OLD.comment_id;
    ELSE
      UPDATE comments SET downvotes = downvotes - 1 WHERE id = OLD.comment_id;
    END IF;
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle vote type changes
    IF OLD.vote_type != NEW.vote_type THEN
      IF OLD.vote_type = 'upvote' THEN
        UPDATE comments SET upvotes = upvotes - 1, downvotes = downvotes + 1 WHERE id = NEW.comment_id;
      ELSE
        UPDATE comments SET downvotes = downvotes - 1, upvotes = upvotes + 1 WHERE id = NEW.comment_id;
      END IF;
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_update_comment_vote_counts
  AFTER INSERT OR UPDATE OR DELETE ON comment_votes
  FOR EACH ROW EXECUTE FUNCTION update_comment_vote_counts();
```

### 2. Comment Audit Logging Function
```sql
-- Function to log comment actions
CREATE OR REPLACE FUNCTION log_comment_action(
  p_comment_id UUID,
  p_action_type TEXT,
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL,
  p_reason TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  audit_id UUID;
BEGIN
  INSERT INTO comment_audit_log (
    comment_id, action_type, performed_by, old_values, new_values, reason
  ) VALUES (
    p_comment_id, p_action_type, auth.uid(), p_old_values, p_new_values, p_reason
  ) RETURNING id INTO audit_id;
  
  RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## ✅ Implementation Checklist

### Database Schema
- [ ] Execute comments table creation SQL
- [ ] Execute comment_votes table creation SQL  
- [ ] Execute comment_moderation_settings table creation SQL
- [ ] Execute comment_audit_log table creation SQL
- [ ] Verify all tables created successfully
- [ ] Check foreign key constraints are working

### Security Implementation
- [ ] Enable RLS on all comment tables
- [ ] Create and test comments table policies
- [ ] Create and test comment_votes table policies
- [ ] Create and test moderation_settings table policies
- [ ] Create and test audit_log table policies
- [ ] Verify policy isolation between users

### Performance Optimization
- [ ] Create all performance indexes
- [ ] Test query performance with sample data
- [ ] Verify index usage in query plans
- [ ] Monitor index size and effectiveness

### Database Functions
- [ ] Create vote counting function and trigger
- [ ] Create audit logging function
- [ ] Test functions with sample operations
- [ ] Verify trigger functionality

---

## 🧪 Testing Instructions

### 1. Schema Validation
```sql
-- Test table creation
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE 'comment%';

-- Test constraints
INSERT INTO comments (review_id, author_id, content) 
VALUES ('invalid-uuid', auth.uid(), 'Test'); -- Should fail

-- Test foreign keys
INSERT INTO comments (review_id, author_id, content) 
VALUES ((SELECT id FROM reviews LIMIT 1), auth.uid(), 'Valid test');
```

### 2. RLS Policy Testing
```sql
-- Test as different user types
-- 1. Anonymous user (should only see approved comments)
-- 2. Comment author (should see own comments)
-- 3. Review owner (should see all comments on their reviews)
-- 4. Admin user (should see all comments)
```

### 3. Performance Testing
```sql
-- Test query performance
EXPLAIN ANALYZE SELECT * FROM comments 
WHERE review_id = 'some-review-id' 
ORDER BY created_at DESC LIMIT 20;
```

---

## 🔐 Security Checklist

- [ ] All tables have RLS enabled
- [ ] Policies prevent unauthorized access
- [ ] Content length limits enforced
- [ ] SQL injection prevention verified
- [ ] Rate limiting infrastructure ready
- [ ] Audit logging captures all actions
- [ ] IP address tracking implemented
- [ ] User agent logging for security

---

## 📝 AI Implementation Prompts

### Prompt 1: Database Schema Creation
```
Create the complete database schema for CriticalPixel's comment system using the provided SQL scripts. Execute each table creation statement in Supabase SQL editor and verify successful creation. Pay special attention to foreign key relationships and constraints. Document any errors and provide solutions.
```

### Prompt 2: RLS Policy Implementation
```
Implement Row Level Security policies for the comment system tables. Test each policy thoroughly by creating test users and verifying access permissions. Ensure that users can only access comments they're authorized to see, and that review owners can moderate comments on their reviews.
```

### Prompt 3: Performance Optimization
```
Create all performance indexes for the comment system and test query performance. Use EXPLAIN ANALYZE to verify that indexes are being used effectively. Focus on common query patterns like fetching comments for a review, user's own comments, and moderation queries.
```

---

## 🚨 Rollback Plan

If issues occur during implementation:

1. **Drop Tables** (in reverse dependency order):
```sql
DROP TABLE IF EXISTS comment_audit_log;
DROP TABLE IF EXISTS comment_votes;
DROP TABLE IF EXISTS comment_moderation_settings;
DROP TABLE IF EXISTS comments;
```

2. **Remove Functions**:
```sql
DROP FUNCTION IF EXISTS update_comment_vote_counts();
DROP FUNCTION IF EXISTS log_comment_action();
```

3. **Document Issues**: Record what went wrong and why
4. **Restore Backup**: If database backup exists, restore it
5. **Notify Team**: Alert about rollback and next steps

---

## 📚 Documentation Requirements

After completing this step, document:

- [ ] All table schemas with field descriptions
- [ ] RLS policies and their purposes
- [ ] Index strategy and performance results
- [ ] Function documentation with examples
- [ ] Security considerations and validations
- [ ] Any deviations from the original plan
- [ ] Performance benchmarks and metrics

---

## ✅ Success Criteria

This step is complete when:

- [ ] All 4 tables created successfully
- [ ] All RLS policies implemented and tested
- [ ] All indexes created and performing well
- [ ] Database functions working correctly
- [ ] Security testing passed
- [ ] Documentation completed
- [ ] No critical errors in logs
- [ ] Ready for Step 2 implementation

---

## 🔄 Next Steps

Upon successful completion:
1. Proceed to **Step 2: Core Comment System Components**
2. Begin frontend component development
3. Implement comment display and form components
4. Test integration with new database schema

---

**⚠️ IMPORTANT NOTES FOR AI:**
- Always test in development environment first
- Backup database before making changes
- Verify each step before proceeding
- Document all changes and decisions
- Follow security best practices
- Test with multiple user scenarios
