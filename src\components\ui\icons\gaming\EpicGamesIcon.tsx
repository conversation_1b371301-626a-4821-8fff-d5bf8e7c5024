import React from 'react';

interface EpicGamesIconProps {
  className?: string;
  size?: number;
}

const EpicGamesIcon: React.FC<EpicGamesIconProps> = ({ 
  className = '', 
  size = 24 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Epic Games official logo with proper contrast */}
      <rect x="2" y="2" width="28" height="28" rx="4" fill="#000000"/>
      <g fill="#FFFFFF">
        <path d="M19.9,7.2c0-0.2,0-0.5,0.1-0.7c0.1-0.9,0.6-1.4,1.5-1.5c0.6-0.1,1.3-0.1,2,0c0.9,0.1,1.4,0.7,1.5,1.7   c0,0.8,0,1.7,0,2.5c0,0,0,0,0,0.1h-1.6c0-0.1,0-0.2,0-0.2c0-0.6,0-1.3,0-1.9c0-0.4-0.1-0.6-0.4-0.7c-0.2,0-0.5,0-0.8,0   c-0.3,0-0.4,0.2-0.4,0.5c0,0.1,0,0.2,0,0.3c0,2.3,0,4.6,0,6.8c0,0.1,0,0.2,0,0.3c0,0.3,0.2,0.5,0.5,0.5c0.2,0,0.4,0,0.5,0   c0.3,0,0.5-0.2,0.5-0.5c0-0.1,0-0.3,0-0.4c0-0.7,0-1.3,0-2c0-0.1,0-0.2,0-0.2h1.6c0,0.1,0,0.1,0,0.2c0,0.9,0,1.7,0,2.6   c0,0.2,0,0.4-0.1,0.7c-0.2,0.6-0.6,1.1-1.2,1.2c-0.7,0.1-1.5,0.1-2.2,0c-0.8-0.1-1.3-0.7-1.4-1.5c0-0.2,0-0.5,0-0.7   C19.9,11.8,19.9,9.5,19.9,7.2L19.9,7.2z"/>
        <path d="M17.3,5H19v11.4h-1.6V5z"/>
        <path d="M17.4,19.2h0.9v3.3h-0.9v-1.9l-0.1,0c-0.3,0.4-0.5,0.8-0.8,1.3c-0.3-0.4-0.6-0.9-0.8-1.3h-0.1v1.9h-0.8v-3.3   h0.9c0.3,0.4,0.5,0.8,0.8,1.3C16.9,20,17.1,19.6,17.4,19.2L17.4,19.2z"/>
        <path d="M7.1,5h3.7l0,1.6H8.8v3.2h2v1.5h-2v3.4h2.1v1.5H7.1L7.1,5z"/>
        <path d="M10.3,22.1c-0.4,0.3-0.8,0.5-1.3,0.5c-0.9,0-1.7-0.4-1.9-1.4c-0.2-1.3,0.7-2.1,1.7-2.1c0.4,0,0.9,0.1,1.2,0.3   c0.1,0,0.1,0.1,0.2,0.1C10,19.8,9.8,20,9.6,20.2c-0.3-0.2-0.7-0.4-1.1-0.2C8.3,20,8.2,20.1,8,20.3c-0.3,0.4-0.2,1,0.1,1.3   c0.3,0.3,0.9,0.3,1.2,0.1v-0.4H8.8v-0.7h1.5L10.3,22.1L10.3,22.1z"/>
        <path d="M18.8,27.3c-0.9,0.3-1.8,0.6-2.7,0.9c-0.1,0-0.2,0-0.4,0c-1.4-0.5-2.9-1-4.3-1.5c0,0-0.1,0-0.2-0.1h9.4v0   C20.1,26.9,19.5,27.1,18.8,27.3L18.8,27.3z"/>
        <path d="M21.8,22.5h-2.7v-3.3h2.6v0.7h-1.8v0.6h1.6v0.7h-1.6v0.6h1.8V22.5z"/>
        <path d="M24.5,22.4c-0.6,0.4-1.9,0.2-2.4-0.3c0.2-0.2,0.3-0.4,0.5-0.6c0.3,0.2,0.6,0.4,1,0.4c0.1,0,0.2,0,0.3,0   c0.1,0,0.1-0.1,0.2-0.2c0-0.1-0.1-0.2-0.1-0.2c-0.2-0.1-0.3-0.1-0.5-0.2c-0.2-0.1-0.4-0.1-0.6-0.2c-0.3-0.1-0.5-0.3-0.6-0.7   c0-0.4,0.1-0.8,0.4-1c0.4-0.3,0.9-0.3,1.4-0.2c0.3,0.1,0.6,0.2,0.8,0.4c-0.1,0.2-0.3,0.4-0.4,0.6c-0.3-0.1-0.6-0.2-0.9-0.3   c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0.1-0.2,0.2c0,0.1,0.1,0.2,0.1,0.2c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.1,0.6,0.2   c0.3,0.1,0.5,0.4,0.5,0.8C25,21.9,24.9,22.2,24.5,22.4L24.5,22.4z"/>
        <path d="M16.5,6.8c0-0.5-0.2-1.1-0.6-1.4C15.5,5.1,15.1,5,14.7,5c-0.9,0-1.8,0-2.7,0c-0.1,0-0.2,0-0.2,0v11.3h1.7v-4.1   c0.1,0,0.2,0,0.3,0c0.4,0,0.8,0,1.2,0c0.9-0.1,1.5-0.7,1.5-1.6C16.5,9.3,16.5,8.1,16.5,6.8L16.5,6.8z M14.8,10.2   c0,0.3-0.2,0.5-0.5,0.5c-0.3,0-0.6,0-0.9,0V6.6c0.3,0,0.6,0,0.8,0c0.3,0,0.5,0.2,0.5,0.6C14.8,8.2,14.8,9.2,14.8,10.2z"/>
        <path d="M12.9,19.2h-0.8c-0.5,1.1-0.9,2.2-1.4,3.4h0.9c0.1-0.2,0.2-0.4,0.2-0.6h1.4c0.1,0.2,0.2,0.4,0.3,0.6h0.9   C13.9,21.4,13.4,20.3,12.9,19.2L12.9,19.2z M12.1,21.2c0.1-0.3,0.3-0.6,0.4-1c0.1,0.4,0.3,0.7,0.4,1H12.1z"/>
      </g>
    </svg>
  );
};

export default EpicGamesIcon;