import React from 'react';

const HelpCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M8.5 9c0-1.5 1.5-3 3.5-3s3.5 1.5 3.5 3c0 1-0.5 1.5-1.5 2l-1 0.5v1.5"/>
    <circle cx="12" cy="18" r="0.8"/>
    <rect x="6" y="6" width="1" height="1"/>
    <rect x="17" y="6" width="1" height="1"/>
    <rect x="6" y="17" width="1" height="1"/>
    <rect x="17" y="17" width="1" height="1"/>
  </svg>
);

export default HelpCircleIcon;