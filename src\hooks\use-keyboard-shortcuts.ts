import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  disabled?: boolean;
}

interface UseKeyboardShortcutsOptions {
  shortcuts: KeyboardShortcut[];
  enableGlobal?: boolean;
}

export function useKeyboardShortcuts({ shortcuts, enableGlobal = true }: UseKeyboardShortcutsOptions) {
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' || 
      target.tagName === 'TEXTAREA' || 
      target.contentEditable === 'true' ||
      target.closest('[contenteditable="true"]')
    ) {
      return;
    }

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => {
      if (shortcut.disabled) return false;
      
      const keyMatches = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatches = shortcut.ctrlKey === event.ctrlKey;
      const shiftMatches = shortcut.shiftKey === event.shiftKey;
      const altMatches = shortcut.altKey === event.altKey;
      const metaMatches = shortcut.metaKey === event.metaKey;
      
      return keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches;
    });

    if (matchingShortcut) {
      event.preventDefault();
      event.stopPropagation();
      matchingShortcut.action();
    }
  }, [shortcuts]);

  useEffect(() => {
    if (!enableGlobal) return;

    document.addEventListener('keydown', handleKeyPress);
    
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress, enableGlobal]);

  // Return helper functions for manual shortcuts and shortcut display
  const getShortcutDisplay = useCallback((shortcut: KeyboardShortcut) => {
    const parts: string[] = [];
    
    if (shortcut.metaKey) parts.push('⌘');
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    parts.push(shortcut.key.toUpperCase());
    
    return parts.join('+');
  }, []);

  const triggerShortcut = useCallback((key: string) => {
    const shortcut = shortcuts.find(s => s.key.toLowerCase() === key.toLowerCase() && !s.disabled);
    if (shortcut) {
      shortcut.action();
    }
  }, [shortcuts]);

  return {
    getShortcutDisplay,
    triggerShortcut,
    activeShortcuts: shortcuts.filter(s => !s.disabled)
  };
}

// Common admin keyboard shortcuts
export const commonAdminShortcuts = {
  // Navigation
  goToReviews: { key: 'r', ctrlKey: true, description: 'Go to Reviews' },
  goToUsers: { key: 'u', ctrlKey: true, description: 'Go to Users' },
  goToModeration: { key: 'm', ctrlKey: true, description: 'Go to Moderation' },
  goToAds: { key: 'a', ctrlKey: true, description: 'Go to Ads' },
  
  // Actions
  createNew: { key: 'n', ctrlKey: true, description: 'Create New' },
  refresh: { key: 'F5', description: 'Refresh Data' },
  search: { key: '/', description: 'Focus Search' },
  
  // Bulk Actions
  selectAll: { key: 'a', ctrlKey: true, shiftKey: true, description: 'Select All' },
  clearSelection: { key: 'Escape', description: 'Clear Selection' },
  
  // Quick Actions
  approve: { key: 'Enter', ctrlKey: true, description: 'Approve Selected' },
  delete: { key: 'Delete', shiftKey: true, description: 'Delete Selected' },
  edit: { key: 'e', ctrlKey: true, description: 'Edit Selected' },
  
  // UI
  toggleSidebar: { key: 'b', ctrlKey: true, description: 'Toggle Sidebar' },
  showHelp: { key: '?', shiftKey: true, description: 'Show Help' }
};

export type CommonShortcut = keyof typeof commonAdminShortcuts; 