"use client";

import Link from 'next/link';
import { useState } from 'react';

const sizeStyles = {
  sm: { height: 36, width: 'auto' },   // 36px tall
  md: { height: 96, width: 'auto' },   // 96px tall
  lg: { height: 128, width: 'auto' },  // 128px tall
};

const Logo = ({ size = 'md', noLink = false }: { size?: 'sm' | 'md' | 'lg'; noLink?: boolean }) => {
  const [isHovering, setIsHovering] = useState(false);

  const logoContent = (
    <div
      className="relative"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Base Image */}
      <img
        src="/imgs/header-critical-pixel-logo.svg"
        alt="Website Logo"
        style={sizeStyles[size]}
        className={`transition-all duration-300 ${isHovering ? 'scale-105' : ''}`}
        draggable={false}
      />

      {/* Glitch Overlay - Only visible on hover */}
      {isHovering && (
        <>
          {/* Red channel offset */}
          <img
            src="/imgs/header-critical-pixel-logo.svg"
            alt=""
            style={sizeStyles[size]}
            className="absolute top-0 left-0 opacity-70 mix-blend-screen filter-red animate-glitch-1"
            draggable={false}
            aria-hidden="true"
          />

          {/* Blue channel offset */}
          <img
            src="/imgs/header-critical-pixel-logo.svg"
            alt=""
            style={sizeStyles[size]}
            className="absolute top-0 left-0 opacity-70 mix-blend-screen filter-blue animate-glitch-2"
            draggable={false}
            aria-hidden="true"
          />
        </>
      )}
    </div>
  );

  if (noLink) {
    return logoContent;
  }

  return (
    <Link href="/" className="flex items-center group" style={{ lineHeight: 0 }}>
      {logoContent}
    </Link>
  );
};

export default Logo;

