'use client';

import { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Gauge,
  BarChart3,
  TrendingUp,
  Monitor,
  Gamepad2,
  HardDrive,
  Plus,
  FileText,
  Zap
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PerformanceSurveyCard } from './PerformanceSurveyCard';
import { SurveyFilters } from './SurveyFilters';
import { PerformanceInsights } from './PerformanceInsights';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';
import type { DashboardFilters } from '@/types/dashboard';

interface PerformanceSurveysSectionProps {
  surveys: PerformanceSurveyRecord[];
  loading?: boolean;
  onDeleteSurvey?: (surveyId: string) => Promise<void>;
  showInsights?: boolean;
  className?: string;
}

export function PerformanceSurveysSection({
  surveys,
  loading = false,
  onDeleteSurvey,
  showInsights = true,
  className
}: PerformanceSurveysSectionProps) {
  const [filters, setFilters] = useState<DashboardFilters['surveys']>({
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Filter and sort surveys
  const filteredSurveys = useMemo(() => {
    let filtered = [...surveys];

    // Apply filters
    if (filters.deviceType) {
      filtered = filtered.filter(survey => survey.device_type === filters.deviceType);
    }

    if (filters.platform) {
      filtered = filtered.filter(survey => survey.platform === filters.platform);
    }

    if (filters.gameTitle) {
      filtered = filtered.filter(survey => 
        survey.game_title?.toLowerCase().includes(filters.gameTitle!.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      // Handle date sorting
      if (sortBy === 'created_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      }

      // Handle string sorting
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue?.toLowerCase() || '';
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [surveys, filters]);

  // Calculate statistics
  const stats = useMemo(() => {
    const deviceTypes = surveys.reduce((acc, survey) => {
      acc[survey.device_type] = (acc[survey.device_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgFps = surveys.reduce((sum, survey) => sum + (survey.fps_average || 0), 0) / surveys.length || 0;
    const avgSmoothness = surveys.reduce((sum, survey) => sum + (survey.smoothness || 0), 0) / surveys.length || 0;

    const uniqueGames = new Set(surveys.map(s => s.game_title).filter(Boolean)).size;

    return {
      deviceTypes,
      avgFps: Math.round(avgFps),
      avgSmoothness: Math.round(avgSmoothness * 10) / 10,
      uniqueGames
    };
  }, [surveys]);

  // Group surveys by game
  const groupedByGame = useMemo(() => {
    return filteredSurveys.reduce((acc, survey) => {
      const game = survey.game_title || 'Unknown Game';
      if (!acc[game]) acc[game] = [];
      acc[game].push(survey);
      return acc;
    }, {} as Record<string, PerformanceSurveyRecord[]>);
  }, [filteredSurveys]);

  

  // Export functionality removed

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 animate-pulse">
              <div className="h-4 bg-slate-700 rounded mb-2"></div>
              <div className="h-8 bg-slate-700 rounded"></div>
            </div>
          ))}
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-slate-900/60 border border-slate-700/50 rounded-xl p-6 animate-pulse">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-slate-700 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-6 bg-slate-700 rounded mb-2 w-1/3"></div>
                  <div className="h-4 bg-slate-700 rounded w-1/2"></div>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
                {[...Array(6)].map((_, j) => (
                  <div key={j} className="bg-slate-800/50 rounded-lg p-3">
                    <div className="h-3 bg-slate-700 rounded mb-2"></div>
                    <div className="h-6 bg-slate-700 rounded"></div>
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="bg-slate-800/30 rounded-lg p-3">
                    <div className="h-3 bg-slate-700 rounded mb-2"></div>
                    <div className="h-4 bg-slate-700 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (surveys.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center py-12"
      >
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-8 max-w-md mx-auto">
          <Gauge className="mx-auto mb-4 text-slate-400" size={48} />
          <h3 className="text-xl font-semibold text-slate-200 mb-2">
            No Performance Surveys Yet
          </h3>
          <p className="text-slate-400 mb-6">
            Start tracking your gaming performance by completing surveys when you write reviews.
          </p>
          <Link href="/review/create">
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Plus className="mr-2" size={16} />
              Create Your First Review
            </Button>
          </Link>
        </div>
      </motion.div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <BarChart3 size={16} />
              Total Surveys
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-200">{surveys.length}</div>
            <p className="text-xs text-slate-500">
              {stats.uniqueGames} unique games
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <Gauge size={16} />
              Avg FPS
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">{stats.avgFps}</div>
            <p className="text-xs text-slate-500">
              Across all games
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <TrendingUp size={16} />
              Avg Smoothness
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-400">{stats.avgSmoothness}/10</div>
            <p className="text-xs text-slate-500">
              Performance rating
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <Monitor size={16} />
              Device Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              {stats.deviceTypes.desktop && (
                <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded">
                  {stats.deviceTypes.desktop} Desktop
                </span>
              )}
              {stats.deviceTypes.laptop && (
                <span className="text-xs bg-green-500/20 text-green-300 px-2 py-1 rounded">
                  {stats.deviceTypes.laptop} Laptop
                </span>
              )}
              {stats.deviceTypes.handheld && (
                <span className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded">
                  {stats.deviceTypes.handheld} Handheld
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Insights */}
      {showInsights && surveys.length > 0 && (
        <PerformanceInsights surveys={surveys} />
      )}

      {/* Survey Comparison Tool removed */}

      {/* Container for Filters and Surveys */}
      <div className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur rounded-xl border overflow-hidden">
        {/* Filters Header */}
        <div className="p-6 border-b border-gray-700/50 bg-gray-800/30">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <SurveyFilters
              filters={filters}
              onFiltersChange={setFilters}
              totalCount={surveys.length}
              filteredCount={filteredSurveys.length}
              className="flex-1"
            />
          </div>
        </div>

        {/* Content Area with proper spacing */}
        <div className="p-6">
          {/* Surveys Grid */}
          <AnimatePresence mode="wait">
            {filteredSurveys.length === 0 ? (
              <motion.div
                key="no-results"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-12"
              >
                <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-8">
                  <FileText className="mx-auto mb-4 text-slate-400" size={48} />
                  <h3 className="text-lg font-medium text-slate-200 mb-2">
                    No surveys match your filters
                  </h3>
                  <p className="text-slate-400">
                    Try adjusting your search criteria or clearing filters.
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="surveys-list"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-4"
              >
                {filteredSurveys.map((survey, index) => (
                  <PerformanceSurveyCard
                    key={survey.id || index}
                    survey={survey}
                    onDelete={onDeleteSurvey}
                    onPrivacyToggle={async (id, isPrivate) => console.log(`Privacy toggle: ${id}, isPrivate: ${isPrivate}`)}
                    fullWidth={true}
                  />
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
