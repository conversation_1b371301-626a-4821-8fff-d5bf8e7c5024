'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Youtube, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink,
  Eye,
  Users,
  Video,
  Settings,
  Save,
  Trash2,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  saveYouTubeChannel,
  getUserYouTubeSettings,
  updateYouTubeSettings,
  removeYouTubeChannel
} from '@/app/u/dashboard/actions';

interface YouTubeChannelConfigProps {
  userId: string;
  className?: string;
}

interface YouTubeChannelData {
  channelUrl: string;
  channelTitle: string;
  channelThumbnail: string;
  subscriberCount: number;
  videoCount: number;
  isValid: boolean;
}

interface YouTubeModuleSettings {
  enabled: boolean;
  visibility: 'public' | 'friends' | 'private';
  maxVideos: number;
  showStats: boolean;
  showDescription: boolean;
  autoplay: boolean;
}

const YouTubeChannelConfig: React.FC<YouTubeChannelConfigProps> = ({
  userId,
  className = ''
}) => {
  const [channelUrl, setChannelUrl] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    success: boolean;
    data?: YouTubeChannelData;
    error?: string;
  } | null>(null);
  
  const [settings, setSettings] = useState<YouTubeModuleSettings>({
    enabled: true,
    visibility: 'public',
    maxVideos: 4,
    showStats: true,
    showDescription: true,
    autoplay: false
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const { toast } = useToast();

  // Load existing configuration on mount
  useEffect(() => {
    loadExistingConfig();
  }, [userId]);

  const loadExistingConfig = async () => {
    try {
      const response = await getUserYouTubeSettings(userId);
      if (response && response.success && response.data) {
        setChannelUrl(response.data.channelUrl || '');
        setSettings(response.data.settings || settings);
        if (response.data.channelData) {
          setValidationResult({
            success: true,
            data: {
              channelUrl: response.data.channelUrl || '',
              channelTitle: response.data.channelData.channel_title,
              channelThumbnail: response.data.channelData.channel_thumbnail,
              subscriberCount: response.data.channelData.subscriber_count,
              videoCount: response.data.channelData.video_count,
              isValid: response.data.channelData.is_valid
            }
          });
        }
      }
    } catch (error) {
      console.error('Error loading YouTube config:', error);
    }
  };

  const validateYouTubeUrl = (url: string): boolean => {
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/(channel\/|c\/|user\/|@)|youtu\.be\/)/;
    return youtubeRegex.test(url);
  };

  const handleUrlChange = (value: string) => {
    setChannelUrl(value);
    setValidationResult(null);
  };

  const validateChannel = async () => {
    if (!channelUrl.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter a YouTube channel URL",
        variant: "destructive"
      });
      return;
    }

    if (!validateYouTubeUrl(channelUrl)) {
      setValidationResult({
        success: false,
        error: 'Please enter a valid YouTube channel URL'
      });
      return;
    }

    setIsValidating(true);
    try {
      // TODO: Replace with actual API call
      // const result = await updateYouTubeChannel(userId, channelUrl);
      
      // Mock validation for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockResult = {
        success: true,
        data: {
          channelUrl,
          channelTitle: 'Gaming Channel',
          channelThumbnail: 'https://images.unsplash.com/photo-1511882150382-421056c89033?w=100&h=100&fit=crop',
          subscriberCount: 15420,
          videoCount: 89,
          isValid: true
        }
      };

      setValidationResult(mockResult);
      
      if (mockResult.success) {
        toast({
          title: "Channel Validated",
          description: "YouTube channel found and validated successfully!",
        });
      }
    } catch (error) {
      setValidationResult({
        success: false,
        error: 'Failed to validate channel. Please try again.'
      });
      toast({
        title: "Validation Failed",
        description: "Could not validate the YouTube channel",
        variant: "destructive"
      });
    }
    setIsValidating(false);
  };

  const saveConfiguration = async () => {
    if (!validationResult?.success) {
      toast({
        title: "Validation Required",
        description: "Please validate your YouTube channel first",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      const result = await saveYouTubeChannel(userId, channelUrl, settings);

      if (result.success) {
        toast({
          title: "Configuration Saved",
          description: "Your YouTube channel has been connected successfully!",
        });
      } else {
        toast({
          title: "Save Failed",
          description: result.error || "Could not save YouTube configuration",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Could not save YouTube configuration",
        variant: "destructive"
      });
    }
    setIsSaving(false);
  };

  const removeChannel = async () => {
    setIsRemoving(true);
    try {
      const result = await removeYouTubeChannel(userId);

      if (result.success) {
        setChannelUrl('');
        setValidationResult(null);
        setSettings(prev => ({ ...prev, enabled: true }));

        toast({
          title: "Channel Removed",
          description: "YouTube channel has been disconnected",
        });
      } else {
        toast({
          title: "Remove Failed",
          description: result.error || "Could not remove YouTube channel",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Remove Failed",
        description: "Could not remove YouTube channel",
        variant: "destructive"
      });
    }
    setIsRemoving(false);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* YouTube Channel Configuration Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                YouTube Channel Connection
              </CardTitle>
              <p className="font-mono text-xs text-gray-400 mt-1">
                Connect your YouTube channel to display latest videos on your profile
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-4">
                {/* Channel URL Input */}
                <div className="space-y-2">
                  <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
                    YouTube Channel URL
                  </label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="https://www.youtube.com/@yourchannel"
                      value={channelUrl}
                      onChange={(e) => handleUrlChange(e.target.value)}
                      className="flex-1 bg-gray-800/50 border-gray-700/50 focus:border-purple-400 text-white placeholder:text-gray-500 font-mono text-xs"
                      disabled={isValidating}
                    />
                    <Button 
                      onClick={validateChannel}
                      disabled={isValidating || !channelUrl.trim()}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-mono text-xs uppercase tracking-wide font-semibold min-w-[100px]"
                    >
                      {isValidating ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Validate'
                      )}
                    </Button>
                  </div>
                </div>

                {/* Validation Result */}
                {validationResult && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-3"
                  >
                    {validationResult.success ? (
                      <Alert className="border-green-500/50 bg-green-500/10">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <AlertDescription className="text-green-300 font-mono text-xs">
                          Channel validated successfully!
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <Alert className="border-red-500/50 bg-red-500/10">
                        <AlertCircle className="h-4 w-4 text-red-400" />
                        <AlertDescription className="text-red-300 font-mono text-xs">
                          {validationResult.error}
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Channel Preview */}
                    {validationResult.success && validationResult.data && (
                      <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
                        <img
                          src={validationResult.data.channelThumbnail}
                          alt="Channel thumbnail"
                          className="w-16 h-16 rounded-full"
                        />
                        <div className="flex-1">
                          <h3 className="font-mono text-sm font-semibold text-white">
                            {validationResult.data.channelTitle}
                          </h3>
                          <div className="flex gap-4 mt-2">
                            <Badge variant="outline" className="font-mono text-xs bg-purple-600/20 border-purple-500/50 text-purple-300">
                              <Users className="h-3 w-3 mr-1" />
                              {validationResult.data.subscriberCount.toLocaleString()}
                            </Badge>
                            <Badge variant="outline" className="font-mono text-xs bg-purple-600/20 border-purple-500/50 text-purple-300">
                              <Video className="h-3 w-3 mr-1" />
                              {validationResult.data.videoCount} videos
                            </Badge>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(channelUrl, '_blank')}
                          className="text-gray-300 border-gray-600 hover:bg-gray-800 font-mono text-xs uppercase tracking-wide"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </motion.div>
                )}

                {/* Action Buttons */}
                {validationResult?.success && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="pt-6 border-t border-gray-700/50 space-y-3"
                  >
                    <Button 
                      onClick={saveConfiguration}
                      disabled={isSaving}
                      className="w-full bg-purple-600 hover:bg-purple-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
                    >
                      {isSaving ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save YouTube Configuration
                    </Button>
                    
                    <Button 
                      variant="outline"
                      onClick={removeChannel}
                      disabled={isRemoving}
                      className="w-full text-red-400 border-red-400/50 hover:bg-red-400/10 font-mono text-xs uppercase tracking-wide"
                    >
                      {isRemoving ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Trash2 className="h-4 w-4 mr-2" />
                      )}
                      Remove YouTube Channel
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};

export default YouTubeChannelConfig;
