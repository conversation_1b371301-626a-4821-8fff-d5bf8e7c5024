// User Reports Hook
// Date: 22/06/2025
// Task: Track user's report status for content to provide visual feedback
// Updated: 22/06/2025 - Only show red flag for pending reports (not resolved/dismissed)

import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/auth-context';
import { useEffect, useState } from 'react';

export function useUserReports(contentIds: string[], contentType: 'review' | 'comment') {
  const { user } = useAuthContext();
  const supabase = createClient();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return useQuery({
    queryKey: ['user-reports', user?.uid, contentIds, contentType],
    queryFn: async (): Promise<Set<string>> => {
      if (!user?.uid || contentIds.length === 0) {
        return new Set();
      }

      const { data, error } = await supabase
        .from('content_flags')
        .select('content_id')
        .eq('reporter_id', user.uid)
        .eq('content_type', contentType)
        .eq('status', 'pending')
        .in('content_id', contentIds);

      if (error) {
        console.error('Error fetching user reports:', error);
        return new Set();
      }

      return new Set(data?.map(flag => flag.content_id) || []);
    },
    enabled: isMounted && !!user?.uid && contentIds.length > 0,
    staleTime: 15000, // 15 seconds - faster updates when reports are resolved
  });
}

export function useUserReport(contentId: string, contentType: 'review' | 'comment') {
  const { user } = useAuthContext();
  const supabase = createClient();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return useQuery({
    queryKey: ['user-report', user?.uid, contentId, contentType],
    queryFn: async (): Promise<boolean> => {
      if (!user?.uid || !contentId) {
        return false;
      }

      const { data, error } = await supabase
        .from('content_flags')
        .select('id')
        .eq('reporter_id', user.uid)
        .eq('content_type', contentType)
        .eq('content_id', contentId)
        .eq('status', 'pending')
        .maybeSingle();

      if (error) {
        console.error('Error checking user report:', error);
        return false;
      }

      return !!data;
    },
    enabled: isMounted && !!user?.uid && !!contentId,
    staleTime: 15000, // 15 seconds - faster updates when reports are resolved
  });
}