// Content Filtering System
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

export interface ContentFilterResult {
  isAllowed: boolean;
  filteredContent: string;
  warnings: string[];
  severity: 'low' | 'medium' | 'high';
}

export class ContentFilter {
  private profanityWords: string[] = [
    // Basic profanity - in production, load from database or external service
    'damn', 'hell', 'crap', 'stupid', 'idiot', 'moron', 'dumb',
    // Gaming-specific toxic terms
    'noob', 'scrub', 'trash', 'garbage', 'ez', 'rekt', 'pwned'
  ];

  private toxicityPatterns = [
    /\b(hate|kill|die|murder|suicide)\b/gi,
    /\b(stupid|idiot|moron|dumb|retard)\b/gi,
    /\b(trash|garbage|worthless|useless)\b/gi,
    /\b(kys|kill yourself)\b/gi,
    /\b(git gud|get good|you suck)\b/gi,
    // Harassment patterns
    /\b(shut up|stfu|gtfo)\b/gi,
    /\b(loser|failure|pathetic)\b/gi,
  ];

  private allowedDomains = [
    // Video platforms
    'youtube.com', 'youtu.be', 'twitch.tv', 'vimeo.com',
    // Gaming platforms
    'steam.com', 'steamcommunity.com', 'epicgames.com', 'gog.com',
    'origin.com', 'ubisoft.com', 'battle.net', 'xbox.com', 'playstation.com',
    // Social platforms
    'reddit.com', 'twitter.com', 'x.com', 'discord.gg', 'discord.com',
    // Development platforms
    'github.com', 'gitlab.com', 'bitbucket.org',
    // Gaming news/review sites
    'metacritic.com', 'ign.com', 'gamespot.com', 'polygon.com',
    'kotaku.com', 'destructoid.com', 'giantbomb.com', 'eurogamer.net',
    // Game wikis and guides
    'fandom.com', 'gamepedia.com', 'wiki.gg', 'pcgamingwiki.com'
  ];

  private blockedDomains = [
    // Known malicious/spam domains
    'bit.ly', 'tinyurl.com', 'goo.gl', 't.co', // URL shorteners (suspicious)
    'free-robux.com', 'free-vbucks.com', 'game-hack.com', // Gaming scams
    'casino', 'gambling', 'poker', 'slots', // Gambling sites
    'viagra', 'pharmacy', 'pills', // Pharmaceutical spam
  ];

  private suspiciousDomainPatterns = [
    /\b\d+\.\d+\.\d+\.\d+\b/, // IP addresses instead of domains
    /[a-z]{20,}\.com/, // Extremely long domain names
    /\w+\.tk$|\.ml$|\.ga$|\.cf$/, // Free TLD domains often used for spam
    /\w+-\w+-\w+\.com/, // Multiple hyphens (often spam)
  ];

  async filterContent(content: string): Promise<ContentFilterResult> {
    let filteredContent = content;
    const warnings: string[] = [];
    let severity: 'low' | 'medium' | 'high' = 'low';

    // 1. Profanity filtering
    this.profanityWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      if (regex.test(filteredContent)) {
        filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
        warnings.push('Profanity detected and filtered');
        severity = 'medium';
      }
    });

    // 2. Toxicity detection
    this.toxicityPatterns.forEach((pattern, index) => {
      if (pattern.test(content)) {
        const toxicityTypes = [
          'Violent language', 'Insulting language', 'Derogatory language',
          'Self-harm references', 'Gaming toxicity', 'Harassment language',
          'Offensive language'
        ];
        warnings.push(`${toxicityTypes[index] || 'Toxic language'} detected`);
        severity = 'high';
      }
    });

    // 3. Personal information detection
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g;
    const discordPattern = /\b\w+#\d{4}\b/g;
    
    if (emailPattern.test(content)) {
      filteredContent = filteredContent.replace(emailPattern, '[email protected]');
      warnings.push('Email address detected and masked');
      severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
    }

    if (phonePattern.test(content)) {
      filteredContent = filteredContent.replace(phonePattern, '[phone number]');
      warnings.push('Phone number detected and masked');
      severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
    }

    if (discordPattern.test(content)) {
      filteredContent = filteredContent.replace(discordPattern, '[discord tag]');
      warnings.push('Discord tag detected and masked');
      severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
    }

    // 4. Enhanced URL filtering
    const urlPattern = /https?:\/\/[^\s]+/gi;
    const urls = content.match(urlPattern);
    if (urls) {
      const urlAnalysis = this.analyzeUrls(urls);

      // Handle blocked domains (high severity - auto-block)
      if (urlAnalysis.blocked.length > 0) {
        warnings.push(`Blocked domains detected: ${urlAnalysis.blocked.length} URLs`);
        severity = 'high';
        urlAnalysis.blocked.forEach(url => {
          filteredContent = filteredContent.replace(url, '[blocked link removed]');
        });
      }

      // Handle suspicious URLs (medium severity - flag for review)
      if (urlAnalysis.suspicious.length > 0) {
        warnings.push(`Suspicious URLs detected: ${urlAnalysis.suspicious.length} URLs`);
        severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
        urlAnalysis.suspicious.forEach(url => {
          filteredContent = filteredContent.replace(url, '[suspicious link flagged]');
        });
      }

      // Handle unknown domains (low-medium severity - flag if multiple)
      if (urlAnalysis.unknown.length > 2) {
        warnings.push(`Multiple unknown domains detected: ${urlAnalysis.unknown.length} URLs`);
        severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
      } else if (urlAnalysis.unknown.length > 0) {
        warnings.push(`Unknown domains detected: ${urlAnalysis.unknown.length} URLs`);
        // Keep URLs but flag for manual review
      }

      // URL shorteners are always suspicious
      const shorteners = urls.filter(url =>
        /bit\.ly|tinyurl|goo\.gl|t\.co|short\.link|tiny\.cc/i.test(url)
      );
      if (shorteners.length > 0) {
        warnings.push('URL shorteners detected (potential spam)');
        severity = 'medium';
        shorteners.forEach(url => {
          filteredContent = filteredContent.replace(url, '[shortened link removed]');
        });
      }
    }

    // 5. Excessive capitalization
    const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
    if (capsRatio > 0.5 && content.length > 20) {
      warnings.push('Excessive capitalization detected');
      filteredContent = this.normalizeCapitalization(filteredContent);
      severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
    }

    // 6. Spam-like repetition
    const repeatedCharsPattern = /(.)\1{5,}/g;
    if (repeatedCharsPattern.test(content)) {
      warnings.push('Excessive character repetition detected');
      filteredContent = filteredContent.replace(repeatedCharsPattern, (match, char) => char.repeat(3));
      severity = Math.max(severity === 'low' ? 'medium' : severity, 'medium') as 'medium' | 'high';
    }

    // 7. Check for gaming-specific inappropriate content
    const gamingToxicPatterns = [
      /\b(cheater|hacker|aimbot|wallhack)\b/gi,
      /\b(report|ban|kick)\s+(this|that|him|her)\b/gi,
      /\b(stream\s*snip|ddos|dox)\b/gi,
    ];

    gamingToxicPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        warnings.push('Gaming-related inappropriate content detected');
        severity = 'high';
      }
    });

    return {
      isAllowed: severity !== 'high',
      filteredContent,
      warnings,
      severity,
    };
  }

  private normalizeCapitalization(text: string): string {
    // Convert excessive caps to normal case, preserving sentence structure
    return text.replace(/\b[A-Z]{3,}\b/g, (match) => {
      return match.charAt(0) + match.slice(1).toLowerCase();
    });
  }

  private analyzeUrls(urls: string[]): {
    allowed: string[];
    blocked: string[];
    suspicious: string[];
    unknown: string[];
  } {
    const result = {
      allowed: [] as string[],
      blocked: [] as string[],
      suspicious: [] as string[],
      unknown: [] as string[]
    };

    urls.forEach(url => {
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname.toLowerCase();

        // Check if domain is explicitly allowed
        if (this.allowedDomains.some(allowedDomain =>
          domain === allowedDomain || domain.endsWith('.' + allowedDomain)
        )) {
          result.allowed.push(url);
          return;
        }

        // Check if domain is explicitly blocked
        if (this.blockedDomains.some(blockedDomain =>
          domain.includes(blockedDomain.toLowerCase())
        )) {
          result.blocked.push(url);
          return;
        }

        // Check for suspicious patterns
        if (this.suspiciousDomainPatterns.some(pattern => pattern.test(domain))) {
          result.suspicious.push(url);
          return;
        }

        // Check for suspicious URL characteristics
        if (this.isUrlSuspicious(url, domain)) {
          result.suspicious.push(url);
          return;
        }

        // If not allowed, blocked, or suspicious, it's unknown
        result.unknown.push(url);
      } catch (error) {
        // Invalid URL format - treat as suspicious
        result.suspicious.push(url);
      }
    });

    return result;
  }

  private isUrlSuspicious(url: string, domain: string): boolean {
    // Check for various suspicious characteristics
    const suspiciousIndicators = [
      // Domain characteristics
      domain.length > 50, // Extremely long domains
      domain.split('.').length > 4, // Too many subdomains
      /\d{4,}/.test(domain), // Long numbers in domain

      // URL characteristics
      url.length > 200, // Extremely long URLs
      (url.match(/[&?]/g) || []).length > 10, // Too many parameters
      /[^\x00-\x7F]/.test(url), // Non-ASCII characters (potential IDN attack)

      // Suspicious patterns
      /free|hack|cheat|generator|unlimited/i.test(domain),
      /download|install|click|now|urgent/i.test(url),
    ];

    return suspiciousIndicators.some(indicator => indicator);
  }

  // Method to check if content should be auto-approved
  public shouldAutoApprove(filterResult: ContentFilterResult): boolean {
    return filterResult.isAllowed && filterResult.severity === 'low';
  }

  // Method to check if content should be auto-flagged
  public shouldAutoFlag(filterResult: ContentFilterResult): boolean {
    return filterResult.severity === 'medium' || !filterResult.isAllowed;
  }

  // Method to check if content should be auto-blocked
  public shouldAutoBlock(filterResult: ContentFilterResult): boolean {
    return filterResult.severity === 'high' && !filterResult.isAllowed;
  }

  // Method to add domains to allowlist (for admin configuration)
  public addAllowedDomain(domain: string): void {
    if (!this.allowedDomains.includes(domain.toLowerCase())) {
      this.allowedDomains.push(domain.toLowerCase());
    }
  }

  // Method to add domains to blocklist (for admin configuration)
  public addBlockedDomain(domain: string): void {
    if (!this.blockedDomains.includes(domain.toLowerCase())) {
      this.blockedDomains.push(domain.toLowerCase());
    }
  }

  // Method to remove domains from allowlist
  public removeAllowedDomain(domain: string): void {
    const index = this.allowedDomains.indexOf(domain.toLowerCase());
    if (index > -1) {
      this.allowedDomains.splice(index, 1);
    }
  }

  // Method to get current domain lists (for admin interface)
  public getDomainLists(): {
    allowed: string[];
    blocked: string[];
  } {
    return {
      allowed: [...this.allowedDomains],
      blocked: [...this.blockedDomains]
    };
  }

  // Method to check a single URL without filtering content
  public checkUrl(url: string): {
    status: 'allowed' | 'blocked' | 'suspicious' | 'unknown';
    reason?: string;
  } {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();

      if (this.allowedDomains.some(allowedDomain =>
        domain === allowedDomain || domain.endsWith('.' + allowedDomain)
      )) {
        return { status: 'allowed' };
      }

      if (this.blockedDomains.some(blockedDomain =>
        domain.includes(blockedDomain.toLowerCase())
      )) {
        return { status: 'blocked', reason: 'Domain is on blocklist' };
      }

      if (this.suspiciousDomainPatterns.some(pattern => pattern.test(domain))) {
        return { status: 'suspicious', reason: 'Domain matches suspicious pattern' };
      }

      if (this.isUrlSuspicious(url, domain)) {
        return { status: 'suspicious', reason: 'URL has suspicious characteristics' };
      }

      return { status: 'unknown', reason: 'Domain not in allowlist' };
    } catch (error) {
      return { status: 'suspicious', reason: 'Invalid URL format' };
    }
  }
}
