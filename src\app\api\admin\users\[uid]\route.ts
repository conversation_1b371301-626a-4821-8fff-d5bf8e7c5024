// Admin User API - Supabase Implementation
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 2 User Management System

import { NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

// Helper function to verify admin permissions
async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createServerClient();
    const { data, error } = await supabase.rpc('is_admin', { user_id: userId });
    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }
    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Helper function to log admin actions
async function logAdminAction(adminId: string, action: string, targetUserId?: string, data?: any): Promise<void> {
  console.log('ADMIN AUDIT LOG:', {
    timestamp: new Date().toISOString(),
    admin_id: adminId,
    action,
    target_user_id: targetUserId,
    data
  });
}

// Get user details (admin only)
export async function GET(
  request: Request,
  { params }: { params: { uid: string } }
) {
  try {
    const supabase = await createServerClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify admin permissions
    const isAuthorized = await verifyAdminPermissions(user.id);
    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get target user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        email,
        avatar_url,
        banner_url,
        bio,
        preferred_genres,
        favorite_consoles,
        theme,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        privacy_settings,
        created_at,
        updated_at
      `)
      .eq('id', params.uid)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Log admin action
    await logAdminAction(user.id, 'getUserDetails', params.uid, {}, { success: true });

    return NextResponse.json({ user: profile });

  } catch (error) {
    console.error('Admin API GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update user profile (admin only)
export async function PUT(
  request: Request,
  { params }: { params: { uid: string } }
) {
  try {
    const supabase = await createServerClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify admin permissions
    const isAuthorized = await verifyAdminPermissions(user.id);
    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate allowed fields
    const allowedFields = ['display_name', 'bio', 'avatar_url', 'banner_url', 'theme', 'is_admin'];
    const updateData: any = {};

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Update user profile
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.uid);

    if (updateError) {
      await logAdminAction(user.id, 'updateUser', params.uid, { error: updateError.message });
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 500 }
      );
    }

    // Log successful admin action
    await logAdminAction(user.id, 'updateUser', params.uid, updateData);

    return NextResponse.json({
      success: true,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Admin API PUT error:', error);

    if (error.message.includes('Unauthorized')) {
      return NextResponse.json(
        { error: error.message },
        { status: 403 }
      );
    }

    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
