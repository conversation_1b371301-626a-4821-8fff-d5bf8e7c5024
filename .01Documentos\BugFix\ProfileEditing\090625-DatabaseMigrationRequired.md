# Database Migration Required: Gaming Profiles & Social Media

**Data:** 25 de janeiro de 2025  
**Priority:** CRÍTICA  
**Status:** 🔄 PENDING MIGRATION  

## 🚨 **Problema Urgente**

As mudanças implementadas para corrigir a persistência de **Gaming Profiles** e **Social Media** requerem uma migration no banco de dados Supabase. Atualmente as colunas `gaming_profiles` e `social_profiles` não existem na tabela `profiles`.

## 📋 **Migration SQL Necessária**

Execute esta migration no Supabase para adicionar as colunas necessárias:

```sql
-- Add gaming_profiles column to profiles table
ALTER TABLE profiles 
ADD COLUMN gaming_profiles JSONB DEFAULT '[]'::jsonb;

-- Add social_profiles column to profiles table  
ALTER TABLE profiles
ADD COLUMN social_profiles JSONB DEFAULT '[]'::jsonb;

-- Add comments for documentation
COMMENT ON COLUMN profiles.gaming_profiles IS 'Array of gaming platform profiles (Steam, Xbox, PlayStation, etc.)';
COMMENT ON COLUMN profiles.social_profiles IS 'Array of social media profiles (Twitter, YouTube, Twitch, etc.)';

-- Create indexes for better performance on JSON queries
CREATE INDEX IF NOT EXISTS idx_profiles_gaming_platforms 
ON profiles USING gin ((gaming_profiles -> 'platform'));

CREATE INDEX IF NOT EXISTS idx_profiles_social_platforms 
ON profiles USING gin ((social_profiles -> 'platform'));
```

## 🔄 **Estrutura Expected dos Dados**

### Gaming Profiles:
```json
[
  {
    "platform": "steam",
    "username": "player123",
    "url": "https://steamcommunity.com/id/player123"
  },
  {
    "platform": "xbox", 
    "username": "GamerTag",
    "url": ""
  }
]
```

### Social Profiles:
```json
[
  {
    "platform": "twitch",
    "username": "streamer123", 
    "url": "https://twitch.tv/streamer123"
  },
  {
    "platform": "youtube",
    "username": "ContentCreator",
    "url": "https://youtube.com/@ContentCreator"
  }
]
```

## 🛠️ **Depois da Migration**

1. **Descomente o código no ProfilePageClient.tsx:**
```typescript
// TODO: Add gaming_profiles and social_profiles once database migration is complete
if (updatedProfile.gamingProfiles !== undefined) {
  profileUpdateData.gaming_profiles = updatedProfile.gamingProfiles;
}
if (updatedProfile.socialMedia !== undefined) {
  profileUpdateData.social_profiles = updatedProfile.socialMedia;
}
```

2. **Restaure os campos no ProfileSchema:**
```typescript
gaming_profiles: z.array(GamingProfileSchema).optional(),
social_profiles: z.array(SocialMediaProfileSchema).optional(),
```

3. **Adicione de volta às queries SELECT:**
```sql
SELECT 
  -- ... outros campos
  gaming_profiles,
  social_profiles,
  -- ... demais campos
```

## ⚠️ **Status Atual**

- ✅ **Website funcionando** - Reverti as mudanças que causavam erro
- ❌ **Gaming/Social profiles** - Não persistem até migration ser feita
- ✅ **Outros campos** - Funcionando normalmente
- 🔄 **Migration** - Aguardando execução no Supabase

## 🎯 **Próximos Passos**

1. Executar migration SQL no Supabase
2. Verificar se colunas foram criadas corretamente
3. Restaurar código comentado nos arquivos
4. Testar funcionalidade completa
5. Atualizar documentação do bug fix

---
*Migration requerida após bug fix 250125-SocialGamingProfilesFix004* 