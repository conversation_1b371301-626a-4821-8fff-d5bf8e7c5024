import { createClient } from '@/lib/supabase/client';

export interface ContentBannerSummary {
  total_impressions: number;
  total_clicks: number;
  ctr: string;
  last_impression: string | null;
  last_click: string | null;
  days_active: number;
  created_at: string;
}

export interface DailyStats {
  date: string;
  impressions: number;
  clicks: number;
  ctr: string;
}

export interface PerformanceMetrics extends ContentBannerSummary {
  impressionTrend: number;
  clickTrend: number;
  ctrTrend: number;
  avgDailyImpressions: number;
  avgDailyClicks: number;
}

// Get content banner summary analytics
export async function getContentBannerSummary(bannerId: string): Promise<ContentBannerSummary | null> {
  try {
    console.log('🔍 Getting content banner summary for bannerId:', bannerId);
    const supabase = createClient();
    const { data, error } = await supabase.rpc('get_content_banner_summary', {
      banner_id: bannerId
    });

    if (error) {
      console.error('❌ Error fetching content banner summary:', error);
      return null;
    }

    console.log('📊 Content banner summary raw data:', data);
    const result = data?.[0] || null;
    console.log('📈 Content banner summary result:', result);
    return result;
  } catch (error) {
    console.error('❌ Error in getContentBannerSummary:', error);
    return null;
  }
}

// Get daily stats for content banner
export async function getContentBannerDailyStats(
  bannerId: string, 
  daysBack: number = 30
): Promise<DailyStats[]> {
  try {
    console.log('📅 Getting content banner daily stats for bannerId:', bannerId, 'daysBack:', daysBack);
    const supabase = createClient();
    const { data, error } = await supabase.rpc('get_content_banner_daily_stats', {
      banner_id: bannerId,
      days_back: daysBack
    });

    if (error) {
      console.error('❌ Error fetching content banner daily stats:', error);
      return [];
    }

    console.log('📊 Content banner daily stats raw data:', data);
    return data || [];
  } catch (error) {
    console.error('❌ Error in getContentBannerDailyStats:', error);
    return [];
  }
}

// Get performance metrics with trends
export async function getContentBannerPerformanceMetrics(bannerId: string): Promise<PerformanceMetrics | null> {
  try {
    const [summary, dailyStats] = await Promise.all([
      getContentBannerSummary(bannerId),
      getContentBannerDailyStats(bannerId, 30)
    ]);

    if (!summary) return null;

    // Calculate trends (comparing last 7 days vs previous 7 days)
    const last7Days = dailyStats.slice(-7);
    const previous7Days = dailyStats.slice(-14, -7);

    const last7Impressions = last7Days.reduce((sum, day) => sum + day.impressions, 0);
    const previous7Impressions = previous7Days.reduce((sum, day) => sum + day.impressions, 0);
    const impressionTrend = previous7Impressions > 0 
      ? ((last7Impressions - previous7Impressions) / previous7Impressions) * 100 
      : 0;

    const last7Clicks = last7Days.reduce((sum, day) => sum + day.clicks, 0);
    const previous7Clicks = previous7Days.reduce((sum, day) => sum + day.clicks, 0);
    const clickTrend = previous7Clicks > 0 
      ? ((last7Clicks - previous7Clicks) / previous7Clicks) * 100 
      : 0;

    const last7CTR = last7Impressions > 0 ? (last7Clicks / last7Impressions) * 100 : 0;
    const previous7CTR = previous7Impressions > 0 ? (previous7Clicks / previous7Impressions) * 100 : 0;
    const ctrTrend = previous7CTR > 0 ? ((last7CTR - previous7CTR) / previous7CTR) * 100 : 0;

    // Calculate averages
    const avgDailyImpressions = dailyStats.length > 0 
      ? dailyStats.reduce((sum, day) => sum + day.impressions, 0) / dailyStats.length 
      : 0;
    const avgDailyClicks = dailyStats.length > 0 
      ? dailyStats.reduce((sum, day) => sum + day.clicks, 0) / dailyStats.length 
      : 0;

    return {
      ...summary,
      impressionTrend,
      clickTrend,
      ctrTrend,
      avgDailyImpressions,
      avgDailyClicks
    };
  } catch (error) {
    console.error('Error in getContentBannerPerformanceMetrics:', error);
    return null;
  }
}

// Export analytics data to CSV
export async function exportContentBannerAnalyticsToCSV(
  bannerId: string,
  daysBack: number = 30
): Promise<string> {
  try {
    const [summary, dailyStats] = await Promise.all([
      getContentBannerSummary(bannerId),
      getContentBannerDailyStats(bannerId, daysBack)
    ]);

    if (!summary || !dailyStats.length) {
      throw new Error('No data available for export');
    }

    // Create CSV content
    let csvContent = 'Content Banner Analytics Export\n\n';
    
    // Summary section
    csvContent += 'Summary\n';
    csvContent += 'Metric,Value\n';
    csvContent += `Total Impressions,${summary.total_impressions}\n`;
    csvContent += `Total Clicks,${summary.total_clicks}\n`;
    csvContent += `Click-Through Rate,${summary.ctr}%\n`;
    csvContent += `Days Active,${summary.days_active}\n`;
    csvContent += `Created At,${new Date(summary.created_at).toLocaleDateString()}\n`;
    csvContent += `Last Impression,${summary.last_impression ? new Date(summary.last_impression).toLocaleDateString() : 'Never'}\n`;
    csvContent += `Last Click,${summary.last_click ? new Date(summary.last_click).toLocaleDateString() : 'Never'}\n\n`;

    // Daily stats section
    csvContent += 'Daily Statistics\n';
    csvContent += 'Date,Impressions,Clicks,CTR\n';
    dailyStats.forEach(day => {
      csvContent += `${day.date},${day.impressions},${day.clicks},${day.ctr}%\n`;
    });

    return csvContent;
  } catch (error) {
    console.error('Error in exportContentBannerAnalyticsToCSV:', error);
    throw error;
  }
}
