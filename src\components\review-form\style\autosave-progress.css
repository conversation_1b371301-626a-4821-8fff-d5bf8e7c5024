/* Auto-save circular progress animations - more discrete */
.autosave-progress-container {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 1rem;
  height: 1rem;
  z-index: 10;
  opacity: 0.7;
}

.autosave-progress-circle {
  transition: stroke-dashoffset 0.3s ease-out;
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.2));
}

.autosave-progress-circle.saving {
  filter: drop-shadow(0 0 3px rgba(34, 197, 94, 0.3));
}

.autosave-progress-circle.error {
  filter: drop-shadow(0 0 3px rgba(239, 68, 68, 0.3));
}

.autosave-checkmark-container {
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.autosave-checkmark-bg {
  transition: all 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.autosave-pulse {
  animation: autosave-pulse 0.6s ease-out;
}

@keyframes autosave-pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* Quick bounce animation for checkmark */
.autosave-checkmark-bounce {
  animation: autosave-bounce 0.3s ease-out;
}

@keyframes autosave-bounce {
  0% {
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.15);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Glow effect for the progress ring */
.autosave-glow {
  filter: drop-shadow(0 0 8px currentColor);
}