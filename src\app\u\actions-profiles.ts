'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import type { GamingProfile, SocialMediaProfile } from '@/lib/types/profile';

/**
 * Get gaming profiles for a user from separate gaming_profiles table
 */
export async function getUserGamingProfiles(userId: string): Promise<GamingProfile[]> {
  try {
    console.log(`[getUserGamingProfiles] Fetching gaming profiles for user: ${userId}`);
    const supabase = await createServerClient(await cookies());

    // Check if user is authenticated (optional for public profile viewing)
    try {
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.log(`[getUserGamingProfiles] No authentication for anonymous user - proceeding with public access`);
      }
    } catch (authError) {
      console.log(`[getUserGamingProfiles] Anonymous user accessing gaming profiles - no authentication required`);
    }

    // First try with all columns, fallback to essential columns if some don't exist
    let { data: gamingProfiles, error } = await supabase
      .from('gaming_profiles')
      .select(`
        platform,
        username,
        url,
        verified
      `)
      .eq('user_id', userId);

    // If url column doesn't exist, try without it
    if (error && error.code === '42703' && error.message?.includes('url does not exist')) {
      console.log(`[getUserGamingProfiles] url column doesn't exist, querying without it`);
      const fallbackResult = await supabase
        .from('gaming_profiles')
        .select(`
          platform,
          username,
          verified
        `)
        .eq('user_id', userId);
      
      gamingProfiles = fallbackResult.data?.map(profile => ({
        ...profile,
        url: '' // Add empty url field for compatibility
      })) || [];
      error = fallbackResult.error;
    }

    if (error) {
      console.error(`[getUserGamingProfiles] Supabase error for user ${userId}:`, {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        fullError: error
      });
      
      // If table doesn't exist, this is expected for new installations
      if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
        console.log(`[getUserGamingProfiles] Gaming profiles table does not exist yet, returning empty array`);
        return [];
      }
      
      // Return empty array instead of throwing for other errors too
      return [];
    }

    console.log(`[getUserGamingProfiles] Successfully fetched ${gamingProfiles?.length || 0} gaming profiles for user ${userId}`);
    return (gamingProfiles || []) as GamingProfile[];
  } catch (error) {
    console.error(`[getUserGamingProfiles] Unexpected error for user ${userId}:`, {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      fullError: error
    });
    // Return empty array instead of throwing
    return [];
  }
}

/**
 * Get social media profiles for a user from separate social_media_profiles table
 */
export async function getUserSocialMediaProfiles(userId: string): Promise<SocialMediaProfile[]> {
  try {
    console.log(`[getUserSocialMediaProfiles] Fetching social media profiles for user: ${userId}`);
    const supabase = await createServerClient(await cookies());

    // Check if user is authenticated (optional for public profile viewing)
    try {
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.log(`[getUserSocialMediaProfiles] No authentication for anonymous user - proceeding with public access`);
      }
    } catch (authError) {
      console.log(`[getUserSocialMediaProfiles] Anonymous user accessing social profiles - no authentication required`);
    }

    // First try with all columns, fallback to essential columns if some don't exist
    let { data: socialProfiles, error } = await supabase
      .from('social_media_profiles')
      .select(`
        platform,
        username,
        url,
        verified
      `)
      .eq('user_id', userId);

    // If url column doesn't exist, try without it
    if (error && error.code === '42703' && error.message?.includes('url does not exist')) {
      console.log(`[getUserSocialMediaProfiles] url column doesn't exist, querying without it`);
      const fallbackResult = await supabase
        .from('social_media_profiles')
        .select(`
          platform,
          username,
          verified
        `)
        .eq('user_id', userId);
      
      socialProfiles = fallbackResult.data?.map(profile => ({
        ...profile,
        url: '' // Add empty url field for compatibility
      })) || [];
      error = fallbackResult.error;
    }

    if (error) {
      console.error(`[getUserSocialMediaProfiles] Supabase error for user ${userId}:`, {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        fullError: error
      });
      
      // If table doesn't exist, this is expected for new installations
      if (error.code === 'PGRST116' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
        console.log(`[getUserSocialMediaProfiles] Social media profiles table does not exist yet, returning empty array`);
        return [];
      }
      
      // Return empty array instead of throwing for other errors too
      return [];
    }

    console.log(`[getUserSocialMediaProfiles] Successfully fetched ${socialProfiles?.length || 0} social media profiles for user ${userId}`);
    return (socialProfiles || []) as SocialMediaProfile[];
  } catch (error) {
    console.error(`[getUserSocialMediaProfiles] Unexpected error for user ${userId}:`, {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      fullError: error
    });
    // Return empty array instead of throwing
    return [];
  }
}

/**
 * Save gaming profiles for a user (replace all existing)
 */
export async function saveUserGamingProfiles(
  userId: string, 
  profiles: GamingProfile[]
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('saveUserGamingProfiles called with:', { userId, profilesCount: profiles.length, profiles });
    
    const supabase = await createServerClient(await cookies());

    // Verify user authentication
    const { data: currentUser } = await supabase.auth.getUser();
    console.log('Current user auth check:', { currentUserId: currentUser.user?.id, requestedUserId: userId });
    
    if (!currentUser.user || currentUser.user.id !== userId) {
      console.error('Authorization failed:', { 
        hasUser: !!currentUser.user, 
        currentUserId: currentUser.user?.id, 
        requestedUserId: userId 
      });
      return { success: false, error: 'Unauthorized' };
    }

    // Validate profile data before processing
    for (const profile of profiles) {
      if (!profile.platform || !profile.username) {
        console.error('Invalid profile data:', profile);
        return { success: false, error: 'Invalid profile data: platform and username are required' };
      }
      
      const validPlatforms = ['steam', 'xbox', 'playstation', 'nintendo', 'epic', 'origin', 'uplay'];
      if (!validPlatforms.includes(profile.platform)) {
        console.error('Invalid platform:', profile.platform);
        return { success: false, error: `Invalid platform: ${profile.platform}` };
      }
    }

    // Delete existing gaming profiles
    console.log('Deleting existing gaming profiles for user:', userId);
    const { error: deleteError } = await supabase
      .from('gaming_profiles')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting gaming profiles:', deleteError);
      return { success: false, error: `Failed to update gaming profiles: ${deleteError.message}` };
    }

    // Insert new gaming profiles if any
    if (profiles.length > 0) {
      const profilesWithUserId = profiles.map(profile => ({
        ...profile,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      console.log('Inserting new gaming profiles:', profilesWithUserId);
      
      const { error: insertError } = await supabase
        .from('gaming_profiles')
        .insert(profilesWithUserId);

      if (insertError) {
        console.error('Error inserting gaming profiles:', insertError);
        return { success: false, error: `Failed to save gaming profiles: ${insertError.message}` };
      }
    }

    console.log('Successfully saved gaming profiles');
    return { success: true };
  } catch (error) {
    console.error('Error in saveUserGamingProfiles:', error);
    return { success: false, error: `Unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

/**
 * Save social media profiles for a user (replace all existing)
 */
export async function saveUserSocialMediaProfiles(
  userId: string, 
  profiles: SocialMediaProfile[]
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('saveUserSocialMediaProfiles called with:', { userId, profilesCount: profiles.length, profiles });
    
    const supabase = await createServerClient(await cookies());

    // Verify user authentication
    const { data: currentUser } = await supabase.auth.getUser();
    console.log('Current user auth check:', { currentUserId: currentUser.user?.id, requestedUserId: userId });
    
    if (!currentUser.user || currentUser.user.id !== userId) {
      console.error('Authorization failed:', { 
        hasUser: !!currentUser.user, 
        currentUserId: currentUser.user?.id, 
        requestedUserId: userId 
      });
      return { success: false, error: 'Unauthorized' };
    }

    // Validate profile data before processing
    for (const profile of profiles) {
      if (!profile.platform || !profile.username) {
        console.error('Invalid profile data:', profile);
        return { success: false, error: 'Invalid profile data: platform and username are required' };
      }
      
      const validPlatforms = ['twitter', 'facebook', 'instagram', 'youtube', 'twitch', 'github', 'linkedin', 'discord', 'reddit', 'tiktok'];
      if (!validPlatforms.includes(profile.platform)) {
        console.error('Invalid platform:', profile.platform);
        return { success: false, error: `Invalid platform: ${profile.platform}` };
      }
    }

    // Delete existing social media profiles
    console.log('Deleting existing social media profiles for user:', userId);
    const { error: deleteError } = await supabase
      .from('social_media_profiles')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting social media profiles:', deleteError);
      return { success: false, error: `Failed to update social media profiles: ${deleteError.message}` };
    }

    // Insert new social media profiles if any
    if (profiles.length > 0) {
      const profilesWithUserId = profiles.map(profile => ({
        ...profile,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      console.log('Inserting new social media profiles:', profilesWithUserId);
      
      const { error: insertError } = await supabase
        .from('social_media_profiles')
        .insert(profilesWithUserId);

      if (insertError) {
        console.error('Error inserting social media profiles:', insertError);
        return { success: false, error: `Failed to save social media profiles: ${insertError.message}` };
      }
    }

    console.log('Successfully saved social media profiles');
    return { success: true };
  } catch (error) {
    console.error('Error in saveUserSocialMediaProfiles:', error);
    return { success: false, error: `Unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}

/**
 * Get complete profile data including related tables
 */
export async function getCompleteUserProfile(userId: string) {
  try {
    // Use individual promises instead of Promise.all to handle individual failures
    let gamingProfiles: GamingProfile[] = [];
    let socialProfiles: SocialMediaProfile[] = [];
    
    try {
      gamingProfiles = await getUserGamingProfiles(userId);
    } catch (gamingError) {
      console.error(`Error fetching gaming profiles for user ${userId}:`, gamingError);
      // Continue execution with empty gaming profiles
    }
    
    try {
      socialProfiles = await getUserSocialMediaProfiles(userId);
    } catch (socialError) {
      console.error(`Error fetching social media profiles for user ${userId}:`, socialError);
      // Continue execution with empty social profiles
    }

    return {
      gaming_profiles: gamingProfiles,
      social_profiles: socialProfiles
    };
  } catch (error) {
    console.error(`Error in getCompleteUserProfile for user ${userId}:`, error);
    // Return empty arrays instead of throwing
    return {
      gaming_profiles: [],
      social_profiles: []
    };
  }
} 