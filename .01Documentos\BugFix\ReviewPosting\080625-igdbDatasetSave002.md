# Bug Fix: IGDB Dataset Not Creating Proper Dataset - FASE 2

**Date:** 06/01/2025  
**Issue ID:** igdbDatasetSave002  
**Severity:** High  
**Status:** Análise Completa / Solução Identificada  

## Problema Identificado: Row Level Security (RLS) 

### **Causa Raiz Confirmada**

Após análise mais profunda, o problema **não está** na estruturação dos dados, mas sim nas **políticas RLS da tabela `games`**. A política atual só permite que **administradores** insiram dados na tabela `games`:

```sql
-- Política RLS atual (PROBLEMÁTICA)
CREATE POLICY "Admins manage game data" ON games
  FOR INSERT WITH CHECK (is_admin(auth.uid()));
```

### **Evidências do Problema**

1. **Logs Melhorados**: Agora detectamos erros de permissão específicos (código 42501)
2. **Fluxo Correto**: Os dados IGDB são estruturados corretamente 
3. **Autenticação OK**: Utilizador está autenticado mas não é administrador
4. **Bloqueio RLS**: Política impede criação de jogos por utilizadores normais

### **Impacto**

- ✅ **Reviews são criadas**: Mas sem `game_id` 
- ❌ **Jogos não são salvos**: Bloqueados por RLS
- ❌ **Dados IGDB perdidos**: Não ficam associados permanentemente
- ❌ **Relações quebradas**: `reviews.game_id` fica NULL

---

## 🔧 **Soluções Propostas**

### **Opção A: Política RLS Menos Restritiva (RECOMENDADA)**

```sql
-- Permitir que utilizadores autenticados criem jogos
DROP POLICY IF EXISTS "Admins manage game data" ON games;
CREATE POLICY "Authenticated users can create games" ON games
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Manter proteção para updates/deletes
CREATE POLICY "Admins update game data" ON games
  FOR UPDATE USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins delete game data" ON games
  FOR DELETE USING (is_admin(auth.uid()));
```

### **Opção B: Função Admin para Criação de Jogos**

Criar uma função privilegiada que bypassa RLS:

```sql
CREATE OR REPLACE FUNCTION create_game_from_igdb(
  igdb_data jsonb,
  igdb_id bigint
) RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER -- Runs with creator privileges
AS $$
DECLARE
  game_id uuid;
BEGIN
  INSERT INTO games (
    igdb_id, name, slug, summary, release_date,
    cover_url, aggregated_rating, aggregated_rating_count,
    developers, publishers, genres, platforms,
    game_engines, player_perspectives, 
    time_to_beat_normally, time_to_beat_completely
  ) VALUES (
    igdb_id,
    igdb_data->>'name',
    lower(replace(igdb_data->>'name', ' ', '-')),
    igdb_data->>'summary',
    (igdb_data->>'first_release_date')::date,
    igdb_data->>'cover_url',
    (igdb_data->>'aggregated_rating')::numeric,
    (igdb_data->>'aggregated_rating_count')::integer,
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'developers')),
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'publishers')),
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'genres')),
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'platforms')),
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'game_engines')),
    ARRAY(SELECT jsonb_array_elements_text(igdb_data->'player_perspectives')),
    (igdb_data->'time_to_beat'->>'normally')::integer,
    (igdb_data->'time_to_beat'->>'completely')::integer
  ) RETURNING id INTO game_id;
  
  RETURN game_id;
END;
$$;
```

### **Opção C: Sistema de Aprovação (COMPLEXA)**

Implementar um sistema onde jogos ficam "pending" para aprovação admin.

---

## 🎯 **Recomendação: Implementar Opção A**

### **Justificação:**

1. **Segurança Balanceada**: Utilizadores autenticados são confiáveis para criar jogos
2. **Funcionalidade Completa**: Reviews ficam corretamente associadas aos jogos
3. **Simplicidade**: Não requer mudanças de código, apenas política RLS
4. **Proteção Mantida**: Updates/deletes ainda requerem admin

### **Implementação Necessária:**

```sql
-- 1. Remover política restritiva atual
DROP POLICY IF EXISTS "Admins manage game data" ON games;

-- 2. Criar políticas balanceadas
CREATE POLICY "Authenticated users can create games" ON games
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can update games" ON games
  FOR UPDATE USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can delete games" ON games
  FOR DELETE USING (is_admin(auth.uid()));
```

---

## ✅ **Resultado Esperado**

Após implementação da Opção A:

1. **✅ Utilizador seleciona jogo IGDB**: Dados carregados corretamente
2. **✅ Review criada**: Com todos os dados estruturados 
3. **✅ Jogo salvo na base de dados**: Com ID único gerado
4. **✅ Relação estabelecida**: `reviews.game_id` aponta para `games.id`
5. **✅ Dados IGDB persistentes**: Disponíveis em `/reviews/view/[slug]`

---

## 🔗 **Próximos Passos**

1. **Aplicar mudança RLS** conforme Opção A
2. **Testar criação de review** com jogo IGDB
3. **Verificar persistência** de dados na visualização
4. **Atualizar documentação** de políticas de segurança

---

**Estado:** ⏳ **AGUARDANDO IMPLEMENTAÇÃO RLS**  
**Criticidade:** 🔴 **ALTA** - Funcionalidade core bloqueada 