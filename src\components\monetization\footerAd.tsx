import React, { useState } from 'react';

const FooterAd = () => {
  const [isHovered, setIsHovered] = useState(false);

  // Code title component with glitch effect
  const CodeTitle = ({ children, className = "" }: { 
    children: React.ReactNode; 
    className?: string;
  }) => (
    <span className={`font-mono relative inline-block ${className}`}>
      <span className="text-violet-400/60">&lt;</span>
      <span className="mx-1 relative hover:animate-pulse">
        {children}
        <span className="absolute inset-0 bg-gradient-to-r from-violet-400/0 via-violet-400/30 to-violet-400/0 opacity-0 hover:opacity-100 transition-opacity duration-200"/>
      </span>
      <span className="text-violet-400/60">/&gt;</span>
    </span>
  );

  return (
    <div className="mb-8">
      <style jsx>{`
        .glitch:hover {
          animation: glitch 0.3s ease-in-out;
        }
        @keyframes glitch {
          0%, 100% { transform: translate(0); }
          10% { transform: translate(-1px, -1px); }
          20% { transform: translate(1px, 1px); }
          30% { transform: translate(-1px, 1px); }
          40% { transform: translate(1px, -1px); }
          50% { transform: translate(-1px, -1px); }
          60% { transform: translate(1px, 1px); }
          70% { transform: translate(-1px, 1px); }
          80% { transform: translate(1px, -1px); }
          90% { transform: translate(-1px, -1px); }
        }
        .ad-glow {
          background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(56, 189, 248, 0.1));
          background-size: 400% 400%;
          animation: gradientShift 8s ease infinite;
        }
        @keyframes gradientShift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
      `}</style>

      <div className="border border-white/5 bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-xl rounded-xl overflow-hidden shadow-lg shadow-black/10 transform transition-all duration-300 ease-out hover:shadow-xl hover:shadow-black/20 hover:scale-[1.005]">
        {/* Ad Space - Directly inside main container */}
        <div className="p-4 sm:p-6">
          <div 
            className={`relative overflow-hidden rounded-xl border border-white/5 bg-slate-800/20 hover:bg-slate-700/30 hover:border-white/10 transition-all duration-500 ${
              isHovered ? 'ad-glow' : ''
            }`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Content Area */}
            <div className="min-h-[120px] sm:min-h-[180px] flex items-center justify-center p-4 sm:p-6">
              <div className="text-center space-y-4">
                {/* Visual Elements */}
                <div className="flex justify-center space-x-3 mb-4">
                  {[1, 2, 3].map((i) => (
                    <div 
                      key={i}
                      className={`w-3 h-3 rounded-full bg-gradient-to-r from-violet-500 to-cyan-500 transition-all duration-300 ${
                        isHovered ? 'animate-pulse' : 'opacity-50'
                      }`}
                      style={{ animationDelay: `${i * 0.2}s` }}
                    />
                  ))}
                </div>
                
                {/* Ad Format Info */}
                <div className="space-y-2">
                  <p className="text-slate-300 font-mono text-sm sm:text-base">
                    <CodeTitle className="glitch">
                      Premium Ad Space
                    </CodeTitle>
                  </p>
                  <div className="flex flex-wrap justify-center gap-2 text-xs">
                    {['728×90', '970×250', '320×50'].map((format) => (
                      <span 
                        key={format}
                        className="px-2 py-1 bg-slate-700/50 rounded border border-white/10 text-slate-400 font-mono hover:text-white hover:border-violet-400/30 transition-all duration-200"
                      >
                        <CodeTitle className="text-xs">
                          {format}
                        </CodeTitle>
                      </span>
                    ))}
                  </div>
                </div>
                
                {/* Call to Action */}
                <div className={`mt-4 transition-all duration-300 ${isHovered ? 'opacity-100' : 'opacity-60'}`}>
                  <button
                    className="px-4 py-2 bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-500 hover:to-cyan-500 text-white rounded-lg transition transform duration-200 hover:scale-105"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-sm uppercase tracking-wide">Your Brand Here</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
            
            {/* Subtle Corner Elements */}
            <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-violet-500/20 rounded-tl" />
            <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-500/20 rounded-tr" />
            <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-violet-500/20 rounded-bl" />
            <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-500/20 rounded-br" />
            
            {/* Hover Glow Effect */}
            <div className={`absolute inset-0 bg-gradient-to-r from-violet-500/5 via-transparent to-cyan-500/5 opacity-0 transition-opacity duration-500 ${
              isHovered ? 'opacity-100' : ''
            }`} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterAd;