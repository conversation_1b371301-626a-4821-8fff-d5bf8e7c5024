'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Lock, Sparkles, Check } from 'lucide-react';

interface ThemeSelectorProps {
  value: string;
  onChange: (themeId: string) => void;
  isPremiumUser?: boolean;
  showUpgradeMessage?: boolean;
}

interface ThemeOption {
  id: string;
  name: string;
  description: string;
  preview: string;
  isPremium: boolean;
}

export const themeOptions: ThemeOption[] = [
  {
    id: 'muted-dark',
    name: 'Muted Dark',
    description: 'Subtle dark theme with soft accents',
    preview: 'bg-gradient-to-r from-slate-800 to-gray-900',
    isPremium: false
  },
  {
    id: 'charcoal',
    name: 'Charcoal',
    description: 'Deep grays with subtle blue hints',
    preview: 'bg-gradient-to-r from-gray-800 to-slate-900',
    isPremium: false
  },
  {
    id: 'deep-forest',
    name: 'Deep Forest',
    description: 'Dark greens with earthy undertones',
    preview: 'bg-gradient-to-r from-emerald-900 to-slate-900',
    isPremium: true
  },
  {
    id: 'midnight',
    name: 'Midnight',
    description: 'Deep blues with subtle highlights',
    preview: 'bg-gradient-to-r from-blue-950 to-slate-950',
    isPremium: true
  },
  {
    id: 'obsidian',
    name: 'Obsidian',
    description: 'Almost black with subtle red accents',
    preview: 'bg-gradient-to-r from-gray-950 to-red-950',
    isPremium: true
  },
  {
    id: 'slate',
    name: 'Slate',
    description: 'Dark grays with teal accents',
    preview: 'bg-gradient-to-r from-slate-900 to-teal-900',
    isPremium: false
  },
  {
    id: 'ember',
    name: 'Ember',
    description: 'Dark with muted orange warmth',
    preview: 'bg-gradient-to-r from-orange-900 to-stone-900',
    isPremium: true
  },
  {
    id: 'graphite',
    name: 'Graphite',
    description: 'Dark with subtle silver highlights',
    preview: 'bg-gradient-to-r from-zinc-800 to-stone-900',
    isPremium: false
  }
];

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  value,
  onChange,
  isPremiumUser = false,
  showUpgradeMessage = true
}) => {
  const [selectedTheme, setSelectedTheme] = useState(value || 'muted-dark');
  
  const handleSelectTheme = (themeId: string) => {
    const theme = themeOptions.find(t => t.id === themeId);
    if (theme?.isPremium && !isPremiumUser) return;
    
    setSelectedTheme(themeId);
    onChange(themeId);
  };
  
  const getThemeConfig = (themeId: string) => 
    themeOptions.find(t => t.id === themeId) || themeOptions[0];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white">Profile Theme</h3>
        <Badge className="bg-gradient-to-r from-slate-600 to-slate-700 backdrop-blur-sm">
          <Sparkles className="h-3.5 w-3.5 mr-1" />
          Customization
        </Badge>
      </div>
      
      {/* Theme Preview */}
      <div className="relative overflow-hidden rounded-lg bg-gray-950/80 border border-gray-800/50 backdrop-blur-sm">
        <div className="absolute inset-0 bg-dot-pattern opacity-5"></div>
        <div className={cn(
          "h-24 w-full relative",
          getThemeConfig(selectedTheme).preview
        )}>
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          <div className="absolute inset-0 bg-black/10 backdrop-blur-[0.5px]"></div>
        </div>
        <div className="p-4 bg-gray-900/60 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">{getThemeConfig(selectedTheme).name}</h4>
              <p className="text-sm text-gray-400">{getThemeConfig(selectedTheme).description}</p>
            </div>
            {getThemeConfig(selectedTheme).isPremium && !isPremiumUser && (
              <Badge className="bg-gray-800/60 text-gray-400 border border-gray-700/50 backdrop-blur-sm">
                <Lock className="h-3 w-3 mr-1" />
                Premium
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      {/* Theme Options */}
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {themeOptions.map((theme) => (
          <ThemeOption
            key={theme.id}
            theme={theme}
            isSelected={selectedTheme === theme.id}
            isPremiumUser={isPremiumUser}
            onSelect={handleSelectTheme}
          />
        ))}
      </div>
      
      {/* Upgrade Message */}
      {showUpgradeMessage && !isPremiumUser && (
        <div className="mt-4 bg-slate-900/40 border border-slate-800/50 rounded-lg p-4 text-sm backdrop-blur-sm">
          <div className="flex items-start gap-3">
            <Sparkles className="h-5 w-5 text-slate-400 shrink-0 mt-0.5" />
            <div>
              <p className="text-slate-200 font-medium mb-1">Unlock Premium Themes</p>
              <p className="text-slate-300/80">Get access to exclusive dark themes that perfectly match your gaming setup and personality.</p>
              <Button 
                className="mt-3 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white border-none backdrop-blur-sm"
                size="sm"
              >
                Upgrade Now
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const ThemeOption: React.FC<{
  theme: ThemeOption;
  isSelected: boolean;
  isPremiumUser: boolean;
  onSelect: (themeId: string) => void;
}> = ({ theme, isSelected, isPremiumUser, onSelect }) => {
  const isLocked = theme.isPremium && !isPremiumUser;
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              "relative rounded-md overflow-hidden border-2 transition-all duration-200 cursor-pointer backdrop-blur-sm",
              isSelected 
                ? "border-slate-500 ring-2 ring-slate-500/30 shadow-lg shadow-slate-500/25" 
                : "border-transparent hover:border-gray-700/50",
              isLocked && "opacity-70"
            )}
            onClick={() => onSelect(theme.id)}
          >
            <div className={cn("h-12 w-full relative", theme.preview)}>
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              <div className="absolute inset-0 bg-black/10 backdrop-blur-[0.5px]"></div>
            </div>
            
            <div className="absolute bottom-1 right-1">
              {isSelected && (
                <div className="bg-slate-500/80 rounded-full p-0.5 backdrop-blur-sm">
                  <Check className="h-3 w-3 text-white" />
                </div>
              )}
            </div>
            
            {isLocked && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center backdrop-blur-sm">
                <div className="bg-gray-800/80 rounded-full p-1.5 backdrop-blur-sm">
                  <Lock className="h-3 w-3 text-gray-300" />
                </div>
              </div>
            )}
            
            {/* Glossy overlay effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/5 to-white/10 opacity-0 hover:opacity-100 transition-opacity duration-200"></div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="bg-gray-900/90 text-white border-gray-800/50 backdrop-blur-sm">
          <div className="text-center">
            <div className="font-medium">{theme.name}</div>
            <div className="text-xs text-gray-400">{theme.description}</div>
            {isLocked && <div className="text-xs text-amber-400 mt-1">Premium Only</div>}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ThemeSelector;