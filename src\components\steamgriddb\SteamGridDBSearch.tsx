'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSteamGridDB, SteamGridDBGame } from '@/hooks/useSteamGridDB';
import { Search, CheckCircle, Gamepad2 } from 'lucide-react';

interface SteamGridDBSearchProps {
  onGameSelect?: (game: SteamGridDBGame) => void;
  className?: string;
}

export function SteamGridDBSearch({ onGameSelect, className }: SteamGridDBSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { loading, error, games, searchGames } = useSteamGridDB();

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    await searchGames(searchQuery.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleGameClick = (game: SteamGridDBGame) => {
    onGameSelect?.(game);
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5" />
            SteamGridDB Game Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="flex gap-2">
            <Input
              placeholder="Search for games on SteamGridDB..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={loading}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={loading || !searchQuery.trim()}
              variant="outline"
              size="icon"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-3 border rounded-lg">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-3 w-[100px]" />
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Results */}
          {!loading && games.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Found {games.length} games
              </p>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {games.map((game) => (
                  <div
                    key={game.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
                    onClick={() => handleGameClick(game)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 bg-muted rounded flex items-center justify-center">
                        <Gamepad2 className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">{game.name}</h3>
                          {game.verified && (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                        </div>
                        <div className="flex gap-1 mt-1">
                          {game.types.map((type) => (
                            <Badge 
                              key={type} 
                              variant="secondary" 
                              className="text-xs"
                            >
                              {type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      Select
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Results */}
          {!loading && searchQuery && games.length === 0 && !error && (
            <div className="text-center py-8 text-muted-foreground">
              <Gamepad2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No games found for "{searchQuery}"</p>
              <p className="text-sm">Try adjusting your search terms</p>
            </div>
          )}

          {/* Initial State */}
          {!loading && !searchQuery && games.length === 0 && !error && (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Search for games to get started</p>
              <p className="text-sm">Find custom artwork from SteamGridDB</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}