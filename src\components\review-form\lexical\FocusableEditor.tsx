// src/components/review-form/lexical/FocusableEditor.tsx
'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { EditorState } from 'lexical';
import Editor from './Editor';

interface FocusableEditorProps {
  onChange?: (editorState: EditorState) => void;
  initialEditorState?: string | null;
  placeholder?: string;
  isLightMode?: boolean;
  isFocusMode?: boolean;
  focusModeContainer?: HTMLElement | null;
}

export default function FocusableEditor({
  onChange,
  initialEditorState,
  placeholder = "Write your review here...",
  isLightMode = false,
  isFocusMode = false,
  focusModeContainer = null
}: FocusableEditorProps) {
  const [mounted, setMounted] = useState(false);
  const [editorState, setEditorState] = useState<string | null>(initialEditorState || null);
  const editorRef = useRef<HTMLDivElement>(null);

  // Mount on client side only
  useEffect(() => {
    setMounted(true);
  }, []);

  // Update editor state when initialEditorState changes
  useEffect(() => {
    if (initialEditorState !== editorState) {
      setEditorState(initialEditorState);
    }
  }, [initialEditorState]);

  // Handle editor changes and preserve state
  const handleEditorChange = useCallback((newEditorState: EditorState) => {
    try {
      newEditorState.read(() => {
        const jsonState = JSON.stringify(newEditorState.toJSON());
        setEditorState(jsonState);
        
        // Call the external onChange handler
        if (onChange) {
          onChange(newEditorState);
        }
      });
    } catch (error) {
      console.error("Error processing editor state:", error);
    }
  }, [onChange]);

  // Create the editor component
  const editorComponent = (
    <div 
      ref={editorRef}
      className={`focusable-editor-wrapper ${isFocusMode ? 'focus-mode-editor-wrapper' : ''}`}
    >
      <Editor
        key={`editor-${isFocusMode ? 'focus' : 'regular'}`}
        onChange={handleEditorChange}
        initialEditorState={editorState}
        placeholder={placeholder}
        isLightMode={isLightMode}
      />
    </div>
  );

  // If not mounted, return null
  if (!mounted) {
    return null;
  }

  // If in focus mode and we have a container, render via portal
  if (isFocusMode && focusModeContainer) {
    return createPortal(editorComponent, focusModeContainer);
  }

  // Otherwise render normally
  return editorComponent;
}
