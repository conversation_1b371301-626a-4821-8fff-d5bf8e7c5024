// src/components/auth/AuthModal.tsx
// PHASE 3 IMPLEMENTATION: Real Supabase Authentication
// Updated: January 2025 - Supabase authentication with RLS integration
'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Eye, EyeOff, X, CheckSquare, Square, AlertTriangle, Info, ChevronRight } from 'lucide-react';
import DiscordIcon from '@/components/ui/icons/socials/DiscordIcon';
import GoogleIcon from '@/components/ui/icons/socials/GoogleIcon';
import SteamIcon from '@/components/ui/icons/gaming/SteamIcon';
import { slugify } from '@/lib/utils/slugify';
import '@/components/style/authModal.css';

import { useAuthContext } from '@/contexts/auth-context';
import { createClient } from '@/lib/supabase/client';
interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CodeTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <span className={`code-title ${className || ''}`}>
    <span className="code-title-bracket">&lt;</span>
    {children}
    <span className="code-title-bracket">/&gt;</span>
  </span>
);

export default function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const { setUser: setAuthContextUser } = useAuthContext(); // Get setUser from AuthContext

  const [view, setView] = useState<'login' | 'register' | 'forgot-password'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [userName, setUserName] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Registration checkboxes state
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [shareData, setShareData] = useState(false);

  const resetFormState = () => {
    setEmail('');
    setPassword('');
    setUserName('');
    setError(null);
    setMessage(null);
    setAcceptTerms(false);
    setShareData(false);
    setShowPassword(false);
  };

  const handleSetView = (newView: 'login' | 'register' | 'forgot-password') => {
    resetFormState();
    setView(newView);
  };

  // Fetch user profile from database
  const fetchUserProfile = async (userId: string): Promise<any> => {
    try {
      console.log('🔐 Fetching profile for user ID:', userId);
      const supabase = createClient();
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      console.log('🔐 Profile query result:', { data, error });
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('🔐 Error fetching user profile:', error);
      return null;
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setError('ERR: Email and Password required.');
      return;
    }
    setError(null);
    setMessage(null);
    setLoading(true);

    try {
      console.log('🔐 Starting login process...');
      const supabase = createClient();
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      console.log('🔐 Supabase auth response:', { signInData, signInError });

      if (signInError) {
        console.error('🔐 Login error:', signInError);
        setError(`Login Failed: ${signInError.message}`);
        setLoading(false); // Reset loading on error
        return;
      }

      if (signInData.user) {
        // Don't manually fetch profile - let the onAuthStateChange listener handle it
        // This avoids RLS timing issues and duplicate profile fetches
        setMessage('Login successful! Welcome back.');

        // Set loading to false and close modal after a short delay
        // The AuthContext will handle setting the user profile
        setLoading(false);

        setTimeout(() => {
          onClose(); // Close modal
          resetFormState(); // Reset form
        }, 1000); // Delay to show success message
      } else {
        // Should not happen if signInError is null, but as a safeguard
        setError('Login failed: No user data received.');
        setLoading(false); // Reset loading
      }
    } catch (err: any) {
      console.error('Login error:', err);
      setError(`Login Failed: ${err.message || 'Unknown error'}`);
      setLoading(false); // Reset loading on catch
    }
    // The finally block for setLoading(false) was removed because 
    // we need to control it more precisely within the try/catch logic,
    // especially before the setTimeout.
  };

  const handleRegister = async () => {
    if (!email || !password || !userName) {
      setError('ERR: Email, Password, and Username required.');
      return;
    }
    if (userName.length < 3) {
      setError('ERR: Username_MIN_3_CHARS');
      return;
    }
    if (password.length < 6) {
      setError('ERR: Password_MIN_6_CHARS');
      return;
    }
    if (!acceptTerms) {
      setError('ERR: Terms_MUST_BE_ACCEPTED');
      return;
    }
    if (!shareData) {
      setError('ERR: Data_Policy_MUST_BE_ACCEPTED');
      return;
    }
    setError(null);
    setMessage(null);
    setLoading(true);

    try {
      const supabase = createClient();

      // Create user account with Supabase Auth with enhanced metadata
      console.log('🔐 Starting registration for:', email, 'with username:', userName);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: userName,
            display_name: userName,
            email: email, // Add email to metadata for trigger access
            signup_source: 'auth_modal', // For debugging
            signup_timestamp: new Date().toISOString()
          }
        }
      });

      if (error) {
        setError(`Registration Failed: ${error.message}`);
        return;
      }

      if (data.user) {
        // Create user profile in profiles table
        // Note: This will be handled by a database trigger automatically
        // The trigger creates a profile when a new user is created in auth.users

        setMessage('Registration successful! Please check your email to verify your account.');
        setTimeout(() => {
          onClose();
          resetFormState();
        }, 3000);
      }
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(`Registration Failed: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      setError('ERR: Email_REQUIRED_FOR_RESET');
      return;
    }
    setError(null);
    setMessage(null);
    setLoading(true);

    try {
      const supabase = createClient();
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        setError(`Reset Failed: ${error.message}`);
      } else {
        setMessage('Password reset email sent! Check your inbox for instructions.');
        setTimeout(() => {
          onClose();
          resetFormState();
        }, 3000);
      }
    } catch (err: any) {
      console.error('Password reset error:', err);
      setError(`Reset Failed: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !loading) {
      e.preventDefault();
      if (view === 'login') handleLogin();
      else if (view === 'register') handleRegister();
      else if (view === 'forgot-password') handleForgotPassword();
    }
  };

  useEffect(() => {
    if (isOpen) {
      resetFormState();
      setView('login');
    }
  }, [isOpen]);

  // Add cleanup for ESC key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Social authentication functions
  const handleSocialAuth = async (provider: 'google' | 'discord') => {
    setLoading(true);
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        }
      });
      
      if (error) {
        setError(`${provider.charAt(0).toUpperCase() + provider.slice(1)} auth failed: ${error.message}`);
      } else {
        // The auth state change will be handled by the onAuthStateChange listener
        onClose();
      }
    } catch (err: any) {
      setError(`${provider.charAt(0).toUpperCase() + provider.slice(1)} auth failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleAuth = () => handleSocialAuth('google');
  const handleDiscordAuth = () => handleSocialAuth('discord');

  if (!isOpen) {
    return null;
  }

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9, y: -20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0, 
      transition: { 
        type: 'spring', 
        stiffness: 300, 
        damping: 25 
      } 
    },
    exit: { 
      opacity: 0, 
      scale: 0.9, 
      y: 20, 
      transition: { 
        duration: 0.2 
      } 
    },
  };

  const contentVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0, 
      transition: { 
        staggerChildren: 0.05, 
        delayChildren: 0.1 
      } 
    },
    exit: { 
      opacity: 0, 
      x: 20, 
      transition: { 
        duration: 0.15 
      } 
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="auth-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          >
            <motion.div
              className="auth-modal-container"
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                className="auth-modal-close-button"
                onClick={onClose}
                aria-label="Close modal"
              >
                <X size={20} />
              </button>

              <div className="auth-modal-header">
                <button
                  className={`auth-modal-tab ${view === 'login' ? 'active' : ''}`}
                  onClick={() => handleSetView('login')}
                >
                  Login
                </button>
                <button
                  className={`auth-modal-tab ${view === 'register' ? 'active' : ''}`}
                  onClick={() => handleSetView('register')}
                >
                  Register
                </button>
                <button
                  className={`auth-modal-tab ${view === 'forgot-password' ? 'active' : ''}`}
                  onClick={() => handleSetView('forgot-password')}
                >
                  Recovery
                </button>
              </div>

              <div className="auth-modal-content">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={view}
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    {view === 'login' && (
                      <>
                        <motion.h2 variants={itemVariants} className="auth-modal-title-main">
                          <CodeTitle>User Login</CodeTitle>
                        </motion.h2>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="login-email">
                            Email
                          </label>
                          <input
                            id="login-email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            onKeyDown={handleKeyPress}
                            disabled={loading}
                          />
                        </motion.div>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="login-password">
                            Password
                          </label>
                          <div className="auth-modal-password-wrapper">
                            <input
                              id="login-password"
                              type={showPassword ? 'text' : 'password'}
                              placeholder="************"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              onKeyDown={handleKeyPress}
                              disabled={loading}
                            />
                            <button
                              type="button"
                              className="auth-modal-show-password-button"
                              onClick={() => setShowPassword(!showPassword)}
                              aria-label={showPassword ? 'Hide password' : 'Show password'}
                            >
                              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                            </button>
                          </div>
                        </motion.div>
                        <motion.button
                          variants={itemVariants}
                          className="auth-modal-button primary"
                          onClick={handleLogin}
                          disabled={loading}
                        >
                          {loading ? 'Authenticating...' : 'Login_System'} <ChevronRight size={16} />
                        </motion.button>
                        <motion.button
                          variants={itemVariants}
                          className="auth-modal-link-button"
                          onClick={() => handleSetView('forgot-password')}
                          disabled={loading}
                        >
                          &gt; Forgot Password?
                        </motion.button>
                      </>
                    )}

                    {view === 'register' && (
                      <>
                        <motion.h2 variants={itemVariants} className="auth-modal-title-main">
                          <CodeTitle>Create Account</CodeTitle>
                        </motion.h2>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="register-username">
                            Username - 3 characters+
                          </label>
                          <input
                            id="register-username"
                            type="text"
                            placeholder="Choose your username"
                            value={userName}
                            onChange={(e) => setUserName(e.target.value)}
                            onKeyDown={handleKeyPress}
                            disabled={loading}
                          />
                        </motion.div>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="register-email">
                            Email
                          </label>
                          <input
                            id="register-email"
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            onKeyDown={handleKeyPress}
                            disabled={loading}
                          />
                        </motion.div>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="register-password">
                            Choose a Password - 6 characters+
                          </label>
                          <div className="auth-modal-password-wrapper">
                            <input
                              id="register-password"
                              type={showPassword ? 'text' : 'password'}
                              placeholder="Choose a strong password"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              onKeyDown={handleKeyPress}
                              disabled={loading}
                            />
                            <button
                              type="button"
                              className="auth-modal-show-password-button"
                              onClick={() => setShowPassword(!showPassword)}
                              aria-label={showPassword ? 'Hide password' : 'Show password'}
                            >
                              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                            </button>
                          </div>
                        </motion.div>

                        <motion.div variants={itemVariants} className="auth-modal-checkbox-group">
                          <label htmlFor="accept-terms" className="auth-modal-checkbox-label">
                            <input
                              type="checkbox"
                              id="accept-terms"
                              checked={acceptTerms}
                              onChange={(e) => setAcceptTerms(e.target.checked)}
                              disabled={loading}
                            />
                            {acceptTerms ? <CheckSquare size={18} className="checked-icon"/> : <Square size={18} className="unchecked-icon"/>}
                            <span>I accept the <a href="/terms-of-service" target="_blank" rel="noopener noreferrer" className="auth-modal-inline-link">Terms_Of_Service</a></span>
                          </label>
                        </motion.div>
                        <motion.div variants={itemVariants} className="auth-modal-checkbox-group">
                          <label htmlFor="share-data" className="auth-modal-checkbox-label">
                            <input
                              type="checkbox"
                              id="share-data"
                              checked={shareData}
                              onChange={(e) => setShareData(e.target.checked)}
                              disabled={loading}
                            />
                            {shareData ? <CheckSquare size={18} className="checked-icon"/> : <Square size={18} className="unchecked-icon"/>}
                            <span>I agree to the <a href="/data-sharing-policy" target="_blank" rel="noopener noreferrer" className="auth-modal-inline-link">Data_Sharing_Policy</a></span>
                          </label>
                        </motion.div>

                        <motion.button
                          variants={itemVariants}
                          className="auth-modal-button primary"
                          onClick={handleRegister}
                          disabled={loading}
                        >
                          {loading ? 'Initializing...' : 'Register_Account'} <ChevronRight size={16} />
                        </motion.button>
                      </>
                    )}

                    {view === 'forgot-password' && (
                      <>
                        <motion.h2 variants={itemVariants} className="auth-modal-title-main">
                          <CodeTitle>Password Recovery</CodeTitle>
                        </motion.h2>
                        <motion.div variants={itemVariants} className="auth-modal-input-group">
                          <label htmlFor="recovery-email">
                            Registered Email
                          </label>
                          <input
                            id="recovery-email"
                            type="email"
                            placeholder="Enter your account email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            onKeyDown={handleKeyPress}
                            disabled={loading}
                          />
                        </motion.div>
                        <motion.p variants={itemVariants} className="auth-modal-info-text">
                          An email will be sent with a reset link.
                        </motion.p>
                        <motion.button
                          variants={itemVariants}
                          className="auth-modal-button primary"
                          onClick={handleForgotPassword}
                          disabled={loading}
                        >
                          {loading ? 'Processing...' : 'Send_Reset_Instructions'} <ChevronRight size={16} />
                        </motion.button>
                      </>
                    )}

                    {(error || message) && (
                      <motion.div
                        variants={itemVariants}
                        className={`auth-modal-feedback ${error ? 'error' : 'message'}`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        role="alert"
                      >
                        {error ? <AlertTriangle size={18} /> : <Info size={18} />}
                        <span>{error || message}</span>
                      </motion.div>
                    )}

                    <motion.div variants={itemVariants} className="auth-modal-divider">
                      <span className="auth-modal-divider-text">Or connect with:</span>
                    </motion.div>

                    <motion.div variants={itemVariants} className="auth-modal-social-logins">
                      <button
                        type="button"
                        className="auth-modal-social-button steam"
                        disabled
                      >
                        <SteamIcon className="social-icon" /> Steam <span className="soon-tag">(SOON)</span>
                      </button>
                      <button
                        type="button"
                        className="auth-modal-social-button discord"
                        onClick={handleDiscordAuth}
                        disabled={loading}
                      >
                        <DiscordIcon className="social-icon" /> Discord
                      </button>
                      <button
                        type="button"
                        className="auth-modal-social-button google"
                        onClick={handleGoogleAuth}
                        disabled={loading}
                      >
                        <GoogleIcon className="social-icon" /> Google
                      </button>
                    </motion.div>

                    <motion.div variants={itemVariants} className="auth-modal-footer-legal">
                      <p>
                        By continuing you agree to our <a href="/terms-of-service" target="_blank" rel="noopener noreferrer">Terms_of_Service</a> and <a href="/privacy-policy" target="_blank" rel="noopener noreferrer">Privacy_Policy</a>.
                      </p>
                      <p>&copy; {new Date().getFullYear()} YourBrand. All rights reserved.</p>
                    </motion.div>
                  </motion.div>
                </AnimatePresence>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}