'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Eye, Heart, Calendar } from 'lucide-react';

interface FeaturedReviewData {
  id: string;
  title: string;
  game_name: string;
  main_image_url?: string;
  overall_score: number;
  content_lexical: any;
  like_count: number;
  view_count: number;
  created_at: string;
}

interface SimpleFeaturedReviewProps {
  userId: string;
}

export default function SimpleFeaturedReview({ userId }: SimpleFeaturedReviewProps) {
  const [featuredReview, setFeaturedReview] = useState<FeaturedReviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedReview = async () => {
      try {
        setLoading(true);
        console.log('🎯 SimpleFeaturedReview: Fetching for user:', userId);
        
        const response = await fetch('/api/u/featured-review', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getFeatured',
            userId: userId
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('🎯 SimpleFeaturedReview: API response:', result);

        if (result.success && result.data) {
          setFeaturedReview(result.data);
        } else {
          setFeaturedReview(null);
        }
      } catch (err) {
        console.error('❌ SimpleFeaturedReview: Error:', err);
        setError(err instanceof Error ? err.message : 'Failed to load featured review');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchFeaturedReview();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className="bg-gray-900/50 border border-gray-700 rounded-xl p-6 animate-pulse">
        <div className="h-4 bg-gray-700 rounded w-1/3 mb-4"></div>
        <div className="h-20 bg-gray-700 rounded mb-4"></div>
        <div className="h-4 bg-gray-700 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-700 rounded-xl p-6">
        <p className="text-red-400">Error loading featured review: {error}</p>
      </div>
    );
  }

  if (!featuredReview) {
    return null; // Don't show anything if no featured review
  }

  // Parse review content if it's JSON
  let reviewText = '';
  try {
    if (typeof featuredReview.content_lexical === 'string') {
      const parsed = JSON.parse(featuredReview.content_lexical);
      // Extract text from Lexical JSON structure
      reviewText = parsed.root?.children?.[0]?.children?.[0]?.text || '';
    } else if (featuredReview.content_lexical?.root?.children) {
      reviewText = featuredReview.content_lexical.root.children[0]?.children?.[0]?.text || '';
    }
  } catch (e) {
    reviewText = 'Review content unavailable';
  }

  return (
    <div className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 border border-purple-500/30 rounded-xl p-6 mb-6">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-purple-600/20 rounded-lg">
          <Star className="h-5 w-5 text-purple-400" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-white">
            Review em Destaque
          </h2>
          <p className="text-sm text-gray-400">
            Escolhida especialmente pelo usuário
          </p>
        </div>
      </div>

      <div className="bg-black/30 rounded-lg p-4 border border-gray-700/50">
        <div className="flex items-start gap-4">
          {/* Game Image */}
          {featuredReview.main_image_url && (
            <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
              <img 
                src={featuredReview.main_image_url} 
                alt={featuredReview.game_name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}

          {/* Content */}
          <div className="flex-1">
            <h3 className="text-lg font-bold text-white mb-2">
              {featuredReview.title || featuredReview.game_name}
            </h3>
            
            <div className="flex items-center gap-4 mb-3">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-white font-medium">
                  {featuredReview.overall_score}/10
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <Heart className="h-4 w-4 text-red-400" />
                <span className="text-gray-300 text-sm">
                  {featuredReview.like_count || 0}
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4 text-blue-400" />
                <span className="text-gray-300 text-sm">
                  {featuredReview.view_count || 0}
                </span>
              </div>
            </div>

            {reviewText && (
              <p className="text-gray-300 text-sm line-clamp-3">
                {reviewText.length > 150 ? `${reviewText.substring(0, 150)}...` : reviewText}
              </p>
            )}

            <div className="flex items-center gap-1 mt-3 text-xs text-gray-500">
              <Calendar className="h-3 w-3" />
              <span>
                {new Date(featuredReview.created_at).toLocaleDateString('pt-BR')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 