/* Performance Survey Component Styles - Scoped to avoid conflicts */

/*
Z-INDEX HIERARCHY:
- IGDB Combobox: 9999
- Performance Survey Overlay: 10000
- Performance Survey Modal: 10001
- Performance Survey Content: 10002
- Performance Survey Search Container: 10003
- Performance Survey Search Input: 10004
- Performance Survey Search Suggestions: 10005
*/

/* Main overlay and modal */
.perf-survey__overlay {
  position: fixed;
  inset: 0;
  z-index: 99998; /* Very high z-index to ensure it's above everything including game search dropdown */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(16, 18, 27, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  /* Isolation to prevent conflicts with other components */
  isolation: isolate;
  contain: layout style paint;
  /* Prevent interaction with elements behind the modal */
  pointer-events: auto;
}

.perf-survey__modal {
  width: 100%;
  max-width: 32rem;
  max-height: 90vh;
  overflow: visible; /* Allow suggestions to show outside modal */
  position: relative;
  z-index: 10001;
}

.perf-survey__content {
  border-radius: 1rem;
  padding: 1.5rem;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(12px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 10002;
}

/* Progress bar */
.perf-survey__progress-container {
  margin-bottom: 1.5rem;
  padding: 0 1.5rem;
}

.perf-survey__progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.perf-survey__progress-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.perf-survey__progress-text {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

.perf-survey__progress-completion {
  font-size: 0.75rem;
  color: #64748b;
}

/* Mini step indicators */
.perf-survey__mini-steps {
  display: flex;
  gap: 0.375rem;
  align-items: center;
}

.perf-survey__mini-step {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
}

.perf-survey__mini-step.upcoming {
  background: rgba(100, 116, 139, 0.3);
}

.perf-survey__mini-step.visited {
  background: rgba(139, 92, 246, 0.6);
}

.perf-survey__mini-step.active {
  background: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
  transform: scale(1.2);
}

.perf-survey__mini-step.completed {
  background: #10b981;
}

.perf-survey__mini-step.completed::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 3px;
  background: white;
  border-radius: 50%;
}

.perf-survey__progress-bar {
  width: 100%;
  background: rgba(71, 85, 105, 0.3);
  border-radius: 9999px;
  height: 0.5rem;
}

.perf-survey__progress-fill {
  height: 0.5rem;
  border-radius: 9999px;
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  transition: width 0.5s ease-out;
}

/* Section titles */
.perf-survey__section-title {
  text-align: center;
  margin-bottom: 1.5rem;
}

.perf-survey__section-title h3 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #f1f5f9;
  font-family: monospace;
}

/* Icon containers */
.perf-survey__icon-container {
  padding: 1rem;
  border-radius: 9999px;
  width: fit-content;
  margin: 0 auto;
}

.perf-survey__icon-container--primary {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.2));
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.4);
}

.perf-survey__icon-container--success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(139, 92, 246, 0.2));
  box-shadow: 0 0 40px rgba(16, 185, 129, 0.4);
  padding: 1.5rem;
}

/* Buttons */
.perf-survey__button {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  font-weight: 500;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  font-family: inherit;
  font-size: inherit;
}

.perf-survey__button:hover:not(:disabled) {
  transform: scale(1.05);
}

.perf-survey__button:active:not(:disabled) {
  transform: scale(0.95);
}

.perf-survey__button--primary {
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  color: #f1f5f9;
  padding: 0.75rem 1.5rem;
}

.perf-survey__button--primary:disabled {
  background: rgba(139, 92, 246, 0.2);
  color: #64748b;
  transform: none;
  cursor: not-allowed;
}

.perf-survey__button--secondary {
  background: transparent;
  border: 1px solid rgba(139, 92, 246, 0.2);
  color: #94a3b8;
  padding: 0.5rem 1rem;
}

.perf-survey__button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #f1f5f9;
  padding: 0.75rem 1.5rem;
}

.perf-survey__button--full {
  width: 100%;
}

.perf-survey__button--flex-1 {
  flex: 1;
}

.perf-survey__button--large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.perf-survey__button--close {
  padding: 0.5rem;
  border-radius: 0.5rem;
  color: #64748b;
  background: transparent;
}

.perf-survey__button--close:hover {
  transform: scale(1.1);
}

/* Option cards */
.perf-survey__option-card {
  position: relative;
  padding: 0.625rem; /* Further reduced from 0.75rem */
  border-radius: 0.75rem;
  border: 2px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  cursor: pointer;
  background: rgba(30, 33, 45, 0.6);
  backdrop-filter: blur(12px);
  font-family: inherit;
  font-size: inherit;
}

.perf-survey__option-card:hover:not(.perf-survey__option-card--disabled) {
  transform: scale(1.02);
}

.perf-survey__option-card:active:not(.perf-survey__option-card--disabled) {
  transform: scale(0.98);
}

.perf-survey__option-card--selected {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(6, 182, 212, 0.15));
  border: 2px solid rgba(139, 92, 246, 0.6);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.perf-survey__option-card--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.perf-survey__option-content {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced from 0.75rem */
}

.perf-survey__option-icon {
  flex-shrink: 0;
  color: #94a3b8;
}

.perf-survey__option-icon--selected {
  color: #8b5cf6;
}

.perf-survey__option-text {
  flex: 1;
  min-width: 0;
}

.perf-survey__option-title {
  font-weight: 500;
  font-size: 0.875rem; /* Increased back to original size */
  color: #f1f5f9;
}

.perf-survey__option-subtitle {
  font-size: 0.6875rem; /* Keep small subtitle size */
  margin-top: 0.125rem; /* Keep reduced margin */
  color: #64748b;
}

.perf-survey__option-check {
  flex-shrink: 0;
}

/* Search input */
.perf-survey__search-container {
  position: relative;
  z-index: 10003; /* Higher than modal content */
}

.perf-survey__search-wrapper {
  position: relative;
}

.perf-survey__search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  z-index: 10004;
  pointer-events: none;
}

.perf-survey__search-input {
  padding-left: 2.5rem !important;
  border: 2px solid rgba(139, 92, 246, 0.2) !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s ease !important;
  background: rgba(24, 27, 36, 0.8) !important;
  color: #f1f5f9 !important;
  width: 100% !important;
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
  padding-right: 1rem !important;
  position: relative;
  z-index: 10004;
}

.perf-survey__search-input:focus {
  border: 2px solid rgba(139, 92, 246, 0.5) !important;
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.2) !important;
  outline: none !important;
}

.perf-survey__search-input::placeholder {
  color: #64748b !important;
}

.perf-survey__search-suggestions {
  border-radius: 0.75rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
  z-index: 10005; /* Consistent with modal hierarchy */
  background: rgba(24, 27, 36, 0.98);
  backdrop-filter: blur(12px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  max-height: 200px;
  overflow-y: auto;
  min-width: 200px; /* Ensure minimum width */
}

.perf-survey__search-suggestion {
  width: 100%;
  padding: 0.75rem;
  text-align: left;
  color: #f1f5f9;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: inherit;
  font-size: inherit;
  display: block;
}

.perf-survey__search-suggestion:hover {
  background: rgba(139, 92, 246, 0.1);
}

.perf-survey__search-suggestion:first-child {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.perf-survey__search-suggestion:last-child {
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

/* Hardware-specific search suggestions */

.perf-survey__hardware-suggestion {
  width: 100%;
  padding: 0.875rem;
  text-align: left;
  color: #f1f5f9;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-family: inherit;
  font-size: inherit;
  display: block;
}

.perf-survey__hardware-suggestion:hover {
  background: rgba(139, 92, 246, 0.1);
}

.perf-survey__hardware-suggestion-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.perf-survey__hardware-suggestion-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.perf-survey__hardware-suggestion-name {
  font-weight: 500;
  font-size: 0.875rem;
  color: #f1f5f9;
}

.perf-survey__hardware-suggestion-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.perf-survey__hardware-suggestion-manufacturer {
  color: #94a3b8;
  font-weight: 500;
}

.perf-survey__hardware-suggestion-specs {
  color: #8b5cf6;
  font-weight: 500;
}

/* Loading indicator for search */
.perf-survey__search-loading {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #8b5cf6;
  z-index: 10004;
  pointer-events: none;
}

/* Info boxes */
.perf-survey__info-box {
  font-size: 0.875rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.perf-survey__info-box--gray {
  color: #94a3b8;
}

.perf-survey__info-box--warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

/* Layout utilities */
.perf-survey__space-y-2 > * + * {
  margin-top: 0.375rem; /* Reduced from 0.5rem */
}

.perf-survey__space-y-3 > * + * {
  margin-top: 0.5rem; /* Reduced from 0.75rem */
}

.perf-survey__space-y-4 > * + * {
  margin-top: 0.75rem; /* Reduced from 1rem */
}

.perf-survey__space-y-6 > * + * {
  margin-top: 1rem; /* Reduced from 1.5rem */
}

.perf-survey__grid-cols-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem; /* Reduced from 0.75rem */
}

.perf-survey__grid-cols-2--small-gap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.25rem; /* Further reduced from 0.375rem */
}

.perf-survey__flex-wrap-gap {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.perf-survey__flex-wrap-gap > * {
  flex: 1 1 calc(50% - 0.125rem);
  min-width: 8rem;
}

.perf-survey__flex-gap-3 {
  display: flex;
  gap: 0.75rem;
}

.perf-survey__text-center {
  text-align: center;
}

.perf-survey__text-sm {
  font-size: 0.875rem;
}

.perf-survey__text-xs {
  font-size: 0.75rem;
}

.perf-survey__text-xl {
  font-size: 1.25rem;
}

.perf-survey__font-medium {
  font-weight: 500;
}

.perf-survey__mb-2 {
  margin-bottom: 0.5rem;
}

.perf-survey__mb-3 {
  margin-bottom: 0.75rem;
}

.perf-survey__mb-4 {
  margin-bottom: 1rem;
}

.perf-survey__ml-2 {
  margin-left: 0.5rem;
}

.perf-survey__ml-4 {
  margin-left: 1rem;
}

.perf-survey__mr-2 {
  margin-right: 0.5rem;
}

.perf-survey__mt-1 {
  margin-top: 0.25rem;
}

.perf-survey__mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.perf-survey__py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.perf-survey__py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.perf-survey__py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.perf-survey__px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Color utilities */
.perf-survey__text-slate-400 {
  color: #94a3b8;
}

.perf-survey__text-slate-500 {
  color: #64748b;
}

.perf-survey__text-white {
  color: #f1f5f9;
}

/* Performance indicator colors */
.perf-survey__performance-red {
  color: #ef4444;
}

.perf-survey__performance-orange {
  color: #f59e0b;
}

.perf-survey__performance-green {
  color: #10b981;
}

.perf-survey__performance-purple {
  color: #8b5cf6;
}

/* Memory speed indicator */
.perf-survey__memory-speed-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 9999px;
  background: #a855f7;
}

/* Graphics preset indicators */
.perf-survey__preset-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 1.5rem;
}

.perf-survey__preset-bars {
  display: flex;
  align-items: end;
  gap: 1px;
  height: 1rem;
}

.perf-survey__preset-bar {
  width: 3px;
  background: rgba(100, 116, 139, 0.3);
  border-radius: 1px;
  transition: all 0.3s ease;
}

.perf-survey__preset-bar:nth-child(1) {
  height: 25%;
}

.perf-survey__preset-bar:nth-child(2) {
  height: 50%;
}

.perf-survey__preset-bar:nth-child(3) {
  height: 75%;
}

.perf-survey__preset-bar:nth-child(4) {
  height: 100%;
}

.perf-survey__preset-bar--active {
  background: #8b5cf6;
}

.perf-survey__option-card--selected .perf-survey__preset-bar--active {
  background: #a855f7;
  box-shadow: 0 0 4px rgba(168, 85, 247, 0.4);
}

/* Responsive design */
@media (max-width: 640px) {
  .perf-survey__modal {
    max-width: 100%;
    margin: 1rem;
  }
  
  .perf-survey__content {
    padding: 1rem;
  }
  
  .perf-survey__progress-container {
    padding: 0 1rem;
  }
}

/* Additional fixes for focus and interaction issues */
.perf-survey__search-input:focus-visible {
  outline: none !important;
}

/* Ensure suggestions are clickable */
.perf-survey__search-suggestions * {
  pointer-events: auto;
}

/* Prevent text selection in option cards */
.perf-survey__option-card {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}