'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, useInView, useMotionValue, useSpring } from 'framer-motion';
import { cn } from '@/lib/utils';

// Componente de partículas flutuantes para backgrounds
export const FloatingParticles = ({
  count = 30,
  theme
}: {
  count?: number;
  theme: any;
}) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    duration: number;
    delay: number;
  }>>([]);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Generate particles only on client side to avoid hydration mismatch
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      duration: Math.random() * 20 + 10,
      delay: Math.random() * 5
    }));
    setParticles(newParticles);
  }, [count]);

  if (!isMounted) {
    return <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30" />;
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: theme?.colors?.accent || '#8b5cf6'
          }}
          animate={{
            y: [0, -50, 0],
            opacity: [0.2, 0.6, 0.2],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

// Componente de borda animada/brilhante
export const AnimatedBorder = ({ 
  children, 
  className, 
  theme,
  isActive = false 
}: { 
  children: React.ReactNode; 
  className?: string; 
  theme: any;
  isActive?: boolean;
}) => {
  return (
    <div className={cn("relative", className)}>
      {/* Borda animada */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          backgroundImage: isActive 
            ? `linear-gradient(45deg, ${theme?.colors?.primary}, ${theme?.colors?.secondary}, ${theme?.colors?.accent}, ${theme?.colors?.primary})`
            : `linear-gradient(45deg, ${theme?.colors?.primary}50, transparent)`,
          backgroundSize: '400% 400%',
          backgroundRepeat: 'no-repeat'
        }}
        animate={{
          backgroundPosition: isActive ? ['0% 50%', '100% 50%', '0% 50%'] : '0% 50%'
        }}
        transition={{
          duration: 3,
          repeat: isActive ? Infinity : 0,
          ease: "easeInOut"
        }}
      />
      
      {/* Conteúdo */}
      <div className="relative bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 m-[1px]">
        {children}
      </div>
    </div>
  );
};

// Efeito de cursor mágico
export const MagicCursor = ({ children }: { children: React.ReactNode }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const element = ref.current;
    if (element) {
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseenter', () => setIsHovering(true));
      element.addEventListener('mouseleave', () => setIsHovering(false));

      return () => {
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseenter', () => setIsHovering(true));
        element.removeEventListener('mouseleave', () => setIsHovering(false));
      };
    }
  }, []);

  return (
    <div ref={ref} className="relative overflow-hidden">
      {/* Efeito de brilho que segue o cursor */}
      {isHovering && (
        <motion.div
          className="absolute pointer-events-none rounded-full"
          style={{
            width: '200px',
            height: '200px',
            backgroundImage: 'radial-gradient(circle, rgba(139, 92, 246, 0.15), transparent 70%)',
            x: mousePosition.x - 100,
            y: mousePosition.y - 100
          }}
          transition={{ type: "spring", damping: 30, stiffness: 200 }}
        />
      )}
      {children}
    </div>
  );
};

// Componente de texto com efeito shimmer
export const ShimmerText = ({ 
  children, 
  className,
  theme 
}: { 
  children: React.ReactNode; 
  className?: string;
  theme: any;
}) => {
  return (
    <motion.div
      className={cn("relative inline-block", className)}
      whileHover="hover"
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        variants={{
          hover: {
            x: ['-100%', '100%'],
            transition: {
              duration: 0.6,
              ease: "easeInOut"
            }
          }
        }}
        style={{
          backgroundImage: `linear-gradient(90deg, transparent, ${theme?.colors?.accent}40, transparent)`
        }}
      />
      <span className="relative z-10">{children}</span>
    </motion.div>
  );
};

// Componente de grid pattern animado para backgrounds
export const AnimatedGridPattern = ({ theme }: { theme: any }) => {
  return (
    <div className="absolute inset-0 opacity-20">
      <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern
            id="grid"
            width="40"
            height="40"
            patternUnits="userSpaceOnUse"
          >
            <motion.path
              d="M 40 0 L 0 0 0 40"
              fill="none"
              stroke={theme?.colors?.primary || '#8b5cf6'}
              strokeWidth="1"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ pathLength: 1, opacity: 0.3 }}
              transition={{ duration: 2, delay: 0.5 }}
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>
  );
};

// Componente de número animado (counter)
export const AnimatedCounter = ({ 
  value, 
  duration = 2000,
  className 
}: { 
  value: number; 
  duration?: number;
  className?: string;
}) => {
  const ref = useRef<HTMLSpanElement>(null);
  const inView = useInView(ref);
  const motionValue = useMotionValue(0);
  const springValue = useSpring(motionValue, { duration: duration });
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    if (inView) {
      motionValue.set(value);
    }
  }, [inView, value, motionValue]);

  useEffect(() => {
    springValue.onChange((latest) => {
      setDisplayValue(Math.floor(latest));
    });
  }, [springValue]);

  return (
    <span ref={ref} className={className}>
      {displayValue.toLocaleString()}
    </span>
  );
};

// Componente de ícone com efeito orbiting
export const OrbitingIcon = ({ 
  children, 
  radius = 50, 
  duration = 10,
  reverse = false
}: { 
  children: React.ReactNode; 
  radius?: number; 
  duration?: number;
  reverse?: boolean;
}) => {
  return (
    <motion.div
      className="absolute"
      animate={{
        rotate: reverse ? -360 : 360
      }}
      transition={{
        duration: duration,
        repeat: Infinity,
        ease: "linear"
      }}
      style={{
        width: radius * 2,
        height: radius * 2,
        left: '50%',
        top: '50%',
        marginLeft: -radius,
        marginTop: -radius
      }}
    >
      <div
        className="absolute"
        style={{
          top: 0,
          left: '50%',
          transform: 'translateX(-50%)'
        }}
      >
        <motion.div
          animate={{
            rotate: reverse ? 360 : -360
          }}
          transition={{
            duration: duration,
            repeat: Infinity,
            ease: "linear"
          }}
        >
          {children}
        </motion.div>
      </div>
    </motion.div>
  );
};

// Componente de progresso circular animado
export const AnimatedProgress = ({ 
  percentage, 
  size = 60, 
  strokeWidth = 6,
  theme 
}: { 
  percentage: number; 
  size?: number; 
  strokeWidth?: number;
  theme: any;
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (percentage / 100) * circumference;

  return (
    <div className="relative inline-block">
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-700"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={theme?.colors?.primary || '#8b5cf6'}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeLinecap="round"
          initial={{ strokeDasharray: circumference, strokeDashoffset: circumference }}
          animate={{ strokeDashoffset: offset }}
          transition={{ duration: 1.5, ease: "easeOut" }}
        />
      </svg>
      
      {/* Percentage text */}
      <div className="absolute inset-0 flex items-center justify-center">
        <AnimatedCounter 
          value={percentage} 
          className="text-xs font-bold text-white"
        />
      </div>
    </div>
  );
};

// Componente de confetti para celebrar conquistas
export const ConfettiEffect = ({ 
  isActive, 
  onComplete 
}: { 
  isActive: boolean; 
  onComplete?: () => void;
}) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    color: string;
    size: number;
    rotation: number;
  }>>([]);

  useEffect(() => {
    if (isActive) {
      const newParticles = Array.from({ length: 50 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: -10,
        color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'][Math.floor(Math.random() * 6)],
        size: Math.random() * 6 + 4,
        rotation: Math.random() * 360
      }));
      
      setParticles(newParticles);
      
      setTimeout(() => {
        setParticles([]);
        onComplete?.();
      }, 3000);
    }
  }, [isActive, onComplete]);

  if (!isActive) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            backgroundColor: particle.color,
            width: `${particle.size}px`,
            height: `${particle.size}px`
          }}
          initial={{
            y: -100,
            rotate: particle.rotation,
            opacity: 1
          }}
          animate={{
            y: window.innerHeight + 100,
            rotate: particle.rotation + 360,
            opacity: 0
          }}
          transition={{
            duration: 3,
            ease: "easeOut"
          }}
        />
      ))}
    </div>
  );
};

// Wrapper principal que combina múltiplos efeitos
export const MagicContainer = ({ 
  children, 
  theme, 
  effects = ['particles', 'cursor', 'border'],
  className 
}: { 
  children: React.ReactNode; 
  theme: any;
  effects?: ('particles' | 'cursor' | 'border' | 'grid')[];
  className?: string;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  let content = children;

  // Aplicar efeitos condicionalmente
  if (effects.includes('cursor')) {
    content = <MagicCursor>{content}</MagicCursor>;
  }

  if (effects.includes('border')) {
    content = (
      <AnimatedBorder theme={theme} isActive={isHovered} className={className}>
        {content}
      </AnimatedBorder>
    );
  }

  return (
    <div 
      className={cn("relative", !effects.includes('border') && className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Particles background */}
      {effects.includes('particles') && <FloatingParticles theme={theme} count={15} />}
      
      {/* Grid pattern */}
      {effects.includes('grid') && <AnimatedGridPattern theme={theme} />}
      
      {content}
    </div>
  );
}; 