import { NextRequest, NextResponse } from 'next/server';
import { getGameBySlug, getSimilarGames } from '@/lib/services/gameService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    
    const limit = parseInt(searchParams.get('limit') || '6');
    
    // Get game first
    const game = await getGameBySlug(slug);
    
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const similarGames = await getSimilarGames(game.id, limit);

    return NextResponse.json({
      similar_games: similarGames,
      count: similarGames.length
    });
  } catch (error) {
    console.error('Error fetching similar games:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}