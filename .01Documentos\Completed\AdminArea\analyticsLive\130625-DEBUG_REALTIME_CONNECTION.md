# Debug: Problema de Conexão Real-time Analytics

## Problema Atual

O usuário clica em "Conectar" mas a conexão WebSocket não é estabelecida. <PERSON><PERSON> logs mostram:

```
🔄 Force reconnecting...
🔍 Window focused, checking connection...
🔄 Force reconnecting...
👁️ Window blurred
```

Mas **NÃO** vemos os logs esperados de:
- `🚀 First time initialization...`
- `🔄 Initializing real-time connection...`
- `🔍 Testing database connection...`
- `📡 Creating WebSocket channel...`

## Possíveis Causas

### 1. **Problemas de Configuração do Supabase**
- ❓ Variáveis de ambiente não configuradas
- ❓ URL ou chave anônima inválidas
- ❓ Cliente Supabase não inicializado corretamente

### 2. **Problemas de Banco de Dados**
- ❓ Tabelas `user_sessions` ou `content_performance` não existem
- ❓ Permissões RLS bloqueando consultas
- ❓ Estrutura de tabelas incompatível

### 3. **Problemas de WebSocket**
- ❓ Firewall bloqueando conexões WebSocket
- ❓ Proxy ou rede corporativa interferindo
- ❓ Configuração de Real-time do Supabase desabilitada

## Steps de Debug

### Passo 1: Verificar Configuração
```bash
# Verificar se as variáveis de ambiente estão definidas
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY
```

### Passo 2: Testar Conectividade Básica
1. Abrir o Console do Navegador
2. Ir para a aba "Tempo Real" no dashboard
3. Clicar no botão "🔍 Diagnóstico"
4. Verificar os logs no console

### Passo 3: Logs Esperados vs Recebidos

**Esperado (mas não visto):**
```
🚀 First time initialization...
🔄 Initializing real-time connection...
📡 Supabase client: true
🔍 Testing database connection...
✅ Database tables accessible
📡 Creating WebSocket channel...
📊 Real-time connection status: SUBSCRIBED
✅ Real-time analytics connected successfully
```

**Recebido:**
```
🔄 Force reconnecting...
🔍 Window focused, checking connection...
👁️ Window blurred
```

## Melhorias Implementadas

### 1. **Logs de Debug Adicionados**
- ✅ Teste de conectividade do banco
- ✅ Verificação de cliente Supabase
- ✅ Logs detalhados de cada etapa
- ✅ Tratamento de erros melhorado

### 2. **Interface de Debug**
- ✅ Botão "🔍 Diagnóstico" para logs manuais
- ✅ Indicadores visuais de status
- ✅ Mensagens de erro contextualizadas

### 3. **Controles de Recuperação**
- ✅ Funções assíncronas com tratamento de erro
- ✅ Cleanup robusto de recursos
- ✅ Prevenção de vazamentos de memória

## Próximos Passos

1. **Executar Diagnóstico Manual**
   - Clicar no botão "🔍 Diagnóstico"
   - Verificar logs no console
   - Anotar quais logs aparecem/não aparecem

2. **Verificar Configuração Supabase**
   ```javascript
   // No console do navegador:
   console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
   console.log('SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
   ```

3. **Testar Conectividade Direta**
   ```javascript
   // No console do navegador:
   import { createClient } from '@supabase/supabase-js';
   const supabase = createClient('YOUR_URL', 'YOUR_ANON_KEY');
   const { data, error } = await supabase.from('user_sessions').select('*').limit(1);
   console.log('Direct test result:', { data, error });
   ```

## Arquivos Modificados

- ✅ `src/hooks/useRealTimeAnalytics.ts` - Logs debug e teste de conectividade
- ✅ `src/app/admin/analytics/page.tsx` - Botão de diagnóstico
- ✅ `REALTIME_ANALYTICS_FIXES.md` - Documentação das melhorias

## Status

🔄 **Aguardando teste manual do usuário**

Por favor, execute os passos de debug acima e reporte os resultados para que possamos identificar a causa raiz do problema. 