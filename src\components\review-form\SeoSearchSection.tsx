'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  ArrowRight,
  Target,
  FileText,
  CheckCircle,
  AlertTriangle,
  Eye,
  Search,
  Info
} from 'lucide-react';

// Import the CSS to ensure button styles are loaded
import '@/components/review-form/style/NewReview.css';

interface SimplifiedSeoConfigProps {
  metaTitle: string;
  onMetaTitleChange: (value: string) => void;
  metaDescription: string;
  onMetaDescriptionChange: (value: string) => void;
  focusKeyword: string;
  onFocusKeywordChange: (value: string) => void;
  contentTitle: string;
  handlePrevStep: () => void;
  handleNextStep: () => void;
}

// Character counter component
const CharacterCounter = ({ current, max }: { current: number; max: number }) => {
  const percentage = (current / max) * 100;
  const isNearLimit = percentage > 80;
  const isOverLimit = percentage > 95;
  
  return (
    <div className="flex items-center space-x-2 mt-2">
      <div className="flex-1 h-1 bg-slate-800 rounded-full overflow-hidden">
        <div
          className={`h-full transition-colors duration-300 ${
            isOverLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-gradient-to-r from-violet-500 to-cyan-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      <span className={`text-xs font-mono transition-colors duration-300 ${
        isOverLimit ? 'text-red-400' : isNearLimit ? 'text-yellow-400' : 'text-slate-400'
      }`}>
        {current}/{max}
      </span>
    </div>
  );
};

const SimplifiedSeoConfig: React.FC<SimplifiedSeoConfigProps> = ({
  metaTitle,
  onMetaTitleChange,
  metaDescription,
  onMetaDescriptionChange,
  focusKeyword,
  onFocusKeywordChange,
  contentTitle,
  handlePrevStep,
  handleNextStep,
}) => {
  // Check if any content is provided
  const hasAnyContent = metaTitle.length > 0 || metaDescription.length > 0 || focusKeyword.length > 0;

  // Auto-populate meta title from content title if empty
  const handleMetaTitleFocus = () => {
    if (!metaTitle && contentTitle) {
      onMetaTitleChange(contentTitle.substring(0, 60));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">

        <div className="font-mono text-xs text-slate-500">
          <span className="text-violet-400">Optional</span>
          <span className="text-slate-600 mx-1">•</span>
          <span className="text-slate-500">Search Optimization</span>
        </div>
      </div>

      {/* Content Cards */}
      <div className="space-y-4">
        
        {/* Meta Title Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <Target className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Meta Title</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The clickable title that appears in search results
                </p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">SEARCH_TITLE</div>
                <Input
                  placeholder="Craft the perfect search-engine title (auto-fills from content title)"
                  value={metaTitle}
                  onChange={(e) => onMetaTitleChange(e.target.value)}
                  onFocus={handleMetaTitleFocus}
                  maxLength={60}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500 
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                />
                {metaTitle && (
                  <div className="absolute right-3 top-9 transform -translate-y-1/2">
                    {metaTitle.length <= 60 ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                )}
              </div>
              
              {metaTitle && (
                <CharacterCounter current={metaTitle.length} max={60} />
              )}
              
              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Eye className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      Search Result Preview
                    </div>
                    <p className="text-xs text-slate-400">
                      This appears as your clickable link in search results
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Meta Description Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <FileText className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Meta Description</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The preview text that appears below your title
                </p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">DESCRIPTION_TEXT</div>
                <Textarea
                  placeholder="Write a compelling preview that makes people want to click and read your content..."
                  value={metaDescription}
                  onChange={(e) => onMetaDescriptionChange(e.target.value)}
                  maxLength={160}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500 
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm
                           min-h-[80px] resize-none pr-10"
                />
                {metaDescription && (
                  <div className="absolute right-3 top-9">
                    {metaDescription.length <= 160 ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                )}
              </div>
              
              {metaDescription && (
                <CharacterCounter current={metaDescription.length} max={160} />
              )}
              
              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Eye className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      Search Snippet Preview
                    </div>
                    <p className="text-xs text-slate-400">
                      This preview text appears below your title in search results
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Focus Keyword Card */}
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <div className="p-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 rounded-md bg-slate-800/60">
                <Search className="h-4 w-4 text-slate-400" />
              </div>
              <div>
                <div className="font-mono text-sm">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-slate-300">Primary Keyword</span>
                  <span className="text-violet-400">/&gt;</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">
                  The main search term people will use to find your content
                </p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="relative">
                <div className="font-mono text-xs text-slate-400 mb-2">SEARCH_KEYWORD</div>
                <Input
                  placeholder="e.g., 'product review', 'tutorial guide', 'how to'"
                  value={focusKeyword}
                  onChange={(e) => onFocusKeywordChange(e.target.value)}
                  className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500 
                           focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                />
                {focusKeyword && (
                  <div className="absolute right-3 top-9 transform -translate-y-1/2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                  </div>
                )}
              </div>
              
              <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Search className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <div className="font-mono text-xs text-slate-300">
                      SEO Optimization Target
                    </div>
                    <p className="text-xs text-slate-400">
                      The main term people will search for to find your content
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 gap-4">
        <div className="flex items-center space-x-2 min-w-0">
          <div className="w-2 h-2 bg-yellow-400/60 rounded-full animate-pulse flex-shrink-0" />
          <span className="text-sm text-slate-400/80 font-mono break-words">
            {hasAnyContent
              ? '<SEO optimization configured />'
              : '<Optional: Configure SEO settings />'
            }
          </span>
        </div>

        <div className="flex items-center space-x-3 flex-shrink-0">
          <Button
            onClick={handlePrevStep}
            className="review-continue-button review-continue-disabled"
          >
            <div className="review-button-content">
              <ArrowLeft className="review-button-arrow" />
              <span className="review-code-brackets">&lt;</span>
              Previous
              <span className="review-code-brackets">/&gt;</span>
            </div>
          </Button>
          <Button
            onClick={handleNextStep}
            className="review-continue-button review-continue-ready"
          >
            <div className="review-button-content">
              <span className="review-code-brackets">&lt;</span>
              Continue
              <span className="review-code-brackets">/&gt;</span>
              <ArrowRight className="review-button-arrow" />
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedSeoConfig;

