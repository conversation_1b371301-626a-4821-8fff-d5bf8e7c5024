'use client';

import { useState, useEffect } from 'react';
import { getUserStats, getUserTotalViews, type UserStats } from '@/lib/services/userStatsService';

/**
 * React hook for getting comprehensive user stats on the client side
 * @param userId - The user ID to get stats for
 * @returns Object with stats, loading state, and error
 */
export function useUserStats(userId: string | null) {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setStats(null);
      setLoading(false);
      return;
    }

    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const userStats = await getUserStats(userId);
        setStats(userStats);
      } catch (err) {
        console.error('Error fetching user stats:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch user stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [userId]);

  return { stats, loading, error };
}

/**
 * React hook for getting just the total views (lightweight version)
 * @param userId - The user ID to get total views for
 * @returns Object with totalViews, loading state, and error
 */
export function useUserTotalViews(userId: string | null) {
  const [totalViews, setTotalViews] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) {
      setTotalViews(0);
      setLoading(false);
      return;
    }

    const fetchTotalViews = async () => {
      try {
        setLoading(true);
        setError(null);
        const views = await getUserTotalViews(userId);
        setTotalViews(views);
      } catch (err) {
        console.error('Error fetching user total views:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch total views');
      } finally {
        setLoading(false);
      }
    };

    fetchTotalViews();
  }, [userId]);

  return { totalViews, loading, error };
}
