'use client';

/**
 * Admin Comment Moderation Page
 * Provides comprehensive comment moderation interface for administrators
 * Created: 20/01/2025 - Admin System Completion
 */

import React, { useState, useEffect } from 'react';
import { useAuthContext } from '@/hooks/use-auth-context';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import {
  getCommentsForModeration,
  moderateComment,
  batchModerateComments,
  type CommentModerationData,
  type CommentModerationAction
} from '@/lib/admin/commentService';
import { CommentActionsDropdown } from '@/components/admin/CommentActionsDropdown';
import {
  MessageSquare,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  Pin,
  PinOff,
  Trash2,
  CheckCircle,
  Flag,
  AlertTriangle,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  User
} from 'lucide-react';
import Link from 'next/link';

type CommentStatus = 'all' | 'flagged' | 'deleted' | 'recent';
type SortBy = 'created_at' | 'updated_at' | 'upvotes' | 'downvotes' | 'flag_count';

export default function CommentModerationPage() {
  const { user, loading: authLoading } = useAuthContext();
  const { toast } = useToast();

  // State management
  const [comments, setComments] = useState<CommentModerationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedComments, setSelectedComments] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalComments, setTotalComments] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Filter and search state
  const [statusFilter, setStatusFilter] = useState<CommentStatus>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortBy>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Moderation state
  const [moderationNotes, setModerationNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const itemsPerPage = 20;

  // Load comments data
  const loadComments = async (page = 1, resetData = false) => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const result = await getCommentsForModeration(user.id, {
        status: statusFilter,
        search: searchQuery,
        sortBy,
        sortOrder,
        page,
        limit: itemsPerPage
      });

      if (resetData || page === 1) {
        setComments(result.comments);
      } else {
        setComments(prev => [...prev, ...result.comments]);
      }

      setTotalComments(result.total);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading comments:', error);
      toast({
        title: "Error loading comments",
        description: "Failed to load comment moderation data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial load and filter changes
  useEffect(() => {
    if (user?.id) {
      loadComments(1, true);
    }
  }, [user?.id, statusFilter, searchQuery, sortBy, sortOrder]);

  // Handle comment selection
  const toggleCommentSelection = (commentId: string) => {
    const newSelection = new Set(selectedComments);
    if (newSelection.has(commentId)) {
      newSelection.delete(commentId);
    } else {
      newSelection.add(commentId);
    }
    setSelectedComments(newSelection);
  };

  const selectAllComments = () => {
    if (selectedComments.size === comments.length) {
      setSelectedComments(new Set());
    } else {
      setSelectedComments(new Set(comments.map(c => c.id)));
    }
  };

  // Handle individual comment moderation
  const handleModerateComment = async (commentId: string, action: CommentModerationAction) => {
    if (!user?.id) return;

    try {
      setIsProcessing(true);
      const result = await moderateComment(user.id, commentId, action);

      if (result.success) {
        toast({
          title: "Comment moderated successfully",
          description: `Comment has been ${action.action}d`,
        });
        await loadComments(1, true); // Reload data
        setSelectedComments(new Set()); // Clear selection
      } else {
        toast({
          title: "Moderation failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Moderation error",
        description: "Failed to moderate comment",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle batch moderation
  const handleBatchModeration = async (action: CommentModerationAction) => {
    if (!user?.id || selectedComments.size === 0) return;

    try {
      setIsProcessing(true);
      const commentIds = Array.from(selectedComments);
      const result = await batchModerateComments(user.id, commentIds, action);

      if (result.success) {
        toast({
          title: "Batch moderation completed",
          description: `Successfully processed ${result.processed} comments`,
        });
      } else {
        toast({
          title: "Batch moderation partially failed",
          description: result.error,
          variant: "destructive"
        });
      }

      await loadComments(1, true); // Reload data
      setSelectedComments(new Set()); // Clear selection
    } catch (error) {
      toast({
        title: "Batch moderation error",
        description: "Failed to process batch moderation",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadComments(1, true);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge
  const getStatusBadge = (comment: CommentModerationData) => {
    if (comment.is_deleted) {
      return <Badge variant="destructive">Deleted</Badge>;
    }
    if (comment.is_pinned) {
      return <Badge variant="default">Pinned</Badge>;
    }
    if (comment.flag_count && comment.flag_count > 0) {
      return <Badge variant="secondary">Flagged ({comment.flag_count})</Badge>;
    }
    return <Badge variant="outline">Active</Badge>;
  };

  if (authLoading) {
    return (
      <AdminLayout title="Comment Moderation" description="Loading comment moderation interface...">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading comments...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!user?.isAdmin) {
    return (
      <AdminLayout title="Access Denied" description="Admin access required">
        <Card className="max-w-md mx-auto mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You need administrator privileges to access comment moderation.
            </CardDescription>
          </CardHeader>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Comment Moderation"
      description="Moderate user comments and manage community discussions"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Comment Moderation' }
      ]}
    >
      <div className="space-y-6">
        {/* Header Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Comments</p>
                  <p className="text-2xl font-bold">{totalComments}</p>
                </div>
                <MessageSquare className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Selected</p>
                  <p className="text-2xl font-bold">{selectedComments.size}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Current Page</p>
                  <p className="text-2xl font-bold">{currentPage}</p>
                </div>
                <Calendar className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <p className="text-2xl font-bold capitalize">{statusFilter}</p>
                </div>
                <Filter className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle>Filters & Search</CardTitle>
            <CardDescription>Filter and search comments for moderation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select value={statusFilter} onValueChange={(value: CommentStatus) => setStatusFilter(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Comments</SelectItem>
                    <SelectItem value="flagged">Flagged</SelectItem>
                    <SelectItem value="deleted">Deleted</SelectItem>
                    <SelectItem value="recent">Recent (24h)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Sort By</label>
                <Select value={sortBy} onValueChange={(value: SortBy) => setSortBy(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Created Date</SelectItem>
                    <SelectItem value="updated_at">Updated Date</SelectItem>
                    <SelectItem value="upvotes">Upvotes</SelectItem>
                    <SelectItem value="downvotes">Downvotes</SelectItem>
                    <SelectItem value="flag_count">Flag Count</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Order</label>
                <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Descending</SelectItem>
                    <SelectItem value="asc">Ascending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Search</label>
                <form onSubmit={handleSearch} className="flex gap-2">
                  <Input
                    placeholder="Search comments..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button type="submit" size="sm">
                    <Search className="h-4 w-4" />
                  </Button>
                </form>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Batch Actions */}
        {selectedComments.size > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Batch Actions ({selectedComments.size} selected)</CardTitle>
              <CardDescription>Apply actions to multiple comments at once</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" disabled={isProcessing}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve All
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Approve Selected Comments</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will approve {selectedComments.size} selected comments. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleBatchModeration({ action: 'approve' })}
                      >
                        Approve All
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" disabled={isProcessing}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete All
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Selected Comments</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will delete {selectedComments.size} selected comments. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleBatchModeration({ action: 'delete' })}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete All
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <Button
                  variant="outline"
                  onClick={() => handleBatchModeration({ action: 'pin' })}
                  disabled={isProcessing}
                >
                  <Pin className="mr-2 h-4 w-4" />
                  Pin All
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleBatchModeration({ action: 'flag' })}
                  disabled={isProcessing}
                >
                  <Flag className="mr-2 h-4 w-4" />
                  Flag All
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Comments List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Comments ({totalComments})</CardTitle>
                <CardDescription>Manage and moderate user comments</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedComments.size === comments.length && comments.length > 0}
                  onCheckedChange={selectAllComments}
                />
                <span className="text-sm text-muted-foreground">Select All</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-accent"></div>
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No comments found</h3>
                <p className="text-muted-foreground">
                  {searchQuery ? 'Try adjusting your search criteria.' : 'No comments match the current filters.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {comments.map((comment) => (
                  <div
                    key={comment.id}
                    className={`border rounded-lg p-4 transition-colors ${
                      selectedComments.has(comment.id) ? 'bg-accent/10 border-accent' : 'hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedComments.has(comment.id)}
                        onCheckedChange={() => toggleCommentSelection(comment.id)}
                      />

                      <div className="flex-1 space-y-3">
                        {/* Comment Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{comment.author_name}</span>
                            {comment.author_username && (
                              <span className="text-sm text-muted-foreground">@{comment.author_username}</span>
                            )}
                            {getStatusBadge(comment)}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            {formatDate(comment.created_at)}
                          </div>
                        </div>

                        {/* Review Context */}
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>On review:</span>
                          <Link
                            href={`/reviews/${comment.review_slug}`}
                            className="text-accent hover:underline flex items-center gap-1"
                            target="_blank"
                          >
                            {comment.review_title}
                            <ExternalLink className="h-3 w-3" />
                          </Link>
                        </div>

                        {/* Comment Content */}
                        <div className="bg-muted/30 rounded-md p-3">
                          <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                        </div>

                        {/* Comment Stats */}
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            {comment.upvotes}
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsDown className="h-4 w-4" />
                            {comment.downvotes}
                          </div>
                          {comment.flag_count && comment.flag_count > 0 && (
                            <div className="flex items-center gap-1 text-destructive">
                              <Flag className="h-4 w-4" />
                              {comment.flag_count} flags
                            </div>
                          )}
                        </div>

                        {/* Moderation Notes */}
                        {comment.moderation_notes && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                            <p className="text-sm font-medium text-yellow-800 mb-1">Moderation Notes:</p>
                            <p className="text-sm text-yellow-700">{comment.moderation_notes}</p>
                            {comment.last_moderated_at && (
                              <p className="text-xs text-yellow-600 mt-1">
                                Last moderated: {formatDate(comment.last_moderated_at)}
                              </p>
                            )}
                          </div>
                        )}

                        {/* Comment Actions */}
                        <div className="flex justify-end">
                          <CommentActionsDropdown
                            comment={comment}
                            onModerate={handleModerateComment}
                            disabled={isProcessing}
                            compact={true}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalComments > itemsPerPage && (
              <div className="flex items-center justify-between mt-6 pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalComments)} of {totalComments} comments
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadComments(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>
                  <span className="text-sm text-muted-foreground px-2">
                    Page {currentPage}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => loadComments(currentPage + 1)}
                    disabled={!hasMore || loading}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
