import type { UserProfile, UnifiedUserProfile } from '@/lib/types/profile';
import type { ExtendedUserProfile, GamingProfile, SocialMediaProfile, CustomColors } from '@/lib/types';

/**
 * Convert Supabase UserProfile to ExtendedUserProfile for component compatibility
 */
export function convertToExtendedProfile(profile: UserProfile): ExtendedUserProfile {
  return {
    // Manter todos os campos originais
    ...profile,

    // Adicionar campos de compatibilidade
    uid: profile.id,
    userName: profile.username,
    displayName: profile.display_name,
    avatarUrl: profile.avatar_url || undefined,
    bannerUrl: profile.banner_url || undefined,
    preferredGenres: profile.preferred_genres || [],
    favoriteConsoles: profile.favorite_consoles || [],
    reviewCount: profile.review_count || 0,
    isAdmin: profile.is_admin || false,
    isOnline: profile.is_online || false,
    
    // Garantir que não há campos undefined que quebrem a interface
    gamingProfiles: (profile.gaming_profiles as GamingProfile[]) || [],
    socialMedia: (profile.social_profiles as SocialMediaProfile[]) || [],

    // Conversão de dates com fallback seguro
    lastSeen: profile.last_seen ? new Date(profile.last_seen) : null,
    createdAt: profile.created_at ? new Date(profile.created_at) : new Date(),
    updatedAt: profile.updated_at ? new Date(profile.updated_at) : new Date(),
    
    // Configurações de privacidade com fallback
    privacySettings: profile.privacy_settings ? {
      showOnlineStatus: profile.privacy_settings.show_online_status,
      showGamingProfiles: profile.privacy_settings.show_gaming_profiles,
      allowFriendRequests: profile.privacy_settings.allow_friend_requests,
      showAchievements: profile.privacy_settings.show_achievements
    } : {
      showOnlineStatus: true,
      showGamingProfiles: true,
      allowFriendRequests: true,
      showAchievements: true
    },
    
    // Cores customizadas com fallback
    customColors: (profile.custom_colors as CustomColors) || { 
      primary: '#8b5cf6', 
      secondary: '#7c3aed', 
      accent: '#ec4899'
    }
  };
}

/**
 * Convert ExtendedUserProfile back to UserProfile format for database operations
 */
export function convertFromExtendedProfile(extendedProfile: ExtendedUserProfile): Partial<UserProfile> {
  const profile: Partial<UserProfile> = {};
  
  // Map legacy fields back to Supabase format
  if (extendedProfile.displayName !== undefined) {
    profile.display_name = extendedProfile.displayName;
  }
  if (extendedProfile.avatarUrl !== undefined) {
    profile.avatar_url = extendedProfile.avatarUrl;
  }
  if (extendedProfile.bannerUrl !== undefined) {
    profile.banner_url = extendedProfile.bannerUrl;
  }
  if (extendedProfile.preferredGenres !== undefined) {
    profile.preferred_genres = extendedProfile.preferredGenres;
  }
  if (extendedProfile.favoriteConsoles !== undefined) {
    profile.favorite_consoles = extendedProfile.favoriteConsoles;
  }
  if (extendedProfile.customColors !== undefined) {
    profile.custom_colors = extendedProfile.customColors;
  }
  
  // Convert privacy settings back to Supabase format
  if (extendedProfile.privacySettings) {
    profile.privacy_settings = {
      profile_visibility: 'public', // Default value
      show_online_status: extendedProfile.privacySettings.showOnlineStatus,
      show_gaming_profiles: extendedProfile.privacySettings.showGamingProfiles,
      show_achievements: extendedProfile.privacySettings.showAchievements,
      allow_contact: true, // Default value
      allow_friend_requests: extendedProfile.privacySettings.allowFriendRequests
    };
  }
  
  // Copy direct Supabase fields
  if (extendedProfile.bio !== undefined) profile.bio = extendedProfile.bio;
  if (extendedProfile.website !== undefined) profile.website = extendedProfile.website;
  if (extendedProfile.location !== undefined) profile.location = extendedProfile.location;
  if (extendedProfile.theme !== undefined) profile.theme = extendedProfile.theme;
  
  return profile;
}

/**
 * Type guard to check if a profile is in the new UserProfile format
 */
export function isUserProfile(profile: any): profile is UserProfile {
  return profile && typeof profile.id === 'string' && typeof profile.username === 'string';
}

/**
 * Type guard to check if a profile is in the legacy ExtendedUserProfile format
 */
export function isExtendedUserProfile(profile: any): profile is ExtendedUserProfile {
  return profile && typeof profile.uid === 'string' && typeof profile.userName === 'string';
}

/**
 * Safely convert any profile format to ExtendedUserProfile
 */
export function ensureExtendedProfile(profile: UserProfile | ExtendedUserProfile): ExtendedUserProfile {
  if (isUserProfile(profile)) {
    return convertToExtendedProfile(profile);
  }
  return profile as ExtendedUserProfile;
}

/**
 * Safely convert any profile format to UserProfile
 */
export function ensureUserProfile(profile: UserProfile | ExtendedUserProfile): UserProfile {
  if (isExtendedUserProfile(profile) && !isUserProfile(profile)) {
    // This is a legacy profile, we need to convert it
    // For now, we'll create a minimal UserProfile structure
    return {
      id: profile.uid || '',
      username: profile.userName || '',
      display_name: profile.displayName || '',
      slug: profile.slug || '',
      slug_lower: profile.slugLower || '',
      avatar_url: profile.avatarUrl || null,
      banner_url: profile.bannerUrl || null,
      bio: profile.bio || null,
      website: profile.website || null,
      location: profile.location || null,
      preferred_genres: profile.preferredGenres || null,
      favorite_consoles: profile.favoriteConsoles || null,
      theme: profile.theme || 'muted-dark',
      custom_colors: profile.customColors || null,
      is_admin: profile.isAdmin || false,
      is_online: profile.isOnline || false,
      last_seen: profile.lastSeen?.toISOString() || null,
      level: profile.level || 1,
      experience: profile.experience || 0,
      review_count: profile.reviewCount || 0,
      privacy_settings: profile.privacySettings ? {
        profile_visibility: 'public',
        show_online_status: profile.privacySettings.showOnlineStatus,
        show_gaming_profiles: profile.privacySettings.showGamingProfiles,
        show_achievements: profile.privacySettings.showAchievements,
        allow_contact: true,
        allow_friend_requests: profile.privacySettings.allowFriendRequests
      } : null,
      created_at: profile.createdAt?.toISOString() || new Date().toISOString(),
      updated_at: profile.updatedAt?.toISOString() || new Date().toISOString()
    };
  }
  return profile as UserProfile;
}
