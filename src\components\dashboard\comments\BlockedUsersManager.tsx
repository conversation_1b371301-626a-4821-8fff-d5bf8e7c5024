'use client';

// Blocked Users Manager Component
// Date: 22/06/2025
// Task: Blocked Users Management Implementation - Redesigned to match dashboard style

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  Users, 
  Shield, 
  UserX, 
  Calendar,
  Unlock,
  AlertTriangle,
  Loader2,
  User
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '@/hooks/use-toast';

interface BlockedUser {
  id: string;
  blocked_id: string;
  reason: string | null;
  created_at: string;
  blocked_user: {
    username: string;
    display_name: string | null;
    avatar_url: string | null;
  };
}

interface BlockedUsersManagerProps {
  userId: string;
}

export function BlockedUsersManager({ userId }: BlockedUsersManagerProps) {
  const queryClient = useQueryClient();
  const supabase = createClient();
  const [unbanDialogOpen, setUnbanDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<BlockedUser | null>(null);

  // Fetch blocked users
  const { data: blockedUsers, isLoading } = useQuery({
    queryKey: ['blocked-users', userId],
    queryFn: async (): Promise<BlockedUser[]> => {
      console.log('🔍 BlockedUsersManager: Starting query for userId:', userId);
      
      // Get user blocks first
      const { data: blocks, error } = await supabase
        .from('user_blocks')
        .select('id, blocked_id, reason, created_at')
        .eq('blocker_id', userId)
        .order('created_at', { ascending: false });

      console.log('🚩 Raw blocks query result:', blocks?.length || 0, blocks);

      if (error) {
        console.error('❌ Error fetching blocks:', error);
        throw error;
      }

      if (!blocks || blocks.length === 0) {
        console.log('❌ No blocks found, returning empty array');
        return [];
      }

      // Get user profile data separately for all blocked users
      const blockedUserIds = blocks.map(b => b.blocked_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, display_name, avatar_url')
        .in('id', blockedUserIds);

      if (profilesError) {
        console.error('❌ Error fetching profiles:', profilesError);
        throw profilesError;
      }

      console.log('👥 Profiles fetched:', profiles?.length || 0, profiles);

      // Create a map of profiles by ID
      const profileMap: Record<string, any> = {};
      if (profiles) {
        profiles.forEach(profile => {
          profileMap[profile.id] = {
            username: profile.username,
            display_name: profile.display_name,
            avatar_url: profile.avatar_url,
          };
        });
      }

      // Combine blocks with profile data
      const enrichedBlocks = blocks.map(block => ({
        id: block.id,
        blocked_id: block.blocked_id,
        reason: block.reason,
        created_at: block.created_at,
        blocked_user: profileMap[block.blocked_id] || {
          username: 'Unknown User',
          display_name: null,
          avatar_url: null,
        },
      }));

      console.log('✅ Returning', enrichedBlocks.length, 'enriched blocked users');
      return enrichedBlocks;
    },
    enabled: !!userId,
  });

  // Unban user mutation
  const unbanUser = useMutation({
    mutationFn: async (blockId: string) => {
      const { error } = await supabase
        .from('user_blocks')
        .delete()
        .eq('id', blockId);
        
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blocked-users', userId] });
      toast({
        title: "User unbanned",
        description: "The user can now interact with your content again.",
      });
      setUnbanDialogOpen(false);
      setSelectedUser(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleUnbanUser = (user: BlockedUser) => {
    setSelectedUser(user);
    setUnbanDialogOpen(true);
  };

  const confirmUnban = () => {
    if (selectedUser) {
      unbanUser.mutate(selectedUser.id);
    }
  };

  if (isLoading) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-16">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
            <span className="ml-3 font-mono text-sm text-slate-400">Loading blocked users...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!blockedUsers || blockedUsers.length === 0) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-12 text-center">
          <Shield className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2 font-mono">
            No blocked users
          </h3>
          <p className="text-slate-400 font-mono text-sm">
            You haven't blocked any users from commenting on your content.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <UserX className="h-5 w-5 text-red-400" />
          <h2 className="font-mono text-sm text-slate-300 uppercase tracking-wide">
            Blocked Users ({blockedUsers.length})
          </h2>
        </div>
      </div>

      {blockedUsers.map((blockedUser) => (
        <motion.div
          key={blockedUser.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="group"
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur hover:border-gray-700/70 transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3 flex-1">
                  <div className="p-2 bg-red-600/20 rounded border border-red-600/30">
                    <UserX className="h-4 w-4 text-red-400" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-mono text-sm text-slate-200 font-semibold">
                        {blockedUser.blocked_user.display_name || blockedUser.blocked_user.username}
                      </h3>
                      {blockedUser.blocked_user.display_name && (
                        <span className="font-mono text-xs text-slate-400">
                          @{blockedUser.blocked_user.username}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-slate-400">
                      <Calendar className="h-3 w-3" />
                      <span className="font-mono">
                        Blocked {formatDistanceToNow(new Date(blockedUser.created_at))} ago
                      </span>
                    </div>
                    
                    {blockedUser.reason && (
                      <div className="mt-2">
                        <Badge variant="outline" className="font-mono text-xs border-slate-600/50 text-slate-400">
                          {blockedUser.reason}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
                
                <Button
                  onClick={() => handleUnbanUser(blockedUser)}
                  size="sm"
                  variant="outline"
                  className="border-green-600/30 text-green-400 hover:bg-green-600/20 font-mono text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Unlock className="h-3 w-3 mr-1" />
                  Unblock
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* Unban Confirmation Dialog */}
      <AlertDialog open={unbanDialogOpen} onOpenChange={setUnbanDialogOpen}>
        <AlertDialogContent className="bg-gray-900 border-gray-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="font-mono text-slate-200">
              Unblock User
            </AlertDialogTitle>
            <AlertDialogDescription className="font-mono text-sm text-slate-400">
              Are you sure you want to unblock{' '}
              <span className="text-slate-300 font-semibold">
                {selectedUser?.blocked_user.display_name || selectedUser?.blocked_user.username}
              </span>
              ? They will be able to comment on your content again.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <AlertDialogFooter>
            <AlertDialogCancel 
              className="font-mono text-xs"
              onClick={() => setSelectedUser(null)}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmUnban}
              disabled={unbanUser.isPending}
              className="bg-green-600 hover:bg-green-700 font-mono text-xs"
            >
              {unbanUser.isPending ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Unblocking...
                </>
              ) : (
                <>
                  <Unlock className="h-3 w-3 mr-1" />
                  Unblock User
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
