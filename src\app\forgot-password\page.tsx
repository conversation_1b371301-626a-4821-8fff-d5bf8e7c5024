'use client';

import { useState } from 'react';
// FIREBASE IMPORTS REMOVED - sendPasswordResetEmail and auth imports removed
// TODO: Replace with Supabase password reset when ready
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { Mail, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // PLACEHOLDER: Firebase auth check removed - always show disabled service message
  // TODO: Replace with <PERSON>pa<PERSON> authentication when ready
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-10rem)] py-12">
      <Card className="w-full max-w-md shadow-2xl glow-purple">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-primary">Service Temporarily Disabled</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-destructive/10 border border-destructive text-destructive p-4 rounded-md flex flex-col items-center text-center space-y-2">
            <AlertTriangle className="h-8 w-8 mb-2" />
            <p className="font-semibold">Password Reset Disabled</p>
            <p className="text-sm">
              Password reset functionality is temporarily disabled due to Firebase removal. Please contact support for assistance.
            </p>
          </div>
        </CardContent>
         <CardFooter className="flex flex-col items-center space-y-2">
          <Link href="/login" passHref>
              <Button variant="outline">Back to Login</Button>
          </Link>
          <Link href="/" passHref>
              <Button variant="link" className="text-sm text-muted-foreground hover:text-accent">Go to Homepage</Button>
          </Link>
         </CardFooter>
      </Card>
    </div>
  );

  // PLACEHOLDER: Firebase password reset functionality removed
  // TODO: Replace with Supabase password reset when ready
  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    console.warn('handlePasswordReset: Firebase functionality removed. Implement Supabase equivalent.');
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Simulate loading delay then show disabled message
    setTimeout(() => {
      setError('Password reset functionality disabled: Firebase removed. Please contact support for assistance.');
      toast({
        title: "Service Disabled",
        description: "Password reset is temporarily disabled. Please contact support.",
        variant: "destructive",
      });
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-10rem)] py-12">
      <Card className="w-full max-w-md shadow-2xl glow-purple">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-primary">Forgot Password?</CardTitle>
          <CardDescription>Enter your email to receive password reset instructions.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <div className="bg-destructive/10 border border-destructive text-destructive p-3 rounded-md flex items-center text-sm">
              <AlertTriangle className="h-5 w-5 mr-2" />
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-500/10 border border-green-500 text-green-700 dark:text-green-400 p-3 rounded-md flex items-center text-sm">
              <CheckCircle className="h-5 w-5 mr-2" />
              {success}
            </div>
          )}
          {!success && (
            <form onSubmit={handlePasswordReset} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="pl-10"
                  />
                </div>
              </div>
              <Button type="submit" className="w-full bg-primary text-primary-foreground hover:bg-primary/90 glow-purple" disabled={loading}>
                {loading ? 'Sending...' : 'Send Reset Email'}
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/login" passHref>
            <Button variant="link" className="text-sm text-muted-foreground hover:text-accent">Back to Login</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ForgotPasswordPage;
