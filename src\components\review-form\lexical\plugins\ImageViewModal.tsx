'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { X, Download, ExternalLink, Minimize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ImageViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  src: string;
  altText: string;
  caption?: string;
}

export default function ImageViewModal({
  isOpen,
  onClose,
  src,
  altText,
  caption,
}: ImageViewModalProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Reset states when modal opens
  useEffect(() => {
    if (isOpen) {
      setImageLoaded(false);
      setImageError(false);
    }
  }, [isOpen, src]);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoaded(false);
    setImageError(true);
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = src;
    link.download = altText || 'image';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenInNewTab = () => {
    window.open(src, '_blank');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <>
      {/* Override light mode styles for image modal */}
      <style>{`
        .image-modal-content,
        .image-modal-content *,
        .image-modal-content button,
        .image-modal-content p,
        .image-modal-content div {
          color: inherit !important;
        }
        
        .image-modal-content {
          background: rgba(0, 0, 0, 0.95) !important;
          border-color: rgba(71, 85, 105, 0.5) !important;
        }
        
        .image-modal-content .text-slate-300 {
          color: rgb(203, 213, 225) !important;
        }
        
        .image-modal-content .text-slate-400 {
          color: rgb(148, 163, 184) !important;
        }
        
        .image-modal-content .text-slate-500 {
          color: rgb(100, 116, 139) !important;
        }
        
        .image-modal-content .text-red-400 {
          color: rgb(248, 113, 113) !important;
        }
      `}</style>
      
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogPortal>
          <DialogOverlay className="bg-black/70 backdrop-blur-xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[100]" />
          <DialogPrimitive.Content
            className="image-modal-content fixed left-[50%] top-[50%] z-[101] translate-x-[-50%] translate-y-[-50%] w-auto max-w-[95vw] max-h-[95vh] bg-black/95 border border-slate-700/50 backdrop-blur-sm overflow-hidden rounded-lg shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] duration-300"
            onKeyDown={handleKeyDown}
          >
          {/* Accessible title for screen readers */}
          <VisuallyHidden>
            <DialogTitle>{altText || 'Image Viewer'}</DialogTitle>
          </VisuallyHidden>
          
          {/* Window Title Bar */}
          <div className="flex items-center justify-end border-b border-slate-700/30 px-3 py-1.5 select-none">
            {/* Window Controls */}
            <div className="flex items-center gap-0.5">
              <button
                onClick={handleDownload}
                className="w-6 h-6 rounded-sm flex items-center justify-center transition-all text-slate-500 hover:text-slate-300 hover:bg-slate-800/50"
                title="Download image"
              >
                <Download className="h-3 w-3" />
              </button>
              
              <button
                onClick={handleOpenInNewTab}
                className="w-6 h-6 rounded-sm flex items-center justify-center transition-all text-slate-500 hover:text-slate-300 hover:bg-slate-800/50"
                title="Open in new tab"
              >
                <ExternalLink className="h-3 w-3" />
              </button>
              
              <button
                onClick={onClose}
                className="w-6 h-6 rounded-sm flex items-center justify-center transition-all text-slate-500 hover:text-slate-300 hover:bg-slate-800/50"
                title="Close (Esc)"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>

          {/* Image Container */}
          <div 
            className="flex items-center justify-center p-4 cursor-pointer min-h-[200px]"
            onClick={onClose}
          >
            {!imageLoaded && !imageError && (
              <div className="flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-slate-400 border-t-transparent rounded-full animate-spin" />
              </div>
            )}
            
            {imageError && (
              <div className="text-center">
                <div className="text-red-400 text-lg mb-2">⚠️</div>
                <p className="text-slate-400 font-mono">Failed to load image</p>
                <p className="text-slate-500 font-mono text-sm mt-1">
                  Click to close
                </p>
              </div>
            )}
            
            <img
              src={src}
              alt={altText}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onClick={(e) => e.stopPropagation()}
              className={`object-contain shadow-2xl cursor-default transition-all duration-300 ${
                imageLoaded ? 'block animate-in fade-in-0 zoom-in-95' : 'hidden'
              }`}
              style={{
                maxWidth: 'calc(95vw - 2rem)',
                maxHeight: 'calc(95vh - 6rem)', // Account for title bar and padding
                width: 'auto',
                height: 'auto',
              }}
            />
          </div>
          
          {/* Caption */}
          {caption && imageLoaded && (
            <div className="border-t border-slate-700/50 px-4 py-2">
              <p className="text-slate-400 font-mono text-xs text-center">
                {caption}
              </p>
            </div>
          )}
        </DialogPrimitive.Content>
      </DialogPortal>
    </Dialog>
    </>
  );
}
