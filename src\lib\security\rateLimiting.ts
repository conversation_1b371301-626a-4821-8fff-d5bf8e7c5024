// Rate Limiting System
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import { createClient } from '@/lib/supabase/client';

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  identifier: string; // user_id or ip_address
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export class CommentRateLimiter {
  private supabase = createClient();

  async checkRateLimit(config: RateLimitConfig): Promise<RateLimitResult> {
    const windowStart = new Date(Date.now() - config.windowMs);
    
    try {
      // Count recent requests
      const { data: recentRequests, error } = await this.supabase
        .from('comment_rate_limits')
        .select('id')
        .eq('identifier', config.identifier)
        .gte('created_at', windowStart.toISOString());

      if (error) throw error;

      const requestCount = recentRequests?.length || 0;
      const remaining = Math.max(0, config.maxRequests - requestCount);
      const allowed = requestCount < config.maxRequests;
      const resetTime = new Date(Date.now() + config.windowMs);

      if (allowed) {
        // Record this request
        await this.supabase
          .from('comment_rate_limits')
          .insert({
            identifier: config.identifier,
            created_at: new Date().toISOString(),
          });
      }

      return {
        allowed,
        remaining,
        resetTime,
        retryAfter: allowed ? undefined : Math.ceil(config.windowMs / 1000),
      };
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Fail open - allow request if rate limiting fails
      return {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: new Date(Date.now() + config.windowMs),
      };
    }
  }

  async cleanupOldRecords(): Promise<void> {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    try {
      await this.supabase
        .from('comment_rate_limits')
        .delete()
        .lt('created_at', cutoff.toISOString());
    } catch (error) {
      console.error('Rate limit cleanup error:', error);
    }
  }

  // Predefined rate limit configurations
  static readonly CONFIGS = {
    COMMENT_CREATE: {
      maxRequests: 10,
      windowMs: 15 * 60 * 1000, // 15 minutes
    },
    COMMENT_VOTE: {
      maxRequests: 50,
      windowMs: 15 * 60 * 1000, // 15 minutes
    },
    COMMENT_REPORT: {
      maxRequests: 5,
      windowMs: 60 * 60 * 1000, // 1 hour
    },
  };
}

// Utility function for easy rate limiting
export async function checkCommentRateLimit(
  identifier: string,
  action: keyof typeof CommentRateLimiter.CONFIGS
): Promise<RateLimitResult> {
  const rateLimiter = new CommentRateLimiter();
  const config = CommentRateLimiter.CONFIGS[action];
  
  return rateLimiter.checkRateLimit({
    ...config,
    identifier,
  });
}

// Rate limiting middleware for API routes
export function createRateLimitMiddleware(action: keyof typeof CommentRateLimiter.CONFIGS) {
  return async (identifier: string): Promise<{ success: boolean; error?: string; retryAfter?: number }> => {
    const result = await checkCommentRateLimit(identifier, action);
    
    if (!result.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${result.retryAfter} seconds.`,
        retryAfter: result.retryAfter,
      };
    }
    
    return { success: true };
  };
}
