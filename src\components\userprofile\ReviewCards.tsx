'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gamepad2, Star, Search, ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import { FastAverageColor } from 'fast-average-color';
import { useUserContent } from '@/hooks/useUserContent';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from '@/lib/utils';

interface ReviewCardsProps {
  profileData: any;
  currentUserId: string | null;
  isOwnProfile: boolean;
  theme: any;
  searchTerm: string;
  filterPlatform: string;
  itemsPerPage?: number;
  defaultSort?: 'date' | 'rating' | 'title';
}

// Reviews Grid Component - Enhanced Grid with Dynamic Text Color and Flexible Layout
const ReviewsGrid = React.memo(({ reviews, theme }: { reviews: any[]; theme: any }) => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [imageColors, setImageColors] = useState<Record<string, { isDark: boolean; rgb: string }>>({});
  const fac = useMemo(() => new FastAverageColor(), []);

  // Handle image load and color analysis with robust CORS handling
  const handleImageLoad = useCallback((reviewId: string, imgElement: HTMLImageElement) => {
    // Skip color analysis for external images that likely won't work with CORS
    const imageUrl = imgElement.src;
    const isExternalImage = imageUrl && !imageUrl.startsWith(window.location.origin) && !imageUrl.startsWith('/');
    
    if (isExternalImage) {
      // Use default dark theme for external images to avoid CORS issues
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: true,
          rgb: 'rgb(64, 64, 64)'
        }
      }));
      return;
    }

    try {
      // Only analyze local/same-origin images
      const result = fac.getColor(imgElement, { 
        algorithm: 'dominant',
        defaultColor: [128, 128, 128, 255]
      });
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: result.isDark,
          rgb: result.rgb
        }
      }));
    } catch (error) {
      // Fallback for any remaining errors
      console.warn(`Color analysis failed for image ${reviewId}:`, error instanceof Error ? error.message : String(error));
      setImageColors(prev => ({
        ...prev,
        [reviewId]: {
          isDark: true,
          rgb: 'rgb(64, 64, 64)'
        }
      }));
    }
  }, [fac]);

  // Calculate grid layout - implementing 2x3 system for odd numbers
  const getGridLayout = (totalReviews: number) => {
    if (totalReviews <= 2) return 'grid-cols-1 md:grid-cols-2';
    if (totalReviews <= 4) return 'grid-cols-1 md:grid-cols-2';
    if (totalReviews <= 6) return 'grid-cols-1 md:grid-cols-2';
    return 'grid-cols-1 md:grid-cols-2';
  };

  // Check if review should span full width (2x3 grid for odd numbers)
  const shouldSpanFullWidth = (index: number, totalReviews: number) => {
    // For 3 reviews: show as 2 top + 1 full width bottom (2x3 layout)
    if (totalReviews === 3) return index === 2;
    // For 5 reviews: show as 2x2 + 1 full width bottom (2x3 layout)
    if (totalReviews === 5) return index === 4;
    return false;
  };

  return (
    <>

      <div className={`grid ${getGridLayout(reviews.length)} gap-6 w-full`}>
        {reviews.map((review, index) => {
          const reviewUrl = `/reviews/view/${review.slug}`;
          // Convert score to 0-100 range and no decimals
          // Handle different possible score formats from database
          const rawScore = review.rating || 0;
          let displayScore = 0;
          
          if (rawScore <= 1) {
            // If score is 0-1 (decimal percentage), convert to 0-100
            displayScore = Math.round(rawScore * 100);
          } else if (rawScore <= 10) {
            // If score is 0-10, convert to 0-100
            displayScore = Math.round(rawScore * 10);
          } else {
            // Assume it's already 0-100, just clamp and round
            displayScore = Math.round(Math.max(0, Math.min(100, rawScore)));
          }
          
          // Debug logging for review cards
          if (process.env.NODE_ENV === 'development') {
            console.log(`🎯 ReviewCard ${review.game_name} score calculation:`, {
              originalScore: review.rating,
              displayScore,
              type: typeof review.rating,
              conversionUsed: rawScore <= 1 ? 'decimal-to-percent' : rawScore <= 10 ? '10-scale-to-100' : 'clamped-100'
            });
          }
          const scorePercentage = displayScore;
          const isFullWidth = shouldSpanFullWidth(index, reviews.length);
          const imageColor = imageColors[review.id];
          
          // Debug logging
          if (process.env.NODE_ENV === 'development') {
            console.log(`Review ${index + 1}: ${review.game_name}`, {
              isFullWidth,
              totalReviews: reviews.length,
              image: review.game_image || review.main_image_url,
              hasImage: !!(review.game_image || review.main_image_url)
            });
          }

          // Dynamic text color based on image brightness
          const textColor = imageColor?.isDark === false ? 'text-gray-900' : 'text-white';
          const scoreColor = imageColor?.isDark === false ? 'text-yellow-600' : 'text-yellow-400';

          return (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ y: -5 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className={`group ${isFullWidth ? 'full-width-card' : ''}`}
              style={isFullWidth ? { 
                gridColumn: '1 / -1', 
                width: '100%',
                backgroundColor: process.env.NODE_ENV === 'development' ? 'rgba(255, 0, 0, 0.1)' : undefined 
              } : undefined}
              onMouseEnter={() => setHoveredCard(review.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <Link href={reviewUrl} className="block h-full">
                <Card 
                  className="review-card-container bg-gray-900/40 backdrop-blur-sm overflow-hidden h-full transition-all duration-500 ease-out rounded-xl border-0"
                  style={{
                    border: 'none',
                    boxShadow: hoveredCard === review.id 
                      ? '0 25px 50px -12px rgba(0, 0, 0, 0.8)'
                      : '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3)'
                  }}
                >
                  <CardContent className="p-0 h-full">
                    <div className={`relative h-full ${isFullWidth ? 'review-card-content' : ''}`}>
                      {/* Banner Image with proper aspect ratio and positioning */}
                      {review.game_image || review.main_image_url ? (
                        <div className={`relative w-full overflow-hidden rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}>
                          <img
                            src={review.game_image || review.main_image_url}
                            alt={review.game_name}
                            className="w-full h-full object-cover transition-all duration-500 ease-out rounded-xl"
                            style={{
                              objectPosition: review.main_image_position || 'center 25%',
                              filter: 'brightness(1.1) contrast(1.05) saturate(1.1)',
                              imageRendering: 'auto',
                              backfaceVisibility: 'hidden',
                              transform: 'translateZ(0)',
                              willChange: 'transform'
                            }}
                            onLoad={(e) => handleImageLoad(review.id, e.target as HTMLImageElement)}
                            onError={(e) => {
                              // Hide broken image and show fallback
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                              if (fallback) fallback.style.display = 'flex';
                            }}
                            loading="lazy"
                          />
                          {/* Gradient overlay for text readability */}
                          <div 
                            className="absolute inset-0 rounded-xl"
                            style={{
                              background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 40%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0.8) 100%)',
                              pointerEvents: 'none'
                            }}
                          />
                          {/* Fallback element - hidden by default */}
                          <div
                            className={`image-fallback absolute inset-0 w-full flex items-center justify-center rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}
                            style={{ 
                              backgroundColor: `${theme?.colors?.primary}30`,
                              display: 'none'
                            }}
                          >
                            <div className="text-center">
                              <Gamepad2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                              <p className="text-xs text-gray-500">Image not available</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div
                          className={`relative w-full flex items-center justify-center rounded-xl ${isFullWidth ? 'h-96' : 'h-80'}`}
                          style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                        >
                          <div className="text-center">
                            <Gamepad2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p className="text-xs text-gray-500">No image</p>
                          </div>
                          {/* Gradient overlay for text readability consistency */}
                          <div 
                            className="absolute inset-0 rounded-xl"
                            style={{
                              background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0) 40%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0.8) 100%)',
                              pointerEvents: 'none'
                            }}
                          />
                        </div>
                      )}

                      {/* Neural Code Overlay */}
                      <div className="absolute inset-0 review-card-overlay p-4">
                        {/* Score - Top Left */}
                        <div className="absolute top-4 left-4">
                          <div className="flex items-center gap-2 px-3 py-2 bg-gray-800/30 border border-gray-700/30 rounded backdrop-blur-sm h-8">
                            <span className="text-white/60 font-mono text-xs">score:</span>
                            <span className="font-mono font-bold text-sm text-white tabular-nums">
                              {displayScore}
                            </span>
                          </div>
                        </div>



                        {/* Title and Stats - Bottom */}
                        <div className="absolute bottom-4 left-4 right-4 pr-2">
                          <h3 className="font-mono font-bold text-lg truncate transition-colors duration-300 text-white drop-shadow-lg max-w-full mb-2">
                            <span className="text-white/50 text-sm mr-2">//</span>
                            {review.game_name}
                          </h3>
                          <div className="flex items-center justify-between text-xs text-white/90 drop-shadow-md">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-1">
                                <span>👁</span>
                                <span>{review.view_count || 0}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <span>♥</span>
                                <span>{review.like_count || 0}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <span>💬</span>
                                <span>{review.comment_count || 0}</span>
                              </div>
                              {review.played_on && (
                                <div className="flex items-center gap-1">
                                  <span>•</span>
                                  <span>Played on {review.played_on}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </motion.div>
          );
      })}
      </div>
    </>
  );
});

ReviewsGrid.displayName = 'ReviewsGrid';

// Filter Controls Component
const FilterControls = React.memo(({
  searchTerm,
  setSearchTerm,
  filterPlatform,
  setFilterPlatform,
  availablePlatforms,
  isVisible
}: {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filterPlatform: string;
  setFilterPlatform: (value: string) => void;
  availablePlatforms: string[];
  isVisible: boolean;
}) => (
  <AnimatePresence>
    {isVisible && (
      <motion.div
        initial={{ opacity: 0, height: 0, y: -20 }}
        animate={{ opacity: 1, height: "auto", y: 0 }}
        exit={{ opacity: 0, height: 0, y: -20 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        className="overflow-hidden"
      >
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4">
          {/* Neural Search and Filters Header */}
          <div className="flex items-center mb-4">
            <div className="h-px bg-gray-600 flex-1" />
            <span className="mx-3 text-xs font-mono uppercase tracking-widest text-gray-400">
              <span className="text-gray-400">//</span> Search & Filter
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            {/* Neural Search Input */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3" />
              <Input
                placeholder="search reviews..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 bg-gray-800 border border-gray-700 text-white placeholder-gray-400 w-full text-xs h-8 font-mono hover:bg-gray-700 focus:bg-gray-700 transition-all duration-300 rounded"
              />
            </div>

            {/* Platform Filter */}
            <div className="flex items-center gap-2">
              <span className="text-xs font-mono text-gray-400">platform:</span>
              <Select value={filterPlatform} onValueChange={setFilterPlatform}>
                <SelectTrigger className="w-28 bg-gray-800 border border-gray-700 text-white text-xs h-8 font-mono rounded">
                  <SelectValue placeholder="all" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border border-gray-700">
                  <SelectItem value="all" className="text-xs font-mono">all</SelectItem>
                  {availablePlatforms.map((platform) => (
                    <SelectItem key={platform} value={platform} className="text-xs font-mono">
                      {platform.toLowerCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </motion.div>
    )}
  </AnimatePresence>
));

FilterControls.displayName = 'FilterControls';

// Reviews Section Component
const ReviewsSection = React.memo(({
  profileData,
  currentUserId,
  isOwnProfile,
  theme,
  searchTerm,
  filterPlatform,
  itemsPerPage = 6,
  defaultSort = 'date'
}: ReviewCardsProps) => {
  const [displayedCount, setDisplayedCount] = useState(itemsPerPage);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Background brightness detection for text adaptation
  const isDarkBackground = useBackgroundBrightness();

  const {
    data,
    isLoading,
    error
  } = useUserContent(profileData.id, currentUserId || undefined);

  const filteredReviews = useMemo(() => {
    if (!data?.reviews) return [];

    let filtered = data.reviews.filter(review => {
      // Search filter
      const matchesSearch = !searchTerm ||
        review.game_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.review_text.toLowerCase().includes(searchTerm.toLowerCase());

      // Platform filter - using played_on field (where user actually played)
      const matchesPlatform = filterPlatform === 'all' ||
        review.played_on?.toLowerCase() === filterPlatform.toLowerCase();

      return matchesSearch && matchesPlatform;
    });

    // Sort based on defaultSort setting
    return filtered.sort((a, b) => {
      switch (defaultSort) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'title':
          return a.game_name.localeCompare(b.game_name);
        case 'date':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [data?.reviews, searchTerm, filterPlatform, defaultSort]);

  // Get unique platforms for filter options
  const availablePlatforms = useMemo(() => {
    if (!data?.reviews) return [];
    const platforms = new Set<string>();
    data.reviews.forEach(review => {
      if (review.platform) platforms.add(review.platform);
    });
    return Array.from(platforms).sort();
  }, [data?.reviews]);

  // Infinite scroll logic
  const currentReviews = filteredReviews.slice(0, displayedCount);
  const hasMoreReviews = displayedCount < filteredReviews.length;

  // Reset displayed count when search or filters change
  useEffect(() => {
    setDisplayedCount(itemsPerPage);
  }, [searchTerm, filterPlatform, itemsPerPage]);

  // Load more reviews function
  const loadMoreReviews = useCallback(async () => {
    if (isLoadingMore || !hasMoreReviews) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    setDisplayedCount(prev => prev + itemsPerPage);
    setIsLoadingMore(false);
  }, [isLoadingMore, hasMoreReviews, itemsPerPage]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return null; // Silently fail for reviews
  }

  if (!filteredReviews.length && !searchTerm) {
    return null; // Don't show empty module if no reviews exist
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >

      <AnimatePresence mode="wait">
        <motion.div
          key={`reviews-${searchTerm}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentReviews.length > 0 ? (
            <>
              <ReviewsGrid reviews={currentReviews} theme={theme} />
              {hasMoreReviews && (
                <div className="flex justify-center mt-8">
                  <Button
                    onClick={loadMoreReviews}
                    disabled={isLoadingMore}
                    className={cn(
                      "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
                      isDarkBackground
                        ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70'
                        : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
                    )}
                    style={{
                      backdropFilter: 'blur(8px)',
                      boxShadow: isDarkBackground
                        ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)'
                        : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
                    }}
                  >
                    {isLoadingMore ? (
                      <motion.div
                        className="flex items-center gap-2"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Clock className="w-4 h-4" />
                        </motion.div>
                        Loading more reviews...
                      </motion.div>
                    ) : (
                      <span className="flex items-center gap-2">
                        Load More Reviews
                        <Badge
                          variant="secondary"
                          className="bg-slate-700 text-slate-300 text-xs"
                        >
                          {filteredReviews.length - displayedCount} remaining
                        </Badge>
                      </span>
                    )}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12 text-gray-400">
              <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No reviews found</p>
              <p className="text-sm">Try adjusting your search filters</p>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
});

ReviewsSection.displayName = 'ReviewsSection';

// Main export - combine all components
export { ReviewsGrid, FilterControls, ReviewsSection };
export default ReviewsSection;