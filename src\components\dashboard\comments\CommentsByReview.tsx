'use client';

// Comments by Review Component - Redesigned to match FlaggedContentManager
// Date: 22/06/2025
// Task: Gateway component that redirects to review pages with comment sections

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { CommentModerationData } from '@/types/commentModeration';
import { CommentModerationModal } from './CommentModerationModal';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  MessageSquare,
  Search,
  Calendar,
  Loader2,
  Flag,
  Clock,
  ArrowUpRight,
  ChevronDown
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from '@/lib/utils';

interface CommentsByReviewProps {
  comments: CommentModerationData[];
  isLoading: boolean;
}

interface ReviewSummary {
  id: string;
  title: string;
  slug: string;
  game_name: string;
  created_at: string;
  total_comments: number;
  flagged_comments: number;
  pending_comments: number;
  recent_activity: string | null;
  comments: CommentModerationData[];
}

export function CommentsByReview({ comments, isLoading }: CommentsByReviewProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<string>('recent_activity');
  const [displayCount, setDisplayCount] = useState(5);
  const [selectedReview, setSelectedReview] = useState<ReviewSummary | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Background brightness detection for text adaptation
  const isDarkBackground = useBackgroundBrightness();

  // Group comments by review and create review summaries
  const reviewSummaries = useMemo(() => {
    const grouped = comments.reduce((acc, comment) => {
      const reviewId = comment.review_id;
      if (!acc[reviewId]) {
        acc[reviewId] = {
          id: reviewId,
          title: comment.review_title,
          slug: comment.review_slug,
          game_name: comment.game_name,
          created_at: comment.review_created_at,
          total_comments: 0,
          flagged_comments: 0,
          pending_comments: 0,
          recent_activity: null,
          comments: []
        };
      }

      acc[reviewId].comments.push(comment);
      acc[reviewId].total_comments++;

      if (comment.flag_count > 0) {
        acc[reviewId].flagged_comments++;
      }

      if (!comment.is_approved) {
        acc[reviewId].pending_comments++;
      }

      // Track most recent activity
      if (!acc[reviewId].recent_activity ||
          new Date(comment.created_at) > new Date(acc[reviewId].recent_activity)) {
        acc[reviewId].recent_activity = comment.created_at;
      }

      return acc;
    }, {} as Record<string, ReviewSummary>);

    return Object.values(grouped);
  }, [comments]);

  // Filter and sort reviews - only by game name
  const filteredReviews = useMemo(() => {
    let filtered = reviewSummaries.filter(review => {
      if (!searchQuery) return true;
      return review.game_name.toLowerCase().includes(searchQuery.toLowerCase());
    });

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent_activity':
          if (!a.recent_activity && !b.recent_activity) return 0;
          if (!a.recent_activity) return 1;
          if (!b.recent_activity) return -1;
          return new Date(b.recent_activity).getTime() - new Date(a.recent_activity).getTime();
        case 'most_comments':
          return b.total_comments - a.total_comments;
        case 'flagged_first':
          return b.flagged_comments - a.flagged_comments;
        case 'pending_first':
          return b.pending_comments - a.pending_comments;
        default:
          return 0;
      }
    });
  }, [reviewSummaries, searchQuery, sortBy]);

  // Reset display count when filters change
  React.useEffect(() => {
    setDisplayCount(5);
  }, [searchQuery, sortBy]);

  // Paginated reviews for display
  const displayedReviews = useMemo(() => {
    return filteredReviews.slice(0, displayCount);
  }, [filteredReviews, displayCount]);

  // Handle opening comment moderation modal
  const handleOpenModal = (review: ReviewSummary) => {
    setSelectedReview(review);
    setIsModalOpen(true);
  };

  // Handle closing modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedReview(null);
  };

  // Handle load more
  const handleLoadMore = () => {
    setDisplayCount(prev => prev + 5);
  };

  if (isLoading) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-16">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
            <span className="ml-3 font-mono text-sm text-slate-400">Loading comments...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (comments.length === 0) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2 font-mono">
            No comments yet
          </h3>
          <p className="text-slate-400 font-mono text-sm">
            Your reviews don't have any comments yet. Share them to get feedback!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      {reviewSummaries.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-3 p-4 bg-slate-800/30 border border-slate-700/40 rounded-lg">
          <div className="flex-1">
            <label className="text-xs font-mono text-slate-400 mb-1 block">
              <span className="text-slate-500">//</span> Game Name
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by game name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-slate-900/60 border-slate-600/50 text-slate-200 h-8 text-xs font-mono"
              />
            </div>
          </div>

          <div className="flex-1">
            <label className="text-xs font-mono text-slate-400 mb-1 block">
              <span className="text-slate-500">//</span> Sort by
            </label>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="bg-slate-900/60 border-slate-600/50 text-slate-200 h-8 text-xs">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900 border-slate-700">
                <SelectItem value="recent_activity">Recent Activity</SelectItem>
                <SelectItem value="most_comments">Most Comments</SelectItem>
                <SelectItem value="flagged_first">Flagged First</SelectItem>
                <SelectItem value="pending_first">Pending First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-end">
            <div className="px-3 py-1 bg-slate-700/50 border border-slate-600/50 rounded text-xs font-mono text-slate-300">
              {filteredReviews.length} reviews
            </div>
          </div>
        </div>
      )}

      {/* Show message if no items match filters */}
      {filteredReviews.length === 0 && reviewSummaries.length > 0 && (
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardContent className="p-8 text-center">
            <MessageSquare className="mx-auto h-8 w-8 text-slate-500 mb-3" />
            <p className="text-slate-400 font-mono text-sm">
              No reviews match the current search
            </p>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      {displayedReviews.map((review) => (
        <motion.div
          key={review.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="group"
        >
          <Card
            className="border-slate-700/50 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur hover:border-slate-600/70 transition-all duration-200 cursor-pointer"
            onClick={() => handleOpenModal(review)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 flex-1">
                  {/* Review Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-mono text-slate-200 text-sm font-semibold truncate">
                        {review.game_name}
                      </h3>
                    </div>

                    <div className="flex items-center gap-1 text-slate-400 text-xs font-mono mb-1">
                      <Calendar className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">
                        Created {formatDistanceToNow(new Date(review.created_at))} ago
                      </span>
                    </div>

                    <div className="text-slate-500 text-xs font-mono truncate">
                      {review.title.length > 30 ? review.title.substring(0, 30) + '...' : review.title}
                    </div>

                    {review.recent_activity && (
                      <div className="text-emerald-400 text-xs font-mono">
                        Last activity {formatDistanceToNow(new Date(review.recent_activity))} ago
                      </div>
                    )}
                  </div>
                </div>

                {/* Badges */}
                <div className="flex items-center gap-2 mr-3 flex-shrink-0">
                  <Badge variant="outline" className="text-xs font-mono bg-purple-500/10 text-purple-300 border-purple-500/30">
                    {review.total_comments} comments
                  </Badge>
                  {review.flagged_comments > 0 && (
                    <Badge variant="outline" className="text-xs font-mono bg-red-500/10 text-red-300 border-red-500/30">
                      <Flag className="h-3 w-3 mr-1" />
                      {review.flagged_comments}
                    </Badge>
                  )}
                  {review.pending_comments > 0 && (
                    <Badge variant="outline" className="text-xs font-mono bg-yellow-500/10 text-yellow-300 border-yellow-500/30">
                      <Clock className="h-3 w-3 mr-1" />
                      {review.pending_comments}
                    </Badge>
                  )}
                </div>

                {/* Go to Review Icon */}
                <ArrowUpRight className="h-4 w-4 text-slate-400 flex-shrink-0" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* Enhanced Load More Button with Theme Integration */}
      {displayedReviews.length < filteredReviews.length && (
        <div className="flex justify-center pt-4">
          <Button
            onClick={handleLoadMore}
            className={cn(
              "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
              isDarkBackground
                ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70'
                : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
            )}
            style={{
              backdropFilter: 'blur(8px)',
              boxShadow: isDarkBackground
                ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)'
                : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
            }}
          >
            <span className="flex items-center gap-2">
              <ChevronDown className="h-4 w-4" />
              Load More ({filteredReviews.length - displayedReviews.length} remaining)
            </span>
          </Button>
        </div>
      )}

      {/* Comment Moderation Modal */}
      <CommentModerationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        review={selectedReview}
        comments={comments}
      />
    </div>
  );
}


