import React from 'react';

const WarningTriangleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M12 2L2 20h20L12 2z" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="11" y="8" width="2" height="6"/>
    <circle cx="12" cy="17" r="1"/>
    <rect x="8" y="18" width="1" height="1"/>
    <rect x="15" y="18" width="1" height="1"/>
    <rect x="5" y="16" width="1" height="1"/>
    <rect x="18" y="16" width="1" height="1"/>
  </svg>
);

export default WarningTriangleIcon;