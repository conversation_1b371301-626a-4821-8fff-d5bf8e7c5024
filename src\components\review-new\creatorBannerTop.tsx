import React, { useState } from 'react';
import type { Review, MonetizationBlock } from '@/lib/types';

interface CreatorBannerTopProps {
  review?: Review;
}

const CreatorBannerTop: React.FC<CreatorBannerTopProps> = ({ review }) => {
  const [isHovered, setIsHovered] = useState(false);

  // Find custom banner for top banner placement
  const customBanner = review?.monetizationBlocks?.find(
    block => 
      (block.placement === 'after-banner' || block.placement === 'after-banner-image' || block.placement === 'After Banner Image') && 
      (block.isActive !== false) && 
      (block.type === 'banner' || block.type === 'imageLink')
  );

  // Code title component with glitch effect
  const CodeTitle = ({ children, className = "" }: { 
    children: React.ReactNode; 
    className?: string;
  }) => (
    <span className={`font-mono relative inline-block ${className}`}>
      <span className="text-emerald-400/60">&lt;</span>
      <span className="mx-1 relative hover:animate-pulse">
        {children}
        <span className="absolute inset-0 bg-gradient-to-r from-emerald-400/0 via-emerald-400/30 to-emerald-400/0 opacity-0 hover:opacity-100 transition-opacity duration-200"/>
      </span>
      <span className="text-emerald-400/60">/&gt;</span>
    </span>
  );

  // CUSTOM BANNER - EXACT SAME STRUCTURE AS BOTTOM BANNER
  if (customBanner) {
    const content = customBanner.content || {};
    const data = customBanner.data || {};
    
    const imageUrl = content.imageUrl || data.imageUrl;
    const linkUrl = content.linkUrl || data.linkUrl;
    
    return (
      <div className="my-12 w-full max-w-full sm:max-w-full md:max-w-full lg:max-w-full 2xl:max-w-[75%] mx-auto">
        <div className="border border-white/5 bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl overflow-hidden">
          <div className="p-3 sm:p-4">
            <a 
              href={linkUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="block relative overflow-hidden rounded-lg border border-white/5 bg-slate-800 hover:bg-slate-700 hover:border-white/10 transition-all duration-500 group"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <div className="relative aspect-[8.09/1] overflow-hidden">
                <img 
                  src={imageUrl} 
                  alt="Sponsored Content"
                  className="w-full h-full object-cover object-center transition-all duration-300 group-hover:scale-105"
                  style={{
                    imageRendering: 'crisp-edges'
                  }}
                  loading="lazy"
                  decoding="async"
                />
                
                {/* Overlay with sponsored label */}
                <div className="absolute top-2 right-2 px-2 py-1 bg-black/60 backdrop-blur-sm rounded text-xs font-mono text-slate-300 border border-white/10">
                  <span className="text-emerald-400">&lt;</span>
                  Sponsored
                  <span className="text-emerald-400">/&gt;</span>
                </div>
                
                {/* Hover effect overlay */}
                <div className={`absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5 opacity-0 transition-opacity duration-500 ${
                  isHovered ? 'opacity-100' : ''
                }`} />
              </div>
              
              {/* Subtle Corner Elements */}
              <div className="absolute top-1.5 left-1.5 w-3 h-3 border-l-2 border-t-2 border-emerald-500/20 rounded-tl" />
              <div className="absolute top-1.5 right-1.5 w-3 h-3 border-r-2 border-t-2 border-blue-500/20 rounded-tr" />
              <div className="absolute bottom-1.5 left-1.5 w-3 h-3 border-l-2 border-b-2 border-emerald-500/20 rounded-bl" />
              <div className="absolute bottom-1.5 right-1.5 w-3 h-3 border-r-2 border-b-2 border-blue-500/20 rounded-br" />
            </a>
          </div>
        </div>
      </div>
    );
  }
  
  // Default Creator Spotlight Banner - EXACT SAME STRUCTURE AS BOTTOM BANNER  
  return (
    <div className="my-12 w-full max-w-full sm:max-w-full md:max-w-full lg:max-w-full 2xl:max-w-[75%] mx-auto">
      <style jsx>{`
        .glitch-top:hover {
          animation: glitchTop 0.3s ease-in-out;
        }
        @keyframes glitchTop {
          0%, 100% { transform: translate(0); }
          10% { transform: translate(-1px, -1px); }
          20% { transform: translate(1px, 1px); }
          30% { transform: translate(-1px, 1px); }
          40% { transform: translate(1px, -1px); }
          50% { transform: translate(-1px, -1px); }
          60% { transform: translate(1px, 1px); }
          70% { transform: translate(-1px, 1px); }
          80% { transform: translate(1px, -1px); }
          90% { transform: translate(-1px, -1px); }
        }
        .ad-glow-top {
          background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
          background-size: 400% 400%;
          animation: gradientShiftTop 6s ease infinite;
        }
        @keyframes gradientShiftTop {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
      `}</style>

      <div className="border border-white/5 bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl overflow-hidden">
        {/* Creator Banner Top - Compact design like bottom placement */}
        <div className="p-3 sm:p-4">
          <div 
            className={`relative overflow-hidden rounded-lg border border-white/5 bg-slate-800 hover:bg-slate-700 hover:border-white/10 transition-all duration-500 ${
              isHovered ? 'ad-glow-top' : ''
            }`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Content Area - More compact for consistency */}
            <div className="min-h-[100px] sm:min-h-[140px] flex items-center justify-center p-3 sm:p-4">
              <div className="text-center space-y-3">
                {/* Visual Elements */}
                <div className="flex justify-center space-x-2 mb-3">
                  {[1, 2, 3, 4].map((i) => (
                    <div 
                      key={i}
                      className={`w-2.5 h-2.5 rounded-full bg-gradient-to-r from-emerald-500 to-blue-500 transition-all duration-300 ${
                        isHovered ? 'animate-bounce' : 'opacity-50'
                      }`}
                      style={{ animationDelay: `${i * 0.1}s` }}
                    />
                  ))}
                </div>
                
                {/* Ad Format Info */}
                <div className="space-y-2">
                  <p className="text-slate-300 font-mono text-sm">
                    <CodeTitle className="glitch-top">
                      Creator Spotlight
                    </CodeTitle>
                  </p>
                  <div className="flex flex-wrap justify-center gap-1.5 text-xs">
                    {['Leaderboard', 'Banner', 'Mobile'].map((format) => (
                      <span 
                        key={format}
                        className="px-2 py-1 bg-slate-700 rounded border border-white/10 text-slate-400 font-mono hover:text-white hover:border-emerald-400/30 transition-all duration-200"
                      >
                        <CodeTitle className="text-xs">
                          {format}
                        </CodeTitle>
                      </span>
                    ))}
                  </div>
                </div>
                
                {/* Call to Action */}
                <div className={`mt-3 transition-all duration-300 ${isHovered ? 'opacity-100' : 'opacity-70'}`}>
                  <button
                    className="px-3 py-1.5 bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-500 hover:to-blue-500 text-white rounded-md transition transform duration-200 hover:scale-105"
                  >
                    <div className="flex items-center justify-center space-x-1.5">
                      <span className="text-xs uppercase tracking-wide">Feature Your Content</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
            
            {/* Subtle Corner Elements */}
            <div className="absolute top-1.5 left-1.5 w-3 h-3 border-l-2 border-t-2 border-emerald-500/20 rounded-tl" />
            <div className="absolute top-1.5 right-1.5 w-3 h-3 border-r-2 border-t-2 border-blue-500/20 rounded-tr" />
            <div className="absolute bottom-1.5 left-1.5 w-3 h-3 border-l-2 border-b-2 border-emerald-500/20 rounded-bl" />
            <div className="absolute bottom-1.5 right-1.5 w-3 h-3 border-r-2 border-b-2 border-blue-500/20 rounded-br" />
            
            {/* Hover Glow Effect */}
            <div className={`absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5 opacity-0 transition-opacity duration-500 ${
              isHovered ? 'opacity-100' : ''
            }`} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatorBannerTop;