'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Tag, TrendingUp, Flame, Zap, Target, Loader2 } from 'lucide-react';
import '../style/trendingSidebar.css';

interface TrendingTag {
  id: string;
  name: string;
  slug: string;
  usage_count: number;
  trend_score: number;
  is_trending: boolean;
  is_featured: boolean;
  review_count: number;
}

interface LegacyTrendingTag {
  id: string;
  name: string;
  count: number;
  trend: 'hot' | 'rising' | 'stable' | 'explosive';
  percentage: number;
}

const TrendingTagsSidebar = () => {
  const [hoveredTag, setHoveredTag] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const [trendingTags, setTrendingTags] = useState<LegacyTrendingTag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback data for when API is not available
  const fallbackTags: LegacyTrendingTag[] = [
    { id: "1",  name: "Action RPG",   count: 2847, trend: "explosive", percentage: 95 },
    { id: "2",  name: "Open World",   count: 1923, trend: "hot",       percentage: 78 },
    { id: "3",  name: "Multiplayer",  count: 1654, trend: "rising",    percentage: 65 },
    { id: "4",  name: "Indie",        count: 1432, trend: "rising",    percentage: 58 },
    { id: "5",  name: "Survival",     count: 1289, trend: "hot",       percentage: 52 }
  ];

  useEffect(() => {
    setMounted(true);
    loadTrendingTags();
  }, []);

  const loadTrendingTags = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/tags?endpoint=trending&limit=10');
      
      if (!response.ok) {
        console.log('Trending API failed, using fallback data');
        setTrendingTags(fallbackTags);
        return;
      }

      const data = await response.json();

      if (data.success && data.tags && data.tags.length > 0) {
        // Convert API data to legacy format for compatibility with existing UI
        const convertedTags: LegacyTrendingTag[] = data.tags.map((tag: TrendingTag, index: number) => ({
          id: tag.id,
          name: tag.name,
          count: tag.usage_count,
          trend: determineTrend(tag, index),
          percentage: Math.min(Math.round((tag.trend_score / 100) * 100), 100)
        }));

        setTrendingTags(convertedTags.slice(0, 5)); // Show top 5
      } else {
        // Use fallback if no data
        setTrendingTags(fallbackTags);
      }
    } catch (err) {
      console.error('Error loading trending tags:', err);
      setError('Failed to load trending tags');
      setTrendingTags(fallbackTags); // Use fallback on error
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to determine trend based on API data
  const determineTrend = (tag: TrendingTag, index: number): 'explosive' | 'hot' | 'rising' | 'stable' => {
    if (tag.trend_score > 80) return 'explosive';
    if (tag.trend_score > 60) return 'hot';
    if (tag.trend_score > 40) return 'rising';
    return 'stable';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'explosive': return Zap;
      case 'hot':       return Flame;
      case 'rising':    return TrendingUp;
      case 'stable':    return Target;
      default:          return Tag;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'explosive': return 'text-cyan-400';
      case 'hot':       return 'text-red-400';
      case 'rising':    return 'text-yellow-400';
      case 'stable':    return 'text-slate-400';
      default:          return 'text-slate-400';
    }
  };

  return (
    <div className="trending-sidebar-container">
      <div className="trending-sidebar-header">
        <div className="trending-sidebar-header-flex">
          <div className="trending-sidebar-divider" />
          <span className="trending-sidebar-title trending-sidebar-title-cyan">
            <span className="trending-sidebar-title-accent">&gt;</span> Hot Tags
          </span>
          <div className="trending-sidebar-divider" />
        </div>
      </div>
      
      <div className="trending-sidebar-main-container">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 size={20} className="animate-spin text-slate-400" />
            <span className="ml-2 text-xs text-slate-500">Loading trending tags...</span>
          </div>
        ) : error ? (
          <div className="py-8 text-center">
            <div className="text-xs text-red-400 mb-2">Failed to load</div>
            <button 
              onClick={loadTrendingTags}
              className="text-xs text-slate-400 hover:text-slate-300 underline"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="trending-sidebar-list">
            {trendingTags.map((tag, idx) => {
              const TrendIcon = getTrendIcon(tag.trend);
              const trendColor = getTrendColor(tag.trend);
              const animDelay = idx * 75;
              
              return (
                <Link
                  key={tag.id}
                  href={`/tags/${tag.name.toLowerCase().replace(/\s+/g, '-')}`}
                  className={`trending-sidebar-item ${mounted ? 'trending-sidebar-animate-fade-slide' : ''}`}
                  onMouseEnter={() => setHoveredTag(tag.id)}
                  onMouseLeave={() => setHoveredTag(null)}
                  style={{
                    animationDelay: `${animDelay}ms`
                  }}
                >
                  <div className="trending-sidebar-item-content">
                    <div className="trending-sidebar-item-left">
                      <div className="trending-sidebar-icon-container">
                        <TrendIcon 
                          size={14} 
                          className={`trending-sidebar-icon ${tag.trend}`}
                        />
                      </div>
                      <span className="trending-sidebar-name">{tag.name}</span>
                    </div>
                  </div>
                  <div className="trending-sidebar-progress-container">
                    <div
                      className="trending-sidebar-progress-bar"
                      style={{ 
                        width: hoveredTag === tag.id ? `${tag.percentage}%` : '0%'
                      }}
                    />
                  </div>
                </Link>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default TrendingTagsSidebar;