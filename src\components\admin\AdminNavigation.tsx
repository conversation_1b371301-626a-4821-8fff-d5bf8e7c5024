'use client';

// Admin Navigation Component
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.2

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { 
  LayoutDashboard,
  Users,
  Newspaper,
  BarChart3,
  MessageSquareWarning,
  DollarSign,
  Settings,
  Shield,
  Database,
  Activity,
  ChevronRight
} from 'lucide-react';

interface NavItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  description?: string;
  badge?: string;
  disabled?: boolean;
}

const navigationItems: NavItem[] = [
  {
    label: 'Dashboard',
    href: '/admin',
    icon: <LayoutDashboard className="h-5 w-5" />,
    description: 'Overview and quick actions'
  },
  {
    label: 'User Management',
    href: '/admin/users',
    icon: <Users className="h-5 w-5" />,
    description: 'Manage user accounts and roles'
  },
  {
    label: 'Content Moderation',
    href: '/admin/reviews',
    icon: <Newspaper className="h-5 w-5" />,
    description: 'Review and moderate content'
  },
  {
    label: 'Multi-Factor Auth',
    href: '/admin/security/mfa',
    icon: <Shield className="h-5 w-5" />,
    description: 'MFA security configuration',
    badge: 'SECURITY'
  },
  {
    label: 'Analytics',
    href: '/admin/analytics',
    icon: <BarChart3 className="h-5 w-5" />,
    description: 'Site statistics and performance'
  },
  {
    label: 'Ad Management',
    href: '/admin/ads',
    icon: <DollarSign className="h-5 w-5" />,
    description: 'Manage advertisements'
  }
];

export function AdminNavigation() {
  const pathname = usePathname();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Animation for initial mount
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Card className={`w-full border border-violet-900/20 overflow-hidden shadow-lg ${mounted ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`} 
          style={{
            background: 'linear-gradient(to bottom right, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6))',
            backdropFilter: 'blur(12px)',
          }}>
      
      {/* Enhanced background with gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-slate-800/10 pointer-events-none" />
      
      <CardContent className="p-4 relative z-10">
        <div className="space-y-2">
          <div className="flex items-center space-x-2 mb-6 pb-2 border-b border-violet-900/20">
            <Activity className="h-5 w-5 text-violet-400" />
            <h3 className="font-mono text-sm uppercase tracking-wider">
              <span className="text-violet-400">&lt;</span>
              <span className="text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">Navigation</span>
              <span className="text-violet-400">/&gt;</span>
            </h3>
          </div>
          
          <nav className="space-y-0.5">
            {navigationItems.map((item, index) => {
              const isActive = pathname === item.href || 
                (item.href !== '/admin' && pathname.startsWith(item.href));
              const isHovered = hoveredItem === item.href;
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2.5 rounded-md text-sm transition-all duration-200 group relative",
                    isActive
                      ? "bg-primary/5 text-primary border-l-2 border-l-primary/70 pl-[10px]"
                      : "text-muted-foreground hover:text-violet-300 hover:bg-violet-500/5 border-l-2 border-l-transparent pl-[10px]",
                    item.disabled && "opacity-70 cursor-not-allowed"
                  )}
                  onMouseEnter={() => setHoveredItem(item.href)}
                  onMouseLeave={() => setHoveredItem(null)}
                  onClick={(e) => {
                    if (item.disabled) {
                      e.preventDefault();
                    }
                  }}
                  style={{
                    animationDelay: mounted ? `${index * 100}ms` : '0ms',
                    animation: mounted ? 'fadeInRight 0.5s ease forwards' : 'none',
                    opacity: 0
                  }}
                >
                  <div className={`flex-shrink-0 transition-transform duration-300 ${isHovered ? 'scale-110' : ''} ${isActive ? 'text-violet-400' : ''}`}>
                    {item.icon}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className={`font-mono truncate transition-colors ${isActive ? 'text-white' : ''}`}>
                        {isActive ? (
                          <>
                            <span className="text-violet-400">&lt;</span>
                            <span className="text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">{item.label}</span>
                            <span className="text-violet-400">/&gt;</span>
                          </>
                        ) : item.label}
                      </span>
                      {item.badge && (
                        <span className="ml-2 px-2 py-0.5 text-xs bg-violet-500/10 text-violet-300 rounded-sm font-mono">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    {item.description && (
                      <div className="text-[10px] text-muted-foreground mt-0.5 truncate opacity-80 font-mono">
                        {item.description}
                      </div>
                    )}
                  </div>
                  
                  {/* Arrow indicator for hover */}
                  <ChevronRight className={`h-4 w-4 transition-transform duration-300 absolute right-2 opacity-0 ${isHovered ? 'opacity-60 translate-x-0' : '-translate-x-1'} ${isActive ? 'text-violet-400' : ''}`} />
                  
                  {/* Subtle glow effect on hover/active */}
                  {(isActive || isHovered) && (
                    <div className="absolute inset-0 bg-gradient-to-r from-violet-500/0 to-violet-500/5 pointer-events-none rounded-md" />
                  )}
                </Link>
              );
            })}
          </nav>
          
          {/* Quick Stats */}
          <div className="mt-6 pt-4 border-t border-violet-900/20" style={{
            animation: mounted ? 'fadeIn 0.5s ease forwards 0.7s' : 'none',
            opacity: 0
          }}>
            <div className="text-xs font-mono text-violet-400 mb-3 flex items-center">
              <span className="inline-block h-1 w-1 bg-violet-400 mr-2"></span>
              <span>system.stats</span>
            </div>
            <div className="space-y-2 font-mono text-[11px]">
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">ACTIVE_USERS</span>
                <span className="text-violet-400 tabular-nums">--</span>
              </div>
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">TOTAL_REVIEWS</span>
                <span className="text-violet-400 tabular-nums">--</span>
              </div>
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">PENDING_MOD</span>
                <span className="text-amber-400 tabular-nums">--</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Add keyframe animations */}
      <style jsx global>{`
        @keyframes fadeInRight {
          from { 
            transform: translateX(-10px);
            opacity: 0;
          }
          to { 
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}</style>
    </Card>
  );
}

export default AdminNavigation;
