import { createClient } from '@/lib/supabase/client';
import { logAdminAction } from '@/lib/audit/adminActions';

// Types for system administration
export interface DatabaseHealth {
  status: 'healthy' | 'warning' | 'critical';
  connections: {
    active: number;
    max: number;
    percentage: number;
  };
  performance: {
    avgQueryTime: number;
    slowQueries: number;
    cacheHitRatio: number;
  };
  storage: {
    used: number;
    total: number;
    percentage: number;
  };
  lastChecked: string;
}

export interface SystemMetrics {
  uptime: number;
  totalUsers: number;
  activeUsers: number;
  totalReviews: number;
  totalComments: number;
  avgResponseTime: number;
  errorRate: number;
  lastUpdated: string;
}

export interface SystemConfiguration {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  reviewApprovalRequired: boolean;
  commentModerationEnabled: boolean;
  maxFileUploadSize: number;
  sessionTimeout: number;
  rateLimit: {
    enabled: boolean;
    requestsPerMinute: number;
  };
}

export interface MaintenanceTask {
  id: string;
  name: string;
  description: string;
  type: 'cleanup' | 'optimization' | 'backup' | 'analysis';
  status: 'pending' | 'running' | 'completed' | 'failed';
  lastRun?: string;
  nextRun?: string;
  duration?: number;
  result?: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(adminId: string): Promise<boolean> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .rpc('is_admin', { user_id: adminId });
    
    if (error) {
      console.error('Error verifying admin permissions:', error);
      return false;
    }
    
    return data === true;
  } catch (error) {
    console.error('Error in verifyAdminPermissions:', error);
    return false;
  }
}

// Get database health metrics
export async function getDatabaseHealth(adminId: string): Promise<DatabaseHealth> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const supabase = createClient();
    
    // Get basic database statistics
    const { data: stats, error: statsError } = await supabase
      .from('profiles')
      .select('id', { count: 'exact', head: true });
    
    if (statsError) {
      console.error('Error getting database stats:', statsError);
    }

    // Simulate database health metrics (in real implementation, these would come from actual monitoring)
    const health: DatabaseHealth = {
      status: 'healthy',
      connections: {
        active: Math.floor(Math.random() * 50) + 10,
        max: 100,
        percentage: 0
      },
      performance: {
        avgQueryTime: Math.random() * 50 + 10, // 10-60ms
        slowQueries: Math.floor(Math.random() * 5),
        cacheHitRatio: Math.random() * 20 + 80 // 80-100%
      },
      storage: {
        used: Math.floor(Math.random() * 1000) + 500, // 500-1500 MB
        total: 5000, // 5GB
        percentage: 0
      },
      lastChecked: new Date().toISOString()
    };

    // Calculate percentages
    health.connections.percentage = Math.round((health.connections.active / health.connections.max) * 100);
    health.storage.percentage = Math.round((health.storage.used / health.storage.total) * 100);

    // Determine status based on metrics
    if (health.connections.percentage > 90 || health.storage.percentage > 90 || health.performance.avgQueryTime > 100) {
      health.status = 'critical';
    } else if (health.connections.percentage > 70 || health.storage.percentage > 70 || health.performance.avgQueryTime > 50) {
      health.status = 'warning';
    }

    await logAdminAction(adminId, 'VIEW_SYSTEM_HEALTH', 'system', undefined, {
      status: health.status,
      connections_percentage: health.connections.percentage,
      storage_percentage: health.storage.percentage
    });

    return health;
  } catch (error) {
    console.error('Error getting database health:', error);
    throw new Error('Failed to retrieve database health metrics');
  }
}

// Get system metrics
export async function getSystemMetrics(adminId: string): Promise<SystemMetrics> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const supabase = createClient();
    
    // Get actual counts from database
    const [usersResult, reviewsResult, commentsResult] = await Promise.all([
      supabase.from('profiles').select('id', { count: 'exact', head: true }),
      supabase.from('reviews').select('id', { count: 'exact', head: true }),
      supabase.from('comments').select('id', { count: 'exact', head: true })
    ]);

    // Get active users (users who logged in within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data: activeUsersData, error: activeUsersError } = await supabase
      .from('profiles')
      .select('id', { count: 'exact', head: true })
      .gte('updated_at', thirtyDaysAgo.toISOString());

    const metrics: SystemMetrics = {
      uptime: Math.floor(Math.random() * 30) + 1, // 1-30 days
      totalUsers: usersResult.count || 0,
      activeUsers: activeUsersData ? (activeUsersData as any).count || 0 : 0,
      totalReviews: reviewsResult.count || 0,
      totalComments: commentsResult.count || 0,
      avgResponseTime: Math.random() * 200 + 100, // 100-300ms
      errorRate: Math.random() * 2, // 0-2%
      lastUpdated: new Date().toISOString()
    };

    await logAdminAction(adminId, 'VIEW_SYSTEM_METRICS', 'system', undefined, {
      total_users: metrics.totalUsers,
      active_users: metrics.activeUsers,
      total_reviews: metrics.totalReviews,
      avg_response_time: metrics.avgResponseTime
    });

    return metrics;
  } catch (error) {
    console.error('Error getting system metrics:', error);
    throw new Error('Failed to retrieve system metrics');
  }
}

// Get system configuration
export async function getSystemConfiguration(adminId: string): Promise<SystemConfiguration> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // In a real implementation, this would come from a system_config table
    // For now, return default configuration
    const config: SystemConfiguration = {
      siteName: 'CriticalPixel',
      siteDescription: 'Gaming Reviews & Community',
      maintenanceMode: false,
      registrationEnabled: true,
      reviewApprovalRequired: false,
      commentModerationEnabled: true,
      maxFileUploadSize: 10, // MB
      sessionTimeout: 24, // hours
      rateLimit: {
        enabled: true,
        requestsPerMinute: 60
      }
    };

    await logAdminAction(adminId, 'VIEW_SYSTEM_CONFIG', 'system', undefined, {
      maintenance_mode: config.maintenanceMode,
      registration_enabled: config.registrationEnabled
    });

    return config;
  } catch (error) {
    console.error('Error getting system configuration:', error);
    throw new Error('Failed to retrieve system configuration');
  }
}

// Update system configuration
export async function updateSystemConfiguration(
  adminId: string,
  updates: Partial<SystemConfiguration>
): Promise<SystemConfiguration> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // Get current config
    const currentConfig = await getSystemConfiguration(adminId);
    
    // Merge updates
    const updatedConfig = { ...currentConfig, ...updates };

    // In a real implementation, this would update the system_config table
    // For now, we'll just log the action and return the updated config
    
    await logAdminAction(adminId, 'UPDATE_SYSTEM_CONFIG', 'system', undefined, {
      updated_fields: Object.keys(updates),
      changes: updates
    });

    return updatedConfig;
  } catch (error) {
    console.error('Error updating system configuration:', error);
    throw new Error('Failed to update system configuration');
  }
}

// Get maintenance tasks
export async function getMaintenanceTasks(adminId: string): Promise<MaintenanceTask[]> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // In a real implementation, this would come from a maintenance_tasks table
    const tasks: MaintenanceTask[] = [
      {
        id: '1',
        name: 'Database Cleanup',
        description: 'Remove orphaned records and optimize tables',
        type: 'cleanup',
        status: 'completed',
        lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        nextRun: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days from now
        duration: 45000, // 45 seconds
        result: 'Cleaned 1,234 orphaned records'
      },
      {
        id: '2',
        name: 'Performance Analysis',
        description: 'Analyze query performance and identify bottlenecks',
        type: 'analysis',
        status: 'pending',
        nextRun: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now
      },
      {
        id: '3',
        name: 'Database Backup',
        description: 'Create full database backup',
        type: 'backup',
        status: 'completed',
        lastRun: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
        nextRun: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(), // 12 hours from now
        duration: 120000, // 2 minutes
        result: 'Backup completed successfully (2.3 GB)'
      },
      {
        id: '4',
        name: 'Index Optimization',
        description: 'Rebuild and optimize database indexes',
        type: 'optimization',
        status: 'running',
        lastRun: new Date().toISOString()
      }
    ];

    await logAdminAction(adminId, 'VIEW_MAINTENANCE_TASKS', 'system', undefined, {
      total_tasks: tasks.length,
      pending_tasks: tasks.filter(t => t.status === 'pending').length,
      running_tasks: tasks.filter(t => t.status === 'running').length
    });

    return tasks;
  } catch (error) {
    console.error('Error getting maintenance tasks:', error);
    throw new Error('Failed to retrieve maintenance tasks');
  }
}

// Run maintenance task
export async function runMaintenanceTask(adminId: string, taskId: string): Promise<MaintenanceTask> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const tasks = await getMaintenanceTasks(adminId);
    const task = tasks.find(t => t.id === taskId);
    
    if (!task) {
      throw new Error('Maintenance task not found');
    }

    if (task.status === 'running') {
      throw new Error('Task is already running');
    }

    // In a real implementation, this would trigger the actual maintenance task
    // For now, we'll simulate the task execution
    const updatedTask: MaintenanceTask = {
      ...task,
      status: 'running',
      lastRun: new Date().toISOString()
    };

    await logAdminAction(adminId, 'RUN_MAINTENANCE_TASK', 'system', taskId, {
      task_name: task.name,
      task_type: task.type
    });

    return updatedTask;
  } catch (error) {
    console.error('Error running maintenance task:', error);
    throw new Error('Failed to run maintenance task');
  }
}
