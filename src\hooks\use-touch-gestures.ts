'use client';

import { useEffect, useRef, useCallback } from 'react';

interface TouchGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onTap?: () => void;
  onDoubleTap?: () => void;
  onLongPress?: () => void;
  threshold?: number;
  longPressDelay?: number;
  enabled?: boolean;
}

interface TouchPoint {
  x: number;
  y: number;
  time: number;
}

export const useTouchGestures = (options: TouchGestureOptions) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onTap,
    onDoubleTap,
    onLongPress,
    threshold = 50,
    longPressDelay = 500,
    enabled = true
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const startPoint = useRef<TouchPoint | null>(null);
  const endPoint = useRef<TouchPoint | null>(null);
  const tapCount = useRef(0);
  const tapTimer = useRef<NodeJS.Timeout | null>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const clearTimers = useCallback(() => {
    if (tapTimer.current) {
      clearTimeout(tapTimer.current);
      tapTimer.current = null;
    }
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, []);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enabled) return;
    
    const touch = e.touches[0];
    startPoint.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    // Setup long press detection
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        onLongPress();
      }, longPressDelay);
    }
  }, [enabled, onLongPress, longPressDelay]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enabled || !startPoint.current) return;
    
    // Cancel long press if user moves finger
    clearTimers();
  }, [enabled, clearTimers]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!enabled || !startPoint.current) return;

    clearTimers();
    
    const touch = e.changedTouches[0];
    endPoint.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    const deltaX = endPoint.current.x - startPoint.current.x;
    const deltaY = endPoint.current.y - startPoint.current.y;
    const deltaTime = endPoint.current.time - startPoint.current.time;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Check for swipe gestures
    if (distance > threshold && deltaTime < 300) {
      const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
      
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight();
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft();
        }
      } else {
        // Vertical swipe
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown();
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp();
        }
      }
    } 
    // Check for tap gestures
    else if (distance < 10 && deltaTime < 300) {
      tapCount.current++;
      
      if (tapCount.current === 1) {
        tapTimer.current = setTimeout(() => {
          if (tapCount.current === 1 && onTap) {
            onTap();
          }
          tapCount.current = 0;
        }, 300);
      } else if (tapCount.current === 2 && onDoubleTap) {
        if (tapTimer.current) {
          clearTimeout(tapTimer.current);
          tapTimer.current = null;
        }
        onDoubleTap();
        tapCount.current = 0;
      }
    }

    startPoint.current = null;
    endPoint.current = null;
  }, [enabled, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onTap, onDoubleTap, clearTimers]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || !enabled) return;

    // Prevent default touch behaviors for better gesture detection
    const preventDefaults = (e: TouchEvent) => {
      e.preventDefault();
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [enabled, handleTouchStart, handleTouchMove, handleTouchEnd]);

  return elementRef;
};