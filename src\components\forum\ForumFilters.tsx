'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, TrendingUp, TrendingDown, MessageCircle, User, X, ChevronDown, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { ForumSortBy } from '@/hooks/useForumPosts';
import { motion, AnimatePresence } from 'framer-motion';

interface ForumFiltersProps {
  searchUsername: string;
  sortBy: ForumSortBy;
  showMyActivity: boolean;
  onSearchChange: (username: string) => void;
  onSortChange: (sortBy: ForumSortBy) => void;
  onMyActivityToggle: (show: boolean) => void;
  onClearFilters: () => void;
  currentUserId?: string;
}

export function ForumFilters({
  searchUsername,
  sortBy,
  showMyActivity,
  onSearchChange,
  onSortChange,
  onMyActivityToggle,
  onClearFilters,
  currentUserId
}: ForumFiltersProps) {
  const [searchInput, setSearchInput] = useState(searchUsername);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowSortDropdown(false);
      }
    };

    if (showSortDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showSortDropdown]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(searchInput.trim());
  };

  const handleSearchClear = () => {
    setSearchInput('');
    onSearchChange('');
  };

  const hasActiveFilters = searchUsername || sortBy !== 'recent' || showMyActivity;
  const activeFilterCount = [searchUsername, sortBy !== 'recent', showMyActivity].filter(Boolean).length;

  const sortOptions = [
    { value: 'recent' as ForumSortBy, label: 'Recent', icon: null, shortLabel: 'Recent' },
    { value: 'popular' as ForumSortBy, label: 'Most Popular', icon: TrendingUp, shortLabel: 'Popular' },
    { value: 'unpopular' as ForumSortBy, label: 'Least Popular', icon: TrendingDown, shortLabel: 'Unpopular' },
    { value: 'comments' as ForumSortBy, label: 'Most Comments', icon: MessageCircle, shortLabel: 'Comments' },
  ];

  const currentSort = sortOptions.find(option => option.value === sortBy);

  return (
    <div className="px-6 py-3 border-b border-slate-700">
      <div className="flex items-center gap-3">
        {/* Compact Search */}
        <form onSubmit={handleSearchSubmit} className="relative flex-1 max-w-xs">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            placeholder="Search users..."
            className="pl-9 pr-8 h-9 bg-slate-800 border-slate-600 text-slate-200 placeholder:text-slate-500 text-sm focus:ring-1 focus:ring-purple-500 focus:border-purple-500 transition-all"
          />
          {searchInput && (
            <button
              type="button"
              onClick={handleSearchClear}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-200 transition-colors"
              aria-label="Clear search"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </form>

        {/* Sort Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSortDropdown(!showSortDropdown)}
            className={cn(
              "h-9 px-3 bg-slate-800 border-slate-600 text-slate-300 hover:bg-slate-700 text-sm font-medium transition-all",
              sortBy !== 'recent' && "border-purple-500 text-purple-300"
            )}
          >
            {currentSort?.icon && <currentSort.icon className="w-3 h-3 mr-2" />}
            {currentSort?.shortLabel}
            <ChevronDown className={cn(
              "w-3 h-3 ml-2 transition-transform",
              showSortDropdown && "rotate-180"
            )} />
          </Button>

          <AnimatePresence>
            {showSortDropdown && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.15 }}
                className="absolute top-full left-0 mt-1 w-48 bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50"
              >
                <div className="p-1">
                  {sortOptions.map((option) => {
                    const Icon = option.icon;
                    const isActive = sortBy === option.value;

                    return (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => {
                          onSortChange(option.value);
                          setShowSortDropdown(false);
                        }}
                        className={cn(
                          "w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors text-left",
                          isActive
                            ? "bg-purple-600/20 text-purple-300"
                            : "text-slate-300 hover:bg-slate-700/50 hover:text-slate-200"
                        )}
                      >
                        {Icon ? <Icon className="w-4 h-4 mr-3" /> : <div className="w-4 h-4 mr-3" />}
                        {option.label}
                      </button>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* My Activity Chip */}
        {currentUserId && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onMyActivityToggle(!showMyActivity)}
            className={cn(
              "h-9 px-3 text-sm font-medium transition-all",
              showMyActivity
                ? "bg-emerald-600 border-emerald-500 text-emerald-300 hover:bg-emerald-500"
                : "bg-slate-800 border-slate-600 text-slate-300 hover:bg-slate-700"
            )}
          >
            <User className="w-3 h-3 mr-2" />
            My Activity
          </Button>
        )}

        {/* Filter Indicator & Clear */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Filter className="w-3 h-3 text-purple-400" />
              <span className="text-xs font-mono text-purple-400">{activeFilterCount}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-9 px-2 text-xs text-slate-400 hover:text-slate-200 font-mono"
            >
              Clear
            </Button>
          </div>
        )}
      </div>

      {/* Active Filters Chips - Only show if multiple filters */}
      <AnimatePresence>
        {hasActiveFilters && activeFilterCount > 1 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="flex flex-wrap gap-1 mt-3 pt-3 border-t border-slate-700"
          >
            {searchUsername && (
              <Badge
                variant="secondary"
                className="bg-slate-700 text-slate-300 border-slate-600 text-xs px-2 py-1 h-6"
              >
                User: {searchUsername}
                <button
                  type="button"
                  onClick={() => onSearchChange('')}
                  className="ml-1 hover:text-slate-100"
                  aria-label="Remove user filter"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}

            {sortBy !== 'recent' && (
              <Badge
                variant="secondary"
                className="bg-slate-700 text-slate-300 border-slate-600 text-xs px-2 py-1 h-6"
              >
                {currentSort?.shortLabel}
                <button
                  type="button"
                  onClick={() => onSortChange('recent')}
                  className="ml-1 hover:text-slate-100"
                  aria-label="Remove sort filter"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}

            {showMyActivity && (
              <Badge
                variant="secondary"
                className="bg-slate-700 text-slate-300 border-slate-600 text-xs px-2 py-1 h-6"
              >
                My Activity
                <button
                  type="button"
                  onClick={() => onMyActivityToggle(false)}
                  className="ml-1 hover:text-slate-100"
                  aria-label="Remove activity filter"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
