'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  GitCompare,
  X,
  Plus,
  TrendingUp,
  TrendingDown,
  Minus,
  Monitor,
  Cpu,
  Gauge,
  Eye
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';

interface SurveyComparisonProps {
  surveys: PerformanceSurveyRecord[];
  className?: string;
}

interface ComparisonMetric {
  label: string;
  getValue: (survey: PerformanceSurveyRecord) => string | number;
  format?: (value: any) => string;
  compareType?: 'higher' | 'lower' | 'equal';
}

const comparisonMetrics: ComparisonMetric[] = [
  {
    label: 'Average FPS',
    getValue: (s) => s.fps_average || 0,
    format: (v) => `${v} FPS`,
    compareType: 'higher'
  },
  {
    label: 'Smoothness',
    getValue: (s) => s.smoothness || 0,
    format: (v) => `${v}/10`,
    compareType: 'higher'
  },
  {
    label: 'Resolution',
    getValue: (s) => s.resolution || 'Unknown',
    compareType: 'equal'
  },
  {
    label: 'Device Type',
    getValue: (s) => s.device_type || 'Unknown',
    compareType: 'equal'
  },
  {
    label: 'CPU',
    getValue: (s) => s.cpu || 'Unknown',
    compareType: 'equal'
  },
  {
    label: 'GPU',
    getValue: (s) => s.gpu || 'Unknown',
    compareType: 'equal'
  },
  {
    label: 'Memory',
    getValue: (s) => s.total_memory ? `${s.total_memory}GB` : 'Unknown',
    compareType: 'higher'
  },
  {
    label: 'Frame Generation',
    getValue: (s) => s.frame_gen ? (s.frame_gen_type || 'Enabled') : 'Disabled',
    compareType: 'equal'
  },
  {
    label: 'Upscaling',
    getValue: (s) => s.upscale ? (s.upscale_type || 'Enabled') : 'Disabled',
    compareType: 'equal'
  }
];

export function SurveyComparison({ surveys, className }: SurveyComparisonProps) {
  const [selectedSurveys, setSelectedSurveys] = useState<PerformanceSurveyRecord[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  const availableSurveys = useMemo(() => {
    return surveys.filter(survey => 
      !selectedSurveys.some(selected => selected.id === survey.id)
    );
  }, [surveys, selectedSurveys]);

  const addSurvey = (surveyId: string) => {
    const survey = surveys.find(s => s.id === surveyId);
    if (survey && selectedSurveys.length < 3) {
      setSelectedSurveys(prev => [...prev, survey]);
    }
  };

  const removeSurvey = (surveyId: string) => {
    setSelectedSurveys(prev => prev.filter(s => s.id !== surveyId));
  };

  const clearAll = () => {
    setSelectedSurveys([]);
  };

  const getComparisonIcon = (metric: ComparisonMetric, survey1: PerformanceSurveyRecord, survey2: PerformanceSurveyRecord) => {
    if (metric.compareType !== 'higher' && metric.compareType !== 'lower') {
      return null;
    }

    const value1 = metric.getValue(survey1);
    const value2 = metric.getValue(survey2);

    if (typeof value1 !== 'number' || typeof value2 !== 'number') {
      return null;
    }

    if (value1 > value2) {
      return metric.compareType === 'higher' ? 
        <TrendingUp className="text-green-400" size={14} /> : 
        <TrendingDown className="text-red-400" size={14} />;
    } else if (value1 < value2) {
      return metric.compareType === 'higher' ? 
        <TrendingDown className="text-red-400" size={14} /> : 
        <TrendingUp className="text-green-400" size={14} />;
    } else {
      return <Minus className="text-yellow-400" size={14} />;
    }
  };

  const formatSurveyTitle = (survey: PerformanceSurveyRecord) => {
    return `${survey.game_title || 'Unknown Game'} (${survey.device_type || 'Unknown'})`;
  };

  if (surveys.length < 2) {
    return null;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Toggle Button */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2"
        >
          <GitCompare size={16} />
          Compare Surveys
          {selectedSurveys.length > 0 && (
            <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
              {selectedSurveys.length}
            </span>
          )}
        </Button>

        {selectedSurveys.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAll}
            className="text-slate-400 hover:text-slate-200"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Comparison Interface */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4"
          >
            {/* Survey Selection */}
            <Card className="bg-slate-900/60 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-slate-200 flex items-center gap-2">
                  <Plus size={18} />
                  Select Surveys to Compare (Max 3)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Add Survey Dropdown */}
                  {selectedSurveys.length < 3 && availableSurveys.length > 0 && (
                    <Select onValueChange={addSurvey}>
                      <SelectTrigger className="bg-slate-800/50 border-slate-600/50">
                        <SelectValue placeholder="Select a survey to add..." />
                      </SelectTrigger>
                      <SelectContent>
                        {availableSurveys.map((survey) => (
                          <SelectItem key={survey.id} value={survey.id!}>
                            {formatSurveyTitle(survey)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}

                  {/* Selected Surveys */}
                  {selectedSurveys.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {selectedSurveys.map((survey) => (
                        <motion.div
                          key={survey.id}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          className="flex items-center gap-2 bg-purple-500/20 text-purple-300 px-3 py-2 rounded-lg"
                        >
                          <span className="text-sm">{formatSurveyTitle(survey)}</span>
                          <button
                            onClick={() => removeSurvey(survey.id!)}
                            className="hover:text-purple-200"
                          >
                            <X size={14} />
                          </button>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Comparison Table */}
            {selectedSurveys.length >= 2 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <Card className="bg-slate-900/60 border-slate-700/50">
                  <CardHeader>
                    <CardTitle className="text-slate-200 flex items-center gap-2">
                      <GitCompare size={18} />
                      Performance Comparison
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-slate-700/50">
                            <th className="text-left py-3 px-4 text-slate-400 font-medium">
                              Metric
                            </th>
                            {selectedSurveys.map((survey, index) => (
                              <th key={survey.id} className="text-left py-3 px-4 text-slate-200 font-medium">
                                Survey {index + 1}
                                <div className="text-xs text-slate-400 font-normal mt-1">
                                  {survey.game_title}
                                </div>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {comparisonMetrics.map((metric) => (
                            <tr key={metric.label} className="border-b border-slate-800/50">
                              <td className="py-3 px-4 text-slate-300 font-medium">
                                {metric.label}
                              </td>
                              {selectedSurveys.map((survey, index) => {
                                const value = metric.getValue(survey);
                                const formattedValue = metric.format ? metric.format(value) : value;
                                
                                return (
                                  <td key={survey.id} className="py-3 px-4">
                                    <div className="flex items-center gap-2">
                                      <span className="text-slate-200">{formattedValue}</span>
                                      {index > 0 && getComparisonIcon(metric, survey, selectedSurveys[0])}
                                    </div>
                                  </td>
                                );
                              })}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Summary */}
                <Card className="bg-slate-900/60 border-slate-700/50">
                  <CardHeader>
                    <CardTitle className="text-slate-200 flex items-center gap-2">
                      <Gauge size={18} />
                      Performance Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedSurveys.map((survey, index) => {
                        const fps = survey.fps_average || 0;
                        const smoothness = survey.smoothness || 0;
                        const performanceScore = Math.round(((fps / 60) + (smoothness / 10)) * 50);
                        
                        return (
                          <div key={survey.id} className="bg-slate-800/50 rounded-lg p-4">
                            <h4 className="text-slate-200 font-medium mb-2">
                              Survey {index + 1}
                            </h4>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Gauge className="text-green-400" size={14} />
                                <span className="text-slate-400 text-sm">Performance Score:</span>
                                <span className="text-green-400 font-bold">{performanceScore}%</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Monitor className="text-blue-400" size={14} />
                                <span className="text-slate-400 text-sm">Device:</span>
                                <span className="text-slate-200 capitalize">{survey.device_type}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Eye className="text-purple-400" size={14} />
                                <span className="text-slate-400 text-sm">Experience:</span>
                                <span className="text-slate-200">
                                  {fps >= 60 && smoothness >= 8 ? 'Excellent' :
                                   fps >= 30 && smoothness >= 6 ? 'Good' :
                                   fps >= 20 && smoothness >= 4 ? 'Fair' : 'Poor'}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
