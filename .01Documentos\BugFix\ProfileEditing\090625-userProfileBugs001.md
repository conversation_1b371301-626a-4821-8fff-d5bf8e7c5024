# User Profile Components Bug Fixes - 18/01/2025

## Problemas Identificados e Soluções

### 1. **Bug Crítico: Inconsistência de Tipos entre UserProfile e ExtendedUserProfile**

**Problema:** O sistema utiliza dois tipos de perfil diferentes (`UserProfile` e `ExtendedUserProfile`) que causam conflitos de mapeamento e quebram a edição de perfil.

**Local do Bug:**
- `src/components/userprofile/EditProfileModal.tsx` (linha 58)
- `src/utils/profile-conversion.ts` (função `convertToExtendedProfile`)
- `src/app/u/[slug]/page.tsx` (linha 330)

**Impacto:** 
- Campos perdidos durante conversão
- Edição de perfil não funciona corretamente
- Dados inconsistentes no GamerCard

**Solução:**
```typescript
// Definir um tipo unificado para componentes
interface UnifiedUserProfile extends UserProfile {
  // Campos adicionais para compatibilidade com componentes legacy
  uid?: string;
  userName?: string;
  displayName?: string;
  avatarUrl?: string;
  bannerUrl?: string;
  photoURL?: string;
  preferredGenres?: string[];
  favoriteConsoles?: string[];
  gamingProfiles?: GamingProfile[];
  socialMedia?: SocialMediaProfile[];
  customColors?: CustomColors;
  privacySettings?: {
    showOnlineStatus: boolean;
    showGamingProfiles: boolean;
    allowFriendRequests: boolean;
    showAchievements: boolean;
  };
}
```

### 2. **Bug: GamerCard não Exibe Dados Corretamente**

**Problema:** O componente GamerCard mostra seções vazias para "Social Profiles" e "Gaming Profiles" mesmo quando o usuário tem dados.

**Local do Bug:**
- `src/components/userprofile/GamerCard.tsx` (linhas 418-520)

**Impacto:**
- Dados sociais e de gaming não aparecem
- UX confusa para usuários

**Solução:**
```typescript
// Mapear corretamente os dados do perfil para o GamerCard
const socialMedia = useMemo(() => {
  // Buscar dados reais das redes sociais do usuário
  return profileData?.social_profiles || [];
}, [profileData]);

const gamingProfiles = useMemo(() => {
  // Buscar dados reais dos perfis de gaming do usuário
  return profileData?.gaming_profiles || [];
}, [profileData]);
```

### 3. **Bug: EditProfileModal com Estados Inconsistentes**

**Problema:** O modal de edição não sincroniza corretamente com os dados do perfil, causando perda de dados durante a edição.

**Local do Bug:**
- `src/components/userprofile/EditProfileModal.tsx` (linhas 342-391)

**Impacto:**
- Dados perdidos ao salvar
- Estado inconsistente entre passos do modal

**Solução:**
```typescript
// Corrigir inicialização do formData
const [formData, setFormData] = useState<Partial<UnifiedUserProfile>>(() => ({
  bio: profile.bio || '',
  preferred_genres: profile.preferred_genres || [],
  favorite_consoles: profile.favorite_consoles || [],
  gaming_profiles: profile.gaming_profiles || [],
  social_media: profile.social_media || [],
  avatar_url: profile.avatar_url || '',
  banner_url: profile.banner_url || '',
  theme: profile.theme || 'muted-dark',
  custom_colors: profile.custom_colors || { 
    primary: '#8b5cf6', 
    secondary: '#7c3aed', 
    accent: '#ec4899' 
  }
}));
```

### 4. **Bug: Validação de Formulário Não Funciona**

**Problema:** As validações Zod não estão sendo aplicadas corretamente nos campos do formulário.

**Local do Bug:**
- `src/components/userprofile/EditProfileModal.tsx` (ausência de validação Zod)

**Solução:**
```typescript
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const profileSchema = z.object({
  bio: z.string().max(500, "Bio deve ter no máximo 500 caracteres").optional(),
  preferred_genres: z.array(z.string()).max(10, "Máximo 10 gêneros").optional(),
  favorite_consoles: z.array(z.string()).max(5, "Máximo 5 consoles").optional(),
  theme: z.string().optional(),
});

// Aplicar no useForm
const form = useForm({
  resolver: zodResolver(profileSchema),
  defaultValues: formData
});
```

### 5. **Bug: Permissões de Privacidade Incorretas**

**Problema:** O sistema de permissões em `calculateProfilePermissions` não funciona corretamente.

**Local do Bug:**
- `src/utils/profile-permissions.ts` (função calculateProfilePermissions)

**Solução:**
```typescript
export function calculateProfilePermissions(
  profile: UserProfile, 
  viewerId?: string
): ProfileViewPermissions {
  const isOwnProfile = viewerId === profile.id;
  const isAdmin = profile.is_admin || false;
  const privacy = profile.privacy_settings;
  
  if (isOwnProfile || isAdmin) {
    return {
      canViewOnlineStatus: true,
      canViewGamingProfiles: true,
      canViewAchievements: true,
      canViewContactInfo: true,
      canViewFullProfile: true,
      canViewStats: true,
      canSendFriendRequest: false,
      canContact: true
    };
  }
  
  // Implementar lógica baseada nas configurações de privacidade
  return {
    canViewOnlineStatus: privacy?.show_online_status ?? true,
    canViewGamingProfiles: privacy?.show_gaming_profiles ?? true,
    canViewAchievements: privacy?.show_achievements ?? true,
    canViewContactInfo: privacy?.allow_contact ?? true,
    canViewFullProfile: privacy?.profile_visibility === 'public',
    canViewStats: privacy?.profile_visibility === 'public',
    canSendFriendRequest: privacy?.allow_friend_requests ?? true,
    canContact: privacy?.allow_contact ?? true
  };
}
```

### 6. **Bug: Conversão de Perfil com Campos Perdidos**

**Problema:** A função `convertToExtendedProfile` perde dados durante a conversão.

**Local do Bug:**
- `src/utils/profile-conversion.ts` (linhas 8-50)

**Solução:**
```typescript
export function convertToExtendedProfile(profile: UserProfile): UnifiedUserProfile {
  return {
    // Manter todos os campos originais
    ...profile,
    
    // Adicionar campos de compatibilidade
    uid: profile.id,
    userName: profile.username,
    displayName: profile.display_name,
    avatarUrl: profile.avatar_url || undefined,
    bannerUrl: profile.banner_url || undefined,
    photoURL: profile.avatar_url || undefined,
    preferredGenres: profile.preferred_genres || [],
    favoriteConsoles: profile.favorite_consoles || [],
    
    // Garantir que não há campos undefined que quebrem a interface
    gamingProfiles: profile.gaming_profiles || [],
    socialMedia: profile.social_media || [],
    
    // Conversão de dates com fallback seguro
    lastSeen: profile.last_seen ? new Date(profile.last_seen) : null,
    createdAt: profile.created_at ? new Date(profile.created_at) : new Date(),
    updatedAt: profile.updated_at ? new Date(profile.updated_at) : new Date(),
    
    // Configurações de privacidade com fallback
    privacySettings: profile.privacy_settings ? {
      showOnlineStatus: profile.privacy_settings.show_online_status,
      showGamingProfiles: profile.privacy_settings.show_gaming_profiles,
      allowFriendRequests: profile.privacy_settings.allow_friend_requests,
      showAchievements: profile.privacy_settings.show_achievements
    } : {
      showOnlineStatus: true,
      showGamingProfiles: true,
      allowFriendRequests: true,
      showAchievements: true
    }
  };
}
```

## Próximos Passos

1. **Prioridade Alta:** Corrigir tipos inconsistentes
2. **Prioridade Alta:** Implementar validação Zod adequada
3. **Prioridade Média:** Corrigir exibição de dados no GamerCard
4. **Prioridade Média:** Corrigir sistema de permissões
5. **Prioridade Baixa:** Otimizar performance dos componentes

## Testes Necessários

- [ ] Testar criação de perfil
- [ ] Testar edição de perfil
- [ ] Testar visualização de perfil público/privado
- [ ] Testar conversão entre tipos de perfil
- [ ] Testar validações de formulário
- [ ] Testar permissões de privacidade

## Observações

Este documento identifica os principais bugs encontrados no sistema de perfis. A correção destes problemas irá significativamente melhorar a experiência do usuário e a estabilidade do sistema. 