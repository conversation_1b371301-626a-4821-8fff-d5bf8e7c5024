// Comment Moderation Hook
// Date: 21/06/2025
// Task: Comment Moderation Dashboard Implementation

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';
import { CommentModerationData, CommentModerationAction, BlockUserAction } from '@/types/commentModeration';
import { AdvancedModerationTools } from '@/lib/moderation/advancedTools';
import { checkCommentRateLimit } from '@/lib/security/rateLimiting';
import { SpamDetector } from '@/lib/security/spamDetection';
import { ContentFilter } from '@/lib/security/contentFilter';

export function useCommentModeration(userId: string) {
  const queryClient = useQueryClient();
  const supabase = createClient();

  // Get all comments on user's reviews
  const commentsQuery = useQuery({
    queryKey: ['user-comments', userId],
    queryFn: async (): Promise<CommentModerationData[]> => {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          review:reviews!inner(id, title, slug, author_id, game_name, created_at),
          author:profiles!author_id(id, username, display_name, avatar_url)
        `)
        .eq('reviews.author_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Get reports for these comments separately
      let reports: any[] = [];
      if (data && data.length > 0) {
        const commentIds = data.map((c: any) => c.id);
        const { data: reportsData } = await supabase
          .from('content_flags')
          .select(`
            *,
            reporter:profiles!reporter_id(username, display_name)
          `)
          .in('content_id', commentIds)
          .eq('content_type', 'comment');

        reports = reportsData || [];
      }

      const mappedData = (data || []).map((comment: any) => ({
        id: comment.id,
        review_id: comment.review_id,
        review_title: comment.review?.title || 'Unknown Review',
        review_slug: comment.review?.slug || '',
        game_name: comment.review?.game_name || 'Unknown Game',
        review_created_at: comment.review?.created_at || '',
        author_id: comment.author_id,
        author_name: comment.author_name,
        author_username: comment.author?.username || 'unknown',
        content: comment.content,
        created_at: comment.created_at,
        is_approved: comment.is_approved,
        is_pinned: comment.is_pinned,
        is_deleted: comment.is_deleted,
        flag_count: comment.flag_count || 0,
        upvotes: comment.upvotes || 0,
        downvotes: comment.downvotes || 0,
        moderation_notes: comment.moderation_notes,
        moderated_by: comment.moderated_by,
        moderated_at: comment.moderated_at,
        reports: reports.filter((r: any) => r.content_id === comment.id) || [],
        author: comment.author,
        review: comment.review,
      }));

      return mappedData;
    },
    enabled: !!userId,
    staleTime: 30000,
  });

  // Moderation actions
  const moderateComment = useMutation({
    mutationFn: async ({ 
      commentId, 
      action, 
      reason, 
      notes 
    }: CommentModerationAction) => {
      const updates: any = {
        moderated_by: userId,
        moderated_at: new Date().toISOString(),
        moderation_notes: notes,
      };

      switch (action) {
        case 'approve':
          updates.is_approved = true;
          break;
        case 'reject':
          updates.is_approved = false;
          break;
        case 'delete':
          updates.is_deleted = true;
          break;
        case 'pin':
          updates.is_pinned = true;
          break;
        case 'unpin':
          updates.is_pinned = false;
          break;
      }

      const { error } = await supabase
        .from('comments')
        .update(updates)
        .eq('id', commentId);

      if (error) throw error;

      // Log the action using RPC function if it exists
      try {
        await supabase.rpc('log_comment_action', {
          p_comment_id: commentId,
          p_action_type: action,
          p_reason: reason,
        });
      } catch (rpcError) {
        // RPC function might not exist, continue without logging
        console.warn('Comment action logging failed:', rpcError);
      }
    },
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "Action completed",
        description: `Comment ${action}d successfully.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Block user
  const blockUser = useMutation({
    mutationFn: async ({ 
      reviewId, 
      userIdToBlock,
      reason
    }: BlockUserAction) => {
      // Get current settings
      const { data: settings } = await supabase
        .from('comment_moderation_settings')
        .select('blocked_users')
        .eq('review_id', reviewId)
        .single();

      const blockedUsers = settings?.blocked_users || [];
      if (!blockedUsers.includes(userIdToBlock)) {
        blockedUsers.push(userIdToBlock);
      }

      const { error } = await supabase
        .from('comment_moderation_settings')
        .upsert({
          review_id: reviewId,
          owner_id: userId,
          blocked_users: blockedUsers,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "User blocked",
        description: "User has been blocked from commenting on this review.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Bulk moderation
  const bulkModerateComments = useMutation({
    mutationFn: async ({
      commentIds,
      action,
      reason
    }: {
      commentIds: string[];
      action: 'approve' | 'reject' | 'delete';
      reason?: string;
    }) => {
      const moderationTools = new AdvancedModerationTools();
      return moderationTools.bulkModerateComments(commentIds, action, userId, reason);
    },
    onSuccess: (result, { action }) => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "Bulk action completed",
        description: `${result.success} comments ${action}d successfully. ${result.failed > 0 ? `${result.failed} failed.` : ''}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Auto-moderation
  const runAutoModeration = useMutation({
    mutationFn: async (reviewId: string) => {
      const moderationTools = new AdvancedModerationTools();
      return moderationTools.applyAutoModerationRules(reviewId);
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "Auto-moderation completed",
        description: `Processed ${result.processed} comments: ${result.approved} approved, ${result.flagged} flagged, ${result.blocked} blocked.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    comments: commentsQuery.data || [],
    isLoading: commentsQuery.isLoading,
    error: commentsQuery.error,
    moderateComment,
    blockUser,
    bulkModerateComments,
    runAutoModeration,
  };
}
