'use client';

import { useBackgroundDimmer } from '@/hooks/useBackgroundDimmer';
import { motion } from 'framer-motion';
import { Sun, Moon } from 'lucide-react';

interface BackgroundDimmerSliderProps {
  className?: string;
}

const BackgroundDimmerSlider = ({ className = '' }: BackgroundDimmerSliderProps) => {
  const { dimmerValue, updateDimmerValue, isLoaded } = useBackgroundDimmer();

  if (!isLoaded) {
    return null; // Prevent hydration mismatch
  }

  return (
    <motion.div 
      className={`flex items-center space-x-2 ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Moon icon for darker */}
      <Moon className="w-4 h-4 text-slate-400" />
      
      {/* Slider */}
      <div className="relative w-20 h-2">
        <input
          type="range"
          min="0"
          max="100"
          value={dimmerValue}
          onChange={(e) => updateDimmerValue(parseInt(e.target.value, 10))}
          className="dimmer-slider w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, 
              #1e293b 0%, 
              #334155 ${dimmerValue < 50 ? dimmerValue * 2 : 100}%, 
              ${dimmerValue > 50 ? '#64748b' : '#334155'} ${dimmerValue}%, 
              #1e293b 100%)`
          }}
        />
        <motion.div
          className={`absolute top-0 left-0 h-2 rounded-lg pointer-events-none ${
            dimmerValue <= 50 
              ? 'bg-gradient-to-r from-slate-800 to-slate-600' 
              : 'bg-gradient-to-r from-slate-500 to-slate-300'
          }`}
          style={{ width: `${dimmerValue}%` }}
          initial={{ width: 0 }}
          animate={{ width: `${dimmerValue}%` }}
          transition={{ duration: 0.2 }}
        />
        {/* Center marker at 50% */}
        <div className="absolute top-0 left-1/2 transform -translate-x-0.5 w-0.5 h-2 bg-slate-400 opacity-50 rounded-full pointer-events-none" />
      </div>
      
      {/* Sun icon for brighter */}
      <Sun className="w-4 h-4 text-slate-300" />
    </motion.div>
  );
};

export default BackgroundDimmerSlider;
