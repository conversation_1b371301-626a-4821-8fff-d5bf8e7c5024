// src/lib/admin/security-utils.ts
// SECURITY UTILITY FUNCTIONS
// Non-server-action utility functions for admin security
// Date: 11/06/2025

// Admin permission levels with hierarchical structure
export enum AdminPermissionLevel {
  SUPER_ADMIN = 'SUPER_ADMIN',      // Level 5 - Full system access
  ADMIN = 'ADMIN',                  // Level 4 - Full user management
  MODERATOR = 'MODERATOR',          // Level 3 - Limited user management
  EDITOR = 'EDITOR',                // Level 2 - Content management only
  VIEWER = 'VIEWER'                 // Level 1 - Read-only access
}

// Critical operations requiring enhanced security
export enum CriticalOperation {
  USER_ROLE_MODIFY = 'USER_ROLE_MODIFY',
  USER_SUSPEND = 'USER_SUSPEND',
  USER_DELETE = 'USER_DELETE',
  ADMIN_PROMOTE = 'ADMIN_PROMOTE',
  BULK_USER_UPDATE = 'BULK_USER_UPDATE',
  SECURITY_CONFIG = 'SECURITY_CONFIG',
  CONTENT_MODERATE = 'CONTENT_MODERATE',
  BULK_CONTENT_UPDATE = 'BULK_CONTENT_UPDATE'
}

// Permission hierarchy mapping
const PERMISSION_HIERARCHY = {
  [AdminPermissionLevel.SUPER_ADMIN]: 5,
  [AdminPermissionLevel.ADMIN]: 4,
  [AdminPermissionLevel.MODERATOR]: 3,
  [AdminPermissionLevel.EDITOR]: 2,
  [AdminPermissionLevel.VIEWER]: 1
};

// Critical operation permission requirements
const OPERATION_PERMISSIONS = {
  [CriticalOperation.USER_ROLE_MODIFY]: AdminPermissionLevel.ADMIN,
  [CriticalOperation.USER_SUSPEND]: AdminPermissionLevel.MODERATOR,
  [CriticalOperation.USER_DELETE]: AdminPermissionLevel.ADMIN,
  [CriticalOperation.ADMIN_PROMOTE]: AdminPermissionLevel.SUPER_ADMIN,
  [CriticalOperation.BULK_USER_UPDATE]: AdminPermissionLevel.ADMIN,
  [CriticalOperation.SECURITY_CONFIG]: AdminPermissionLevel.SUPER_ADMIN,
  [CriticalOperation.CONTENT_MODERATE]: AdminPermissionLevel.MODERATOR,
  [CriticalOperation.BULK_CONTENT_UPDATE]: AdminPermissionLevel.ADMIN
};

/**
 * HIERARCHICAL PERMISSION VERIFICATION
 * Prevents privilege escalation attacks
 */
export function hasPermissionForOperation(
  userLevel: AdminPermissionLevel,
  operation: CriticalOperation
): boolean {
  const requiredLevel = OPERATION_PERMISSIONS[operation];
  const userHierarchy = PERMISSION_HIERARCHY[userLevel];
  const requiredHierarchy = PERMISSION_HIERARCHY[requiredLevel];
  
  return userHierarchy >= requiredHierarchy;
}

/**
 * MFA REQUIREMENT DETERMINATION
 * Determines if MFA is required based on operation and permission level
 * SECURITY UPDATE: Now requires MFA for ALL admin access
 */
export function shouldRequireMFA(
  operation?: CriticalOperation,
  permissionLevel?: AdminPermissionLevel
): boolean {
  // SECURITY ENHANCEMENT: Always require MFA for SUPER_ADMIN
  // For other admin levels, require MFA for critical operations only

  if (permissionLevel === AdminPermissionLevel.SUPER_ADMIN) {
    return true; // SUPER_ADMIN always requires MFA
  }

  // For other admin levels, require MFA for critical operations
  const criticalOperations = [
    'USER_DELETE',
    'ADMIN_PROMOTE',
    'SECURITY_CONFIG',
    'BULK_USER_UPDATE',
    'SYSTEM_SETTINGS_MODIFY',
    'AUDIT_LOG_ACCESS',
  ];

  if (operation && criticalOperations.includes(operation)) {
    return true;
  }

  return false; // Other operations don't require MFA for regular admins
}

// Admin verification result interface
export interface AdminVerificationResult {
  isValid: boolean;
  adminId: string;
  permissionLevel: AdminPermissionLevel;
  permissions: string[];
  isSuspended: boolean;
  lastVerified: string;
  mfaRequired: boolean;
  rateLimitStatus: {
    allowed: boolean;
    retryAfter?: number;
  };
}

// Export constants for use in other files
export { PERMISSION_HIERARCHY, OPERATION_PERMISSIONS };
