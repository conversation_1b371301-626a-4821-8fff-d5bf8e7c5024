/* 
 * IGDB Search Component - Unified Input+Dropdown CSS
 * Priority: Maximum - overrides all inherited styles
 * Purpose: Force Lato font family and create seamless dropdown
 */

/* Global reset for IGDB component */
.game-search-combobox,
.game-search-combobox *,
.game-search-combobox input,
.game-search-combobox textarea,
.game-search-combobox select,
.game-search-combobox input::placeholder,
.game-search-combobox textarea::placeholder,
.game-search-combobox [role="combobox"],
.game-search-combobox [role="listbox"],
.game-search-combobox [role="option"],
.tyq-input-container,
.tyq-input-container *,
.tyq-input-container input,
.tyq-input-container textarea,
.tyq-input-container select,
.tyq-input-container input::placeholder,
.tyq-input-container textarea::placeholder {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
}

/* Unified dropdown styles - no portal needed */
.game-search-combobox [role="listbox"] {
  background: rgb(30 41 59) !important;
  border: 1px solid rgb(51 65 85) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3) !important;
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

/* Dropdown options styling */
.game-search-combobox [role="option"] {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
}

/* Specific weight overrides for interactive states */
.game-search-combobox span[class*="font-medium"] {
  font-weight: 500 !important;
}

.game-search-combobox span[class*="font-semibold"] {
  font-weight: 600 !important;
}

/* Headless UI component overrides */
[data-headlessui-combobox] input,
[data-headlessui-combobox] input::placeholder,
[data-headlessui-listbox] *,
[data-headlessui-option] * {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  font-style: normal !important;
}

/* Ultra-specific placeholder override */
input[placeholder*="Search for a game"]::placeholder {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
}

/* Ensure the container positioning works */
.tyq-input-container.relative {
  position: relative !important;
}

/* Override any z-index conflicts */
.game-search-combobox .absolute {
  z-index: 100 !important;
}

/* Font utility class */
.font-lato {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
  font-style: normal !important;
  font-weight: 400 !important;
}