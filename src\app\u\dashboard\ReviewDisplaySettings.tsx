'use client';

import React, { useState, useEffect, useTransition } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Grid, List, Settings, Check, Loader2, ChevronDown, ChevronUp, FileText } from 'lucide-react';
import { saveReviewDisplaySettings, getReviewDisplaySettings } from './actions';
import { useAuthContext } from '@/contexts/auth-context';

interface ReviewDisplaySettings {
  viewMode: 'grid' | 'list' | 'excerpt';
  itemsPerPage: number;
  showFilters: boolean;
  defaultSort: 'date' | 'rating' | 'title';
}

interface ReviewDisplaySettingsProps {
  userId: string;
}

export default function ReviewDisplaySettings({ userId }: ReviewDisplaySettingsProps) {
  const [settings, setSettings] = useState<ReviewDisplaySettings>({
    viewMode: 'grid',
    itemsPerPage: 6,
    showFilters: false,
    defaultSort: 'date'
  });
  const [originalSettings, setOriginalSettings] = useState<ReviewDisplaySettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isExpanded, setIsExpanded] = useState(false);
  const { user } = useAuthContext();

  // Load current settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        console.log('🔄 Loading review display settings for user:', userId);
        const response = await getReviewDisplaySettings(userId);
        
        console.log('📥 getReviewDisplaySettings response:', response);
        
        if (response && response.success && response.data) {
          console.log('✅ Setting loaded data:', response.data);
          setSettings(response.data);
          setOriginalSettings(response.data);
        } else {
          console.log('⚠️ No data found, using defaults');
          // If no data found, set originalSettings to current defaults
          const defaultSettings = {
            viewMode: 'grid' as const,
            itemsPerPage: 6,
            showFilters: false,
            defaultSort: 'date' as const
          };
          setSettings(defaultSettings);
          setOriginalSettings(defaultSettings);
        }
      } catch (error) {
        console.error('❌ Error loading review display settings:', error);
        // On error, also set defaults
        const defaultSettings = {
          viewMode: 'grid' as const,
          itemsPerPage: 6,
          showFilters: false,
          defaultSort: 'date' as const
        };
        setOriginalSettings(defaultSettings);
      } finally {
        setIsLoading(false);
      }
    };

    if (userId) {
      loadSettings();
    }
  }, [userId]);

  // Check if settings have changed
  const hasChanges = originalSettings ? (
    settings.viewMode !== originalSettings.viewMode ||
    settings.itemsPerPage !== originalSettings.itemsPerPage ||
    settings.showFilters !== originalSettings.showFilters ||
    settings.defaultSort !== originalSettings.defaultSort
  ) : false;

  const handleSave = async () => {
    if (!hasChanges || !user) return;

    setIsSaving(true);
    setSaveStatus('idle');

    try {
      startTransition(async () => {
        const response = await saveReviewDisplaySettings(userId, settings);
        
        if (response && response.success) {
          setOriginalSettings(settings);
          setSaveStatus('success');
          
          // Clear success message after 3 seconds
          setTimeout(() => setSaveStatus('idle'), 3000);
        } else {
          setSaveStatus('error');
          console.error('Failed to save settings:', response?.error);
        }
      });
    } catch (error) {
      setSaveStatus('error');
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    if (originalSettings) {
      setSettings(originalSettings);
      setSaveStatus('idle');
    }
  };

  if (isLoading) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono text-base font-semibold tracking-wide">
            <Settings className="h-5 w-5 text-emerald-500" />
            <span className="text-slate-200 uppercase">LOADING...</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="py-6">
          <div className="flex items-center justify-center h-16">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Review Display Settings
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              Customize how your reviews are displayed with infinite scroll loading
            </p>
          </div>
          <div className="text-gray-400 hover:text-white ml-4">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent className="space-y-5">
              {/* View Mode Selection */}
              <div className="space-y-2">
                <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
                  Display Mode
                </label>
                <div className="grid grid-cols-3 gap-3">
                  <motion.button
                    type="button"
                    onClick={() => setSettings(prev => ({ ...prev, viewMode: 'grid' }))}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      settings.viewMode === 'grid'
                        ? 'border-purple-500/50 bg-purple-500/20'
                        : 'border-gray-700/50 bg-gray-800/30 hover:border-gray-600'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Grid className={`h-5 w-5 mx-auto mb-2 ${
                      settings.viewMode === 'grid' ? 'text-purple-400' : 'text-gray-400'
                    }`} />
                    <div className="font-mono text-xs font-semibold text-white uppercase tracking-wide">Grid</div>
                    <div className="font-['Lato'] text-xs text-gray-400 mt-1">Visual card layout</div>
                  </motion.button>

                  <motion.button
                    type="button"
                    onClick={() => setSettings(prev => ({ ...prev, viewMode: 'list' }))}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      settings.viewMode === 'list'
                        ? 'border-purple-500/50 bg-purple-500/20'
                        : 'border-gray-700/50 bg-gray-800/30 hover:border-gray-600'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <List className={`h-5 w-5 mx-auto mb-2 ${
                      settings.viewMode === 'list' ? 'text-purple-400' : 'text-gray-400'
                    }`} />
                    <div className="font-mono text-xs font-semibold text-white uppercase tracking-wide">List</div>
                    <div className="font-['Lato'] text-xs text-gray-400 mt-1">Compact list layout</div>
                  </motion.button>

                  <motion.button
                    type="button"
                    onClick={() => setSettings(prev => ({ ...prev, viewMode: 'excerpt' }))}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      settings.viewMode === 'excerpt'
                        ? 'border-purple-500/50 bg-purple-500/20'
                        : 'border-gray-700/50 bg-gray-800/30 hover:border-gray-600'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <FileText className={`h-5 w-5 mx-auto mb-2 ${
                      settings.viewMode === 'excerpt' ? 'text-purple-400' : 'text-gray-400'
                    }`} />
                    <div className="font-mono text-xs font-semibold text-white uppercase tracking-wide">Excerpt</div>
                    <div className="font-['Lato'] text-xs text-gray-400 mt-1">Preview with excerpts</div>
                  </motion.button>
                </div>
              </div>

              {/* Items Per Load */}
              <div className="space-y-2">
                <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
                  Items Per Load
                </label>
                <Select
                  value={settings.itemsPerPage.toString()}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, itemsPerPage: parseInt(value) }))}
                >
                  <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    <SelectItem value="4" className="font-mono text-xs">4 items</SelectItem>
                    <SelectItem value="6" className="font-mono text-xs">6 items</SelectItem>
                    <SelectItem value="8" className="font-mono text-xs">8 items</SelectItem>
                    <SelectItem value="10" className="font-mono text-xs">10 items</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Default Sort */}
              <div className="space-y-2">
                <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
                  Default Sort Order
                </label>
                <Select
                  value={settings.defaultSort}
                  onValueChange={(value: 'date' | 'rating' | 'title') => 
                    setSettings(prev => ({ ...prev, defaultSort: value }))
                  }
                >
                  <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    <SelectItem value="date" className="font-mono text-xs">Latest First</SelectItem>
                    <SelectItem value="rating" className="font-mono text-xs">Highest Rated</SelectItem>
                    <SelectItem value="title" className="font-mono text-xs">Alphabetical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status Messages */}
              {(saveStatus === 'success' || saveStatus === 'error') && (
                <div className="flex items-center justify-center py-2">
                  {saveStatus === 'success' && (
                    <div className="flex items-center gap-2 text-green-400">
                      <Check className="h-4 w-4" />
                      <span className="font-mono text-xs tracking-wide">Settings saved!</span>
                    </div>
                  )}
                  {saveStatus === 'error' && (
                    <span className="font-mono text-xs text-red-400 tracking-wide">Failed to save settings</span>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              {hasChanges && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="pt-4 border-t border-slate-700/50 space-y-3"
                >
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    disabled={isSaving || isPending}
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-800 font-mono text-xs uppercase tracking-wide"
                  >
                    Reset to Original
                  </Button>
                  
                  <Button
                    onClick={handleSave}
                    disabled={!hasChanges || isSaving || isPending || !user}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50 font-mono text-xs uppercase tracking-wide font-semibold"
                    title={!user ? 'User not authenticated' : !hasChanges ? 'No changes to save' : 'Save changes'}
                  >
                    {isSaving || isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Settings className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </motion.div>
              )}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}