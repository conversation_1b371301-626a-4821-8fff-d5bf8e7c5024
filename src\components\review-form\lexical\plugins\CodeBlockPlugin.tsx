// src/components/review-form/lexical/plugins/CodeBlockPlugin.tsx
'use client';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $createCodeNode, $isCodeNode, CodeNode } from '@lexical/code';
import { $getSelection, $isRangeSelection } from 'lexical';
import { useEffect } from 'react';

export default function CodeBlockPlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // Register the code node if not already registered
    if (!editor.hasNodes([CodeNode])) {
      throw new Error('CodeBlockPlugin: CodeNode not registered on editor');
    }
  }, [editor]);

  return null;
}
