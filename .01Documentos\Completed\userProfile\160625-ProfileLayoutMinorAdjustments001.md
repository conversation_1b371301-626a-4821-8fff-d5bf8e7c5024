# Profile Layout Minor Adjustments - Task Log

**Date:** 16/06/25  
**Task:** ProfileLayoutMinorAdjustments001  
**Status:** In Progress  

## 📋 **TASK OVERVIEW**

### **Issues to Fix:**
1. ✅ **Sidebar Offset Issue** - UserContentTabs sticky positioning goes under navbar when scrolling
2. 🔄 **Featured Review Saving Error** - Error when saving featured review in dashboard
3. ✅ **Review Card Size** - Increase height of review cards to make them taller
4. 🔄 **Featured Review Sync** - Ensure featured review in profile matches dashboard setting
5. 🔄 **Navbar Stats Widget** - Fix display of review and survey counts in navbar

## 🔧 **TECHNICAL ANALYSIS**

### **Current Issues Identified:**
- **Sidebar Positioning**: Uses `sticky top-4` but navbar has `pt-24` offset
- **Review Cards**: Currently use `h-32` for image height, need to increase
- **Featured Review API**: `/api/u/featured-review` endpoint exists but may have error handling issues
- **Stats Display**: Need to locate and fix user stats in navbar/user menu

## 📁 **FILES TO MODIFY**

### **Primary Files:**
1. `src/app/u/[slug]/ProfilePageClient.tsx` - Fix sticky positioning and review card sizing
2. `src/components/userprofile/UserContentTabs.tsx` - Update sticky positioning
3. `src/components/dashboard/FeaturedBannerConfig.tsx` - Investigate saving error
4. `src/components/layout/GameStyleUserMenu.tsx` - Fix stats display
5. `src/app/api/u/featured-review/route.ts` - Check error handling

### **Supporting Files:**
- `src/components/userprofile/ReviewsModule.tsx` - Review card styling
- `src/components/dashboard/ReviewCard.tsx` - Dashboard review card sizing
- `src/hooks/useUserContent.ts` - Data fetching for stats

## 🛠 **IMPLEMENTATION PLAN**

### **Phase 1: Quick Fixes**
- [x] Fix UserContentTabs sticky positioning
- [x] Increase review card heights
- [x] Test sidebar behavior on scroll

### **Phase 2: Data & API Issues**
- [x] Fix featured review saving error (database column mismatch + API error handling)
- [x] Add navbar stats display
- [x] Ensure featured review sync (fetch real data)

### **Phase 3: UI Improvements**
- [x] Reduce sidebar offset distance (top-28 to top-20)
- [x] Remove filters from surveys tab (keep only search)
- [x] Remove grid layout from surveys (list only)
- [x] Add flow-style pagination (6 surveys per page)
- [x] Improve error handling for featured review API

### **Phase 4: Testing & Validation**
- [ ] Test all fixes in development
- [ ] Verify responsive behavior
- [ ] Document changes

## 📝 **CHANGE LOG**

### **Files Modified:**

#### **1. `src/app/u/[slug]/ProfilePageClient.tsx`**
- **Lines 1125-1127**: Fixed sticky positioning from `top-4` to `top-28` and adjusted max-height
- **Lines 432, 452, 466**: Increased review card heights from `h-72` to `h-80`
- **Lines 20-22**: Added import for `getFeaturedReview`
- **Lines 798-803**: Added state for featured review
- **Lines 1025-1039**: Replaced hardcoded featured review with real data fetching

#### **2. `src/app/u/actions.ts`**
- **Lines 1162-1166**: Fixed database column from `user_id` to `author_id`
- **Lines 1173-1178**: Fixed database column from `user_id` to `author_id`
- **Lines 1218-1221**: Fixed database column from `user_id` to `author_id`

#### **3. `src/components/layout/GameStyleUserMenu.tsx`**
- **Lines 3-5**: Added useState import
- **Lines 127-147**: Added user stats display (reviews and surveys count)

#### **4. `src/components/dashboard/FeaturedBannerConfig.tsx`**
- **Lines 115-126**: Added better error handling for API responses
- **Lines 140-146**: Added null check for result object

#### **5. `src/components/userprofile/SurveysModule.tsx`**
- **Lines 10-20**: Updated imports (removed Grid, List, added ChevronLeft, ChevronRight)
- **Lines 45-130**: Removed SurveysGrid component (grid view)
- **Lines 205-214**: Removed viewMode and sortBy states, added pagination
- **Lines 223-248**: Updated filtering logic and added pagination
- **Lines 269-347**: Completely redesigned UI - removed filters, view toggle, added flow pagination
- **Line 8**: Removed Select component imports

#### **6. `src/app/u/actions-content.ts`**
- **Lines 370-398**: Fixed getFeaturedReview query (removed .single(), fixed column selection)
- **Lines 400-409**: Updated error handling for array response instead of single object
- **Lines 411-431**: Fixed data transformation to use reviewData object and parse overall_score

#### **7. `src/app/api/u/featured-review/route.ts`**
- **Lines 4-21**: Added console logging for debugging API calls
- **Lines 23-52**: Added detailed logging for each action and result

#### **8. `src/types/user-content.ts`**
- **Lines 3-26**: Added missing `title` and `slug` properties to UserReview interface

#### **9. `src/components/dashboard/FeaturedBannerConfig.tsx`**
- **Lines 69-105**: Added debugging console logs to track review loading process

#### **10. `src/app/u/[slug]/ProfilePageClient.tsx`**
- **Lines 1025-1044**: Added debugging logs for featured review fetching
- **Lines 1109-1117**: Cleaned up featured review display logic

---

**Tools Used:** Sequential Thinking, Codebase Retrieval, File Editor  
**Next Steps:** Begin implementation of fixes  
