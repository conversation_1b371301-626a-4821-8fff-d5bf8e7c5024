/**
 * SteamGridDB API utility functions for fetching game artwork and assets
 */
import SGDB from 'steamgriddb';

// SteamGridDB API key from environment variables
const STEAMGRIDDB_API_KEY = process.env.STEAMGRIDDB_API_KEY;

// Initialize SteamGridDB client
let sgdbClient: SGDB | null = null;

/**
 * Initialize the SteamGridDB client
 */
function initClient(): SGDB {
  if (!sgdbClient) {
    if (!STEAMGRIDDB_API_KEY) {
      throw new Error('STEAMGRIDDB_API_KEY not configured in environment variables');
    }
    sgdbClient = new SGDB(STEAMGRIDDB_API_KEY);
  }
  return sgdbClient;
}

/**
 * Search for games on SteamGridDB
 * @param query Game title to search for
 */
export async function searchSteamGridDBGames(query: string) {
  try {
    const client = initClient();
    const games = await client.searchGame(query);
    
    return games.map((game: any) => ({
      id: game.id,
      name: game.name,
      types: game.types || [],
      verified: game.verified || false
    }));
  } catch (error) {
    console.error('Error searching SteamGridDB games:', error);
    throw new Error('Failed to search games on SteamGridDB');
  }
}

/**
 * Get grid artwork by game ID
 * @param gameId SteamGridDB game ID
 * @param options Grid options (styles, dimensions, etc.)
 */
export async function getSteamGridDBGrids(
  gameId: number, 
  options: {
    styles?: string[];
    dimensions?: string[];
    limit?: number;
  } = {}
) {
  try {
    const client = initClient();
    
    const gridOptions: any = {
      type: 'game',
      id: gameId
    };

    if (options.styles?.length) {
      gridOptions.styles = options.styles;
    }

    if (options.dimensions?.length) {
      gridOptions.dimensions = options.dimensions;
    }

    const grids = await client.getGrids(gridOptions);
    
    // Limit results if specified
    const limitedGrids = options.limit ? grids.slice(0, options.limit) : grids;
    
    return limitedGrids.map((grid: any) => ({
      id: grid.id,
      url: grid.url,
      thumb: grid.thumb,
      tags: grid.tags || [],
      style: grid.style,
      width: grid.width,
      height: grid.height,
      score: grid.score,
      author: {
        name: grid.author?.name || 'Unknown',
        steam64: grid.author?.steam64 || null,
        avatar: grid.author?.avatar || null
      },
      notes: grid.notes || null
    }));
  } catch (error) {
    console.error('Error fetching SteamGridDB grids:', error);
    throw new Error('Failed to fetch grids from SteamGridDB');
  }
}

/**
 * Get grid artwork by Steam App ID
 * @param steamAppId Steam application ID
 * @param options Grid options (styles, dimensions, etc.)
 */
export async function getSteamGridDBGridsBySteamId(
  steamAppId: number,
  options: {
    styles?: string[];
    dimensions?: string[];
    limit?: number;
  } = {}
) {
  try {
    const client = initClient();
    
    const gridOptions: any = {
      type: 'steam',
      id: steamAppId
    };

    if (options.styles?.length) {
      gridOptions.styles = options.styles;
    }

    if (options.dimensions?.length) {
      gridOptions.dimensions = options.dimensions;
    }

    const grids = await client.getGrids(gridOptions);
    
    // Limit results if specified
    const limitedGrids = options.limit ? grids.slice(0, options.limit) : grids;
    
    return limitedGrids.map((grid: any) => ({
      id: grid.id,
      url: grid.url,
      thumb: grid.thumb,
      tags: grid.tags || [],
      style: grid.style,
      width: grid.width,
      height: grid.height,
      score: grid.score,
      author: {
        name: grid.author?.name || 'Unknown',
        steam64: grid.author?.steam64 || null,
        avatar: grid.author?.avatar || null
      },
      notes: grid.notes || null
    }));
  } catch (error) {
    console.error('Error fetching SteamGridDB grids by Steam ID:', error);
    throw new Error('Failed to fetch grids from SteamGridDB');
  }
}

/**
 * Get heroes (wide artwork) by game ID
 * @param gameId SteamGridDB game ID
 * @param options Hero options
 */
export async function getSteamGridDBHeroes(
  gameId: number,
  options: {
    styles?: string[];
    limit?: number;
  } = {}
) {
  try {
    const client = initClient();
    
    const heroOptions: any = {
      type: 'game',
      id: gameId
    };

    if (options.styles?.length) {
      heroOptions.styles = options.styles;
    }

    const heroes = await client.getHeroes(heroOptions);
    
    // Limit results if specified
    const limitedHeroes = options.limit ? heroes.slice(0, options.limit) : heroes;
    
    return limitedHeroes.map((hero: any) => ({
      id: hero.id,
      url: hero.url,
      thumb: hero.thumb,
      tags: hero.tags || [],
      style: hero.style,
      width: hero.width,
      height: hero.height,
      score: hero.score,
      author: {
        name: hero.author?.name || 'Unknown',
        steam64: hero.author?.steam64 || null,
        avatar: hero.author?.avatar || null
      },
      notes: hero.notes || null
    }));
  } catch (error) {
    console.error('Error fetching SteamGridDB heroes:', error);
    throw new Error('Failed to fetch heroes from SteamGridDB');
  }
}

/**
 * Get logos by game ID
 * @param gameId SteamGridDB game ID
 * @param options Logo options
 */
export async function getSteamGridDBLogos(
  gameId: number,
  options: {
    styles?: string[];
    limit?: number;
  } = {}
) {
  try {
    const client = initClient();
    
    const logoOptions: any = {
      type: 'game',
      id: gameId
    };

    if (options.styles?.length) {
      logoOptions.styles = options.styles;
    }

    const logos = await client.getLogos(logoOptions);
    
    // Limit results if specified
    const limitedLogos = options.limit ? logos.slice(0, options.limit) : logos;
    
    return limitedLogos.map((logo: any) => ({
      id: logo.id,
      url: logo.url,
      thumb: logo.thumb,
      tags: logo.tags || [],
      style: logo.style,
      width: logo.width,
      height: logo.height,
      score: logo.score,
      author: {
        name: logo.author?.name || 'Unknown',
        steam64: logo.author?.steam64 || null,
        avatar: logo.author?.avatar || null
      },
      notes: logo.notes || null
    }));
  } catch (error) {
    console.error('Error fetching SteamGridDB logos:', error);
    throw new Error('Failed to fetch logos from SteamGridDB');
  }
}

/**
 * Get icons by game ID
 * @param gameId SteamGridDB game ID
 * @param options Icon options
 */
export async function getSteamGridDBIcons(
  gameId: number,
  options: {
    styles?: string[];
    dimensions?: string[];
    limit?: number;
  } = {}
) {
  try {
    const client = initClient();
    
    const iconOptions: any = {
      type: 'game',
      id: gameId
    };

    if (options.styles?.length) {
      iconOptions.styles = options.styles;
    }

    if (options.dimensions?.length) {
      iconOptions.dimensions = options.dimensions;
    }

    const icons = await client.getIcons(iconOptions);
    
    // Limit results if specified
    const limitedIcons = options.limit ? icons.slice(0, options.limit) : icons;
    
    return limitedIcons.map((icon: any) => ({
      id: icon.id,
      url: icon.url,
      thumb: icon.thumb,
      tags: icon.tags || [],
      style: icon.style,
      width: icon.width,
      height: icon.height,
      score: icon.score,
      author: {
        name: icon.author?.name || 'Unknown',
        steam64: icon.author?.steam64 || null,
        avatar: icon.author?.avatar || null
      },
      notes: icon.notes || null
    }));
  } catch (error) {
    console.error('Error fetching SteamGridDB icons:', error);
    throw new Error('Failed to fetch icons from SteamGridDB');
  }
}

/**
 * Available grid styles
 */
export const STEAMGRIDDB_STYLES = [
  'alternate',
  'blurred',
  'white_logo',
  'material',
  'no_logo'
] as const;

/**
 * Available grid dimensions
 */
export const STEAMGRIDDB_DIMENSIONS = [
  '460x215',
  '920x430',
  '600x900',
  '342x482',
  '660x930'
] as const;

export type SteamGridDBStyle = typeof STEAMGRIDDB_STYLES[number];
export type SteamGridDBDimension = typeof STEAMGRIDDB_DIMENSIONS[number];