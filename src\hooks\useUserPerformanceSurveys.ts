'use client';

import { useState, useEffect, useCallback } from 'react';
import { getUserPerformanceSurveys, deletePerformanceSurvey } from '@/lib/services/performanceSurveyService';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';

export interface UseUserPerformanceSurveysOptions {
  autoFetch?: boolean;
  sortBy?: 'created_at' | 'game_title' | 'device_type';
  sortOrder?: 'asc' | 'desc';
}

export interface UseUserPerformanceSurveysReturn {
  surveys: PerformanceSurveyRecord[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  deleteSurvey: (surveyId: string) => Promise<boolean>;
  totalCount: number;
  groupedByGame: Record<string, PerformanceSurveyRecord[]>;
  groupedByDevice: Record<string, PerformanceSurveyRecord[]>;
}

export function useUserPerformanceSurveys(
  userId: string | null,
  options: UseUserPerformanceSurveysOptions = {}
): UseUserPerformanceSurveysReturn {
  const {
    autoFetch = true,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = options;

  const [surveys, setSurveys] = useState<PerformanceSurveyRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSurveys = useCallback(async () => {
    if (!userId) {
      setSurveys([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await getUserPerformanceSurveys(userId);
      
      if (result.success && result.data) {
        // Sort the surveys based on options
        const sortedSurveys = [...result.data].sort((a, b) => {
          let aValue: any;
          let bValue: any;

          switch (sortBy) {
            case 'created_at':
              aValue = new Date(a.created_at || 0);
              bValue = new Date(b.created_at || 0);
              break;
            case 'game_title':
              aValue = (a.game_title || '').toLowerCase();
              bValue = (b.game_title || '').toLowerCase();
              break;
            case 'device_type':
              aValue = (a.device_type || '').toLowerCase();
              bValue = (b.device_type || '').toLowerCase();
              break;
            default:
              aValue = a.created_at || '';
              bValue = b.created_at || '';
          }

          if (sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });

        setSurveys(sortedSurveys);
      } else {
        setError(result.error || 'Failed to load performance surveys');
        setSurveys([]);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error fetching performance surveys:', errorMessage, err);
      setError('Failed to load performance surveys. Please try again.');
      setSurveys([]);
    } finally {
      setLoading(false);
    }
  }, [userId, sortBy, sortOrder]);

  const handleDeleteSurvey = useCallback(async (surveyId: string): Promise<boolean> => {
    if (!userId) return false;

    try {
      const result = await deletePerformanceSurvey(surveyId, userId);
      
      if (result.success) {
        // Remove the survey from local state
        setSurveys(prev => prev.filter(survey => survey.id !== surveyId));
        return true;
      } else {
        setError(result.error || 'Failed to delete survey');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error deleting performance survey:', errorMessage, err);
      setError('Failed to delete survey. Please try again.');
      return false;
    }
  }, [userId]);

  // Group surveys by game title
  const groupedByGame = useCallback(() => {
    return surveys.reduce((groups, survey) => {
      const gameTitle = survey.game_title || 'Unknown Game';
      if (!groups[gameTitle]) {
        groups[gameTitle] = [];
      }
      groups[gameTitle].push(survey);
      return groups;
    }, {} as Record<string, PerformanceSurveyRecord[]>);
  }, [surveys]);

  // Group surveys by device type
  const groupedByDevice = useCallback(() => {
    return surveys.reduce((groups, survey) => {
      const deviceType = survey.device_type || 'Unknown Device';
      if (!groups[deviceType]) {
        groups[deviceType] = [];
      }
      groups[deviceType].push(survey);
      return groups;
    }, {} as Record<string, PerformanceSurveyRecord[]>);
  }, [surveys]);

  useEffect(() => {
    if (autoFetch) {
      fetchSurveys();
    }
  }, [fetchSurveys, autoFetch]);

  return {
    surveys,
    loading,
    error,
    refetch: fetchSurveys,
    deleteSurvey: handleDeleteSurvey,
    totalCount: surveys.length,
    groupedByGame: groupedByGame(),
    groupedByDevice: groupedByDevice()
  };
}
