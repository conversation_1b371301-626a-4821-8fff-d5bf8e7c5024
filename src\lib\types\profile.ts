// Enhanced UserProfile interface for the profile system
export interface UserProfile {
  // Core identification
  id: string; // Supabase UUID, primary key
  username: string; // User-chosen unique username
  slug: string; // User-friendly URL slug
  slug_lower: string; // Lowercase version of slug for querying
  
  // Basic profile info
  display_name: string; // Display name, can be different from username
  email?: string | null; // User's email, optional
  avatar_url?: string | null; // URL for profile picture
  banner_url?: string | null; // URL for profile banner
  bio?: string | null; // User's biography
  
  // Contact & Social
  website?: string | null; // Personal website URL
  location?: string | null; // User's location
  
  // Gaming Preferences
  preferred_genres?: string[] | null; // Favorite game genres
  favorite_consoles?: string[] | null; // Favorite gaming consoles
  gaming_profiles?: GamingProfile[] | null; // External gaming platform profiles
  social_profiles?: SocialMediaProfile[] | null; // Social media profiles
  
  // Theme and Customization
  theme?: string; // Theme ID
  custom_colors?: CustomColors | null; // Custom color configuration
  
  // Account status & metadata
  is_admin?: boolean; // Admin privileges
  admin_level?: string; // Admin permission level (SUPER_ADMIN, ADMIN, MODERATOR, EDITOR, VIEWER)
  is_system_account?: boolean; // Whether this is a protected system account
  is_online?: boolean; // Online status
  last_seen?: string | null; // Last seen timestamp
  created_at?: string | null; // Account creation timestamp
  updated_at?: string | null; // Last profile update timestamp
  last_admin_action?: string | null; // Last admin action timestamp
  admin_notes?: string | null; // Admin notes about the user
  
  // User suspension system
  suspended?: boolean; // Whether the user is suspended
  suspension_reason?: string | null; // Reason for suspension
  suspended_at?: string | null; // When the suspension was applied
  suspended_by?: string | null; // ID of admin who applied suspension
  
  // Gamification & Social
  level?: number; // User level
  experience?: number; // Experience points
  review_count?: number; // Number of reviews written
  achievements?: Achievement[] | null; // User achievements
  
  // Privacy settings
  privacy_settings?: PrivacySettings | null;
  
  // Statistics (computed fields)
  user_stats?: UserStats | null;
}

// Unified profile interface for component compatibility
export interface UnifiedUserProfile extends UserProfile {
  // Legacy compatibility fields
  uid?: string;
  userName?: string;
  displayName?: string;
  avatarUrl?: string;
  bannerUrl?: string;
  photoURL?: string;
  preferredGenres?: string[];
  favoriteConsoles?: string[];
  gamingProfiles?: GamingProfile[];
  socialMedia?: SocialMediaProfile[];
  customColors?: CustomColors;
  adminLevel?: string; // Admin permission level for enhanced security
  
  // Legacy privacy settings format
  privacySettings?: {
    showOnlineStatus: boolean;
    showGamingProfiles: boolean;
    allowFriendRequests: boolean;
    showAchievements: boolean;
  };
  
  // Date objects for legacy compatibility
  lastSeen?: Date | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
}

// Privacy settings interface
export interface PrivacySettings {
  profile_visibility: 'public' | 'friends' | 'private';
  show_online_status: boolean;
  show_gaming_profiles: boolean;
  show_social_profiles: boolean;
  show_achievements: boolean;
  allow_contact: boolean;
  allow_friend_requests: boolean;
}

// User statistics interface
export interface UserStats {
  profile_views: number;
  total_reviews: number;
  total_likes_received: number;
  total_comments_received: number;
  average_review_score: number;
  followers_count: number;
  following_count: number;
  achievements_count: number;
}

// Gaming profile interface
export interface GamingProfile {
  platform: 'steam' | 'xbox' | 'playstation' | 'nintendo' | 'epic' | 'origin' | 'uplay';
  username: string;
  url?: string;
  verified?: boolean;
}

// Social media profile interface
export interface SocialMediaProfile {
  platform: 'twitter' | 'facebook' | 'instagram' | 'youtube' | 'twitch' | 'github' | 'linkedin' | 'discord' | 'reddit' | 'tiktok';
  username: string;
  url?: string;
  verified?: boolean;
}

// Achievement interface
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon_url?: string;
  achieved_at?: string;
  category: string;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
}

// Custom colors interface
export interface CustomColors {
  primary: string;
  secondary: string;
  accent: string;
}

// Profile update input interface (for forms)
export interface ProfileUpdateInput {
  display_name?: string;
  bio?: string;
  website?: string;
  location?: string;
  avatar_url?: string;
  banner_url?: string;
  preferred_genres?: string[];
  favorite_consoles?: string[];
  theme?: string;
  custom_colors?: CustomColors;
  privacy_settings?: PrivacySettings;
}

// Username change input interface
export interface UsernameChangeInput {
  username: string;
  current_password: string;
}

// Profile view permissions interface
export interface ProfileViewPermissions {
  canViewOnlineStatus: boolean;
  canViewGamingProfiles: boolean;
  canViewSocialProfiles: boolean;
  canViewAchievements: boolean;
  canViewContactInfo: boolean;
  canViewFullProfile: boolean;
  canViewStats: boolean;
  canSendFriendRequest: boolean;
  canContact: boolean;
}

// Profile creation input interface
export interface ProfileCreationInput {
  username: string;
  display_name: string;
  bio?: string;
  avatar_url?: string;
  preferred_genres?: string[];
  favorite_consoles?: string[];
  privacy_settings?: Partial<PrivacySettings>;
}

// Username suggestion interface
export interface UsernameSuggestion {
  username: string;
  available: boolean;
  similarity_score?: number;
}

// Profile search result interface
export interface ProfileSearchResult {
  profiles: UserProfile[];
  total_count: number;
  has_more: boolean;
  next_offset?: number;
}

// Profile analytics interface
export interface ProfileAnalytics {
  views_today: number;
  views_this_week: number;
  views_this_month: number;
  views_total: number;
  top_referrers: Array<{ source: string; count: number }>;
  geographic_data: Array<{ country: string; count: number }>;
  device_data: Array<{ device_type: string; count: number }>;
}

// Image upload result interface
export interface ImageUploadResult {
  success: boolean;
  url?: string;
  path?: string;
  error?: string;
  optimized_url?: string;
  thumbnail_url?: string;
}

// Signed upload URL result interface
export interface SignedUploadResult {
  success: boolean;
  signed_url?: string;
  path?: string;
  error?: string;
  expires_at?: string;
}

// Profile validation result interface
export interface ProfileValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings?: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

// Username availability result interface
export interface UsernameAvailabilityResult {
  available: boolean;
  suggestions?: UsernameSuggestion[];
  error?: string;
  reserved?: boolean;
}

// Profile operation result interface
export interface ProfileOperationResult<T = UserProfile> {
  success: boolean;
  data?: T;
  error?: string;
  validation_errors?: Array<{
    field: string;
    message: string;
  }>;
}

// Export commonly used type unions
export type ProfileVisibility = 'public' | 'friends' | 'private';
export type GamingPlatform = 'steam' | 'xbox' | 'playstation' | 'nintendo' | 'epic' | 'origin' | 'uplay';
export type AchievementRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
export type ImageType = 'avatar' | 'banner';
