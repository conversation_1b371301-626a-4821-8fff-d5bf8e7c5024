'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, MessageCircle, Clock, User, ArrowLeft, TrendingUp, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useForumPosts, useForumStats, useFilteredForumPosts, ForumSortBy } from '@/hooks/useForumPosts';
import { useAuthContext } from '@/contexts/auth-context';
import { ForumPostForm } from './ForumPostForm';
import { ForumPostList } from './ForumPostList';
import { ForumThread } from './ForumThread';
import { ForumFilters } from './ForumFilters';
import { BlockedUserModal } from '@/components/moderation/BlockedUserModal';
import { formatDistanceToNow } from 'date-fns';
import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';

interface ForumSystemProps {
  reviewId: string;
  reviewTitle: string;
  reviewAuthorId: string;
  onOpenSidebar?: () => void;
  highlightedCommentId?: string | null;
}

type ViewMode = 'list' | 'create' | 'thread';

export function ForumSystem({
  reviewId,
  reviewTitle,
  reviewAuthorId,
  onOpenSidebar,
  highlightedCommentId
}: ForumSystemProps) {
  const { user } = useAuthContext();
  const [currentView, setCurrentView] = useState<ViewMode>('list');
  const [selectedPostId, setSelectedPostId] = useState<string | null>(null);
  const [showBlockedModal, setShowBlockedModal] = useState(false);
  const [blockedByUserName, setBlockedByUserName] = useState('');

  // Filter state
  const [searchUsername, setSearchUsername] = useState('');
  const [sortBy, setSortBy] = useState<ForumSortBy>('recent');
  const [showMyActivity, setShowMyActivity] = useState(false);

  // Use filtered posts when filters are active, otherwise use regular posts
  const hasActiveFilters = searchUsername || sortBy !== 'recent' || showMyActivity;

  const { data: regularPosts, isLoading: regularPostsLoading, error: regularPostsError } = useForumPosts(reviewId);
  const { data: filteredPosts, isLoading: filteredPostsLoading, error: filteredPostsError } = useFilteredForumPosts(
    reviewId,
    hasActiveFilters ? { searchUsername, sortBy, showMyActivity } : {}
  );

  // Use filtered data when filters are active
  const posts = hasActiveFilters ? filteredPosts : regularPosts;
  const postsLoading = hasActiveFilters ? filteredPostsLoading : regularPostsLoading;
  const postsError = hasActiveFilters ? filteredPostsError : regularPostsError;

  const { data: stats, isLoading: statsLoading, error: statsError } = useForumStats(reviewId);

  // Auto-navigate to correct view when highlighting a comment
  React.useEffect(() => {
    if (highlightedCommentId && posts) {
      // Check if highlighted comment is a forum post (parent)
      const isMainPost = posts.some(post => post.id === highlightedCommentId);
      
      if (isMainPost) {
        // It's a main forum post, stay in list view
        setCurrentView('list');
        setSelectedPostId(null);
      } else {
        // It might be a reply, find the parent post
        const findParentPost = async () => {
          const supabase = createClient();
          const { data: comment } = await supabase
            .from('comments')
            .select('parent_id')
            .eq('id', highlightedCommentId)
            .single();
            
          if (comment?.parent_id) {
            // It's a reply, switch to thread view of the parent post
            setSelectedPostId(comment.parent_id);
            setCurrentView('thread');
          }
        };
        
        findParentPost();
      }
    }
  }, [highlightedCommentId, posts]);

  // Check if current user is blocked from commenting on this review
  const { data: blockingData } = useQuery({
    queryKey: ['user-block-check', reviewId, user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      
      const supabase = createClient();
      
      // Get review author info
      const { data: reviewData, error: reviewError } = await supabase
        .from('reviews')
        .select(`
          author_id,
          author:profiles!author_id(username, display_name)
        `)
        .eq('id', reviewId)
        .single();
        
      if (reviewError) throw new Error('Review not found');
      
      // Check if current user is blocked by the review author
      const { data: blockCheck, error: blockError } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewData.author_id)
        .eq('blocked_id', user.id)
        .maybeSingle();
        
      if (blockError) throw blockError;
      
      return {
        isBlocked: !!blockCheck,
        authorName: reviewData.author?.display_name || reviewData.author?.username || 'this user'
      };
    },
    enabled: !!user?.id && !!reviewId,
  });

  const handleCreateSuccess = (post: any) => {
    // Navigate to the newly created thread
    setSelectedPostId(post.id);
    setCurrentView('thread');
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedPostId(null);
  };

  const handleJoinDiscussion = (postId: string) => {
    setSelectedPostId(postId);
    setCurrentView('thread');
  };

  const handleNewPostClick = () => {
    // Check if user is blocked before allowing access to create form
    if (blockingData?.isBlocked) {
      setBlockedByUserName(blockingData.authorName);
      setShowBlockedModal(true);
      return;
    }

    setCurrentView('create');
  };

  // Filter handlers
  const handleSearchChange = (username: string) => {
    setSearchUsername(username);
  };

  const handleSortChange = (newSortBy: ForumSortBy) => {
    setSortBy(newSortBy);
  };

  const handleMyActivityToggle = (show: boolean) => {
    setShowMyActivity(show);
  };

  const handleClearFilters = () => {
    setSearchUsername('');
    setSortBy('recent');
    setShowMyActivity(false);
  };

  if (postsError || statsError) {
    return (
      <div className="w-full bg-slate-900 border border-slate-700 rounded-xl">
        <CardContent className="p-6">
          <div className="text-center text-slate-400">
            <MessageCircle className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Unable to load forum discussions. Please try again later.</p>
          </div>
        </CardContent>
      </div>
    );
  }

  return (
    <div className="w-full bg-slate-900 border border-slate-700 rounded-xl overflow-hidden shadow-lg">
      {/* Header */}
      <div className="bg-slate-800 border-b border-slate-700 p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {currentView !== 'list' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToList}
                className="text-slate-400 hover:text-white"
              >
                <ArrowLeft className="w-4 h-4" />
              </Button>
            )}
            <div className="space-y-1">
              <h1 className="text-lg font-mono text-white leading-tight">
                <span className="text-slate-400 mr-1">//</span>
                {currentView === 'list' && 'Discussion'}
                {currentView === 'create' && 'New Post'}
                {currentView === 'thread' && 'Thread'}
              </h1>
              {currentView === 'list' && (
                <p className="font-mono text-xs text-slate-400 leading-tight">
                  Community discussion for "{reviewTitle}"
                </p>
              )}
              {currentView === 'list' && (
                <div className="flex items-center gap-6 font-mono text-xs text-slate-400 pt-1">
                  {statsLoading ? (
                    <div className="flex gap-6">
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  ) : stats ? (
                    <>
                      <div className="flex items-center gap-2">
                        <MessageCircle className="h-3 w-3" />
                        <span>{stats.total_posts} posts</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-3 w-3" />
                        <span>{stats.active_users} users</span>
                      </div>
                      {stats.recent_activity && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>last {formatDistanceToNow(new Date(stats.recent_activity))} ago</span>
                        </div>
                      )}
                    </>
                  ) : null}
                </div>
              )}
            </div>
          </div>
          {currentView === 'list' && user && (
            <Button
              onClick={handleNewPostClick}
              className="bg-slate-700 hover:bg-slate-600 text-white transition-all duration-200 text-sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Post
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      {currentView === 'list' && (
        <ForumFilters
          searchUsername={searchUsername}
          sortBy={sortBy}
          showMyActivity={showMyActivity}
          onSearchChange={handleSearchChange}
          onSortChange={handleSortChange}
          onMyActivityToggle={handleMyActivityToggle}
          onClearFilters={handleClearFilters}
          currentUserId={user?.id}
        />
      )}

      {/* Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {currentView === 'list' && (
            <motion.div
              key="list"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            >
              {!user && (
                <Card className="bg-slate-800 border-slate-700 mb-6">
                  <CardContent className="p-6 text-center">
                    <p className="text-slate-400 mb-4">
                      Sign in to join the discussion and create posts
                    </p>
                    <Button
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                      onClick={onOpenSidebar}
                    >
                      Sign In
                    </Button>
                  </CardContent>
                </Card>
              )}
              
              <ForumPostList
                posts={posts || []}
                isLoading={postsLoading}
                onJoinDiscussion={handleJoinDiscussion}
                currentUserId={user?.id}
                reviewAuthorId={reviewAuthorId}
                highlightedCommentId={highlightedCommentId}
                hasActiveFilters={hasActiveFilters}
              />
            </motion.div>
          )}

          {currentView === 'create' && (
            <motion.div
              key="create"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            >
              <ForumPostForm
                reviewId={reviewId}
                onSuccess={handleCreateSuccess}
                onCancel={handleBackToList}
              />
            </motion.div>
          )}

          {currentView === 'thread' && selectedPostId && (
            <motion.div
              key="thread"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            >
              <ForumThread
                postId={selectedPostId}
                reviewId={reviewId}
                currentUserId={user?.id}
                reviewAuthorId={reviewAuthorId}
                highlightedCommentId={highlightedCommentId}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Blocked User Modal */}
      <BlockedUserModal
        isOpen={showBlockedModal}
        onClose={() => setShowBlockedModal(false)}
        contentOwnerName={blockedByUserName}
      />
    </div>
  );
}
