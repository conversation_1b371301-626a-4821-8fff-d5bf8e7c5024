import { MFASetup } from '@/components/admin/MFASetup';
import { AdminLayout } from '@/components/admin/AdminLayout';

export default function AdminMFAPage() {
  return (
    <AdminLayout
      title="Multi-Factor Authentication"
      description="Configure and manage two-factor authentication for administrative security"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Multi-Factor Authentication' }
      ]}
    >
      <div className="space-y-6">
        <MFASetup showTitle={false} />
      </div>
    </AdminLayout>
  );
} 