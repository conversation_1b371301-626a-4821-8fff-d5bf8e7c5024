-- =====================================================
-- CriticalPixel MFA Tables - Versão Simplificada
-- Execute este SQL no Supabase Dashboard > SQL Editor
-- =====================================================

-- 1. <PERSON><PERSON><PERSON> tabel<PERSON> de configurações MFA
CREATE TABLE IF NOT EXISTS user_mfa_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
  secret_encrypted TEXT NOT NULL,
  backup_codes_encrypted TEXT NOT NULL,
  recovery_phrase_encrypted TEXT,
  is_enabled BOOLEAN DEFAULT FALSE NOT NULL,
  admin_level VARCHAR(20) DEFAULT 'ADMIN',
  setup_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  disabled_at TIMESTAMP WITH TIME ZONE,
  disabled_reason TEXT,
  disabled_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  CONSTRAINT chk_admin_level CHECK (admin_level IN ('SUPER_ADMIN', 'ADMIN', 'MODERATOR', 'EDITOR', 'VIEWER'))
);

-- 2. Criar tabela de sessões de verificação MFA
CREATE TABLE IF NOT EXISTS mfa_verification_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  session_token TEXT NOT NULL UNIQUE,
  operation VARCHAR(50),
  verified BOOLEAN DEFAULT FALSE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE
);

-- 3. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_user_id ON user_mfa_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_enabled ON user_mfa_settings(is_enabled) WHERE is_enabled = TRUE;
CREATE INDEX IF NOT EXISTS idx_mfa_sessions_token ON mfa_verification_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_mfa_sessions_user_id ON mfa_verification_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_sessions_expires ON mfa_verification_sessions(expires_at);

-- 4. Habilitar RLS
ALTER TABLE user_mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_verification_sessions ENABLE ROW LEVEL SECURITY;

-- 5. Criar políticas RLS
CREATE POLICY "Users can manage their own MFA settings" ON user_mfa_settings
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Super admins can view all MFA settings" ON user_mfa_settings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.admin_level = 'SUPER_ADMIN'
      AND p.is_admin = TRUE
    )
  );

CREATE POLICY "Users can manage their own MFA sessions" ON mfa_verification_sessions
  FOR ALL USING (user_id = auth.uid());

-- 6. Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_mfa_settings_updated_at 
  BEFORE UPDATE ON user_mfa_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Função de estatísticas MFA
CREATE OR REPLACE FUNCTION get_mfa_statistics()
RETURNS TABLE (
  total_users_with_mfa INTEGER,
  enabled_mfa_users INTEGER,
  super_admin_mfa_enabled INTEGER,
  admin_mfa_enabled INTEGER,
  recent_mfa_usage INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE AND admin_level = 'SUPER_ADMIN'),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE AND admin_level = 'ADMIN'),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE last_used_at > NOW() - INTERVAL '7 days');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Função de limpeza de sessões expiradas
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM mfa_verification_sessions 
  WHERE expires_at < NOW() - INTERVAL '1 hour';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Comentários para documentação
COMMENT ON TABLE user_mfa_settings IS 'Stores encrypted MFA secrets and settings for admin users';
COMMENT ON TABLE mfa_verification_sessions IS 'Temporary sessions for MFA verification challenges';
COMMENT ON COLUMN user_mfa_settings.secret_encrypted IS 'AES-256 encrypted TOTP secret';
COMMENT ON COLUMN user_mfa_settings.backup_codes_encrypted IS 'AES-256 encrypted backup codes array';

-- Sucesso!
SELECT 'MFA tables created successfully! 🎉' as result; 