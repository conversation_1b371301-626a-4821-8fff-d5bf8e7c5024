// src/components/image-management/ImageLibrary.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { Search, Grid, List, Trash2, Download, Eye, Filter } from 'lucide-react';
import { createClient } from '@/lib/supabase/client';

interface UserImage {
  id: string;
  b2_url: string;
  b2_key: string;
  original_name: string;
  file_size: number;
  mime_type: string;
  variants: any;
  created_at: string;
  usage_context?: string;
  reference_id?: string;
}

interface ImageLibraryProps {
  onSelectImage?: (image: UserImage) => void;
  selectionMode?: boolean;
  className?: string;
}

export default function ImageLibrary({
  onSelectImage,
  selectionMode = false,
  className = '',
}: ImageLibraryProps) {
  const [images, setImages] = useState<UserImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterContext, setFilterContext] = useState<string>('all');
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

  const supabase = createClient();

  useEffect(() => {
    loadImages();
  }, [searchTerm, filterContext]);

  const loadImages = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('user_images')
        .select('*')
        .order('created_at', { ascending: false });

      if (searchTerm) {
        query = query.ilike('original_name', `%${searchTerm}%`);
      }

      if (filterContext !== 'all') {
        query = query.eq('usage_context', filterContext);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading images:', error);
        return;
      }

      setImages(data || []);
    } catch (error) {
      console.error('Failed to load images:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (image: UserImage) => {
    if (selectionMode) {
      const newSelected = new Set(selectedImages);
      if (newSelected.has(image.id)) {
        newSelected.delete(image.id);
      } else {
        newSelected.add(image.id);
      }
      setSelectedImages(newSelected);
    }
    
    onSelectImage?.(image);
  };

  const handleDeleteImage = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return;

    try {
      const { error } = await supabase
        .from('user_images')
        .delete()
        .eq('id', imageId);

      if (error) {
        console.error('Error deleting image:', error);
        return;
      }

      setImages(prev => prev.filter(img => img.id !== imageId));
    } catch (error) {
      console.error('Failed to delete image:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getImageUrl = (image: UserImage, variant: 'thumbnail' | 'medium' | 'original' = 'medium') => {
    // For now, return the original URL. In production, you might use CDN variants
    return image.b2_url;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className={`bg-slate-900 rounded-lg border border-slate-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-slate-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-medium">Image Library</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-purple-500 text-white' : 'text-slate-400 hover:text-white'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-purple-500 text-white' : 'text-slate-400 hover:text-white'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search images..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded text-white placeholder-slate-400 focus:outline-none focus:border-purple-400"
            />
          </div>
          <select
            value={filterContext}
            onChange={(e) => setFilterContext(e.target.value)}
            className="px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white focus:outline-none focus:border-purple-400"
          >
            <option value="all">All Images</option>
            <option value="review">Reviews</option>
            <option value="profile">Profile</option>
            <option value="comment">Comments</option>
          </select>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {images.length === 0 ? (
          <div className="text-center py-8 text-slate-400">
            <Eye className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>No images found</p>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image) => (
              <div
                key={image.id}
                className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                  selectedImages.has(image.id) 
                    ? 'border-purple-500' 
                    : 'border-slate-600 hover:border-slate-500'
                }`}
                onClick={() => handleImageSelect(image)}
              >
                <img
                  src={getImageUrl(image, 'thumbnail')}
                  alt={image.original_name}
                  className="w-full h-32 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 flex gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(image.b2_url, '_blank');
                      }}
                      className="p-2 bg-slate-800 rounded text-white hover:bg-slate-700"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteImage(image.id);
                      }}
                      className="p-2 bg-red-600 rounded text-white hover:bg-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 p-2">
                  <p className="text-white text-xs truncate">{image.original_name}</p>
                  <p className="text-slate-300 text-xs">{formatFileSize(image.file_size)}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {images.map((image) => (
              <div
                key={image.id}
                className={`flex items-center gap-4 p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedImages.has(image.id)
                    ? 'border-purple-500 bg-purple-500/10'
                    : 'border-slate-600 hover:border-slate-500 hover:bg-slate-800/50'
                }`}
                onClick={() => handleImageSelect(image)}
              >
                <img
                  src={getImageUrl(image, 'thumbnail')}
                  alt={image.original_name}
                  className="w-12 h-12 object-cover rounded"
                />
                <div className="flex-1">
                  <p className="text-white font-medium">{image.original_name}</p>
                  <p className="text-slate-400 text-sm">
                    {formatFileSize(image.file_size)} • {new Date(image.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(image.b2_url, '_blank');
                    }}
                    className="p-2 text-slate-400 hover:text-white"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteImage(image.id);
                    }}
                    className="p-2 text-slate-400 hover:text-red-400"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
