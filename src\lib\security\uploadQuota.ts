// src/lib/security/uploadQuota.ts
export interface QuotaLimits {
  maxFilesPerDay: number;
  maxTotalSize: number; // in bytes
  maxFileSize: number; // in bytes
  allowedFormats: string[];
}

export interface QuotaUsage {
  filesUploadedToday: number;
  totalSizeUsed: number;
  remainingFiles: number;
  remainingSize: number;
}

/**
 * User quota management
 */
export class UserQuotaManager {
  private static readonly DEFAULT_LIMITS: QuotaLimits = {
    maxFilesPerDay: 50,
    maxTotalSize: 100 * 1024 * 1024, // 100MB
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFormats: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  };

  private static readonly PREMIUM_LIMITS: QuotaLimits = {
    maxFilesPerDay: 200,
    maxTotalSize: 1024 * 1024 * 1024, // 1GB
    maxFileSize: 25 * 1024 * 1024, // 25MB
    allowedFormats: ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/bmp', 'image/tiff'],
  };

  static getLimits(isPremium: boolean): QuotaLimits {
    return isPremium ? this.PREMIUM_LIMITS : this.DEFAULT_LIMITS;
  }

  static async checkQuota(
    userId: string,
    fileSize: number,
    isPremium: boolean,
    supabase: any
  ): Promise<{ allowed: boolean; reason?: string; usage?: QuotaUsage }> {
    const limits = this.getLimits(isPremium);

    // Check file size limit
    if (fileSize > limits.maxFileSize) {
      return {
        allowed: false,
        reason: `File size exceeds limit of ${limits.maxFileSize / 1024 / 1024}MB`,
      };
    }

    try {
      // Get today's uploads
      const today = new Date().toISOString().split('T')[0];
      const { data: todayUploads, error: todayError } = await supabase
        .from('user_images')
        .select('file_size')
        .eq('user_id', userId)
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lte('created_at', `${today}T23:59:59.999Z`);

      if (todayError) {
        console.error('Quota check error:', todayError);
        return { allowed: true }; // Allow on error, log for investigation
      }

      const filesUploadedToday = todayUploads?.length || 0;
      const totalSizeToday = todayUploads?.reduce((sum, img) => sum + (img.file_size || 0), 0) || 0;

      // Check daily file limit
      if (filesUploadedToday >= limits.maxFilesPerDay) {
        return {
          allowed: false,
          reason: `Daily file limit of ${limits.maxFilesPerDay} reached`,
          usage: {
            filesUploadedToday,
            totalSizeUsed: totalSizeToday,
            remainingFiles: 0,
            remainingSize: Math.max(0, limits.maxTotalSize - totalSizeToday),
          },
        };
      }

      // Check total size limit
      if (totalSizeToday + fileSize > limits.maxTotalSize) {
        return {
          allowed: false,
          reason: `Upload would exceed daily size limit of ${limits.maxTotalSize / 1024 / 1024}MB`,
          usage: {
            filesUploadedToday,
            totalSizeUsed: totalSizeToday,
            remainingFiles: limits.maxFilesPerDay - filesUploadedToday,
            remainingSize: Math.max(0, limits.maxTotalSize - totalSizeToday),
          },
        };
      }

      return {
        allowed: true,
        usage: {
          filesUploadedToday,
          totalSizeUsed: totalSizeToday,
          remainingFiles: limits.maxFilesPerDay - filesUploadedToday - 1,
          remainingSize: limits.maxTotalSize - totalSizeToday - fileSize,
        },
      };
    } catch (error) {
      console.error('Quota check failed:', error);
      return { allowed: true }; // Allow on error, but log for investigation
    }
  }
}
