import React from 'react';
import type { Review } from '@/lib/types';

interface SimpleBannerProps {
  review?: Review;
  children?: React.ReactNode;
}

const SimpleBanner: React.FC<SimpleBannerProps> = ({ review, children }) => {
  if (!review) return null;
  
  // Get banner image from review
  const bannerImage = review.mainImageUrl || review.igdbCoverUrl;
  
  return (
    <div className="relative w-full h-[50vh] min-h-[400px] max-h-[600px] overflow-hidden">
      {/* Background Image with Gradient Overlay */}
      <div className="absolute inset-0 bg-slate-900">
        {bannerImage && (
          <img
            src={bannerImage}
            alt={review.gameName || 'Game banner'}
            className="w-full h-full object-cover opacity-40"
            style={{ objectPosition: 'center 20%' }}
          />
        )}
        {/* Gradients for better text legibility */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/80 via-slate-900/40 to-slate-900/90" />
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/60 to-slate-900/40" />
      </div>

      {/* Content Container */}
      <div className="absolute inset-0 flex flex-col justify-end">
        <div className="pb-12">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SimpleBanner;
