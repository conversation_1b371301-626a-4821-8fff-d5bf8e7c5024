/* src/components/style/editProfileModal.css */

:root {
    /* Elegant dark theme - matching auth modal */
    --edit-bg-overlay: rgba(16, 18, 27, 0.85);
    --edit-bg-primary: rgba(30, 33, 45, 0.96);
    --edit-bg-secondary: rgba(24, 27, 36, 0.92);
    --edit-bg-tertiary: rgba(20, 22, 32, 0.4);
    
    /* Code/Gamer accent colors */
    --edit-accent-primary: #8b5cf6;
    --edit-accent-secondary: #a855f7;
    --edit-accent-muted: rgba(139, 92, 246, 0.4);
    --edit-accent-success: #10b981;
    --edit-accent-warning: #f59e0b;
    --edit-accent-error: #ef4444;
    
    /* Refined border colors */
    --edit-border-primary: rgba(71, 85, 105, 0.3);
    --edit-border-secondary: rgba(71, 85, 105, 0.2);
    --edit-border-focus: rgba(139, 92, 246, 0.5);
    --edit-border-dashed: rgba(71, 85, 105, 0.4);
    
    /* Clean text colors hierarchy */
    --edit-text-primary: #f1f5f9;
    --edit-text-secondary: #94a3b8;
    --edit-text-muted: #64748b;
    --edit-text-placeholder: rgba(148, 163, 184, 0.4);
    
    /* Enhanced gradients */
    --edit-button-gradient: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(168, 85, 247, 0.8) 100%);
    --edit-button-gradient-hover: linear-gradient(135deg, rgba(139, 92, 246, 1) 0%, rgba(168, 85, 247, 0.95) 100%);
    --edit-button-success-gradient: linear-gradient(135deg, rgba(16, 185, 129, 0.9) 0%, rgba(5, 150, 105, 0.8) 100%);
    --edit-button-success-gradient-hover: linear-gradient(135deg, rgba(16, 185, 129, 1) 0%, rgba(5, 150, 105, 0.95) 100%);
    
    /* Input styling */
    --edit-input-bg: rgba(30, 32, 44, 0.8);
    --edit-input-bg-focus: rgba(35, 38, 52, 0.9);
    
    /* Fonts - Code/Gamer aesthetic */
    --edit-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --edit-font-mono: 'Fira Code', 'SF Mono', 'Monaco', 'Courier New', monospace;
    
    --edit-disabled-opacity: 0.5;
  }
  
  /* Custom Scrollbar */
  .edit-modal-scrollbar::-webkit-scrollbar {
    width: 10px;
  }
  
  .edit-modal-scrollbar::-webkit-scrollbar-track {
    background: rgba(15, 15, 15, 0.4);
    border-radius: 8px;
    backdrop-filter: blur(4px);
  }
  
  .edit-modal-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(55, 65, 81, 0.8), rgba(31, 41, 55, 0.9));
    border-radius: 8px;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  
  .edit-modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(75, 85, 99, 0.9), rgba(55, 65, 81, 0.9));
  }
  
  /* Modal Container */
  .edit-modal-overlay {
    position: fixed;
    inset: 0;
    background-color: var(--edit-bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(8px);
    padding: 1.5rem;
    overflow: hidden; /* Prevent overlay from scrolling */
  }
  
  .edit-modal-container {
    background: linear-gradient(145deg, rgba(28, 31, 43, 0.97) 0%, rgba(22, 25, 35, 0.95) 100%);
    border: 1px solid var(--edit-border-primary);
    border-radius: 16px;
    box-shadow: 
      0 25px 80px rgba(0, 0, 0, 0.6), 
      0 0 0 1px rgba(139, 92, 246, 0.1) inset;
    width: 100%;
    max-width: 900px;
    max-height: 65vh;
    position: relative;
    overflow: hidden;
    color: var(--edit-text-primary);
    font-family: var(--edit-font-primary);
    display: flex;
    flex-direction: column;
  }
  
  /* Header */
  .edit-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px 20px;
    border-bottom: 1px solid var(--edit-border-secondary);
    background: var(--edit-bg-tertiary);
    backdrop-filter: blur(10px);
  }

  .edit-modal-header-content {
    flex: 1;
  }

  .edit-modal-subtitle {
    color: var(--edit-text-secondary);
    font-size: 0.9rem;
    margin-top: 4px;
    opacity: 0.9;
  }

  .edit-mobile-menu-button {
    display: none;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.4);
    color: var(--edit-text-secondary);
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .edit-mobile-menu-button:hover {
    background: rgba(139, 92, 246, 0.15);
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.4);
    transform: scale(1.05);
  }

  .edit-modal-close {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.4);
    color: var(--edit-text-muted);
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .edit-modal-close:hover {
    background: rgba(239, 68, 68, 0.15);
    color: var(--edit-accent-error);
    border-color: rgba(239, 68, 68, 0.4);
    transform: scale(1.05);
  }

  .edit-modal-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .edit-modal-title {
    font-family: var(--edit-font-mono);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--edit-text-primary);
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .edit-modal-title-comment {
    color: var(--edit-accent-primary);
    opacity: 0.8;
    font-weight: 600;
    margin-right: 8px;
  }
  
  /* Modal Body Layout */
  .edit-modal-body {
    display: flex;
    flex: 1;
    overflow: hidden;
    min-height: 0; /* Allows flex child to shrink */
  }

  /* Sidebar Navigation */
  .edit-sidebar {
    width: 240px;
    background: linear-gradient(to bottom, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
    border-right: 1px solid var(--edit-border-secondary);
    backdrop-filter: blur(12px);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
  }

  .edit-sidebar-nav {
    padding: 16px 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
  }

  .edit-sidebar-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    border-radius: 10px;
    background: transparent;
    border: 1px solid transparent;
    color: var(--edit-text-secondary);
    font-family: var(--edit-font-mono);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    width: 100%;
  }

  .edit-sidebar-button:hover:not(.active) {
    background: rgba(139, 92, 246, 0.1);
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.2);
    transform: translateX(4px);
  }

  .edit-sidebar-button.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.1));
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  }

  .edit-sidebar-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;
  }

  .edit-sidebar-label {
    flex: 1;
    color: inherit;
  }

  /* Mobile Navigation */
  .edit-mobile-nav {
    background: linear-gradient(to right, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
    border-bottom: 1px solid var(--edit-border-secondary);
    backdrop-filter: blur(12px);
  }

  .edit-mobile-nav-container {
    display: flex;
    padding: 16px;
    gap: 8px;
    overflow-x: auto;
  }

  .edit-mobile-nav-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 12px;
    background: transparent;
    border: 1px solid transparent;
    color: var(--edit-text-secondary);
    font-family: var(--edit-font-mono);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 80px;
    flex-shrink: 0;
  }

  .edit-mobile-nav-button:hover:not(.active) {
    background: rgba(139, 92, 246, 0.1);
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.2);
  }

  .edit-mobile-nav-button.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.1));
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  }

  .edit-mobile-nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;
  }

  .edit-mobile-nav-label {
    color: inherit;
    white-space: nowrap;
  }

  /* Content Area */
  .edit-modal-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 32px;
    min-height: 0; /* Allows flex child to shrink */
    max-height: 100%; /* Ensure content doesn't exceed container */
  }
  
  /* Step Indicator */
  .edit-step-container {
    margin-bottom: 24px;
    padding-top: 24px;
  }
  
  .edit-step-indicator {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-bottom: 20px;
  }
  
  .edit-step-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-width: 0;
    flex: 1;
    max-width: 200px;
    backdrop-filter: blur(8px);
    border: 1px solid transparent;
    cursor: pointer;
    touch-action: manipulation;
    font-family: var(--edit-font-primary);
  }
  
  .edit-step-button.active {
    background: var(--edit-button-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
    border-color: rgba(139, 92, 246, 0.3);
  }
  
  .edit-step-button.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--edit-accent-success);
    border-color: rgba(16, 185, 129, 0.2);
  }
  
  .edit-step-button.inactive {
    background: rgba(30, 32, 44, 0.6);
    color: var(--edit-text-muted);
    border-color: var(--edit-border-secondary);
  }
  
  .edit-step-button:hover:not(.active) {
    background: rgba(35, 38, 52, 0.8);
    color: var(--edit-text-secondary);
    border-color: var(--edit-border-primary);
  }
  
  .edit-step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-weight: 600;
    font-family: var(--edit-font-mono);
  }
  
  .edit-step-title {
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    font-family: var(--edit-font-mono);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* Progress Bar */
  .edit-progress-container {
    width: 100%;
    background: rgba(30, 32, 44, 0.6);
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    backdrop-filter: blur(4px);
  }
  
  .edit-progress-bar {
    background: var(--edit-button-gradient);
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.4);
  }
  
  /* Section Content */
  .edit-section-content {
    padding: 0 0 40px;
    animation: editFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .edit-section-header {
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--edit-border-secondary);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .edit-section-header-content {
    flex: 1;
  }

  .edit-section-header h3 {
    font-family: var(--edit-font-mono);
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--edit-text-primary);
    margin-bottom: 8px;
    letter-spacing: 0.3px;
  }

  .edit-section-header p {
    color: var(--edit-text-secondary);
    font-size: 0.95rem;
    opacity: 0.9;
  }

  .edit-section-close {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(71, 85, 105, 0.3);
    color: var(--edit-text-muted);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    margin-left: 16px;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .edit-section-close:hover {
    background: rgba(239, 68, 68, 0.15);
    color: var(--edit-accent-error);
    border-color: rgba(239, 68, 68, 0.4);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }
  
  /* Form Elements */
  .edit-form-group {
    margin-bottom: 24px;
  }
  
  .edit-form-label {
    font-family: var(--edit-font-mono);
    font-size: 0.85rem;
    color: var(--edit-text-secondary);
    letter-spacing: 0.05em;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .edit-form-label.primary {
    font-size: 1rem;
    color: var(--edit-text-primary);
    font-weight: 600;
  }
  
  .edit-input {
    background-color: var(--edit-input-bg);
    border: 1px solid var(--edit-border-secondary);
    border-radius: 10px;
    padding: 14px 16px;
    color: var(--edit-text-primary);
    font-size: 0.95rem;
    width: 100%;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) inset;
    font-family: var(--edit-font-primary);
  }
  
  .edit-input::placeholder {
    color: var(--edit-text-placeholder);
    font-style: normal;
    opacity: 0.8;
  }
  
  .edit-input:focus {
    outline: none;
    border-color: var(--edit-border-focus);
    background-color: var(--edit-input-bg-focus);
    box-shadow: 
      0 0 0 2px rgba(139, 92, 246, 0.2), 
      0 2px 4px rgba(0,0,0,0.2) inset;
  }
  
  .edit-textarea {
    min-height: 128px;
    resize: vertical;
  }
  
  .edit-select {
    background-color: var(--edit-input-bg);
    border: 1px solid var(--edit-border-secondary);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--edit-text-primary);
    font-size: 0.9rem;
    width: 100%;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
  }
  
  .edit-select:focus {
    outline: none;
    border-color: var(--edit-border-focus);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }
  
  /* Buttons */
  .edit-button {
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    letter-spacing: 0.03em;
    font-family: var(--edit-font-mono);
    backdrop-filter: blur(8px);
  }
  
  .edit-button.primary {
    background: var(--edit-button-gradient);
    color: white;
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
  }
  
  .edit-button.primary:hover:not(:disabled) {
    background: var(--edit-button-gradient-hover);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
  }
  
  .edit-button.success {
    background: var(--edit-button-success-gradient);
    color: white;
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
  }
  
  .edit-button.success:hover:not(:disabled) {
    background: var(--edit-button-success-gradient-hover);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
  }
  
  .edit-button.secondary {
    background: rgba(30, 32, 44, 0.8);
    color: var(--edit-text-secondary);
    border-color: var(--edit-border-secondary);
  }
  
  .edit-button.secondary:hover:not(:disabled) {
    background: rgba(35, 38, 52, 0.9);
    color: var(--edit-text-primary);
    border-color: var(--edit-border-primary);
  }
  
  .edit-button.outline {
    background: transparent;
    color: var(--edit-text-secondary);
    border-color: var(--edit-border-primary);
  }
  
  .edit-button.outline:hover:not(:disabled) {
    background: rgba(35, 38, 52, 0.6);
    color: var(--edit-text-primary);
    border-color: var(--edit-accent-muted);
  }
  
  .edit-button.ghost {
    background: transparent;
    color: var(--edit-text-muted);
    border: none;
  }
  
  .edit-button.ghost:hover:not(:disabled) {
    background: rgba(35, 38, 52, 0.6);
    color: var(--edit-text-secondary);
  }
  
  .edit-button:disabled {
    opacity: var(--edit-disabled-opacity);
    cursor: not-allowed;
    background: rgba(30, 32, 44, 0.4);
    color: var(--edit-text-muted);
    border-color: var(--edit-border-secondary);
    box-shadow: none;
  }
  
  /* Badge System */
  .edit-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 14px;
    background: rgba(30, 32, 44, 0.8);
    border: 1px solid var(--edit-border-secondary);
    border-radius: 8px;
    color: var(--edit-text-primary);
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
  }
  
  .edit-badge:hover {
    background: rgba(35, 38, 52, 0.9);
    border-color: var(--edit-border-primary);
  }
  
  .edit-badge-remove {
    color: var(--edit-text-muted);
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
  }
  
  .edit-badge-remove:hover {
    color: var(--edit-accent-error);
    opacity: 1;
  }
  
  /* Profile Cards */
  .edit-profile-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--edit-input-bg);
    border: 1px solid var(--edit-border-secondary);
    border-radius: 12px;
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
    margin-bottom: 12px;
  }
  
  .edit-profile-card:hover {
    border-color: var(--edit-border-primary);
    background: var(--edit-input-bg-focus);
  }
  
  .edit-profile-card-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .edit-profile-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(30, 32, 44, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--edit-text-secondary);
    backdrop-filter: blur(4px);
  }
  
  .edit-profile-card-info h4 {
    font-weight: 600;
    color: var(--edit-text-primary);
    margin-bottom: 2px;
  }
  
  .edit-profile-card-info p {
    color: var(--edit-text-muted);
    font-size: 0.8rem;
    font-family: var(--edit-font-mono);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .edit-profile-card-remove {
    opacity: 0;
    transition: all 0.2s ease;
    color: var(--edit-text-muted);
  }
  
  .edit-profile-card:hover .edit-profile-card-remove {
    opacity: 1;
  }
  
  .edit-profile-card-remove:hover {
    color: var(--edit-accent-error);
    background: rgba(239, 68, 68, 0.1);
  }
  
  /* Add Profile Section */
  .edit-add-profile-section {
    padding: 20px;
    border: 2px dashed var(--edit-border-dashed);
    border-radius: 12px;
    background: rgba(30, 32, 44, 0.3);
    backdrop-filter: blur(8px);
    margin-top: 16px;
  }
  
  .edit-add-profile-title {
    font-family: var(--edit-font-mono);
    font-weight: 600;
    color: var(--edit-text-primary);
    margin-bottom: 16px;
    letter-spacing: 0.05em;
  }
  
  /* Image Customization */
  .edit-image-customization {
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid var(--edit-border-secondary);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
  }
  
  .edit-image-customization:hover {
    border-color: var(--edit-border-primary);
  }
  
  .edit-image-tabs {
    display: flex;
    background: rgba(30, 32, 44, 0.8);
    border-radius: 8px;
    padding: 4px;
    backdrop-filter: blur(8px);
  }
  
  .edit-image-tab {
    flex: 1;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--edit-text-muted);
    font-family: var(--edit-font-mono);
    letter-spacing: 0.05em;
  }
  
  .edit-image-tab.active {
    background: var(--edit-button-gradient);
    color: white;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.25);
  }
  
  .edit-image-tab:hover:not(.active) {
    color: var(--edit-text-secondary);
    background: rgba(35, 38, 52, 0.6);
  }

  /* Vertical Image Tabs */
  .edit-image-tabs-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }

  .edit-image-tab-vertical {
    padding: 8px 16px;
    border-radius: 8px;
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid var(--edit-border-secondary);
    color: var(--edit-text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--edit-font-mono);
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-align: center;
  }

  .edit-image-tab-vertical.active {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.1));
    color: var(--edit-text-primary);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
  }

  .edit-image-tab-vertical:hover:not(.active) {
    background: rgba(139, 92, 246, 0.1);
    color: var(--edit-text-primary);
  }
  
  .edit-image-preview {
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(71, 85, 105, 0.2);
    transition: all 0.2s ease;
    margin-bottom: 16px;
  }
  
  .edit-image-preview:hover {
    border-color: var(--edit-border-primary);
  }
  
    .edit-image-preview.profile {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto;
  }

  .edit-image-preview.banner {
    width: 100%;
    height: 96px;
    border-radius: 8px;
  }
  
  .edit-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .edit-image-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    opacity: 0;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
  }
  
  .edit-image-preview:hover .edit-image-overlay {
    opacity: 1;
  }
  
  .edit-image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: rgba(30, 32, 44, 0.4);
    color: var(--edit-text-muted);
    border: 2px dashed var(--edit-border-secondary);
    backdrop-filter: blur(4px);
  }
  
    .edit-image-placeholder.profile {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto;
  }

  .edit-image-placeholder.banner {
    width: 100%;
    height: 96px;
    border-radius: 8px;
  }
  
  .edit-image-url-input {
    position: relative;
  }
  
  .edit-image-url-input .edit-input {
    padding-right: 40px;
  }
  
  .edit-image-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--edit-text-muted);
  }
  
  .edit-image-hint {
    font-size: 0.75rem;
    color: var(--edit-text-muted);
    text-align: center;
    margin-top: 8px;
    font-family: var(--edit-font-mono);
  }
  

  
  /* Confirmation Dialog */
  .edit-confirmation-dialog {
    background: var(--edit-bg-primary);
    border: 1px solid var(--edit-border-primary);
    color: var(--edit-text-primary);
  }
  
  .edit-confirmation-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--edit-accent-warning);
    font-family: var(--edit-font-mono);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .edit-confirmation-content {
    color: var(--edit-text-secondary);
    padding: 16px 0;
  }
  
  /* Empty States */
  .edit-empty-state {
    text-align: center;
    padding: 24px;
    color: var(--edit-text-muted);
    font-size: 0.9rem;
    font-family: var(--edit-font-mono);
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .edit-modal-overlay {
      padding: 1rem;
    }

    .edit-modal-container {
      max-width: 100%;
      max-height: 80vh;
      margin: 0;
    }

    .edit-modal-header {
      padding: 20px 24px 16px;
    }

    .edit-mobile-menu-button {
      display: block;
    }

    .edit-modal-body {
      flex-direction: column;
    }

    .edit-sidebar {
      display: none;
    }

    .edit-modal-content {
      padding: 24px;
    }



    .edit-section-content {
      padding: 0 0 20px;
    }

    .edit-section-header {
      margin-bottom: 24px;
    }

    .edit-section-header h3 {
      font-size: 1.2rem;
    }

    .edit-form-group {
      margin-bottom: 20px;
    }

    .edit-image-preview.profile {
      width: 120px;
      height: 120px;
    }

    .edit-image-placeholder.profile {
      width: 120px;
      height: 120px;
    }

    .edit-image-preview.banner {
      height: 100px;
    }

    .edit-image-placeholder.banner {
      height: 100px;
    }

    /* Image upload buttons responsive */
    .edit-image-upload-button {
      flex-direction: column;
      gap: 8px;
      padding: 12px;
      font-size: 0.85rem;
    }

    .edit-image-upload-button .edit-upload-icon {
      width: 20px;
      height: 20px;
    }

    /* Image containers responsive */
    .edit-image-container {
      flex-direction: column;
      gap: 16px;
    }

    .edit-image-section {
      width: 100%;
    }

    .edit-image-section.profile {
      align-items: center;
    }

    .edit-image-section.banner {
      align-items: stretch;
    }
  }

  @media (max-width: 480px) {
    .edit-modal-header {
      padding: 16px 20px 12px;
    }

    .edit-modal-content {
      padding: 20px;
    }



    .edit-section-header h3 {
      font-size: 1.1rem;
    }

    .edit-button {
      padding: 10px 16px;
      font-size: 0.85rem;
    }

    .edit-mobile-nav-button {
      min-width: 70px;
      padding: 10px 12px;
      font-size: 0.7rem;
    }

    .edit-mobile-nav-container {
      padding: 12px;
      gap: 6px;
    }
  }
  
  /* Animation Utilities */
  .edit-fade-in {
    animation: editFadeIn 0.3s ease-out;
  }
  
  .edit-slide-up {
    animation: editSlideUp 0.3s ease-out;
  }
  
  @keyframes editFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes editSlideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Section Subtitle Styling */
  .edit-section-subtitle {
    font-family: var(--edit-font-mono);
    font-size: 0.8rem;
    color: var(--edit-text-muted);
    margin-top: 2px;
    margin-bottom: 0;
    line-height: 1.4;
  }

  /* Emoji Widget Styles */
  .edit-emoji-widget-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1200;
    backdrop-filter: blur(4px);
  }

  .edit-emoji-widget {
    background: linear-gradient(145deg, rgba(28, 31, 43, 0.97) 0%, rgba(22, 25, 35, 0.95) 100%);
    border: 1px solid var(--edit-border-primary);
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
    width: 90%;
    max-width: 320px;
    max-height: 400px;
    color: var(--edit-text-primary);
    font-family: var(--edit-font-primary);
    display: flex;
    flex-direction: column;
  }

  .edit-emoji-widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--edit-border-secondary);
    background: var(--edit-bg-tertiary);
  }

  .edit-emoji-widget-title {
    font-family: var(--edit-font-mono);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--edit-text-primary);
  }

  .edit-emoji-widget-close {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.4);
    color: var(--edit-text-muted);
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .edit-emoji-widget-close:hover {
    background: rgba(239, 68, 68, 0.15);
    color: var(--edit-accent-error);
    border-color: rgba(239, 68, 68, 0.4);
  }

  .edit-emoji-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 4px;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
  }

  .edit-emoji-button {
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid transparent;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    aspect-ratio: 1;
  }

  .edit-emoji-button:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
    transform: scale(1.1);
  }

  .edit-emoji-button-trigger {
    background: rgba(30, 32, 44, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.4);
    color: var(--edit-text-secondary);
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
  }

  .edit-emoji-button-trigger:hover {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.4);
    transform: scale(1.05);
  }

  /* Actions area at bottom of sidebar */
  .edit-sidebar-actions {
    margin-top: auto;
    border-top: 1px solid var(--edit-border-secondary);
    padding-top: 12px;
  }

  /* Action buttons aligned to bottom of sidebar navigation */
  .edit-sidebar-button.action {
    padding: 8px 16px;
    font-size: 0.8rem;
    min-height: auto;
  }

  .edit-sidebar-button.action.save {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
    border-color: rgba(16, 185, 129, 0.2);
    color: var(--edit-accent-success);
    margin-top: 4px;
  }

  .edit-sidebar-button.action.save:hover:not(.disabled) {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
    border-color: rgba(16, 185, 129, 0.3);
  }

  .edit-sidebar-button.action.save.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .edit-sidebar-nav-unsaved {
    margin: 0 16px 8px;
    padding: 6px 10px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
  }

  .edit-unsaved-text.nav {
    display: flex;
    align-items: center;
    gap: 4px;
    font-family: var(--edit-font-mono);
    font-size: 0.7rem;
    color: var(--edit-accent-error);
    font-weight: 600;
    letter-spacing: 0.5px;
    margin: 0;
    text-align: center;
    justify-content: center;
  }

  /* Mobile action buttons integrated into mobile navigation */
  .edit-mobile-nav-button.action {
    background: rgba(30, 32, 44, 0.8);
    border-color: rgba(71, 85, 105, 0.4);
    padding: 8px 12px;
    min-width: 60px;
    font-size: 0.7rem;
  }

  .edit-mobile-nav-button.action.save {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05));
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--edit-accent-success);
  }

  .edit-mobile-nav-button.action.save:hover:not(.disabled) {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
  }

  .edit-mobile-nav-button.action.save.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .edit-mobile-nav-button.unsaved {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: var(--edit-accent-error);
    cursor: default;
    padding: 8px 10px;
    min-width: 60px;
    font-size: 0.65rem;
  }

  /* Image Upload Styles */
  .edit-image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .edit-image-upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: var(--edit-button-gradient);
    border: 1px solid var(--edit-border-primary);
    border-radius: 8px;
    color: white;
    font-family: var(--edit-font-mono);
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
  }

  .edit-image-upload-button:hover {
    background: var(--edit-button-gradient-hover);
    border-color: var(--edit-border-accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(139, 92, 246, 0.25);
  }

  .edit-image-upload-button.uploading {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .edit-image-upload-button.uploading:hover {
    transform: none;
    box-shadow: none;
  }