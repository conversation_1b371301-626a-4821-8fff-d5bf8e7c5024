/**
 * IGDB API utility functions for authentication and data fetching
 */

// IGDB API credentials - you'll need to obtain these from Twitch Developer Portal
const IGDB_CLIENT_ID = process.env.IGDB_CLIENT_ID;
const IGDB_CLIENT_SECRET = process.env.IGDB_CLIENT_SECRET;

// Cache for the access token to avoid making too many auth requests
let accessToken: string | null = null;
let tokenExpiry: number = 0;

/**
 * Get an access token for the IGDB API
 */
async function getAccessToken(): Promise<string> {
  // Check if token is still valid
  if (accessToken && tokenExpiry > Date.now()) {
    console.log('[IGDB API] Using cached access token.');
    return accessToken;
  }
  console.log('[IGDB API] Attempting to get new access token.');

  if (!IGDB_CLIENT_ID) {
    console.error('[IGDB API] IGDB_CLIENT_ID is not set in environment variables.');
    throw new Error('IGDB_CLIENT_ID not configured');
  } else {
    console.log('[IGDB API] IGDB_CLIENT_ID found.');
  }
  if (!IGDB_CLIENT_SECRET) {
    // Not logging the secret itself, just its presence
    console.error('[IGDB API] IGDB_CLIENT_SECRET is not set in environment variables.');
    throw new Error('IGDB_CLIENT_SECRET not configured');
  } else {
    console.log('[IGDB API] IGDB_CLIENT_SECRET found.');
  }

  // Request a new token
  try {
    const response = await fetch(
      `https://id.twitch.tv/oauth2/token?client_id=${IGDB_CLIENT_ID}&client_secret=${IGDB_CLIENT_SECRET}&grant_type=client_credentials`,
      {
        method: 'POST',
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[IGDB API] Failed to get access token from Twitch. Status: ${response.status}, Body: ${errorText}`);
      throw new Error(`Failed to get access token: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    accessToken = data.access_token;
    // Set expiry time (Twitch tokens typically last 60 days, but we'll be cautious)
    tokenExpiry = Date.now() + (data.expires_in * 1000 * 0.9); // 90% of the expiry time to be safe
    
    return accessToken;
  } catch (error) {
    console.error('Error getting IGDB access token:', error);
    throw error;
  }
}

/**
 * Fetch data from the IGDB API
 * @param endpoint The IGDB API endpoint (e.g., 'games', 'covers')
 * @param query The IGDB API query string
 */
export async function fetchIGDBData(endpoint: string, query: string): Promise<any> {
  try {
    if (!IGDB_CLIENT_ID) {
      throw new Error('IGDB_CLIENT_ID not found in environment variables');
    }

    // Get an access token
    const token = await getAccessToken();
    if (!token) {
      console.error('[IGDB API] No access token available for fetchIGDBData.');
      throw new Error('Missing access token for IGDB request');
    }

    // Make the API request
    console.log(`[IGDB API] Fetching data for endpoint: ${endpoint}, query: ${query}`);
    const response = await fetch(`https://api.igdb.com/v4/${endpoint}`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Client-ID': IGDB_CLIENT_ID,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'text/plain', // IGDB expects the query as plain text
      },
      body: query,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`IGDB API error (${response.status}): ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching IGDB data for ${endpoint}:`, error);
    throw error;
  }
}