'use client';

// DASHBOARD REDESIGN: Phase 1 - Navigation Implementation
// Date: 15/06/2025
// Task: dashboardStyleAdmin001
// 
// Created UserDashboardNavigation component based on AdminNavigation structure
// - Sidebar navigation similar to AdminNavigation with dashboard-specific items
// - User-focused quick stats section instead of system stats
// - Consistent animations and hover effects with purple cosmic theme
// - Code-themed typography with <tag/> brackets for active items

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import {
  LayoutDashboard,
  FileText,
  Gauge,
  Settings,
  Activity,
  ChevronRight,
  Shield,
  Link2,
  MessageSquare,
  User
} from 'lucide-react';

interface NavItem {
  label: string;
  tabKey: string;
  icon: React.ReactNode;
  description?: string;
  badge?: string;
}

const navigationItems: NavItem[] = [
  {
    label: 'Profile',
    tabKey: 'settings',
    icon: <Settings className="h-5 w-5" />,
    description: 'Personalize your profile and settings'
  },
  {
    label: 'Account',
    tabKey: 'account',
    icon: <User className="h-5 w-5" />,
    description: 'Security, connections, and preferences'
  },
  {
    label: 'My Reviews',
    tabKey: 'reviews',
    icon: <FileText className="h-5 w-5" />,
    description: 'Manage your game reviews'
  },
  {
    label: 'My Surveys',
    tabKey: 'performance',
    icon: <Gauge className="h-5 w-5" />,
    description: 'Hardware surveys and data'
  },
  {
    label: 'My Connections',
    tabKey: 'connections',
    icon: <Link2 className="h-5 w-5" />,
    description: 'Connect gaming & social platforms'
  },
  {
    label: 'Comments Mod',
    tabKey: 'comments',
    icon: <MessageSquare className="h-5 w-5" />,
    description: 'Moderate comments on your reviews'
  },
  {
    label: 'Privacy',
    tabKey: 'privacy',
    icon: <Shield className="h-5 w-5" />,
    description: 'Control your privacy and visibility'
  }
];

interface UserDashboardNavigationProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  stats?: {
    totalReviews: number;
    publishedReviews: number;
    averageScore: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalFollowers: number;
  };
  compact?: boolean;
  mobile?: boolean;
}

export function UserDashboardNavigation({
  activeTab = 'settings',
  onTabChange,
  stats,
  compact = false,
  mobile = false
}: UserDashboardNavigationProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Animation for initial mount
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleItemClick = (tabKey: string) => {
    if (onTabChange) {
      onTabChange(tabKey);
    }
  };

  return (
    <Card className={`w-full border border-violet-900/20 overflow-hidden shadow-lg ${mounted ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500 bg-slate-900`}>
      
      {/* Enhanced background with gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-slate-800/10 pointer-events-none" />
      
      <CardContent className="p-4 relative z-10">
        <div className="space-y-2">
          <div className="flex items-center space-x-2 mb-6 pb-2 border-b border-violet-900/20">
            <h3 className="font-mono text-sm uppercase tracking-wider">
              <span className="text-violet-400">&lt;</span>
              <span className="text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">Navigation</span>
              <span className="text-violet-400">/&gt;</span>
            </h3>
          </div>
          
          <nav className="space-y-0.5">
            {navigationItems.map((item, index) => {
              const isActive = activeTab === item.tabKey;
              const isHovered = hoveredItem === item.tabKey;
              
              return (
                <button
                  key={item.tabKey}
                  onClick={() => handleItemClick(item.tabKey)}
                  className={cn(
                    "w-full flex items-center space-x-3 px-3 py-2.5 rounded-md text-sm transition-all duration-200 group relative text-left",
                    isActive
                      ? "bg-primary/5 text-primary border-l-2 border-l-primary/70 pl-[10px]"
                      : "text-muted-foreground hover:text-violet-300 hover:bg-violet-500/5 border-l-2 border-l-transparent pl-[10px]"
                  )}
                  onMouseEnter={() => setHoveredItem(item.tabKey)}
                  onMouseLeave={() => setHoveredItem(null)}

                >
                  <div className={`flex-shrink-0 transition-transform duration-300 ${isHovered ? 'scale-110' : ''}`}>
                    <div className={`w-8 h-8 rounded-lg border flex items-center justify-center transition-all duration-200 ${
                      isActive 
                        ? 'bg-violet-500/20 border-violet-500/40 text-violet-400' 
                        : 'bg-slate-800/50 border-slate-700/50 text-slate-400 group-hover:bg-violet-500/10 group-hover:border-violet-500/30 group-hover:text-violet-300'
                    }`}>
                      {item.icon}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className={`font-mono truncate transition-colors ${isActive ? 'text-white' : ''}`}>
                        {isActive ? (
                          <>
                            <span className="text-violet-400">&lt;</span>
                            <span className="text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.8)]">{item.label}</span>
                            <span className="text-violet-400">/&gt;</span>
                          </>
                        ) : item.label}
                      </span>
                      {item.badge && (
                        <span className="ml-2 px-2 py-0.5 text-xs bg-violet-500/10 text-violet-300 rounded-sm font-mono">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    {item.description && (
                      <div className="text-[10px] text-muted-foreground mt-0.5 truncate opacity-80 font-mono">
                        {item.description}
                      </div>
                    )}
                  </div>
                  
                  {/* Arrow indicator for hover */}
                  <ChevronRight className={`h-4 w-4 transition-transform duration-300 absolute right-2 opacity-0 ${isHovered ? 'opacity-60 translate-x-0' : '-translate-x-1'} ${isActive ? 'text-violet-400' : ''}`} />
                  
                  {/* Subtle glow effect on hover/active */}
                  {(isActive || isHovered) && (
                    <div className="absolute inset-0 bg-gradient-to-r from-violet-500/0 to-violet-500/5 pointer-events-none rounded-md" />
                  )}
                </button>
              );
            })}
          </nav>
          
          {/* User Quick Stats */}
          <div className="mt-6 pt-4 border-t border-violet-900/20">
            <div className="text-xs font-mono text-violet-400 mb-3 flex items-center">
              <span className="inline-block h-1 w-1 bg-violet-400 mr-2"></span>
              <span>user.stats</span>
            </div>
            <div className="space-y-2 font-mono text-[11px]">
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">Total Likes</span>
                <span className="text-red-400 tabular-nums">{stats?.totalLikes || 0}</span>
              </div>
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">Total Comments</span>
                <span className="text-blue-400 tabular-nums">{stats?.totalComments || 0}</span>
              </div>
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">Total Views</span>
                <span className="text-green-400 tabular-nums">{stats?.totalViews || 0}</span>
              </div>
              <div className="flex justify-between items-center bg-violet-500/5 px-2 py-1.5 rounded-sm">
                <span className="text-muted-foreground">Total Followers</span>
                <span className="text-cyan-400 tabular-nums">{stats?.totalFollowers || 0}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 