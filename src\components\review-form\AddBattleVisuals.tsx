'use client';
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import './style/bannerPreview.css';
import {
  Trash2,
  ChevronDown,
  Image,
  Images,
  ExternalLink,
  ArrowRight,
  Info,
  AlertCircle,
  Play,
  Move,
  Eye,
  Ruler,
  RotateCcw,
  Grid3X3
} from 'lucide-react';

// Interfaces
interface PopularImageService {
  name: string;
  url: string;
  icon: React.ReactNode;
}

interface BannerImageInfo {
  url: string;
  width?: number;
  height?: number;
  objectPosition?: string;
  userAdjusted?: boolean;
  isLoading?: boolean;
}

interface SizeRecommendation {
  type: 'success' | 'warning' | 'info';
  message: string;
}

interface SimplifiedMediaVisualsProps {
  bannerImageUrl: string;
  bannerImagePosition?: string;
  videoUrl: string;
  onBannerImageUrlChange: (url: string) => void;
  onBannerImagePositionChange?: (position: string) => void;
  onVideoUrlChange: (url: string) => void;
  handleNextStep: () => void;
  onFindBannerClick?: () => void;
  gameName?: string;
}

const SimplifiedMediaVisuals: React.FC<SimplifiedMediaVisualsProps> = ({
  bannerImageUrl,
  bannerImagePosition,
  videoUrl,
  onBannerImageUrlChange,
  onBannerImagePositionChange,
  onVideoUrlChange,
  handleNextStep,
  onFindBannerClick,
  gameName
}) => {
  // State management
  const [isImageHostsExpanded, setIsImageHostsExpanded] = useState(false);
  const [bannerImageInfo, setBannerImageInfo] = useState<BannerImageInfo>({
    url: bannerImageUrl,
    objectPosition: bannerImagePosition || 'center 25%',
    userAdjusted: !!bannerImagePosition,
    isLoading: false
  });
  const [isDragging, setIsDragging] = useState(false);
  const [showGrid, setShowGrid] = useState(false);
  const [showPositionOverlay, setShowPositionOverlay] = useState(false);
  
  const previewRef = useRef<HTMLDivElement>(null);

  // Image hosting services
  const popularImageServices: PopularImageService[] = [
    { 
      name: "ImgBB", 
      url: "https://imgbb.com/", 
      icon: <Images className="h-4 w-4" />
    },
    { 
      name: "PostImg", 
      url: "https://postimg.cc/", 
      icon: <Image className="h-4 w-4" />
    },
    { 
      name: "ImgPile", 
      url: "https://imgpile.com/", 
      icon: <Image className="h-4 w-4" />
    },
    { 
      name: "FreeImage", 
      url: "https://freeimage.host/", 
      icon: <Images className="h-4 w-4" />
    }
  ];

  // YouTube ID extraction
  const getYouTubeId = (url: string): string | null => {
    const youtubeRegex = /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
    const match = url.match(youtubeRegex);
    return match ? match[1] : null;
  };

  const youTubeId = getYouTubeId(videoUrl);

  // Enhanced image load handler
  const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    setBannerImageInfo(prev => ({
      ...prev,
      width: img.naturalWidth,
      height: img.naturalHeight,
      url: bannerImageUrl,
      isLoading: false,
      // Only reset position if user hasn't manually adjusted it
      objectPosition: prev.userAdjusted ? prev.objectPosition : 'center 25%'
    }));
  }, [bannerImageUrl]);

  // Image error handler
  const handleImageError = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    setBannerImageInfo(prev => ({
      ...prev,
      isLoading: false,
      width: undefined,
      height: undefined
    }));
    
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
    target.nextElementSibling?.classList.remove('hidden');
  }, []);

  // Enhanced size recommendation function with EXACT 4:1 ratio enforcement
  const getOptimalSizeRecommendation = (width?: number, height?: number): SizeRecommendation | null => {
    if (!width || !height) return null;

    const aspectRatio = width / height;
    const optimalRatio = 4.0; // EXACT 4:1 ratio - enforced in preview container

    if (width < 1200 || height < 300) {
      return {
        type: 'warning',
        message: 'Image resolution is too low. May appear blurry on larger screens.'
      };
    }

    // More strict aspect ratio checking since we enforce 4:1 in the preview
    if (Math.abs(aspectRatio - optimalRatio) > 0.2) {
      return {
        type: 'info',
        message: 'Preview enforces 4:1 ratio. Your image will be cropped to fit this exact aspect ratio.'
      };
    }

    if (width === 1920 && height === 480) {
      return {
        type: 'success',
        message: 'Perfect! This matches the 4:1 aspect ratio exactly with optimal dimensions.'
      };
    }

    if (Math.abs(aspectRatio - optimalRatio) <= 0.1) {
      return {
        type: 'success',
        message: 'Excellent! Your image has the perfect 4:1 aspect ratio.'
      };
    }

    return {
      type: 'info',
      message: 'Good aspect ratio. Preview shows exactly how it will appear with 4:1 enforcement.'
    };
  };

  // Enhanced mouse interaction handlers for smooth drag positioning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!previewRef.current) return;

    setIsDragging(true);
    setShowPositionOverlay(true);
    setBannerImageInfo(prev => ({ ...prev, userAdjusted: true }));
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleGlobalMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !previewRef.current) return;

    const rect = previewRef.current.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    const clampedX = Math.max(0, Math.min(100, x));
    const clampedY = Math.max(0, Math.min(100, y));

    // Use requestAnimationFrame for smooth updates
    requestAnimationFrame(() => {
      const newPosition = `${clampedX.toFixed(1)}% ${clampedY.toFixed(1)}%`;
      setBannerImageInfo(prev => ({
        ...prev,
        objectPosition: newPosition,
        userAdjusted: true
      }));
      // Notify parent component of position change
      if (onBannerImagePositionChange) {
        onBannerImagePositionChange(newPosition);
      }
    });
  }, [isDragging]);

  const handleGlobalMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      setShowPositionOverlay(false);
    }
  }, [isDragging]);

  // Global mouse event handlers for smooth dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false });
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, handleGlobalMouseMove, handleGlobalMouseUp]);

  // Reset position function
  const resetToResponsiveDefault = useCallback(() => {
    setBannerImageInfo(prev => ({
      ...prev,
      objectPosition: 'center 25%',
      userAdjusted: false
    }));
    // Notify parent component of position reset
    if (onBannerImagePositionChange) {
      onBannerImagePositionChange('center 25%');
    }
  }, [onBannerImagePositionChange]);

  // Toggle grid function
  const toggleGrid = useCallback(() => {
    setShowGrid(prev => !prev);
  }, []);

  // Update banner info when URL changes
  useEffect(() => {
    setBannerImageInfo(prev => ({
      ...prev,
      url: bannerImageUrl,
      width: undefined,
      height: undefined,
      userAdjusted: false,
      objectPosition: 'center 25%',
      isLoading: !!bannerImageUrl
    }));
  }, [bannerImageUrl]);

  const sizeRecommendation = getOptimalSizeRecommendation(bannerImageInfo.width, bannerImageInfo.height);

  return (
    <div className="space-y-4">

      {/* Content Cards - Side by Side Layout */}
      <div className="space-y-4">
        {/* Media Cards Container - Horizontal Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Banner Image Card */}
          <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-md bg-slate-800/60">
                    <Image className="h-4 w-4 text-slate-400" />
                  </div>
                  <div>
                    <div className="font-mono text-sm">
                      <span className="text-violet-400">//</span>
                      <span className="text-slate-300 ml-1">Banner Image</span>
                    </div>
                    <p className="text-slate-500 text-xs mt-1">
                      Main banner displayed at the top of your content
                    </p>
                  </div>
                </div>
                {gameName && onFindBannerClick && (
                  <Button
                    onClick={onFindBannerClick}
                    variant="outline"
                    size="sm"
                    className="border-violet-500/50 bg-violet-600/20 hover:bg-violet-600/30 text-violet-300 hover:text-violet-200 transition-all duration-200 group shadow-lg shadow-violet-500/10 hover:shadow-violet-500/20 hover:border-violet-400/60"
                  >
                    <Image size={14} className="mr-2 group-hover:scale-110 transition-transform" />
                    <span className="font-medium text-xs">Find Banner</span>
                  </Button>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <div className="font-mono text-xs text-slate-400 mb-2">Image URL</div>
                  <div className="relative">
                    <Input
                      placeholder="https://your-image-url.jpg"
                      value={bannerImageUrl}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        const oldValue = bannerImageUrl;

                        // If user removes one character (backspace/delete), reset the field
                        if (oldValue.length > 0 && newValue.length === oldValue.length - 1) {
                          onBannerImageUrlChange('');
                          // Reset banner image info state
                          setBannerImageInfo({
                            url: '',
                            objectPosition: 'center 25%',
                            userAdjusted: false,
                            isLoading: false
                          });
                          // Reset position in parent
                          if (onBannerImagePositionChange) {
                            onBannerImagePositionChange('center 25%');
                          }
                        } else {
                          onBannerImageUrlChange(newValue);
                        }
                      }}
                      className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                               focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                    />
                    {bannerImageUrl && (
                      <button
                        type="button"
                        onClick={() => {
                          onBannerImageUrlChange('');
                          // Reset banner image info state
                          setBannerImageInfo({
                            url: '',
                            objectPosition: 'center 25%',
                            userAdjusted: false,
                            isLoading: false
                          });
                          // Reset position in parent
                          if (onBannerImagePositionChange) {
                            onBannerImagePositionChange('center 25%');
                          }
                        }}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1
                                 text-slate-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-colors"
                        title="Clear banner image URL"
                        aria-label="Clear banner image URL"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* YouTube Video Card */}
          <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg">
            <div className="p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-md bg-slate-800/60">
                  <Play className="h-4 w-4 text-slate-400" />
                </div>
                <div>
                  <div className="font-mono text-sm">
                    <span className="text-violet-400">//</span>
                    <span className="text-slate-300 ml-1">YouTube Video</span>
                  </div>
                  <p className="text-slate-500 text-xs mt-1">
                    Embedded video showcase for your content
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="font-mono text-xs text-slate-400 mb-2">Video URL</div>
                  <div className="relative">
                    <Input
                      placeholder="https://www.youtube.com/watch?v=..."
                      value={videoUrl}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        const oldValue = videoUrl;

                        // If user removes one character (backspace/delete), reset the field
                        if (oldValue.length > 0 && newValue.length === oldValue.length - 1) {
                          onVideoUrlChange('');
                        } else {
                          onVideoUrlChange(newValue);
                        }
                      }}
                      className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                               focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm pr-10"
                    />
                    {videoUrl && (
                      <button
                        type="button"
                        onClick={() => onVideoUrlChange('')}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1
                                 text-slate-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-colors"
                        title="Clear video URL"
                        aria-label="Clear video URL"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Media Previews Container */}
        {(bannerImageUrl || youTubeId) && (
          <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg">
            <div className="p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-md bg-slate-800/60">
                  <Image className="h-4 w-4 text-slate-400" />
                </div>
                <div>
                  <div className="font-mono text-sm">
                    <span className="text-violet-400">//</span>
                    <span className="text-slate-300 ml-1">Media Previews</span>
                  </div>
                  <p className="text-slate-500 text-xs mt-1">
                    Preview of your uploaded media content
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {/* ENHANCED Banner Image Preview - Exact Match to ReviewBanner */}
                {bannerImageUrl && (
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <div className="font-mono text-xs text-slate-400">Banner Preview</div>
                      <div className="flex items-center gap-3">
                        <button
                          type="button"
                          onClick={toggleGrid}
                          className={`flex items-center gap-1 px-2 py-1 rounded text-xs transition-colors ${
                            showGrid
                              ? 'bg-violet-600/20 text-violet-300 border border-violet-500/30'
                              : 'bg-slate-700/50 text-slate-400 border border-slate-600/30 hover:text-slate-300'
                          }`}
                        >
                          <Grid3X3 className="h-3 w-3" />
                          Grid
                        </button>
                        <div className="flex items-center gap-2">
                          <Eye className="h-3 w-3 text-slate-500" />
                          <span className="text-xs text-slate-500">Drag to position</span>
                        </div>
                      </div>
                    </div>

                    {/* ACCURATE Preview Container with Exact 4:1 Ratio Matching */}
                    <div className="space-y-3">
                      <div
                        ref={previewRef}
                        className={`relative bg-slate-800/40 border border-slate-600/40 rounded-lg overflow-hidden banner-preview-container banner-preview-transition ${
                          isDragging ? 'dragging' : ''
                        }`}
                        onMouseDown={handleMouseDown}
                        onMouseEnter={() => setShowPositionOverlay(true)}
                        onMouseLeave={() => {
                          if (!isDragging) {
                            setShowPositionOverlay(false);
                          }
                        }}
                      >
                        {/* EXACT Banner Image with Matching Styling and 4:1 Ratio Enforcement */}
                        <div className="relative w-full h-full banner-preview-mask">
                          <div className="absolute inset-0 transition-all duration-500">
                            <div className="absolute inset-0">
                              {bannerImageInfo.isLoading && (
                                <div className="w-full h-full banner-image-loading" />
                              )}

                              <img
                                src={bannerImageUrl}
                                alt="Banner preview"
                                className={`banner-preview-image transition-all duration-300 banner-image-smart banner-image-hq ${
                                  bannerImageInfo.isLoading ? 'loading' : 'loaded'
                                }`}
                                onLoad={handleImageLoad}
                                onError={handleImageError}
                                style={{
                                  objectPosition: bannerImageInfo.userAdjusted
                                    ? bannerImageInfo.objectPosition
                                    : undefined
                                }}
                                draggable={false}
                              />

                              {/* EXACT Gradient Overlays - Matching ReviewBanner */}
                              <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/30" />
                              <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50" />

                              {/* Error State */}
                              <div className="hidden absolute inset-0 flex items-center justify-center bg-slate-800/60">
                                <div className="text-center">
                                  <Image className="h-6 w-6 text-slate-500 mx-auto mb-1" />
                                  <p className="text-xs text-slate-500">Invalid image URL</p>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Position Overlay */}
                          <div className={`absolute inset-0 position-overlay ${showPositionOverlay ? 'active' : ''}`}>
                            <div className={`absolute inset-0 position-grid ${showGrid ? 'active' : ''}`} />
                            {showPositionOverlay && !isDragging && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2 text-white text-sm">
                                  Click and drag to adjust image position
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Drag Indicator */}
                          {isDragging && (
                            <div className="absolute inset-0 bg-violet-500/20 border-2 border-violet-400/50 rounded-lg flex items-center justify-center">
                              <Move className="h-6 w-6 text-violet-300" />
                            </div>
                          )}
                        </div>

                        {/* Remove Button */}
                        <button
                          type="button"
                          onClick={() => {
                            onBannerImageUrlChange('');
                            // Reset banner image info state
                            setBannerImageInfo({
                              url: '',
                              objectPosition: 'center 25%',
                              userAdjusted: false,
                              isLoading: false
                            });
                            // Reset position in parent
                            if (onBannerImagePositionChange) {
                              onBannerImagePositionChange('center 25%');
                            }
                          }}
                          className="absolute top-2 right-2 p-1 bg-slate-900/80 hover:bg-red-600/80
                                   text-slate-400 hover:text-white rounded transition-colors z-10"
                          title="Remove banner image"
                          aria-label="Remove banner image"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Enhanced Information Section */}
                      <div className="space-y-3">
                        {bannerImageInfo.width && bannerImageInfo.height && (
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center gap-2">
                              <Ruler className="h-3 w-3 text-slate-500" />
                              <span className="text-slate-400">
                                {bannerImageInfo.width} × {bannerImageInfo.height}px
                              </span>
                            </div>
                            <span className="text-slate-500">
                              Ratio: {(bannerImageInfo.width / bannerImageInfo.height).toFixed(2)}:1
                            </span>
                          </div>
                        )}

                        {/* Current Position Display */}
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-slate-400">Current Position:</span>
                          <span className="text-violet-300 font-mono">
                            {bannerImageInfo.userAdjusted 
                              ? bannerImageInfo.objectPosition 
                              : 'Responsive (CSS controlled)'
                            }
                          </span>
                        </div>

                        {/* Control Buttons */}
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={resetToResponsiveDefault}
                            className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-slate-200 rounded text-xs transition-colors"
                          >
                            <RotateCcw className="h-3 w-3" />
                            Reset Position
                          </button>
                        </div>

                        {sizeRecommendation && (
                          <div className={`flex items-start gap-2 p-3 rounded-md text-xs ${
                            sizeRecommendation.type === 'success'
                              ? 'size-recommendation-success'
                              : sizeRecommendation.type === 'warning'
                              ? 'size-recommendation-warning'
                              : 'size-recommendation-info'
                          }`}>
                            <AlertCircle className={`h-3 w-3 mt-0.5 flex-shrink-0 ${
                              sizeRecommendation.type === 'success'
                                ? 'text-green-400'
                                : sizeRecommendation.type === 'warning'
                                ? 'text-yellow-400'
                                : 'text-blue-400'
                            }`} />
                            <span className={
                              sizeRecommendation.type === 'success'
                                ? 'text-green-300'
                                : sizeRecommendation.type === 'warning'
                                ? 'text-yellow-300'
                                : 'text-blue-300'
                            }>
                              {sizeRecommendation.message}
                            </span>
                          </div>
                        )}

                        {/* Enhanced Optimal Size Guidance */}
                        <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                          <div className="flex items-start gap-2">
                            <Info className="h-3 w-3 text-cyan-400 mt-0.5 flex-shrink-0" />
                            <div className="space-y-1">
                              <div className="font-mono text-xs text-slate-300">
                                Exact 4:1 Ratio Preview
                              </div>
                              <p className="text-xs text-slate-400">
                                This preview enforces the exact 4:1 aspect ratio used in the review banner. Your image will be automatically cropped to maintain this ratio, preserving the original image proportions within the container.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* YouTube Video Indicator */}
                {youTubeId && (
                  <div className="bg-slate-800/40 border border-slate-600/40 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-md bg-red-600/20">
                          <svg className="h-4 w-4 text-red-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                          </svg>
                        </div>
                        <div>
                          <div className="font-mono text-sm text-slate-300">YouTube Video Included</div>
                          <div className="text-xs text-slate-500 mt-1">Video will be embedded in your review</div>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => onVideoUrlChange('')}
                        className="p-2 bg-slate-900/80 hover:bg-red-600/80 text-slate-400 hover:text-white rounded transition-colors"
                        title="Remove video"
                        aria-label="Remove video"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Image Hosting Services - Collapsible */}
        <div className="bg-slate-800/50 border border-slate-700/50 rounded-lg">
          <div 
            className="p-4 cursor-pointer hover:bg-slate-800/20 transition-colors"
            onClick={() => setIsImageHostsExpanded(!isImageHostsExpanded)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-slate-800/60">
                  <Info className="h-4 w-4 text-slate-400" />
                </div>
                <div>
                  <div className="font-mono text-sm">
                    <span className="text-violet-400">//</span>
                    <span className="text-slate-300 ml-1">Image Hosting Services</span>
                  </div>
                  <p className="text-slate-500 text-xs mt-1">
                    Recommended free image hosting platforms
                  </p>
                </div>
              </div>
              <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform ${
                isImageHostsExpanded ? 'rotate-180' : ''
              }`} />
            </div>
          </div>
          
          <div className={`border-t border-slate-700/40 bg-slate-800/20 transition-all duration-300 overflow-hidden ${
            isImageHostsExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}>
              <div className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {popularImageServices.map((service, index) => (
                    <a 
                      key={index}
                      href={service.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 p-3 bg-slate-800/40 border border-slate-700/40 rounded-md
                               hover:border-slate-600/50 hover:bg-slate-700/40 transition-all group"
                    >
                      <div className="p-2 rounded bg-slate-700/60 group-hover:bg-slate-600/60 transition-colors">
                        {service.icon}
                      </div>
                      <div className="flex-1">
                        <div className="font-mono text-sm text-slate-300">{service.name}</div>
                      </div>
                      <ExternalLink className="h-4 w-4 text-slate-500 group-hover:text-slate-400 transition-colors" />
                    </a>
                  ))}
                </div>
                
                <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-cyan-400 mt-0.5 flex-shrink-0" />
                    <div className="space-y-1">
                      <div className="font-mono text-xs text-slate-300">
                        Image URL Requirements
                      </div>
                      <p className="text-xs text-slate-400">
                        Use services that provide direct URLs ending in .jpg, .png, .gif, or .webp
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 gap-3">
        <div className="flex items-center space-x-2 min-w-0">
          <div className="w-2 h-2 bg-cyan-400/60 rounded-full animate-pulse flex-shrink-0" />
          <span className="text-sm text-slate-400/80 font-mono break-words">
            {bannerImageUrl || videoUrl
              ? '// Media configured'
              : '// Optional: Add media content'
            }
          </span>
        </div>

        <div className="flex items-center justify-end flex-shrink-0">
          <Button
            onClick={handleNextStep}
            className="review-continue-button review-continue-ready"
          >
            <div className="review-button-content">
              <span className="review-code-brackets">&lt;</span>
              Continue
              <span className="review-code-brackets">/&gt;</span>
              <ArrowRight className="review-button-arrow" />
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SimplifiedMediaVisuals;