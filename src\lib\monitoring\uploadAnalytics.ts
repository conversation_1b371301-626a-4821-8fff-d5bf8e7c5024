// src/lib/monitoring/uploadAnalytics.ts
export interface UploadMetrics {
  totalUploads: number;
  successfulUploads: number;
  failedUploads: number;
  totalSize: number;
  averageFileSize: number;
  compressionSavings: number;
  securityIssues: number;
  duplicatesDetected: number;
  quotaExceeded: number;
  rateLimitHits: number;
}

export interface UploadEvent {
  id: string;
  userId: string;
  timestamp: Date;
  eventType: 'upload_start' | 'upload_success' | 'upload_failure' | 'security_issue' | 'quota_exceeded' | 'rate_limit';
  metadata: {
    fileName?: string;
    fileSize?: number;
    compressionRatio?: number;
    securityIssues?: string[];
    errorMessage?: string;
    processingTime?: number;
  };
}

/**
 * Upload analytics and monitoring
 */
export class UploadAnalytics {
  private static events: UploadEvent[] = [];

  static logEvent(event: Omit<UploadEvent, 'id' | 'timestamp'>) {
    const fullEvent: UploadEvent = {
      ...event,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };

    this.events.push(fullEvent);

    // Keep only last 1000 events in memory
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    // In production, you would send this to your analytics service
    console.log('Upload Event:', fullEvent);
  }

  static getMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): UploadMetrics {
    const now = new Date();
    const cutoff = new Date();

    switch (timeRange) {
      case 'hour':
        cutoff.setHours(now.getHours() - 1);
        break;
      case 'day':
        cutoff.setDate(now.getDate() - 1);
        break;
      case 'week':
        cutoff.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoff.setMonth(now.getMonth() - 1);
        break;
    }

    const relevantEvents = this.events.filter(event => event.timestamp >= cutoff);

    const totalUploads = relevantEvents.filter(e => e.eventType === 'upload_start').length;
    const successfulUploads = relevantEvents.filter(e => e.eventType === 'upload_success').length;
    const failedUploads = relevantEvents.filter(e => e.eventType === 'upload_failure').length;
    const securityIssues = relevantEvents.filter(e => e.eventType === 'security_issue').length;
    const quotaExceeded = relevantEvents.filter(e => e.eventType === 'quota_exceeded').length;
    const rateLimitHits = relevantEvents.filter(e => e.eventType === 'rate_limit').length;

    const successEvents = relevantEvents.filter(e => e.eventType === 'upload_success');
    const totalSize = successEvents.reduce((sum, e) => sum + (e.metadata.fileSize || 0), 0);
    const averageFileSize = successEvents.length > 0 ? totalSize / successEvents.length : 0;

    const compressionSavings = successEvents.reduce((sum, e) => {
      const ratio = e.metadata.compressionRatio || 1;
      const originalSize = e.metadata.fileSize || 0;
      return sum + (originalSize - (originalSize / ratio));
    }, 0);

    const duplicatesDetected = relevantEvents.filter(e => 
      e.eventType === 'upload_success' && e.metadata.fileName?.includes('duplicate')
    ).length;

    return {
      totalUploads,
      successfulUploads,
      failedUploads,
      totalSize,
      averageFileSize,
      compressionSavings,
      securityIssues,
      duplicatesDetected,
      quotaExceeded,
      rateLimitHits,
    };
  }

  static getTopUsers(limit: number = 10): Array<{ userId: string; uploads: number }> {
    const userCounts = new Map<string, number>();

    this.events
      .filter(e => e.eventType === 'upload_success')
      .forEach(event => {
        const count = userCounts.get(event.userId) || 0;
        userCounts.set(event.userId, count + 1);
      });

    return Array.from(userCounts.entries())
      .map(([userId, uploads]) => ({ userId, uploads }))
      .sort((a, b) => b.uploads - a.uploads)
      .slice(0, limit);
  }

  static getErrorAnalysis(): Array<{ error: string; count: number }> {
    const errorCounts = new Map<string, number>();

    this.events
      .filter(e => e.eventType === 'upload_failure')
      .forEach(event => {
        const error = event.metadata.errorMessage || 'Unknown error';
        const count = errorCounts.get(error) || 0;
        errorCounts.set(error, count + 1);
      });

    return Array.from(errorCounts.entries())
      .map(([error, count]) => ({ error, count }))
      .sort((a, b) => b.count - a.count);
  }

  static getPerformanceMetrics(): {
    averageProcessingTime: number;
    p95ProcessingTime: number;
    slowestUploads: Array<{ fileName: string; processingTime: number }>;
  } {
    const successEvents = this.events.filter(e => 
      e.eventType === 'upload_success' && e.metadata.processingTime
    );

    if (successEvents.length === 0) {
      return {
        averageProcessingTime: 0,
        p95ProcessingTime: 0,
        slowestUploads: [],
      };
    }

    const processingTimes = successEvents
      .map(e => e.metadata.processingTime!)
      .sort((a, b) => a - b);

    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
    const p95Index = Math.floor(processingTimes.length * 0.95);
    const p95ProcessingTime = processingTimes[p95Index] || 0;

    const slowestUploads = successEvents
      .filter(e => e.metadata.processingTime && e.metadata.fileName)
      .sort((a, b) => (b.metadata.processingTime || 0) - (a.metadata.processingTime || 0))
      .slice(0, 5)
      .map(e => ({
        fileName: e.metadata.fileName!,
        processingTime: e.metadata.processingTime!,
      }));

    return {
      averageProcessingTime,
      p95ProcessingTime,
      slowestUploads,
    };
  }
}

/**
 * Health check for B2 service
 */
export class B2HealthCheck {
  static async checkHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    latency: number;
    errors: string[];
  }> {
    const startTime = Date.now();
    const errors: string[] = [];

    try {
      // Test B2 connectivity with a simple HEAD request
      const response = await fetch(`https://${process.env.B2_ENDPOINT}`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });

      const latency = Date.now() - startTime;

      if (!response.ok) {
        errors.push(`B2 endpoint returned ${response.status}`);
      }

      if (latency > 2000) {
        errors.push(`High latency: ${latency}ms`);
      }

      const status = errors.length === 0 ? 'healthy' : 
                   errors.length === 1 && latency > 2000 ? 'degraded' : 'unhealthy';

      return { status, latency, errors };
    } catch (error) {
      const latency = Date.now() - startTime;
      errors.push(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      return { status: 'unhealthy', latency, errors };
    }
  }
}
