-- ==========================================
-- TWITCH INTEGRATION DATABASE SCHEMA
-- CriticalPixel - Twitch Module Implementation
-- Created: January 16, 2025
-- ==========================================

-- ==========================================
-- TABLE 1: USER TWITCH DATA
-- Stores user Twitch account information and OAuth tokens
-- ==========================================

CREATE TABLE user_twitch_data (
    -- Primary key linking to profiles table
    user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Twitch user identification
    twitch_user_id VARCHAR(50) NOT NULL UNIQUE,
    username VARCHAR(25) NOT NULL,
    display_name VARCHAR(50),
    profile_image_url TEXT,
    description TEXT,
    broadcaster_type VARCHAR(20) DEFAULT 'affiliate',
    
    -- OAuth tokens and authentication
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    scopes TEXT[] DEFAULT ARRAY['user:read:email', 'clips:read'],
    
    -- Metadata and status
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_fetched TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_valid BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    
    -- Constraints
    CONSTRAINT valid_broadcaster_type CHECK (broadcaster_type IN ('partner', 'affiliate', '')),
    CONSTRAINT valid_username_length CHECK (LENGTH(username) >= 1 AND LENGTH(username) <= 25),
    CONSTRAINT future_token_expiry CHECK (token_expires_at > NOW())
);

-- Indexes for performance optimization
CREATE INDEX idx_user_twitch_data_twitch_user_id ON user_twitch_data(twitch_user_id);
CREATE INDEX idx_user_twitch_data_updated_at ON user_twitch_data(updated_at);
CREATE INDEX idx_user_twitch_data_token_expires ON user_twitch_data(token_expires_at);
CREATE INDEX idx_user_twitch_data_is_valid ON user_twitch_data(is_valid);

-- RLS (Row Level Security) Policy
ALTER TABLE user_twitch_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own Twitch data" ON user_twitch_data
    FOR ALL USING (auth.uid() = user_id);

-- ==========================================
-- TABLE 2: USER TWITCH CLIPS
-- Cache Twitch clips data for performance
-- ==========================================

CREATE TABLE user_twitch_clips (
    -- Twitch clip identification
    id VARCHAR(36) PRIMARY KEY, -- Twitch clip ID
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Clip metadata
    title VARCHAR(100) NOT NULL,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Media URLs
    thumbnail_url TEXT NOT NULL,
    embed_url TEXT NOT NULL,
    url TEXT NOT NULL,
    
    -- Content details
    duration DECIMAL(6,2) DEFAULT 0,
    game_id VARCHAR(20),
    game_name VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    
    -- Creator information
    creator_id VARCHAR(50),
    creator_name VARCHAR(25),
    
    -- Cache metadata
    fetched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_view_count CHECK (view_count >= 0),
    CONSTRAINT positive_duration CHECK (duration >= 0),
    CONSTRAINT valid_language_code CHECK (LENGTH(language) <= 10),
    CONSTRAINT non_empty_title CHECK (LENGTH(TRIM(title)) > 0)
);

-- Indexes for performance optimization
CREATE INDEX idx_user_twitch_clips_user_id ON user_twitch_clips(user_id);
CREATE INDEX idx_user_twitch_clips_created_at ON user_twitch_clips(created_at DESC);
CREATE INDEX idx_user_twitch_clips_fetched_at ON user_twitch_clips(fetched_at);
CREATE INDEX idx_user_twitch_clips_view_count ON user_twitch_clips(view_count DESC);
CREATE INDEX idx_user_twitch_clips_game_id ON user_twitch_clips(game_id);

-- RLS (Row Level Security) Policy
ALTER TABLE user_twitch_clips ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can only access their own clips" ON user_twitch_clips
    FOR ALL USING (auth.uid() = user_id);

-- Public read policy for published profiles
CREATE POLICY "Public read access for clips" ON user_twitch_clips
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = user_twitch_clips.user_id 
            AND (privacy_settings->>'profile_visibility' = 'public' OR privacy_settings IS NULL)
        )
    );

-- ==========================================
-- TABLE 3: USER TWITCH STREAM STATUS
-- Track live streaming status for real-time updates
-- ==========================================

CREATE TABLE user_twitch_stream_status (
    -- Primary key linking to profiles table
    user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Stream status
    is_live BOOLEAN DEFAULT FALSE,
    stream_id VARCHAR(50),
    stream_title VARCHAR(140),
    
    -- Stream details
    game_id VARCHAR(20),
    game_name VARCHAR(100),
    viewer_count INTEGER DEFAULT 0,
    language VARCHAR(10) DEFAULT 'en',
    thumbnail_url TEXT,
    
    -- Timing information
    started_at TIMESTAMP WITH TIME ZONE,
    last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_viewer_count CHECK (viewer_count >= 0),
    CONSTRAINT valid_stream_title_length CHECK (LENGTH(stream_title) <= 140),
    CONSTRAINT live_status_consistency CHECK (
        (is_live = TRUE AND stream_id IS NOT NULL) OR 
        (is_live = FALSE)
    ),
    CONSTRAINT recent_last_checked CHECK (last_checked >= NOW() - INTERVAL '1 hour')
);

-- Indexes for performance optimization
CREATE INDEX idx_user_twitch_stream_status_is_live ON user_twitch_stream_status(is_live);
CREATE INDEX idx_user_twitch_stream_status_last_checked ON user_twitch_stream_status(last_checked);
CREATE INDEX idx_user_twitch_stream_status_viewer_count ON user_twitch_stream_status(viewer_count DESC);
CREATE INDEX idx_user_twitch_stream_status_game_id ON user_twitch_stream_status(game_id);

-- RLS (Row Level Security) Policy
ALTER TABLE user_twitch_stream_status ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own stream status" ON user_twitch_stream_status
    FOR ALL USING (auth.uid() = user_id);

-- Public read policy for live status
CREATE POLICY "Public read access for stream status" ON user_twitch_stream_status
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = user_twitch_stream_status.user_id 
            AND (privacy_settings->>'profile_visibility' = 'public' OR privacy_settings IS NULL)
        )
    );

-- ==========================================
-- TABLE 4: UPDATE EXISTING CONTENT PREFERENCES
-- Add Twitch module settings to existing table
-- ==========================================

-- Add Twitch module configuration to user_content_preferences
ALTER TABLE user_content_preferences 
ADD COLUMN IF NOT EXISTS twitch_module JSONB DEFAULT '{
    "enabled": false,
    "visibility": "public",
    "maxClips": 12,
    "showStats": true,
    "showStreamStatus": true,
    "autoRefresh": true,
    "refreshInterval": 300
}';

-- Update existing records to have default Twitch module settings
UPDATE user_content_preferences 
SET twitch_module = '{
    "enabled": false,
    "visibility": "public",
    "maxClips": 12,
    "showStats": true,
    "showStreamStatus": true,
    "autoRefresh": true,
    "refreshInterval": 300
}'
WHERE twitch_module IS NULL;

-- Add constraint to validate Twitch module settings
ALTER TABLE user_content_preferences
ADD CONSTRAINT valid_twitch_module_settings CHECK (
    twitch_module ? 'enabled' AND
    twitch_module ? 'visibility' AND
    twitch_module ? 'maxClips' AND
    twitch_module ? 'showStats' AND
    twitch_module ? 'showStreamStatus' AND
    twitch_module ? 'autoRefresh' AND
    twitch_module ? 'refreshInterval' AND
    (twitch_module->>'visibility') IN ('public', 'friends', 'private') AND
    (twitch_module->>'maxClips')::integer BETWEEN 6 AND 24 AND
    (twitch_module->>'refreshInterval')::integer BETWEEN 60 AND 1800
);

-- ==========================================
-- FUNCTIONS AND TRIGGERS
-- Automated maintenance and data integrity
-- ==========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_user_twitch_data_updated_at 
    BEFORE UPDATE ON user_twitch_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_twitch_stream_status_updated_at 
    BEFORE UPDATE ON user_twitch_stream_status 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired clips cache
CREATE OR REPLACE FUNCTION cleanup_expired_twitch_clips()
RETURNS void AS $$
BEGIN
    DELETE FROM user_twitch_clips 
    WHERE fetched_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Function to reset offline streams that haven't been checked recently
CREATE OR REPLACE FUNCTION reset_stale_stream_status()
RETURNS void AS $$
BEGIN
    UPDATE user_twitch_stream_status 
    SET is_live = FALSE,
        stream_id = NULL,
        stream_title = NULL,
        viewer_count = 0,
        updated_at = NOW()
    WHERE last_checked < NOW() - INTERVAL '30 minutes'
    AND is_live = TRUE;
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- INITIAL DATA AND CONFIGURATION
-- Setup default configurations
-- ==========================================

-- Insert default Twitch module settings for existing users without preferences
INSERT INTO user_content_preferences (user_id, enabled_modules, twitch_module, created_at, updated_at)
SELECT 
    p.id,
    ARRAY['reviews', 'surveys', 'activity'],
    '{
        "enabled": false,
        "visibility": "public",
        "maxClips": 12,
        "showStats": true,
        "showStreamStatus": true,
        "autoRefresh": true,
        "refreshInterval": 300
    }'::jsonb,
    NOW(),
    NOW()
FROM profiles p
WHERE NOT EXISTS (
    SELECT 1 FROM user_content_preferences ucp 
    WHERE ucp.user_id = p.id
);

-- ==========================================
-- MAINTENANCE VIEWS
-- Helper views for monitoring and maintenance
-- ==========================================

-- View for monitoring Twitch integration health
CREATE OR REPLACE VIEW twitch_integration_health AS
SELECT 
    COUNT(*) as total_connected_users,
    COUNT(*) FILTER (WHERE is_valid = TRUE) as valid_connections,
    COUNT(*) FILTER (WHERE token_expires_at < NOW() + INTERVAL '24 hours') as tokens_expiring_soon,
    COUNT(*) FILTER (WHERE last_fetched < NOW() - INTERVAL '1 day') as stale_connections,
    AVG(EXTRACT(EPOCH FROM (NOW() - last_fetched))) as avg_seconds_since_fetch
FROM user_twitch_data;

-- View for live streaming statistics
CREATE OR REPLACE VIEW twitch_live_stats AS
SELECT 
    COUNT(*) FILTER (WHERE is_live = TRUE) as currently_live,
    COUNT(*) as total_streamers,
    AVG(viewer_count) FILTER (WHERE is_live = TRUE) as avg_viewers,
    MAX(viewer_count) FILTER (WHERE is_live = TRUE) as max_viewers,
    COUNT(*) FILTER (WHERE last_checked < NOW() - INTERVAL '10 minutes') as stale_status_checks
FROM user_twitch_stream_status;

-- View for clips statistics
CREATE OR REPLACE VIEW twitch_clips_stats AS
SELECT 
    COUNT(*) as total_clips,
    COUNT(DISTINCT user_id) as users_with_clips,
    AVG(view_count) as avg_clip_views,
    AVG(duration) as avg_clip_duration,
    COUNT(*) FILTER (WHERE fetched_at < NOW() - INTERVAL '1 day') as stale_clips
FROM user_twitch_clips;

-- ==========================================
-- PERFORMANCE OPTIMIZATION
-- Additional indexes and optimizations
-- ==========================================

-- Composite indexes for common query patterns
CREATE INDEX idx_twitch_clips_user_created ON user_twitch_clips(user_id, created_at DESC);
CREATE INDEX idx_twitch_clips_game_views ON user_twitch_clips(game_id, view_count DESC);
CREATE INDEX idx_stream_status_live_viewers ON user_twitch_stream_status(is_live, viewer_count DESC) WHERE is_live = TRUE;

-- Partial indexes for active data
CREATE INDEX idx_twitch_data_active ON user_twitch_data(user_id) WHERE is_valid = TRUE;
CREATE INDEX idx_stream_status_live ON user_twitch_stream_status(user_id) WHERE is_live = TRUE;

-- ==========================================
-- BACKUP AND RECOVERY CONSIDERATIONS
-- Important notes for data management
-- ==========================================

/*
BACKUP CONSIDERATIONS:
1. user_twitch_data contains sensitive OAuth tokens - encrypt backups
2. Clips data can be regenerated from API - lower backup priority
3. Stream status is ephemeral - can be excluded from critical backups

RECOVERY PROCEDURES:
1. OAuth tokens may need refresh after restore
2. Clips cache should be rebuilt via API calls
3. Stream status should be reset and checked fresh

RETENTION POLICIES:
1. Keep OAuth data for active users indefinitely
2. Clips cache: 30 days rolling window
3. Stream status: 7 days for analytics
4. Error logs: 90 days for debugging
*/

-- ==========================================
-- SECURITY NOTES
-- Important security considerations
-- ==========================================

/*
SECURITY IMPLEMENTATION NOTES:

1. ENCRYPTION:
   - access_token and refresh_token should be encrypted at application level
   - Consider using Supabase's encryption features for sensitive columns

2. ACCESS CONTROL:
   - RLS policies prevent cross-user data access
   - API calls should validate user ownership before database operations

3. TOKEN MANAGEMENT:
   - Implement automatic token refresh before expiration
   - Revoke tokens immediately upon user disconnection
   - Monitor for unusual API usage patterns

4. AUDIT TRAIL:
   - Log all OAuth operations for security monitoring
   - Track API usage patterns for abuse detection
   - Monitor failed authentication attempts

5. DATA MINIMIZATION:
   - Store only necessary Twitch data
   - Regular cleanup of expired/stale data
   - User-initiated data deletion support
*/

-- ==========================================
-- MIGRATION COMPLETION
-- Final steps and validation
-- ==========================================

-- Verify all tables were created successfully
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_twitch_data') THEN
        RAISE EXCEPTION 'user_twitch_data table was not created';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_twitch_clips') THEN
        RAISE EXCEPTION 'user_twitch_clips table was not created';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_twitch_stream_status') THEN
        RAISE EXCEPTION 'user_twitch_stream_status table was not created';
    END IF;
    
    RAISE NOTICE 'Twitch integration database schema migration completed successfully!';
END
$$;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT ALL ON user_twitch_data TO your_app_user;
-- GRANT ALL ON user_twitch_clips TO your_app_user;
-- GRANT ALL ON user_twitch_stream_status TO your_app_user;

-- ==========================================
-- END OF MIGRATION SCRIPT
-- ==========================================