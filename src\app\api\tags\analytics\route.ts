// src/app/api/tags/analytics/route.ts
// Tag analytics tracking and management

import { NextRequest, NextResponse } from 'next/server';
import { createServerTagService } from '@/lib/services/tagService';
import { createServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const tagService = createServerTagService();
    const body = await request.json();

    const { action, tag_id, data } = body;

    if (!action || !tag_id) {
      return NextResponse.json(
        { error: 'Action and tag_id are required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'view':
        result = await tagService.incrementTagView(tag_id);
        break;

      case 'click':
        result = await tagService.incrementTagClick(tag_id);
        break;

      case 'search':
        result = await tagService.incrementTagSearch(tag_id);
        break;

      case 'track':
        if (!data) {
          return NextResponse.json(
            { error: 'Analytics data is required for track action' },
            { status: 400 }
          );
        }
        result = await tagService.trackTagAnalytics({
          tag_id,
          views: data.views || 0,
          clicks: data.clicks || 0,
          searches: data.searches || 0,
          new_usages: data.new_usages || 0,
          avg_time_on_page: data.avg_time_on_page,
          bounce_rate: data.bounce_rate
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Tag ${action} tracked successfully`
    });

  } catch (error) {
    console.error('Error in tag analytics API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const tagService = createServerTagService();

    // Check authentication and admin privileges
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    let result;

    switch (action) {
      case 'recalculate_trends':
        result = await tagService.recalculateTrendScores();
        break;

      case 'reset_recent_usage':
        result = await tagService.resetRecentUsage();
        break;

      case 'migrate_existing':
        result = await tagService.migrateExistingTags();
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid admin action' },
          { status: 400 }
        );
    }

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Admin action ${action} completed successfully`
    });

  } catch (error) {
    console.error('Error in tag analytics admin API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}