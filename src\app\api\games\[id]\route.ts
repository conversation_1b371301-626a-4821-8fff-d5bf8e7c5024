// API endpoint for managing games in Supabase
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { fetchIGDBData } from '@/lib/igdb-api';
import { downloadAndCacheGameCover, shouldRecacheCover } from '@/lib/services/gameCoverService';
import { extractIgdbIdServer } from '@/lib/utils/coverUtils.server';
import { slugify } from '@/lib/utils/slugify';

// Helper function to ensure proper IGDB URL format
function normalizeIgdbUrl(url: string): string {
  if (!url) return '';

  // If URL starts with //, add https:
  if (url.startsWith('//')) {
    return `https:${url}`;
  }

  // If URL already has protocol, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Otherwise, assume it needs https://
  return `https://${url}`;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = await createServerClient();

    // First check if game exists in our database
    const { data: existingGame, error: dbError } = await supabase
      .from('games')
      .select('*')
      .eq('igdb_id', parseInt(id))
      .single();

    if (existingGame && !dbError) {
      console.log('[API /api/games/[id]] Found game in database:', existingGame.name);
      return NextResponse.json(existingGame);
    }

    // If not found in database, fetch from IGDB and store
    console.log('[API /api/games/[id]] Game not found in database, fetching from IGDB...');
    
    const query = `
      where id = ${id};
      fields name, id, cover.url, first_release_date, 
             platforms.name, genres.name, 
             involved_companies.company.name, involved_companies.developer, involved_companies.publisher,
             rating, summary, total_rating, aggregated_rating, aggregated_rating_count,
             screenshots.url, game_engines.name, player_perspectives.name, videos.video_id;
      limit 1;
    `;

    const igdbData = await fetchIGDBData('games', query);

    if (!igdbData || igdbData.length === 0) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const game = igdbData[0];

    // Transform IGDB data to our database format
    const gameData = {
      igdb_id: game.id,
      name: game.name,
      slug: slugify(game.name),
      summary: game.summary || null,
      release_date: game.first_release_date 
        ? new Date(game.first_release_date * 1000).toISOString().split('T')[0]
        : null,
      cover_url: game.cover?.url ? normalizeIgdbUrl(game.cover.url.replace('t_thumb', 't_cover_big')) : null,
      aggregated_rating: game.aggregated_rating || null,
      aggregated_rating_count: game.aggregated_rating_count || null,
      developers: game.involved_companies 
        ? game.involved_companies
            .filter((ic: any) => ic.developer)
            .map((ic: any) => ic.company.name)
        : [],
      publishers: game.involved_companies 
        ? game.involved_companies
            .filter((ic: any) => ic.publisher)
            .map((ic: any) => ic.company.name)
        : [],
      genres: game.genres ? game.genres.map((g: any) => g.name) : [],
      platforms: game.platforms ? game.platforms.map((p: any) => p.name) : [],
      game_engines: game.game_engines ? game.game_engines.map((ge: any) => ge.name) : [],
      player_perspectives: game.player_perspectives ? game.player_perspectives.map((pp: any) => pp.name) : [],
      time_to_beat_normally: null, // IGDB doesn't provide this
      time_to_beat_completely: null, // IGDB doesn't provide this
    };

    // Store in database
    const { data: insertedGame, error: insertError } = await supabase
      .from('games')
      .insert(gameData)
      .select()
      .single();

    if (insertError) {
      console.error('[API /api/games/[id]] Error storing game:', insertError);
      // Return IGDB data even if storage fails
      return NextResponse.json({
        ...gameData,
        id: game.id.toString()
      });
    }

    console.log('[API /api/games/[id]] Game stored successfully:', insertedGame.name);

    // Start cover caching in background if cover URL exists
    if (insertedGame.cover_url) {
      const igdbId = extractIgdbIdServer(insertedGame);
      if (igdbId) {
        console.log('[API /api/games/[id]] Starting background cover caching for game:', insertedGame.name);
        // Don't await - let it run in background
        downloadAndCacheGameCover(insertedGame.id, igdbId, insertedGame.cover_url)
          .then(result => {
            if (result.success) {
              console.log('[API /api/games/[id]] Cover cached successfully for:', insertedGame.name);
            } else {
              console.error('[API /api/games/[id]] Cover caching failed for:', insertedGame.name, result.error);
            }
          })
          .catch(error => {
            console.error('[API /api/games/[id]] Cover caching error for:', insertedGame.name, error);
          });
      }
    }

    return NextResponse.json(insertedGame);

  } catch (error: any) {
    console.error('[API /api/games/[id]] Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch game data', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const igdbGameData = await request.json();
    console.log('[API POST /api/games/[id]] Received game data for ID:', id, 'Name:', igdbGameData.name);
    const supabase = await createServerClient();

    // Debug: Log the IGDB data structure
    console.log('[API POST /api/games/[id]] IGDB data structure:', {
      name: igdbGameData.name,
      hasGenres: !!igdbGameData.genres,
      genresLength: igdbGameData.genres?.length,
      genresData: igdbGameData.genres,
      hasPlatforms: !!igdbGameData.platforms,
      platformsLength: igdbGameData.platforms?.length,
      platformsData: igdbGameData.platforms,
      hasInvolvedCompanies: !!igdbGameData.involved_companies,
      involvedCompaniesLength: igdbGameData.involved_companies?.length
    });

    // Transform IGDB data to our database format
    const gameData = {
      igdb_id: parseInt(id),
      name: igdbGameData.name,
      slug: slugify(igdbGameData.name),
      summary: igdbGameData.summary || null,
      release_date: igdbGameData.first_release_date 
        ? new Date(igdbGameData.first_release_date * 1000).toISOString().split('T')[0]
        : null,
      cover_url: igdbGameData.cover?.url ? normalizeIgdbUrl(igdbGameData.cover.url.replace('t_thumb', 't_cover_big')) : null,
      aggregated_rating: igdbGameData.aggregated_rating || null,
      aggregated_rating_count: igdbGameData.aggregated_rating_count || null,
      developers: igdbGameData.involved_companies 
        ? igdbGameData.involved_companies
            .filter((ic: any) => ic.developer)
            .map((ic: any) => ic.company.name)
        : [],
      publishers: igdbGameData.involved_companies 
        ? igdbGameData.involved_companies
            .filter((ic: any) => ic.publisher)
            .map((ic: any) => ic.company.name)
        : [],
      genres: igdbGameData.genres ? igdbGameData.genres.map((g: any) => 
        typeof g === 'string' ? g : (g?.name || g?.toString() || null)
      ).filter(Boolean) : [],
      platforms: igdbGameData.platforms ? igdbGameData.platforms.map((p: any) => 
        typeof p === 'string' ? p : (p?.name || p?.toString() || null)
      ).filter(Boolean) : [],
      game_engines: igdbGameData.game_engines ? igdbGameData.game_engines.map((ge: any) => 
        typeof ge === 'string' ? ge : (ge?.name || ge?.toString() || null)
      ).filter(Boolean) : [],
      player_perspectives: igdbGameData.player_perspectives ? igdbGameData.player_perspectives.map((pp: any) => 
        typeof pp === 'string' ? pp : (pp?.name || pp?.toString() || null)
      ).filter(Boolean) : [],
      time_to_beat_normally: igdbGameData.time_to_beat?.normally || null,
      time_to_beat_completely: igdbGameData.time_to_beat?.completely || null,
    };

    console.log('[API POST /api/games/[id]] Processed game data:', {
      name: gameData.name,
      genres: gameData.genres,
      platforms: gameData.platforms,
      developers: gameData.developers,
      publishers: gameData.publishers
    });

    // Use upsert to handle both insert and update
    const { data: upsertedGame, error: upsertError } = await supabase
      .from('games')
      .upsert(gameData, { 
        onConflict: 'igdb_id',
        ignoreDuplicates: false 
      })
      .select()
      .single();

    if (upsertError) {
      console.error('[API POST /api/games/[id]] Error upserting game:', upsertError);
      return NextResponse.json(
        { error: 'Failed to store game', details: upsertError.message },
        { status: 500 }
      );
    }

    console.log('[API POST /api/games/[id]] Game stored successfully:', upsertedGame.name);

    // Start cover caching in background if cover URL exists and should be cached
    if (upsertedGame.cover_url && shouldRecacheCover(upsertedGame)) {
      const igdbId = extractIgdbIdServer(upsertedGame);
      if (igdbId) {
        console.log('[API POST /api/games/[id]] Starting background cover caching for game:', upsertedGame.name);
        // Don't await - let it run in background
        downloadAndCacheGameCover(upsertedGame.id, igdbId, upsertedGame.cover_url)
          .then(result => {
            if (result.success) {
              console.log('[API POST /api/games/[id]] Cover cached successfully for:', upsertedGame.name);
            } else {
              console.error('[API POST /api/games/[id]] Cover caching failed for:', upsertedGame.name, result.error);
            }
          })
          .catch(error => {
            console.error('[API POST /api/games/[id]] Cover caching error for:', upsertedGame.name, error);
          });
      }
    }

    return NextResponse.json(upsertedGame);

  } catch (error: any) {
    console.error('[API POST /api/games/[id]] Error:', error);
    return NextResponse.json(
      { error: 'Failed to store game data', details: error.message },
      { status: 500 }
    );
  }
}