/* Review Controls Component Styles - Matching AddBattleVisuals */

.review-creation-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.controls-section {
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 0.5rem;
  overflow: hidden;
}

.section-header-minimal {
  padding: 1rem 1.5rem;
  background: transparent;
}

.controls-content {
  padding: 1rem;
}

.settings-content-inner {
  padding: 1rem;
  padding-top: 0;
}

/* Navigation Step Buttons */
.nav-step-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(100, 116, 139, 0.4);
  border-radius: 0.375rem;
  color: rgb(148, 163, 184);
  transition: all 0.2s ease;
  font-family: ui-monospace, SFMono-Regular, monospace;
  font-size: 0.875rem;
  cursor: pointer;
}

.nav-step-button:hover {
  background: rgba(51, 65, 85, 0.5);
  border-color: rgba(100, 116, 139, 0.6);
  color: rgb(203, 213, 225);
}

.nav-step-button.active {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
  color: rgb(196, 181, 253);
}

.nav-step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid rgba(100, 116, 139, 0.4);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.nav-step-button.active .nav-step-icon {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.4);
}

.nav-step-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.nav-step-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgb(139, 92, 246);
}

.nav-step-name {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Settings Toggle Button */
.settings-toggle-button {
  padding: 0.5rem;
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(100, 116, 139, 0.4);
  border-radius: 0.375rem;
  color: rgb(148, 163, 184);
  transition: all 0.2s ease;
  cursor: pointer;
}

.settings-toggle-button:hover {
  background: rgba(51, 65, 85, 0.5);
  color: rgb(203, 213, 225);
}

/* Settings Grid */
.settings-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  margin-top: 1rem;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.2);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.setting-item:hover {
  background: rgba(51, 65, 85, 0.4);
  border-color: rgba(100, 116, 139, 0.5);
}

.setting-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.setting-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.setting-icon {
  color: rgb(139, 92, 246);
}

.setting-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgb(248, 250, 252);
  font-family: ui-monospace, SFMono-Regular, monospace;
}

.setting-description {
  font-size: 0.75rem;
  color: rgb(148, 163, 184);
}

/* Completion Indicator */
.completion-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.completion-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(139, 92, 246);
  font-family: ui-monospace, SFMono-Regular, monospace;
}

/* Publishing Controls */
.publishing-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.visibility-controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.visibility-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.2);
  border: 1px solid rgba(100, 116, 139, 0.3);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.visibility-option:hover {
  background: rgba(51, 65, 85, 0.4);
  border-color: rgba(100, 116, 139, 0.5);
}

.visibility-info {
  display: flex;
  align-items: center;
  gap: 0.875rem;
}

.visibility-icon {
  color: rgb(6, 182, 212);
}

.visibility-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(248, 250, 252);
  font-family: ui-monospace, SFMono-Regular, monospace;
}

.visibility-description {
  font-size: 0.8rem;
  color: rgb(148, 163, 184);
  margin: 0;
}

/* Schedule Controls */
.schedule-controls {
  padding: 1rem;
  background: rgba(51, 65, 85, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}

.schedule-input {
  width: 100%;
  padding: 0.75rem;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  color: rgb(248, 250, 252);
  font-family: ui-monospace, SFMono-Regular, monospace;
  font-size: 0.875rem;
}

.schedule-input:focus {
  outline: none;
  border-color: rgb(139, 92, 246);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.draft-button {
  background: rgba(51, 65, 85, 0.4) !important;
  border: 1px solid rgba(100, 116, 139, 0.4) !important;
  color: rgb(203, 213, 225) !important;
  font-family: ui-monospace, SFMono-Regular, monospace !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
  flex: 1;
  min-width: 140px;
}

.draft-button:hover:not(:disabled) {
  background: rgba(51, 65, 85, 0.6) !important;
  border-color: rgba(100, 116, 139, 0.6) !important;
  color: rgb(248, 250, 252) !important;
}

.publish-button {
  background: rgba(139, 92, 246, 0.6) !important;
  border: 1px solid rgba(139, 92, 246, 0.4) !important;
  color: white !important;
  font-family: ui-monospace, SFMono-Regular, monospace !important;
  font-weight: 600 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
  flex: 1;
  min-width: 140px;
}

.publish-button:hover:not(:disabled) {
  background: rgba(139, 92, 246, 0.8) !important;
  border-color: rgba(139, 92, 246, 0.6) !important;
}

.publish-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* Continue Button Styles - Matching other form components */
.review-continue-button {
  background: rgba(51, 65, 85, 0.4) !important;
  border: 1px solid rgba(100, 116, 139, 0.4) !important;
  color: rgb(203, 213, 225) !important;
  font-family: ui-monospace, SFMono-Regular, monospace !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
  min-width: 140px;
}

.review-continue-button:hover:not(:disabled) {
  background: rgba(51, 65, 85, 0.6) !important;
  border-color: rgba(100, 116, 139, 0.6) !important;
  color: rgb(248, 250, 252) !important;
}

.review-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
}

.review-code-brackets {
  color: rgba(139, 92, 246, 0.7);
  opacity: 0.8;
  transition: all 0.2s ease;
  font-weight: 400;
}

.review-button-arrow {
  width: 1rem;
  height: 1rem;
  transition: all 0.2s ease;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .draft-button,
  .action-buttons .publish-button {
    flex: none;
    width: 100%;
  }
}
