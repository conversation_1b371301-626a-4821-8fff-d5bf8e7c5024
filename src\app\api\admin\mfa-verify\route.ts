import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { authenticator } from 'otplib';
import { createDecipheriv } from 'crypto';

/**
 * ADMIN MFA VERIFICATION API ROUTE
 * Handles MFA verification for admin access
 * 
 * Date: June 16, 2025
 * Security Level: CRITICAL
 * Purpose: Verify MFA tokens and create verification sessions
 */

const ENCRYPTION_KEY = process.env.MFA_ENCRYPTION_KEY || 'default-key-change-in-production';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token } = body;

    if (!token || token.length !== 6) {
      return NextResponse.json(
        { success: false, error: 'Código MFA inválido' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createServerClient();
    
    // Get authenticated user
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Get user's MFA settings directly from table
    const { data: mfaSettings, error: mfaError } = await supabase
      .from('user_mfa_settings')
      .select('is_enabled, secret_encrypted')
      .eq('user_id', user.id as any)
      .single();

    if (mfaError || !mfaSettings) {
      console.error('MFA settings error:', mfaError);
      return NextResponse.json(
        { success: false, error: 'MFA não configurado. Configure o MFA primeiro.' },
        { status: 400 }
      );
    }

    const settings = mfaSettings as any;
    if (!settings.is_enabled || !settings.secret_encrypted) {
      return NextResponse.json(
        { success: false, error: 'MFA não configurado. Configure o MFA primeiro.' },
        { status: 400 }
      );
    }

    // Decrypt the secret using the same method as MFAService
    let decryptedSecret: string;
    try {
      const encryptedSecret = settings.secret_encrypted;
      const [ivHex, encrypted] = encryptedSecret.split(':');
      const algorithm = 'aes-256-cbc';
      const key = Buffer.from(ENCRYPTION_KEY, 'hex');
      const iv = Buffer.from(ivHex, 'hex');

      const decipher = createDecipheriv(algorithm, key, iv);
      decryptedSecret = decipher.update(encrypted, 'hex', 'utf8');
      decryptedSecret += decipher.final('utf8');

      if (!decryptedSecret) {
        throw new Error('Decryption failed');
      }
    } catch (decryptError) {
      console.error('MFA decryption error:', decryptError);
      return NextResponse.json(
        { success: false, error: 'Erro na descriptografia MFA. Reconfigure o MFA.' },
        { status: 500 }
      );
    }

    // Verify the token
    const isValid = authenticator.check(token, decryptedSecret);

    if (!isValid) {
      console.warn('🔒 MFA verification failed', {
        userId: user.id,
        tokenLength: token.length,
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      });

      return NextResponse.json(
        { success: false, error: 'Código MFA inválido' },
        { status: 400 }
      );
    }

    // Create MFA verification session directly
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 8);

    const sessionToken = `mfa_${user.id}_${Date.now()}`;

    const { error: sessionError } = await supabase
      .from('mfa_verification_sessions')
      .insert({
        user_id: user.id,
        session_token: sessionToken,
        verified: true,
        expires_at: expiresAt.toISOString(),
        ip_address: request.headers.get('x-forwarded-for') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        verified_at: new Date().toISOString(),
      } as any);

    if (sessionError) {
      console.error('MFA session creation error:', sessionError);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar sessão MFA' },
        { status: 500 }
      );
    }

    // Log successful verification
    console.info('✅ MFA verification successful', {
      userId: user.id,
      sessionDurationHours: 8,
      expiresAt: expiresAt.toISOString(),
      ip: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    });

    return NextResponse.json({
      success: true,
      message: 'MFA verificado com sucesso',
      expiresAt: expiresAt.toISOString()
    });

  } catch (error) {
    console.error('MFA verification error:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
} 