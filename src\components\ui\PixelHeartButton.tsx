// src/components/ui/pixel-heart-button.tsx
"use client";

import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PixelHeartButtonProps {
  initialIsFollowing?: boolean;
  className?: string;
}

const PixelHeartButton: React.FC<PixelHeartButtonProps> = ({ initialIsFollowing = false, className }) => {
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);

  const handleClick = () => {
    setIsFollowing(!isFollowing);
    // Future functionality to handle actual follow/unfollow action
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        "inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium",
        "bg-secondary text-secondary-foreground border-b-4 border-r-4 border-border",
        "active:border-b-0 active:border-r-0 active:mt-1 active:ml-1",
        "transition-all duration-100 ease-in-out",
        "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring", // Example focus style
        // "pixelated-border", // Custom class for pixel border effect (if needed)
        className
      )}
      style={{
        // Basic pixelated look simulation
        // imageRendering: 'pixelated', // This might not be needed for a simple button
        boxShadow: isFollowing
          ? '2px 2px 0px 0px #DC2626' // Red shadow for followed state
          : '2px 2px 0px 0px #111827', // Dark shadow for unfollowed state
      }}
    >
      <Heart
        className={cn(
          "mr-1 h-4 w-4",
          isFollowing ? 'fill-primary text-primary' : 'fill-muted-foreground text-muted-foreground'
        )}
      />
      <span>{isFollowing ? 'Unfollow' : 'Follow'}</span>
    </button>
  );
};

export default PixelHeartButton;