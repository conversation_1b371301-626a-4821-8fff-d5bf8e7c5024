"use client";

import React, { 
  createContext, 
  useContext, 
  useEffect, 
  useRef, 
  useState, 
  useCallback,
  ReactNode 
} from "react";

// DASHBOARD REDESIGN: Phase 3 - Advanced Features & Polish
// Date: 15/06/2025
// Task: dashboardStyleAdmin003 (FINAL PHASE)
//
// Created AccessibilityProvider with comprehensive accessibility features:
// - Keyboard navigation system with arrow keys and tab management
// - Screen reader announcements with live regions
// - Focus management and trap for modal states
// - Skip links for efficient navigation
// - Reduced motion preferences support
// - WCAG 2.1 AA compliance features

interface AccessibilityContextType {
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => void;
  focusElement: (selector: string) => void;
  skipToContent: () => void;
  prefersReducedMotion: boolean;
  setFocusTrap: (element: HTMLElement | null) => void;
  keyboardNavigation: {
    currentIndex: number;
    navigate: (direction: 'up' | 'down' | 'left' | 'right') => void;
    select: () => void;
    setItems: (items: HTMLElement[]) => void;
  };
}

const AccessibilityContext = createContext<AccessibilityContextType | null>(null);

interface AccessibilityProviderProps {
  children: ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [focusTrapElement, setFocusTrapElement] = useState<HTMLElement | null>(null);
  const [keyboardNavItems, setKeyboardNavItems] = useState<HTMLElement[]>([]);
  const [currentKeyboardIndex, setCurrentKeyboardIndex] = useState(0);
  
  const announcementRef = useRef<HTMLDivElement>(null);
  const skipLinkRef = useRef<HTMLAnchorElement>(null);

  // Detect reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = () => setPrefersReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Screen reader announcement function
  const announceToScreenReader = useCallback((
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    if (!announcementRef.current) return;

    // Clear previous announcement
    announcementRef.current.textContent = '';
    announcementRef.current.setAttribute('aria-live', priority);
    
    // Small delay to ensure screen readers pick up the change
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = message;
      }
    }, 100);

    // Clear after announcement
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = '';
      }
    }, 3000);
  }, []);

  // Focus management
  const focusElement = useCallback((selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      announceToScreenReader(`Focused on ${element.getAttribute('aria-label') || element.textContent || 'element'}`);
    }
  }, [announceToScreenReader]);

  // Skip to main content
  const skipToContent = useCallback(() => {
    const mainContent = document.querySelector('main, [role="main"], #main-content') as HTMLElement;
    if (mainContent) {
      mainContent.focus();
      mainContent.setAttribute('tabindex', '-1');
      announceToScreenReader('Skipped to main content');
    }
  }, [announceToScreenReader]);

  // Focus trap for modals and overlays
  useEffect(() => {
    if (!focusTrapElement) return;

    const focusableElements = focusTrapElement.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;
    
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    };

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        // Close modal/overlay
        setFocusTrapElement(null);
        announceToScreenReader('Modal closed');
      }
    };

    focusTrapElement.addEventListener('keydown', handleTabKey);
    document.addEventListener('keydown', handleEscape);
    
    // Focus first element
    firstElement?.focus();

    return () => {
      focusTrapElement.removeEventListener('keydown', handleTabKey);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [focusTrapElement, announceToScreenReader]);

  // Keyboard navigation system
  const navigateKeyboard = useCallback((direction: 'up' | 'down' | 'left' | 'right') => {
    if (keyboardNavItems.length === 0) return;

    let nextIndex = currentKeyboardIndex;

    switch (direction) {
      case 'up':
        nextIndex = Math.max(0, currentKeyboardIndex - 1);
        break;
      case 'down':
        nextIndex = Math.min(keyboardNavItems.length - 1, currentKeyboardIndex + 1);
        break;
      case 'left':
        nextIndex = Math.max(0, currentKeyboardIndex - 1);
        break;
      case 'right':
        nextIndex = Math.min(keyboardNavItems.length - 1, currentKeyboardIndex + 1);
        break;
    }

    setCurrentKeyboardIndex(nextIndex);
    keyboardNavItems[nextIndex]?.focus();
    
    const label = keyboardNavItems[nextIndex]?.getAttribute('aria-label') || 
                  keyboardNavItems[nextIndex]?.textContent || 
                  'navigation item';
    announceToScreenReader(`${label}, ${nextIndex + 1} of ${keyboardNavItems.length}`);
  }, [keyboardNavItems, currentKeyboardIndex, announceToScreenReader]);

  const selectKeyboardItem = useCallback(() => {
    if (keyboardNavItems[currentKeyboardIndex]) {
      keyboardNavItems[currentKeyboardIndex].click();
    }
  }, [keyboardNavItems, currentKeyboardIndex]);

  // Global keyboard handlers
  useEffect(() => {
    const handleGlobalKeydown = (e: KeyboardEvent) => {
      // Skip if user is typing in an input
      if (e.target instanceof HTMLInputElement || 
          e.target instanceof HTMLTextAreaElement || 
          e.target instanceof HTMLSelectElement) {
        return;
      }

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          navigateKeyboard('up');
          break;
        case 'ArrowDown':
          e.preventDefault();
          navigateKeyboard('down');
          break;
        case 'ArrowLeft':
          e.preventDefault();
          navigateKeyboard('left');
          break;
        case 'ArrowRight':
          e.preventDefault();
          navigateKeyboard('right');
          break;
        case 'Enter':
        case ' ':
          if (keyboardNavItems.length > 0) {
            e.preventDefault();
            selectKeyboardItem();
          }
          break;
        case '?':
          if (e.shiftKey) {
            e.preventDefault();
            announceToScreenReader(
              'Keyboard shortcuts: Arrow keys to navigate, Enter or Space to select, Escape to close menus, Tab to move between sections',
              'assertive'
            );
          }
          break;
      }
    };

    document.addEventListener('keydown', handleGlobalKeydown);
    return () => document.removeEventListener('keydown', handleGlobalKeydown);
  }, [navigateKeyboard, selectKeyboardItem, keyboardNavItems.length]);

  const contextValue: AccessibilityContextType = {
    announceToScreenReader,
    focusElement,
    skipToContent,
    prefersReducedMotion,
    setFocusTrap: setFocusTrapElement,
    keyboardNavigation: {
      currentIndex: currentKeyboardIndex,
      navigate: navigateKeyboard,
      select: selectKeyboardItem,
      setItems: setKeyboardNavItems
    }
  };

  return (
    <AccessibilityContext.Provider value={contextValue}>
      {/* Skip Links */}
      <div className="sr-only focus-within:not-sr-only">
        <a
          ref={skipLinkRef}
          href="#main-content"
          onClick={(e) => {
            e.preventDefault();
            skipToContent();
          }}
          className={`
            fixed top-4 left-4 z-[9999] px-4 py-2 bg-violet-600 text-white rounded-lg
            focus:outline-none focus:ring-2 focus:ring-violet-400 font-mono text-sm
            transform -translate-y-16 focus:translate-y-0 transition-transform duration-200
          `}
        >
          Skip to main content
        </a>
      </div>

      {/* Screen Reader Announcements */}
      <div
        ref={announcementRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />

      {/* Reduced Motion CSS Override */}
      {prefersReducedMotion && (
        <style jsx global>{`
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        `}</style>
      )}

      {children}
    </AccessibilityContext.Provider>
  );
}

// Hook to use accessibility context
export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
}

// Custom hook for keyboard navigation
export function useKeyboardNavigation(items: HTMLElement[] = []) {
  const { keyboardNavigation } = useAccessibility();
  
  useEffect(() => {
    if (items.length > 0) {
      keyboardNavigation.setItems(items);
    }
  }, [items, keyboardNavigation]);

  return keyboardNavigation;
}

// Custom hook for focus trap
export function useFocusTrap(isActive: boolean) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { setFocusTrap } = useAccessibility();

  useEffect(() => {
    if (isActive && containerRef.current) {
      setFocusTrap(containerRef.current);
    } else {
      setFocusTrap(null);
    }

    return () => setFocusTrap(null);
  }, [isActive, setFocusTrap]);

  return containerRef;
}

// Custom hook for screen reader announcements
export function useScreenReader() {
  const { announceToScreenReader } = useAccessibility();
  return announceToScreenReader;
} 