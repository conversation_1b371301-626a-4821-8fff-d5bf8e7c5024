# CriticalPixel Animation & Interaction Guidelines

## 🎬 Animation Philosophy

CriticalPixel uses **subtle, purposeful animations** that enhance user experience without overwhelming the gaming-focused interface. All animations follow the principle of **functional motion** - every animation serves a purpose.

### Core Animation Principles
- **Smooth & Responsive**: 60fps performance target
- **Purposeful**: Animations guide user attention and provide feedback
- **Consistent**: Unified timing and easing across components
- **Respectful**: Honor `prefers-reduced-motion` accessibility setting
- **Gaming-Inspired**: Subtle effects that complement the gaming aesthetic

## ⏱️ Timing & Easing

### Standard Timing Functions
```css
/* Primary easing - smooth and natural */
--ease-primary: cubic-bezier(0.4, 0, 0.2, 1);

/* Fast easing - quick interactions */
--ease-fast: cubic-bezier(0.4, 0, 1, 1);

/* Slow easing - dramatic effects */
--ease-slow: cubic-bezier(0, 0, 0.2, 1);

/* Bounce easing - playful interactions */
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

/* Gaming-inspired easing */
--ease-gaming: cubic-bezier(0.25, 0.46, 0.45, 0.94);
```

### Duration Scale
```css
/* Animation duration system */
--duration-fast: 150ms;      /* Quick feedback */
--duration-normal: 300ms;    /* Standard transitions */
--duration-slow: 500ms;      /* Dramatic effects */
--duration-slower: 750ms;    /* Page transitions */
--duration-slowest: 1000ms;  /* Loading animations */
```

## 🎯 Component Animations

### Button Interactions
```css
/* Standard button hover */
.btn-hover {
  transition: all var(--duration-fast) var(--ease-primary);
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

/* Gradient button animation */
.btn-gradient {
  background-size: 200% 100%;
  transition: all var(--duration-normal) var(--ease-primary);
}

.btn-gradient:hover {
  background-position: right center;
  transform: translateY(-2px);
}

/* Button press feedback */
.btn-press:active {
  transform: translateY(0) scale(0.98);
  transition: transform var(--duration-fast) var(--ease-fast);
}
```

### Card Animations
```css
/* Card hover lift effect */
.card-hover {
  transition: all var(--duration-normal) var(--ease-primary);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(139, 92, 246, 0.2);
}

/* Card entrance animation */
.card-entrance {
  opacity: 0;
  transform: translateY(20px);
  animation: cardEnter var(--duration-slow) var(--ease-primary) forwards;
}

@keyframes cardEnter {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

### Form Interactions
```css
/* Input focus animation */
.input-focus {
  transition: all var(--duration-fast) var(--ease-primary);
}

.input-focus:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  transform: scale(1.01);
}

/* Label float animation */
.label-float {
  transition: all var(--duration-normal) var(--ease-primary);
}

.input-group:focus-within .label-float {
  transform: translateY(-1.5rem) scale(0.85);
  color: var(--text-accent);
}
```

## 🌊 Page Transitions

### Framer Motion Page Transitions
```tsx
// Page transition variants
const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0,
    y: -20,
  }
};

const pageTransition = {
  type: "tween",
  ease: [0.4, 0, 0.2, 1],
  duration: 0.3
};

// Usage in components
<motion.div
  initial="initial"
  animate="in"
  exit="out"
  variants={pageVariants}
  transition={pageTransition}
>
  {children}
</motion.div>
```

### Route Transitions
```css
/* Page fade transition */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--duration-normal) var(--ease-primary);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all var(--duration-normal) var(--ease-primary);
}
```

## 🎮 Gaming-Specific Animations

### Score Counter Animation
```css
/* Animated score counting */
.score-counter {
  font-variant-numeric: tabular-nums;
  transition: all var(--duration-slow) var(--ease-gaming);
}

@keyframes scoreCount {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    color: var(--text-accent);
  }
  100% {
    transform: scale(1);
  }
}

.score-update {
  animation: scoreCount var(--duration-slow) var(--ease-bounce);
}
```

### Progress Bar Animation
```css
/* Animated progress fill */
.progress-fill {
  width: 0%;
  transition: width var(--duration-slow) var(--ease-primary);
  background: linear-gradient(90deg, var(--theme-primary), var(--theme-accent));
}

.progress-fill.animate {
  animation: progressFill 1s var(--ease-primary) forwards;
}

@keyframes progressFill {
  to {
    width: var(--progress-value);
  }
}
```

### Gaming UI Effects
```css
/* Glow pulse effect */
.glow-pulse {
  animation: glowPulse 2s var(--ease-primary) infinite;
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.6);
  }
}

/* Terminal typing effect */
.typing-effect {
  overflow: hidden;
  border-right: 2px solid var(--text-accent);
  animation: typing 3s steps(40) 1s forwards, blink 1s infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: var(--text-accent); }
  51%, 100% { border-color: transparent; }
}
```

## 🎨 Modal & Overlay Animations

### Modal Entrance
```tsx
// Framer Motion modal animation
const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 50,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 50,
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 }
};
```

### Sidebar Slide Animation
```css
/* Sidebar slide-in animation */
.sidebar-enter {
  transform: translateX(100%);
}

.sidebar-enter-active {
  transform: translateX(0);
  transition: transform var(--duration-normal) var(--ease-primary);
}

.sidebar-exit {
  transform: translateX(0);
}

.sidebar-exit-active {
  transform: translateX(100%);
  transition: transform var(--duration-normal) var(--ease-primary);
}
```

## 🔄 Loading Animations

### Shimmer Loading
```css
/* Shimmer skeleton loading */
.shimmer {
  background: linear-gradient(90deg, 
    rgba(71, 85, 105, 0.1) 25%, 
    rgba(139, 92, 246, 0.1) 50%, 
    rgba(71, 85, 105, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

### Spinner Animation
```css
/* Gaming-themed spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(139, 92, 246, 0.3);
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse loading */
.pulse-loader {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.05);
  }
}
```

## 🎯 Micro-Interactions

### Icon Animations
```css
/* Icon hover effects */
.icon-hover {
  transition: all var(--duration-fast) var(--ease-primary);
}

.icon-hover:hover {
  transform: scale(1.1);
  color: var(--text-accent);
}

/* Icon rotation */
.icon-rotate:hover {
  transform: rotate(180deg);
  transition: transform var(--duration-normal) var(--ease-primary);
}
```

### Notification Animations
```css
/* Toast notification slide-in */
.toast-enter {
  transform: translateX(100%);
  opacity: 0;
}

.toast-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all var(--duration-normal) var(--ease-primary);
}

.toast-exit {
  transform: translateX(0);
  opacity: 1;
}

.toast-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--duration-normal) var(--ease-primary);
}
```

## ♿ Accessibility Considerations

### Reduced Motion Support
```css
/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Alternative static states for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .card-hover:hover {
    transform: none;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5);
  }
}
```

### Focus Animations
```css
/* Accessible focus indicators */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.5);
  transition: box-shadow var(--duration-fast) var(--ease-primary);
}

/* Skip link animation */
.skip-link:focus {
  transform: translateY(0);
  transition: transform var(--duration-fast) var(--ease-primary);
}
```

## 📱 Performance Guidelines

### Animation Performance
- **Use `transform` and `opacity`**: Hardware accelerated properties
- **Avoid animating layout properties**: `width`, `height`, `padding`, `margin`
- **Use `will-change`**: For complex animations (remove after completion)
- **Limit concurrent animations**: Maximum 3-4 simultaneous animations
- **Test on low-end devices**: Ensure smooth performance across devices

### Implementation Best Practices
```css
/* Optimize for performance */
.optimized-animation {
  will-change: transform, opacity;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Clean up after animation */
.animation-complete {
  will-change: auto;
}
```

---

*These animation guidelines ensure smooth, purposeful motion that enhances the CriticalPixel gaming experience while maintaining accessibility and performance standards.*
