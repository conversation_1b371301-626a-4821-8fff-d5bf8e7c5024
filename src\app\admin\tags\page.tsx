// src/app/admin/tags/page.tsx
// Admin tag management and analytics dashboard

import { Metadata } from 'next';
import TagsAdminClient from './TagsAdminClient';
import { createServerTagService } from '@/lib/services/tagService';

export const metadata: Metadata = {
  title: 'Tag Management - Admin Dashboard',
  description: 'Manage tags, view analytics, and monitor tag performance.',
};

export default async function TagsAdminPage() {
  const tagService = createServerTagService();

  // Get various tag data for admin overview
  const [
    trendingResult,
    popularResult,
    featuredResult,
    allTagsResult
  ] = await Promise.all([
    tagService.getTrendingTags(10),
    tagService.getPopularTags(20),
    tagService.getFeaturedTags(10),
    tagService.searchTags({ limit: 100, sortBy: 'usage_count', sortOrder: 'desc' })
  ]);

  return (
    <TagsAdminClient
      trendingTags={trendingResult.tags || []}
      popularTags={popularResult.tags || []}
      featuredTags={featuredResult.tags || []}
      allTags={allTagsResult.tags || []}
      totalTags={allTagsResult.total || 0}
    />
  );
}