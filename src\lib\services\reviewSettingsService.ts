import { createClient } from '@/lib/supabase/client';

export interface ReviewSettings {
  enableComments: boolean;
  enableNotifications: boolean;
  makePrivate: boolean;
}

export interface ReviewSettingsResult {
  success: boolean;
  error?: string;
  data?: ReviewSettings;
}

/**
 * Update individual review privacy and comment settings
 */
export async function updateReviewSettings(
  reviewId: string,
  userId: string,
  settings: ReviewSettings
): Promise<ReviewSettingsResult> {
  try {
    const supabase = createClient();

    // Verify user owns the review
    const { data: review, error: fetchError } = await supabase
      .from('reviews')
      .select('author_id')
      .eq('id', reviewId)
      .single();

    if (fetchError) {
      console.error('Error fetching review:', fetchError);
      return {
        success: false,
        error: 'Review not found'
      };
    }

    if (review.author_id !== userId) {
      return {
        success: false,
        error: 'Unauthorized: You can only modify your own reviews'
      };
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Handle privacy setting - update is_private field
    updateData.is_private = settings.makePrivate;
    updateData.privacy_updated_at = new Date().toISOString();

    // NOTE: comment_settings and notification_settings columns do not exist in the reviews table
    // These would need to be stored in a separate table or in an existing JSON column
    // For now, we'll only update the status based on privacy setting

    // Update the review
    const { error: updateError } = await supabase
      .from('reviews')
      .update(updateData)
      .eq('id', reviewId);

    if (updateError) {
      console.error('Error updating review settings:', updateError);
      return {
        success: false,
        error: updateError.message
      };
    }

    return {
      success: true,
      data: settings
    };

  } catch (error) {
    console.error('Unexpected error updating review settings:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get review settings for a specific review
 */
export async function getReviewSettings(
  reviewId: string,
  userId?: string
): Promise<ReviewSettingsResult> {
  try {
    const supabase = createClient();

    const { data: review, error } = await supabase
      .from('reviews')
      .select('status, author_id, enable_comments, is_private')
      .eq('id', reviewId)
      .single();

    if (error) {
      console.error('Error fetching review settings:', error);
      return {
        success: false,
        error: 'Review not found'
      };
    }

    // Extract settings from review data
    const settings: ReviewSettings = {
      enableComments: review.enable_comments ?? true, // Use database value or default to true
      enableNotifications: true, // Default value since notification_settings doesn't exist yet
      makePrivate: review.is_private || false
    };

    return {
      success: true,
      data: settings
    };

  } catch (error) {
    console.error('Unexpected error fetching review settings:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update review privacy status (public/private)
 */
export async function updateReviewPrivacy(
  reviewId: string,
  userId: string,
  isPrivate: boolean
): Promise<ReviewSettingsResult> {
  try {
    const supabase = createClient();

    // Verify user owns the review
    const { data: review, error: fetchError } = await supabase
      .from('reviews')
      .select('author_id')
      .eq('id', reviewId)
      .single();

    if (fetchError || review.author_id !== userId) {
      return {
        success: false,
        error: 'Unauthorized or review not found'
      };
    }

    // Update privacy field and status
    const updateData: any = {
      is_private: isPrivate,
      privacy_updated_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // When making public, set status to published so it appears in Published tab
    if (!isPrivate) {
      updateData.status = 'published';
    }

    const { error: updateError } = await supabase
      .from('reviews')
      .update(updateData)
      .eq('id', reviewId);

    if (updateError) {
      console.error('Error updating review privacy:', updateError);
      return {
        success: false,
        error: updateError.message
      };
    }

    return {
      success: true,
      data: {
        enableComments: true, // Default, would need to fetch actual value
        enableNotifications: true, // Default, would need to fetch actual value
        makePrivate: isPrivate
      }
    };

  } catch (error) {
    console.error('Unexpected error updating review privacy:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update review comment settings
 */
export async function updateReviewCommentSettings(
  reviewId: string,
  userId: string,
  enableComments: boolean
): Promise<ReviewSettingsResult> {
  try {
    const supabase = createClient();

    // Verify user owns the review
    const { data: review, error: fetchError } = await supabase
      .from('reviews')
      .select('author_id')
      .eq('id', reviewId)
      .single();

    if (fetchError || review.author_id !== userId) {
      return {
        success: false,
        error: 'Unauthorized or review not found'
      };
    }

    // NOTE: comment_settings column doesn't exist in the reviews table yet
    // For now, we'll just update the timestamp to acknowledge the settings change
    const { error: updateError } = await supabase
      .from('reviews')
      .update({ 
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId);

    if (updateError) {
      console.error('Error updating comment settings:', updateError);
      return {
        success: false,
        error: updateError.message
      };
    }

    return {
      success: true,
      data: {
        enableComments,
        enableNotifications: true, // Default, would need to fetch actual value
        makePrivate: false // Default, would need to fetch actual value
      }
    };

  } catch (error) {
    console.error('Unexpected error updating comment settings:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}
