import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { motion, AnimatePresence } from "framer-motion";

import { filterSpecialEditions } from "@/lib/utils/gameFilters";
import { useDebounce } from "@/lib/hooks/useDebounce";
import './style/igdb-font-fixes.css';

interface Game {
  id: string;
  name: string;
  cover?: {
    id: string;
    url: string;
  };
  releaseDate?: number;
  platforms?: string[];
  genres?: string[];
  developers?: {
    id: string;
    name: string;
  }[];
  rating?: number;
  summary?: string;
}

interface GameSearchComboboxProps {
  onGameSelect: (game: Game | null) => void;
  initialSearchQuery?: string;
}

export function GameSearchCombobox({
  onGameSelect,
  initialSearchQuery: initialSearchQueryProp = ""
}: GameSearchComboboxProps) {
  const [internalQuery, setInternalQuery] = useState(initialSearchQueryProp);
  const [activeGameSelection, setActiveGameSelection] = useState<Game | null>(null);
  const [ghostSuggestion, setGhostSuggestion] = useState<string>('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Match navbar's 300ms debounce for consistency
  const debouncedQuery = useDebounce(internalQuery, 300);

  const prevInitialSearchQueryPropRef = useRef(initialSearchQueryProp);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Sync with parent prop changes
  useEffect(() => {
    if (initialSearchQueryProp !== prevInitialSearchQueryPropRef.current) {
      setInternalQuery(initialSearchQueryProp);
      // Clear selection if parent cleared the search query or changed to a different game
      if (activeGameSelection && (
        !initialSearchQueryProp ||
        initialSearchQueryProp !== activeGameSelection.name
      )) {
        setActiveGameSelection(null);
      }
      // If parent cleared the search query, also clear our internal state
      if (!initialSearchQueryProp) {
        setActiveGameSelection(null);
        setGhostSuggestion('');
        setHighlightedIndex(-1);
      }
      prevInitialSearchQueryPropRef.current = initialSearchQueryProp;
    }
  }, [initialSearchQueryProp, activeGameSelection]);


  // Show results container when typing and no game is selected
  const shouldShowResults = useMemo(() => {
    return internalQuery.length >= 2 && !activeGameSelection;
  }, [internalQuery, activeGameSelection]);

  // Handle game selection from results
  const handleGameClick = useCallback((game: Game) => {
    setActiveGameSelection(game);
    setInternalQuery(game.name);
    onGameSelect(game);
    setHighlightedIndex(-1);
  }, [onGameSelect]);


  // Query configuration with better caching
  const { data: games = [], isLoading, isError } = useQuery<Game[], Error>({
    queryKey: ["gameSearch", debouncedQuery],
    queryFn: async () => {
      if (debouncedQuery.length < 2) return [];
      
      try {
        const response = await fetch('/api/igdb/search', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title: debouncedQuery }),
        });
        
        if (!response.ok) throw new Error("Search failed");
        return response.json();
      } catch (error) {
        console.error("Error fetching games:", error);
        return [];
      }
    },
    enabled: debouncedQuery.length >= 2,
    staleTime: 30000, // Cache for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });

  const filteredGames = useMemo(() => {
    return filterSpecialEditions(games) as Game[];
  }, [games]);

  // Ghost suggestion logic - update immediately when results change
  useEffect(() => {
    if (!internalQuery || activeGameSelection) {
      setGhostSuggestion('');
      return;
    }

    if (filteredGames.length > 0) {
      const firstMatch = filteredGames[0];
      if (firstMatch.name.toLowerCase().startsWith(internalQuery.toLowerCase())) {
        setGhostSuggestion(firstMatch.name);
      } else {
        setGhostSuggestion('');
      }
    } else {
      setGhostSuggestion('');
    }
  }, [filteredGames, internalQuery, activeGameSelection]);

  const handleSelectionChange = useCallback((game: Game | null) => {
    if (game) {
      setActiveGameSelection(game);
      setInternalQuery(game.name);
      onGameSelect(game);
      setGhostSuggestion('');
      setHighlightedIndex(-1);
    } else {
      setActiveGameSelection(null);
      setInternalQuery("");
      onGameSelect(null);
      setGhostSuggestion('');
      setHighlightedIndex(-1);
    }
  }, [onGameSelect]);

  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newQueryValue = event.target.value;
    setInternalQuery(newQueryValue);
    setHighlightedIndex(-1); // Reset highlight on input change

    // Clear selection if user modifies the input
    if (activeGameSelection && newQueryValue !== activeGameSelection.name) {
      setActiveGameSelection(null);
      onGameSelect(null);
    }
    
    // Clear selection if input is emptied
    if (!newQueryValue && activeGameSelection) {
      setActiveGameSelection(null);
      onGameSelect(null);
    }
  }, [activeGameSelection, onGameSelect]);

  // Enhanced keyboard navigation
  const handleInputKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    // Tab or right arrow to accept ghost suggestion
    if (ghostSuggestion && (e.key === 'Tab' || (e.key === 'ArrowRight' && 
        searchInputRef.current?.selectionStart === internalQuery.length))) {
      e.preventDefault();
      setInternalQuery(ghostSuggestion);
      setGhostSuggestion('');
      // Don't select the game yet, let user confirm with Enter
    } 
    // Navigate results with arrow keys
    else if (e.key === 'ArrowDown' && filteredGames.length > 0) {
      e.preventDefault();
      setHighlightedIndex(prev => 
        prev < filteredGames.length - 1 ? prev + 1 : 0
      );
    } 
    else if (e.key === 'ArrowUp' && filteredGames.length > 0) {
      e.preventDefault();
      setHighlightedIndex(prev => 
        prev > 0 ? prev - 1 : filteredGames.length - 1
      );
    }
    // Select highlighted game with Enter
    else if (e.key === 'Enter' && highlightedIndex >= 0 && filteredGames[highlightedIndex]) {
      e.preventDefault();
      handleSelectionChange(filteredGames[highlightedIndex]);
    }
    // Close results with Escape
    else if (e.key === 'Escape') {
      searchInputRef.current?.blur();
    }
  }, [ghostSuggestion, internalQuery, highlightedIndex, filteredGames, handleSelectionChange]);


  const showLoadingMessage = shouldShowResults && isLoading;
  const showResults = shouldShowResults && !isLoading && !isError && filteredGames.length > 0;
  const showEmpty = shouldShowResults && !isLoading && !isError && filteredGames.length === 0;



  // Render smooth results container
  const renderResultsContainer = () => {
    return (
      <AnimatePresence>
        {shouldShowResults && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -10 }}
            animate={{ opacity: 1, height: "auto", y: 0 }}
            exit={{ opacity: 0, height: 0, y: -10 }}
            transition={{
              duration: 0.3,
              ease: [0.4, 0, 0.2, 1],
              height: { duration: 0.4 }
            }}
            className="mt-2 bg-slate-800 border border-slate-700 rounded-lg shadow-lg overflow-hidden"
          >
            <div className="max-h-64 overflow-y-auto">
              {/* Loading State */}
              {showLoadingMessage && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-3 text-slate-400 text-sm"
                >
                  <div className="flex items-center">
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Searching for "{debouncedQuery}"...
                  </div>
                </motion.div>
              )}

              {/* Error State */}
              {isError && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-3 text-red-400 text-sm"
                >
                  Error fetching games. Please try again.
                </motion.div>
              )}

              {/* Results */}
              {showResults && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 }}
                >
                  {filteredGames.map((game, index) => {
                    const isHighlighted = index === highlightedIndex;
                    const releaseYear = game.releaseDate
                      ? new Date(game.releaseDate * 1000).getFullYear()
                      : null;
                    const developer = game.developers?.[0]?.name || null;
                    const platforms = game.platforms?.slice(0, 2).join(', ') +
                                     (game.platforms && game.platforms.length > 2 ? ', ...' : '') || null;

                    return (
                      <motion.div
                        key={game.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        onClick={() => handleGameClick(game)}
                        className={`relative cursor-pointer select-none p-3 transition-colors ${
                          isHighlighted
                            ? 'bg-slate-700 text-slate-200'
                            : 'text-slate-300 hover:bg-slate-700/50'
                        }`}
                      >
                        <div className="flex items-center">
                          <div className="flex-grow overflow-hidden">
                            <div className="flex items-center">
                              <span className="block truncate text-sm font-normal">
                                {game.name}
                              </span>
                              {releaseYear && (
                                <span className="ml-2 text-xs px-2 py-0.5 rounded-md bg-slate-600/50 text-slate-300">
                                  {releaseYear}
                                </span>
                              )}
                            </div>
                            {(developer || platforms) && (
                              <div className={`text-xs truncate mt-1 ${
                                isHighlighted ? 'text-slate-400' : 'text-slate-500'
                              }`}>
                                {[developer, platforms].filter(Boolean).join(' • ')}
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </motion.div>
              )}

              {/* Empty State */}
              {showEmpty && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-3 text-slate-400 text-sm"
                >
                  No games found matching "{debouncedQuery}"
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  };

  return (
    <div className="relative w-full game-search-combobox">
      {/* Use site's consistent styling for the main input container */}
      <div ref={containerRef} className="relative tyq-input-container font-lato">
        <div className="relative">
          <input
            ref={searchInputRef}
            className="tyq-input w-full pr-10 border-none focus:ring-0 bg-transparent relative z-10 font-lato"
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            value={internalQuery}
            placeholder="Search for a game (min. 2 chars)..."
            autoComplete="off"
            spellCheck={false}
          />

          {/* Ghost suggestion overlay - only show when results are NOT visible */}
          {ghostSuggestion && internalQuery &&
           ghostSuggestion.toLowerCase().startsWith(internalQuery.toLowerCase()) &&
           !activeGameSelection && !shouldShowResults && (
            <div
              className="absolute inset-0 pointer-events-none flex items-center pl-3 pr-10"
              aria-hidden="true"
            >
              <span className="text-sm leading-5 font-mono">
                <span className="invisible">{internalQuery}</span>
                <span className="text-slate-500">
                  {ghostSuggestion.slice(internalQuery.length)}
                </span>
              </span>
            </div>
          )}
        </div>

        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {isLoading && debouncedQuery.length >= 2 && !activeGameSelection ? (
            <LoaderIcon className="h-4 w-4 text-slate-400 animate-spin" />
          ) : activeGameSelection ? (
            <XMarkIcon
              className="h-4 w-4 text-slate-400 hover:text-slate-300 cursor-pointer transition-colors"
              aria-hidden="true"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                handleSelectionChange(null);
              }}
            />
          ) : (
            <MagnifyingGlassIcon className="h-4 w-4 text-slate-400" aria-hidden="true" />
          )}
        </div>
      </div>

      {/* Render smooth results container */}
      {renderResultsContainer()}
    </div>
  );
}

// Loader icon component
const LoaderIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);
