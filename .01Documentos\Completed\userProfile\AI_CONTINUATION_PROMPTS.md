# 🤖 Prompts para IA - Continuação do Módulo YouTube

## 🎯 PROMPT PRINCIPAL PARA PRÓXIMA IA

```
Olá! Preciso continuar a implementação do módulo YouTube para o CriticalPixel.

CONTEXTO ATUAL:
- Sistema de perfis de usuário já existe
- Módulo YouTube foi parcialmente implementado
- Componentes visuais estão prontos
- Falta configuração de API e integração real

ARQUIVOS JÁ IMPLEMENTADOS:
- src/types/user-content.ts (tipos completos)
- src/app/u/actions-content.ts (server actions)
- src/hooks/useUserContent.ts (hook com cache)
- src/components/userprofile/YouTubeModule.tsx (UI completa)
- src/components/userprofile/UserContentModules.tsx (integração)

PRÓXIMOS PASSOS CRÍTICOS:
1. Configurar YouTube API Key
2. Criar tabelas do banco de dados
3. Implementar dashboard de configuração
4. Integrar com ProfilePageClient real
5. Testes básicos

DOCUMENTAÇÃO DISPONÍVEL:
- .01Documentos/YOUTUBE_MODULE_IMPLEMENTATION_GUIDE.md
- USER_CONTENT_SYSTEM_README.md

Por favor, comece pela FASE 1 do guia de implementação.
```

---

## 📋 PROMPTS POR FASE

### **FASE 1.1: Configurar YouTube API Key**

```
Preciso configurar a YouTube Data API v3 para o projeto CriticalPixel.

SITUAÇÃO ATUAL:
- Server actions já implementados em src/app/u/actions-content.ts
- Função fetchYouTubeChannelData() existe mas precisa de API key
- Variável YOUTUBE_API_KEY não está configurada

TAREFAS ESPECÍFICAS:
1. Verificar se existe .env.local no projeto
2. Adicionar YOUTUBE_API_KEY=your_key_here
3. Testar função fetchYouTubeChannelData() com canal real
4. Validar que dados são retornados corretamente
5. Implementar tratamento de erro para quota excedida

TESTE SUGERIDO:
- Usar canal: https://www.youtube.com/@MrBeast
- Verificar se retorna dados de canal e vídeos
- Confirmar que cache funciona

VALIDAÇÃO:
- API key funciona sem erros
- Dados de canal são retornados
- Lista de vídeos é populada
- Cache localStorage funciona
```

### **FASE 1.2: Criar Tabelas do Banco**

```
Preciso criar as tabelas do banco de dados para o módulo YouTube.

CONTEXTO:
- Projeto usa Supabase/PostgreSQL
- Tabelas users e user_content_preferences já existem
- SQL completo está no guia de implementação

TAREFAS:
1. Conectar ao banco Supabase
2. Executar SQL para criar user_youtube_data
3. Executar SQL para criar user_youtube_videos
4. Criar índices de performance
5. Alterar user_content_preferences (adicionar youtube_module)
6. Testar inserção de dados mock

SQL A EXECUTAR:
[Copiar SQL do guia de implementação]

VALIDAÇÃO:
- Tabelas criadas sem erro
- Relacionamentos funcionam
- Índices melhoram performance
- Inserção/consulta funciona
```

### **FASE 1.3: Dashboard de Configuração**

```
Preciso criar formulário no dashboard para conectar canal YouTube.

CONTEXTO:
- Dashboard existe em src/app/dashboard
- Usuários precisam inserir URL do canal
- Validação em tempo real necessária
- Integração com server actions existentes

TAREFAS:
1. Criar src/components/dashboard/YouTubeChannelConfig.tsx
2. Implementar formulário com validação de URL
3. Conectar com updateYouTubeChannel server action
4. Adicionar estados: loading, success, error
5. Integrar no dashboard principal
6. Testar fluxo completo

FUNCIONALIDADES NECESSÁRIAS:
- Input para URL do canal
- Validação de formato de URL
- Botão "Conectar Canal"
- Feedback visual (loading spinner)
- Mensagens de sucesso/erro
- Configurações de visibilidade

VALIDAÇÃO:
- Formulário aceita URLs válidas
- Rejeita URLs inválidas
- Dados salvos no banco
- Feedback visual funciona
```

### **FASE 1.4: Integração Real**

```
Preciso integrar o módulo YouTube com ProfilePageClient existente.

CONTEXTO:
- ProfilePageClient já funciona
- useUserContent hook foi atualizado
- YouTubeModule está implementado
- Falta conectar dados reais

TAREFAS:
1. Localizar ProfilePageClient atual
2. Verificar estrutura de dados do perfil
3. Adicionar youtubeChannelUrl aos dados do usuário
4. Modificar useUserContent para usar dados reais
5. Testar exibição completa
6. Verificar temas e responsividade

INTEGRAÇÃO NECESSÁRIA:
```tsx
const { youtubeData, refetchYouTube } = useUserContent(
  profile.id, 
  currentUserId,
  profile.youtubeChannelUrl // Do banco de dados
);
```

VALIDAÇÃO:
- Aba YouTube aparece corretamente
- Dados reais são carregados
- Cache funciona
- Temas aplicados
- Responsivo em mobile
```

---

## 🔧 PROMPTS PARA MELHORIAS

### **Filtros e Pesquisa**

```
Preciso adicionar filtros e pesquisa ao YouTubeModule.

CONTEXTO:
- YouTubeModule já exibe grid de vídeos
- Usuários querem filtrar por data/popularidade
- Pesquisa por título necessária
- Manter performance

FUNCIONALIDADES:
1. Barra de pesquisa (debounced)
2. Filtro por data (recentes/antigos)
3. Filtro por popularidade (views)
4. Filtro por duração (curto/médio/longo)
5. Reset de filtros
6. Persistir no localStorage

IMPLEMENTAÇÃO:
- Adicionar controles no topo do YouTubeModule
- Lógica de filtragem no frontend
- Debounce de 300ms para pesquisa
- Animações suaves para mudanças

VALIDAÇÃO:
- Filtros funcionam corretamente
- Pesquisa é responsiva
- Performance mantida
- UI intuitiva
```

### **Paginação de Vídeos**

```
Preciso implementar paginação para vídeos do YouTube.

CONTEXTO:
- Canais podem ter centenas de vídeos
- Carregar todos impacta performance
- Usuários querem ver vídeos antigos
- UX deve ser fluida

IMPLEMENTAÇÃO:
1. Modificar server actions para paginação
2. Botão "Carregar mais" no final
3. Loading state para paginação
4. Cache por página
5. Infinite scroll (opcional)

ESPECIFICAÇÕES:
- 12 vídeos por página
- Botão no final da lista
- Spinner durante carregamento
- Manter vídeos carregados
- Cache inteligente

VALIDAÇÃO:
- Paginação suave
- Performance mantida
- Loading claro
- Cache otimizado
```

---

## 🧪 PROMPTS PARA TESTES

### **Testes Básicos**

```
Preciso implementar testes para o módulo YouTube.

CONTEXTO:
- Sistema crítico para UX
- Integração com API externa
- Múltiplos pontos de falha
- Performance importante

TESTES NECESSÁRIOS:
1. Testes unitários dos componentes
2. Testes de integração
3. Testes de performance
4. Testes de acessibilidade
5. Testes de erro

ARQUIVOS DE TESTE:
- __tests__/YouTubeModule.test.tsx
- __tests__/integration/youtube-flow.test.tsx
- __tests__/performance/youtube-performance.test.tsx

CENÁRIOS CRÍTICOS:
- Canal conectado com sucesso
- Falha na API
- Cache funcionando
- Modal responsivo
- Filtros funcionais

VALIDAÇÃO:
- Cobertura > 80%
- Cenários de erro cobertos
- Performance dentro dos limites
- Acessibilidade validada
```

### **Testes de Performance**

```
Preciso validar performance do módulo YouTube.

MÉTRICAS ALVO:
- First Contentful Paint < 1.5s
- Largest Contentful Paint < 2.5s
- Time to Interactive < 3.0s
- Cumulative Layout Shift < 0.1

TESTES:
1. Carregamento inicial
2. Cache hit/miss ratio
3. Uso de memória
4. Rate limiting
5. Lazy loading

FERRAMENTAS:
- Lighthouse
- Chrome DevTools
- Jest performance tests
- Bundle analyzer

VALIDAÇÃO:
- Métricas dentro do alvo
- Cache otimizado
- Memória controlada
- Bundle size aceitável
```

---

## 🚀 PROMPTS PARA FUNCIONALIDADES AVANÇADAS

### **YouTube Shorts**

```
Preciso adicionar suporte a YouTube Shorts.

CONTEXTO:
- Shorts são vídeos < 60s verticais
- Precisam layout diferenciado
- Crescente popularidade
- UX específica

IMPLEMENTAÇÃO:
1. Detectar Shorts (duração < 60s)
2. Layout especial (vertical)
3. Badge "Short"
4. Player otimizado
5. Filtro específico

ESPECIFICAÇÕES:
- Grid mais estreito para Shorts
- Badge visual distintivo
- Modal adaptado para vertical
- Filtro "Apenas Shorts"
- Estatísticas específicas

VALIDAÇÃO:
- Shorts identificados corretamente
- Layout vertical funciona
- Player se adapta
- Filtro funciona
```

### **Playlists Destacadas**

```
Preciso adicionar suporte a playlists do YouTube.

CONTEXTO:
- Criadores organizam em playlists
- Importante para descoberta
- Valor adicional ao perfil
- Showcase de organização

IMPLEMENTAÇÃO:
1. Buscar playlists via API
2. Seção dedicada
3. Preview de vídeos
4. Links para playlist completa
5. Cache de playlists

FUNCIONALIDADES:
- Até 6 playlists principais
- Preview com 3-4 vídeos
- Thumbnail da playlist
- Contador de vídeos
- Link direto para YouTube

VALIDAÇÃO:
- Playlists carregadas
- Previews funcionam
- Links corretos
- Performance mantida
```

---

## 📊 PROMPTS PARA MONITORAMENTO

### **Analytics e Métricas**

```
Preciso implementar monitoramento para o módulo YouTube.

CONTEXTO:
- Funcionalidade nova e crítica
- API externa com limitações
- Preciso entender uso
- Identificar problemas rapidamente

MÉTRICAS IMPORTANTES:
1. Performance (tempo de carregamento)
2. Uso (usuários com canal conectado)
3. Erros (falhas de API, cache)
4. Engagement (cliques em vídeos)
5. Quota da API (uso diário)

IMPLEMENTAÇÃO:
- Logging estruturado
- Métricas customizadas
- Alertas para erros
- Dashboard de monitoramento
- Tracking de eventos

VALIDAÇÃO:
- Métricas coletadas corretamente
- Alertas funcionam
- Dashboard informativo
- Performance monitorada
```

### **Alertas e Monitoramento**

```
Preciso configurar alertas para o módulo YouTube.

CENÁRIOS DE ALERTA:
1. Quota da API baixa (< 20%)
2. Taxa de erro alta (> 5%)
3. Performance degradada
4. Cache hit ratio baixo
5. Canais quebrados

IMPLEMENTAÇÃO:
- Alertas automáticos
- Notificações por email/Slack
- Dashboard de status
- Logs estruturados
- Métricas em tempo real

FERRAMENTAS:
- Console logging
- Custom metrics
- Error tracking
- Performance monitoring
- API quota tracking

VALIDAÇÃO:
- Alertas disparam corretamente
- Notificações chegam
- Dashboard atualiza
- Logs são úteis
```

---

## 🔄 PROMPTS PARA MANUTENÇÃO

### **Rotina de Manutenção**

```
Preciso estabelecer rotina de manutenção para o módulo YouTube.

CONTEXTO:
- Sistema depende de API externa
- Monitoramento contínuo necessário
- Usuários dependem da funcionalidade
- Performance deve ser mantida

TAREFAS RECORRENTES:

SEMANAIS:
- Verificar quota da API
- Monitorar erros
- Revisar cache performance
- Verificar canais quebrados

MENSAIS:
- Atualizar dependências
- Revisar logs de erro
- Otimizar queries
- Análise de uso

TRIMESTRAIS:
- Revisar limites da API
- Avaliar novas funcionalidades
- Otimizações de performance
- Feedback dos usuários

VALIDAÇÃO:
- Procedimentos documentados
- Alertas automáticos
- Backup/restore funciona
- Equipe treinada
```

---

## 🎯 PROMPT DE FINALIZAÇÃO

```
Preciso finalizar e documentar o módulo YouTube.

CHECKLIST FINAL:
- [ ] API configurada e funcionando
- [ ] Banco de dados criado
- [ ] Dashboard implementado
- [ ] Integração real funcionando
- [ ] Testes básicos passando
- [ ] Performance validada
- [ ] Documentação atualizada
- [ ] Deploy realizado

DOCUMENTAÇÃO:
1. Atualizar README principal
2. Documentar configuração
3. Guia de troubleshooting
4. Changelog de versão
5. Guia para próximas funcionalidades

VALIDAÇÃO FINAL:
- Sistema funciona end-to-end
- Performance dentro dos limites
- Usuários conseguem usar
- Monitoramento ativo
- Documentação completa

PRÓXIMOS PASSOS:
- Coletar feedback dos usuários
- Planejar melhorias
- Monitorar métricas
- Iterar baseado no uso real
```

---

*Prompts criados em Janeiro 2025 - CriticalPixel YouTube Module v1.0* 