// lib/services/performanceSurveyService.ts
// FIREBASE IMPORTS REMOVED - PLACEHOLDER PERFORMANCE SURVEY SERVICE
// TODO: Replace Firebase user validation with Supabase authentication when ready
import { createClient } from '@/lib/supabase/client';
import type { PerformanceSurveyData } from '@/components/review-form/performanceSurvey';

export interface PerformanceSurveyRecord {
  id?: string;
  user_id: string;
  user_email?: string;
  game_title?: string;
  platform: string;
  created_at?: string;
  updated_at?: string;

  // Hardware fields (snake_case from database)
  device_type: string;
  laptop_model?: string | null;
  handheld_model?: string | null;
  cpu?: string | null;
  gpu?: string | null;
  total_memory?: number | null;
  memory_gen?: string | null;
  memory_speed?: number | null;

  // Performance fields (snake_case from database)
  fps_average?: number | null;
  smoothness?: number | null;
  resolution?: string | null;
  ultrawide?: boolean | null;

  // Feature fields (snake_case from database)
  frame_gen?: boolean | null;
  frame_gen_type?: string | null;
  upscale?: boolean | null;
  upscale_type?: string | null;
  upscale_preset?: string | null;
}

export interface SavePerformanceSurveyParams {
  surveyData: PerformanceSurveyData;
  userId: string;
  userEmail?: string;
  gameTitle?: string;
  platform: string;
}

/**
 * Save performance survey data to Supabase
 */
export async function savePerformanceSurvey({
  surveyData,
  userId,
  userEmail,
  gameTitle,
  platform
}: SavePerformanceSurveyParams): Promise<{ success: boolean; data?: PerformanceSurveyRecord; error?: string }> {
  try {
    // PLACEHOLDER: Firebase user validation removed
    // TODO: Replace with Supabase authentication when ready
    
    // Temporary: Allow operation without authentication (for development)
    // In production, this should be replaced with proper Supabase auth validation

    // Helper function to convert memory string to integer
    const convertMemoryToInt = (memory: string): number | null => {
      if (!memory) return null;
      const match = memory.match(/(\d+)/);
      return match ? parseInt(match[1]) : null;
    };

    // Helper function to convert FPS string to number
    const convertFpsToNumber = (fps: string): number | null => {
      if (!fps) return null;
      if (fps === 'under-30') return 25;
      if (fps === '144+') return 144;
      const match = fps.match(/(\d+)/);
      return match ? parseFloat(match[1]) : null;
    };

    // Helper function to convert smoothness string to integer
    const convertSmoothnessToInt = (smoothness: string): number | null => {
      if (!smoothness) return null;
      if (smoothness === 'yes') return 1; // Smooth
      if (smoothness === 'traversal-stuttering') return 2; // Minor stutters
      if (smoothness === 'no') return 3; // Choppy
      return null;
    };

    // Helper function to convert memory speed string to integer
    const convertMemorySpeedToInt = (speed: string): number | null => {
      if (!speed) return null;
      const match = speed.match(/(\d+)/);
      return match ? parseInt(match[1]) : null;
    };

    // Prepare data for database
    const surveyRecord: Omit<PerformanceSurveyRecord, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      user_email: userEmail,
      game_title: gameTitle,
      platform,
      device_type: surveyData.deviceType,
      laptop_model: surveyData.laptopModel || null,
      handheld_model: surveyData.handheldModel || null,
      cpu: surveyData.cpu || null,
      gpu: surveyData.gpu || null,
      total_memory: convertMemoryToInt(surveyData.totalMemory),
      memory_gen: surveyData.memoryGen || null,
      memory_speed: convertMemorySpeedToInt(surveyData.memorySpeed),
      frame_gen: surveyData.frameGen === 'yes',
      frame_gen_type: surveyData.frameGenType || null,
      upscale: surveyData.upscale === 'yes',
      upscale_type: surveyData.upscaleType || null,
      upscale_preset: surveyData.upscalePreset || null,
      resolution: surveyData.resolution || null,
      ultrawide: surveyData.ultrawide === 'yes' || surveyData.ultrawide === 'display-native',
      fps_average: convertFpsToNumber(surveyData.fpsAverage),
      smoothness: convertSmoothnessToInt(surveyData.smoothness)
    };

    // Insert into Supabase
    const supabase = createClient();
    const { data, error } = await supabase
      .from('performance_surveys')
      .insert([surveyRecord])
      .select()
      .single();

    if (error) {
      console.error('Error saving performance survey:', error);
      return {
        success: false,
        error: 'Failed to save performance survey. Please try again.'
      };
    }

    return {
      success: true,
      data: data as PerformanceSurveyRecord
    };

  } catch (error) {
    console.error('Unexpected error saving performance survey:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    };
  }
}

/**
 * Get user's performance surveys
 */
export async function getUserPerformanceSurveys(userId: string): Promise<{ success: boolean; data?: PerformanceSurveyRecord[]; error?: string }> {
  try {
    // PLACEHOLDER: Firebase user validation removed
    // TODO: Replace with Supabase authentication when ready
    
    // Temporary: Allow operation without authentication (for development)
    // In production, this should be replaced with proper Supabase auth validation

    if (!userId) {
      console.warn('getUserPerformanceSurveys called with no userId');
      return {
        success: true,
        data: []
      };
    }

    let supabase;
    try {
      supabase = createClient();
    } catch (clientError) {
      console.error('Failed to create Supabase client:', clientError);
      return {
        success: false,
        error: 'Database connection failed.'
      };
    }

    const { data, error } = await supabase
      .from('performance_surveys')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase query error fetching performance surveys:', {
        message: error.message || 'Database error',
        details: error.details || 'No details available',
        hint: error.hint || 'No hint available',
        code: error.code || 'No code available',
        userId: userId
      });
      return {
        success: false,
        error: 'Failed to fetch performance surveys.'
      };
    }

    return {
      success: true,
      data: data as PerformanceSurveyRecord[]
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : 'No stack trace';
    console.error('Unexpected error in getUserPerformanceSurveys:', {
      message: errorMessage,
      stack: errorStack,
      errorType: typeof error,
      errorConstructor: error?.constructor?.name,
      userId: userId
    });
    return {
      success: false,
      error: 'An unexpected error occurred.'
    };
  }
}

/**
 * Check if user has already submitted a performance survey for a specific game
 */
export async function hasUserSubmittedSurveyForGame(userId: string, gameTitle: string): Promise<{ success: boolean; hasSubmitted?: boolean; error?: string }> {
  try {
    // PLACEHOLDER: Firebase user validation removed
    // TODO: Replace with Supabase authentication when ready
    
    // Temporary: Allow operation without authentication (for development)
    // In production, this should be replaced with proper Supabase auth validation

    const supabase = createClient();
    const { data, error } = await supabase
      .from('performance_surveys')
      .select('id')
      .eq('user_id', userId)
      .eq('game_title', gameTitle)
      .limit(1);

    if (error) {
      console.error('Error checking performance survey:', error);
      return {
        success: false,
        error: 'Failed to check performance survey status.'
      };
    }

    return {
      success: true,
      hasSubmitted: data && data.length > 0
    };

  } catch (error) {
    console.error('Unexpected error checking performance survey:', error);
    return {
      success: false,
      error: 'An unexpected error occurred.'
    };
  }
}

/**
 * Delete a performance survey
 */
export async function deletePerformanceSurvey(surveyId: string, userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // PLACEHOLDER: Firebase user validation removed
    // TODO: Replace with Supabase authentication when ready
    
    // Temporary: Allow operation without authentication (for development)
    // In production, this should be replaced with proper Supabase auth validation

    const supabase = createClient();
    const { error } = await supabase
      .from('performance_surveys')
      .delete()
      .eq('id', surveyId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting performance survey:', error);
      return {
        success: false,
        error: 'Failed to delete performance survey.'
      };
    }

    return { success: true };

  } catch (error) {
    console.error('Unexpected error deleting performance survey:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    };
  }
}
