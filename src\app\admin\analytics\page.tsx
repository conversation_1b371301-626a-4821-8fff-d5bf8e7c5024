/**
 * Admin Analytics Dashboard Page
 * Comprehensive analytics and metrics for site administration
 * Created: 18/01/2025 - Sprint 3 Milestone 3.2
 */

'use client';

import { AdminLayout } from '@/components/admin/AdminLayout';
import { useState, useEffect } from 'react';
import { useAuthContext } from '@/contexts/auth-context';
import { getSiteAnalytics, SiteAnalytics } from '@/lib/admin/analyticsService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  FileText, 
  Eye, 
  Heart, 
  MessageSquare, 
  TrendingUp, 
  Calendar,
  Download,
  RefreshCw,
  BarChart3,
  Activity
} from 'lucide-react';
import { format } from 'date-fns';

export default function AdminAnalyticsPage() {
  const { user, isAdmin } = useAuthContext();
  const [analytics, setAnalytics] = useState<SiteAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    end: new Date()
  });

  const loadAnalytics = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await getSiteAnalytics(user.id, dateRange.start, dateRange.end);
      if (data) {
        setAnalytics(data);
      } else {
        setError('Failed to load analytics data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [user?.id, dateRange]);

  const handleRefresh = () => {
    loadAnalytics();
  };

  const handleExport = () => {
    if (!analytics) return;
    
    const exportData = {
      generatedAt: new Date().toISOString(),
      dateRange,
      analytics
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <AdminLayout
        title="Analytics Dashboard"
        description="Site performance and user engagement metrics"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Analytics Dashboard' }
        ]}
      >
        <Card className="w-full max-w-lg mx-auto shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-red-500 font-mono text-xl tracking-tight">
              <span className="text-red-400">&lt;</span>
              <span className="text-red-300 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Error Loading Analytics</span>
              <span className="text-red-400">/&gt;</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 font-mono text-sm text-muted-foreground">{error}</p>
            <Button onClick={handleRefresh} variant="outline" className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  if (!analytics) {
    return (
      <AdminLayout
        title="Analytics Dashboard"
        description="Site performance and user engagement metrics"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Analytics Dashboard' }
        ]}
      >
        <Card className="w-full max-w-lg mx-auto shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-mono text-xl tracking-tight">
              <span className="text-violet-400">&lt;</span>
              <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">No Analytics Data</span>
              <span className="text-violet-400">/&gt;</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="font-mono text-sm text-muted-foreground">No analytics data available.</p>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Analytics Dashboard"
      description="Site performance and user engagement metrics"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Analytics Dashboard' }
      ]}
    >
      {/* Header Actions */}
      <div className="flex justify-end items-center gap-2 mb-6">
        <Button onClick={handleRefresh} variant="outline" size="sm" className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
        <Button onClick={handleExport} variant="outline" size="sm" className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Users"
          value={analytics.totalUsers}
          icon={<Users className="h-6 w-6" />}
          description={`${analytics.activeUsers} active users`}
        />
        <MetricCard
          title="Total Reviews"
          value={analytics.totalReviews}
          icon={<FileText className="h-6 w-6" />}
          description={`${analytics.publishedReviews} published`}
        />
        <MetricCard
          title="Total Views"
          value={analytics.totalViews}
          icon={<Eye className="h-6 w-6" />}
          description="All time views"
        />
        <MetricCard
          title="Total Likes"
          value={analytics.totalLikes}
          icon={<Heart className="h-6 w-6" />}
          description="All time likes"
        />
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/60 border-slate-700/50">
          <TabsTrigger value="overview" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Overview</TabsTrigger>
          <TabsTrigger value="content" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Content</TabsTrigger>
          <TabsTrigger value="engagement" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engagement Metrics */}
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center font-mono text-lg tracking-tight">
                  <Activity className="h-5 w-5 mr-2 text-violet-400" />
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Engagement Metrics</span>
                  <span className="text-violet-400">/&gt;</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground font-mono text-sm">Avg Reviews per User</span>
                  <span className="font-medium font-mono text-violet-400">{analytics.engagementMetrics.averageReviewsPerUser.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground font-mono text-sm">Avg Views per Review</span>
                  <span className="font-medium font-mono text-violet-400">{analytics.engagementMetrics.averageViewsPerReview.toFixed(0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground font-mono text-sm">Avg Likes per Review</span>
                  <span className="font-medium font-mono text-violet-400">{analytics.engagementMetrics.averageLikesPerReview.toFixed(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground font-mono text-sm">User Retention Rate</span>
                  <span className="font-medium font-mono text-violet-400">{analytics.engagementMetrics.userRetentionRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground font-mono text-sm">Content Creation Rate</span>
                  <span className="font-medium font-mono text-violet-400">{analytics.engagementMetrics.contentCreationRate.toFixed(1)}/day</span>
                </div>
              </CardContent>
            </Card>

            {/* Content Status */}
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center font-mono text-lg tracking-tight">
                  <BarChart3 className="h-5 w-5 mr-2 text-violet-400" />
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Content Status</span>
                  <span className="text-violet-400">/&gt;</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground font-mono text-sm">Published Reviews</span>
                  <Badge variant="default" className="font-mono text-xs">{analytics.publishedReviews}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground font-mono text-sm">Draft Reviews</span>
                  <Badge variant="secondary" className="font-mono text-xs">{analytics.draftReviews}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground font-mono text-sm">Total Comments</span>
                  <Badge variant="outline" className="font-mono text-xs">{analytics.totalComments}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground font-mono text-sm">Active Users (30d)</span>
                  <Badge variant="default" className="font-mono text-xs">{analytics.activeUsers}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>


        <TabsContent value="content" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Reviews */}
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="font-mono text-lg tracking-tight">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Top Performing Reviews</span>
                  <span className="text-violet-400">/&gt;</span>
                </CardTitle>
                <CardDescription className="font-mono text-sm">Most viewed reviews</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.topReviews.slice(0, 5).map((review, index) => (
                    <div key={review.id} className="flex items-center justify-between p-3 border-slate-700/30 border rounded-lg bg-slate-800/30 hover:bg-slate-700/30 transition-colors">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate font-mono text-sm text-violet-300">{review.title}</p>
                        <p className="text-xs text-muted-foreground font-mono">by {review.author_name}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium font-mono text-xs text-violet-400">{review.view_count} views</p>
                        <p className="text-xs text-muted-foreground font-mono">{review.like_count} likes</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Users */}
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="font-mono text-lg tracking-tight">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Top Contributors</span>
                  <span className="text-violet-400">/&gt;</span>
                </CardTitle>
                <CardDescription className="font-mono text-sm">Most active users</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.topUsers.slice(0, 5).map((user, index) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border-slate-700/30 border rounded-lg bg-slate-800/30 hover:bg-slate-700/30 transition-colors">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate font-mono text-sm text-violet-300">{user.display_name || user.username}</p>
                        <p className="text-xs text-muted-foreground font-mono">Level {user.level || 1}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium font-mono text-xs text-violet-400">{user.review_count} reviews</p>
                        <p className="text-xs text-muted-foreground font-mono">{user.total_views} views</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-6">
          <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="font-mono text-lg tracking-tight">
                <span className="text-violet-400">&lt;</span>
                <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Engagement Analysis</span>
                <span className="text-violet-400">/&gt;</span>
              </CardTitle>
              <CardDescription className="font-mono text-sm">User interaction and engagement patterns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/30">
                  <div className="text-3xl font-bold text-violet-400 font-mono">{analytics.engagementMetrics.averageViewsPerReview.toFixed(0)}</div>
                  <div className="text-sm text-muted-foreground font-mono">Avg Views per Review</div>
                </div>
                <div className="text-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/30">
                  <div className="text-3xl font-bold text-violet-400 font-mono">{analytics.engagementMetrics.averageLikesPerReview.toFixed(1)}</div>
                  <div className="text-sm text-muted-foreground font-mono">Avg Likes per Review</div>
                </div>
                <div className="text-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/30">
                  <div className="text-3xl font-bold text-violet-400 font-mono">{analytics.engagementMetrics.userRetentionRate.toFixed(1)}%</div>
                  <div className="text-sm text-muted-foreground font-mono">User Retention Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
}

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  description: string;
}

function MetricCard({ title, value, icon, description }: MetricCardProps) {
  return (
    <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm hover:border-violet-500/50 transition-all duration-300">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium font-mono text-violet-300">{title}</CardTitle>
        <div className="text-violet-400">{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold font-mono text-white">{value.toLocaleString()}</div>
        <p className="text-xs text-muted-foreground font-mono">{description}</p>
      </CardContent>
    </Card>
  );
}
