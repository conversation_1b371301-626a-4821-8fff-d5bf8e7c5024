'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Palette, Shuffle, RefreshCw, Check } from 'lucide-react';

export interface CustomColors {
  primary: string;
  secondary: string;
  accent: string;
}

interface CustomColorPickerProps {
  colors: CustomColors;
  onChange: (colors: CustomColors) => void;
  className?: string;
}

// Predefined color suggestions organized by category
const colorSuggestions = {
  gaming: [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7',
    '#dda0dd', '#98d8c8', '#6c5ce7', '#fd79a8', '#fdcb6e'
  ],
  professional: [
    '#2d3436', '#636e72', '#74b9ff', '#0984e3', '#00b894',
    '#6c5ce7', '#a29bfe', '#fd79a8', '#e17055', '#fdcb6e'
  ],
  vibrant: [
    '#e74c3c', '#e67e22', '#f39c12', '#f1c40f', '#2ecc71',
    '#1abc9c', '#3498db', '#9b59b6', '#34495e', '#95a5a6'
  ],
  pastel: [
    '#ffeaa7', '#fab1a0', '#ff7675', '#fd79a8', '#e84393',
    '#a29bfe', '#6c5ce7', '#74b9ff', '#0984e3', '#00cec9'
  ]
};

const CustomColorPicker: React.FC<CustomColorPickerProps> = ({
  colors,
  onChange,
  className
}) => {
  const [localColors, setLocalColors] = useState<CustomColors>(colors);
  const [activeColorType, setActiveColorType] = useState<'primary' | 'secondary' | 'accent'>('primary');

  // Update local colors when props change
  useEffect(() => {
    setLocalColors(colors);
  }, [colors]);

  // Handle color change for specific type
  const handleColorChange = (colorType: keyof CustomColors, value: string) => {
    const newColors = { ...localColors, [colorType]: value };
    setLocalColors(newColors);
    onChange(newColors);
  };

  // Generate random colors
  const generateRandomColors = () => {
    const generateRandomHex = (): string => {
      return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
    };
    
    const newColors: CustomColors = {
      primary: generateRandomHex(),
      secondary: generateRandomHex(),
      accent: generateRandomHex()
    };
    
    setLocalColors(newColors);
    onChange(newColors);
  };

  // Apply color suggestion
  const applyColorSuggestion = (color: string) => {
    handleColorChange(activeColorType, color);
  };

  // Validate hex color
  const isValidHex = (hex: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
  };

  return (
    <div className={cn("space-y-6 p-4 bg-gray-900/40 rounded-lg border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200 backdrop-blur-sm", className)}>
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2 text-slate-300 font-medium text-lg">
          <Palette className="h-5 w-5" />
          Custom Colors
        </Label>
        <Button
          onClick={generateRandomColors}
          variant="outline"
          size="sm"
          className="bg-gray-800/60 border-gray-700 text-gray-300 hover:bg-gray-700/60 hover:text-white backdrop-blur-sm"
        >
          <Shuffle className="h-4 w-4 mr-2" />
          Random
        </Button>
      </div>

      {/* Color Preview */}
      <div className="grid grid-cols-3 gap-3">
        {(Object.keys(localColors) as Array<keyof CustomColors>).map((colorType) => (
          <div key={colorType} className="space-y-2">
            <Label className="text-sm capitalize text-gray-300">{colorType}</Label>
            <div className="relative group">
              <div
                className={cn(
                  "w-full h-16 rounded-lg border-2 cursor-pointer transition-all duration-200",
                  activeColorType === colorType 
                    ? "border-slate-500 ring-2 ring-slate-500/30" 
                    : "border-gray-700/50 hover:border-gray-600/50"
                )}
                style={{ backgroundColor: localColors[colorType] }}
                onClick={() => setActiveColorType(colorType)}
              >
                {activeColorType === colorType && (
                  <div className="absolute top-1 right-1">
                    <div className="bg-slate-500/80 rounded-full p-1 backdrop-blur-sm">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  </div>
                )}
              </div>
              <div className="mt-2">
                <Input
                  type="text"
                  value={localColors[colorType]}
                  onChange={(e) => handleColorChange(colorType, e.target.value)}
                  className={cn(
                    "text-center text-sm bg-gray-800/60 border-gray-700/50 focus:border-slate-500 backdrop-blur-sm",
                    !isValidHex(localColors[colorType]) && "border-red-500/50 focus:border-red-500"
                  )}
                  placeholder="#000000"
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Live Preview */}
      <div className="space-y-3">
        <Label className="text-sm text-gray-300">Live Preview</Label>
        <div 
          className="h-20 w-full rounded-lg relative overflow-hidden border border-gray-700/50"
          style={{
            background: `linear-gradient(135deg, ${localColors.primary}, ${localColors.secondary})`
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          <div className="absolute bottom-2 left-2 right-2">
            <div className="flex justify-between items-center">
              <div 
                className="w-4 h-4 rounded-full border border-white/20"
                style={{ backgroundColor: localColors.primary }}
              />
              <div 
                className="w-4 h-4 rounded-full border border-white/20"
                style={{ backgroundColor: localColors.accent }}
              />
              <div 
                className="w-4 h-4 rounded-full border border-white/20"
                style={{ backgroundColor: localColors.secondary }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Color Suggestions */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-sm text-gray-300">
            Color Suggestions for {activeColorType}
          </Label>
          <div className="text-xs text-gray-400">
            Click to apply
          </div>
        </div>
        
        {Object.entries(colorSuggestions).map(([category, colors]) => (
          <div key={category} className="space-y-2">
            <div className="text-xs font-medium text-gray-400 uppercase tracking-wider">
              {category}
            </div>
            <div className="grid grid-cols-5 sm:grid-cols-10 gap-2">
              {colors.map((color, index) => (
                <button
                  key={`${category}-${index}`}
                  className="w-8 h-8 rounded-md border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-slate-500/30"
                  style={{ backgroundColor: color }}
                  onClick={() => applyColorSuggestion(color)}
                  title={color}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700/30 backdrop-blur-sm">
        <div className="text-xs text-gray-400 space-y-1">
          <p>• Click on a color preview to select it for editing</p>
          <p>• Enter hex color codes manually (e.g., #ff6b6b)</p>
          <p>• Use color suggestions or generate random colors</p>
          <p>• Primary and Secondary create the gradient background</p>
          <p>• Accent is used for highlights and interactive elements</p>
        </div>
      </div>
    </div>
  );
};

export default CustomColorPicker;