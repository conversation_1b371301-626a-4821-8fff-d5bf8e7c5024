/* src/components/style/authModal.css */

:root {
  /* Elegant dark theme - less intense, more refined */
  --auth-bg-overlay: rgba(16, 18, 27, 0.85);
  --auth-bg-primary: rgba(30, 33, 45, 0.96);
  --auth-bg-secondary: rgba(24, 27, 36, 0.92);
  
  /* Subtle accent colors - less aggressive */
  --auth-accent-primary: #8b5cf6;
  --auth-accent-secondary: #a855f7;
  --auth-accent-muted: rgba(139, 92, 246, 0.4);
  
  /* Refined border colors - much more subtle */
  --auth-border-primary: rgba(71, 85, 105, 0.3);
  --auth-border-secondary: rgba(71, 85, 105, 0.2);
  --auth-border-focus: rgba(139, 92, 246, 0.5);
  
  /* Clean text colors */
  --auth-text-primary: #f1f5f9;
  --auth-text-secondary: #94a3b8;
  --auth-text-muted: #64748b;
  --auth-text-placeholder: rgba(148, 163, 184, 0.4);
  --auth-text-error: #ef4444;
  --auth-text-success: #10b981;

  /* Clean button styling */
  --auth-button-gradient: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(168, 85, 247, 0.8) 100%);
  --auth-button-gradient-hover: linear-gradient(135deg, rgba(139, 92, 246, 1) 0%, rgba(168, 85, 247, 0.95) 100%);
  
  /* Refined input styling */
  --auth-input-bg: rgba(30, 32, 44, 0.8);
  --auth-input-bg-focus: rgba(35, 38, 52, 0.9);

  --auth-disabled-opacity: 0.5;
  --auth-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --auth-font-mono: 'Fira Code', 'SF Mono', 'Monaco', 'Courier New', monospace;
}

.auth-modal-overlay {
  position: fixed;
  inset: 0;
  background-color: var(--auth-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  padding: 1.5rem;
  overflow-y: auto; /* Allow scrolling if needed */
}

/* Mobile-first responsive approach */
@media (max-height: 700px) {
  .auth-modal-overlay {
    align-items: flex-start;
    padding: 1rem 1rem 2rem;
  }
}

.auth-modal-container {
  /* Wider container to accommodate X button properly */
  background: linear-gradient(145deg, rgba(28, 31, 43, 0.97) 0%, rgba(22, 25, 35, 0.95) 100%);
  border: 1px solid var(--auth-border-primary);
  border-radius: 16px;
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.6), 
    0 0 0 1px rgba(139, 92, 246, 0.1) inset;
  width: 100%;
  max-width: 520px; /* Increased width to give X button more room */
  max-height: 90vh; /* Prevent modal from being too tall */
  position: relative;
  overflow: hidden;
  color: var(--auth-text-primary);
  font-family: var(--auth-font-primary);
  display: flex;
  flex-direction: column;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .auth-modal-container {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
    margin: 0;
    border-radius: 12px;
  }
}

@media (max-height: 700px) {
  .auth-modal-container {
    max-height: calc(100vh - 2rem);
    margin-top: 1rem;
  }
}

.auth-modal-close-button {
  position: absolute;
  top: 10px;
  right: 10px; /* Moved further right */
  background: rgba(30, 32, 44, 0.95);
  border: 1px solid var(--auth-border-secondary);
  color: var(--auth-text-secondary);
  cursor: pointer;
  padding: 10px;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  z-index: 20; /* Higher z-index to ensure it's always on top */
}

/* Mobile close button adjustments */
@media (max-width: 640px) {
  .auth-modal-close-button {
    top: 8px;
    right: 8px;
    width: 36px;
    height: 36px;
    padding: 8px;
  }
}

.auth-modal-close-button:hover {
  background: rgba(139, 92, 246, 0.15);
  border-color: var(--auth-accent-muted);
  color: var(--auth-accent-primary);
  transform: scale(1.05);
}

.auth-modal-header {
  display: flex;
  justify-content: stretch;
  padding: 12px 12px 0 12px;
  border-bottom: 1px solid var(--auth-border-secondary);
  background: rgba(20, 22, 32, 0.4);
  margin-right: 0px; /* Add right margin to avoid X button overlap */
  flex-shrink: 0; /* Prevent header from shrinking */
}

/* Mobile header adjustments */
@media (max-width: 640px) {
  .auth-modal-header {
    padding: 8px 8px 0 8px;
  }
}

.auth-modal-tab {
  flex-grow: 1;
  padding: 14px 12px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--auth-text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

/* Mobile tab adjustments */
@media (max-width: 640px) {
  .auth-modal-tab {
    padding: 12px 8px;
    font-size: 0.8rem;
    letter-spacing: 0.03em;
  }
}

@media (max-height: 700px) {
  .auth-modal-tab {
    padding: 10px 12px;
    font-size: 0.85rem;
  }
}

.auth-modal-tab:hover {
  color: var(--auth-text-primary);
  background: rgba(139, 92, 246, 0.05);
}

.auth-modal-tab.active {
  color: var(--auth-accent-primary);
  font-weight: 600;
  border-bottom-color: var(--auth-accent-primary);
  background: rgba(139, 92, 246, 0.08);
}

.auth-modal-content {
  padding: 30px 36px 32px; /* Reduced padding for less height */
  display: flex;
  flex-direction: column;
  gap: 20px; /* Reduced gap for more compact layout */
  overflow-y: auto; /* Allow scrolling within content area */
  flex: 1; /* Take remaining space */
  scrollbar-width: thin;
  scrollbar-color: var(--auth-accent-muted) transparent;
}

.auth-modal-content::-webkit-scrollbar {
  width: 6px;
}

.auth-modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.auth-modal-content::-webkit-scrollbar-thumb {
  background-color: var(--auth-accent-muted);
  border-radius: 3px;
}

.auth-modal-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--auth-accent-primary);
}

/* Mobile content adjustments */
@media (max-width: 640px) {
  .auth-modal-content {
    padding: 20px 24px 24px;
    gap: 16px;
  }
}

@media (max-height: 700px) {
  .auth-modal-content {
    padding: 20px 30px 24px;
    gap: 14px;
  }
}

.code-title {
  font-family: var(--auth-font-mono);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--auth-text-primary);
  display: inline-block;
  letter-spacing: 0.5px;
}

/* Mobile title adjustments */
@media (max-width: 640px) {
  .code-title {
    font-size: 1.3rem;
    letter-spacing: 0.3px;
  }
}

@media (max-height: 700px) {
  .code-title {
    font-size: 1.2rem;
  }
}

.code-title-bracket {
  color: var(--auth-accent-primary);
  opacity: 0.7;
  font-weight: 500;
}

.auth-modal-title-main {
  text-align: center;
  margin-bottom: 8px; /* Reduced bottom margin */
}

/* Mobile title main adjustments */
@media (max-width: 640px) {
  .auth-modal-title-main {
    margin-bottom: 6px;
  }
}

@media (max-height: 700px) {
  .auth-modal-title-main {
    margin-bottom: 4px;
  }
}

.auth-modal-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Standard gap */
  margin-bottom: 4px; /* Minimal bottom margin */
}

/* Mobile input group adjustments */
@media (max-width: 640px) {
  .auth-modal-input-group {
    gap: 6px;
    margin-bottom: 2px;
  }
}

@media (max-height: 700px) {
  .auth-modal-input-group {
    gap: 6px;
    margin-bottom: 2px;
  }
}

.auth-modal-input-group label {
  font-family: var(--auth-font-mono);
  font-size: 0.8rem;
  text-transform: uppercase;
  color: var(--auth-text-secondary);
  opacity: 0.9;
  letter-spacing: 0.05em;
  transition: color 0.2s ease;
  padding-left: 4px;
}

.auth-modal-input-group input,
.auth-modal-password-wrapper input {
  background-color: var(--auth-input-bg);
  border: 1px solid var(--auth-border-secondary);
  border-radius: 10px;
  padding: 14px 16px;
  color: var(--auth-text-primary);
  font-size: 0.95rem;
  width: 100%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) inset;
}

/* Mobile input adjustments */
@media (max-width: 640px) {
  .auth-modal-input-group input,
  .auth-modal-password-wrapper input {
    padding: 12px 14px;
    font-size: 0.9rem;
    border-radius: 8px;
  }
}

@media (max-height: 700px) {
  .auth-modal-input-group input,
  .auth-modal-password-wrapper input {
    padding: 10px 14px;
    font-size: 0.9rem;
  }
}

.auth-modal-input-group input::placeholder,
.auth-modal-password-wrapper input::placeholder {
  color: var(--auth-text-placeholder);
  font-style: normal;
  opacity: 0.8;
}

.auth-modal-input-group input:focus,
.auth-modal-password-wrapper input:focus {
  outline: none;
  border-color: var(--auth-border-focus);
  background-color: var(--auth-input-bg-focus);
  box-shadow: 
    0 0 0 2px rgba(139, 92, 246, 0.2), 
    0 2px 4px rgba(0,0,0,0.2) inset;
}

.auth-modal-input-group input:disabled,
.auth-modal-password-wrapper input:disabled {
  opacity: var(--auth-disabled-opacity);
  cursor: not-allowed;
  border-color: var(--auth-border-secondary);
  background-color: rgba(20, 22, 30, 0.5);
}

.auth-modal-password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.auth-modal-password-wrapper input {
  padding-right: 48px;
}

.auth-modal-show-password-button {
  position: absolute;
  right: 4px;
  top: 4px;
  bottom: 4px;
  background: transparent;
  border: none;
  color: var(--auth-text-secondary);
  opacity: 0.7;
  cursor: pointer;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.auth-modal-show-password-button:hover {
  color: var(--auth-accent-primary);
  opacity: 1;
  background: rgba(139, 92, 246, 0.1);
}

.auth-modal-button {
  padding: 14px 20px;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  letter-spacing: 0.03em;
  text-transform: uppercase;
  margin: 20px auto 8px; /* More space above login button, less below */
  min-width: 200px;
}

/* Mobile button adjustments */
@media (max-width: 640px) {
  .auth-modal-button {
    padding: 12px 16px;
    font-size: 0.9rem;
    margin: 16px auto 6px;
    min-width: 180px;
    border-radius: 10px;
  }
}

@media (max-height: 700px) {
  .auth-modal-button {
    padding: 10px 16px;
    font-size: 0.85rem;
    margin: 12px auto 4px;
    min-width: 160px;
  }
}

.auth-modal-button.primary {
  background: var(--auth-button-gradient);
  color: white;
  border-color: rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
}

.auth-modal-button.primary:hover:not(:disabled) {
  background: var(--auth-button-gradient-hover);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.auth-modal-button.primary:active:not(:disabled) {
  transform: translateY(0px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
}

.auth-modal-button:disabled {
  opacity: var(--auth-disabled-opacity);
  cursor: not-allowed;
  background: rgba(30, 32, 44, 0.6);
  border-color: var(--auth-border-secondary);
  color: var(--auth-text-muted);
  box-shadow: none;
}

.auth-modal-link-button {
  background: none;
  border: none;
  color: var(--auth-text-secondary);
  font-family: var(--auth-font-mono);
  font-size: 0.85rem;
  cursor: pointer;
  text-align: center;
  padding: 8px 0; /* Reduced padding */
  margin-top: 4px; /* Reduced margin */
  transition: all 0.2s ease;
  letter-spacing: 0.05em;
  opacity: 0.9;
}

.auth-modal-link-button:hover:not(:disabled) {
  color: var(--auth-accent-primary);
  opacity: 1;
  text-decoration: none;
}

.auth-modal-link-button:disabled {
  opacity: var(--auth-disabled-opacity);
  cursor: not-allowed;
}

.auth-modal-checkbox-group {
  margin: 8px 0; /* Reduced margin */
}

/* Mobile checkbox adjustments */
@media (max-width: 640px) {
  .auth-modal-checkbox-group {
    margin: 6px 0;
  }
}

@media (max-height: 700px) {
  .auth-modal-checkbox-group {
    margin: 4px 0;
  }
}

.auth-modal-checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  font-size: 0.9rem;
  color: var(--auth-text-secondary);
  cursor: pointer;
  transition: color 0.2s ease;
  line-height: 1.5;
  padding: 4px 0; /* Reduced padding */
}

/* Mobile checkbox label adjustments */
@media (max-width: 640px) {
  .auth-modal-checkbox-label {
    gap: 10px;
    font-size: 0.85rem;
    line-height: 1.4;
    padding: 2px 0;
  }
}

@media (max-height: 700px) {
  .auth-modal-checkbox-label {
    gap: 8px;
    font-size: 0.85rem;
    padding: 2px 0;
  }
}

.auth-modal-checkbox-label input[type="checkbox"] {
  display: none;
}

.auth-modal-checkbox-label .checked-icon,
.auth-modal-checkbox-label .unchecked-icon {
  color: var(--auth-accent-primary);
  opacity: 0.8;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.auth-modal-checkbox-label:hover .unchecked-icon,
.auth-modal-checkbox-label:hover .checked-icon {
  color: var(--auth-accent-secondary);
  opacity: 1;
  transform: scale(1.05);
}

.auth-modal-checkbox-label:hover span {
  color: var(--auth-text-primary);
}

.auth-modal-inline-link {
  color: var(--auth-accent-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  opacity: 0.9;
}

.auth-modal-inline-link:hover {
  color: var(--auth-accent-secondary);
  text-decoration: underline;
  text-underline-offset: 2px;
  opacity: 1;
}

.auth-modal-feedback {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px; /* Reduced padding */
  border-radius: 10px;
  font-size: 0.9rem;
  margin: 16px 0; /* Reduced spacing */
  border: 1px solid transparent;
  line-height: 1.5;
}

/* Mobile feedback adjustments */
@media (max-width: 640px) {
  .auth-modal-feedback {
    gap: 10px;
    padding: 10px 14px;
    font-size: 0.85rem;
    margin: 12px 0;
    border-radius: 8px;
  }
}

@media (max-height: 700px) {
  .auth-modal-feedback {
    padding: 8px 12px;
    font-size: 0.85rem;
    margin: 10px 0;
  }
}

.auth-modal-feedback.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--auth-text-error);
  border-color: rgba(239, 68, 68, 0.2);
}

.auth-modal-feedback.message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--auth-text-success);
  border-color: rgba(16, 185, 129, 0.2);
}

.auth-modal-feedback svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.auth-modal-info-text {
  font-size: 0.85rem;
  color: var(--auth-text-secondary);
  font-family: var(--auth-font-mono);
  text-align: center;
  line-height: 1.6;
  letter-spacing: 0.03em;
  opacity: 0.8;
  margin: 16px 0; /* Reduced spacing */
  padding: 0 8px; /* Reduced horizontal padding */
}

/* Mobile info text adjustments */
@media (max-width: 640px) {
  .auth-modal-info-text {
    font-size: 0.8rem;
    margin: 12px 0;
    line-height: 1.5;
  }
}

@media (max-height: 700px) {
  .auth-modal-info-text {
    font-size: 0.8rem;
    margin: 10px 0;
  }
}

.auth-modal-divider {
  display: flex;
  align-items: center;
  text-align: center;
  color: var(--auth-text-secondary);
  margin: 20px 0; /* Reduced spacing */
  font-family: var(--auth-font-mono);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  opacity: 0.7;
}

/* Mobile divider adjustments */
@media (max-width: 640px) {
  .auth-modal-divider {
    margin: 16px 0;
    font-size: 0.7rem;
  }
}

@media (max-height: 700px) {
  .auth-modal-divider {
    margin: 12px 0;
    font-size: 0.7rem;
  }
}

.auth-modal-divider::before,
.auth-modal-divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid var(--auth-border-secondary);
  opacity: 0.4;
}

.auth-modal-divider-text {
  padding: 0 16px;
}

.auth-modal-social-logins {
  display: flex;
  flex-direction: row; /* Changed to horizontal layout */
  gap: 12px; /* Horizontal gap between buttons */
  justify-content: center; /* Center the buttons */
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

/* Mobile social buttons - stack vertically for better UX */
@media (max-width: 640px) {
  .auth-modal-social-logins {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-height: 700px) {
  .auth-modal-social-logins {
    gap: 8px;
  }
}

.auth-modal-social-button {
  padding: 10px 16px;
  border-radius: 10px;
  font-size: 0.85rem; /* Slightly smaller for horizontal layout */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--auth-border-secondary);
  background-color: rgba(30, 32, 44, 0.6);
  color: var(--auth-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex: 1; /* Equal width buttons */
  min-width: 0; /* Allow shrinking */
}

/* Mobile social button adjustments */
@media (max-width: 640px) {
  .auth-modal-social-button {
    padding: 10px 14px;
    font-size: 0.8rem;
    border-radius: 8px;
    flex: none; /* Don't flex on mobile when stacked */
    width: 100%; /* Full width when stacked */
  }
}

@media (max-height: 700px) {
  .auth-modal-social-button {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

/* Updated styling for actual icon components instead of placeholders */
.auth-modal-social-button .social-icon {
  width: 20px; /* Slightly smaller for horizontal layout */
  height: 20px;
  flex-shrink: 0;
}

/* Remove the old placeholder styling as we're now using real icons */
.auth-modal-social-button .social-icon-placeholder {
  display: none; /* Hide old placeholders if any remain */
}

.auth-modal-social-button:hover:not(:disabled) {
  border-color: var(--auth-accent-muted);
  color: var(--auth-text-primary);
  background-color: rgba(139, 92, 246, 0.08);
  transform: translateY(-1px);
}

.auth-modal-social-button:disabled {
  opacity: var(--auth-disabled-opacity);
  cursor: not-allowed;
  background-color: rgba(20, 22, 30, 0.4);
  border-color: var(--auth-border-secondary);
}

.auth-modal-social-button .soon-tag {
  font-size: 0.65rem;
  font-family: var(--auth-font-mono);
  color: var(--auth-text-muted);
  background: rgba(0,0,0,0.5);
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.auth-modal-footer-legal {
  margin-top: 20px; /* Reduced spacing */
  padding-top: 14px; /* Reduced padding */
  border-top: 1px solid var(--auth-border-secondary);
  font-size: 0.8rem;
  color: var(--auth-text-secondary);
  opacity: 0.8;
  text-align: center;
  line-height: 1.6;
}

/* Mobile footer adjustments */
@media (max-width: 640px) {
  .auth-modal-footer-legal {
    margin-top: 16px;
    padding-top: 12px;
    font-size: 0.75rem;
    line-height: 1.5;
  }
}

@media (max-height: 700px) {
  .auth-modal-footer-legal {
    margin-top: 12px;
    padding-top: 10px;
    font-size: 0.75rem;
  }
}

.auth-modal-footer-legal a {
  color: var(--auth-text-secondary);
  opacity: 0.9;
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: all 0.2s ease;
}

.auth-modal-footer-legal a:hover {
  color: var(--auth-accent-primary);
  opacity: 1;
}

.auth-modal-footer-legal p {
  margin-bottom: 6px; /* Reduced spacing between paragraphs */
}

/* Animation helper for items */
.auth-modal-content > * {
  opacity: 1;
  transform: translateY(0);
}