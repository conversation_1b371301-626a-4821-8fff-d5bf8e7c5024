'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/auth-context';

export type VoteType = 'human' | 'ai' | 'unsure';

interface AIVotingStats {
  humanVotes: number;
  aiVotes: number;
  unsureVotes: number;
}

export function useAIVoting(reviewId: string) {
  const { user } = useAuthContext();
  const [stats, setStats] = useState<AIVotingStats>({
    humanVotes: 0,
    aiVotes: 0,
    unsureVotes: 0,
  });
  const [userVote, setUserVote] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const supabase = createClient();

  // Fetch current stats and user vote
  const fetchData = async () => {
    if (!reviewId) return;

    try {
      // Fetch voting stats
      const { data: statsData, error: statsError } = await supabase
        .from('ai_detection_votes')
        .select('vote_type')
        .eq('review_id', reviewId);

      if (statsError) {
        console.error('Error fetching AI voting stats:', statsError);
      } else {
        const newStats = {
          humanVotes: statsData?.filter(v => v.vote_type === 'human').length || 0,
          aiVotes: statsData?.filter(v => v.vote_type === 'ai').length || 0,
          unsureVotes: statsData?.filter(v => v.vote_type === 'unsure').length || 0,
        };
        setStats(newStats);
      }

      // Fetch user's current vote if logged in
      if (user) {
        const { data: userVoteData, error: userVoteError } = await supabase
          .from('ai_detection_votes')
          .select('vote_type')
          .eq('review_id', reviewId)
          .eq('user_id', user.id)
          .single();

        if (userVoteError && userVoteError.code !== 'PGRST116') {
          console.error('Error fetching user vote:', userVoteError);
        } else {
          setUserVote(userVoteData?.vote_type || null);
        }
      }
    } catch (error) {
      console.error('Error in fetchData:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Submit or update vote
  const submitVote = async (voteType: 'ai' | 'human' | 'unsure') => {
    if (!user || !reviewId || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from('ai_detection_votes')
        .upsert({
          review_id: reviewId,
          user_id: user.id,
          vote_type: voteType,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'review_id,user_id'
        });

      if (error) {
        throw error;
      }

      // Update local state
      setUserVote(voteType);
      
      // Refresh stats
      await fetchData();
    } catch (error) {
      console.error('Error submitting vote:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [reviewId, user]);

  return {
    stats,
    userVote,
    submitVote,
    isSubmitting,
    isLoading,
    refetch: fetchData
  };
}

/**
 * Helper function to get confidence level description
 */
export function getConfidenceDescription(confidence: number): {
  label: string;
  color: string;
  description: string;
} {
  if (confidence >= 80) {
    return {
      label: 'Likely Human',
      color: 'text-emerald-400',
      description: 'Community believes this is human-written'
    };
  } else if (confidence >= 65) {
    return {
      label: 'Probably Human',
      color: 'text-green-400',
      description: 'Community leans towards human authorship'
    };
  } else if (confidence >= 35) {
    return {
      label: 'Uncertain',
      color: 'text-yellow-400',
      description: 'Community is split on authorship'
    };
  } else if (confidence >= 20) {
    return {
      label: 'Probably AI',
      color: 'text-orange-400',
      description: 'Community leans towards AI generation'
    };
  } else {
    return {
      label: 'Likely AI',
      color: 'text-red-400',
      description: 'Community believes this is AI-generated'
    };
  }
}

/**
 * Helper function to get vote type display info
 */
export function getVoteTypeInfo(voteType: VoteType): {
  label: string;
  icon: string;
  color: string;
  description: string;
} {
  switch (voteType) {
    case 'human':
      return {
        label: 'Human',
        icon: '👤',
        color: 'text-emerald-400',
        description: 'This feels human-written'
      };
    case 'ai':
      return {
        label: 'AI',
        icon: '🤖',
        color: 'text-red-400',
        description: 'This seems AI-generated'
      };
    case 'unsure':
      return {
        label: "Can't Tell",
        icon: '🤷',
        color: 'text-yellow-400',
        description: "I'm not sure about this one"
      };
  }
}