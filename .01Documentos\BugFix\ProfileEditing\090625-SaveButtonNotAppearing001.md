# Bug Fix Report: Save Button Not Appearing + Social Media Collection Issues

**Date:** January 14, 2025
**Bug ID:** 140125-SaveButtonNotAppearing001
**Reporter:** User
**Severity:** Medium
**Status:** ✅ FIXED

## Problem Description

**Primary Issue:** The save button in the EditProfileModal was not appearing/enabling when users added new social media or gaming profiles.

**Secondary Issue:** Social media collections were not persistent and new additions were overwriting the entire collection instead of adding to existing profiles.

### Symptoms
- Users could add gaming profiles and social media profiles
- The save button remained disabled (grayed out) after adding profiles
- No visual indication that changes needed to be saved
- Users couldn't save their newly added profiles
- Existing social media profiles disappeared when adding new ones
- New social media additions overwrote the entire collection

## Root Cause Analysis

### Investigation Process
1. **Used Context7** to analyze the EditProfileModal component structure
2. **Applied Sequential Thinking** to understand the save button logic flow
3. **Examined code** to identify state management issues

### Root Cause

**Primary Issue - Save Button:**
The save button in EditProfileModal was controlled by the `hasUnsavedChanges` state, which was only updated when react-hook-form's `isDirty` flag changed. However, gaming profiles and social media profiles were managed as separate state arrays outside of react-hook-form:

```typescript
// These state arrays were not tracked by react-hook-form
const [gamingProfiles, setGamingProfiles] = useState<GamingProfile[]>([]);
const [socialMedia, setSocialMedia] = useState<SocialMediaProfile[]>([]);

// Save button was disabled when hasUnsavedChanges was false
<button
  disabled={!hasUnsavedChanges}
  className={cn("edit-button success", !hasUnsavedChanges && "opacity-50")}
>
```

When users added/removed profiles using `addGamingProfile`, `removeGamingProfile`, `addSocialProfile`, or `removeSocialProfile` functions, these arrays changed but `isDirty` didn't update, so `hasUnsavedChanges` remained false.

**Secondary Issue - Data Loading:**
There was a Next.js caching issue with `unstable_cache` that was preventing social media profiles from being loaded consistently. The cached functions were trying to access cookies inside the cache scope, which Next.js doesn't allow, causing intermittent failures in loading existing social media profiles.

## Solution Implemented

### Primary Issue: Save Button Not Enabling
Modified four functions in `src/components/userprofile/EditProfileModal.tsx` to call `setHasUnsavedChanges(true)` when profiles are added or removed:

1. **addGamingProfile** (line 544):
```typescript
setGamingProfiles(prev => [...prev, newProfile]);
setTempGamingProfile({});
setHasUnsavedChanges(true); // ✅ Added this line
```

2. **removeGamingProfile** (line 549):
```typescript
setGamingProfiles(prev => prev.filter((_, i) => i !== index));
setHasUnsavedChanges(true); // ✅ Added this line
```

3. **addSocialProfile** (line 573):
```typescript
setSocialMedia(prev => [...prev, newProfile]);
setTempSocialProfile({});
setHasUnsavedChanges(true); // ✅ Added this line
```

4. **removeSocialProfile** (line 579):
```typescript
setSocialMedia(prev => prev.filter((_, i) => i !== index));
setHasUnsavedChanges(true); // ✅ Added this line
```

### Secondary Issue: Social Media Data Loading
Enhanced the profile data initialization to handle potential data mapping issues:

5. **Profile Data Initialization** (line 425):
```typescript
// Ensure we get the social media data from the correct source
// Check both socialMedia (converted) and social_profiles (raw) fields
const socialMediaData = profile.socialMedia ||
                       (profile as any).social_profiles ||
                       [];

const gamingProfilesData = profile.gamingProfiles ||
                          (profile as any).gaming_profiles ||
                          [];
```

### Technical Details
- **Files Modified:**
  - `src/components/userprofile/EditProfileModal.tsx` (primary fix)
  - `src/app/u/actions.ts` (caching fix)
- **Lines Changed:**
  - EditProfileModal: 425-435 (data initialization), 544, 549, 573, 579 (state management)
  - actions.ts: 170-206 (removed unstable_cache to fix cookies issue)
- **Change Type:** State management fix + Next.js caching fix
- **Impact:** Low risk - adds state updates and resolves server-side data loading issues

## Testing Verification

### Expected Behavior After Fix
1. ✅ User opens EditProfileModal
2. ✅ User navigates to Gaming Profiles or Social Profiles step
3. ✅ User adds a new gaming profile or social media profile
4. ✅ Save button becomes enabled (no longer grayed out)
5. ✅ User can successfully save their changes
6. ✅ Same behavior when removing existing profiles

### Test Cases
- [ ] Add gaming profile → Save button enables
- [ ] Remove gaming profile → Save button enables  
- [ ] Add social media profile → Save button enables
- [ ] Remove social media profile → Save button enables
- [ ] Save changes successfully persists new profiles
- [ ] Modal close protection works when unsaved changes exist

## Related Issues
- Previous similar issue with social media content on GamerCard component
- Part of ongoing profile management system improvements

## Prevention Measures
- Consider migrating gaming profiles and social media profiles to react-hook-form for consistent state management
- Add automated tests for profile addition/removal workflows
- Document state management patterns for future profile features

## Notes
- Fix maintains existing functionality while resolving the save button issue
- No breaking changes to existing profile data or UI
- Solution follows existing code patterns and conventions
