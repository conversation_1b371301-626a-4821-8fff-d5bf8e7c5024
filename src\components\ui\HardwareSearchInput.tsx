'use client';

import { useState, useCallback, useMemo, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Search, Loader2, Cpu, Monitor } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCPUSearch, useGPUSearch, useTopCPUs, useTopGPUs } from '@/hooks/useHardwareData';
import { CPUSpec } from '@/lib/cpu-data';
import { GPUSpec } from '@/lib/gpu-data';
import useDebounce from '@/hooks/useDebounce';

interface HardwareSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  type: 'cpu' | 'gpu';
  onHardwareSelect?: (hardware: CPUSpec | GPUSpec) => void;
  className?: string;
}

export const HardwareSearchInput: React.FC<HardwareSearchInputProps> = ({
  value,
  onChange,
  placeholder,
  type,
  onHardwareSelect,
  className = ''
}) => {
  const [focused, setFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Debounce the search query to avoid too many API calls
  const debouncedQuery = useDebounce(value, 300);
  
  // Fetch hardware data based on type
  const { data: cpuResults = [], isLoading: cpuLoading } = useCPUSearch(
    debouncedQuery, 
    type === 'cpu' && focused
  );
  
  const { data: gpuResults = [], isLoading: gpuLoading } = useGPUSearch(
    debouncedQuery, 
    type === 'gpu' && focused
  );
  
  // Get top hardware for initial suggestions
  const { data: topCPUs = [] } = useTopCPUs(5);
  const { data: topGPUs = [] } = useTopGPUs(5);
  
  const isLoading = type === 'cpu' ? cpuLoading : gpuLoading;
  const searchResults = type === 'cpu' ? cpuResults : gpuResults;
  const topResults = type === 'cpu' ? topCPUs : topGPUs;
  
  // Determine what suggestions to show
  const suggestions = useMemo(() => {
    if (!focused) return [];

    if (!value.trim() || value.length < 2) {
      // Show top hardware when input is empty or too short
      return topResults.slice(0, 5);
    }

    // Show search results
    return searchResults.slice(0, 5);
  }, [focused, value, searchResults, topResults]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  const handleSuggestionClick = useCallback((hardware: CPUSpec | GPUSpec) => {
    onChange(hardware.name);
    setFocused(false);
    onHardwareSelect?.(hardware);
  }, [onChange, onHardwareSelect]);

  const handleFocus = useCallback(() => {
    setFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    // Use a longer timeout to prevent premature closing when clicking suggestions
    setTimeout(() => setFocused(false), 300);
  }, []);

  const getSuggestionsStyle = useCallback(() => {
    return {
      position: 'absolute' as const,
      top: '100%',
      left: 0,
      right: 0,
      marginTop: '4px',
      zIndex: 50
    };
  }, []);

  const formatHardwareInfo = useCallback((hardware: CPUSpec | GPUSpec) => {
    // Just show the model name since we simplified the data structure
    return hardware.model;
  }, []);

  return (
    <div className={`perf-survey__search-container ${className}`}>
      <div className="perf-survey__search-wrapper">
        {type === 'cpu' ? (
          <Cpu size={16} className="perf-survey__search-icon" />
        ) : (
          <Monitor size={16} className="perf-survey__search-icon" />
        )}
        <Input
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className="perf-survey__search-input"
          autoComplete="off"
        />
        {isLoading && (
          <Loader2 size={16} className="perf-survey__search-loading animate-spin" />
        )}
      </div>

      {focused && suggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="perf-survey__search-suggestions perf-survey__hardware-suggestions"
          style={getSuggestionsStyle()}
        >
          {suggestions.map((hardware, index) => (
            <button
              key={`${type}-suggestion-${index}-${hardware.id}`}
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionClick(hardware);
              }}
              className="perf-survey__search-suggestion perf-survey__hardware-suggestion"
              type="button"
            >
              <div className="perf-survey__hardware-suggestion-content">
                <div className="perf-survey__hardware-suggestion-header">
                  <span className="perf-survey__hardware-suggestion-name">
                    {hardware.name}
                  </span>
                </div>
                <div className="perf-survey__hardware-suggestion-details">
                  <span className="perf-survey__hardware-suggestion-manufacturer">
                    {hardware.manufacturer}
                  </span>
                  <span className="perf-survey__hardware-suggestion-specs">
                    {formatHardwareInfo(hardware)}
                  </span>
                </div>
              </div>
            </button>
          ))}
        </motion.div>
      )}
    </div>
  );
};
