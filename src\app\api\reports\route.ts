import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

export interface ReportSubmission {
  contentId: string;
  contentType: 'review' | 'comment';
  reporterId: string;
  reason: string;
  description?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ReportSubmission = await request.json();
    console.log('🚨 API Report submission started:', { 
      contentId: body.contentId, 
      contentType: body.contentType, 
      reporterId: body.reporterId, 
      reason: body.reason 
    });

    // Create Supabase client with cookies
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Validate input
    if (!body.contentId || !body.reporterId || !body.reason) {
      console.error('Missing required fields:', { 
        contentId: !!body.contentId, 
        reporterId: !!body.reporterId, 
        reason: !!body.reason 
      });
      return NextResponse.json(
        { success: false, error: 'Campos obrigatórios faltando' },
        { status: 400 }
      );
    }

    // Check authentication
    console.log('🔐 Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('🔐 Auth result:', { 
      user: user ? { id: user.id, email: user.email } : null, 
      authError 
    });
    
    if (authError) {
      console.error('Authentication error:', authError);
      return NextResponse.json(
        { success: false, error: 'Erro de autenticação' },
        { status: 401 }
      );
    }
    
    if (!user) {
      console.error('No user found in session');
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verify reporter ID matches authenticated user
    if (user.id !== body.reporterId) {
      console.error('Reporter ID mismatch:', { userId: user.id, reporterId: body.reporterId });
      return NextResponse.json(
        { success: false, error: 'ID do usuário não confere' },
        { status: 403 }
      );
    }
    
    console.log('✅ Authentication successful for user:', user.id);
    
    // Check for existing report (use maybeSingle to avoid errors)
    const { data: existingReport, error: duplicateError } = await supabase
      .from('content_flags')
      .select('id')
      .eq('content_id', body.contentId)
      .eq('reporter_id', body.reporterId)
      .eq('content_type', body.contentType)
      .maybeSingle();
      
    if (duplicateError) {
      console.error('Error checking for duplicate report:', duplicateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar denúncia existente' },
        { status: 500 }
      );
    }
    
    if (existingReport) {
      return NextResponse.json(
        { success: false, error: 'Você já denunciou este conteúdo' },
        { status: 409 }
      );
    }
    
    // Verify content exists (use maybeSingle to avoid errors)
    const tableName = body.contentType === 'review' ? 'reviews' : 'comments';
    const { data: content, error: contentError } = await supabase
      .from(tableName)
      .select('id')
      .eq('id', body.contentId)
      .maybeSingle();
      
    if (contentError) {
      console.error('Error checking content existence:', contentError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar conteúdo' },
        { status: 500 }
      );
    }
    
    if (!content) {
      console.error('Content not found:', { contentId: body.contentId, contentType: body.contentType });
      return NextResponse.json(
        { success: false, error: 'Conteúdo não encontrado' },
        { status: 404 }
      );
    }
    
    // Insert the report
    const insertData = {
      content_id: body.contentId,
      content_type: body.contentType,
      reporter_id: body.reporterId,
      reason: body.reason,
      description: body.description,
      status: 'pending',
      created_at: new Date().toISOString(),
    };
    
    console.log('📝 Attempting to insert report with data:', insertData);
    
    const { error: insertError } = await supabase
      .from('content_flags')
      .insert(insertData);
      
    if (insertError) {
      console.error('Error inserting report:', insertError);
      return NextResponse.json(
        { success: false, error: 'Erro ao registrar denúncia' },
        { status: 500 }
      );
    }
    
    console.log('✅ Report inserted successfully');
    
    // Update flag_count on the content
    console.log('📊 Updating flag count for content...');
    
    // Get current flag count for this content
    const { data: flagCount, error: countError } = await supabase
      .from('content_flags')
      .select('id')
      .eq('content_id', body.contentId)
      .eq('content_type', body.contentType)
      .eq('status', 'pending');
    
    if (countError) {
      console.error('Error getting flag count:', countError);
      // Don't fail the request if flag count update fails
    } else {
      const newFlagCount = flagCount?.length || 0;
      
      // Update the flag_count on the actual content
      const { error: updateError } = await supabase
        .from(tableName)
        .update({ flag_count: newFlagCount })
        .eq('id', body.contentId);
        
      if (updateError) {
        console.error('Error updating flag count:', updateError);
        // Don't fail the request if flag count update fails
      } else {
        console.log('✅ Flag count updated successfully:', newFlagCount);
      }
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Unexpected error in report API:', error);
    return NextResponse.json(
      { success: false, error: 'Erro inesperado ao denunciar' },
      { status: 500 }
    );
  }
}
