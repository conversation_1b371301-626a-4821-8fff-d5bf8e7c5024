import { NextRequest, NextResponse } from 'next/server';
import { searchCPUs, getTopCPUs } from '@/lib/cpu-data';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');

    let results;
    
    if (query && query.length >= 2) {
      // Search for CPUs matching the query
      results = searchCPUs(query).slice(0, limit);
    } else {
      // Return top CPUs if no query provided
      results = getTopCPUs(limit);
    }

    return NextResponse.json({
      success: true,
      data: results,
      count: results.length
    });

  } catch (error) {
    console.error('Error searching CPUs:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to search CPUs',
        data: []
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, limit = 10 } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid search query',
          data: []
        },
        { status: 400 }
      );
    }

    const results = searchCPUs(query).slice(0, limit);

    return NextResponse.json({
      success: true,
      data: results,
      count: results.length
    });

  } catch (error) {
    console.error('Error searching CPUs:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to search CPUs',
        data: []
      },
      { status: 500 }
    );
  }
}
