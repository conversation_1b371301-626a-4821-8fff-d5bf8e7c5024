'use client';

import { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Award,
  Target,
  Zap,
  Monitor,
  Gamepad2,
  HardDrive
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';

interface PerformanceInsightsProps {
  surveys: PerformanceSurveyRecord[];
  className?: string;
}

interface PerformanceMetrics {
  avgFps: number;
  avgSmoothness: number;
  totalSurveys: number;
  uniqueGames: number;
  deviceBreakdown: Record<string, number>;
  topPerformingGames: Array<{ game: string; avgFps: number; count: number }>;
  performanceTrend: 'up' | 'down' | 'stable';
  recommendations: string[];
}

export function PerformanceInsights({ surveys, className }: PerformanceInsightsProps) {
  const metrics = useMemo((): PerformanceMetrics => {
    if (surveys.length === 0) {
      return {
        avgFps: 0,
        avgSmoothness: 0,
        totalSurveys: 0,
        uniqueGames: 0,
        deviceBreakdown: {},
        topPerformingGames: [],
        performanceTrend: 'stable',
        recommendations: []
      };
    }

    // Calculate basic metrics
    const totalFps = surveys.reduce((sum, s) => sum + (s.fps_average || 0), 0);
    const totalSmoothness = surveys.reduce((sum, s) => sum + (s.smoothness || 0), 0);
    const avgFps = Math.round(totalFps / surveys.length);
    const avgSmoothness = Math.round((totalSmoothness / surveys.length) * 10) / 10;

    // Device breakdown
    const deviceBreakdown = surveys.reduce((acc, survey) => {
      const device = survey.device_type || 'unknown';
      acc[device] = (acc[device] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top performing games
    const gamePerformance = surveys.reduce((acc, survey) => {
      const game = survey.game_title || 'Unknown Game';
      if (!acc[game]) {
        acc[game] = { totalFps: 0, count: 0 };
      }
      acc[game].totalFps += survey.fps_average || 0;
      acc[game].count += 1;
      return acc;
    }, {} as Record<string, { totalFps: number; count: number }>);

    const topPerformingGames = Object.entries(gamePerformance)
      .map(([game, data]) => ({
        game,
        avgFps: Math.round(data.totalFps / data.count),
        count: data.count
      }))
      .sort((a, b) => b.avgFps - a.avgFps)
      .slice(0, 5);

    // Performance trend (simplified - based on recent vs older surveys)
    const sortedSurveys = [...surveys].sort((a, b) => 
      new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime()
    );
    const recentSurveys = sortedSurveys.slice(0, Math.ceil(surveys.length / 3));
    const olderSurveys = sortedSurveys.slice(-Math.ceil(surveys.length / 3));
    
    const recentAvgFps = recentSurveys.reduce((sum, s) => sum + (s.fps_average || 0), 0) / recentSurveys.length;
    const olderAvgFps = olderSurveys.reduce((sum, s) => sum + (s.fps_average || 0), 0) / olderSurveys.length;
    
    let performanceTrend: 'up' | 'down' | 'stable' = 'stable';
    if (recentAvgFps > olderAvgFps * 1.1) performanceTrend = 'up';
    else if (recentAvgFps < olderAvgFps * 0.9) performanceTrend = 'down';

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (avgFps < 30) {
      recommendations.push('Consider lowering graphics settings for better performance');
    }
    if (avgSmoothness < 6) {
      recommendations.push('Frame pacing issues detected - try enabling V-Sync or FreeSync');
    }
    if (deviceBreakdown.handheld && avgFps > 40) {
      recommendations.push('Great handheld performance! Consider sharing your settings');
    }
    if (surveys.some(s => s.frame_gen) && avgFps > 60) {
      recommendations.push('Frame generation is working well for your setup');
    }
    if (surveys.some(s => s.upscale) && avgSmoothness > 7) {
      recommendations.push('Upscaling technology is providing good quality gains');
    }

    return {
      avgFps,
      avgSmoothness,
      totalSurveys: surveys.length,
      uniqueGames: new Set(surveys.map(s => s.game_title).filter(Boolean)).size,
      deviceBreakdown,
      topPerformingGames,
      performanceTrend,
      recommendations
    };
  }, [surveys]);

  const getTrendIcon = () => {
    switch (metrics.performanceTrend) {
      case 'up':
        return <TrendingUp className="text-green-400" size={16} />;
      case 'down':
        return <TrendingDown className="text-red-400" size={16} />;
      default:
        return <Minus className="text-yellow-400" size={16} />;
    }
  };

  const getTrendColor = () => {
    switch (metrics.performanceTrend) {
      case 'up':
        return 'text-green-400';
      case 'down':
        return 'text-red-400';
      default:
        return 'text-yellow-400';
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'desktop':
        return <Monitor className="text-blue-400" size={14} />;
      case 'laptop':
        return <HardDrive className="text-green-400" size={14} />;
      case 'handheld':
        return <Gamepad2 className="text-purple-400" size={14} />;
      default:
        return <Monitor className="text-gray-400" size={14} />;
    }
  };

  if (surveys.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <Target size={16} />
              Performance Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold text-slate-200">
                {Math.round((metrics.avgFps / 60) * 100)}%
              </div>
              {getTrendIcon()}
            </div>
            <p className="text-xs text-slate-500">
              Based on {metrics.avgFps} avg FPS
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <Zap size={16} />
              Smoothness Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-200">
              {metrics.avgSmoothness}/10
            </div>
            <p className="text-xs text-slate-500">
              Across {metrics.uniqueGames} games
            </p>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center gap-2">
              <Award size={16} />
              Performance Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn("text-2xl font-bold capitalize", getTrendColor())}>
              {metrics.performanceTrend}
            </div>
            <p className="text-xs text-slate-500">
              Recent vs historical
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Device Breakdown */}
      <Card className="bg-slate-900/60 border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-slate-200 flex items-center gap-2">
            <Monitor size={18} />
            Device Performance Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(metrics.deviceBreakdown).map(([device, count]) => (
              <motion.div
                key={device}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg"
              >
                {getDeviceIcon(device)}
                <div>
                  <div className="text-slate-200 font-medium capitalize">{device}</div>
                  <div className="text-slate-400 text-sm">{count} surveys</div>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Games */}
      {metrics.topPerformingGames.length > 0 && (
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-slate-200 flex items-center gap-2">
              <Award size={18} />
              Top Performing Games
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.topPerformingGames.map((game, index) => (
                <motion.div
                  key={game.game}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg"
                >
                  <div>
                    <div className="text-slate-200 font-medium">{game.game}</div>
                    <div className="text-slate-400 text-sm">{game.count} surveys</div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 font-bold">{game.avgFps} FPS</div>
                    <div className="text-slate-500 text-sm">Average</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {metrics.recommendations.length > 0 && (
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-slate-200 flex items-center gap-2">
              <Target size={18} />
              Performance Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.recommendations.map((recommendation, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg"
                >
                  <Zap className="text-purple-400 mt-0.5" size={16} />
                  <p className="text-slate-200 text-sm">{recommendation}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
