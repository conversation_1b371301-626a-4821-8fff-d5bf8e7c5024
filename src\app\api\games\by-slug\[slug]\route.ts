import { NextRequest, NextResponse } from 'next/server';
import { getGameBySlug, getGameStats } from '@/lib/services/gameService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    
    const game = await getGameBySlug(slug);
    
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Get basic stats
    const stats = await getGameStats(game.id);

    return NextResponse.json({
      game,
      stats
    });
  } catch (error) {
    console.error('Error fetching game:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}