'use client';

import React, { useState, useEffect, memo, ReactNode } from 'react';
import type { Review } from '@/lib/types';
import { 
  Star, Heart, Share2, Bookmark, ExternalLink,
  Gamepad2, Calendar, Clock, Users, Trophy, Code, Cpu,
  Monitor, Eye, MessageCircle, Smartphone, Info, Award,
  Palette, Sparkles, Target, Zap, ArrowRight, ArrowLeft, BookOpen
} from 'lucide-react';
import PlatformIcon from './PlatformIcon';

import { formatDate } from '@/lib/utils/dateUtils'; // Import shared formatDate
import ReviewScoreComponent from '@/components/review-new/reviewScoreComponent'; // Keep this import

interface CodeTitleProps {
  children: React.ReactNode;
  className?: string;
}

// Import standardized types
import type { MonetizationBlock } from '@/lib/types';

interface CreatorBannerTopProps {
  review?: Review;
}

const CreatorBannerTop = ({ review }: CreatorBannerTopProps) => {
  const [isHovered, setIsHovered] = useState(false);

  // Return early if no review data
  if (!review) {
    return null;
  }

  // Check for custom banner from monetization blocks
  const customBanner = review.monetizationBlocks?.find(
    (block: MonetizationBlock) => block.placement === 'after-banner'
  );

  const CodeTitle = ({ children, className = "" }: CodeTitleProps) => (
    <span className={`font-mono relative inline-block ${className}`}>
    <span className="text-emerald-400/60">&lt;</span>
    <span className="mx-1 relative hover:animate-pulse">
    {children}
    <span className="absolute inset-0 bg-gradient-to-r from-emerald-400/0 via-emerald-400/30 to-emerald-400/0 opacity-0 hover:opacity-100 transition-opacity duration-200"/>
    </span>
    <span className="text-emerald-400/60">/&gt;</span>
    </span>
  );

  // If custom banner exists, render it
  if (customBanner && customBanner.data.imageUrl && customBanner.data.linkUrl) {
    return (
      <div className="mx-auto responsive-content-width mb-8">
        <div className="border border-white/5 bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl overflow-hidden shadow-lg shadow-black/10 transform transition-all duration-300 ease-out hover:shadow-xl hover:shadow-black/20 hover:scale-[1.002]">
          <div className="p-3 sm:p-4">
            <a 
              href={customBanner.data.linkUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="block relative overflow-hidden rounded-lg border border-white/5 bg-slate-800 hover:bg-slate-700 hover:border-white/10 transition-all duration-500 group"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <div className="relative">
                <img 
                  src={customBanner.data.imageUrl} 
                  alt="Sponsored Content"
                  className="w-full h-auto max-h-[200px] object-cover transition-all duration-300 group-hover:scale-105"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                <div className="hidden p-8 flex items-center justify-center bg-slate-800">
                  <div className="text-center">
                    <div className="w-12 h-12 rounded-md bg-slate-700 flex items-center justify-center mb-2 mx-auto">
                      <ExternalLink className="h-6 w-6 text-slate-400" />
                    </div>
                    <p className="text-sm text-slate-400">Custom Banner</p>
                  </div>
                </div>
                
                {/* Overlay with sponsored label */}
                <div className="absolute top-2 right-2 px-2 py-1 bg-black rounded text-xs font-mono text-slate-300 border border-white/10">
                  <span className="text-violet-400">&lt;</span>
                  Sponsored
                  <span className="text-violet-400">/&gt;</span>
                </div>
                
                {/* Hover effect overlay */}
                <div className={`absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5 opacity-0 transition-opacity duration-500 ${
                  isHovered ? 'opacity-100' : ''
                }`} />
              </div>
              
              {/* Subtle Corner Elements */}
              <div className="absolute top-1.5 left-1.5 w-3 h-3 border-l-2 border-t-2 border-emerald-500/20 rounded-tl" />
              <div className="absolute top-1.5 right-1.5 w-3 h-3 border-r-2 border-t-2 border-blue-500/20 rounded-tr" />
              <div className="absolute bottom-1.5 left-1.5 w-3 h-3 border-l-2 border-b-2 border-emerald-500/20 rounded-bl" />
              <div className="absolute bottom-1.5 right-1.5 w-3 h-3 border-r-2 border-b-2 border-blue-500/20 rounded-br" />
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Default creator spotlight banner
  return (
    <div className="mx-auto responsive-content-width mb-8">
    <style jsx>{`
    .glitch-top:hover {
    animation: glitchTop 0.3s ease-in-out;
    }
    @keyframes glitchTop {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-1px, -1px); }
    20% { transform: translate(1px, 1px); }
    30% { transform: translate(-1px, 1px); }
    40% { transform: translate(1px, -1px); }
    50% { transform: translate(-1px, -1px); }
    60% { transform: translate(1px, 1px); }
    70% { transform: translate(-1px, 1px); }
    80% { transform: translate(1px, -1px); }
    90% { transform: translate(-1px, -1px); }
    }
    .ad-glow-top {
    background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
    background-size: 400% 400%;
    animation: gradientShiftTop 6s ease infinite;
    }
    @keyframes gradientShiftTop {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    }
    `}</style>

<div className="border border-white/5 bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl overflow-hidden shadow-lg shadow-black/10 transform transition-all duration-300 ease-out hover:shadow-xl hover:shadow-black/20 hover:scale-[1.002]">
    {/* Creator Banner Top - Compact design for top placement */}
    <div className="p-3 sm:p-4">
    <div 
    className={`relative overflow-hidden rounded-lg border border-white/5 bg-slate-800 hover:bg-slate-700 hover:border-white/10 transition-all duration-500 ${
    isHovered ? 'ad-glow-top' : ''
    }`}
    onMouseEnter={() => setIsHovered(true)}
    onMouseLeave={() => setIsHovered(false)}
    >
    {/* Content Area - More compact for top banner */}
    <div className="min-h-[100px] sm:min-h-[140px] flex items-center justify-center p-3 sm:p-4">
    <div className="text-center space-y-3">
    {/* Visual Elements */}
    <div className="flex justify-center space-x-2 mb-3">
    {[1, 2, 3, 4].map((i) => (
    <div 
    key={i}
    className={`w-2.5 h-2.5 rounded-full bg-gradient-to-r from-emerald-500 to-blue-500 transition-all duration-300 ${
    isHovered ? 'animate-bounce' : 'opacity-50'
    }`}
    style={{ animationDelay: `${i * 0.1}s` }}
    />
    ))}
    </div>
    
    {/* Ad Format Info */}
    <div className="space-y-2">
    <p className="text-slate-300 font-mono text-sm">
    <CodeTitle className="glitch-top">
    Creator Spotlight
    </CodeTitle>
    </p>
    <div className="flex flex-wrap justify-center gap-1.5 text-xs">
    {['Leaderboard', 'Banner', 'Mobile'].map((format) => (
    <span 
    key={format}
    className="px-2 py-1 bg-slate-700/50 rounded border border-white/10 text-slate-400 font-mono hover:text-white hover:border-emerald-400/30 transition-all duration-200"
    >
    <CodeTitle className="text-xs">
    {format}
    </CodeTitle>
    </span>
    ))}
    </div>
    </div>
    
    {/* Call to Action */}
    <div className={`mt-3 transition-all duration-300 ${isHovered ? 'opacity-100' : 'opacity-70'}`}>
    <button
    className="px-3 py-1.5 bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-500 hover:to-blue-500 text-white rounded-md transition transform duration-200 hover:scale-105"
    >
    <div className="flex items-center justify-center space-x-1.5">
    <span className="text-xs uppercase tracking-wide">Feature Your Content</span>
    </div>
    </button>
    </div>
    </div>
    </div>
    
    {/* Subtle Corner Elements */}
    <div className="absolute top-1.5 left-1.5 w-3 h-3 border-l-2 border-t-2 border-emerald-500/20 rounded-tl" />
    <div className="absolute top-1.5 right-1.5 w-3 h-3 border-r-2 border-t-2 border-blue-500/20 rounded-tr" />
    <div className="absolute bottom-1.5 left-1.5 w-3 h-3 border-l-2 border-b-2 border-emerald-500/20 rounded-bl" />
    <div className="absolute bottom-1.5 right-1.5 w-3 h-3 border-r-2 border-b-2 border-blue-500/20 rounded-br" />
    
    {/* Hover Glow Effect */}
    <div className={`absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-blue-500/5 opacity-0 transition-opacity duration-500 ${
    isHovered ? 'opacity-100' : ''
    }`} />
    </div>
    </div>
    </div>
    </div>
  );
};

// Custom Avatar Component
const Image = memo(({ src, alt, fallback, className }: { src: string, alt: string, fallback?: string, className?: string }) => {
  const [imageError, setImageError] = useState(false);
  
  return (
    <div className={`relative overflow-hidden rounded-full ${className}`}>
    {src && !imageError ? (
    <img 
    src={src} 
    alt={alt} 
    className="w-full h-full object-cover"
    onError={() => setImageError(true)}
    />
    ) : (
    <div className="w-full h-full bg-slate-700/50 flex items-center justify-center text-white font-mono text-sm">
    {fallback}
    </div>
    )}
    </div>
  );
});

// Platform Icon Component
// PlatformIcon removed - now imported from its own component file

// LinearToggle moved to a standalone component file

// Removed mock review data - components should handle missing data properly

interface InteractiveReviewBannerProps {
  review?: Review;
}

const InteractiveReviewBanner = ({ review }: InteractiveReviewBannerProps) => {
  const [animationStarted, setAnimationStarted] = useState(false);
  const [showCover, setShowCover] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setAnimationStarted(true), 300);
    return () => clearTimeout(timer);
  }, []);

  // Return loading state if no review data is provided
  if (!review) {
    return (
      <div className="flex flex-col items-center justify-center w-full px-4 sm:px-6 lg:px-8 mx-auto">
        <div className="w-full relative mb-24 shadow-xl flex-shrink-0 responsive-banner bg-slate-800 rounded-lg">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl text-slate-400 mb-4">Loading review...</div>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-400 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const formatTime = (seconds: number | null | undefined | string): string => {
    if (typeof seconds === 'string') return seconds; // If it's already a string, return as is
    if (seconds == null) return 'N/A';
    const h = Math.floor(Number(seconds) / 3600);
    const m = Math.floor((Number(seconds) % 3600) / 60);
    return h > 0 ? `${h}h ${m}m` : `${m}m`;
  };

  // Enhanced date formatting function for release date (DD of Month, YYYY)
  const formatReleaseDate = (dateValue: any): string => {
    if (!dateValue) return 'N/A';
    
    try {
      // Handle Timestamp object with seconds/nanoseconds properties
      let date: Date;
      if (dateValue.seconds && dateValue.nanoseconds) {
        // Firebase Timestamp format
        date = new Date(dateValue.seconds * 1000);
      } else if (typeof dateValue === 'string') {
        date = new Date(dateValue);
      } else if (dateValue instanceof Date) {
        date = dateValue;
      } else if (typeof dateValue === 'number') {
        // Handle Unix timestamp (seconds) or JavaScript timestamp (milliseconds)
        date = dateValue > 1000000000000 
          ? new Date(dateValue) 
          : new Date(dateValue * 1000);
      } else {
        console.warn('Unknown date format in formatReleaseDate:', dateValue);
        return 'N/A';
      }
      
      if (isNaN(date.getTime())) {
        console.warn('Invalid date in formatReleaseDate:', dateValue);
        return 'N/A';
      }
      
      // Format as DD of Month, YYYY for release date
      const day = String(date.getDate());
      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      const month = months[date.getMonth()];
      const year = date.getFullYear();
      return `${day} of ${month}, ${year}`;
    } catch (error) {
      console.error('Error formatting release date:', dateValue, error);
      return 'N/A';
    }
  };

  // SIMPLE: Just extract MM/YYYY from whatever date format we have
  const formatDatePlayed = (dateValue: any): string => {
    if (!dateValue) return 'N/A';

    try {
      let date: Date;

      // Handle string dates (like "2022-02-01" from database)
      if (typeof dateValue === 'string') {
        // If it's already MM/YYYY format, return it
        if (/^\d{1,2}\/\d{4}$/.test(dateValue)) {
          const [month, year] = dateValue.split('/');
          return `${month.padStart(2, '0')}/${year}`;
        }

        // Parse any other string date format
        date = new Date(dateValue);
      }
      // Handle Timestamp objects
      else if (dateValue.seconds && dateValue.nanoseconds) {
        date = new Date(dateValue.seconds * 1000);
      }
      // Handle Date objects
      else if (dateValue instanceof Date) {
        date = dateValue;
      }
      else {
        return 'N/A';
      }

      // Extract MM/YYYY from the date
      if (!isNaN(date.getTime())) {
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${month}/${year}`;
      }
    } catch (error) {
      console.warn('Error formatting date played:', error);
    }

    return 'N/A';
  };

  return (
    <>
    <style dangerouslySetInnerHTML={{
    __html: `
    @keyframes slideInScale {
    0% {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
    }
    100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    }
    }
    
    .responsive-banner {
    height: clamp(280px, 55vw, 320px);
    }
    
    @media (min-width: 640px) {
    .responsive-banner {
    height: clamp(220px, 35vw, 280px);
    }
    }
    
    @media (min-width: 1024px) {
    .responsive-banner {
    height: clamp(500px, 50vw, 640px);
    }
    }
    
    .mobile-scroll-area {
    max-height: 80px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
    }
    
    .mobile-scroll-area::-webkit-scrollbar {
    width: 3px;
    }
    
    .mobile-scroll-area::-webkit-scrollbar-track {
    background: transparent;
    }
    
    .mobile-scroll-area::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 1px;
    }
    
    .mobile-scroll-area::-webkit-scrollbar-thumb:hover {
    background-color: rgba(148, 163, 184, 0.5);
    }
    
    @media (min-width: 640px) {
    .mobile-scroll-area {
    max-height: none;
    overflow-y: visible;
    }
    }
    
    /* Custom responsive width utilities */
    .responsive-text-width {
    width: 100%;
    }
    
    .responsive-banner-width {
    width: 100%;
    }
    
    .responsive-content-width {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    }
    
    
    @media (min-width: 640px) {
    .responsive-content-width {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    }
    }
    
    @media (min-width: 1024px) {
    .responsive-content-width {
    padding-left: 2rem;
    padding-right: 2rem;
    }
    }
    
    @media (min-width: 1360px) {
    .responsive-text-width {
    width: 80%;
    }
    
    .responsive-banner-width {
    width: 80%;
    }
    
    .responsive-content-width {
    width: 75%;
    padding-left: 0;
    padding-right: 0;
    }
    }
    
    /* Gaming-Appropriate Vertical Spacing */
    .review-spacing-large {
      margin-bottom: 4rem; /* 64px - substantial breathing room */
    }
    
    .review-spacing-medium {
      margin-bottom: 2rem; /* 32px - clear separation */
    }
    
    .review-spacing-small {
      margin-bottom: 1rem; /* 16px - minimal but visible */
    }
    
    /* Mobile spacing adjustments for better UX */
    @media (max-width: 767px) {
      .review-spacing-large {
        margin-bottom: 3rem; /* 48px on mobile */
      }
      .review-spacing-medium {
        margin-bottom: 1.5rem; /* 24px on mobile */
      }
    }
    
    /* Legacy responsive utilities for backward compatibility */
    .responsive-text-width {
    width: 100%;
    }
    
    .responsive-banner-width {
    width: 100%;
    }
    
    .responsive-content-width {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
    }
    
    @media (min-width: 640px) {
    .responsive-content-width {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    }
    }
    
    @media (min-width: 1024px) {
    .responsive-content-width {
    padding-left: 2rem;
    padding-right: 2rem;
    }
    }
    
    @media (min-width: 1360px) {
    .responsive-text-width {
    width: 80%;
    }
    
    .responsive-banner-width {
    width: 80%;
    }
    
    .responsive-content-width {
    width: 75%;
    padding-left: 0;
    padding-right: 0;
    }
    }

    /* Banner image styling with smart positioning */
    .banner-image-smart {
    object-position: center 25%;
    filter: brightness(1.1) contrast(1.05) saturate(1.1);
    }

    /* High-quality image rendering */
    .banner-image-hq {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: high-quality;
    image-rendering: pixelated;
    image-rendering: auto;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: transform;
    }

    /* Responsive object positioning for better cropping */
    @media (max-width: 640px) {
    .banner-image-smart {
    object-position: center 30%;
    }
    }

    @media (min-width: 1024px) {
    .banner-image-smart {
    object-position: center 20%;
    }
    }
    `
    }} />
    
    <div className="flex flex-col items-center justify-center w-full px-4 sm:px-6 lg:px-8 mx-auto">
    <div
    className="w-full relative mb-24 shadow-xl flex-shrink-0 responsive-banner"
    style={{
    filter: 'drop-shadow(0 20px 40px rgba(0, 0, 0, 0.6)) drop-shadow(0 10px 20px rgba(0, 0, 0, 0.4))'
    }}
    >
    <div
    className="relative w-full h-full rounded-lg overflow-visible"
    style={{
    mask: `linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 1%, rgba(0,0,0,0.3) 3%, rgba(0,0,0,0.7) 5%, rgba(0,0,0,1) 10%, rgba(0,0,0,1) 90%, rgba(0,0,0,0.7) 95%, rgba(0,0,0,0.3) 97%, rgba(0,0,0,0.05) 99%, rgba(0,0,0,0) 100%)`,
    WebkitMask: `linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 1%, rgba(0,0,0,0.3) 3%, rgba(0,0,0,0.7) 5%, rgba(0,0,0,1) 10%, rgba(0,0,0,1) 90%, rgba(0,0,0,0.7) 95%, rgba(0,0,0,0.3) 97%, rgba(0,0,0,0.05) 99%, rgba(0,0,0,0) 100%)`
    }}
    >
    <div className="absolute inset-0 transition-all duration-500">
    <div className={`absolute inset-0 transition-all duration-500 ${showCover ? 'backdrop-blur-sm' : ''}`}>
    {review.mainImageUrl ? (
    <img
    src={review.mainImageUrl}
    alt="Game Image"
    className={`w-full h-full object-cover transition-all duration-500 banner-image-smart banner-image-hq ${
    showCover ? 'opacity-70' : 'opacity-100'
    }`}
    style={{
      objectPosition: review.mainImagePosition || undefined
    }}
    loading="eager"
    decoding="sync"
    fetchPriority="high"
    />
    ) : (
    <div className="w-full h-full bg-gradient-to-br from-slate-900 via-violet-900 to-cyan-900" />
    )}
    <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/30" />
    <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50" />
    </div>
    </div>

    {/* Dark overlay when score is active */}
    <div 
    className={`absolute inset-0 bg-black transition-all duration-500 z-20 pointer-events-none ${
    showCover ? 'opacity-60' : 'opacity-0'
    }`}
    />

    {/* Game Cover Image - Positioned Higher */}
    <div 
    className={`absolute left-1/2 top-1/3 transform -translate-x-1/2 -translate-y-1/2 z-30 w-32 h-44 md:w-36 md:h-50 lg:w-40 lg:h-56 transition-all duration-500 ease-in-out ${
    showCover ? 'opacity-100 scale-100' : 'opacity-0 scale-90 pointer-events-none'
    }`}
    >
    {review.igdbCoverUrl && (
    <img
    src={review.igdbCoverUrl}
    alt={review.gameName}
    className="w-full h-full object-cover rounded-lg shadow-2xl border-2 border-white/10"
    onError={(e) => {
      const target = e.target as HTMLImageElement;
      target.style.display = 'none';
      console.warn(`Failed to load IGDB cover in banner: ${target.src}`);
    }}
    />
    )}
    </div>

    {/* Rating System - Centered Above Bottom Strip */}
    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-40 flex justify-center items-center">
    <div className="flex justify-center items-center">
    <ReviewScoreComponent
    review={review}
    gameCoverUrl={review.igdbCoverUrl || undefined}
    onToggleCover={setShowCover}
    />
    </div>
    </div>

    {/* Action Button - Top Left */}
    <div className="absolute bottom-4 left-4 sm:bottom-6 sm:left-6 z-20">
    <div className="p-4 sm:p-6">
    {review.officialGameLink && (
    <a 
    href={review.officialGameLink} 
    target="_blank" 
    className="flex items-center space-x-2 px-4 py-2 sm:px-6 sm:py-2.5 bg-slate-800 hover:bg-slate-700 text-slate-200 hover:text-white border border-slate-600 hover:border-slate-500 rounded-lg transition-all font-semibold text-sm sm:text-base"
    >
    <ExternalLink className="w-4 h-4" /> 
    <span>&lt;Visit Game Site/&gt;</span>
    </a>
    )}
    </div>
    </div>

    {/* LinearToggle component removed and moved to a standalone component */}

    {/* Compact Info Strip Overlay - Bottom with fixed conditional rendering and separate date fields */}
    <div className="absolute bottom-0 left-0 right-0 z-30">
    <div className="bg-black border-t border-white/10 px-3 sm:px-6 py-2 sm:py-3 w-full">
    <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-4 lg:gap-6 text-xs text-white/80">
    <span className="whitespace-nowrap">Date Played: <span className="text-white font-medium">{formatDatePlayed(review.datePlayed)}</span></span>
    <span className="hidden sm:inline">•</span>
    <span className="whitespace-nowrap">Release Date: <span className="text-white font-medium">{formatReleaseDate(review.releaseDate)}</span></span>
    {review.developers && Array.isArray(review.developers) && review.developers.length > 0 && (
    <>
    <span className="hidden md:inline">•</span>
    <span className="whitespace-nowrap">Developed by: <span className="text-white font-medium">
      {review.developers.length === 1 
        ? review.developers[0] 
        : review.developers[0].length > 20 
          ? `${review.developers[0].substring(0, 20)}...` 
          : review.developers[0]}
    </span></span>
    </>
    )}
    {review.publishers && Array.isArray(review.publishers) && review.publishers.length > 0 && (
    <>
    <span className="hidden md:inline">•</span>
    <span className="whitespace-nowrap">Published by: <span className="text-white font-medium">
      {review.publishers.length === 1 
        ? review.publishers[0] 
        : review.publishers[0].length > 20 
          ? `${review.publishers[0].substring(0, 20)}...` 
          : review.publishers[0]}
    </span></span>
    </>
    )}
    {review.gameEngines && Array.isArray(review.gameEngines) && review.gameEngines.length > 0 && 
    // Only show Engine if there's a single developer and publisher
    (!review.developers || !Array.isArray(review.developers) || review.developers.length <= 1) && 
    (!review.publishers || !Array.isArray(review.publishers) || review.publishers.length <= 1) && (
    <>
    <span className="hidden lg:inline">•</span>
    <span className="whitespace-nowrap">Engine: <span className="text-white">{review.gameEngines[0]}</span></span>
    </>
    )}
    {review.timeToBeatNormally && (
    <>
    <span className="hidden lg:inline">•</span>
    <span className="whitespace-nowrap">~{formatTime(review.timeToBeatNormally)}</span>
    </>
    )}
    </div>
    </div>
    </div>
    </div>
    </div>
    </div>
    </>
  );
};

export default InteractiveReviewBanner;