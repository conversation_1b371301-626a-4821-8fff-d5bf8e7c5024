# CriticalPixel RLS Security Implementation & Testing Guide
## Comprehensive Row Level Security Validation and Implementation

### 🎯 **Guide Objective**
This guide provides a complete implementation and testing plan for Row Level Security (RLS) policies in the CriticalPixel project, ensuring maximum database security and proper access control.

### 📊 **Current Status Analysis**
**Date:** January 13, 2025
**QA Specialist:** Microsoft Senior QA Standards
**Project:** CriticalPixel Supabase RLS Implementation

#### **Current RLS Status Summary:**
- ✅ **RLS Enabled Tables:** 12/18 tables have RLS enabled
- ✅ **Security Functions:** 4/6 core functions implemented
- ⚠️ **Missing RLS:** 6 tables need RLS activation
- ⚠️ **Missing Policies:** Several critical policies need implementation
- ⚠️ **Missing Functions:** 2 advanced security functions needed

#### **Tables with RLS Enabled:**
- ✅ profiles, reviews, games, comments
- ✅ performance_surveys, post_views, activity_log
- ✅ comment_votes, gaming_profiles, social_media_profiles
- ✅ user_achievements, user_relationships

#### **Tables Missing RLS:**
- ❌ achievements
- ❌ cpu_specs, gpu_specs
- ❌ hardware_configs
- ❌ review_analytics
- ❌ review_likes

#### **Existing Security Functions:**
- ✅ `is_admin(user_id uuid)` - Admin verification
- ✅ `is_owner(user_id uuid, content_user_id uuid)` - Ownership check
- ✅ `is_public_content(content_status text)` - Public content check
- ✅ `is_current_user_admin()` - Current user admin check

#### **Missing Security Functions:**
- ❌ `can_view_profile_field()` - Privacy-aware field access
- ❌ `can_publish_review()` - Review publishing permissions
- ❌ `can_moderate_comment()` - Comment moderation rights

### 🔒 **Security Architecture Overview**

```mermaid
graph TD
    A[User Request] --> B[Supabase Auth]
    B --> C{RLS Policies}
    C --> D[Database Access]
    
    C --> C1[User Policies]
    C --> C2[Content Policies]
    C --> C3[Admin Policies]
    C --> C4[Analytics Policies]
    
    C1 --> D1[Profile Access]
    C2 --> D2[Review/Comment Access]
    C3 --> D3[Admin Operations]
    C4 --> D4[Analytics Data]
    
    E[auth.uid()] --> C
    F[User Roles] --> C
    G[Content Ownership] --> C
```

### 📝 **Implementation Plan**

## Phase 1: Complete Missing RLS Enablement (Priority: CRITICAL)

### **Task 1.1: Enable RLS on Missing Tables**
**Estimated Time:** 30 minutes
**Risk Level:** LOW

```sql
-- Enable RLS on missing tables
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE cpu_specs ENABLE ROW LEVEL SECURITY;
ALTER TABLE gpu_specs ENABLE ROW LEVEL SECURITY;
ALTER TABLE hardware_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_likes ENABLE ROW LEVEL SECURITY;
```

### **Task 1.2: Create Missing Security Functions**
**Estimated Time:** 45 minutes
**Risk Level:** MEDIUM

```sql
-- Function to check privacy settings for profile fields
CREATE OR REPLACE FUNCTION can_view_profile_field(
  viewer_id uuid, 
  profile_owner_id uuid, 
  field_name text
)
RETURNS boolean AS $$
DECLARE
  privacy_settings jsonb;
BEGIN
  -- Owner can always see their own data
  IF viewer_id = profile_owner_id THEN
    RETURN true;
  END IF;
  
  -- Get privacy settings
  SELECT p.privacy_settings INTO privacy_settings
  FROM profiles p WHERE p.id = profile_owner_id;
  
  -- Check specific field privacy
  CASE field_name
    WHEN 'online_status' THEN
      RETURN COALESCE((privacy_settings->>'showOnlineStatus')::boolean, true);
    WHEN 'gaming_profiles' THEN
      RETURN COALESCE((privacy_settings->>'showGamingProfiles')::boolean, true);
    WHEN 'achievements' THEN
      RETURN COALESCE((privacy_settings->>'showAchievements')::boolean, true);
    ELSE
      RETURN true;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle review publishing permissions
CREATE OR REPLACE FUNCTION can_publish_review(user_id uuid, review_author_id uuid)
RETURNS boolean AS $$
BEGIN
  -- User can publish their own reviews
  -- Admins can publish any review
  RETURN is_owner(user_id, review_author_id) OR is_admin(user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for comment moderation
CREATE OR REPLACE FUNCTION can_moderate_comment(moderator_id uuid, comment_author_id uuid)
RETURNS boolean AS $$
BEGIN
  -- Admins can moderate any comment
  -- Users can moderate comments on their own content
  RETURN is_admin(moderator_id) OR 
         EXISTS (
           SELECT 1 FROM comments c
           JOIN reviews r ON c.review_id = r.id
           WHERE c.author_id = comment_author_id 
           AND r.author_id = moderator_id
         );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Phase 2: Implement Missing Critical Policies (Priority: HIGH)

### **Task 2.1: Hardware Configuration Security**
**Estimated Time:** 1 hour
**Risk Level:** MEDIUM

```sql
-- Users can manage their own hardware configurations
CREATE POLICY "Users manage own hardware configs" ON hardware_configs
  FOR ALL USING (is_owner(auth.uid(), user_id))
  WITH CHECK (is_owner(auth.uid(), user_id));

-- Public hardware configs for performance comparison (anonymized)
CREATE POLICY "Public hardware comparison data" ON hardware_configs
  FOR SELECT USING (
    auth.uid() IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM performance_surveys ps
      JOIN reviews r ON ps.review_id = r.id
      WHERE ps.hardware_config_id = hardware_configs.id
      AND r.status = 'published'
    )
  );

-- Admins can manage all hardware configs
CREATE POLICY "Admins manage all hardware configs" ON hardware_configs
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));
```

### **Task 2.2: Review Analytics Security**
**Estimated Time:** 1 hour
**Risk Level:** HIGH

```sql
-- Users can view analytics for their own content
CREATE POLICY "Authors view own review analytics" ON review_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND is_owner(auth.uid(), r.author_id)
    )
  );

-- Admins can view all analytics data
CREATE POLICY "Admins view all analytics" ON review_analytics
  FOR SELECT USING (is_admin(auth.uid()));

-- System can insert analytics data
CREATE POLICY "System can insert analytics" ON review_analytics
  FOR INSERT WITH CHECK (true);

-- Only admins can update/delete analytics
CREATE POLICY "Admins manage analytics data" ON review_analytics
  FOR UPDATE USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins delete analytics data" ON review_analytics
  FOR DELETE USING (is_admin(auth.uid()));
```

### **Task 2.3: Review Likes Security**
**Estimated Time:** 45 minutes
**Risk Level:** MEDIUM

```sql
-- Users can like reviews (insert only)
CREATE POLICY "Users can like reviews" ON review_likes
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );

-- Users can remove their own likes
CREATE POLICY "Users can remove own likes" ON review_likes
  FOR DELETE USING (is_owner(auth.uid(), user_id));

-- Users can view like counts (aggregated)
CREATE POLICY "Public like visibility" ON review_likes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );

-- Admins can manage all likes
CREATE POLICY "Admins manage all likes" ON review_likes
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));
```

### **Task 2.4: Achievements Security**
**Estimated Time:** 30 minutes
**Risk Level:** LOW

```sql
-- Public achievements are viewable by everyone
CREATE POLICY "Public achievements viewable" ON achievements
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only admins can manage achievement definitions
CREATE POLICY "Admins manage achievements" ON achievements
  FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins update achievements" ON achievements
  FOR UPDATE USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins delete achievements" ON achievements
  FOR DELETE USING (is_admin(auth.uid()));
```

### **Task 2.5: Hardware Specs Security**
**Estimated Time:** 30 minutes
**Risk Level:** LOW

```sql
-- CPU Specs policies
CREATE POLICY "Public CPU specs viewable" ON cpu_specs
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins manage CPU specs" ON cpu_specs
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

-- GPU Specs policies
CREATE POLICY "Public GPU specs viewable" ON gpu_specs
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins manage GPU specs" ON gpu_specs
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));
```

## Phase 3: Advanced Security Testing (Priority: HIGH)

### **Task 3.1: Access Control Testing**
**Estimated Time:** 2 hours
**Risk Level:** CRITICAL

#### **Test Suite 1: User Isolation Tests**
```sql
-- Test 1: User cannot access other users' private data
-- Create test users and verify data isolation

-- Test 2: Draft reviews not visible to non-owners
-- Verify draft content remains private

-- Test 3: Admin access works across all data
-- Verify admin privileges function correctly

-- Test 4: Unauthenticated users properly restricted
-- Test anonymous access limitations
```

#### **Test Suite 2: Privacy Settings Tests**
```sql
-- Test 5: Privacy settings properly enforced
-- Verify profile field visibility controls

-- Test 6: Anonymous data aggregation working
-- Test hardware comparison data anonymization

-- Test 7: Personal information properly protected
-- Verify sensitive data protection

-- Test 8: GDPR compliance verified
-- Test data access and deletion rights
```

#### **Test Suite 3: Edge Case Tests**
```sql
-- Test 9: Deleted user data handling
-- Verify orphaned data management

-- Test 10: Ownership transfer scenarios
-- Test content ownership changes

-- Test 11: Admin privilege escalation prevention
-- Verify admin role cannot be self-assigned

-- Test 12: SQL injection attack prevention
-- Test RLS policy injection resistance
```

### **Task 3.2: Performance Impact Assessment**
**Estimated Time:** 1.5 hours
**Risk Level:** MEDIUM

#### **Performance Benchmarks:**
- Profile queries: < 100ms (baseline comparison)
- Review listing: < 200ms (with RLS overhead)
- Comment threads: < 150ms (with nested policies)
- Admin queries: < 300ms (with elevated access)
- Search operations: < 500ms (with content filtering)

---

**Next Steps:** Proceed to implementation using the completion tracking file for detailed progress monitoring.
