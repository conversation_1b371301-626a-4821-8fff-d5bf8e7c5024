/**
 * Handheld gaming PC database and related functionality
 * Data includes Windows and Linux handheld gaming PCs only
 */

export interface HandheldSpec {
  id: string;
  name: string;
  manufacturer: 'Valve' | 'ASUS' | 'Lenovo' | 'MSI' | 'GPD' | 'Ayaneo' | 'OneXPlayer' | 'TECNO';
  model: string; // Just the model name like "Steam Deck OLED" or "ROG Ally"
}

// Windows and Linux Handheld Gaming PC Database
export const HANDHELD_DATABASE: HandheldSpec[] = [
  // Valve Steam Deck Series (SteamOS/Linux)
  { id: 'valve-steam-deck', name: 'Valve Steam Deck', manufacturer: 'Valve', model: 'Steam Deck' },
  { id: 'valve-steam-deck-oled', name: 'Valve Steam Deck OLED', manufacturer: 'Valve', model: 'Steam Deck OLED' },

  // ASUS ROG Ally Series (Windows)
  { id: 'asus-rog-ally', name: 'ASUS ROG Ally', manufacturer: 'ASUS', model: 'ROG Ally' },
  { id: 'asus-rog-ally-z1', name: 'ASUS ROG Ally Z1', manufacturer: 'ASUS', model: 'ROG Ally Z1' },
  { id: 'asus-rog-ally-x', name: 'ASUS ROG Ally X', manufacturer: 'ASUS', model: 'ROG Ally X' },

  // Lenovo Legion Go Series (Windows)
  { id: 'lenovo-legion-go', name: 'Lenovo Legion Go', manufacturer: 'Lenovo', model: 'Legion Go' },
  { id: 'lenovo-legion-go-s', name: 'Lenovo Legion Go S', manufacturer: 'Lenovo', model: 'Legion Go S' },

  // MSI Claw Series (Windows)
  { id: 'msi-claw-a1m', name: 'MSI Claw A1M', manufacturer: 'MSI', model: 'Claw A1M' },
  { id: 'msi-claw-7-ai-plus', name: 'MSI Claw 7 AI+', manufacturer: 'MSI', model: 'Claw 7 AI+' },
  { id: 'msi-claw-8-ai', name: 'MSI Claw 8 AI', manufacturer: 'MSI', model: 'Claw 8 AI' },
  { id: 'msi-claw-8-ai-plus', name: 'MSI Claw 8 AI+', manufacturer: 'MSI', model: 'Claw 8 AI+' },

  // GPD Win Series (Windows)
  { id: 'gpd-win', name: 'GPD Win', manufacturer: 'GPD', model: 'Win' },
  { id: 'gpd-win-2', name: 'GPD Win 2', manufacturer: 'GPD', model: 'Win 2' },
  { id: 'gpd-win-3', name: 'GPD Win 3', manufacturer: 'GPD', model: 'Win 3' },
  { id: 'gpd-win-4', name: 'GPD Win 4', manufacturer: 'GPD', model: 'Win 4' },
  { id: 'gpd-win-max', name: 'GPD Win Max', manufacturer: 'GPD', model: 'Win Max' },
  { id: 'gpd-win-max-2', name: 'GPD Win Max 2', manufacturer: 'GPD', model: 'Win Max 2' },
  { id: 'gpd-win-mini', name: 'GPD Win Mini', manufacturer: 'GPD', model: 'Win Mini' },

  // Ayaneo Series (Windows)
  { id: 'ayaneo-neo', name: 'AYA Neo', manufacturer: 'Ayaneo', model: 'Neo' },
  { id: 'ayaneo-pro', name: 'Ayaneo Pro', manufacturer: 'Ayaneo', model: 'Pro' },
  { id: 'ayaneo-next', name: 'Ayaneo Next', manufacturer: 'Ayaneo', model: 'Next' },
  { id: 'ayaneo-air', name: 'Ayaneo Air', manufacturer: 'Ayaneo', model: 'Air' },
  { id: 'ayaneo-air-pro', name: 'Ayaneo Air Pro', manufacturer: 'Ayaneo', model: 'Air Pro' },
  { id: 'ayaneo-2', name: 'Ayaneo 2', manufacturer: 'Ayaneo', model: '2' },
  { id: 'ayaneo-geek', name: 'Ayaneo Geek', manufacturer: 'Ayaneo', model: 'Geek' },
  { id: 'ayaneo-next-pro', name: 'Ayaneo Next Pro', manufacturer: 'Ayaneo', model: 'Next Pro' },
  { id: 'ayaneo-air-1s', name: 'Ayaneo Air 1S', manufacturer: 'Ayaneo', model: 'Air 1S' },
  { id: 'ayaneo-2s', name: 'Ayaneo 2S', manufacturer: 'Ayaneo', model: '2S' },
  { id: 'ayaneo-geek-1s', name: 'Ayaneo Geek 1S', manufacturer: 'Ayaneo', model: 'Geek 1S' },
  { id: 'ayaneo-flip-ds', name: 'Ayaneo Flip DS', manufacturer: 'Ayaneo', model: 'Flip DS' },
  { id: 'ayaneo-flip-kb', name: 'Ayaneo Flip KB', manufacturer: 'Ayaneo', model: 'Flip KB' },

  // OneXPlayer Series (Windows)
  { id: 'onexplayer', name: 'OneXPlayer', manufacturer: 'OneXPlayer', model: 'OneXPlayer' },
  { id: 'onexplayer-pro', name: 'OneXPlayer Pro', manufacturer: 'OneXPlayer', model: 'Pro' },
  { id: 'onexplayer-ultimate-edition', name: 'OneXPlayer Ultimate Edition', manufacturer: 'OneXPlayer', model: 'Ultimate Edition' },
  { id: 'onexplayer-1s', name: 'OneXPlayer 1S', manufacturer: 'OneXPlayer', model: '1S' },
  { id: 'onexplayer-x1-mini', name: 'OneXPlayer X1 Mini', manufacturer: 'OneXPlayer', model: 'X1 Mini' },
  { id: 'onexplayer-g1', name: 'OneXPlayer G1', manufacturer: 'OneXPlayer', model: 'G1' },
  { id: 'onexplayer-x1-pro', name: 'OneXPlayer X1 Pro', manufacturer: 'OneXPlayer', model: 'X1 Pro' },
  { id: 'onexplayer-2', name: 'OneXPlayer 2', manufacturer: 'OneXPlayer', model: '2' },
  { id: 'onexplayer-2-pro', name: 'OneXPlayer 2 Pro', manufacturer: 'OneXPlayer', model: '2 Pro' },

  // TECNO Pocket Go (Windows)
  { id: 'tecno-pocket-go', name: 'TECNO Pocket Go', manufacturer: 'TECNO', model: 'Pocket Go' },
];

// Handheld utility functions
export function searchHandhelds(query: string): HandheldSpec[] {
  if (!query || query.length < 2) return [];

  const normalizedQuery = query.toLowerCase();
  return HANDHELD_DATABASE.filter(handheld =>
    handheld.name.toLowerCase().includes(normalizedQuery) ||
    handheld.manufacturer.toLowerCase().includes(normalizedQuery) ||
    handheld.model.toLowerCase().includes(normalizedQuery)
  );
}

export function getHandheldById(id: string): HandheldSpec | undefined {
  return HANDHELD_DATABASE.find(handheld => handheld.id === id);
}

export function getTopHandhelds(limit: number = 10): HandheldSpec[] {
  return HANDHELD_DATABASE.slice(0, limit);
}

export function getHandheldsByManufacturer(manufacturer: HandheldSpec['manufacturer']): HandheldSpec[] {
  return HANDHELD_DATABASE.filter(handheld => handheld.manufacturer === manufacturer);
}