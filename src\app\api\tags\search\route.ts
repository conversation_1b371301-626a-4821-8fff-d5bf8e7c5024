// src/app/api/tags/search/route.ts
// Tag search and suggestions API

import { NextRequest, NextResponse } from 'next/server';
import { createServerTagService } from '@/lib/services/tagService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tagService = createServerTagService();

    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    if (query.length < 2) {
      return NextResponse.json(
        { error: 'Search query must be at least 2 characters' },
        { status: 400 }
      );
    }

    const result = await tagService.getTagSuggestions(query.trim(), limit);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      suggestions: result.suggestions,
      query: query.trim()
    });

  } catch (error) {
    console.error('Error in tag search API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}