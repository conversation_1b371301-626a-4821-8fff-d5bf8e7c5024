// Debug script to test MFA functionality
// Run this in the browser console while on the admin page

console.log('🔍 MFA Debug Script Starting...');

// 1. Check if MFA components are loaded
console.log('1. Checking MFA components...');
console.log('MFAChallenge component:', typeof window.MFAChallenge);
console.log('useMFAGuard hook:', typeof window.useMFAGuard);

// 2. Test the admin verify API
console.log('2. Testing admin verify API...');
fetch('/api/admin/verify', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
})
.then(response => response.json())
.then(data => {
  console.log('✅ Admin verify response:', data);
  
  if (data.mfaRequired && !data.mfaVerified) {
    console.log('🔐 MFA should be required but not verified - modal should appear');
  } else if (data.mfaRequired && data.mfaVerified) {
    console.log('✅ MFA is required and verified - no modal needed');
  } else {
    console.log('ℹ️ MFA not required for this user');
  }
})
.catch(error => {
  console.error('❌ Admin verify API error:', error);
});

// 3. Check for JavaScript errors
console.log('3. Checking for JavaScript errors...');
window.addEventListener('error', (event) => {
  console.error('🚨 JavaScript Error:', event.error);
});

// 4. Check if user is authenticated
console.log('4. Checking authentication state...');
console.log('Current URL:', window.location.href);
console.log('User agent:', navigator.userAgent);

// 5. Check local storage for any MFA-related data
console.log('5. Checking local storage...');
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && (key.includes('mfa') || key.includes('auth') || key.includes('admin'))) {
    console.log(`LocalStorage ${key}:`, localStorage.getItem(key));
  }
}

// 6. Force MFA modal test (if needed)
console.log('6. To force MFA modal, run: forceMFAModal()');
window.forceMFAModal = function() {
  console.log('🔧 Attempting to force MFA modal...');
  
  // Try to find and trigger MFA challenge
  const event = new CustomEvent('forceMFA', { detail: { required: true, verified: false } });
  document.dispatchEvent(event);
  
  // Alternative: try to modify React state (if accessible)
  if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
    console.log('React internals available - attempting state modification');
  }
};

console.log('🔍 MFA Debug Script Complete. Check the logs above for issues.');
