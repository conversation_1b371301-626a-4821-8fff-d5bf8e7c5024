'use client';

/**
 * Admin Ad Management Page
 * Provides comprehensive ad and affiliate management interface for administrators
 * Created: 20/01/2025 - Admin System Completion
 */

import React, { useState, useEffect } from 'react';
import { useAuthContext } from '@/hooks/use-auth-context';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from '@/hooks/use-toast';
import {
  getAdConfigurations,
  getAdPerformanceMetrics,
  getAffiliateLinks,
  saveAdConfiguration,
  deleteAdConfiguration,
  type AdConfiguration,
  type AdPerformanceMetrics,
  type AffiliateLink
} from '@/lib/admin/adService';
import { AdActionsDropdown } from '@/components/admin/AdActionsDropdown';
import {
  DollarSign,
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  TrendingUp,
  MousePointer,
  Monitor,
  Smartphone,
  Tablet,
  ExternalLink,
  AlertTriangle,
  BarChart3,
  Target,
  Link as LinkIcon
} from 'lucide-react';

export default function AdManagementPage() {
  const { user, loading: authLoading } = useAuthContext();
  const { toast } = useToast();

  // State management
  const [ads, setAds] = useState<AdConfiguration[]>([]);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<AdPerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // Filter state
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Dialog state
  const [isAdDialogOpen, setIsAdDialogOpen] = useState(false);
  const [editingAd, setEditingAd] = useState<AdConfiguration | null>(null);

  // Form state
  const [adForm, setAdForm] = useState({
    name: '',
    type: 'banner' as const,
    position: 'header' as const,
    content: '',
    image_url: '',
    link_url: '',
    affiliate_code: '',
    is_active: true,
    priority: 5,
    target_audience: 'all' as const,
    device_targeting: 'all' as const,
    start_date: '',
    end_date: ''
  });

  // Load data
  const loadData = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const [adsData, metricsData, affiliateData] = await Promise.all([
        getAdConfigurations(user.id),
        getAdPerformanceMetrics(user.id),
        getAffiliateLinks(user.id)
      ]);

      setAds(adsData);
      setPerformanceMetrics(metricsData);
      setAffiliateLinks(affiliateData);
    } catch (error) {
      console.error('Error loading ad management data:', error);
      toast({
        title: "Error loading data",
        description: "Failed to load ad management data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (user?.id) {
      loadData();
    }
  }, [user?.id]);

  // Handle ad form submission
  const handleSaveAd = async () => {
    if (!user?.id) return;

    try {
      setIsProcessing(true);
      const result = await saveAdConfiguration(user.id, {
        ...adForm,
        id: editingAd?.id
      });

      if (result.success) {
        toast({
          title: "Ad configuration saved",
          description: `Ad "${adForm.name}" has been ${editingAd ? 'updated' : 'created'} successfully`,
        });
        setIsAdDialogOpen(false);
        setEditingAd(null);
        resetAdForm();
        await loadData();
      } else {
        toast({
          title: "Save failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Save error",
        description: "Failed to save ad configuration",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle ad deletion
  const handleDeleteAd = async (adId: string) => {
    if (!user?.id) return;

    try {
      setIsProcessing(true);
      const result = await deleteAdConfiguration(user.id, adId);

      if (result.success) {
        toast({
          title: "Ad deleted",
          description: "Ad configuration has been deleted successfully",
        });
        await loadData();
      } else {
        toast({
          title: "Delete failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Delete error",
        description: "Failed to delete ad configuration",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Reset ad form
  const resetAdForm = () => {
    setAdForm({
      name: '',
      type: 'banner',
      position: 'header',
      content: '',
      image_url: '',
      link_url: '',
      affiliate_code: '',
      is_active: true,
      priority: 5,
      target_audience: 'all',
      device_targeting: 'all',
      start_date: '',
      end_date: ''
    });
  };

  // Open edit dialog
  const openEditDialog = (ad: AdConfiguration) => {
    setEditingAd(ad);
    setAdForm({
      name: ad.name,
      type: ad.type,
      position: ad.position,
      content: ad.content,
      image_url: ad.image_url || '',
      link_url: ad.link_url || '',
      affiliate_code: ad.affiliate_code || '',
      is_active: ad.is_active,
      priority: ad.priority,
      target_audience: ad.target_audience || 'all',
      device_targeting: ad.device_targeting || 'all',
      start_date: ad.start_date || '',
      end_date: ad.end_date || ''
    });
    setIsAdDialogOpen(true);
  };

  // Open create dialog
  const openCreateDialog = () => {
    setEditingAd(null);
    resetAdForm();
    setIsAdDialogOpen(true);
  };

  // Filter ads
  const filteredAds = ads.filter(ad => {
    if (typeFilter !== 'all' && ad.type !== typeFilter) return false;
    if (statusFilter === 'active' && !ad.is_active) return false;
    if (statusFilter === 'inactive' && ad.is_active) return false;
    return true;
  });

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  // Get device icon
  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'desktop': return <Monitor className="h-4 w-4" />;
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Tablet className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  if (authLoading) {
    return (
      <AdminLayout title="Ad Management" description="Loading ad management interface...">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading ad management...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!user?.isAdmin) {
    return (
      <AdminLayout title="Access Denied" description="Admin access required">
        <Card className="max-w-md mx-auto mt-8 shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-mono text-xl tracking-tight">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <span className="text-red-400">&lt;</span>
              <span className="text-red-300 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Access Denied</span>
              <span className="text-red-400">/&gt;</span>
            </CardTitle>
            <CardDescription className="font-mono text-sm">
              You need administrator privileges to access ad management.
            </CardDescription>
          </CardHeader>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Ad Management"
      description="Manage advertisements and affiliate links"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Ad Management' }
      ]}
    >
      <div className="space-y-6">
        {/* Performance Overview */}
        {performanceMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground font-mono">Total Revenue</p>
                    <p className="text-2xl font-bold font-mono text-green-400">{formatCurrency(performanceMetrics.totalRevenue)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-400" />
                </div>
              </CardContent>
            </Card>
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground font-mono">Total Clicks</p>
                    <p className="text-2xl font-bold font-mono text-blue-400">{performanceMetrics.totalClicks.toLocaleString()}</p>
                  </div>
                  <MousePointer className="h-8 w-8 text-blue-400" />
                </div>
              </CardContent>
            </Card>
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground font-mono">Impressions</p>
                    <p className="text-2xl font-bold font-mono text-purple-400">{performanceMetrics.totalImpressions.toLocaleString()}</p>
                  </div>
                  <Eye className="h-8 w-8 text-purple-400" />
                </div>
              </CardContent>
            </Card>
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground font-mono">Average CTR</p>
                    <p className="text-2xl font-bold font-mono text-orange-400">{formatPercentage(performanceMetrics.averageCTR)}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-400" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="ads" className="space-y-6">
          <TabsList className="bg-slate-800/60 border-slate-700/50">
            <TabsTrigger value="ads" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Ad Configurations</TabsTrigger>
            <TabsTrigger value="performance" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Performance</TabsTrigger>
            <TabsTrigger value="affiliates" className="font-mono text-sm data-[state=active]:bg-violet-600/80 data-[state=active]:text-white">Affiliate Links</TabsTrigger>
          </TabsList>

          <TabsContent value="ads" className="space-y-6">
            {/* Filters and Actions */}
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="font-mono text-xl tracking-tight">
                      <span className="text-violet-400">&lt;</span>
                      <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Ad Configurations</span>
                      <span className="text-violet-400">/&gt;</span>
                    </CardTitle>
                    <CardDescription className="font-mono text-sm">Manage your advertisement placements and settings</CardDescription>
                  </div>
                  <Button onClick={openCreateDialog} className="bg-violet-600/80 hover:bg-violet-600 transition-colors font-mono">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Ad
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 mb-4">
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-48 bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                      <SelectItem value="all" className="font-mono text-sm">All Types</SelectItem>
                      <SelectItem value="banner" className="font-mono text-sm">Banner</SelectItem>
                      <SelectItem value="sidebar" className="font-mono text-sm">Sidebar</SelectItem>
                      <SelectItem value="inline" className="font-mono text-sm">Inline</SelectItem>
                      <SelectItem value="popup" className="font-mono text-sm">Popup</SelectItem>
                      <SelectItem value="affiliate" className="font-mono text-sm">Affiliate</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48 bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                      <SelectItem value="all" className="font-mono text-sm">All Status</SelectItem>
                      <SelectItem value="active" className="font-mono text-sm">Active</SelectItem>
                      <SelectItem value="inactive" className="font-mono text-sm">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Ads List */}
                <div className="space-y-4">
                  {filteredAds.length === 0 ? (
                    <div className="text-center py-8">
                      <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2 font-mono">No ads found</h3>
                      <p className="text-muted-foreground mb-4 font-mono text-sm">
                        Create your first ad configuration to get started.
                      </p>
                      <Button onClick={openCreateDialog} className="bg-violet-600/80 hover:bg-violet-600 transition-colors font-mono">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Ad
                      </Button>
                    </div>
                  ) : (
                    filteredAds.map((ad) => (
                      <div
                        key={ad.id}
                        className="border-slate-700/30 border rounded-lg p-4 hover:bg-slate-800/20 transition-colors bg-slate-800/10"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium font-mono text-violet-300">{ad.name}</h3>
                              <Badge variant={ad.is_active ? "default" : "secondary"} className="font-mono text-xs">
                                {ad.is_active ? 'Active' : 'Inactive'}
                              </Badge>
                              <Badge variant="outline" className="font-mono text-xs">{ad.type}</Badge>
                              <Badge variant="outline" className="font-mono text-xs">{ad.position}</Badge>
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1 font-mono text-xs">
                                {getDeviceIcon(ad.device_targeting || 'all')}
                                {ad.device_targeting || 'All devices'}
                              </div>
                              <div className="flex items-center gap-1 font-mono text-xs">
                                <Target className="h-4 w-4" />
                                {ad.target_audience || 'All users'}
                              </div>
                              <div className="flex items-center gap-1 font-mono text-xs">
                                <BarChart3 className="h-4 w-4" />
                                Priority: {ad.priority}
                              </div>
                            </div>

                            <div className="flex items-center gap-6 text-sm">
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Clicks:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-violet-400">{ad.click_count.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Impressions:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-violet-400">{ad.impression_count.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Revenue:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-green-400">{formatCurrency(ad.revenue)}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">CTR:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-orange-400">
                                  {formatPercentage((ad.click_count / ad.impression_count) * 100)}
                                </span>
                              </div>
                            </div>

                            {ad.link_url && (
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <ExternalLink className="h-3 w-3" />
                                <a href={ad.link_url} target="_blank" rel="noopener noreferrer" className="hover:underline font-mono text-xs hover:text-violet-400 transition-colors">
                                  {ad.link_url}
                                </a>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            <AdActionsDropdown
                              ad={ad}
                              onEdit={openEditDialog}
                              onDelete={handleDeleteAd}
                              disabled={isProcessing}
                              compact={true}
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            {performanceMetrics && (
              <>
                {/* Revenue by Type */}
                <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="font-mono text-lg tracking-tight">
                      <span className="text-violet-400">&lt;</span>
                      <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Revenue by Ad Type</span>
                      <span className="text-violet-400">/&gt;</span>
                    </CardTitle>
                    <CardDescription className="font-mono text-sm">Revenue breakdown by advertisement type</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {performanceMetrics.revenueByType.map((item) => (
                        <div key={item.type} className="flex items-center justify-between p-2 bg-slate-800/20 rounded border border-slate-700/30">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="capitalize font-mono text-xs">{item.type}</Badge>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className="text-sm text-muted-foreground font-mono">{formatPercentage(item.percentage)}</span>
                            <span className="font-medium font-mono text-green-400">{formatCurrency(item.revenue)}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Top Performing Ads */}
                <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="font-mono text-lg tracking-tight">
                      <span className="text-violet-400">&lt;</span>
                      <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Top Performing Ads</span>
                      <span className="text-violet-400">/&gt;</span>
                    </CardTitle>
                    <CardDescription className="font-mono text-sm">Best performing advertisements by revenue</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {performanceMetrics.topPerformingAds.map((ad, index) => (
                        <div key={ad.id} className="flex items-center justify-between p-3 border-slate-700/30 border rounded-lg bg-slate-800/20 hover:bg-slate-700/30 transition-colors">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full bg-violet-600/80 text-white flex items-center justify-center text-sm font-medium font-mono">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium font-mono text-sm text-violet-300">{ad.name}</p>
                              <p className="text-xs text-muted-foreground font-mono">
                                CTR: {formatPercentage(ad.ctr)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium font-mono text-sm text-green-400">{formatCurrency(ad.revenue)}</p>
                            <p className="text-xs text-muted-foreground font-mono">
                              {ad.clicks.toLocaleString()} clicks
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          <TabsContent value="affiliates" className="space-y-6">
            <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="font-mono text-lg tracking-tight">
                  <span className="text-violet-400">&lt;</span>
                  <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Affiliate Links</span>
                  <span className="text-violet-400">/&gt;</span>
                </CardTitle>
                <CardDescription className="font-mono text-sm">Manage your affiliate partnerships and tracking links</CardDescription>
              </CardHeader>
              <CardContent>
                {affiliateLinks.length === 0 ? (
                  <div className="text-center py-8">
                    <LinkIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2 font-mono">No affiliate links</h3>
                    <p className="text-muted-foreground font-mono text-sm">
                      Set up affiliate partnerships to start earning commissions.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {affiliateLinks.map((link) => (
                      <div
                        key={link.id}
                        className="border-slate-700/30 border rounded-lg p-4 hover:bg-slate-800/20 transition-colors bg-slate-800/10"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium font-mono text-sm text-violet-300">{link.name}</h3>
                              <Badge variant={link.is_active ? "default" : "secondary"} className="font-mono text-xs">
                                {link.is_active ? 'Active' : 'Inactive'}
                              </Badge>
                              <Badge variant="outline" className="font-mono text-xs">{link.category}</Badge>
                            </div>

                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <ExternalLink className="h-3 w-3" />
                              <span className="font-mono text-xs">Partner: {link.partner}</span>
                            </div>

                            <div className="flex items-center gap-6 text-sm">
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Clicks:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-violet-400">{link.click_count.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Conversions:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-blue-400">{link.conversion_count.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Commission:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-orange-400">{formatPercentage(link.commission_rate)}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground font-mono text-xs">Revenue:</span>
                                <span className="ml-1 font-medium font-mono text-xs text-green-400">{formatCurrency(link.revenue)}</span>
                              </div>
                            </div>

                            <div className="text-xs text-muted-foreground font-mono bg-slate-800/30 p-2 rounded border border-slate-700/30">
                              {link.affiliate_code}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Ad Creation/Edit Dialog */}
        <Dialog open={isAdDialogOpen} onOpenChange={setIsAdDialogOpen}>
          <DialogContent className="max-w-2xl bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
            <DialogHeader>
              <DialogTitle className="font-mono text-xl tracking-tight">
                <span className="text-violet-400">&lt;</span>
                <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">{editingAd ? 'Edit Ad Configuration' : 'Create Ad Configuration'}</span>
                <span className="text-violet-400">/&gt;</span>
              </DialogTitle>
              <DialogDescription className="font-mono text-sm">
                {editingAd ? 'Update the ad configuration settings.' : 'Create a new advertisement configuration.'}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block font-mono text-violet-300">Name</label>
                  <Input
                    value={adForm.name}
                    onChange={(e) => setAdForm({ ...adForm, name: e.target.value })}
                    placeholder="Ad configuration name"
                    className="bg-slate-800/60 border-slate-600/50 focus:border-violet-500/50 transition-colors font-mono text-sm"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block font-mono text-violet-300">Type</label>
                  <Select value={adForm.type} onValueChange={(value: any) => setAdForm({ ...adForm, type: value })}>
                    <SelectTrigger className="bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                      <SelectItem value="banner" className="font-mono text-sm">Banner</SelectItem>
                      <SelectItem value="sidebar" className="font-mono text-sm">Sidebar</SelectItem>
                      <SelectItem value="inline" className="font-mono text-sm">Inline</SelectItem>
                      <SelectItem value="popup" className="font-mono text-sm">Popup</SelectItem>
                      <SelectItem value="affiliate" className="font-mono text-sm">Affiliate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Position</label>
                  <Select value={adForm.position} onValueChange={(value: any) => setAdForm({ ...adForm, position: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="header">Header</SelectItem>
                      <SelectItem value="footer">Footer</SelectItem>
                      <SelectItem value="sidebar-left">Sidebar Left</SelectItem>
                      <SelectItem value="sidebar-right">Sidebar Right</SelectItem>
                      <SelectItem value="content-top">Content Top</SelectItem>
                      <SelectItem value="content-bottom">Content Bottom</SelectItem>
                      <SelectItem value="between-reviews">Between Reviews</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Priority</label>
                  <Input
                    type="number"
                    min="1"
                    max="10"
                    value={adForm.priority}
                    onChange={(e) => setAdForm({ ...adForm, priority: parseInt(e.target.value) || 5 })}
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Content (HTML)</label>
                <Textarea
                  value={adForm.content}
                  onChange={(e) => setAdForm({ ...adForm, content: e.target.value })}
                  placeholder="HTML content for the advertisement"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Image URL</label>
                  <Input
                    value={adForm.image_url}
                    onChange={(e) => setAdForm({ ...adForm, image_url: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Link URL</label>
                  <Input
                    value={adForm.link_url}
                    onChange={(e) => setAdForm({ ...adForm, link_url: e.target.value })}
                    placeholder="https://example.com"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Target Audience</label>
                  <Select value={adForm.target_audience} onValueChange={(value: any) => setAdForm({ ...adForm, target_audience: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="registered">Registered Users</SelectItem>
                      <SelectItem value="anonymous">Anonymous Users</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Device Targeting</label>
                  <Select value={adForm.device_targeting} onValueChange={(value: any) => setAdForm({ ...adForm, device_targeting: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Devices</SelectItem>
                      <SelectItem value="desktop">Desktop</SelectItem>
                      <SelectItem value="mobile">Mobile</SelectItem>
                      <SelectItem value="tablet">Tablet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Affiliate Code</label>
                  <Input
                    value={adForm.affiliate_code}
                    onChange={(e) => setAdForm({ ...adForm, affiliate_code: e.target.value })}
                    placeholder="AFFILIATE123"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={adForm.is_active}
                  onCheckedChange={(checked) => setAdForm({ ...adForm, is_active: checked })}
                />
                <label className="text-sm font-medium">Active</label>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAdDialogOpen(false)} className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
                Cancel
              </Button>
              <Button onClick={handleSaveAd} disabled={isProcessing} className="bg-violet-600/80 hover:bg-violet-600 transition-colors font-mono">
                {isProcessing ? 'Saving...' : (editingAd ? 'Update Ad' : 'Create Ad')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
}
