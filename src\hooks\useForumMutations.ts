import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { useAuthContext } from '@/contexts/auth-context';
import { ForumPost, ForumReply, ForumThread } from '@/types/forum';

// Custom error class for blocked users
export class UserBlockedError extends Error {
  public contentOwnerName: string;
  
  constructor(contentOwnerName: string) {
    super('User is blocked from commenting on this content');
    this.name = 'UserBlockedError';
    this.contentOwnerName = contentOwnerName;
  }
}

interface CreatePostData {
  title: string;
  content: string;
  category?: string | null;
}

interface CreateReplyData {
  postId: string;
  content: string;
}

interface VoteData {
  postId?: string;
  replyId?: string;
  voteType: 'upvote' | 'downvote' | null;
}

export function useForumMutations(reviewId: string) {
  const { user, supabaseUser } = useAuthContext();
  const queryClient = useQueryClient();

  const createPost = useMutation({
    mutationFn: async (data: CreatePostData) => {
      if (!user) throw new Error('User not authenticated');

      const supabase = createClient();
      
      // Check if user is blocked from commenting on this review
      const { data: reviewData, error: reviewError } = await supabase
        .from('reviews')
        .select(`
          author_id,
          author:profiles!author_id(username, display_name)
        `)
        .eq('id', reviewId)
        .single();
        
      if (reviewError) throw new Error('Review not found');
      
      // Check if current user is blocked by the review author
      const { data: blockCheck, error: blockError } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewData.author_id)
        .eq('blocked_id', user.id)
        .maybeSingle();
        
      if (blockError) throw blockError;
      
      if (blockCheck) {
        const ownerName = reviewData.author?.display_name || reviewData.author?.username || 'this user';
        throw new UserBlockedError(ownerName);
      }

      const { data: post, error } = await supabase
        .from('comments')
        .insert({
          review_id: reviewId,
          author_id: user.id,
          author_name: user.displayName || user.userName || 'Anonymous',
          title: data.title, // Add the title field
          content: data.content,
          parent_id: null, // Top-level comment
          upvotes: 0,
          downvotes: 0,
          is_pinned: false,
          is_deleted: false,
          is_approved: true // Auto-approve for now
        })
        .select()
        .single();

      if (error) throw error;
      return post;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['forum-posts', reviewId] });
      queryClient.invalidateQueries({ queryKey: ['forum-stats', reviewId] });
    },
  });

  const createReply = useMutation({
    mutationFn: async (data: CreateReplyData) => {
      if (!user) throw new Error('User not authenticated');

      const supabase = createClient();
      
      // Check if user is blocked from commenting on this review
      const { data: reviewData, error: reviewError } = await supabase
        .from('reviews')
        .select(`
          author_id,
          author:profiles!author_id(username, display_name)
        `)
        .eq('id', reviewId)
        .single();
        
      if (reviewError) throw new Error('Review not found');
      
      // Check if current user is blocked by the review author
      const { data: blockCheck, error: blockError } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewData.author_id)
        .eq('blocked_id', user.id)
        .maybeSingle();
        
      if (blockError) throw blockError;
      
      if (blockCheck) {
        const ownerName = reviewData.author?.display_name || reviewData.author?.username || 'this user';
        throw new UserBlockedError(ownerName);
      }

      const { data: reply, error } = await supabase
        .from('comments')
        .insert({
          review_id: reviewId,
          parent_id: data.postId, // This makes it a reply
          author_id: user.id,
          author_name: user.displayName || user.userName || 'Anonymous',
          content: data.content,
          upvotes: 0,
          downvotes: 0,
          is_deleted: false,
          is_approved: true // Auto-approve for now
        })
        .select()
        .single();

      if (error) throw error;

      return reply;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['forum-thread', variables.postId] });
      queryClient.invalidateQueries({ queryKey: ['forum-posts', reviewId] });
    },
  });

  const voteOnPost = useMutation({
    mutationFn: async (data: VoteData) => {
      if (!user || !data.postId) throw new Error('Invalid vote data');

      const supabase = createClient();
      if (data.voteType === null) {
        // Remove vote
        await supabase
          .from('comment_votes')
          .delete()
          .eq('comment_id', data.postId)
          .eq('user_id', user.id);
      } else {
        // Upsert vote
        await supabase
          .from('comment_votes')
          .upsert({
            comment_id: data.postId,
            user_id: user.id,
            vote_type: data.voteType
          });
      }

      // Update vote counts manually since we don't have RPC functions yet
      const { data: votes } = await supabase
        .from('comment_votes')
        .select('vote_type')
        .eq('comment_id', data.postId);

      const upvotes = votes?.filter(v => v.vote_type === 'upvote').length || 0;
      const downvotes = votes?.filter(v => v.vote_type === 'downvote').length || 0;

      await supabase
        .from('comments')
        .update({ upvotes, downvotes })
        .eq('id', data.postId);

      return { voteType: data.voteType, upvotes, downvotes };
    },
    onMutate: async (variables) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['forum-thread', variables.postId] });

      // Snapshot the previous value
      const previousThread = queryClient.getQueryData(['forum-thread', variables.postId]);

      // Optimistically update to the new value
      queryClient.setQueryData(['forum-thread', variables.postId], (old: any) => {
        if (!old) return old;

        const currentVote = old.post.user_vote;
        const newVote = variables.voteType;
        let newUpvotes = old.post.upvotes;
        let newDownvotes = old.post.downvotes;

        // Remove previous vote effect
        if (currentVote === 'upvote') newUpvotes--;
        if (currentVote === 'downvote') newDownvotes--;

        // Add new vote effect
        if (newVote === 'upvote') newUpvotes++;
        if (newVote === 'downvote') newDownvotes++;

        return {
          ...old,
          post: {
            ...old.post,
            user_vote: newVote,
            upvotes: Math.max(0, newUpvotes),
            downvotes: Math.max(0, newDownvotes)
          }
        };
      });

      return { previousThread };
    },
    onError: (err, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousThread) {
        queryClient.setQueryData(['forum-thread', variables.postId], context.previousThread);
      }
    },
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ queryKey: ['forum-thread', variables.postId] });
      queryClient.invalidateQueries({ queryKey: ['forum-posts', reviewId] });
    },
  });

  const voteOnReply = useMutation({
    mutationFn: async (data: VoteData) => {
      if (!user || !data.replyId) throw new Error('Invalid vote data');

      const supabase = createClient();
      if (data.voteType === null) {
        // Remove vote
        await supabase
          .from('comment_votes')
          .delete()
          .eq('comment_id', data.replyId)
          .eq('user_id', user.id);
      } else {
        // Upsert vote
        await supabase
          .from('comment_votes')
          .upsert({
            comment_id: data.replyId,
            user_id: user.id,
            vote_type: data.voteType
          });
      }

      // Update vote counts manually since we don't have RPC functions yet
      const { data: votes } = await supabase
        .from('comment_votes')
        .select('vote_type')
        .eq('comment_id', data.replyId);

      const upvotes = votes?.filter(v => v.vote_type === 'upvote').length || 0;
      const downvotes = votes?.filter(v => v.vote_type === 'downvote').length || 0;

      await supabase
        .from('comments')
        .update({ upvotes, downvotes })
        .eq('id', data.replyId);
    },
    onSuccess: (_, variables) => {
      if (variables.replyId) {
        queryClient.invalidateQueries({ queryKey: ['forum-thread'] });
      }
    },
  });

  return {
    createPost,
    createReply,
    voteOnPost,
    voteOnReply,
  };
}

export function useForumThread(postId: string) {
  const { supabaseUser } = useAuthContext();
  
  return useQuery({
    queryKey: ['forum-thread', postId, supabaseUser?.id],
    queryFn: async (): Promise<ForumThread> => {
      const supabase = createClient();
      // Get the main post (comment)
      const { data: post, error: postError } = await supabase
        .from('comments')
        .select(`
          *,
          author:profiles (
            id,
            username,
            display_name,
            avatar_url
          )
        `)
        .eq('id', postId)
        .single();

      if (postError) throw postError;

      // Get replies (comments with parent_id = postId, including deleted ones)
      const { data: replies, error: repliesError } = await supabase
        .from('comments')
        .select(`
          *,
          author:profiles (
            id,
            username,
            display_name,
            avatar_url
          )
        `)
        .eq('parent_id', postId)
        .order('created_at', { ascending: true });

      if (repliesError) throw repliesError;

      // Filter out replies from users blocked by the current user (if logged in)
      let filteredReplies = replies || [];
      if (supabaseUser?.id) {
        const { data: blockedUsers, error: blockedError } = await supabase
          .from('user_blocks')
          .select('blocked_id')
          .eq('blocker_id', supabaseUser.id);
          
        if (blockedError) {
          console.error('Error fetching blocked users:', blockedError);
        } else if (blockedUsers && blockedUsers.length > 0) {
          const blockedUserIds = blockedUsers.map(b => b.blocked_id);
          filteredReplies = replies?.filter(reply => !blockedUserIds.includes(reply.author_id)) || [];
        }
      }

      // Get current user's votes for the post and all replies if logged in
      let postUserVote = null;
      const replyUserVotes: Record<string, string | null> = {};

      if (supabaseUser?.id) {
        // Get user vote for the main post
        const { data: postVoteData, error: postVoteError } = await supabase
          .from('comment_votes')
          .select('vote_type')
          .eq('comment_id', postId)
          .eq('user_id', supabaseUser.id)
          .maybeSingle();

        // Only log error if it's not a "no rows" error
        if (postVoteError && postVoteError.code !== 'PGRST116') {
          console.error('Error fetching post vote:', postVoteError);
        }

        postUserVote = postVoteData?.vote_type || null;

        // Get user votes for all replies
        if (filteredReplies && filteredReplies.length > 0) {
          const replyIds = filteredReplies.map(r => r.id);
          const { data: replyVotesData, error: replyVotesError } = await supabase
            .from('comment_votes')
            .select('comment_id, vote_type')
            .in('comment_id', replyIds)
            .eq('user_id', supabaseUser.id);

          if (replyVotesError) {
            console.error('Error fetching reply votes:', replyVotesError);
          }

          replyVotesData?.forEach(vote => {
            replyUserVotes[vote.comment_id] = vote.vote_type;
          });
        }
      }

      return {
        post: {
          id: post.id,
          review_id: post.review_id,
          author_id: post.author_id,
          title: post.title || `Comment by ${post.author_name}`, // Use actual title or fallback
          content: post.content,
          category: null,
          upvotes: post.upvotes || 0,
          downvotes: post.downvotes || 0,
          reply_count: filteredReplies?.length || 0,
          is_pinned: post.is_pinned || false,
          is_hot: false,
          is_deleted: post.is_deleted || false,
          created_at: post.created_at,
          updated_at: post.updated_at,
          author: post.author,
          user_vote: postUserVote
        },
        replies: filteredReplies?.map(reply => ({
          id: reply.id,
          post_id: postId,
          author_id: reply.author_id,
          content: reply.content,
          upvotes: reply.upvotes || 0,
          downvotes: reply.downvotes || 0,
          is_deleted: reply.is_deleted || false,
          created_at: reply.created_at,
          updated_at: reply.updated_at,
          author: reply.author,
          user_vote: replyUserVotes[reply.id] || null
        })) || []
      };
    },
    enabled: !!postId,
  });
}
