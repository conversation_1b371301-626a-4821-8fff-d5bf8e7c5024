// src/app/admin/tags/TagsAdminClient.tsx
// Admin client for tag management and analytics

'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Hash,
  TrendingUp,
  Star,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  Search,
  Filter,
  Eye,
  Users,
  MessageSquare,
  Calendar,
  ArrowUp,
  ArrowDown,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import type { Tag, TrendingTag } from '@/lib/services/tagService';

interface TagsAdminClientProps {
  trendingTags: TrendingTag[];
  popularTags: TrendingTag[];
  featuredTags: Tag[];
  allTags: Tag[];
  totalTags: number;
}

interface TagFormData {
  name: string;
  description: string;
  category: string;
  is_featured: boolean;
}

export default function TagsAdminClient({
  trendingTags,
  popularTags,
  featuredTags,
  allTags: initialTags,
  totalTags: initialTotal
}: TagsAdminClientProps) {
  const [allTags, setAllTags] = useState<Tag[]>(initialTags);
  const [totalTags, setTotalTags] = useState(initialTotal);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [formData, setFormData] = useState<TagFormData>({
    name: '',
    description: '',
    category: 'custom',
    is_featured: false
  });

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'genre', label: 'Genre' },
    { value: 'platform', label: 'Platform' },
    { value: 'feature', label: 'Feature' },
    { value: 'mood', label: 'Mood' },
    { value: 'difficulty', label: 'Difficulty' },
    { value: 'length', label: 'Length' },
    { value: 'style', label: 'Style' },
    { value: 'custom', label: 'Custom' }
  ];

  const filteredTags = allTags.filter(tag => {
    const matchesSearch = tag.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tag.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCreateTag = async () => {
    if (!formData.name.trim()) {
      toast({ title: 'Error', description: 'Tag name is required', variant: 'destructive' });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast({ title: 'Success', description: 'Tag created successfully' });
        setAllTags(prev => [data.tag, ...prev]);
        setTotalTags(prev => prev + 1);
        setIsCreateModalOpen(false);
        setFormData({ name: '', description: '', category: 'custom', is_featured: false });
      } else {
        toast({ title: 'Error', description: data.error, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to create tag', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditTag = async () => {
    if (!editingTag) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/tags/${editingTag.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast({ title: 'Success', description: 'Tag updated successfully' });
        setAllTags(prev => prev.map(tag => tag.id === editingTag.id ? data.tag : tag));
        setIsEditModalOpen(false);
        setEditingTag(null);
      } else {
        toast({ title: 'Error', description: data.error, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to update tag', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteTag = async (tag: Tag) => {
    if (!confirm(`Are you sure you want to delete the tag "${tag.name}"?`)) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/tags/${tag.id}`, { method: 'DELETE' });
      const data = await response.json();

      if (data.success) {
        toast({ title: 'Success', description: 'Tag deleted successfully' });
        setAllTags(prev => prev.filter(t => t.id !== tag.id));
        setTotalTags(prev => prev - 1);
      } else {
        toast({ title: 'Error', description: data.error, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to delete tag', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecalculateTrends = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/tags/analytics', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'recalculate_trends' })
      });

      const data = await response.json();

      if (data.success) {
        toast({ title: 'Success', description: 'Trend scores recalculated successfully' });
        // Refresh the page data
        window.location.reload();
      } else {
        toast({ title: 'Error', description: data.error, variant: 'destructive' });
      }
    } catch (error) {
      toast({ title: 'Error', description: 'Failed to recalculate trends', variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const openEditModal = (tag: Tag) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
      description: tag.description || '',
      category: tag.category || 'custom',
      is_featured: tag.is_featured
    });
    setIsEditModalOpen(true);
  };

  const getCategoryIcon = (category: string | null) => {
    switch (category) {
      case 'genre': return '🎮';
      case 'platform': return '💻';
      case 'feature': return '⭐';
      case 'mood': return '🎭';
      case 'difficulty': return '🎯';
      case 'length': return '⏱️';
      case 'style': return '🎨';
      default: return '🏷️';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Tag Management</h1>
          <p className="text-slate-400 mt-2">Manage tags, view analytics, and monitor performance</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRecalculateTrends}
            disabled={isLoading}
            variant="outline"
          >
            <RefreshCw size={16} className="mr-2" />
            Recalculate Trends
          </Button>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus size={16} className="mr-2" />
                Create Tag
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Tag</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Tag name"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Optional description"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.filter(cat => cat.value !== 'all').map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.is_featured}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
                  />
                  <Label htmlFor="featured">Featured tag</Label>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTag} disabled={isLoading}>
                    Create Tag
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center">
              <Hash size={16} className="mr-2" />
              Total Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{totalTags}</div>
            <p className="text-xs text-slate-500 mt-1">All time</p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center">
              <TrendingUp size={16} className="mr-2" />
              Trending Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-400">{trendingTags.length}</div>
            <p className="text-xs text-slate-500 mt-1">Currently trending</p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center">
              <Star size={16} className="mr-2" />
              Featured Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-400">{featuredTags.length}</div>
            <p className="text-xs text-slate-500 mt-1">Editor's choice</p>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-400 flex items-center">
              <Activity size={16} className="mr-2" />
              Active Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">
              {allTags.filter(tag => tag.status === 'active').length}
            </div>
            <p className="text-xs text-slate-500 mt-1">Ready to use</p>
          </CardContent>
        </Card>
      </div>

      {/* Trending Tags */}
      {trendingTags.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp size={20} className="mr-2 text-orange-400" />
              Currently Trending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {trendingTags.slice(0, 6).map((tag) => (
                <div key={tag.id} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                  <div className="text-lg">{getCategoryIcon(tag.category)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-white truncate">{tag.name}</div>
                    <div className="text-sm text-slate-400">
                      {tag.usage_count} uses • Score: {tag.trend_score.toFixed(1)}
                    </div>
                  </div>
                  {tag.is_trending && (
                    <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/50">
                      HOT
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tag Management */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Hash size={20} className="mr-2" />
            All Tags
          </CardTitle>
          <div className="flex items-center gap-4 mt-4">
            <div className="relative flex-1 max-w-sm">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <Input
                placeholder="Search tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-slate-700/50 border-slate-600"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 bg-slate-700/50 border-slate-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredTags.map((tag) => (
              <motion.div
                key={tag.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg"
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="text-lg">{getCategoryIcon(tag.category)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-white truncate">{tag.name}</h3>
                      {tag.is_featured && (
                        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/50">
                          <Star size={12} className="mr-1" />
                          Featured
                        </Badge>
                      )}
                      {tag.is_trending && (
                        <Badge className="bg-orange-500/20 text-orange-400 border-orange-500/50">
                          <TrendingUp size={12} className="mr-1" />
                          Trending
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-slate-400 mt-1">
                      <span>{tag.usage_count} uses</span>
                      <span>{tag.view_count} views</span>
                      <span>Rank: #{tag.popularity_rank || 'N/A'}</span>
                      <span>Score: {tag.trend_score.toFixed(1)}</span>
                      {tag.category && (
                        <Badge variant="outline" className="text-xs">
                          {tag.category}
                        </Badge>
                      )}
                    </div>
                    {tag.description && (
                      <p className="text-sm text-slate-500 mt-1 line-clamp-1">
                        {tag.description}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => openEditModal(tag)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleDeleteTag(tag)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 size={16} />
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
          
          {filteredTags.length === 0 && (
            <div className="text-center py-8 text-slate-400">
              No tags found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Tag</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Tag name"
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description"
              />
            </div>
            <div>
              <Label htmlFor="edit-category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.filter(cat => cat.value !== 'all').map(category => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-featured"
                checked={formData.is_featured}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
              />
              <Label htmlFor="edit-featured">Featured tag</Label>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditTag} disabled={isLoading}>
                Update Tag
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}