// src/app/providers.tsx
'use client';

import { useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { persistQueryClient }          from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister }   from '@tanstack/query-sync-storage-persister';

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  // Create the QueryClient once
  const [queryClient] = useState(() => new QueryClient());

  useEffect(() => {
    // Only run in the browser
    if (typeof window === 'undefined') return;

    // Create a localStorage persister
    const persister = createSyncStoragePersister({
      storage: window.localStorage,
      // Optional settings:
      // key: 'MY_APP_CACHE',
      // throttleTime: 2_000,
      // serialize, deserialize, retry…
    });

    // Start persisting the query cache
    persistQueryClient({
      queryClient,
      persister,
      // Optional maxAge (in ms):
      // maxAge: 1000 * 60 * 60 * 24, // 24 hours
    });

    return () => {
      // React Query Persistence does not need cleanup - it's handled automatically
    };
  }, [queryClient]);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
