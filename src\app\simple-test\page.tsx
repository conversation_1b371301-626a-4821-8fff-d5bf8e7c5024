'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function SimpleTestPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    console.log('Files selected:', files.length, files);
    setSelectedFiles(files);
  };

  const handleUpload = async () => {
    if (selectedFiles.length > 0) {
      setIsUploading(true);
      console.log('Starting upload of', selectedFiles.length, 'files');
      
      // Simulate upload
      setTimeout(() => {
        setIsUploading(false);
        console.log('Upload completed');
      }, 2000);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Simple Upload Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Selection */}
          <div>
            <label htmlFor="file-input" className="block text-sm font-medium mb-2">
              Select Images to Upload
            </label>
            <input
              id="file-input"
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Selected Files:</h3>
              <ul className="space-y-1">
                {selectedFiles.map((file, index) => (
                  <li key={index} className="text-sm text-gray-600">
                    {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Upload Controls */}
          <div className="flex gap-2 items-center">
            <Button
              onClick={handleUpload}
              disabled={selectedFiles.length === 0 || isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload Files'}
            </Button>
            <div className="text-xs text-gray-500">
              Debug: Files={selectedFiles.length}, Uploading={isUploading.toString()}
            </div>
          </div>

          {/* Status */}
          {isUploading && (
            <div className="text-sm text-blue-600">
              Uploading {selectedFiles.length} files...
            </div>
          )}

          {/* Configuration Info */}
          <div className="mt-8 p-4 bg-gray-50 rounded">
            <h3 className="text-sm font-medium mb-2">Test Status:</h3>
            <div className="text-xs space-y-1">
              <p>Selected Files: {selectedFiles.length}</p>
              <p>Upload Button Enabled: {selectedFiles.length > 0 && !isUploading ? 'Yes' : 'No'}</p>
              <p>Is Uploading: {isUploading ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
