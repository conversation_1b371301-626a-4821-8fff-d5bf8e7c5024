'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface UserUniqueViewsResponse {
  success: boolean;
  data?: {
    totalReviews: number;
    totalUniqueViews: number;
    totalAuthenticatedViews: number;
    totalAnonymousViews: number;
    averageViewsPerReview: number;
    oldViewCount: number; // From reviews.view_count (legacy)
    dailyViews: Array<{
      date: string;
      uniqueViews: number;
      authenticatedViews: number;
      anonymousViews: number;
    }>;
  };
  error?: string;
}

interface ReviewViewBreakdownResponse {
  success: boolean;
  data?: Array<{
    reviewId: string;
    title: string;
    gameImage: string;
    uniqueViews: number;
    authenticatedViews: number;
    anonymousViews: number;
    oldViewCount: number;
    lastViewed: string;
  }>;
  error?: string;
}

/**
 * Get comprehensive unique view analytics for a user
 * Uses the new review_view_tracking table for accurate daily unique counts
 */
export async function getUserUniqueViews(userId: string, daysBack: number = 30): Promise<UserUniqueViewsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Use the PostgreSQL function for efficient calculation
    const { data: analyticsData, error: analyticsError } = await supabase
      .rpc('get_user_total_unique_views', { user_uuid: userId });

    if (analyticsError) {
      console.error('Error fetching unique views analytics:', analyticsError);
      return { success: false, error: 'Failed to fetch analytics data' };
    }

    const analytics = analyticsData?.[0] || {
      total_reviews: 0,
      total_unique_views: 0,
      total_authenticated_views: 0,
      total_anonymous_views: 0,
      avg_views_per_review: 0
    };

    // Get legacy view count for comparison
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('author_id', userId);

    const oldViewCount = reviewsData?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;

    // Get daily breakdown for the last N days
    const { data: dailyData, error: dailyError } = await supabase
      .from('review_view_tracking')
      .select(`
        view_date,
        is_authenticated
      `)
      .gte('view_date', new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .in('review_id', 
        supabase
          .from('reviews')
          .select('id')
          .eq('author_id', userId)
      );

    let dailyViews: Array<{
      date: string;
      uniqueViews: number;
      authenticatedViews: number;
      anonymousViews: number;
    }> = [];

    if (!dailyError && dailyData) {
      // Group by date
      const dailyMap = new Map<string, { auth: number; anon: number }>();
      
      dailyData.forEach(view => {
        const date = view.view_date;
        const current = dailyMap.get(date) || { auth: 0, anon: 0 };
        
        if (view.is_authenticated) {
          current.auth++;
        } else {
          current.anon++;
        }
        
        dailyMap.set(date, current);
      });

      dailyViews = Array.from(dailyMap.entries())
        .map(([date, counts]) => ({
          date,
          uniqueViews: counts.auth + counts.anon,
          authenticatedViews: counts.auth,
          anonymousViews: counts.anon
        }))
        .sort((a, b) => b.date.localeCompare(a.date));
    }

    return {
      success: true,
      data: {
        totalReviews: Number(analytics.total_reviews),
        totalUniqueViews: Number(analytics.total_unique_views),
        totalAuthenticatedViews: Number(analytics.total_authenticated_views),
        totalAnonymousViews: Number(analytics.total_anonymous_views),
        averageViewsPerReview: Number(analytics.avg_views_per_review),
        oldViewCount,
        dailyViews
      }
    };
  } catch (error) {
    console.error('Error getting user unique views:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get view breakdown for each review by a user
 */
export async function getUserReviewViewBreakdown(userId: string): Promise<ReviewViewBreakdownResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get all reviews with their view tracking data
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select(`
        id,
        title,
        game_name,
        main_image_url,
        igdb_cover_url,
        view_count,
        created_at
      `)
      .eq('author_id', userId)
      .order('created_at', { ascending: false });

    if (reviewsError) {
      console.error('Error fetching reviews:', reviewsError);
      return { success: false, error: 'Failed to fetch reviews' };
    }

    if (!reviewsData || reviewsData.length === 0) {
      return { success: true, data: [] };
    }

    // Get view tracking data for all reviews
    const reviewIds = reviewsData.map(r => r.id);
    const { data: trackingData, error: trackingError } = await supabase
      .from('review_view_tracking')
      .select(`
        review_id,
        is_authenticated,
        created_at
      `)
      .in('review_id', reviewIds);

    if (trackingError) {
      console.error('Error fetching tracking data:', trackingError);
      return { success: false, error: 'Failed to fetch tracking data' };
    }

    // Process the data
    const result = reviewsData.map(review => {
      const viewData = trackingData?.filter(t => t.review_id === review.id) || [];
      const uniqueViews = viewData.length;
      const authenticatedViews = viewData.filter(v => v.is_authenticated).length;
      const anonymousViews = uniqueViews - authenticatedViews;
      const lastViewed = viewData.length > 0 
        ? viewData.reduce((latest, view) => 
            new Date(view.created_at) > new Date(latest.created_at) ? view : latest
          ).created_at
        : review.created_at;

      return {
        reviewId: review.id,
        title: review.title,
        gameName: review.game_name,
        gameImage: review.igdb_cover_url || review.main_image_url || '',
        uniqueViews,
        authenticatedViews,
        anonymousViews,
        oldViewCount: review.view_count || 0,
        lastViewed
      };
    });

    // Sort by unique views descending
    result.sort((a, b) => b.uniqueViews - a.uniqueViews);

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error getting review view breakdown:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get view comparison: old system vs new tracking system
 */
export async function getViewSystemComparison(userId: string): Promise<{
  success: boolean;
  data?: {
    oldSystemTotal: number;
    newSystemTotal: number;
    difference: number;
    accuracyImprovement: number; // percentage
    reviewsWithTracking: number;
    totalReviews: number;
  };
  error?: string;
}> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get old system total (sum of view_count)
    const { data: oldData, error: oldError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('author_id', userId);

    if (oldError) {
      return { success: false, error: 'Failed to fetch old view data' };
    }

    const oldSystemTotal = oldData?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;
    const totalReviews = oldData?.length || 0;

    // Get new system total (count of unique tracking records)
    const { data: newData, error: newError } = await supabase
      .rpc('get_user_total_unique_views', { user_uuid: userId });

    if (newError) {
      return { success: false, error: 'Failed to fetch new view data' };
    }

    const newSystemTotal = Number(newData?.[0]?.total_unique_views || 0);

    // Count reviews that have tracking data
    const { data: trackingData, error: trackingError } = await supabase
      .from('review_view_tracking')
      .select('review_id')
      .in('review_id', 
        supabase
          .from('reviews')
          .select('id')
          .eq('author_id', userId)
      );

    const reviewsWithTracking = trackingData ? new Set(trackingData.map(t => t.review_id)).size : 0;

    const difference = oldSystemTotal - newSystemTotal;
    const accuracyImprovement = oldSystemTotal > 0 
      ? Math.round((difference / oldSystemTotal) * 100 * 100) / 100
      : 0;

    return {
      success: true,
      data: {
        oldSystemTotal,
        newSystemTotal,
        difference,
        accuracyImprovement,
        reviewsWithTracking,
        totalReviews
      }
    };
  } catch (error) {
    console.error('Error getting view system comparison:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}