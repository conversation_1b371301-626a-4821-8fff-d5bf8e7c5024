'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShieldAlert, Settings } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AdminMFANotConfiguredProps {
  onSetupMFA: () => void;
  onContinueWithoutMFA: () => void;
}

export function AdminMFANotConfigured({ onSetupMFA, onContinueWithoutMFA }: AdminMFANotConfiguredProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
      <Card className="w-full max-w-md shadow-lg glow-orange">
        <CardHeader className="items-center">
          <ShieldAlert className="h-16 w-16 text-warning mb-4" />
          <CardTitle className="text-2xl font-bold text-warning">
            MFA Não Configurado
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="default" className="border-warning/20 bg-warning/5">
            <ShieldAlert className="h-4 w-4 text-warning" />
            <AlertDescription className="text-warning">
              Para maior segurança, recomendamos configurar a autenticação de dois fatores (MFA).
            </AlertDescription>
          </Alert>

          <p className="text-muted-foreground">
            Você pode configurar MFA agora para proteger sua conta administrativa, ou continuar sem MFA por enquanto.
          </p>

          <div className="space-y-3">
            <Button
              onClick={onSetupMFA}
              className="w-full bg-primary hover:bg-primary/90"
            >
              <Settings className="mr-2 h-4 w-4" />
              Configurar MFA Agora
            </Button>
            
            <Button
              variant="outline"
              onClick={onContinueWithoutMFA}
              className="w-full"
            >
              Continuar Sem MFA
            </Button>
          </div>

          <div className="text-xs text-muted-foreground mt-4 space-y-1">
            <p>🔒 MFA adiciona uma camada extra de segurança</p>
            <p>⚡ Configuração rápida com aplicativo autenticador</p>
            <p>🛡️ Protege contra acesso não autorizado</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AdminMFANotConfigured; 