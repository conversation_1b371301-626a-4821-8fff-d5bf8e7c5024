"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X } from "lucide-react";
import { UserDashboardNavigation } from "./UserDashboardNavigation";
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';

// Import CSS for adaptive text classes
import '@/components/review-form/style/NewReview.css';

// DASHBOARD REDESIGN: Phase 3 - Advanced Features & Polish
// Date: 15/06/2025
// Task: dashboardStyleAdmin003 (FINAL PHASE)
//
// Created ResponsiveNavigation component with mobile optimization:
// - Collapsible sidebar for tablet/mobile devices
// - Hamburger menu with smooth slide animations
// - Touch-friendly interactive elements (44px minimum)
// - Performance-optimized for mobile devices
// - Keyboard navigation support
// - Backdrop blur and overlay system

interface ResponsiveNavigationProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  stats?: {
    totalReviews: number;
    publishedReviews: number;
    averageScore: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalFollowers: number;
  };
}

export function ResponsiveNavigation({
  activeTab,
  onTabChange,
  stats
}: ResponsiveNavigationProps) {
  const isDarkBackground = useBackgroundBrightness();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Responsive breakpoint detection
  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };
    
    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  // Handle mobile menu toggle with keyboard support
  const toggleMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(prev => !prev);
  }, []);

  // Close mobile menu when tab changes
  const handleTabChange = useCallback((tab: string) => {
    onTabChange?.(tab);
    setIsMobileMenuOpen(false);
  }, [onTabChange]);

  // Handle escape key to close mobile menu
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isMobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobileMenuOpen]);

  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const sidebarVariants = {
    hidden: { 
      x: "-100%",
      transition: {
        type: "spring",
        damping: 30,
        stiffness: 300
      }
    },
    visible: { 
      x: 0,
      transition: {
        type: "spring",
        damping: 30,
        stiffness: 300
      }
    }
  };

  const menuButtonVariants = {
    closed: { rotate: 0 },
    open: { rotate: 180 }
  };

  return (
    <>
      {/* Desktop Sidebar */}
      {!isMobile && !isTablet && (
        <div className="lg:col-span-3">
          <div className="sticky top-44">
            <UserDashboardNavigation 
              activeTab={activeTab}
              onTabChange={onTabChange}
              stats={stats}
            />
          </div>
        </div>
      )}

      {/* Tablet Collapsible Sidebar */}
      {isTablet && (
        <div className="md:col-span-4 lg:col-span-3">
          <div className="sticky top-44">
            <UserDashboardNavigation 
              activeTab={activeTab}
              onTabChange={onTabChange}
              stats={stats}
              compact={true}
            />
          </div>
        </div>
      )}

      {/* Mobile Menu Button */}
      {isMobile && (
        <motion.button
          variants={menuButtonVariants}
          animate={isMobileMenuOpen ? "open" : "closed"}
          whileTap={{ scale: 0.95 }}
          onClick={toggleMobileMenu}
          className={`
            fixed top-[4rem] right-3 z-50 p-2 rounded-md shadow-sm
            bg-slate-800 border border-slate-700/50 text-slate-300
            hover:bg-slate-700 hover:text-slate-200 transition-all duration-200
            focus:outline-none focus:ring-1 focus:ring-violet-400/50
            h-8 w-8 flex items-center justify-center
          `}
          aria-label={isMobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
          aria-expanded={isMobileMenuOpen}
        >
          <AnimatePresence mode="wait">
            {isMobileMenuOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="h-4 w-4" />
              </motion.div>
            ) : (
              <motion.div
                key="menu"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Menu className="h-4 w-4" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.button>
      )}

      {/* Mobile Slide-out Menu */}
      <AnimatePresence>
        {isMobile && isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              variants={backdropVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              onClick={() => setIsMobileMenuOpen(false)}
              className="fixed inset-0 bg-black/80 z-40"
              aria-hidden="true"
            />
            
            {/* Sidebar */}
            <motion.div
              variants={sidebarVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className={`
                fixed left-0 top-14 bottom-0 w-80 max-w-[80vw] z-50 overflow-y-auto
                bg-slate-900 border-r border-violet-500/20
                scrollbar-thin scrollbar-track-slate-800 scrollbar-thumb-violet-600
              `}
              role="dialog"
              aria-modal="true"
              aria-label="Navigation menu"
            >
              <div className="p-6">
                {/* Mobile Header */}
                <motion.div 
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="mb-6 pb-4 border-b border-violet-500/20"
                >
                  <h2 className="text-lg font-bold text-white font-mono">
                    <span className="text-violet-400">&lt;</span>
                    dashboard
                    <span className="text-violet-400">/&gt;</span>
                  </h2>
                  <p className="text-sm text-slate-400 mt-1">Navigation Menu</p>
                </motion.div>

                {/* Navigation Component */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <UserDashboardNavigation 
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    stats={stats}
                    mobile={true}
                  />
                </motion.div>
                
                {/* Mobile Footer */}
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mt-8 pt-4 border-t border-violet-500/20"
                >
                  <p className="text-xs text-slate-500 font-mono">
                    Swipe left or tap outside to close
                  </p>
                </motion.div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Swipe gesture detection for mobile */}
      {isMobile && (
        <div
          className="fixed left-0 top-0 w-8 h-full z-30"
          onTouchStart={(e) => {
            const touch = e.touches[0];
            if (touch.clientX < 32 && !isMobileMenuOpen) {
              setIsMobileMenuOpen(true);
            }
          }}
          aria-hidden="true"
        />
      )}
    </>
  );
} 