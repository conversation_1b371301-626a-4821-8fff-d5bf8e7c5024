-- Migration: Add Privacy Columns to Reviews and Performance Surveys
-- Date: 2025-01-27
-- Purpose: Enable privacy toggle functionality for user content

-- ===========================
-- 1. Add Privacy Columns to Reviews Table
-- ===========================

-- Add privacy columns to reviews table
ALTER TABLE public.reviews 
ADD COLUMN IF NOT EXISTS is_private BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS privacy_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

-- Create index for efficient privacy filtering
CREATE INDEX IF NOT EXISTS idx_reviews_privacy ON public.reviews(author_id, is_private) WHERE is_private = false;

-- Update RLS policies for reviews privacy
DROP POLICY IF EXISTS "Public reviews are viewable by everyone" ON public.reviews;
DROP POLICY IF EXISTS "Users can view their own reviews" ON public.reviews;

-- Create new RLS policies that respect privacy settings
CREATE POLICY "Public reviews are viewable by everyone" 
ON public.reviews FOR SELECT 
USING (is_private = false OR is_private IS NULL);

CREATE POLICY "Users can view their own reviews" 
ON public.reviews FOR SELECT 
USING (auth.uid() = author_id::uuid);

CREATE POLICY "Users can update their own reviews" 
ON public.reviews FOR UPDATE 
USING (auth.uid() = author_id::uuid);

-- ===========================
-- 2. Create Performance Surveys Table (if not exists)
-- ===========================

CREATE TABLE IF NOT EXISTS public.performance_surveys (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email TEXT,
    game_title TEXT,
    platform TEXT NOT NULL,
    
    -- Hardware fields
    device_type TEXT NOT NULL,
    laptop_model TEXT,
    handheld_model TEXT,
    cpu TEXT,
    gpu TEXT,
    total_memory INTEGER,
    memory_gen TEXT,
    memory_speed INTEGER,
    
    -- Performance fields
    fps_average DECIMAL(5,2),
    smoothness INTEGER, -- 1=smooth, 2=minor stutters, 3=choppy
    resolution TEXT,
    ultrawide BOOLEAN DEFAULT FALSE,
    
    -- Feature fields
    frame_gen BOOLEAN DEFAULT FALSE,
    frame_gen_type TEXT,
    upscale BOOLEAN DEFAULT FALSE,
    upscale_type TEXT,
    upscale_preset TEXT,
    
    -- Privacy fields
    is_private BOOLEAN DEFAULT FALSE,
    privacy_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for performance_surveys
CREATE INDEX IF NOT EXISTS idx_performance_surveys_user_id ON public.performance_surveys(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_surveys_privacy ON public.performance_surveys(user_id, is_private) WHERE is_deleted = false;
CREATE INDEX IF NOT EXISTS idx_performance_surveys_game ON public.performance_surveys(game_title, platform) WHERE is_deleted = false AND is_private = false;

-- ===========================
-- 3. RLS Policies for Performance Surveys
-- ===========================

-- Enable RLS
ALTER TABLE public.performance_surveys ENABLE ROW LEVEL SECURITY;

-- Public surveys are viewable by everyone (not private and not deleted)
CREATE POLICY "Public performance surveys are viewable by everyone" 
ON public.performance_surveys FOR SELECT 
USING (is_private = false AND is_deleted = false);

-- Users can view their own surveys (including private and deleted)
CREATE POLICY "Users can view their own performance surveys" 
ON public.performance_surveys FOR SELECT 
USING (auth.uid() = user_id);

-- Users can insert their own surveys
CREATE POLICY "Users can insert their own performance surveys" 
ON public.performance_surveys FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Users can update their own surveys
CREATE POLICY "Users can update their own performance surveys" 
ON public.performance_surveys FOR UPDATE 
USING (auth.uid() = user_id);

-- Users can delete their own surveys (soft delete only)
CREATE POLICY "Users can delete their own performance surveys" 
ON public.performance_surveys FOR UPDATE 
USING (auth.uid() = user_id);

-- ===========================
-- 4. Update Triggers for Timestamps
-- ===========================

-- Update function for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_reviews_updated_at ON public.reviews;
CREATE TRIGGER update_reviews_updated_at
    BEFORE UPDATE ON public.reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_performance_surveys_updated_at ON public.performance_surveys;
CREATE TRIGGER update_performance_surveys_updated_at
    BEFORE UPDATE ON public.performance_surveys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===========================
-- 5. Privacy Analytics View
-- ===========================

-- Create view for privacy analytics
CREATE OR REPLACE VIEW public.privacy_analytics AS
SELECT 
    'reviews' as content_type,
    author_id as user_id,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_private = true) as private_count,
    COUNT(*) FILTER (WHERE is_private = false OR is_private IS NULL) as public_count
FROM public.reviews
GROUP BY author_id

UNION ALL

SELECT 
    'performance_surveys' as content_type,
    user_id,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_private = true) as private_count,
    COUNT(*) FILTER (WHERE is_private = false AND is_deleted = false) as public_count
FROM public.performance_surveys
WHERE is_deleted = false
GROUP BY user_id;

-- Grant access to the view
GRANT SELECT ON public.privacy_analytics TO authenticated;

-- ===========================
-- 6. Comments
-- ===========================

COMMENT ON COLUMN public.reviews.is_private IS 'When true, review is only visible to the author';
COMMENT ON COLUMN public.reviews.privacy_updated_at IS 'Timestamp when privacy setting was last changed';
COMMENT ON COLUMN public.performance_surveys.is_private IS 'When true, survey is only visible to the author';
COMMENT ON COLUMN public.performance_surveys.privacy_updated_at IS 'Timestamp when privacy setting was last changed';
COMMENT ON COLUMN public.performance_surveys.is_deleted IS 'Soft delete flag';
COMMENT ON COLUMN public.performance_surveys.deleted_at IS 'Timestamp when survey was soft deleted';

-- ===========================
-- End of Migration
-- ===========================