/* Banner Preview Component Styles - Enhanced Version */

/* Banner image styling with smart positioning - matches reviewBanner.tsx */
.banner-image-smart {
  object-position: center 25%;
  filter: brightness(1.1) contrast(1.05) saturate(1.1);
}

/* High-quality image rendering - matches reviewBanner.tsx */
.banner-image-hq {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  image-rendering: high-quality;
  image-rendering: pixelated;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform;
}

/* Banner preview image with enforced 4:1 ratio styling */
.banner-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* Default object position when not user-adjusted */
  object-position: center 25%;
  /* Smooth object position transitions for dragging */
  transition: object-position 0.1s ease-out;
}

/* Banner preview image states */
.banner-preview-image.loading {
  display: none;
}

.banner-preview-image.loaded {
  display: block;
}

/* Preview container specific styles - ENFORCED 4:1 ASPECT RATIO */
.banner-preview-container {
  width: 100%;
  /* ENFORCE EXACT 4:1 ASPECT RATIO - This ensures the preview container maintains the same ratio as reviewBanner */
  aspect-ratio: 4 / 1;
  position: relative; /* Added for overlay positioning */
  overflow: hidden; /* Added to contain overlays */
  /* Ensure minimum height for usability on very small screens */
  min-height: 120px;
  /* Drop shadow effect matching reviewBanner.tsx */
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.6)) drop-shadow(0 10px 20px rgba(0, 0, 0, 0.4));
}

/* Fallback heights for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 4 / 1) {
  .banner-preview-container {
    height: clamp(280px, 55vw, 320px); /* Mobile: exact match */
  }

  @media (min-width: 640px) {
    .banner-preview-container {
      height: clamp(220px, 35vw, 280px); /* Tablet: exact match */
    }
  }

  @media (min-width: 1024px) {
    .banner-preview-container {
      height: clamp(500px, 50vw, 640px); /* Desktop: exact match */
    }
  }
}

/* Ensure full width for banner preview */
.banner-preview-full-width {
  width: 100% !important;
  max-width: none !important;
}

/* Video preview container for consistent styling */
.video-preview-container {
  width: 100%;
  aspect-ratio: 16/9; /* Standard video aspect ratio */
}

/* Banner mask effect - matches reviewBanner.tsx exactly */
.banner-preview-mask {
  -webkit-mask: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 1%, rgba(0,0,0,0.3) 3%, rgba(0,0,0,0.7) 5%, rgba(0,0,0,1) 10%, rgba(0,0,0,1) 90%, rgba(0,0,0,0.7) 95%, rgba(0,0,0,0.3) 97%, rgba(0,0,0,0.05) 99%, rgba(0,0,0,0) 100%);
  mask: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 1%, rgba(0,0,0,0.3) 3%, rgba(0,0,0,0.7) 5%, rgba(0,0,0,1) 10%, rgba(0,0,0,1) 90%, rgba(0,0,0,0.7) 95%, rgba(0,0,0,0.3) 97%, rgba(0,0,0,0.05) 99%, rgba(0,0,0,0) 100%);
}

/* Responsive object positioning for better cropping - matches reviewBanner.tsx */
@media (max-width: 640px) {
  .banner-image-smart {
    object-position: center 30%;
  }
}

@media (min-width: 1024px) {
  .banner-image-smart {
    object-position: center 20%;
  }
}

/* Enhanced drag interaction styles */
.banner-preview-dragging {
  cursor: grabbing !important;
}

.banner-preview-drag-indicator {
  background: rgba(139, 92, 246, 0.2);
  border: 2px solid rgba(139, 92, 246, 0.5);
}

/* ENHANCED: Position adjustment overlay for better UX */
.position-overlay {
  background: linear-gradient(45deg, 
    rgba(139, 92, 246, 0.1) 0%, 
    transparent 25%, 
    transparent 75%, 
    rgba(139, 92, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.position-overlay.active {
  opacity: 1;
  pointer-events: auto;
}

/* ENHANCED: Grid overlay for positioning guidance */
.position-grid {
  background-image: 
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.position-grid.active {
  opacity: 0.3;
}

/* Size recommendation styles - your existing ones are perfect */
.size-recommendation-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.size-recommendation-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.size-recommendation-info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* ENHANCED: Smooth transitions for all interactions */
.banner-preview-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ENHANCED: Loading state animation */
.banner-image-loading {
  background: linear-gradient(90deg, 
    rgba(71, 85, 105, 0.4) 0%, 
    rgba(71, 85, 105, 0.6) 50%, 
    rgba(71, 85, 105, 0.4) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ENHANCED: Better hover states */
.banner-preview-container:hover .position-overlay {
  opacity: 0.5;
}

/* ENHANCED: Improved drag states */
.banner-preview-container {
  cursor: grab;
}

.banner-preview-container:active,
.banner-preview-container.dragging {
  cursor: grabbing;
}

.banner-preview-container.dragging .banner-image-smart {
  -webkit-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
}

/* ENHANCED: Focus states for accessibility */
.banner-preview-container:focus-within {
  outline: 2px solid rgba(139, 92, 246, 0.5);
  outline-offset: 2px;
}

/* ENHANCED: Responsive text scaling for mobile */
@media (max-width: 640px) {
  .banner-preview-info {
    font-size: 0.75rem;
  }
}