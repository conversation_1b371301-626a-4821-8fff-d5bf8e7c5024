// src/app/api/b2/test-upload/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { uploadImageToB2, generateFile<PERSON>ey } from '@/lib/services/b2StorageService';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Create a simple test file (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45,
      0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Generate a test key
    const testKey = generateFileKey('test-upload.png', user.id);

    try {
      // Test upload
      const result = await uploadImageToB2(
        testImageBuffer,
        testKey,
        'image/png',
        {
          testUpload: 'true',
          uploadedBy: user.id,
          timestamp: new Date().toISOString(),
        }
      );

      return NextResponse.json({
        success: true,
        message: 'Test upload successful',
        result,
        testKey,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('B2 Test Upload Error:', error);

      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : 'Test upload failed',
        details: error,
      });
    }

  } catch (error) {
    console.error('Test Upload API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return NextResponse.json({}, { status: 200 });
}