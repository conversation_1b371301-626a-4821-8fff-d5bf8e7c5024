import { NextResponse } from 'next/server';
import { getSiteStats } from '@/lib/services/lineCounterService';

export async function GET() {
  try {
    const stats = await getSiteStats();

    if (!stats) {
      return NextResponse.json(
        { error: 'Failed to fetch site statistics' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      totalUsers: stats.totalUsers,
      totalReviews: stats.totalReviews,
      totalSurveys: stats.totalSurveys,
      totalLines: stats.totalLines,
      lastUpdated: stats.lastUpdated,
      averageLinesPerReview: stats.totalReviews > 0 ? Math.round(stats.totalLines / stats.totalReviews) : 0
    });
  } catch (error) {
    console.error('Error in site stats API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
