// src/app/api/b2/upload/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { uploadImageWorkflow } from '@/lib/services/b2StorageService';
import { createServerClient } from '@/lib/supabase/server';
import { validateImageSecurity, UploadRateLimit, detectDuplicate } from '@/lib/security/imageValidation';
import { UserQuotaManager } from '@/lib/security/uploadQuota';
import { ImageOptimizer } from '@/lib/performance/imageOptimization';

// Rate limiter instance
const rateLimiter = new UploadRateLimit(20, 60); // 20 uploads per hour

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile for premium status
    const { data: profile } = await supabase
      .from('profiles')
      .select('premium_status')
      .eq('id', user.id)
      .single();

    const isPremium = profile?.premium_status === 'active';

    // Check rate limiting
    const rateCheck = rateLimiter.checkRateLimit(user.id);
    if (!rateCheck.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          resetTime: rateCheck.resetTime,
          remaining: rateCheck.remaining,
        },
        { status: 429 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const files = formData.getAll('images') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Check file count limit (max 10 files per request)
    if (files.length > 10) {
      return NextResponse.json(
        { error: 'Too many files. Maximum 10 files per request.' },
        { status: 400 }
      );
    }

    // Process uploads with security checks
    const uploadPromises = files.map(async (file) => {
      if (!(file instanceof File)) {
        return { success: false, error: 'Invalid file object' };
      }

      try {
        // Check quota before processing
        const quotaCheck = await UserQuotaManager.checkQuota(
          user.id,
          file.size,
          isPremium,
          supabase
        );

        if (!quotaCheck.allowed) {
          return {
            success: false,
            error: quotaCheck.reason || 'Quota exceeded',
            originalName: file.name,
            quota: quotaCheck.usage,
          };
        }

        // Process upload with enhanced workflow
        const result = await uploadImageWorkflow(file, user.id);

        // If successful, store in database
        if (result.success && result.metadata) {
          const { error: dbError } = await supabase
            .from('user_images')
            .insert({
              user_id: user.id,
              b2_file_id: result.key || '',
              b2_url: result.url || '',
              b2_key: result.key || '',
              original_name: file.name,
              file_size: result.metadata.size || file.size,
              mime_type: `image/${result.metadata.format || 'webp'}`,
              checksum: result.metadata.checksum || '',
              variants: result.metadata.variants || {},
              metadata: {
                originalSize: file.size,
                compressionRatio: result.metadata.compressionRatio || 1,
                width: result.metadata.width,
                height: result.metadata.height,
              },
            });

          if (dbError) {
            console.error('Database insert error:', dbError);
            // Continue with upload success even if DB insert fails
          }
        }

        return {
          ...result,
          quota: quotaCheck.usage,
        };
      } catch (error) {
        console.error(`Upload error for ${file.name}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Upload failed',
          originalName: file.name,
        };
      }
    });

    const results = await Promise.all(uploadPromises);

    // Separate successful and failed uploads
    const successful = results.filter(result => result.success);
    const failed = results.filter(result => !result.success);

    return NextResponse.json({
      success: true,
      uploaded: successful.length,
      failed: failed.length,
      results: results,
    });

  } catch (error) {
    console.error('Upload API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return NextResponse.json({}, { status: 200 });
}
