// FIREBASE IMPORT REMOVED - PLACEHOLDER TYPE DEFINITIONS
// TODO: Replace Firebase types with Supabase equivalents when ready
// import firebase from 'firebase/compat/app';

// Generic Timestamp type to replace Firebase Timestamp
type Timestamp = {
  seconds: number;
  nanoseconds: number;
} | Date | string;

// Import the unified UserProfile interface from the profile types
import type { UserProfile, PrivacySettings, UserStats, Achievement, GamingProfile as ProfileGamingProfile } from '@/lib/types/profile';
export type { UserProfile, PrivacySettings, UserStats, Achievement, ProfileGamingProfile };

// Legacy GamingProfile interface for backward compatibility
export interface GamingProfile {
  platform: 'steam' | 'xbox' | 'playstation' | 'nintendo';
  username: string;
  url?: string;
}

export interface SocialMediaProfile {
  platform: 'twitch' | 'youtube' | 'twitter' | 'github';
  username: string;
  url?: string;
}

// Add these interfaces and types to your existing src/lib/types.ts file

// Custom Colors Interface - NEW
export interface CustomColors {
  primary: string;
  secondary: string;
  accent: string;
}

// Theme-related types - NEW
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  gradient: string;
}

export interface ProfileTheme {
  id: string;
  name: string;
  description: string;
  preview: string;
  colors: ThemeColors;
  isPremium?: boolean;
  isCustom?: boolean;
  customColors?: CustomColors;
}

// Form data types for profile editing - NEW
export interface ProfileFormData {
  bio?: string;
  preferredGenres?: string[];
  favoriteConsoles?: string[];
  gamingProfiles?: GamingProfile[];
  socialMedia?: SocialMediaProfile[];
  photoURL?: string;
  bannerURL?: string;
  theme?: string;
  customColors?: CustomColors;
}

// Theme-related utility types - NEW
export type ThemeId = string;
export type ColorType = 'primary' | 'secondary' | 'accent';

// Color validation types - NEW
export interface ColorValidation {
  isValid: boolean;
  error?: string;
}

export interface CustomColorState {
  colors: CustomColors;
  isValid: boolean;
  isDirty: boolean;
}

// Component prop types for theme components - NEW
export interface ThemeProviderProps {
  theme: string;
  customColors?: CustomColors;
  children: React.ReactNode;
}

export interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  label?: string;
  disabled?: boolean;
}

export interface ThemeSelectorProps {
  value: string;
  customColors?: CustomColors;
  onChange: (themeId: string) => void;
  onCustomColorsChange?: (colors: CustomColors) => void;
  showCustomOption?: boolean;
  isPremiumUser?: boolean;
}

// API response types - NEW
export interface ProfileUpdateResponse {
  success: boolean;
  profile?: UserProfile; // Updated to UserProfile
  error?: string;
}

export interface ThemeValidationResponse {
  isValid: boolean;
  colors?: CustomColors;
  errors?: string[];
}

// Export commonly used default values - NEW
export const DEFAULT_CUSTOM_COLORS: CustomColors = {
  primary: '#8b5cf6',
  secondary: '#7c3aed',
  accent: '#ec4899'
};

export const DEFAULT_THEME_ID = 'muted-dark';

// Extended UserProfile for components that need additional legacy fields
export interface ExtendedUserProfile extends UserProfile {
  // Legacy compatibility fields
  uid?: string; // Maps to id
  userName?: string; // Maps to username
  displayName?: string; // Maps to display_name
  avatarUrl?: string; // Maps to avatar_url
  bannerUrl?: string; // Maps to banner_url
  preferredGenres?: string[]; // Maps to preferred_genres
  favoriteConsoles?: string[]; // Maps to favorite_consoles
  reviewCount?: number; // Maps to review_count
  isAdmin?: boolean; // Maps to is_admin
  isOnline?: boolean; // Maps to is_online
  lastSeen?: Date | null; // Maps to last_seen
  createdAt?: Date | null; // Maps to created_at
  updatedAt?: Date | null; // Maps to updated_at
  customColors?: CustomColors; // Maps to custom_colors
  privacySettings?: {
    showOnlineStatus: boolean;
    showGamingProfiles: boolean;
    allowFriendRequests: boolean;
    showAchievements: boolean;
  }; // Legacy format for privacy_settings

  // Additional fields that might be needed by specific components
  rawDisplayName?: string;
  gamingProfiles?: GamingProfile[];
  socialMedia?: SocialMediaProfile[];
  
  // User suspension system for legacy compatibility
  isSuspended?: boolean; // Maps to suspended
  suspensionReason?: string | null; // Maps to suspension_reason
  suspendedAt?: Date | string | null; // Maps to suspended_at
  suspendedBy?: string | null; // Maps to suspended_by
}

// Standardized MonetizationBlock interface
export interface MonetizationBlockData {
  code?: string;
  imageUrl?: string;
  linkUrl?: string;
  title?: string;
  description?: string;
  buttonText?: string;
}

export interface MonetizationBlock {
  id: string;
  type: 'code' | 'imageLink' | 'banner' | 'affiliate' | 'sponsored';
  placement: string;
  data: MonetizationBlockData;
  content?: MonetizationBlockData; // For backward compatibility
  styling?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderRadius?: string;
  };
  isActive?: boolean;
}

// Review Interface
export interface Review {
  id: string; // Document ID
  title: string;
  gameName: string;
  slug: string;
  releaseDate?: string | Timestamp; // Optional release date
  publishDate?: string | Timestamp; // Optional publish date
  scoringCriteria: { id: string; name: string; score: number }[];
  overallScore: number;
  platforms: string[];
  genres: string[];
  language: string;
  playedOn: string; // e.g., "PC", "PlayStation 5", "Xbox Series X"
  datePlayed?: string; // Optional, ISO string or formatted date
  contentLexical: any; // Lexical editor state object or null
  metaTitle?: string;
  metaDescription?: string;
  focusKeyword?: string;
  tags?: string[];
  mainImageUrl?: string;
  mainImagePosition?: string; // Custom object position for banner image (e.g., "50% 25%")
  igdbCoverUrl?: string;
  galleryImageUrls?: string[];
  videoUrl?: string;
  monetizationBlocks?: MonetizationBlock[];
  status: 'draft' | 'pending' | 'published'; // Review status
  featuredHomepage?: boolean;
  authorId: string;
  authorName: string; // Author's display name or a generic placeholder (not email)
  authorPhotoURL?: string; // URL for author's profile picture
  authorSlug?: string; // Slug for linking to author's profile
  createdAt: Timestamp | Date;
  // Additional IGDB metadata fields
  igdbId?: number;
  summary?: string;
  aggregatedRating?: number;
  aggregatedRatingCount?: number;
  developers?: string[];
  publishers?: string[];
  gameEngines?: string[];
  playerPerspectives?: string[];
  timeToBeatNormally?: number;
  timeToBeatCompletely?: number;
  // Additional fields needed by ReviewBanner component
  officialGameLink?: string; // Link to the official game website/store page
  discussionLink?: string; // Link to the review's discussion section
  // Adicionado para bloqueio de review
  is_blocked?: boolean;
  // Analytics fields
  view_count?: number;
  likes_count?: number;
  // Comment settings
  enable_comments?: boolean;
  // Privacy settings
  is_private?: boolean;
}