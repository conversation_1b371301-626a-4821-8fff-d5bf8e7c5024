# 📖 Guide 1: Database Setup and Migration

## 🎯 Objective
Set up the complete database infrastructure for the admin settings system including tables, RLS policies, indexes, triggers, and default data.

## 🚀 Implementation Steps

### Step 1: Create the Migration File

Create a new migration file in your Supabase project:

```bash
# If using local Supabase CLI
supabase migration new create_admin_settings_system

# Or create the file manually
# File: supabase/migrations/YYYYMMDDHHMMSS_create_admin_settings_system.sql
```

### Step 2: Implement the Complete Migration

Copy the following SQL into your migration file and add detailed comments:

```sql
-- ============================================================================
-- ADMIN SETTINGS SYSTEM MIGRATION
-- ============================================================================
-- Description: Creates a comprehensive admin settings system with JSONB storage,
--              RLS policies, audit logging, and default configuration data
-- Author: AI Assistant
-- Date: [Current Date]
-- Version: 1.0.0
-- ============================================================================

-- Step 1: Create the category enum for type safety
-- This enum defines all available setting categories in the system
CREATE TYPE admin_setting_category AS ENUM (
  'general',       -- Basic site information (name, URL, description, etc.)
  'seo',          -- SEO and analytics settings (meta tags, GA, etc.)
  'content',      -- Content management settings (registration, moderation, etc.)
  'security',     -- Security policies (passwords, sessions, file uploads, etc.)
  'notifications', -- Email and notification settings (SMTP, webhooks, etc.)
  'integrations'  -- Third-party integrations (IGDB, Discord, backups, etc.)
);

-- Comment: Using an enum ensures only valid categories can be stored
-- and provides database-level validation for setting organization

-- Step 2: Create the main admin settings table
-- This table uses JSONB for flexible storage while maintaining structure
CREATE TABLE admin_settings (
  -- Primary key with UUID for global uniqueness
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Category classification using our enum
  category admin_setting_category NOT NULL,
  
  -- Setting key within the category (e.g., 'site_name', 'meta_title')
  key TEXT NOT NULL,
  
  -- Setting value stored as JSONB for flexibility and performance
  value JSONB NOT NULL,
  
  -- Optional schema for runtime validation metadata
  schema JSONB,
  
  -- Audit timestamps for tracking changes
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Track who created/modified the setting
  created_by UUID REFERENCES auth.users(id),
  
  -- Ensure unique category+key combinations
  UNIQUE(category, key)
);

-- Comment: JSONB provides excellent performance for JSON operations
-- while maintaining the ability to index and query nested data

-- Step 3: Create performance indexes
-- Index on category for fast category-based queries
CREATE INDEX idx_admin_settings_category ON admin_settings(category);

-- Comment: Most queries will filter by category, making this index essential

-- Index on key for fast key-based lookups
CREATE INDEX idx_admin_settings_key ON admin_settings(key);

-- Comment: Allows for efficient single-setting retrievals

-- Index on created_by for admin audit tracking
CREATE INDEX idx_admin_settings_created_by ON admin_settings(created_by);

-- Comment: Essential for admin activity tracking and audit reports

-- Composite index for the most common query pattern
CREATE INDEX idx_admin_settings_category_key ON admin_settings(category, key);

-- Comment: Optimizes the primary lookup pattern used by the application

-- Step 4: Enable Row Level Security
-- This ensures only authorized users can access settings
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Comment: RLS provides database-level security that cannot be bypassed
-- even if application logic has vulnerabilities

-- Step 5: Create RLS policies for admin access
-- Policy allows all operations (SELECT, INSERT, UPDATE, DELETE) for admin users
CREATE POLICY "Admin users can manage all settings" ON admin_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'isAdmin' = 'true'
    )
  );

-- Comment: This policy checks if the current user has isAdmin = true
-- in their user metadata. Adjust this based on your admin identification method

-- Alternative admin check policy (uncomment if using admin_users table):
-- CREATE POLICY "Admin users can manage all settings" ON admin_settings
--   FOR ALL USING (
--     EXISTS (
--       SELECT 1 FROM admin_users 
--       WHERE user_id = auth.uid()
--     )
--   );

-- Step 6: Grant necessary permissions
-- Allow authenticated users to access the table (RLS will control actual access)
GRANT ALL ON admin_settings TO authenticated;

-- Comment: GRANT gives permission to the role, but RLS policies control
-- which rows each user can actually access

-- Step 7: Create automatic timestamp update function
-- This function automatically updates the updated_at column on changes
CREATE OR REPLACE FUNCTION update_admin_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Set the updated_at timestamp to current time
  NEW.updated_at = NOW();
  
  -- Return the modified row
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Comment: Using a trigger ensures updated_at is always accurate
-- and cannot be forgotten or manipulated by application code

-- Step 8: Create the update trigger
-- This trigger calls our function before any UPDATE operation
CREATE TRIGGER update_admin_settings_updated_at
  BEFORE UPDATE ON admin_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_settings_updated_at();

-- Comment: BEFORE UPDATE ensures the timestamp is set before
-- the row is actually updated in the database

-- Step 9: Insert comprehensive default settings
-- These defaults provide a working configuration out of the box
INSERT INTO admin_settings (category, key, value) VALUES

-- GENERAL SETTINGS: Basic site configuration
('general', 'site_name', '"CriticalPixel"'),
('general', 'site_url', '"https://criticalpixel.com"'),
('general', 'site_description', '"Gaming reviews and community platform for gamers worldwide"'),
('general', 'admin_email', '"<EMAIL>"'),
('general', 'timezone', '"UTC"'),
('general', 'language', '"en"'),
('general', 'maintenance_mode', 'false'),
('general', 'maintenance_message', '"Site under maintenance. Please check back later."'),

-- SEO SETTINGS: Search engine optimization and analytics
('seo', 'meta_title', '"CriticalPixel - Gaming Reviews & Community"'),
('seo', 'meta_description', '"The ultimate destination for gaming reviews, news, and community discussions."'),
('seo', 'meta_keywords', '"gaming, reviews, community, esports, game reviews, gaming news"'),
('seo', 'og_image', '""'),
('seo', 'twitter_card', '"summary_large_image"'),
('seo', 'google_analytics_id', '""'),
('seo', 'google_search_console', '""'),

-- CONTENT SETTINGS: User-generated content management
('content', 'allow_user_registration', 'true'),
('content', 'require_email_verification', 'true'),
('content', 'allow_anonymous_comments', 'false'),
('content', 'moderate_comments', 'true'),
('content', 'max_review_length', '10000'),
('content', 'max_comment_length', '1000'),
('content', 'featured_reviews_count', '6'),

-- SECURITY SETTINGS: Platform security and user protection
('security', 'enable_rate_limiting', 'true'),
('security', 'require_strong_passwords', 'true'),
('security', 'enable_two_factor', 'false'),
('security', 'max_login_attempts', '5'),
('security', 'session_timeout', '3600'),
('security', 'max_file_size', '5242880'),

-- NOTIFICATION SETTINGS: Email and messaging configuration
('notifications', 'email_notifications', 'true'),
('notifications', 'admin_notifications', 'true'),
('notifications', 'user_notifications', 'true'),
('notifications', 'newsletter_enabled', 'true'),
('notifications', 'smtp_host', '""'),
('notifications', 'smtp_port', '587'),
('notifications', 'smtp_username', '""'),
('notifications', 'smtp_password', '""'),

-- INTEGRATION SETTINGS: External service connections
('integrations', 'igdb_api_key', '""'),
('integrations', 'discord_webhook', '""'),
('integrations', 'slack_webhook', '""'),
('integrations', 'backup_enabled', 'false'),
('integrations', 'backup_frequency', '"daily"'),
('integrations', 'backup_retention', '30');

-- Comment: Default values are stored as JSON strings in JSONB format
-- Empty strings are used for optional settings to maintain consistency

-- Step 10: Create audit log table for change tracking
-- This table maintains a complete history of all setting changes
CREATE TABLE admin_settings_audit (
  -- Primary key for audit record
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Reference to the setting that was changed
  setting_id UUID REFERENCES admin_settings(id),
  
  -- Copy of category and key for historical integrity
  category admin_setting_category NOT NULL,
  key TEXT NOT NULL,
  
  -- Previous value (NULL for new settings)
  old_value JSONB,
  
  -- New value after the change
  new_value JSONB NOT NULL,
  
  -- Who made the change
  changed_by UUID REFERENCES auth.users(id),
  
  -- When the change occurred
  changed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Comment: Audit tables are crucial for compliance and debugging
-- They provide a complete history of all changes to sensitive settings

-- Step 11: Create audit table indexes
-- Index for finding changes to a specific setting
CREATE INDEX idx_admin_settings_audit_setting_id ON admin_settings_audit(setting_id);

-- Index for finding changes by a specific user
CREATE INDEX idx_admin_settings_audit_changed_by ON admin_settings_audit(changed_by);

-- Index for finding changes within a time range
CREATE INDEX idx_admin_settings_audit_changed_at ON admin_settings_audit(changed_at);

-- Comment: These indexes support common audit queries like
-- "show all changes by user X" or "show all changes to setting Y"

-- Step 12: Enable RLS on audit table
ALTER TABLE admin_settings_audit ENABLE ROW LEVEL SECURITY;

-- Step 13: Create audit table RLS policy
-- Only admin users can view audit logs
CREATE POLICY "Admin users can view audit logs" ON admin_settings_audit
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'isAdmin' = 'true'
    )
  );

-- Comment: Audit logs are read-only for security reasons
-- Only SELECT permission is granted to prevent tampering

-- Step 14: Grant audit table permissions
GRANT SELECT, INSERT ON admin_settings_audit TO authenticated;

-- Comment: INSERT is needed for the trigger, SELECT for admin queries
-- UPDATE and DELETE are intentionally omitted for audit integrity

-- Step 15: Create audit logging function
-- This function automatically logs all changes to settings
CREATE OR REPLACE FUNCTION log_admin_setting_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle UPDATE operations
  IF TG_OP = 'UPDATE' THEN
    INSERT INTO admin_settings_audit (
      setting_id, 
      category, 
      key, 
      old_value, 
      new_value, 
      changed_by
    ) VALUES (
      NEW.id,
      NEW.category,
      NEW.key,
      OLD.value,    -- Previous value
      NEW.value,    -- New value
      NEW.created_by
    );
    
  -- Handle INSERT operations (new settings)
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO admin_settings_audit (
      setting_id, 
      category, 
      key, 
      old_value, 
      new_value, 
      changed_by
    ) VALUES (
      NEW.id,
      NEW.category,
      NEW.key,
      NULL,         -- No previous value for new settings
      NEW.value,    -- New value
      NEW.created_by
    );
  END IF;
  
  -- Return the row for the main operation to proceed
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Comment: This function captures both new settings and modifications
-- providing a complete audit trail for compliance and debugging

-- Step 16: Create audit trigger
-- This trigger calls our audit function after any INSERT or UPDATE
CREATE TRIGGER admin_settings_audit_trigger
  AFTER INSERT OR UPDATE ON admin_settings
  FOR EACH ROW
  EXECUTE FUNCTION log_admin_setting_change();

-- Comment: AFTER trigger ensures the main operation succeeds
-- before attempting to log it

-- Step 17: Create utility functions for common operations

-- Function to get a single setting value by category and key
CREATE OR REPLACE FUNCTION get_admin_setting(
  p_category admin_setting_category,
  p_key TEXT
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT value INTO result
  FROM admin_settings
  WHERE category = p_category AND key = p_key;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comment: SECURITY DEFINER allows this function to bypass RLS
-- for internal system use while maintaining audit trails

-- Function to safely update a setting value
CREATE OR REPLACE FUNCTION set_admin_setting(
  p_category admin_setting_category,
  p_key TEXT,
  p_value JSONB,
  p_user_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO admin_settings (category, key, value, created_by)
  VALUES (p_category, p_key, p_value, p_user_id)
  ON CONFLICT (category, key) 
  DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = NOW(),
    created_by = EXCLUDED.created_by;
    
  RETURN TRUE;
EXCEPTION WHEN OTHERS THEN
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comment: This function provides a safe way to update settings
-- with proper error handling and automatic conflict resolution

-- Step 18: Verification and validation
-- Check that everything was created successfully
DO $$
DECLARE
  settings_count INTEGER;
  tables_exist BOOLEAN;
BEGIN
  -- Verify tables were created
  SELECT COUNT(*) > 0 INTO tables_exist
  FROM information_schema.tables 
  WHERE table_name IN ('admin_settings', 'admin_settings_audit');
  
  IF NOT tables_exist THEN
    RAISE EXCEPTION 'Required tables were not created successfully';
  END IF;
  
  -- Verify default data was inserted
  SELECT COUNT(*) INTO settings_count FROM admin_settings;
  
  IF settings_count = 0 THEN
    RAISE EXCEPTION 'Default settings were not inserted';
  END IF;
  
  -- Success message
  RAISE NOTICE 'Admin settings system created successfully!';
  RAISE NOTICE 'Tables: admin_settings, admin_settings_audit';
  RAISE NOTICE 'Default settings count: %', settings_count;
  RAISE NOTICE 'RLS enabled on both tables';
  RAISE NOTICE 'Audit logging active';
END $$;

-- Step 19: Display system summary
-- Show a summary of what was created
SELECT 
  'admin_settings' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT category) as categories,
  MIN(created_at) as first_created,
  MAX(updated_at) as last_updated
FROM admin_settings

UNION ALL

SELECT 
  'admin_settings_audit' as table_name,
  COUNT(*) as record_count,
  COUNT(DISTINCT category) as categories,
  MIN(changed_at) as first_created,
  MAX(changed_at) as last_updated
FROM admin_settings_audit;

-- Comment: This summary helps verify the migration completed successfully
-- and provides baseline metrics for the new system

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================
-- Next Steps:
-- 1. Verify that RLS policies work by testing with admin and non-admin users
-- 2. Test that audit logging captures changes correctly
-- 3. Confirm that all indexes are being used in query plans
-- 4. Proceed to Guide 2: TypeScript Schemas and Validation Layer
-- ============================================================================
```

### Step 3: Execute the Migration

Run the migration using your preferred method:

```bash
# Using Supabase CLI
supabase db push

# Or using SQL directly in Supabase Dashboard
# Copy and paste the migration SQL into the SQL Editor
```

### Step 4: Verify the Implementation

Run these verification queries:

```sql
-- Test 1: Verify table structure
\d admin_settings;

-- Test 2: Check default data
SELECT category, COUNT(*) as setting_count 
FROM admin_settings 
GROUP BY category 
ORDER BY category;

-- Test 3: Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('admin_settings', 'admin_settings_audit');

-- Test 4: Test utility functions
SELECT get_admin_setting('general', 'site_name');
```

### Step 5: Test Admin Access

```sql
-- This should work for admin users
SELECT * FROM admin_settings WHERE category = 'general';

-- This should fail for non-admin users (empty result set)
-- (Test with a non-admin user account)
```

## 📝 Implementation Comments Required

When implementing this guide, add comments explaining:

1. **Why JSONB was chosen** for value storage
2. **How RLS policies protect** sensitive settings
3. **The purpose of each index** and its performance impact
4. **How the audit system maintains** data integrity
5. **Why triggers are used** for automatic timestamping
6. **How utility functions** simplify common operations

## ✅ Completion Checklist

- [x] Migration file created and executed successfully ✅ **COMPLETED 13/06/2025** - All 17 SQL steps executed via Supabase MCP
- [x] admin_settings table exists with correct structure ✅ **COMPLETED 13/06/2025** - Table created with JSONB storage, UUID keys, and enum categories
- [x] admin_settings_audit table exists with correct structure ✅ **COMPLETED 13/06/2025** - Audit table with complete change tracking
- [x] All indexes created and optimized ✅ **COMPLETED 13/06/2025** - 7 strategic indexes for performance optimization
- [x] RLS policies active and tested ✅ **COMPLETED 13/06/2025** - Admin-only access policies verified on both tables
- [x] Default settings data inserted (30+ records) ✅ **COMPLETED 13/06/2025** - 42 default settings across 6 categories inserted
- [x] Triggers functioning for timestamps and audit ✅ **COMPLETED 13/06/2025** - Automatic timestamp updates and audit logging active
- [x] Utility functions created and tested ✅ **COMPLETED 13/06/2025** - get_admin_setting() and set_admin_setting() functions working
- [x] Admin access verified ✅ **COMPLETED 13/06/2025** - RLS policies enforcing admin-only access
- [x] Non-admin access blocked ✅ **COMPLETED 13/06/2025** - Non-admin users cannot access settings tables

## 🔄 Next Step Initialization

✅ **GUIDE 1 COMPLETED - 13/06/2025**

All database infrastructure has been successfully implemented and verified. Ready to proceed to:

**Guide 2: TypeScript Schemas and Validation Layer** (`02-SCHEMAS_VALIDATION_GUIDE.md`)

The next guide will create type-safe schemas and validation for the database structure you just implemented.

**Implementation Log**: See `.01Documentos/130625-AdminSettingsDBSetup001.md` for complete details.

## 🎯 AI Implementation Instructions

**AI Assistant**: When implementing this guide:

1. ✅ **Copy the complete SQL migration** into the appropriate file - **COMPLETED**
2. ✅ **Add detailed comments** explaining each section's purpose - **COMPLETED**
3. ✅ **Execute the migration** and verify all components are created - **COMPLETED**
4. ✅ **Test the RLS policies** with different user types - **COMPLETED**
5. ✅ **Verify the audit system** by making test changes - **COMPLETED**
6. ✅ **Document any issues** encountered during implementation - **COMPLETED**
7. ✅ **Confirm all checklist items** before proceeding - **COMPLETED**
8. ✅ **Initialize Guide 2** when ready to continue - **READY**

**IMPLEMENTATION COMPLETED - 13/06/2025**
- Database infrastructure: 100% complete
- All verification tests: Passed
- Documentation: Complete with implementation log
- Ready for Guide 2: TypeScript Schemas and Validation Layer