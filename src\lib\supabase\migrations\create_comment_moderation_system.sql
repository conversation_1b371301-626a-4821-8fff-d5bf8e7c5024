-- Comment Moderation System Migration
-- Creates tables for content reporting and user blocking functionality
-- Date: 22/06/2025

-- Create content_flags table for reporting system
CREATE TABLE IF NOT EXISTS content_flags (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  content_id uuid NOT NULL,
  content_type text NOT NULL CHECK (content_type IN ('review', 'comment')),
  reporter_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reason text NOT NULL,
  description text,
  status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'resolved', 'dismissed')),
  resolved_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  resolved_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now()
);

-- Create user_blocks table for user banning system
CREATE TABLE IF NOT EXISTS user_blocks (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  blocker_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  blocked_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reason text,
  created_at timestamp with time zone DEFAULT now(),
  
  -- Prevent duplicate blocks
  UNIQUE(blocker_id, blocked_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_content_flags_content ON content_flags(content_id, content_type);
CREATE INDEX IF NOT EXISTS idx_content_flags_reporter ON content_flags(reporter_id);
CREATE INDEX IF NOT EXISTS idx_content_flags_status ON content_flags(status);
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocker ON user_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocked ON user_blocks(blocked_id);

-- Add flag_count columns to reviews and comments tables if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reviews' AND column_name = 'flag_count') THEN
    ALTER TABLE reviews ADD COLUMN flag_count integer DEFAULT 0;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'flag_count') THEN
    ALTER TABLE comments ADD COLUMN flag_count integer DEFAULT 0;
  END IF;
END $$;

-- Add is_deleted columns to comments table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'is_deleted') THEN
    ALTER TABLE comments ADD COLUMN is_deleted boolean DEFAULT false;
  END IF;
END $$;

-- RLS Policies for content_flags
ALTER TABLE content_flags ENABLE ROW LEVEL SECURITY;

-- Users can insert their own reports
CREATE POLICY "Users can create reports" ON content_flags
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- Users can view reports for content they own
CREATE POLICY "Users can view reports for their content" ON content_flags
  FOR SELECT USING (
    content_type = 'review' AND content_id IN (
      SELECT id FROM reviews WHERE author_id = auth.uid()
    )
    OR 
    content_type = 'comment' AND content_id IN (
      SELECT c.id FROM comments c 
      JOIN reviews r ON c.review_id = r.id 
      WHERE r.author_id = auth.uid() OR c.author_id = auth.uid()
    )
  );

-- Users can update (resolve) reports for their content
CREATE POLICY "Users can resolve reports for their content" ON content_flags
  FOR UPDATE USING (
    content_type = 'review' AND content_id IN (
      SELECT id FROM reviews WHERE author_id = auth.uid()
    )
    OR 
    content_type = 'comment' AND content_id IN (
      SELECT c.id FROM comments c 
      JOIN reviews r ON c.review_id = r.id 
      WHERE r.author_id = auth.uid()
    )
  );

-- RLS Policies for user_blocks
ALTER TABLE user_blocks ENABLE ROW LEVEL SECURITY;

-- Users can manage their own blocks
CREATE POLICY "Users can manage their blocks" ON user_blocks
  FOR ALL USING (auth.uid() = blocker_id);

-- Users can see if they are blocked by others (for filtering content)
CREATE POLICY "Users can see their blocked status" ON user_blocks
  FOR SELECT USING (auth.uid() = blocked_id);