### Search Analytics and Testing

#### Search Analytics Implementation

```typescript
// File: /src/lib/utils/searchAnalytics.ts
import { SearchState } from "@/components/search/SearchInterface";

/**
 * Interface for search analytics event data
 */
interface SearchAnalyticsEvent {
  query: string;
  type: 'all' | 'games' | 'reviews' | 'users' | 'hardware';
  filters?: SearchState['filters'];
  resultsCount?: number;
  searchTime?: number;
  fromCache?: boolean;
  resultId?: string;
  resultPosition?: number;
  refinementType?: 'filter_change' | 'query_refinement';
}

/**
 * Utility class for tracking search analytics
 */
export class SearchAnalytics {
  private analyticsEndpoint: string;
  
  constructor(analyticsEndpoint = '/api/analytics/search') {
    this.analyticsEndpoint = analyticsEndpoint;
  }
  
  /**
   * Track search query execution
   */
  public trackSearch(data: SearchAnalyticsEvent): void {
    this.sendEvent('search_executed', data);
  }
  
  /**
   * Track when a user clicks on a search result
   */
  public trackResultClick(data: Pick<SearchAnalyticsEvent, 'query' | 'type' | 'resultId' | 'resultPosition'>): void {
    this.sendEvent('result_clicked', data);
  }
  
  /**
   * Track when filters are applied or changed
   */
  public trackFilterChange(data: Pick<SearchAnalyticsEvent, 'query' | 'type' | 'filters'>): void {
    this.sendEvent('filter_applied', {
      ...data,
      refinementType: 'filter_change'
    });
  }
  
  /**
   * Track when a search is refined (query modified)
   */
  public trackQueryRefinement(data: Pick<SearchAnalyticsEvent, 'query'>, previousQuery: string): void {
    this.sendEvent('query_refined', {
      ...data,
      refinementType: 'query_refinement',
      previousQuery
    });
  }
  
  /**
   * Track search abandonment (user left search without clicking results)
   */
  public trackSearchAbandonment(data: Pick<SearchAnalyticsEvent, 'query' | 'type' | 'resultsCount'>): void {
    this.sendEvent('search_abandoned', data);
  }
  
  /**
   * Send analytics event to the backend
   */
  private sendEvent(eventType: string, data: any): void {
    // Add session ID and timestamp
    const eventData = {
      ...data,
      eventType,
      sessionId: this.getSessionId(),
      timestamp: new Date().toISOString(),
      userId: this.getUserId(),
    };
    
    // Use Navigator.sendBeacon for reliable delivery, even on page unload
    if (navigator.sendBeacon) {
      navigator.sendBeacon(this.analyticsEndpoint, JSON.stringify(eventData));
    } else {
      // Fallback to fetch for older browsers
      fetch(this.analyticsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
        // Use keepalive to ensure the request completes even if the page unloads
        keepalive: true,
      }).catch(error => {
        console.error('Error sending analytics:', error);
      });
    }
    
    // Log for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Search Analytics:', eventType, eventData);
    }
  }
  
  /**
   * Get or create session ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('search_session_id');
    
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
      sessionStorage.setItem('search_session_id', sessionId);
    }
    
    return sessionId;
  }
  
  /**
   * Get user ID if available
   */
  private getUserId(): string | null {
    // This should be replaced with your user authentication logic
    return localStorage.getItem('user_id');
  }
}
```

---

### ✅ **Testing & Verification**

#### Unit Testing Search Components

```typescript
// File: /src/components/search/__tests__/SearchInterface.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SearchInterface } from '../SearchInterface';
import { mockSearchResponse } from '../__mocks__/searchMocks';

// Mock fetch API
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve(mockSearchResponse),
  })
) as jest.Mock;

// Mock router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

describe('SearchInterface Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });
  
  it('renders search input and type selector', () => {
    render(<SearchInterface />);
    
    expect(screen.getByPlaceholderText(/search for games/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /all/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /games/i })).toBeInTheDocument();
  });
  
  it('performs search when button is clicked', async () => {
    render(<SearchInterface />);
    
    // Enter search query
    fireEvent.change(screen.getByPlaceholderText(/search for games/i), { 
      target: { value: 'zelda' } 
    });
    
    // Click search button
    fireEvent.click(screen.getByRole('button', { name: /search/i }));
    
    // Verify fetch was called with correct parameters
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/search', expect.objectContaining({
        method: 'POST',
        body: expect.stringContaining('zelda'),
      }));
    });
  });
  
  it('changes search type when tab is clicked', () => {
    render(<SearchInterface />);
    
    // Click on Reviews tab
    fireEvent.click(screen.getByRole('tab', { name: /reviews/i }));
    
    // Enter search query
    fireEvent.change(screen.getByPlaceholderText(/search for games/i), { 
      target: { value: 'final fantasy' } 
    });
    
    // Click search button
    fireEvent.click(screen.getByRole('button', { name: /search/i }));
    
    // Verify search was performed with reviews type
    expect(fetch).toHaveBeenCalledWith('/api/search', expect.objectContaining({
      body: expect.stringContaining('"type":"reviews"'),
    }));
  });
  
  it('displays search results after successful search', async () => {
    render(<SearchInterface />);
    
    // Enter search query and submit
    fireEvent.change(screen.getByPlaceholderText(/search for games/i), { 
      target: { value: 'mario' } 
    });
    fireEvent.click(screen.getByRole('button', { name: /search/i }));
    
    // Wait for results to be displayed
    await waitFor(() => {
      expect(screen.getByText(/found \d+ results/i)).toBeInTheDocument();
    });
  });
});
```

#### Integration Testing Search Components

```typescript
// This test would run in Cypress, Playwright, or a similar E2E testing framework
describe('Search System Integration', () => {
  beforeEach(() => {
    // Visit the search page
    cy.visit('/search');
    // Clear storage to ensure clean test state
    cy.clearLocalStorage();
    cy.clearCookies();
  });
  
  it('performs end-to-end search flow', () => {
    // Enter search query
    cy.get('input[placeholder*="Search for games"]').type('The Last of Us');
    cy.get('button').contains('Search').click();
    
    // Verify results appear
    cy.contains('Search Results').should('be.visible');
    cy.contains('The Last of Us').should('be.visible');
    
    // Filter results
    cy.contains('Filters').should('be.visible');
    cy.contains('Genres').click();
    cy.contains('Action').click();
    
    // Verify filtered results
    cy.get('body').should('contain.text', 'Action');
    
    // Click on a result
    cy.contains('The Last of Us').click();
    
    // Verify navigation to detail page
    cy.url().should('include', '/games/');
  });
  
  it('handles search with no results', () => {
    // Enter gibberish search query
    cy.get('input[placeholder*="Search for games"]').type('xyznonexistentgame123');
    cy.get('button').contains('Search').click();
    
    // Verify no results message
    cy.contains('No results found').should('be.visible');
  });
  
  it('saves recent searches', () => {
    // Perform a search
    cy.get('input[placeholder*="Search for games"]').type('Minecraft');
    cy.get('button').contains('Search').click();
    
    // Go back to search page
    cy.visit('/search');
    
    // Verify recent search is displayed
    cy.contains('Recent Searches').should('be.visible');
    cy.contains('Minecraft').should('be.visible');
  });
});
```

#### Manual Testing Checklist

1. **Functional Tests**
   - [ ] Search with empty query should not trigger search
   - [ ] Search with valid query returns expected results
   - [ ] Changing search type filters results appropriately
   - [ ] "Load more" button retrieves additional results
   - [ ] Score filter affects results appropriately
   - [ ] Genre, platform, and tag filters work as expected
   - [ ] Date range filter properly limits results
   - [ ] Recent searches are saved and displayed
   - [ ] Clicking on a result navigates to the correct page

2. **UI/UX Tests**
   - [ ] Search interface is responsive on mobile, tablet, and desktop
   - [ ] Loading states are displayed during searches
   - [ ] Error states display appropriate messages
   - [ ] Filter badges show when filters are active
   - [ ] Tab counting works correctly for each result type
   - [ ] Empty states are displayed when no results match
   - [ ] Keyboard navigation works for all interactive elements

3. **Performance Tests**
   - [ ] Search requests complete within acceptable time (< 2s)
   - [ ] UI remains responsive during search operations
   - [ ] Page load with many results doesn't cause layout shifts
   - [ ] Filter operations execute without noticeable delay
   - [ ] Analytics events don't impact UI performance
