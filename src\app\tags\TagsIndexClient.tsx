// src/app/tags/TagsIndexClient.tsx
// Client component for tags index page

'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Search, 
  Hash, 
  TrendingUp, 
  Star, 
  Grid, 
  List,
  Filter,
  Tag as TagIcon,
  Flame,
  Zap,
  Target
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Tag, TrendingTag, TagSuggestion } from '@/lib/services/tagService';

interface TagsIndexClientProps {
  trendingTags: TrendingTag[];
  popularTags: TrendingTag[];
  featuredTags: Tag[];
  categories: TagSuggestion[];
}

export default function TagsIndexClient({
  trendingTags,
  popularTags,
  featuredTags,
  categories
}: TagsIndexClientProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchResults, setSearchResults] = useState<Tag[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(`/api/tags/search?q=${encodeURIComponent(query)}&limit=20`);
      const data = await response.json();
      
      if (data.success) {
        setSearchResults(data.suggestions || []);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const getCategoryIcon = (category: string | null) => {
    switch (category) {
      case 'genre': return '🎮';
      case 'platform': return '💻';
      case 'feature': return '⭐';
      case 'mood': return '🎭';
      case 'difficulty': return '🎯';
      case 'length': return '⏱️';
      case 'style': return '🎨';
      default: return '🏷️';
    }
  };

  const getTrendIcon = (trendScore: number) => {
    if (trendScore > 80) return Zap;
    if (trendScore > 60) return Flame;
    if (trendScore > 40) return TrendingUp;
    return Target;
  };

  const getTrendColor = (trendScore: number) => {
    if (trendScore > 80) return 'text-cyan-400';
    if (trendScore > 60) return 'text-red-400';
    if (trendScore > 40) return 'text-yellow-400';
    return 'text-slate-400';
  };

  const TagCard = ({ tag, index, showStats = false }: { tag: Tag | TrendingTag; index: number; showStats?: boolean }) => {
    const isTrendingTag = 'trend_score' in tag;
    const TrendIcon = isTrendingTag ? getTrendIcon(tag.trend_score) : TagIcon;
    const trendColor = isTrendingTag ? getTrendColor(tag.trend_score) : 'text-slate-400';

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.05 }}
      >
        <Link
          href={`/tags/${tag.slug}`}
          className="block bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 hover:border-violet-500/50 transition-all duration-300 group"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex items-center justify-center text-sm">
                {getCategoryIcon(tag.category)}
              </div>
              <div>
                <h3 className="font-bold text-white group-hover:text-violet-300 transition-colors">
                  {tag.name}
                </h3>
                {tag.category && (
                  <Badge variant="outline" className="text-xs mt-1">
                    {tag.category}
                  </Badge>
                )}
              </div>
            </div>
            
            {isTrendingTag && (
              <TrendIcon size={16} className={`${trendColor} flex-shrink-0`} />
            )}
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-400">
              {isTrendingTag ? tag.usage_count : (tag as Tag).usage_count} uses
            </span>
            
            {showStats && isTrendingTag && (
              <div className="flex items-center gap-2 text-xs text-slate-500">
                <span>{tag.review_count} reviews</span>
                {tag.is_trending && (
                  <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-xs px-1 py-0">
                    HOT
                  </Badge>
                )}
              </div>
            )}
          </div>

          {tag.description && (
            <p className="text-slate-500 text-sm mt-2 line-clamp-2">
              {tag.description}
            </p>
          )}
        </Link>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-white mb-4">
            <Hash size={32} className="inline mr-3 text-violet-400" />
            Browse Tags
          </h1>
          <p className="text-slate-300 text-lg max-w-2xl mx-auto">
            Discover gaming reviews by tags, categories, and themes. Find exactly what you're looking for.
          </p>
        </motion.div>

        {/* Search */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-2xl mx-auto mb-12"
        >
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            <Input
              type="text"
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                handleSearch(e.target.value);
              }}
              className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder-slate-400"
            />
          </div>

          {/* Search Results */}
          {searchQuery && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 bg-slate-800/90 border border-slate-700 rounded-lg p-4"
            >
              {isSearching ? (
                <div className="text-center text-slate-400">Searching...</div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2">
                  <div className="text-sm text-slate-400 mb-3">
                    Found {searchResults.length} results
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {searchResults.map((tag, index) => (
                      <TagCard key={tag.id} tag={tag} index={index} />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center text-slate-400">
                  No tags found for "{searchQuery}"
                </div>
              )}
            </motion.div>
          )}
        </motion.div>

        {/* Featured Tags */}
        {featuredTags.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-12"
          >
            <div className="flex items-center gap-3 mb-6">
              <Star size={24} className="text-yellow-400" />
              <h2 className="text-2xl font-bold text-white">Featured Tags</h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {featuredTags.map((tag, index) => (
                <TagCard key={tag.id} tag={tag} index={index} />
              ))}
            </div>
          </motion.section>
        )}

        {/* Trending Tags */}
        {trendingTags.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-12"
          >
            <div className="flex items-center gap-3 mb-6">
              <TrendingUp size={24} className="text-orange-400" />
              <h2 className="text-2xl font-bold text-white">Trending Now</h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {trendingTags.map((tag, index) => (
                <TagCard key={tag.id} tag={tag} index={index} showStats />
              ))}
            </div>
          </motion.section>
        )}

        {/* Categories */}
        {categories.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-12"
          >
            <div className="flex items-center gap-3 mb-6">
              <Grid size={24} className="text-violet-400" />
              <h2 className="text-2xl font-bold text-white">Browse by Category</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category, index) => (
                <motion.div
                  key={category.category || 'uncategorized'}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.05 * index }}
                  className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-6"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex items-center justify-center text-lg">
                      {getCategoryIcon(category.category)}
                    </div>
                    <h3 className="text-lg font-bold text-white capitalize">
                      {category.category || 'Other'}
                    </h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {category.suggested_tags.slice(0, 8).map((tagName) => (
                      <Link
                        key={tagName}
                        href={`/tags/${tagName.toLowerCase().replace(/\s+/g, '-')}`}
                        className="inline-block"
                      >
                        <Badge
                          variant="outline"
                          className="text-xs hover:bg-violet-500/20 hover:border-violet-500/50 transition-colors cursor-pointer"
                        >
                          {tagName}
                        </Badge>
                      </Link>
                    ))}
                    {category.suggested_tags.length > 8 && (
                      <Badge variant="outline" className="text-xs text-slate-500">
                        +{category.suggested_tags.length - 8} more
                      </Badge>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.section>
        )}

        {/* Popular Tags */}
        {popularTags.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <div className="flex items-center gap-3 mb-6">
              <Flame size={24} className="text-red-400" />
              <h2 className="text-2xl font-bold text-white">Most Popular</h2>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3">
              {popularTags.map((tag, index) => (
                <Link
                  key={tag.id}
                  href={`/tags/${tag.slug}`}
                  className="block"
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.02 * index }}
                    className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-3 text-center hover:border-violet-500/50 transition-all duration-300 group"
                  >
                    <div className="text-2xl mb-2">
                      {getCategoryIcon(tag.category)}
                    </div>
                    <div className="text-sm font-medium text-white group-hover:text-violet-300 transition-colors">
                      {tag.name}
                    </div>
                    <div className="text-xs text-slate-500 mt-1">
                      {tag.usage_count} uses
                    </div>
                  </motion.div>
                </Link>
              ))}
            </div>
          </motion.section>
        )}
      </div>
    </div>
  );
}