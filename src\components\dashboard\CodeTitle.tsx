"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

// Background brightness detection hook
export const useBackgroundBrightness = () => {
  const [isDarkBackground, setIsDarkBackground] = useState(true);

  useEffect(() => {
    const detectBackgroundBrightness = () => {
      try {
        // Check multiple elements to get the most accurate background
        const elements = [
          document.body,
          document.documentElement,
          document.querySelector('.page-main-container'),
          document.querySelector('main')
        ].filter(Boolean);

        for (const element of elements) {
          const computedStyle = window.getComputedStyle(element as Element);
          const backgroundColor = computedStyle.backgroundColor;

          // Skip transparent backgrounds
          if (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)') {
            continue;
          }

          // Parse RGB values from background color
          const rgbMatch = backgroundColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
          if (rgbMatch) {
            const [, r, g, b] = rgbMatch.map(Number);
            // Calculate luminance using standard formula
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
            setIsDarkBackground(luminance < 0.5);
            return; // Found a valid background, stop checking
          }
        }

        // Default to dark if we can't detect any background
        setIsDarkBackground(true);
      } catch (error) {
        console.warn('Background brightness detection failed:', error);
        setIsDarkBackground(true);
      }
    };

    // Initial detection
    detectBackgroundBrightness();

    // Create observer for background changes
    const observer = new MutationObserver(detectBackgroundBrightness);
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true
    });

    // Also listen for theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', detectBackgroundBrightness);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', detectBackgroundBrightness);
    };
  }, []);

  return isDarkBackground;
};

// DASHBOARD REDESIGN: Phase 2 - Component Styling
// Date: 15/06/2025  
// Task: dashboardStyleAdmin002
//
// Created CodeTitle component with code-themed design patterns:
// - Implemented bracket styling < /> for code theme
// - Added animated glow effects on hover
// - Applied consistent Geist Mono typography
// - Created smooth color transitions
// - Supports different sizes and variants

interface CodeTitleProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'muted' | 'primary';
  glow?: boolean;
  animated?: boolean;
  adaptive?: boolean; // Enable adaptive background-aware styling
}

export function CodeTitle({
  children,
  className,
  size = 'md',
  variant = 'default',
  glow = true,
  animated = true,
  adaptive = false
}: CodeTitleProps) {
  const isDarkBackground = useBackgroundBrightness();
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const variantClasses = {
    default: adaptive ? `adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}` : 'text-slate-100',
    muted: adaptive ? `adaptive-text-subtitle ${isDarkBackground ? 'dark-background' : 'light-background'}` : 'text-slate-400',
    primary: adaptive ? `adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}` : 'text-violet-300'
  };

  const bracketColor = {
    default: 'text-violet-400/60',
    muted: 'text-slate-500/60',
    primary: 'text-violet-300/70'
  };

  return (
    <span className={cn(
      "font-mono inline-block relative group",
      sizeClasses[size],
      variantClasses[variant],
      animated && "transition-all duration-300",
      className
    )}>
      {/* Opening bracket */}
      <span className={cn(
        bracketColor[variant],
        animated && "transition-colors duration-300 group-hover:text-violet-300"
      )}>
        &lt;
      </span>
      
      {/* Content with glow effect */}
      <span className="px-1 relative">
        {children}
        
        {/* Animated glow overlay */}
        {glow && (
          <span 
            className={cn(
              "absolute inset-0 bg-gradient-to-r from-violet-400/0 via-violet-400/30 to-violet-400/0",
              "opacity-0 group-hover:opacity-100 transition-opacity duration-700",
              animated && "animate-pulse"
            )} 
          />
        )}
      </span>
      
      {/* Closing bracket */}
      <span className={cn(
        bracketColor[variant],
        animated && "transition-colors duration-300 group-hover:text-violet-300"
      )}>
        /&gt;
      </span>
    </span>
  );
}

// Additional utility component for sectioned code titles
export function SectionCodeTitle({
  section,
  title,
  className
}: {
  section: string;
  title: string;
  className?: string;
}) {
  return (
    <div className={cn("flex items-center gap-2 font-mono", className)}>
      <span className="text-cyan-400/60 text-sm">&gt;</span>
      <span className="text-slate-400 text-sm">{section}.</span>
      <CodeTitle variant="primary" size="lg">
        {title}
      </CodeTitle>
    </div>
  );
}

// Utility for status/badge code styling
export function CodeBadge({
  children,
  status = 'active',
  className
}: {
  children: React.ReactNode;
  status?: 'active' | 'inactive' | 'pending';
  className?: string;
}) {
  const statusColors = {
    active: 'bg-violet-500/10 text-violet-300 border-violet-500/20',
    inactive: 'bg-slate-500/10 text-slate-400 border-slate-500/20',
    pending: 'bg-orange-500/10 text-orange-300 border-orange-500/20'
  };

  return (
    <span className={cn(
      "px-2 py-1 text-xs font-mono rounded-sm border",
      "transition-all duration-200 hover:scale-105",
      statusColors[status],
      className
    )}>
      {children}
    </span>
  );
} 