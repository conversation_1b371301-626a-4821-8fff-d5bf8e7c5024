// src/components/ui/UpgradePrompt.tsx
'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ThemeOption } from '@/lib/types';
import { Sparkles, Star, Zap, Palette, Gamepad2, Crown } from 'lucide-react';

interface UpgradePromptProps {
  open: boolean;
  onClose: () => void;
  selectedTheme?: ThemeOption;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ 
  open, 
  onClose,
  selectedTheme 
}) => {
  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-md bg-gray-950 border-gray-800 text-gray-100">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
              Upgrade to Supporter
            </span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-6">
          {/* Theme Preview */}
          {selectedTheme && (
            <div className="mb-6 rounded-lg overflow-hidden border border-gray-800">
              <div className={`h-24 w-full ${selectedTheme.preview}`}></div>
              <div className="p-3 bg-gray-900">
                <div className="font-medium">{selectedTheme.name}</div>
                <div className="text-sm text-gray-400">{selectedTheme.description}</div>
              </div>
            </div>
          )}
          
          <div className="text-center mb-6">
            <p className="text-lg text-gray-300">
              Unlock premium themes and more with Supporter status!
            </p>
          </div>
          
          {/* Benefits */}
          <div className="space-y-3 mb-6">
            <BenefitItem 
              icon={<Palette className="h-5 w-5 text-indigo-400" />}
              title="Premium Themes"
              description="Access all exclusive profile themes"
            />
            
            <BenefitItem 
              icon={<Crown className="h-5 w-5 text-amber-400" />}
              title="Supporter Badge"
              description="Show off your support with a special badge"
            />
            
            <BenefitItem 
              icon={<Gamepad2 className="h-5 w-5 text-green-400" />}
              title="Early Features"
              description="Get early access to new platform features"
            />
            
            <BenefitItem 
              icon={<Star className="h-5 w-5 text-yellow-400" />}
              title="Featured Profiles"
              description="Your profile may be featured on our homepage"
            />
          </div>
        </div>
        
        <DialogFooter className="flex flex-col sm:flex-row gap-3">
          <Button 
            variant="outline" 
            onClick={onClose}
            className="w-full sm:w-auto border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            Maybe Later
          </Button>
          
          <Button 
            className="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Become a Supporter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Individual benefit item
const BenefitItem: React.FC<{
  icon: React.ReactNode;
  title: string;
  description: string;
}> = ({ icon, title, description }) => {
  return (
    <div className="flex items-center gap-3 bg-gray-900/50 p-3 rounded-lg border border-gray-800">
      <div className="shrink-0">
        {icon}
      </div>
      <div>
        <div className="font-medium">{title}</div>
        <div className="text-sm text-gray-400">{description}</div>
      </div>
    </div>
  );
};

export default UpgradePrompt;