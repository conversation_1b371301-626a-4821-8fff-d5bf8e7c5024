'use client';

import { useState, useEffect } from 'react';

/**
 * Hook that monitors background brightness changes and determines optimal text color
 * Supports both dimmer slider overlays and computed background color detection
 */
export const useBackgroundBrightness = () => {
  const [isDarkBackground, setIsDarkBackground] = useState(true);

  useEffect(() => {
    const detectBackgroundBrightness = () => {
      try {
        // Priority 1: Check dimmer overlay values from CSS custom properties
        const rootStyles = getComputedStyle(document.documentElement);
        const darkOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-dark-opacity') || '0');
        const lightOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-light-opacity') || '0');

        // If we have dimmer overlays, determine brightness based on those
        if (darkOpacity > 0) {
          // Dark overlay is active, background is being darkened
          setIsDarkBackground(true);
          return;
        } else if (lightOpacity > 0.3) {
          // Light overlay is significant, background is being lightened
          setIsDarkBackground(false);
          return;
        }

        // Priority 2: Fallback to computed background color detection
        const elements = [
          document.body,
          document.documentElement,
          document.querySelector('.page-main-container'),
          document.querySelector('main')
        ].filter(Boolean);

        for (const element of elements) {
          const computedStyle = window.getComputedStyle(element as Element);
          const backgroundColor = computedStyle.backgroundColor;

          // Skip transparent backgrounds
          if (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)') {
            continue;
          }

          // Parse RGB values from background color
          const rgbMatch = backgroundColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
          if (rgbMatch) {
            const [, r, g, b] = rgbMatch.map(Number);
            // Calculate luminance using standard formula
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
            setIsDarkBackground(luminance < 0.5);
            return; // Found a valid background, stop checking
          }
        }

        // Default to dark if we can't detect any background
        setIsDarkBackground(true);
      } catch (error) {
        console.warn('Background brightness detection failed:', error);
        setIsDarkBackground(true);
      }
    };

    // Initial detection
    detectBackgroundBrightness();

    // Create observer for CSS custom property changes
    const observer = new MutationObserver(detectBackgroundBrightness);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style'],
      subtree: false
    });

    // Also observe body for background changes
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true
    });

    // Listen for theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', detectBackgroundBrightness);

    // Poll for CSS custom property changes (backup for slider changes)
    const interval = setInterval(detectBackgroundBrightness, 100);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', detectBackgroundBrightness);
      clearInterval(interval);
    };
  }, []);

  return isDarkBackground;
};