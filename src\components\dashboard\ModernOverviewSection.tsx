"use client";

import React, { useMemo, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  TrendingUp,
  FileText,
  Gauge,
  Award,
  Plus,
  ExternalLink,
  Star,
  Settings
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import type { DashboardStats } from "@/types/dashboard";
import { DashboardCard } from "./DashboardCard";
import { CodeTitle, SectionCodeTitle, CodeBadge } from "./CodeTitle";

// DASHBOARD REDESIGN: Phase 2 - Component Styling
// Date: 15/06/2025  
// Task: dashboardStyleAdmin002
//
// Updated ModernOverviewSection with admin-style design patterns:
// - Replaced card components with new DashboardCard component
// - Applied CodeTitle components for consistent typography
// - Implemented enhanced stat cards with glassmorphism
// - Added gaming-themed visual effects and animations
// - Applied purple cosmic theme throughout

interface ModernOverviewSectionProps {
  user: {
    uid: string;
    displayName?: string;
    userName?: string;
    slug?: string;
    createdAt?: any;
    photoURL?: string | null;
  };
  reviews: any[];
  surveys: any[];
  stats: DashboardStats;
  isLoading: boolean;
  onEditProfile?: () => void;
}

interface StatCard {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: string;
  colSpan?: number;
}


export function ModernOverviewSection({
  user,
  reviews,
  surveys,
  stats,
  isLoading,
  onEditProfile
}: ModernOverviewSectionProps) {
  
  // Calculate user insights
  const insights = useMemo(() => {
    const isNewUser = stats.totalReviews === 0 && stats.totalSurveys === 0;
    const hasRecentActivity = stats.lastActivity && 
      new Date().getTime() - new Date(stats.lastActivity).getTime() < 7 * 24 * 60 * 60 * 1000;

    let userLevel = 'Newcomer';
    let levelProgress = 0;
    
    if (stats.totalReviews >= 20 || stats.totalSurveys >= 10) {
      userLevel = 'Expert Reviewer';
      levelProgress = 100;
    } else if (stats.totalReviews >= 10 || stats.totalSurveys >= 5) {
      userLevel = 'Active Contributor';
      levelProgress = 75;
    } else if (stats.totalReviews >= 3 || stats.totalSurveys >= 2) {
      userLevel = 'Rising Reviewer';
      levelProgress = 50;
    } else if (stats.totalReviews >= 1 || stats.totalSurveys >= 1) {
      userLevel = 'Getting Started';
      levelProgress = 25;
    }

    return {
      isNewUser,
      hasRecentActivity,
      userLevel,
      levelProgress
    };
  }, [stats]);

  // Stat cards configuration
  const statCards: StatCard[] = [
    {
      title: "Total Reviews",
      value: stats.totalReviews,
      subtitle: `${stats.publishedReviews} published`,
      icon: <FileText className="w-5 h-5" />,
      color: "text-purple-400",
      colSpan: 1
    },
    {
      title: "Performance Surveys",
      value: stats.totalSurveys,
      subtitle: "Hardware data points",
      icon: <Gauge className="w-5 h-5" />,
      color: "text-green-400",
      colSpan: 1
    },
    {
      title: "Average Score",
      value: stats.averageScore > 0 ? stats.averageScore.toFixed(1) : "—",
      subtitle: stats.totalReviews > 0 ? "Out of 10" : "No reviews yet",
      icon: <Star className="w-5 h-5" />,
      color: "text-purple-400",
      colSpan: 1
    }
  ];


  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-slate-900/40 rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
          <div>
            <div className="mb-2">
              <CodeTitle size="xl" variant="primary" glow animated>
                {insights.isNewUser ? "Welcome" : "Dashboard Overview"}
              </CodeTitle>
            </div>
            <p className="text-slate-400 text-sm font-mono">
              {insights.isNewUser
                ? "Ready to start your gaming journey?"
                : "Here's your content overview"
              }
            </p>
          </div>

          <div className="flex gap-3">
            <Button asChild className="bg-violet-600 hover:bg-violet-700 font-mono transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-violet-500/25">
              <Link href="/reviews/create">
                <Plus className="w-4 h-4 mr-2" />
                <span>Create Review</span>
              </Link>
            </Button>
            <Button variant="outline" asChild className="font-mono border-violet-500/30 hover:border-violet-400/50 hover:bg-violet-500/10 transition-all duration-300">
              <Link href={`/u/${user?.slug || ''}`}>
                <ExternalLink className="w-4 h-4 mr-2" />
                <span>View Profile</span>
              </Link>
            </Button>
            {onEditProfile && (
              <Button 
                variant="outline" 
                onClick={onEditProfile}
                className="font-mono border-orange-500/30 hover:border-orange-400/50 hover:bg-orange-500/10 transition-all duration-300"
              >
                <Settings className="w-4 h-4 mr-2" />
                <span>Edit Profile</span>
              </Button>
            )}
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((card, index) => (
          <DashboardCard
            key={card.title}
            icon={card.icon}
            className={cn(
              "dashboard-card transition-all duration-500 ease-out",
              card.colSpan === 2 && "md:col-span-2"
            )}
            delay={index * 0.1}
            animated={true}
            hoverable={true}
          >
            <div className="space-y-4">
              {/* Enhanced header with code styling */}
              <div className="flex items-center justify-between">
                <div className="text-xs font-semibold text-slate-400 uppercase tracking-wider font-mono">
                  {card.title}
                </div>
                {card.trend && (
                  <div className={cn(
                    "flex items-center gap-1 text-xs font-mono px-2 py-1 rounded-sm",
                    card.trend.isPositive 
                      ? "text-emerald-400 bg-emerald-400/10" 
                      : "text-red-400 bg-red-400/10"
                  )}>
                    <TrendingUp className={cn(
                      "w-3 h-3",
                      !card.trend.isPositive && "rotate-180"
                    )} />
                    {Math.abs(card.trend.value)}%
                  </div>
                )}
              </div>

              {/* Enhanced value display */}
              <div className="space-y-2">
                <div className="text-2xl font-bold text-white font-mono tabular-nums">
                  {card.value}
                </div>
                
                {card.subtitle && (
                  <p className="text-sm text-slate-400 font-mono">
                    {card.subtitle}
                  </p>
                )}


              </div>
            </div>
          </DashboardCard>
        ))}
      </div>

    </div>
  );
}
