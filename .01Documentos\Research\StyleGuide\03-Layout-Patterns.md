# CriticalPixel Layout Patterns

## 🏗️ Layout Architecture

CriticalPixel follows a **consistent, responsive layout system** that adapts seamlessly across devices while maintaining the gaming aesthetic.

### Core Layout Structure
```tsx
<html className="dark">
  <body className="bg-gradient-to-br from-[#1a202c] to-[#35363A]">
    <div className="min-h-screen">
      <Navbar />                    {/* Fixed header */}
      <main className="py-8 pt-24"> {/* Main content with navbar offset */}
        {children}
      </main>
      <Footer />                    {/* Site footer */}
    </div>
    <Toaster />                     {/* Global notifications */}
  </body>
</html>
```

## 📐 Container System

### Container Widths
```css
/* Responsive container system */
.container-full {
  width: 100%;
  max-width: none;
}

.container-wide {
  width: 100%;
  max-width: 1536px;  /* 2xl breakpoint */
  margin: 0 auto;
}

.container-standard {
  width: 100%;
  max-width: 1280px;  /* xl breakpoint */
  margin: 0 auto;
}

.container-narrow {
  width: 100%;
  max-width: 1024px;  /* lg breakpoint */
  margin: 0 auto;
}

.container-content {
  width: 100%;
  max-width: 768px;   /* md breakpoint */
  margin: 0 auto;
}
```

### Responsive Container Usage
```tsx
{/* Homepage - wide layout */}
<div className="container-wide px-4 sm:px-6 lg:px-8">

{/* Content pages - standard layout */}
<div className="container-standard px-4 sm:px-6 lg:px-8">

{/* Article/blog content - narrow layout */}
<div className="container-content px-4 sm:px-6">
```

## 🎯 Page Layout Patterns

### Homepage Layout
```tsx
<main className="py-8 pt-24">
  {/* Hero Section */}
  <section className="container-wide px-4 mb-12">
    <HeroCard />
  </section>
  
  {/* Featured Reviews Grid */}
  <section className="container-wide px-4 mb-12">
    <SectionHeader title="Featured Reviews" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {featuredReviews.map(review => <ReviewCard key={review.id} {...review} />)}
    </div>
  </section>
  
  {/* Latest Articles */}
  <section className="container-wide px-4 mb-12">
    <SectionHeader title="Latest Articles" />
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      {articles.map(article => <ArticleCard key={article.id} {...article} />)}
    </div>
  </section>
</main>
```

### Content Page Layout
```tsx
<main className="py-8 pt-24">
  <div className="container-standard px-4 sm:px-6 lg:px-8">
    {/* Breadcrumb Navigation */}
    <nav className="mb-6">
      <Breadcrumb />
    </nav>
    
    {/* Page Header */}
    <header className="mb-8">
      <PageHeader />
    </header>
    
    {/* Main Content Grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Primary Content */}
      <div className="lg:col-span-2">
        <ContentArea />
      </div>
      
      {/* Sidebar */}
      <aside className="lg:col-span-1">
        <Sidebar />
      </aside>
    </div>
  </div>
</main>
```

### Dashboard Layout
```tsx
<main className="py-8 pt-24">
  <div className="container-wide px-4 sm:px-6 lg:px-8">
    {/* Dashboard Header */}
    <header className="mb-8">
      <DashboardHeader />
    </header>
    
    {/* Tab Navigation */}
    <nav className="mb-6">
      <TabNavigation />
    </nav>
    
    {/* Dashboard Content */}
    <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
      {/* Main Dashboard Area */}
      <div className="xl:col-span-3">
        <DashboardContent />
      </div>
      
      {/* Dashboard Sidebar */}
      <aside className="xl:col-span-1">
        <DashboardSidebar />
      </aside>
    </div>
  </div>
</main>
```

## 📱 Responsive Grid System

### Standard Grid Patterns
```css
/* Review/Article Cards Grid */
.grid-reviews {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid-reviews {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-reviews {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1536px) {
  .grid-reviews {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### Dashboard Grid
```css
/* Dashboard metrics grid */
.grid-dashboard {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .grid-dashboard {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-dashboard {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .grid-dashboard {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

## 🎨 Section Spacing

### Vertical Rhythm
```css
/* Section spacing system */
.section-spacing {
  margin-bottom: 3rem;  /* 48px */
}

@media (min-width: 768px) {
  .section-spacing {
    margin-bottom: 4rem;  /* 64px */
  }
}

@media (min-width: 1024px) {
  .section-spacing {
    margin-bottom: 5rem;  /* 80px */
  }
}
```

### Content Spacing
```css
/* Content element spacing */
.content-spacing > * + * {
  margin-top: 1.5rem;  /* 24px */
}

.content-spacing h2 {
  margin-top: 2.5rem;  /* 40px */
  margin-bottom: 1rem; /* 16px */
}

.content-spacing h3 {
  margin-top: 2rem;    /* 32px */
  margin-bottom: 0.75rem; /* 12px */
}
```

## 🏠 Header & Navigation Layout

### Fixed Navbar
```css
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  height: 4rem;  /* 64px */
  background: rgba(3, 7, 18, 0.8);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

/* Main content offset for fixed navbar */
.main-content {
  padding-top: 6rem;  /* 96px - navbar height + extra space */
}
```

### Navigation Menu Layout
```tsx
<nav className="flex items-center justify-between h-16 px-4">
  {/* Logo */}
  <div className="flex-shrink-0">
    <Logo />
  </div>
  
  {/* Desktop Navigation */}
  <div className="hidden md:flex items-center space-x-8">
    <NavLinks />
  </div>
  
  {/* User Menu */}
  <div className="flex items-center space-x-4">
    <UserMenu />
  </div>
  
  {/* Mobile Menu Button */}
  <div className="md:hidden">
    <MobileMenuButton />
  </div>
</nav>
```

## 🦶 Footer Layout

### Footer Structure
```tsx
<footer className="footer-container">
  {/* Footer Ad Space */}
  <section className="footer-ad-section">
    <FooterAd />
  </section>
  
  {/* Main Footer Content */}
  <div className="footer-content">
    {/* Top Row - Navigation & Newsletter */}
    <div className="footer-top-row">
      <FooterNavigation />
      <NewsletterSignup />
    </div>
    
    {/* Heat Index Section */}
    <div className="footer-heat-index">
      <Top20HeatIndex />
    </div>
    
    {/* Bottom Row - Copyright & Social */}
    <div className="footer-bottom-row">
      <CopyrightInfo />
      <SocialLinks />
    </div>
  </div>
</footer>
```

### Footer Responsive Layout
```css
.footer-content {
  width: 100%;
  max-width: 1360px;  /* Custom breakpoint */
  margin: 0 auto;
  padding: 3rem 1rem;
}

@media (min-width: 1360px) {
  .footer-content {
    width: 80%;
  }
}
```

## 📋 Form Layout Patterns

### Single Column Form
```tsx
<form className="form-container">
  <div className="form-section">
    <h2 className="form-section-title">Basic Information</h2>
    <div className="form-grid-single">
      <FormField />
      <FormField />
      <FormField />
    </div>
  </div>
  
  <div className="form-actions">
    <Button variant="outline">Cancel</Button>
    <Button variant="gradient">Save</Button>
  </div>
</form>
```

### Two Column Form
```tsx
<form className="form-container">
  <div className="form-section">
    <h2 className="form-section-title">Game Details</h2>
    <div className="form-grid-two">
      <FormField />
      <FormField />
      <FormField className="col-span-2" />
    </div>
  </div>
</form>
```

### Form Grid Classes
```css
.form-grid-single {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.form-grid-two {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .form-grid-two {
    grid-template-columns: repeat(2, 1fr);
  }
}

.col-span-2 {
  grid-column: span 2;
}
```

## 🎯 Modal & Overlay Layout

### Modal Positioning
```css
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.modal-content {
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

@media (min-width: 768px) {
  .modal-content {
    max-width: 600px;
  }
}
```

### Sidebar Layout
```css
.sidebar-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  max-width: 400px;
  z-index: 100;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.sidebar-overlay.open {
  transform: translateX(0);
}
```

## 📊 Data Layout Patterns

### Stats Grid
```tsx
<div className="stats-grid">
  <StatCard title="Total Reviews" value="1,234" />
  <StatCard title="Average Score" value="8.2" />
  <StatCard title="Active Users" value="5,678" />
  <StatCard title="Games Reviewed" value="890" />
</div>
```

```css
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}
```

---

*These layout patterns provide the structural foundation for all CriticalPixel pages, ensuring consistent spacing, responsive behavior, and optimal user experience across devices.*
