import { createClient } from '@/lib/supabase/client';

interface PrivacySettings {
  profile_visibility: 'public' | 'private';
  show_reviews: 'public' | 'private';
  show_activity: 'public' | 'private';
  show_favorites: 'public' | 'private';
  show_performance_data: 'public' | 'private';
  show_gaming_profiles: 'public' | 'private';
  allow_friend_requests: boolean;
  show_online_status: boolean;
}

/**
 * Get user privacy settings from the profiles table
 */
export async function getUserPrivacySettings(userId: string): Promise<{
  success: boolean;
  data?: PrivacySettings;
  error?: string;
}> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('privacy_settings')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching privacy settings:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Default privacy settings if none exist
    const defaultSettings: PrivacySettings = {
      profile_visibility: 'public',
      show_reviews: 'public',
      show_activity: 'public',
      show_favorites: 'public',
      show_performance_data: 'public',
      show_gaming_profiles: 'public',
      allow_friend_requests: true,
      show_online_status: true
    };

    // Merge existing settings with defaults
    const privacySettings = data?.privacy_settings 
      ? { ...defaultSettings, ...data.privacy_settings }
      : defaultSettings;

    return {
      success: true,
      data: privacySettings
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update user privacy settings in the profiles table
 */
export async function updateUserPrivacySettings(
  userId: string, 
  settings: PrivacySettings
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('profiles')
      .update({ 
        privacy_settings: settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating privacy settings:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get privacy settings for content visibility checks
 */
export async function getContentVisibilitySettings(userId: string): Promise<{
  success: boolean;
  data?: {
    show_reviews: string;
    show_activity: string;
    show_favorites: string;
    show_performance_data: string;
    profile_visibility: string;
  };
  error?: string;
}> {
  try {
    const result = await getUserPrivacySettings(userId);
    
    if (!result.success || !result.data) {
      return result;
    }

    return {
      success: true,
      data: {
        show_reviews: result.data.show_reviews,
        show_activity: result.data.show_activity,
        show_favorites: result.data.show_favorites,
        show_performance_data: result.data.show_performance_data,
        profile_visibility: result.data.profile_visibility
      }
    };
  } catch (error) {
    console.error('Error getting content visibility settings:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Check if user content should be visible to viewer based on privacy settings
 */
export async function checkContentVisibility(
  contentOwnerId: string,
  viewerId: string | null,
  contentType: 'reviews' | 'activity' | 'favorites' | 'performance_data' | 'profile'
): Promise<{
  visible: boolean;
  reason?: string;
}> {
  try {
    // If viewing own content, always visible
    if (contentOwnerId === viewerId) {
      return { visible: true };
    }

    const settings = await getUserPrivacySettings(contentOwnerId);
    
    if (!settings.success || !settings.data) {
      // Default to private if we can't get settings
      return { 
        visible: false, 
        reason: 'Unable to load privacy settings' 
      };
    }

    const privacyLevel = (() => {
      switch (contentType) {
        case 'reviews': return settings.data.show_reviews;
        case 'activity': return settings.data.show_activity;
        case 'favorites': return settings.data.show_favorites;
        case 'performance_data': return settings.data.show_performance_data;
        case 'profile': return settings.data.profile_visibility;
        default: return 'private';
      }
    })();

    // Check visibility based on privacy level
    switch (privacyLevel) {
      case 'public':
        return { visible: true };
      
      case 'friends':
        if (!viewerId) {
          return { 
            visible: false, 
            reason: 'Content is only visible to friends' 
          };
        }
        
        // TODO: Implement friend relationship check
        // For now, default to not visible for friends-only content
        return { 
          visible: false, 
          reason: 'Friend relationship check not implemented' 
        };
      
      case 'private':
      default:
        return { 
          visible: false, 
          reason: 'Content is private' 
        };
    }
  } catch (error) {
    console.error('Error checking content visibility:', error);
    return { 
      visible: false, 
      reason: 'Error checking permissions' 
    };
  }
}

/**
 * Bulk update review privacy based on user privacy preferences
 */
export async function bulkUpdateReviewsPrivacy(
  userId: string, 
  isPrivate: boolean
): Promise<{
  success: boolean;
  updatedCount?: number;
  error?: string;
}> {
  try {
    const supabase = createClient();
    
    const newStatus = isPrivate ? 'draft' : 'published';
    
    const { data, error } = await supabase
      .from('reviews')
      .update({ 
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select('id');

    if (error) {
      console.error('Error updating reviews privacy:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      updatedCount: data?.length || 0
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}