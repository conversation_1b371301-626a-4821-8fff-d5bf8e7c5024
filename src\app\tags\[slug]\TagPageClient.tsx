// src/app/tags/[slug]/TagPageClient.tsx
// Client component for tag page

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Tag, 
  Hash, 
  TrendingUp, 
  Calendar, 
  Star, 
  Eye, 
  Heart, 
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  Filter,
  Grid,
  List,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Tag as TagType } from '@/lib/services/tagService';

interface Review {
  id: string;
  title: string;
  slug: string;
  game_name: string;
  author_name: string;
  overall_score: number;
  main_image_url?: string;
  created_at: string;
  view_count?: number;
  like_count?: number;
}

interface TagPageClientProps {
  tag: TagType;
  reviews: Review[];
  total: number;
  currentPage: number;
  totalPages: number;
  error?: string;
}

export default function TagPageClient({
  tag,
  reviews,
  total,
  currentPage,
  totalPages,
  error
}: TagPageClientProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [isLoading, setIsLoading] = useState(false);

  // Track tag view
  useEffect(() => {
    const trackView = async () => {
      try {
        await fetch('/api/tags/analytics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'view',
            tag_id: tag.id,
          }),
        });
      } catch (error) {
        // Silently fail analytics tracking
        console.error('Failed to track tag view:', error);
      }
    };

    trackView();
  }, [tag.id]);

  const getCategoryIcon = (category: string | null) => {
    switch (category) {
      case 'genre': return '🎮';
      case 'platform': return '💻';
      case 'feature': return '⭐';
      case 'mood': return '🎭';
      case 'difficulty': return '🎯';
      case 'length': return '⏱️';
      case 'style': return '🎨';
      default: return '🏷️';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-red-400';
  };

  const ReviewCard = ({ review, index }: { review: Review; index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-slate-800/50 border border-slate-700/50 rounded-lg overflow-hidden hover:border-violet-500/50 transition-all duration-300 group"
    >
      <Link href={`/reviews/view/${review.slug}`} className="block">
        {/* Image */}
        <div className="relative h-48 bg-slate-700/50 overflow-hidden">
          {review.main_image_url ? (
            <img
              src={review.main_image_url}
              alt={review.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-4xl opacity-50">🎮</div>
            </div>
          )}
          
          {/* Score overlay */}
          <div className="absolute top-3 right-3">
            <div className="bg-slate-900/80 backdrop-blur-sm rounded-lg px-3 py-1 flex items-center gap-1">
              <Star size={14} className={getScoreColor(review.overall_score)} />
              <span className={`font-bold ${getScoreColor(review.overall_score)}`}>
                {review.overall_score}
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="space-y-2">
            <h3 className="font-bold text-white group-hover:text-violet-300 transition-colors line-clamp-2">
              {review.title}
            </h3>
            
            <p className="text-slate-400 text-sm">
              Review of <span className="text-violet-300">{review.game_name}</span>
            </p>
            
            <div className="flex items-center justify-between text-xs text-slate-500">
              <span>by {review.author_name}</span>
              <span>{formatDate(review.created_at)}</span>
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-xs text-slate-500 pt-2">
              {review.view_count && (
                <div className="flex items-center gap-1">
                  <Eye size={12} />
                  <span>{review.view_count}</span>
                </div>
              )}
              {review.like_count && (
                <div className="flex items-center gap-1">
                  <Heart size={12} />
                  <span>{review.like_count}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );

  const ReviewListItem = ({ review, index }: { review: Review; index: number }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.05 }}
      className="bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 hover:border-violet-500/50 transition-all duration-300"
    >
      <Link href={`/reviews/view/${review.slug}`} className="block">
        <div className="flex gap-4">
          {/* Image */}
          <div className="w-20 h-20 bg-slate-700/50 rounded-lg overflow-hidden flex-shrink-0">
            {review.main_image_url ? (
              <img
                src={review.main_image_url}
                alt={review.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-2xl opacity-50">
                🎮
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-white hover:text-violet-300 transition-colors line-clamp-1">
                  {review.title}
                </h3>
                <p className="text-slate-400 text-sm mt-1">
                  Review of <span className="text-violet-300">{review.game_name}</span>
                </p>
                <div className="flex items-center gap-4 text-xs text-slate-500 mt-2">
                  <span>by {review.author_name}</span>
                  <span>{formatDate(review.created_at)}</span>
                </div>
              </div>

              {/* Score */}
              <div className="flex items-center gap-1 flex-shrink-0">
                <Star size={16} className={getScoreColor(review.overall_score)} />
                <span className={`font-bold ${getScoreColor(review.overall_score)}`}>
                  {review.overall_score}
                </span>
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-xs text-slate-500 mt-3">
              {review.view_count && (
                <div className="flex items-center gap-1">
                  <Eye size={12} />
                  <span>{review.view_count}</span>
                </div>
              )}
              {review.like_count && (
                <div className="flex items-center gap-1">
                  <Heart size={12} />
                  <span>{review.like_count}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-slate-400 mb-4">
            <Link href="/" className="hover:text-violet-300 transition-colors">
              Home
            </Link>
            <ChevronRight size={14} />
            <Link href="/tags" className="hover:text-violet-300 transition-colors">
              Tags
            </Link>
            <ChevronRight size={14} />
            <span className="text-white">{tag.name}</span>
          </div>

          {/* Tag Header */}
          <div className="flex items-start justify-between gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl flex items-center justify-center text-2xl">
                  {getCategoryIcon(tag.category)}
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-white">
                    <Hash size={24} className="inline mr-2 text-violet-400" />
                    {tag.name}
                  </h1>
                  <div className="flex items-center gap-3 mt-2">
                    {tag.category && (
                      <Badge variant="outline" className="text-xs">
                        {tag.category}
                      </Badge>
                    )}
                    <span className="text-sm text-slate-400">
                      {tag.usage_count} uses
                    </span>
                    {tag.is_trending && (
                      <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-xs">
                        <TrendingUp size={12} className="mr-1" />
                        Trending
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {tag.description && (
                <p className="text-slate-300 mb-4 max-w-2xl">
                  {tag.description}
                </p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8"
        >
          <div className="flex items-center gap-4">
            <span className="text-sm text-slate-400">
              {total} review{total !== 1 ? 's' : ''} found
            </span>
          </div>

          <div className="flex items-center gap-3">
            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="score-high">Highest Score</SelectItem>
                <SelectItem value="score-low">Lowest Score</SelectItem>
                <SelectItem value="popular">Most Popular</SelectItem>
              </SelectContent>
            </Select>

            {/* View Mode */}
            <div className="flex rounded-lg bg-slate-800/50 border border-slate-600 p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${
                  viewMode === 'grid'
                    ? 'bg-violet-500 text-white'
                    : 'text-slate-400 hover:text-white'
                }`}
              >
                <Grid size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${
                  viewMode === 'list'
                    ? 'bg-violet-500 text-white'
                    : 'text-slate-400 hover:text-white'
                }`}
              >
                <List size={16} />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Content */}
        {error ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-red-400 mb-4">⚠️ {error}</div>
            <Button onClick={() => window.location.reload()} variant="outline">
              Retry
            </Button>
          </motion.div>
        ) : reviews.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4 opacity-50">🏷️</div>
            <h3 className="text-xl font-bold text-white mb-2">No Reviews Yet</h3>
            <p className="text-slate-400 mb-6">
              No reviews have been tagged with "{tag.name}" yet. Be the first to create one!
            </p>
            <Button asChild>
              <Link href="/reviews/new">Create Review</Link>
            </Button>
          </motion.div>
        ) : (
          <>
            {/* Reviews Grid/List */}
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {reviews.map((review, index) =>
                viewMode === 'grid' ? (
                  <ReviewCard key={review.id} review={review} index={index} />
                ) : (
                  <ReviewListItem key={review.id} review={review} index={index} />
                )
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="flex justify-center items-center gap-2 mt-12"
              >
                <Button
                  variant="outline"
                  disabled={currentPage === 1}
                  asChild={currentPage > 1}
                >
                  {currentPage > 1 ? (
                    <Link href={`/tags/${tag.slug}?page=${currentPage - 1}`}>
                      <ChevronLeft size={16} className="mr-1" />
                      Previous
                    </Link>
                  ) : (
                    <>
                      <ChevronLeft size={16} className="mr-1" />
                      Previous
                    </>
                  )}
                </Button>

                <span className="text-sm text-slate-400 px-4">
                  Page {currentPage} of {totalPages}
                </span>

                <Button
                  variant="outline"
                  disabled={currentPage === totalPages}
                  asChild={currentPage < totalPages}
                >
                  {currentPage < totalPages ? (
                    <Link href={`/tags/${tag.slug}?page=${currentPage + 1}`}>
                      Next
                      <ChevronRight size={16} className="ml-1" />
                    </Link>
                  ) : (
                    <>
                      Next
                      <ChevronRight size={16} className="ml-1" />
                    </>
                  )}
                </Button>
              </motion.div>
            )}
          </>
        )}
      </div>
    </div>
  );
}