# Step 3: Review Owner Moderation Dashboard
## Comment System Implementation Guide - Phase 3

**Date:** 2025-01-20  
**Task:** User Dashboard Comment Moderation Interface  
**Priority:** HIGH  
**Estimated Time:** 8-10 hours  

---

## 🎯 Overview & Objectives

This step implements a comprehensive comment moderation interface within the user dashboard, allowing review owners to manage comments on their reviews with granular control and reporting capabilities.

### Key Objectives:
- [ ] Add "Comments" tab to user dashboard navigation
- [ ] Create comment management interface
- [ ] Implement moderation queue for pending comments
- [ ] Add per-review comment settings
- [ ] Create blocked users management
- [ ] Integrate with existing report system
- [ ] Add comment analytics and insights

---

## 📋 Prerequisites

- [ ] Step 1 & 2 completed successfully
- [ ] User dashboard structure in place
- [ ] Comment system components working
- [ ] Existing admin moderation system understanding
- [ ] UserDashboardNavigation component accessible

---

## 🏗️ Dashboard Integration Architecture

### Navigation Integration
```typescript
// Update src/components/dashboard/UserDashboardNavigation.tsx
const navigationItems: NavItem[] = [
  // ... existing items
  {
    label: 'Comments',
    tabKey: 'comments',
    icon: <MessageSquare className="h-5 w-5" />,
    description: 'Moderate comments on your reviews'
  },
  // ... rest of items
];
```

### Dashboard Tab Structure
```
src/components/dashboard/comments/
├── CommentModerationSection.tsx    # Main moderation interface
├── CommentModerationQueue.tsx      # Pending comments
├── CommentsByReview.tsx           # Comments grouped by review
├── BlockedUsersManager.tsx        # Manage blocked users
├── CommentSettings.tsx            # Per-review settings
├── CommentAnalytics.tsx           # Comment insights
└── hooks/
    ├── useCommentModeration.ts    # Moderation operations
    ├── useCommentSettings.ts      # Settings management
    └── useCommentAnalytics.ts     # Analytics data
```

---

## 🔧 Implementation Details

### 1. Comment Moderation Types
```typescript
// src/types/commentModeration.ts
export interface CommentModerationData {
  id: string;
  review_id: string;
  review_title: string;
  review_slug: string;
  author_id: string;
  author_name: string;
  author_username: string;
  content: string;
  created_at: string;
  is_approved: boolean;
  is_pinned: boolean;
  is_deleted: boolean;
  flag_count: number;
  upvotes: number;
  downvotes: number;
  moderation_notes?: string;
  reports?: CommentReport[];
}

export interface CommentReport {
  id: string;
  reporter_id: string;
  reporter_name: string;
  reason: string;
  description?: string;
  created_at: string;
  status: 'pending' | 'resolved' | 'dismissed';
}

export interface CommentModerationSettings {
  review_id: string;
  auto_approve: boolean;
  require_approval: boolean;
  allow_anonymous: boolean;
  max_comment_length: number;
  rate_limit_minutes: number;
  blocked_users: string[];
  blocked_words: string[];
}

export interface CommentAnalytics {
  total_comments: number;
  pending_comments: number;
  approved_comments: number;
  flagged_comments: number;
  comments_this_week: number;
  comments_this_month: number;
  top_commenters: Array<{
    user_id: string;
    username: string;
    comment_count: number;
  }>;
  engagement_rate: number;
}
```

### 2. Comment Moderation Hooks
```typescript
// src/hooks/useCommentModeration.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';

export function useCommentModeration(userId: string) {
  const queryClient = useQueryClient();
  const supabase = createClient();

  // Get all comments on user's reviews
  const commentsQuery = useQuery({
    queryKey: ['user-comments', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          review:reviews!inner(id, title, slug, author_id),
          author:profiles!author_id(id, username, display_name, avatar_url),
          reports:content_flags(
            id, reporter_id, reason, description, created_at, status,
            reporter:profiles!reporter_id(username, display_name)
          )
        `)
        .eq('reviews.author_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    staleTime: 30000,
  });

  // Moderation actions
  const moderateComment = useMutation({
    mutationFn: async ({ 
      commentId, 
      action, 
      reason, 
      notes 
    }: {
      commentId: string;
      action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin';
      reason?: string;
      notes?: string;
    }) => {
      const updates: any = {
        moderated_by: userId,
        moderated_at: new Date().toISOString(),
        moderation_notes: notes,
      };

      switch (action) {
        case 'approve':
          updates.is_approved = true;
          break;
        case 'reject':
          updates.is_approved = false;
          break;
        case 'delete':
          updates.is_deleted = true;
          break;
        case 'pin':
          updates.is_pinned = true;
          break;
        case 'unpin':
          updates.is_pinned = false;
          break;
      }

      const { error } = await supabase
        .from('comments')
        .update(updates)
        .eq('id', commentId);

      if (error) throw error;

      // Log the action
      await supabase.rpc('log_comment_action', {
        p_comment_id: commentId,
        p_action_type: action,
        p_reason: reason,
      });
    },
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "Action completed",
        description: `Comment ${action}d successfully.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Block user
  const blockUser = useMutation({
    mutationFn: async ({ 
      reviewId, 
      userIdToBlock 
    }: {
      reviewId: string;
      userIdToBlock: string;
    }) => {
      // Get current settings
      const { data: settings } = await supabase
        .from('comment_moderation_settings')
        .select('blocked_users')
        .eq('review_id', reviewId)
        .single();

      const blockedUsers = settings?.blocked_users || [];
      if (!blockedUsers.includes(userIdToBlock)) {
        blockedUsers.push(userIdToBlock);
      }

      const { error } = await supabase
        .from('comment_moderation_settings')
        .upsert({
          review_id: reviewId,
          owner_id: userId,
          blocked_users: blockedUsers,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      toast({
        title: "User blocked",
        description: "User has been blocked from commenting on this review.",
      });
    },
  });

  return {
    comments: commentsQuery.data || [],
    isLoading: commentsQuery.isLoading,
    error: commentsQuery.error,
    moderateComment,
    blockUser,
  };
}
```

### 3. Main Moderation Section Component
```typescript
// src/components/dashboard/comments/CommentModerationSection.tsx
'use client';

import React, { useState } from 'react';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { useAuthContext } from '@/contexts/AuthContext';
import { CommentModerationQueue } from './CommentModerationQueue';
import { CommentsByReview } from './CommentsByReview';
import { BlockedUsersManager } from './BlockedUsersManager';
import { CommentAnalytics } from './CommentAnalytics';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare, 
  Clock, 
  Shield, 
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Users
} from 'lucide-react';

export function CommentModerationSection() {
  const { user } = useAuthContext();
  const { comments, isLoading } = useCommentModeration(user?.id || '');

  if (!user) return null;

  // Calculate stats
  const pendingComments = comments.filter(c => !c.is_approved && !c.is_deleted);
  const flaggedComments = comments.filter(c => c.flag_count > 0);
  const totalComments = comments.filter(c => !c.is_deleted).length;

  return (
    <div className="space-y-6">
      {/* Header with Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600/20 rounded-lg">
                <MessageSquare className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-slate-200">{totalComments}</p>
                <p className="text-sm text-slate-400">Total Comments</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-600/20 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-slate-200">{pendingComments.length}</p>
                <p className="text-sm text-slate-400">Pending Review</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-600/20 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-slate-200">{flaggedComments.length}</p>
                <p className="text-sm text-slate-400">Flagged</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-600/20 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-slate-200">
                  {Math.round((totalComments - pendingComments.length) / Math.max(totalComments, 1) * 100)}%
                </p>
                <p className="text-sm text-slate-400">Moderated</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Moderation Interface */}
      <Card className="bg-slate-900/60 border-slate-700/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-slate-200">
            <Shield className="h-6 w-6 text-purple-400" />
            Comment Moderation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="queue" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-slate-800/50">
              <TabsTrigger value="queue" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Queue
                {pendingComments.length > 0 && (
                  <Badge variant="destructive" className="ml-1">
                    {pendingComments.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="by-review" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                By Review
              </TabsTrigger>
              <TabsTrigger value="blocked" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Blocked Users
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Analytics
              </TabsTrigger>
            </TabsList>

            <TabsContent value="queue" className="mt-6">
              <CommentModerationQueue 
                comments={pendingComments}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="by-review" className="mt-6">
              <CommentsByReview 
                comments={comments}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="blocked" className="mt-6">
              <BlockedUsersManager userId={user.id} />
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <CommentAnalytics userId={user.id} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
```

### 4. Moderation Queue Component
```typescript
// src/components/dashboard/comments/CommentModerationQueue.tsx
'use client';

import React, { useState } from 'react';
import { CommentModerationData } from '@/types/commentModeration';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Check, 
  X, 
  Pin, 
  Flag, 
  ExternalLink,
  MessageSquare,
  Calendar,
  User
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

interface CommentModerationQueueProps {
  comments: CommentModerationData[];
  isLoading: boolean;
}

export function CommentModerationQueue({ 
  comments, 
  isLoading 
}: CommentModerationQueueProps) {
  const [selectedComment, setSelectedComment] = useState<string | null>(null);
  const [moderationNotes, setModerationNotes] = useState('');
  const { moderateComment } = useCommentModeration('');

  const handleModeration = async (
    commentId: string, 
    action: 'approve' | 'reject' | 'delete' | 'pin'
  ) => {
    await moderateComment.mutateAsync({
      commentId,
      action,
      notes: moderationNotes,
    });
    
    setSelectedComment(null);
    setModerationNotes('');
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                <div className="h-4 bg-slate-700 rounded w-1/2"></div>
                <div className="h-20 bg-slate-700 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2">
            No pending comments
          </h3>
          <p className="text-slate-400">
            All comments have been reviewed. Great job!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map((comment) => (
        <Card key={comment.id} className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600/50 transition-colors">
          <CardContent className="p-6">
            {/* Comment Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-slate-400" />
                  <span className="font-medium text-slate-200">
                    {comment.author_name}
                  </span>
                  <span className="text-slate-400">@{comment.author_username}</span>
                </div>
                {comment.flag_count > 0 && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <Flag className="h-3 w-3" />
                    {comment.flag_count} reports
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2 text-sm text-slate-400">
                <Calendar className="h-4 w-4" />
                {formatDistanceToNow(new Date(comment.created_at))} ago
              </div>
            </div>

            {/* Review Context */}
            <div className="mb-4 p-3 bg-slate-900/50 rounded-lg border border-slate-700/50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-400">Comment on:</p>
                  <p className="font-medium text-slate-200">{comment.review_title}</p>
                </div>
                <Link 
                  href={`/reviews/view/${comment.review_slug}`}
                  className="text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Comment Content */}
            <div className="mb-4 p-4 bg-slate-900/30 rounded-lg border border-slate-700/30">
              <p className="text-slate-300 whitespace-pre-wrap leading-relaxed">
                {comment.content}
              </p>
            </div>

            {/* Moderation Actions */}
            <div className="space-y-4">
              {selectedComment === comment.id && (
                <div className="space-y-3">
                  <Textarea
                    value={moderationNotes}
                    onChange={(e) => setModerationNotes(e.target.value)}
                    placeholder="Add moderation notes (optional)..."
                    className="bg-slate-800/50 border-slate-600 text-slate-200"
                  />
                </div>
              )}
              
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => handleModeration(comment.id, 'approve')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  disabled={moderateComment.isPending}
                >
                  <Check className="h-4 w-4 mr-2" />
                  Approve
                </Button>
                
                <Button
                  onClick={() => handleModeration(comment.id, 'reject')}
                  variant="destructive"
                  disabled={moderateComment.isPending}
                >
                  <X className="h-4 w-4 mr-2" />
                  Reject
                </Button>
                
                <Button
                  onClick={() => handleModeration(comment.id, 'pin')}
                  variant="outline"
                  className="border-yellow-600 text-yellow-400 hover:bg-yellow-600/20"
                  disabled={moderateComment.isPending}
                >
                  <Pin className="h-4 w-4 mr-2" />
                  Pin
                </Button>
                
                <Button
                  variant="ghost"
                  onClick={() => setSelectedComment(
                    selectedComment === comment.id ? null : comment.id
                  )}
                  className="text-slate-400 hover:text-slate-200"
                >
                  Add Notes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

---

## ✅ Implementation Checklist

### Dashboard Integration
- [ ] Add "Comments" tab to UserDashboardNavigation
- [ ] Update dashboard page.tsx to handle comments tab
- [ ] Create CommentModerationSection component
- [ ] Test navigation and tab switching

### Moderation Components
- [ ] Create CommentModerationQueue component
- [ ] Create CommentsByReview component
- [ ] Create BlockedUsersManager component
- [ ] Create CommentAnalytics component
- [ ] Create CommentSettings component

### Hooks and Services
- [ ] Implement useCommentModeration hook
- [ ] Implement useCommentSettings hook
- [ ] Implement useCommentAnalytics hook
- [ ] Create moderation API endpoints
- [ ] Test all moderation actions

### User Experience
- [ ] Add loading states and error handling
- [ ] Implement responsive design
- [ ] Add accessibility features
- [ ] Test with different user scenarios
- [ ] Add confirmation dialogs for destructive actions

---

## 📝 AI Implementation Prompts

### Prompt 1: Dashboard Integration
```
Integrate the comment moderation system into the existing user dashboard. Add the "Comments" tab to the navigation and create the main CommentModerationSection component. Follow the existing dashboard design patterns and ensure proper tab switching functionality.
```

### Prompt 2: Moderation Interface
```
Implement the comment moderation queue and management interfaces. Create components for reviewing pending comments, managing settings per review, and handling blocked users. Ensure the interface is intuitive and follows the existing admin moderation patterns.
```

### Prompt 3: Analytics and Insights
```
Create comment analytics components that provide insights into comment engagement, moderation statistics, and user behavior. Include charts and metrics that help review owners understand their comment community.
```

---

## 🔄 Next Steps

Upon completion:
1. Proceed to **Step 4: Advanced Features & Security**
2. Implement spam detection and prevention
3. Add advanced moderation tools
4. Enhance security measures

---

**⚠️ IMPORTANT NOTES FOR AI:**
- Follow existing dashboard design patterns
- Ensure proper error handling and loading states
- Test moderation actions thoroughly
- Maintain consistency with admin moderation interface
- Implement proper confirmation dialogs for destructive actions
