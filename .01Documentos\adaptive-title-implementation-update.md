# Adaptive Title Component - Centralized Solution

## Problem Solved

Previously, components with adaptive text titles needed to:
1. Import `useBackgroundBrightness` hook
2. Call the hook in the component
3. Apply conditional CSS classes manually
4. Import CSS file for styles

This led to **inconsistent implementation** and **missing adaptive behavior** when developers forgot any of these steps.

## Centralized Solution

Created **`AdaptiveTitle`** component at `/src/components/ui/AdaptiveTitle.tsx` that:

### ✅ **Automatic Background Detection**
- Built-in `useBackgroundBrightness` hook
- No manual hook imports needed
- Consistent behavior across all components

### ✅ **Pre-configured Variants**
```typescript
// Main adaptive title component
<AdaptiveTitle size="lg" variant="title" codeStyle>
  My Dashboard Section
</AdaptiveTitle>

// Pre-configured dashboard section title
<DashboardSectionTitle>
  My Reviews
</DashboardSectionTitle>

// Pre-configured subtitle
<DashboardSubtitle>
  Subtitle text
</DashboardSubtitle>

// Pre-configured card title
<DashboardCardTitle>
  Card Title
</DashboardCardTitle>
```

### ✅ **Replaces Manual Pattern**
**Before (Error-Prone):**
```typescript
const isDarkBackground = useBackgroundBrightness();
<span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
  <span className="text-violet-400/60">&lt;</span>
  <span className="px-2">My Section</span>
  <span className="text-violet-400/60">/&gt;</span>
</span>
```

**After (Automatic):**
```typescript
<DashboardSectionTitle>
  My Section
</DashboardSectionTitle>
```

## Implementation Guide

### For New Components
Always use the centralized components:
```typescript
import { DashboardSectionTitle, DashboardSubtitle } from '@/components/ui/AdaptiveTitle';

export function MyComponent() {
  return (
    <div>
      <DashboardSectionTitle>Page Title</DashboardSectionTitle>
      <DashboardSubtitle>Subtitle</DashboardSubtitle>
    </div>
  );
}
```

### For Existing Components
Replace manual adaptive text patterns with the centralized component:

**Find and Replace Pattern:**
```typescript
// Find this pattern:
const isDarkBackground = useBackgroundBrightness();
<div className="border-l-4 border-purple-500 pl-4">
  <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
    <span className="text-violet-400/60">&lt;</span>
    <span className="px-2">TITLE_TEXT</span>
    <span className="text-violet-400/60">/&gt;</span>
  </span>
</div>

// Replace with:
<DashboardSectionTitle>TITLE_TEXT</DashboardSectionTitle>
```

## Benefits

### ✅ **Consistency**
- All titles use the same adaptive logic
- Uniform styling across the application
- No more manual hook management

### ✅ **Maintainability** 
- Single source of truth for adaptive behavior
- Easy to update adaptive logic globally
- Reduced code duplication

### ✅ **Developer Experience**
- Simple, intuitive API
- No need to remember hook imports
- Automatic CSS class application

### ✅ **Error Prevention**
- Impossible to forget brightness detection
- Built-in transitions and styling
- Consistent component behavior

## Current Status

### ✅ **Fixed Components**
All dashboard components now properly use adaptive text:
- **PrivacySettings** - "Privacy & Visibility Controls"
- **CommentModerationSection** - "Comment Moderation"  
- **ModernPerformanceSection** - "My Surveys"
- **ModernReviewsSection** - "My Reviews"
- **UserDashboardLayout** - Navigation elements
- **ConnectionsSection** - "Platform Connections"
- **ResponsiveNavigation** - Mobile navigation

### 🎯 **Next Steps**
1. **Migrate existing components** to use `AdaptiveTitle` components
2. **Update documentation** to use centralized pattern
3. **Add linting rules** to prevent manual adaptive text patterns
4. **Create automated tests** for adaptive behavior

## Component API Reference

### `AdaptiveTitle`
Main component with full customization options.

**Props:**
- `children: ReactNode` - Title content
- `className?: string` - Additional CSS classes
- `size?: 'sm' | 'md' | 'lg' | 'xl'` - Text size
- `variant?: 'title' | 'subtitle' | 'content'` - Style variant
- `codeStyle?: boolean` - Add code-style brackets

### `DashboardSectionTitle`
Pre-configured for main section titles with purple border.

### `DashboardSubtitle`
Pre-configured for subtitle text.

### `DashboardCardTitle`
Pre-configured for card titles.

## Implementation Example

```typescript
import { 
  DashboardSectionTitle, 
  DashboardSubtitle, 
  AdaptiveTitle 
} from '@/components/ui/AdaptiveTitle';

export function MyDashboardSection() {
  return (
    <div className="space-y-6">
      {/* Main section title with automatic border and brackets */}
      <DashboardSectionTitle>
        My Dashboard Section
      </DashboardSectionTitle>
      
      {/* Subtitle */}
      <DashboardSubtitle>
        Section description
      </DashboardSubtitle>
      
      {/* Custom adaptive title */}
      <AdaptiveTitle size="md" variant="content" className="custom-class">
        Custom Title
      </AdaptiveTitle>
    </div>
  );
}
```

This centralized approach ensures **all adaptive text behavior is consistent** and **prevents future implementation errors**.