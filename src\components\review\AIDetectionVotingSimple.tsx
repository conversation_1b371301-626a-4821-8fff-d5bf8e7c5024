'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, User, <PERSON><PERSON>, HelpCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useAIVoting } from '@/hooks/useAIVoting';
import { useAuthContext } from '@/contexts/auth-context';

interface AIDetectionVotingSimpleProps {
  reviewId: string;
  className?: string;
}

export function AIDetectionVotingSimple({ reviewId, className }: AIDetectionVotingSimpleProps) {
  const { user } = useAuthContext();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const { stats, userVote, submitVote, isSubmitting } = useAIVoting(reviewId);

  const handleVote = async (vote: 'ai' | 'human' | 'unsure') => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to vote',
        variant: 'destructive',
      });
      return;
    }

    try {
      await submitVote(vote);
      toast({
        title: 'Vote submitted',
        description: 'Thank you for your feedback!',
      });
    } catch (error) {
      toast({
        title: 'Failed to submit vote',
        description: 'Please try again later',
        variant: 'destructive',
      });
    }
  };

  const hasVoted = !!userVote;
  const totalVotes = stats.humanVotes + stats.aiVotes + stats.unsureVotes;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <span
          className={`select-none cursor-pointer font-mono text-[0.65rem] md:text-xs font-bold transition-colors hover:text-violet-400 pb-2 drop-shadow-sm ${className || ''}`}
          style={{ display: 'inline-flex', alignItems: 'center', gap: 2 }}
        >
          <span style={{ color: '#a78bfa', fontWeight: 700, fontSize: '1em', textShadow: '0 1px 4px rgba(80,0,120,0.18)' }}>&lt;</span>
          <span className="mx-1" style={{ color: '#e5e7eb', fontWeight: 700, fontSize: '0.85em', textShadow: '0 1px 4px rgba(0,0,0,0.18)' }}>Is this AI?</span>
          <span style={{ color: '#a78bfa', fontWeight: 700, fontSize: '1em', textShadow: '0 1px 4px rgba(80,0,120,0.18)' }}>/&gt;</span>
        </span>
      </DialogTrigger>
      <DialogContent className="max-w-lg border border-slate-700/50 bg-slate-900/95 backdrop-blur-xl rounded-xl shadow-2xl p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-slate-800/60 rounded-lg border border-slate-600/30">
              <Brain className="w-5 h-5 text-slate-300" />
            </div>
            <div>
              <span className="text-lg font-bold text-slate-200">
                AI Detection
              </span>
              <p className="text-slate-400 text-sm font-normal">
                Help identify AI-generated content
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="relative">

          <div className="space-y-6">
            {/* Current Stats */}
            {totalVotes > 0 && (
              <div className="bg-slate-800/40 rounded-lg p-4 border border-slate-600/30">
                <div className="text-sm text-slate-300 mb-3">
                  Community votes: {totalVotes}
                </div>
                <div className="grid grid-cols-3 gap-3 text-xs">
                  <div className="text-center p-3 bg-slate-700/40 rounded-lg border border-slate-600/30">
                    <div className="text-slate-300 flex items-center justify-center gap-1 mb-1">
                      <User className="w-3 h-3" />
                      Human
                    </div>
                    <div className="text-lg font-bold text-slate-200">{stats.humanVotes}</div>
                  </div>
                  <div className="text-center p-3 bg-slate-700/40 rounded-lg border border-slate-600/30">
                    <div className="text-slate-300 flex items-center justify-center gap-1 mb-1">
                      <HelpCircle className="w-3 h-3" />
                      Unsure
                    </div>
                    <div className="text-lg font-bold text-slate-200">{stats.unsureVotes}</div>
                  </div>
                  <div className="text-center p-3 bg-slate-700/40 rounded-lg border border-slate-600/30">
                    <div className="text-slate-300 flex items-center justify-center gap-1 mb-1">
                      <Bot className="w-3 h-3" />
                      AI
                    </div>
                    <div className="text-lg font-bold text-slate-200">{stats.aiVotes}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Voting Section */}
            {user ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-lg text-slate-200 mb-2">
                    {hasVoted ? 'Vote Recorded' : 'Cast Your Vote'}
                  </div>
                  <div className="text-sm text-slate-400">
                    {hasVoted ? 'You can change your vote anytime' : 'What do you think about this content?'}
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { type: 'human' as const, icon: User, label: 'Human', desc: 'Feels authentic' },
                    { type: 'unsure' as const, icon: HelpCircle, label: 'Unsure', desc: "Can't tell" },
                    { type: 'ai' as const, icon: Bot, label: 'AI', desc: 'Seems generated' }
                  ].map((option) => {
                    const isSelected = userVote === option.type;
                    const Icon = option.icon;

                    return (
                      <button
                        key={option.type}
                        onClick={() => handleVote(option.type)}
                        disabled={isSubmitting}
                        className={`relative p-4 rounded-lg border transition-all duration-200 ${
                          isSelected 
                            ? 'bg-slate-700/60 border-slate-500/70 text-slate-200' 
                            : 'bg-slate-800/40 border-slate-600/30 text-slate-300 hover:bg-slate-700/40 hover:border-slate-500/50'
                        } ${
                          isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                        }`}
                      >
                        {isSelected && (
                          <div className="absolute -top-2 -right-2 w-5 h-5 bg-slate-600 rounded-full border border-slate-500 flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                        <div className="flex flex-col items-center gap-2">
                          <Icon className="w-5 h-5" />
                          <span className="font-medium text-xs">{option.label}</span>
                          <span className="text-[10px] text-slate-500 text-center leading-tight">{option.desc}</span>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <HelpCircle className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg text-slate-300 mb-2">
                  Authentication Required
                </h3>
                <p className="text-sm text-slate-400">
                  Sign in to participate in AI detection
                </p>
              </div>
            )}

            {/* Action Button */}
            <div className="flex justify-end pt-4 border-t border-slate-700/30">
              <button
                onClick={() => setOpen(false)}
                className="px-4 py-2 bg-slate-800/60 hover:bg-slate-700/60 border border-slate-600/30 hover:border-slate-500/50 rounded-lg text-slate-300 hover:text-white transition-all duration-200 text-sm"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}