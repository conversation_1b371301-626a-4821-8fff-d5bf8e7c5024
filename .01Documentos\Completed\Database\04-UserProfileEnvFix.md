# 🔧 Environment Variables Fix Guide - User Profile System

## 📋 **Issue Overview**
The User Profile System's SEO metadata generation requires proper environment variables for full URL generation. Without these variables, the system falls back to hardcoded URLs which can cause issues in different environments.

**Affected Files:**
- `src/app/u/[slug]/metadata.ts`
- SEO metadata generation
- Open Graph URLs
- Twitter Card images
- Canonical URLs
- Schema.org structured data

---

## 🎯 **Required Environment Variables**

### **1. NEXT_PUBLIC_SITE_URL**
**Purpose:** Base URL for generating absolute URLs in metadata
**Required for:** Open Graph, Twitter Cards, canonical URLs, structured data

**Format:**
```bash
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

**Examples by Environment:**
```bash
# Development
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Staging
NEXT_PUBLIC_SITE_URL=https://staging.criticalpixel.com

# Production
NEXT_PUBLIC_SITE_URL=https://critical-pixel.com
```

---

## 🛠️ **Step-by-Step Fix Instructions**

### **Step 1: Check Current Environment Files**

1. **Check if `.env.local` exists:**
   ```bash
   ls -la .env*
   ```

2. **Verify current environment variables:**
   ```bash
   cat .env.local | grep NEXT_PUBLIC_SITE_URL
   ```

### **Step 2: Add Missing Environment Variables**

#### **For Development Environment:**
1. **Open or create `.env.local`:**
   ```bash
   # Create if doesn't exist
   touch .env.local

   # Edit the file
   code .env.local
   ```

2. **Add the required variable:**
   ```bash
   # Development Environment
   NEXT_PUBLIC_SITE_URL=http://localhost:3000

   # Existing Supabase variables (keep these)
   NEXT_PUBLIC_SUPABASE_URL=
   NEXT_PUBLIC_SUPABASE_ANON_KEY=
   SUPABASE_URL=
   SUPABASE_ANON_KEY=
   ```

#### **For Production Environment:**
1. **Update production environment variables:**
   ```bash
   # Production Environment
   NEXT_PUBLIC_SITE_URL=https://critical-pixel.com
   ```

2. **For Vercel deployment:**
   - Go to Vercel Dashboard → Project Settings → Environment Variables
   - Add: `NEXT_PUBLIC_SITE_URL` = `https://critical-pixel.com`

3. **For other hosting platforms:**
   - Add the environment variable through your hosting platform's dashboard
   - Ensure it's available at build time

### **Step 3: Restart Development Server**

```bash
# Stop current server (Ctrl+C)
# Restart to load new environment variables
npm run dev
# or
yarn dev
```

### **Step 4: Verify the Fix**

1. **Check environment variable loading:**
   ```javascript
   // Add temporary console.log in metadata.ts
   console.log('SITE_URL:', process.env.NEXT_PUBLIC_SITE_URL);
   ```

2. **Test metadata generation:**
   - Visit any profile page: `http://localhost:3000/u/username`
   - Check browser dev tools → Network → Document → Response Headers
   - Look for proper Open Graph tags

3. **Validate URLs in metadata:**
   ```bash
   # Should show localhost:3000 in development
   curl -s http://localhost:3000/u/testuser | grep -i "og:url"
   ```

---

## 🔍 **Verification Checklist**

### **Development Environment:**
- [ ] `.env.local` contains `NEXT_PUBLIC_SITE_URL=http://localhost:3000`
- [ ] Profile pages load without console errors
- [ ] Open Graph URLs point to `localhost:3000`
- [ ] Twitter Card images use correct base URL
- [ ] Canonical URLs are properly formatted

### **Production Environment:**
- [ ] Production env vars include `NEXT_PUBLIC_SITE_URL=https://critical-pixel.com`
- [ ] Build process completes without warnings
- [ ] SEO metadata uses production URLs
- [ ] Social media previews work correctly

---

## 🧪 **Testing SEO Metadata**

### **1. Open Graph Debugger:**
```bash
# Facebook Sharing Debugger
https://developers.facebook.com/tools/debug/

# Test URL format:
https://critical-pixel.com/u/username
```

### **2. Twitter Card Validator:**
```bash
# Twitter Card Validator
https://cards-dev.twitter.com/validator

# Test URL format:
https://critical-pixel.com/u/username
```

### **3. Manual Verification:**
```bash
# Check generated metadata
curl -s https://critical-pixel.com/u/testuser | grep -E "(og:|twitter:|canonical)"
```

---

## 🚨 **Common Issues & Solutions**

### **Issue 1: Environment Variable Not Loading**
**Symptoms:** URLs still show fallback domain
**Solution:**
```bash
# Ensure variable name is correct (case-sensitive)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Restart development server
npm run dev
```

### **Issue 2: Build-Time vs Runtime Variables**
**Symptoms:** Works in dev but not in production
**Solution:**
```bash
# NEXT_PUBLIC_ prefix is required for client-side access
# Ensure it's set in production environment
```

### **Issue 3: Trailing Slash Issues**
**Symptoms:** Double slashes in generated URLs
**Solution:**
```bash
# Don't include trailing slash
NEXT_PUBLIC_SITE_URL=https://critical-pixel.com  ✅
NEXT_PUBLIC_SITE_URL=https://critical-pixel.com/ ❌
```

### **Issue 4: HTTPS vs HTTP in Production**
**Symptoms:** Mixed content warnings
**Solution:**
```bash
# Always use HTTPS in production
NEXT_PUBLIC_SITE_URL=https://critical-pixel.com  ✅
NEXT_PUBLIC_SITE_URL=http://criticalpixel.com   ❌
```

---

## 📊 **Impact Assessment**

### **Before Fix:**
- ❌ Hardcoded URLs in metadata
- ❌ Wrong URLs in development
- ❌ Social media previews broken
- ❌ SEO canonical URLs incorrect

### **After Fix:**
- ✅ Dynamic URL generation
- ✅ Environment-specific URLs
- ✅ Proper social media previews
- ✅ Correct canonical URLs
- ✅ Better SEO performance

---

## 🔄 **Deployment Workflow**

### **Development → Staging → Production:**

1. **Development:**
   ```bash
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   ```

2. **Staging:**
   ```bash
   NEXT_PUBLIC_SITE_URL=https://staging.criticalpixel.com
   ```

3. **Production:**
   ```bash
   NEXT_PUBLIC_SITE_URL=https://critical-pixel.com
   ```

### **CI/CD Integration:**
```yaml
# Example for GitHub Actions
env:
  NEXT_PUBLIC_SITE_URL: ${{ secrets.SITE_URL }}
```

---

## ✅ **Completion Verification**

**Test these scenarios:**
1. [ ] Profile page loads correctly in development
2. [ ] Open Graph tags show correct localhost URLs in dev
3. [ ] Production deployment uses production URLs
4. [ ] Social media sharing works properly
5. [ ] SEO tools validate metadata correctly

**Success Criteria:**
- No hardcoded URLs in generated metadata
- Environment-appropriate URLs in all contexts
- Social media previews display correctly
- SEO metadata passes validation tools

---

## 🔧 **Quick Fix Commands**

### **Development Setup:**
```bash
# Add to .env.local
echo "NEXT_PUBLIC_SITE_URL=http://localhost:3000" >> .env.local

# Restart server
npm run dev
```

### **Production Deployment:**
```bash
# Vercel
vercel env add NEXT_PUBLIC_SITE_URL production

# Netlify
netlify env:set NEXT_PUBLIC_SITE_URL https://critical-pixel.com

# Manual deployment
export NEXT_PUBLIC_SITE_URL=https://critical-pixel.com
npm run build
```

### **Validation:**
```bash
# Test metadata generation
curl -s http://localhost:3000/u/testuser | grep -E "og:url|twitter:url|canonical"

# Should output URLs with correct domain
```

---

**Status:** 🔧 **READY FOR IMPLEMENTATION**
**Estimated Time:** 15-30 minutes
**Complexity:** Low
**Impact:** High (SEO and social sharing)
**Priority:** Medium (affects SEO quality but has fallbacks)