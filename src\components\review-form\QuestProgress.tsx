import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { 
  BookOpen, 
  Sparkles, 
  Trophy, 
  Swords, 
  Tag, 
  DollarSign, 
  UploadCloud, 
  CheckCircle, 
  Lock
} from 'lucide-react';

interface QuestProgressProps {
  currentStep: number;
  steps: { id: number; name: string; completed: boolean }[];
  formCompletion: number;
  onStepClick?: (stepId: number) => void; // For navigation
}

const QuestProgress: React.FC<QuestProgressProps> = ({ 
  currentStep, 
  steps, 
  formCompletion,
  onStepClick 
}) => {
  // Map the existing steps to our status-based format
  const [progressSteps, setProgressSteps] = useState(
    steps.map(step => ({
      ...step,
      status: step.completed ? 'complete' : step.id === currentStep ? 'current' : 'upcoming'
    }))
  );

  // Update steps when props change
  useEffect(() => {
    setProgressSteps(steps.map(step => ({
      ...step,
      status: step.completed ? 'complete' : step.id === currentStep ? 'current' : 'upcoming'
    })));
  }, [steps, currentStep]);

  // Icon mapping
  const getStepIcon = (index: number) => {
    const icons = [
      <BookOpen key="book" className="h-4 w-4" />,
      <Sparkles key="sparkles" className="h-4 w-4" />,
      <Trophy key="trophy" className="h-4 w-4" />, 
      <Swords key="swords" className="h-4 w-4" />,
      <Tag key="tag" className="h-4 w-4" />,
      <DollarSign key="dollar" className="h-4 w-4" />,
      <UploadCloud key="upload" className="h-4 w-4" />
    ];
    return icons[index] || <BookOpen className="h-4 w-4" />;
  };

  // Handle step clicking
  const handleStepClick = (stepId: number) => {
    if (onStepClick) {
      onStepClick(stepId);
    } else {
      // Default behavior: scroll to section with id matching the step
      const element = document.getElementById(`step-${stepId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <div className="w-full mt-6"> {/* Added margin-top */}
      {/* Simplified header */}
      <div className="rounded-lg bg-indigo-900 p-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Trophy className="h-4 w-4 text-indigo-300" />
          <h3 className="text-sm font-medium text-white">Quest Progress</h3>
        </div>
        <div className="bg-indigo-800 rounded-full px-3 py-1 text-xs text-white font-medium">
          {formCompletion}%
        </div>
      </div>
      
      {/* Simplified progress bar */}
      <div className="mt-2 h-1 w-full bg-indigo-950 rounded-full overflow-hidden">
        <div 
          className="h-full bg-indigo-500 transition-all duration-500 ease-out"
          style={{ width: `${formCompletion}%` }}
        ></div>
      </div>
      
      {/* Simplified step indicators */}
      <div className="relative mt-3 overflow-x-auto hide-scrollbar pb-2">
        <div className="flex items-center min-w-max px-1">
          {progressSteps.map((step, index) => (
            <React.Fragment key={step.id}>
              {/* Step Item */}
              <div className="flex flex-col items-center relative">
                {/* Clickable button area */}
                <button
                  onClick={() => handleStepClick(step.id)}
                  disabled={step.status === 'upcoming' && index > currentStep}
                  className={cn(
                    "relative w-8 h-8 rounded-full flex items-center justify-center mb-1",
                    "transition-all duration-300 outline-none focus:ring-1 focus:ring-indigo-500",
                    step.status === 'upcoming' && index > currentStep ? "cursor-not-allowed" : "cursor-pointer"
                  )}
                  aria-label={`Go to step ${step.name}`}
                >
                  {/* Main circle */}
                  <div className={cn(
                    "w-9 h-9 rounded-full flex items-center justify-center relative",
                    step.status === 'complete' ? "bg-indigo-500 text-white" : 
                    step.status === 'current' ? "bg-indigo-600 ring-2 ring-indigo-400 text-white" :
                    "bg-indigo-950 text-gray-400"
                  )}>
                    {/* Icon or checkmark */}
                    {step.status === 'complete' ? (
                      <CheckCircle className="h-4 w-4 text-white" />
                    ) : step.status === 'upcoming' && index > currentStep ? (
                      <Lock className="h-3 w-3 text-gray-500" />
                    ) : (
                      getStepIcon(index)
                    )}
                    
                    {/* Step number badge - made smaller */}
                    <div className={cn(
                      "absolute -top-1 -right-1 w-4 h-4 text-[10px] font-bold rounded-full flex items-center justify-center",
                      step.status === 'complete' ? "bg-green-500 text-white" :
                      step.status === 'current' ? "bg-indigo-400 text-white" : "bg-gray-700 text-gray-400"
                    )}>
                      {step.id}
                    </div>
                  </div>
                </button>
                
                {/* Step name */}
                <span
                  className={cn(
                    "text-[10px] font-medium text-center max-w-10 px-1",
                    step.status === 'complete' ? "text-indigo-400" : 
                    step.status === 'current' ? "text-white" : "text-gray-500"
                  )}
                >
                  {step.name}
                </span>
              </div>
              
              {/* Connector Line */}
              {index < progressSteps.length - 1 && (
                <div className="flex-grow mx-1 flex items-center self-center h-0.5 mt-4">
                  <div className="h-0.5 w-full bg-indigo-900"></div>
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuestProgress;