-- Game Covers Storage Migration
-- Date: January 29, 2025
-- Purpose: Add support for caching IGDB game covers in Supabase storage

-- Add new columns to games table for cover caching
ALTER TABLE games ADD COLUMN IF NOT EXISTS supabase_cover_url TEXT;
ALTER TABLE games ADD COLUMN IF NOT EXISTS igdb_cover_cached_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE games ADD COLUMN IF NOT EXISTS cover_cache_status TEXT DEFAULT 'pending' CHECK (cover_cache_status IN ('pending', 'processing', 'cached', 'failed'));

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_games_cover_cache_status ON games(cover_cache_status);
CREATE INDEX IF NOT EXISTS idx_games_cached_at ON games(igdb_cover_cached_at DESC);

-- Create storage bucket for game covers
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'game-covers',
  'game-covers',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for game covers bucket
-- Policy 1: Allow public read access to game covers
CREATE POLICY "Public read access for game covers" ON storage.objects
  FOR SELECT USING (bucket_id = 'game-covers');

-- Policy 2: Allow service role to upload covers
CREATE POLICY "Service role can upload game covers" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'game-covers' AND
    auth.role() = 'service_role'
  );

-- Policy 3: Allow service role to update covers
CREATE POLICY "Service role can update game covers" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'game-covers' AND
    auth.role() = 'service_role'
  );

-- Policy 4: Allow service role to delete covers
CREATE POLICY "Service role can delete game covers" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'game-covers' AND
    auth.role() = 'service_role'
  );

-- Function to get optimal cover URL (cached or fallback)
CREATE OR REPLACE FUNCTION get_optimal_cover_url(game_record games)
RETURNS TEXT AS $$
BEGIN
  -- Return cached cover if available and status is 'cached'
  IF game_record.cover_cache_status = 'cached' AND game_record.supabase_cover_url IS NOT NULL THEN
    RETURN game_record.supabase_cover_url;
  END IF;
  
  -- Fallback to IGDB cover URL
  RETURN game_record.cover_url;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to mark cover for recaching
CREATE OR REPLACE FUNCTION mark_cover_for_recache(game_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE games 
  SET 
    cover_cache_status = 'pending',
    igdb_cover_cached_at = NULL
  WHERE id = game_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update cover cache status
CREATE OR REPLACE FUNCTION update_cover_cache_status(
  game_id UUID,
  new_status TEXT,
  cached_url TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  UPDATE games 
  SET 
    cover_cache_status = new_status,
    supabase_cover_url = COALESCE(cached_url, supabase_cover_url),
    igdb_cover_cached_at = CASE 
      WHEN new_status = 'cached' THEN NOW()
      ELSE igdb_cover_cached_at
    END
  WHERE id = game_id;
END;
$$ LANGUAGE plpgsql;

-- Create audit table for cover processing
CREATE TABLE IF NOT EXISTS game_cover_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  action TEXT NOT NULL CHECK (action IN ('cache_started', 'cache_completed', 'cache_failed', 'cache_deleted')),
  igdb_cover_url TEXT,
  supabase_cover_url TEXT,
  error_message TEXT,
  processing_time_ms INTEGER,
  file_size_bytes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for audit table
CREATE INDEX IF NOT EXISTS idx_game_cover_audit_game_id ON game_cover_audit(game_id);
CREATE INDEX IF NOT EXISTS idx_game_cover_audit_created_at ON game_cover_audit(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_game_cover_audit_action ON game_cover_audit(action);

-- Enable RLS on audit table
ALTER TABLE game_cover_audit ENABLE ROW LEVEL SECURITY;

-- RLS policy for audit table (admin access only)
CREATE POLICY "Admin access to cover audit" ON game_cover_audit
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Function to log cover processing events
CREATE OR REPLACE FUNCTION log_cover_processing(
  p_game_id UUID,
  p_action TEXT,
  p_igdb_url TEXT DEFAULT NULL,
  p_supabase_url TEXT DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_processing_time_ms INTEGER DEFAULT NULL,
  p_file_size_bytes INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO game_cover_audit (
    game_id,
    action,
    igdb_cover_url,
    supabase_cover_url,
    error_message,
    processing_time_ms,
    file_size_bytes
  ) VALUES (
    p_game_id,
    p_action,
    p_igdb_url,
    p_supabase_url,
    p_error_message,
    p_processing_time_ms,
    p_file_size_bytes
  );
END;
$$ LANGUAGE plpgsql;

-- Create view for games with optimal cover URLs
CREATE OR REPLACE VIEW games_with_covers AS
SELECT 
  g.*,
  get_optimal_cover_url(g) as optimal_cover_url,
  CASE 
    WHEN g.cover_cache_status = 'cached' AND g.supabase_cover_url IS NOT NULL 
    THEN 'cached'
    ELSE 'igdb'
  END as cover_source
FROM games g;

-- Grant necessary permissions
GRANT SELECT ON games_with_covers TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_optimal_cover_url(games) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION mark_cover_for_recache(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_cover_cache_status(UUID, TEXT, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION log_cover_processing(UUID, TEXT, TEXT, TEXT, TEXT, INTEGER, INTEGER) TO service_role;

-- Verify migration
DO $$
BEGIN
    -- Check if new columns exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'games' AND column_name = 'supabase_cover_url'
    ) THEN
        RAISE EXCEPTION 'supabase_cover_url column was not created';
    END IF;
    
    -- Check if bucket exists
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE id = 'game-covers'
    ) THEN
        RAISE EXCEPTION 'game-covers bucket was not created';
    END IF;
    
    RAISE NOTICE 'Game covers storage migration completed successfully!';
END
$$;
