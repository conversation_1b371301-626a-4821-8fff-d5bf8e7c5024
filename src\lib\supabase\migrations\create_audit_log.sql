-- CriticalPixel Security Audit Log Database Schema
-- Date: June 14, 2025
-- Security Implementation: CRITICAL vulnerability fix
-- Purpose: Implement persistent audit logging for admin actions

-- Create audit log table with comprehensive security event tracking
CREATE TABLE IF NOT EXISTS security_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type TEXT NOT NULL,
  user_id UUID,
  admin_id UUID,
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  resource_id TEXT,
  resource_type TEXT,
  action TEXT,
  event_data JSONB,
  severity TEXT CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')) DEFAULT 'MEDIUM',
  risk_score INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for security
ALTER TABLE security_audit_log ENABLE ROW LEVEL SECURITY;

-- Only super admins can read audit logs
CREATE POLICY "Super admins can read audit logs" ON security_audit_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true 
      AND profiles.admin_level = 'SUPER_ADMIN'
    )
  );

-- System can insert audit logs (for server actions)
CREATE POLICY "System can insert audit logs" ON security_audit_log
  FOR INSERT WITH CHECK (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON security_audit_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_log_severity ON security_audit_log(severity);
CREATE INDEX IF NOT EXISTS idx_audit_log_risk_score ON security_audit_log(risk_score DESC);

-- Create function for log retention (cleanup old logs)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retention_days INTEGER DEFAULT 730)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM security_audit_log 
  WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  INSERT INTO security_audit_log (
    event_type, 
    user_id, 
    event_data, 
    severity
  ) VALUES (
    'AUDIT_LOG_CLEANUP',
    auth.uid(),
    jsonb_build_object(
      'deleted_count', deleted_count,
      'retention_days', retention_days
    ),
    'LOW'
  );
  
  RETURN deleted_count;
END;
$$;

-- Create admin role assignments table for proper super admin tracking
CREATE TABLE IF NOT EXISTS admin_role_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  admin_level TEXT NOT NULL CHECK (admin_level IN ('SUPER_ADMIN', 'ADMIN', 'MODERATOR', 'EDITOR', 'VIEWER')),
  assigned_by UUID REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  justification TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for role assignments
ALTER TABLE admin_role_assignments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Super admins can manage all role assignments" ON admin_role_assignments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true 
      AND profiles.admin_level = 'SUPER_ADMIN'
    )
  );

-- Create secure function to assign super admin
CREATE OR REPLACE FUNCTION assign_super_admin(
  target_user_email TEXT,
  justification TEXT DEFAULT 'Initial super admin assignment'
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  target_user_id UUID;
BEGIN
  -- Find user by email
  SELECT id INTO target_user_id
  FROM auth.users
  WHERE email = target_user_email;

  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User not found with email: %', target_user_email;
  END IF;

  -- Update profile
  UPDATE profiles 
  SET 
    is_admin = TRUE,
    admin_level = 'SUPER_ADMIN',
    updated_at = NOW()
  WHERE id = target_user_id;

  -- Record assignment
  INSERT INTO admin_role_assignments (
    user_id,
    admin_level,
    assigned_by,
    justification
  ) VALUES (
    target_user_id,
    'SUPER_ADMIN',
    auth.uid(),
    justification
  );

  -- Log the assignment
  INSERT INTO security_audit_log (
    event_type,
    user_id,
    admin_id,
    event_data,
    severity
  ) VALUES (
    'SUPER_ADMIN_ASSIGNED',
    target_user_id,
    auth.uid(),
    jsonb_build_object(
      'target_email', target_user_email,
      'justification', justification
    ),
    'CRITICAL'
  );
END;
$$;

-- Create function to log security events (for use by server actions)
CREATE OR REPLACE FUNCTION log_security_event(
  p_event_type TEXT,
  p_user_id UUID,
  p_admin_id UUID,
  p_ip_address INET,
  p_user_agent TEXT,
  p_session_id TEXT,
  p_resource_id TEXT,
  p_resource_type TEXT,
  p_action TEXT,
  p_event_data JSONB,
  p_severity TEXT DEFAULT 'MEDIUM'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id UUID;
  calculated_risk_score INTEGER;
BEGIN
  -- Calculate risk score based on event type and severity
  calculated_risk_score := CASE p_severity
    WHEN 'LOW' THEN 10
    WHEN 'MEDIUM' THEN 30
    WHEN 'HIGH' THEN 60
    WHEN 'CRITICAL' THEN 90
    ELSE 30
  END;

  -- Add event-specific risk factors
  calculated_risk_score := calculated_risk_score + CASE p_event_type
    WHEN 'ADMIN_VERIFICATION_FAILED' THEN 20
    WHEN 'PRIVILEGE_ESCALATION_ATTEMPT' THEN 40
    WHEN 'SELF_MODIFICATION_ATTEMPT' THEN 30
    WHEN 'UNAUTHORIZED_ADMIN_PROMOTION_ATTEMPT' THEN 50
    WHEN 'SUSPENDED_ADMIN_ACCESS_ATTEMPT' THEN 35
    WHEN 'INVALID_SUPER_ADMIN_ASSIGNMENT' THEN 45
    WHEN 'BULK_OPERATION_SIZE_EXCEEDED' THEN 25
    WHEN 'NON_ADMIN_ACCESS_ATTEMPT' THEN 15
    WHEN 'RATE_LIMIT_EXCEEDED' THEN 25
    ELSE 0
  END;

  -- Cap risk score at 100
  calculated_risk_score := LEAST(calculated_risk_score, 100);

  -- Insert audit log entry
  INSERT INTO security_audit_log (
    event_type,
    user_id,
    admin_id,
    ip_address,
    user_agent,
    session_id,
    resource_id,
    resource_type,
    action,
    event_data,
    severity,
    risk_score
  ) VALUES (
    p_event_type,
    p_user_id,
    p_admin_id,
    p_ip_address,
    p_user_agent,
    p_session_id,
    p_resource_id,
    p_resource_type,
    p_action,
    p_event_data,
    p_severity,
    calculated_risk_score
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- Create notification trigger for high-risk events
CREATE OR REPLACE FUNCTION notify_high_risk_events()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Send notification for high-risk events
  IF NEW.risk_score >= 80 OR NEW.severity = 'CRITICAL' THEN
    PERFORM pg_notify(
      'high_risk_security_event',
      json_build_object(
        'event_type', NEW.event_type,
        'user_id', NEW.user_id,
        'severity', NEW.severity,
        'risk_score', NEW.risk_score,
        'created_at', NEW.created_at
      )::text
    );
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger for high-risk event notifications
CREATE TRIGGER high_risk_security_event_trigger
  AFTER INSERT ON security_audit_log
  FOR EACH ROW
  EXECUTE FUNCTION notify_high_risk_events();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON security_audit_log TO authenticated;
GRANT EXECUTE ON FUNCTION log_security_event TO authenticated;
GRANT EXECUTE ON FUNCTION assign_super_admin TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_audit_logs TO authenticated; 