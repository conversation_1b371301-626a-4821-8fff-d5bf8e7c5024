# 🚨 Review Report System Implementation Guide

## 📋 **Overview**

This guide will walk you through implementing a comprehensive report system that allows users to report inappropriate reviews. The reported content will then appear in the existing admin moderation tool we just created.

**Implementation Date**: January 8, 2025  
**Target**: Add report functionality to review pages  
**Integration**: Connect with existing ReportsActionsDropdown admin tool

---

## 🎯 **System Architecture**

### **User Flow**
1. User visits review page (`/reviews/view/[slug]`)
2. User clicks "Report Review" button
3. <PERSON><PERSON> opens with report reasons
4. User submits report
5. Report appears in admin moderation tool
6. Ad<PERSON> can resolve/dismiss reports

### **Technical Flow**
```
ReviewPageClient.tsx → ReportModal → Server Action → Database → Admin Tool
```

---

## 🗄️ **Database Schema** 

### **Required Table: `content_flags`**

```sql
-- This table should already exist, but verify the structure:
CREATE TABLE IF NOT EXISTS content_flags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  content_id uuid NOT NULL,           -- Review ID
  content_type varchar(50) NOT NULL,  -- 'review' 
  reporter_id uuid NOT NULL,          -- User who reported
  reason varchar(100) NOT NULL,       -- Report reason
  description text,                   -- Optional details
  status varchar(20) DEFAULT 'pending', -- 'pending', 'resolved', 'dismissed'
  created_at timestamp DEFAULT now(),
  resolved_by uuid,                   -- Admin who resolved
  resolved_at timestamp,
  
  -- Foreign key constraints
  CONSTRAINT fk_content_flags_reporter 
    FOREIGN KEY (reporter_id) REFERENCES profiles(id),
  CONSTRAINT fk_content_flags_review 
    FOREIGN KEY (content_id) REFERENCES reviews(id),
  CONSTRAINT fk_content_flags_resolver 
    FOREIGN KEY (resolved_by) REFERENCES profiles(id)
);

-- Add indexes for performance
CREATE INDEX idx_content_flags_content_id ON content_flags(content_id);
CREATE INDEX idx_content_flags_status ON content_flags(status);
CREATE INDEX idx_content_flags_reporter ON content_flags(reporter_id);
```

---

## 🛠️ **Implementation Steps**

### **Step 1: Create Report Button Component**

**File**: `src/components/review/ReportButton.tsx`

```tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Flag, AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { submitReportAction } from '@/lib/actions/report-actions';
import { useAuthContext } from '@/contexts/auth-context';

interface ReportButtonProps {
  reviewId: string;
  reviewTitle: string;
  className?: string;
}

const REPORT_REASONS = [
  { value: 'inappropriate_content', label: 'Inappropriate Content' },
  { value: 'spam', label: 'Spam' },
  { value: 'harassment', label: 'Harassment' },
  { value: 'misinformation', label: 'Misinformation' },
  { value: 'copyright', label: 'Copyright Violation' },
  { value: 'other', label: 'Other' },
];

export function ReportButton({ reviewId, reviewTitle, className }: ReportButtonProps) {
  const { user } = useAuthContext();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [reason, setReason] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to report content",
        variant: "destructive"
      });
      return;
    }

    if (!reason) {
      toast({
        title: "Reason Required",
        description: "Please select a reason for reporting",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await submitReportAction({
        contentId: reviewId,
        contentType: 'review',
        reporterId: user.id,
        reason,
        description: description.trim() || undefined,
      });

      if (result.success) {
        toast({
          title: "Report Submitted",
          description: "Thank you for reporting this content. Our team will review it shortly.",
        });
        setOpen(false);
        setReason('');
        setDescription('');
      } else {
        toast({
          title: "Report Failed",
          description: result.error || "Failed to submit report",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Report submission error:', error);
      toast({
        title: "Report Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) {
    return null; // Don't show report button to unauthenticated users
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`text-slate-400 hover:text-red-400 hover:border-red-400 transition-colors ${className}`}
        >
          <Flag className="h-4 w-4 mr-2" />
          Report
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Report Review
          </DialogTitle>
          <DialogDescription>
            Help us maintain a safe community by reporting inappropriate content.
            <br />
            <strong>Review:</strong> {reviewTitle}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Reason for reporting
            </label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select a reason..." />
              </SelectTrigger>
              <SelectContent>
                {REPORT_REASONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">
              Additional details (optional)
            </label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Provide any additional context..."
              className="min-h-[80px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-slate-500 mt-1">
              {description.length}/500 characters
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!reason || isSubmitting}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Report'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
```

### **Step 2: Create Server Action for Reports**

**File**: `src/lib/actions/report-actions.ts`

```tsx
'use server';

import { createServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

export interface ReportSubmission {
  contentId: string;
  contentType: 'review' | 'comment';
  reporterId: string;
  reason: string;
  description?: string;
}

export async function submitReportAction(report: ReportSubmission): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const supabase = await createServerClient();

    // Validate input
    if (!report.contentId || !report.reporterId || !report.reason) {
      return { success: false, error: 'Missing required fields' };
    }

    // Check if user already reported this content
    const { data: existingReport } = await supabase
      .from('content_flags')
      .select('id')
      .eq('content_id', report.contentId)
      .eq('reporter_id', report.reporterId)
      .eq('content_type', report.contentType)
      .single();

    if (existingReport) {
      return { success: false, error: 'You have already reported this content' };
    }

    // Verify the content exists
    const tableName = report.contentType === 'review' ? 'reviews' : 'comments';
    const { data: content, error: contentError } = await supabase
      .from(tableName)
      .select('id')
      .eq('id', report.contentId)
      .single();

    if (contentError || !content) {
      return { success: false, error: 'Content not found' };
    }

    // Insert the report
    const { error: insertError } = await supabase
      .from('content_flags')
      .insert({
        content_id: report.contentId,
        content_type: report.contentType,
        reporter_id: report.reporterId,
        reason: report.reason,
        description: report.description,
        status: 'pending',
        created_at: new Date().toISOString(),
      });

    if (insertError) {
      console.error('Report insertion error:', insertError);
      return { success: false, error: 'Failed to submit report' };
    }

    // Revalidate admin pages
    revalidatePath('/admin/reviews/reports');

    return { success: true };

  } catch (error) {
    console.error('Report submission error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function getReportCountForContent(contentId: string): Promise<number> {
  try {
    const supabase = await createServerClient();
    
    const { count, error } = await supabase
      .from('content_flags')
      .select('*', { count: 'exact', head: true })
      .eq('content_id', contentId)
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching report count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getReportCountForContent:', error);
    return 0;
  }
}
```

### **Step 3: Integrate Report Button into Review Page**

**File**: `src/app/reviews/view/[slug]/ReviewPageClient.tsx` *(modification)*

Add the import at the top:
```tsx
import { ReportButton } from '@/components/review/ReportButton';
```

Add the report button in an appropriate location (suggested: near the user profile or in a floating action area):

```tsx
// Add this section after the UserProfileCard or before the content area
<div className="flex justify-end mb-4">
  <ReportButton 
    reviewId={review.id} 
    reviewTitle={review.gameName || review.title || 'Review'}
    className="ml-auto"
  />
</div>
```

### **Step 4: Add Report Counter for Admins (Optional)**

**File**: `src/components/admin/ReviewActionsDropdown.tsx` *(modification)*

Add a report indicator to show how many reports a review has:

```tsx
// Add this interface at the top
interface ReviewWithReports extends SecureReviewModerationData {
  reportCount?: number;
}

// In the dropdown content, add a report indicator
{review.reportCount && review.reportCount > 0 && (
  <>
    <DropdownMenuSeparator />
    <DropdownMenuItem disabled className="text-red-600">
      <AlertTriangle className="h-4 w-4 mr-2" />
      {review.reportCount} Report{review.reportCount > 1 ? 's' : ''}
    </DropdownMenuItem>
  </>
)}
```

---

## 🔧 **Configuration & Settings**

### **Environment Variables**

No additional environment variables needed. Uses existing Supabase configuration.

### **Permissions & Security**

1. **RLS Policies for `content_flags` table:**

```sql
-- Allow users to insert their own reports
CREATE POLICY "Users can create reports" ON content_flags
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- Allow users to view their own reports
CREATE POLICY "Users can view own reports" ON content_flags
  FOR SELECT USING (auth.uid() = reporter_id);

-- Allow admins to view all reports
CREATE POLICY "Admins can view all reports" ON content_flags
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Allow admins to update report status
CREATE POLICY "Admins can update reports" ON content_flags
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );
```

---

## 🎨 **UI/UX Considerations**

### **Report Button Placement Options**

1. **Top Right Corner** (Recommended)
   ```tsx
   // Near user profile, non-intrusive
   <div className="absolute top-4 right-4 z-10">
     <ReportButton reviewId={review.id} reviewTitle={review.title} />
   </div>
   ```

2. **Floating Action Button**
   ```tsx
   // Fixed position, always visible
   <div className="fixed bottom-6 right-6 z-50">
     <ReportButton reviewId={review.id} reviewTitle={review.title} />
   </div>
   ```

3. **In Content Header**
   ```tsx
   // Integrated with review metadata
   <div className="flex justify-between items-center mb-4">
     <h1>{review.title}</h1>
     <ReportButton reviewId={review.id} reviewTitle={review.title} />
   </div>
   ```

### **Visual Design**

- **Subtle appearance** to avoid encouraging frivolous reports
- **Clear modal** with good reason categorization
- **Confirmation feedback** when report is submitted
- **Rate limiting** to prevent spam (one report per user per content)

---

## 📊 **Testing Checklist**

### **Functional Testing**
- [ ] Report button appears for authenticated users
- [ ] Report button hidden for unauthenticated users
- [ ] Modal opens with all form fields
- [ ] Reason selection works
- [ ] Description field accepts input
- [ ] Character counter works
- [ ] Submit button validates required fields
- [ ] Duplicate report prevention works
- [ ] Success toast appears on submission
- [ ] Report appears in admin moderation tool

### **Security Testing**
- [ ] Users cannot report content twice
- [ ] Users cannot report non-existent content
- [ ] Invalid data is rejected
- [ ] SQL injection attempts fail
- [ ] Unauthorized access is blocked

### **UX Testing**
- [ ] Button placement is non-intrusive
- [ ] Modal is easy to use
- [ ] Success/error messages are clear
- [ ] Admin tool shows reports correctly

---

## 🚀 **Deployment Steps**

### **1. Database Setup**
```sql
-- Run the schema creation and RLS policies above
-- Verify table structure matches expectations
```

### **2. File Creation Order**
1. Create `src/lib/actions/report-actions.ts`
2. Create `src/components/review/ReportButton.tsx`
3. Modify `src/app/reviews/view/[slug]/ReviewPageClient.tsx`
4. Test functionality
5. Optional: Modify admin components for report counters

### **3. Testing**
```bash
# Run development server
npm run dev

# Test report submission
# Check admin moderation tool
# Verify database entries
```

---

## 🔮 **Future Enhancements**

### **Phase 2 Features**
- **Email notifications** to admins for new reports
- **Automatic moderation** for reviews with multiple reports
- **User reputation system** based on report accuracy
- **Report analytics** dashboard
- **Bulk actions** for similar reports

### **Phase 3 Features**
- **AI-powered content analysis** to auto-flag inappropriate content
- **Community moderation** with trusted user voting
- **Appeal system** for reported content
- **Integration with external moderation services**

---

## 📝 **Implementation Summary**

This guide provides:

✅ **Complete report button component** with modal interface  
✅ **Server action** for secure report submission  
✅ **Database schema** and security policies  
✅ **Integration points** with existing admin tools  
✅ **Testing procedures** and deployment steps  
✅ **Future enhancement** roadmap  

**Estimated Implementation Time**: 2-4 hours  
**Complexity**: Medium  
**Dependencies**: Existing auth system, Supabase, admin moderation tool

The system integrates seamlessly with the existing `ReportsActionsDropdown` component we created earlier, providing a complete report-to-resolution workflow.

---

**Author**: Development Team  
**Date**: January 8, 2025  
**Status**: Ready for Implementation  
**Next Steps**: Follow implementation steps in order 