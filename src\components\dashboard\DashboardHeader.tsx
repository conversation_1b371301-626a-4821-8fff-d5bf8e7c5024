'use client';

import { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Calendar,
  TrendingUp,
  FileText,
  Gauge,
  Plus,
  ExternalLink,
  Clock,
  Award,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { DashboardStats } from '@/types/dashboard';

export interface DashboardHeaderProps {
  user: {
    uid: string;
    displayName?: string;
    userName?: string;
    slug?: string;
    createdAt?: any;
    photoURL?: string | null;
  };
  stats: DashboardStats;
  className?: string;
}

export function DashboardHeader({ user, stats, className }: DashboardHeaderProps) {
  // Calculate additional insights
  const insights = useMemo(() => {
    const isNewUser = stats.totalReviews === 0 && stats.totalSurveys === 0;
    const isActiveUser = stats.totalReviews >= 5 || stats.totalSurveys >= 3;
    const hasRecentActivity = stats.lastActivity && 
      new Date().getTime() - new Date(stats.lastActivity).getTime() < 7 * 24 * 60 * 60 * 1000; // 7 days

    let userLevel = 'Newcomer';
    if (stats.totalReviews >= 20 || stats.totalSurveys >= 10) {
      userLevel = 'Expert Reviewer';
    } else if (stats.totalReviews >= 10 || stats.totalSurveys >= 5) {
      userLevel = 'Active Contributor';
    } else if (stats.totalReviews >= 3 || stats.totalSurveys >= 2) {
      userLevel = 'Rising Reviewer';
    }

    return {
      isNewUser,
      isActiveUser,
      hasRecentActivity,
      userLevel
    };
  }, [stats]);

  const formatJoinDate = (date: Date | null) => {
    if (!date) return '—';
    const joinDate = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - joinDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} days ago`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years} year${years > 1 ? 's' : ''} ago`;
    }
  };

  const formatLastActivity = (date: Date | null) => {
    if (!date) return 'No recent activity';
    const activityDate = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - activityDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return activityDate.toLocaleDateString();
  };

  return (
    <div className={className}>
      {/* User Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex flex-col md:flex-row md:items-center gap-6 mb-6">
          {/* User Avatar and Info */}
          <div className="flex items-center gap-4">
            <div className="relative">
              {user.photoURL ? (
                <img
                  src={user.photoURL}
                  alt={user.displayName || user.userName || 'User'}
                  className="w-16 h-16 rounded-full object-cover border-2 border-purple-500/30"
                />
              ) : (
                <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center border-2 border-purple-500/30">
                  <User className="text-purple-400" size={32} />
                </div>
              )}
              {insights.hasRecentActivity && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-gray-950 flex items-center justify-center">
                  <Activity size={10} className="text-white" />
                </div>
              )}
            </div>
            
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-slate-200">
                Welcome back, {user.displayName || user.userName}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <Award className="text-purple-400" size={16} />
                <span className="text-purple-300 font-medium">{insights.userLevel}</span>
              </div>
              <p className="text-slate-400 mt-1">
                {insights.isNewUser 
                  ? "Ready to start your gaming journey?"
                  : "Manage your reviews and performance data"
                }
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-3 md:ml-auto">
            <Button asChild className="bg-purple-600 hover:bg-purple-700">
              <Link href="/reviews/create">
                <Plus size={16} className="mr-2" />
                Create Review
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/u/${user?.slug || ''}`}>
                <ExternalLink size={16} className="mr-2" />
                View Profile
              </Link>
            </Button>
          </div>
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {/* Total Reviews */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-purple-500/50 transition-all duration-300"
          >
            <div className="flex items-center gap-2 mb-2">
              <FileText className="text-purple-400" size={16} />
              <span className="text-slate-400 text-sm">Reviews</span>
            </div>
            <div className="text-2xl font-bold text-slate-200">
              {stats.totalReviews}
            </div>
            {stats.publishedReviews > 0 && (
              <div className="text-xs text-slate-500 mt-1">
                {stats.publishedReviews} published
              </div>
            )}
          </motion.div>

          {/* Total Surveys */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-cyan-500/50 transition-all duration-300"
          >
            <div className="flex items-center gap-2 mb-2">
              <Gauge className="text-green-400" size={16} />
              <span className="text-slate-400 text-sm">Surveys</span>
            </div>
            <div className="text-2xl font-bold text-slate-200">
              {stats.totalSurveys}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              Performance data
            </div>
          </motion.div>

          {/* Average Score */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-purple-500/50 transition-all duration-300"
          >
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="text-purple-400" size={16} />
              <span className="text-slate-400 text-sm">Avg Score</span>
            </div>
            <div className="text-2xl font-bold text-slate-200">
              {stats.averageScore > 0 ? stats.averageScore : '—'}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {stats.totalReviews > 0 ? 'Out of 10' : 'No reviews yet'}
            </div>
          </motion.div>

          {/* Member Since */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-orange-500/50 transition-all duration-300"
          >
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="text-orange-400" size={16} />
              <span className="text-slate-400 text-sm">Member</span>
            </div>
            <div className="text-sm font-medium text-slate-200">
              {formatJoinDate(stats.joinDate)}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {stats.joinDate ? new Date(stats.joinDate).toLocaleDateString() : '—'}
            </div>
          </motion.div>

          {/* Last Activity */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-cyan-500/50 transition-all duration-300"
          >
            <div className="flex items-center gap-2 mb-2">
              <Clock className="text-cyan-400" size={16} />
              <span className="text-slate-400 text-sm">Last Active</span>
            </div>
            <div className="text-sm font-medium text-slate-200">
              {formatLastActivity(stats.lastActivity)}
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {insights.hasRecentActivity ? 'Recently active' : 'Come back soon!'}
            </div>
          </motion.div>

          {/* Draft Reviews (if any) */}
          {stats.draftReviews > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-yellow-500/50 transition-all duration-300"
            >
              <div className="flex items-center gap-2 mb-2">
                <FileText className="text-yellow-400" size={16} />
                <span className="text-slate-400 text-sm">Drafts</span>
              </div>
              <div className="text-2xl font-bold text-slate-200">
                {stats.draftReviews}
              </div>
              <div className="text-xs text-slate-500 mt-1">
                Unpublished
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
