'use client';

import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  MoreHorizontal,
  CheckCircle,
  Star,
  StarOff,
  Archive,
  Edit,
  ExternalLink,
  Eye,
  AlertTriangle,
  Ban,
  Unlock,
} from 'lucide-react';
import Link from 'next/link';
import { SecureReviewModerationData, SecureModerationAction } from '@/app/admin/reviews/actions';

interface ReviewActionsDropdownProps {
  review: SecureReviewModerationData;
  onModerate: (action: SecureModerationAction) => Promise<void>;
  compact?: boolean;
  showEditAction?: boolean;
  showViewAction?: boolean;
}

export function ReviewActionsDropdown({
  review,
  onModerate,
  compact = false,
  showEditAction = true,
  showViewAction = true,
}: ReviewActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    action: SecureModerationAction | null;
    title: string;
    description: string;
    variant: 'default' | 'destructive';
  }>({
    isOpen: false,
    action: null,
    title: '',
    description: '',
    variant: 'default',
  });

  const handleAction = async (action: SecureModerationAction) => {
    setIsOpen(false);
    
    // Show confirmation for destructive actions
    if (action.action === 'block') {
      setConfirmDialog({
        isOpen: true,
        action,
        title: 'Bloquear Review',
        description: 'Tem certeza que deseja bloquear este review? Ele ficará inacessível para todos os usuários, mas não será deletado.',
        variant: 'destructive',
      });
      return;
    }
    if (action.action === 'unblock') {
      setConfirmDialog({
        isOpen: true,
        action,
        title: 'Desbloquear Review',
        description: 'Deseja realmente desbloquear este review? Ele voltará a ficar acessível conforme o status.',
        variant: 'default',
      });
      return;
    }
    if (action.action === 'archive') {
      // Remover opção de arquivar (não mostrar mais)
      return;
    }

    // Execute action immediately for non-destructive actions
    await onModerate(action);
  };

  const handleConfirmAction = async () => {
    if (confirmDialog.action) {
      await onModerate(confirmDialog.action);
    }
    setConfirmDialog({
      isOpen: false,
      action: null,
      title: '',
      description: '',
      variant: 'default',
    });
  };

  const canApprove = review.status === 'pending';
  const canView = review.status === 'published' && review.slug;

  return (
    <TooltipProvider>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size={compact ? "sm" : "icon"}
                className="hover:bg-muted/50 transition-colors"
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open actions menu</span>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Review Actions</p>
          </TooltipContent>
        </Tooltip>
        
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="font-semibold text-primary">
            Moderation Actions
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Primary Moderation Actions */}
          {canApprove && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction({ action: 'approve' })}
                  className="text-green-600 hover:text-green-700 hover:bg-green-50 cursor-pointer"
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve & Publish
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Approve this review and make it publicly visible</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Feature Actions */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({
                  action: review.is_featured ? 'unfeature' : 'feature'
                })}
                className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50 cursor-pointer"
              >
                {review.is_featured ? (
                  <>
                    <StarOff className="mr-2 h-4 w-4" />
                    Remove Featured
                  </>
                ) : (
                  <>
                    <Star className="mr-2 h-4 w-4" />
                    Feature Review
                  </>
                )}
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>
                {review.is_featured 
                  ? 'Remove this review from featured section' 
                  : 'Promote this review to featured section'}
              </p>
            </TooltipContent>
          </Tooltip>
          
          <DropdownMenuSeparator />
          
          {/* View and Edit Actions */}
          {showViewAction && canView && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/reviews/view/${review.slug}`}
                    target="_blank"
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Review
                  </Link>
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Open this review in a new tab</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          {showEditAction && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/admin/reviews/edit/${review.id}`}
                    className="text-primary hover:text-primary/80 hover:bg-primary/10 cursor-pointer"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Review
                  </Link>
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Edit review details and moderation settings</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Bloquear/Desbloquear Review */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({ action: review.is_blocked ? 'unblock' : 'block' })}
                className={review.is_blocked ? 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer' : 'text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer'}
              >
                {review.is_blocked ? (
                  <>
                    <Unlock className="mr-2 h-4 w-4" />
                    Desbloquear Review
                  </>
                ) : (
                  <>
                    <Ban className="mr-2 h-4 w-4" />
                    Bloquear Review
                  </>
                )}
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>{review.is_blocked ? 'Desbloqueia o review para voltar a ser acessível' : 'Bloqueia o review tornando-o inacessível para todos'}</p>
            </TooltipContent>
          </Tooltip>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => !open && setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              {confirmDialog.title}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAction}
              className={confirmDialog.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </TooltipProvider>
  );
} 