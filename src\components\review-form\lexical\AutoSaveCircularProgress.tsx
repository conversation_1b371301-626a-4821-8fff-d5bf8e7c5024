'use client';

import { useState, useEffect } from 'react';
import { Check } from 'lucide-react';
import '@/components/review-form/style/autosave-progress.css';

interface AutoSaveCircularProgressProps {
  isEnabled: boolean;
  saveStatus: 'idle' | 'pending' | 'saving' | 'saved' | 'error';
  debounceMs: number;
}

export default function AutoSaveCircularProgress({
  isEnabled,
  saveStatus,
  debounceMs
}: AutoSaveCircularProgressProps) {
  const [progress, setProgress] = useState(0);
  const [showCheckmark, setShowCheckmark] = useState(false);

  // Reset progress when saving starts or status changes
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    if (saveStatus === 'pending') {
      // Reset state and start progress animation
      setShowCheckmark(false);
      setProgress(0);
      
      const startTime = Date.now();
      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const newProgress = Math.min((elapsed / debounceMs) * 100, 100);
        setProgress(newProgress);
        
        // Show checkmark immediately when circle reaches 100%
        if (newProgress >= 100) {
          setShowCheckmark(true);
          clearInterval(interval);
        }
      }, 100); // Update every 100ms for smooth animation
      
      cleanup = () => clearInterval(interval);
    } else if (saveStatus === 'saving') {
      setProgress(100);
      setShowCheckmark(true); // Show checkmark immediately when saving starts
    } else if (saveStatus === 'saved') {
      setProgress(100);
      setShowCheckmark(true);
      
      // Hide checkmark after animation
      const timeout = setTimeout(() => {
        setShowCheckmark(false);
      }, 2000);
      
      cleanup = () => clearTimeout(timeout);
    } else if (saveStatus === 'idle') {
      // Reset everything when idle
      setProgress(0);
      setShowCheckmark(false);
    } else if (saveStatus === 'error') {
      setProgress(100); // Show full circle in red for errors
      setShowCheckmark(false);
      
      // Reset after showing error for 3 seconds
      const timeout = setTimeout(() => {
        setProgress(0);
      }, 3000);
      
      cleanup = () => clearTimeout(timeout);
    }

    return cleanup;
  }, [saveStatus, debounceMs]);

  if (!isEnabled) {
    return null;
  }

  const circumference = 2 * Math.PI * 10; // radius = 10
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="autosave-progress-container">
      <div className="relative w-full h-full">
        {/* Background circle */}
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 24 24"
          fill="none"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="rgba(148, 163, 184, 0.1)"
            strokeWidth="2"
            fill="none"
          />
          
          {/* Progress circle */}
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke={
              saveStatus === 'error' 
                ? 'rgba(239, 68, 68, 0.6)' 
                : saveStatus === 'saved'
                ? 'rgba(34, 197, 94, 0.6)'
                : 'rgba(59, 130, 246, 0.5)'
            }
            strokeWidth="2"
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            className={`autosave-progress-circle ${
              saveStatus === 'saving' ? 'saving' : ''
            } ${saveStatus === 'error' ? 'error' : ''} ${
              progress > 50 ? 'autosave-glow' : ''
            }`}
          />
        </svg>

        {/* Checkmark animation */}
        <div
          className={`absolute inset-0 flex items-center justify-center autosave-checkmark-container ${
            showCheckmark
              ? 'scale-110 opacity-100'
              : 'scale-75 opacity-0'
          }`}
        >
          <div
            className={`bg-green-500 rounded-full p-1 autosave-checkmark-bg ${
              showCheckmark
                ? 'scale-100'
                : 'scale-0'
            }`}
          >
            <Check 
              size={8} 
              className={`text-white ${showCheckmark ? 'autosave-checkmark-bounce' : ''}`}
            />
          </div>
        </div>

        {/* Pulse effect on save completion */}
        {showCheckmark && (
          <div className="absolute inset-0 rounded-full bg-green-400 autosave-pulse" />
        )}
      </div>
    </div>
  );
}