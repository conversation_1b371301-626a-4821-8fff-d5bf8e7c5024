"use client";

import React, { useState, create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react";
import { cn } from "@/lib/utils";
import Link, { LinkProps } from "next/link";
import { AnimatePresence, motion, Transition } from "framer-motion";
import {
  Menu,
  X,
  LayoutDashboard,
  UserCog,
  Settings,
  LogOut,
  TrendingUp,
  CheckCircle,
  Video,
  Globe,
  Home,
  Bell,
  Search,
  ChevronDown,
  FileText,
  Gauge,
  Plus,
  ExternalLink,
  Calendar,
  Clock,
  Award,
  Activity
} from "lucide-react";

// Types
interface NavLink {
  label: string;
  href: string;
  icon: React.ReactNode;
  isActive?: boolean;
}

interface SidebarContextProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  animate: boolean;
}

interface BentoItem {
  title: string;
  description: string;
  icon: React.ReactNode;
  status?: string;
  tags?: string[];
  meta?: string;
  cta?: string;
  colSpan?: number;
  hasPersistentHover?: boolean;
  onClick?: () => void;
}

interface AnimatedBackgroundProps {
  children: React.ReactElement<{ 'data-id': string }>[] | React.ReactElement<{ 'data-id': string }>;
  defaultValue?: string;
  onValueChange?: (newActiveId: string | null) => void;
  className?: string;
  transition?: Transition;
  enableHover?: boolean;
}

interface ModernDashboardLayoutProps {
  children: ReactNode;
  user?: {
    displayName?: string;
    userName?: string;
    photoURL?: string | null;
    slug?: string;
  };
  activeTab: string;
  onTabChange: (tab: string) => void;
  stats?: {
    totalReviews: number;
    totalSurveys: number;
    averageScore: number;
    publishedReviews: number;
    draftReviews: number;
  };
}

// Context
const SidebarContext = createContext<SidebarContextProps | undefined>(undefined);

// Hooks
const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};

// Sidebar Components
const SidebarProvider = ({
  children,
  open: openProp,
  setOpen: setOpenProp,
  animate = true,
}: {
  children: ReactNode;
  open?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  animate?: boolean;
}) => {
  const [openState, setOpenState] = useState(false);

  const open = openProp !== undefined ? openProp : openState;
  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;

  return (
    <SidebarContext.Provider value={{ open, setOpen, animate }}>
      {children}
    </SidebarContext.Provider>
  );
};

const Sidebar = ({
  children,
  open,
  setOpen,
  animate,
}: {
  children: ReactNode;
  open?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  animate?: boolean;
}) => {
  return (
    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>
      {children}
    </SidebarProvider>
  );
};

const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {
  return (
    <>
      <DesktopSidebar {...props} />
      <MobileSidebar {...(props as React.ComponentProps<"div">)} />
    </>
  );
};

const DesktopSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<typeof motion.div>) => {
  const { open, setOpen, animate } = useSidebar();
  return (
    <motion.div
      className={cn(
        "h-full px-4 py-4 hidden md:flex md:flex-col bg-slate-950/95 border-r border-slate-800/50 w-[280px] flex-shrink-0 backdrop-blur-sm",
        className
      )}
      animate={{
        width: animate ? (open ? "280px" : "70px") : "280px",
      }}
      onMouseEnter={() => setOpen(true)}
      onMouseLeave={() => setOpen(false)}
      {...props}
    >
      {children}
    </motion.div>
  );
};

const MobileSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  const { open, setOpen } = useSidebar();
  return (
    <>
      <div
        className={cn(
          "h-14 px-4 py-4 flex flex-row md:hidden items-center justify-between bg-slate-950/95 border-b border-slate-800/50 w-full backdrop-blur-sm"
        )}
        {...props}
      >
        <div className="flex justify-end z-20 w-full">
          <Menu
            className="text-slate-300 cursor-pointer hover:text-purple-400 transition-colors"
            onClick={() => setOpen(!open)}
          />
        </div>
        <AnimatePresence>
          {open && (
            <motion.div
              initial={{ x: "-100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "-100%", opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className={cn(
                "fixed h-full w-full inset-0 bg-slate-950/98 p-6 z-[100] flex flex-col justify-between backdrop-blur-md",
                className
              )}
            >
              <div
                className="absolute right-6 top-6 z-50 text-slate-300 cursor-pointer hover:text-purple-400 transition-colors"
                onClick={() => setOpen(!open)}
              >
                <X />
              </div>
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

const SidebarLink = ({
  link,
  className,
  onClick,
  ...props
}: {
  link: NavLink;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  props?: LinkProps;
}) => {
  const { open, animate } = useSidebar();
  return (
    <Link
      href={link.href}
      className={cn(
        "flex items-center justify-start gap-3 group/sidebar py-3 px-2 rounded-lg text-slate-400 hover:text-purple-300 hover:bg-slate-800/50 transition-all duration-200",
        link.isActive && "text-purple-400 bg-purple-500/10 border border-purple-500/20",
        className
      )}
      onClick={onClick}
      {...props}
    >
      {link.icon}
      <motion.span
        animate={{
          display: animate ? (open ? "inline-block" : "none") : "inline-block",
          opacity: animate ? (open ? 1 : 0) : 1,
        }}
        className="text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0"
      >
        {link.label}
      </motion.span>
    </Link>
  );
};

// Logo Components
const Logo = () => {
  return (
    <Link
      href="/u/dashboard"
      className="font-normal flex space-x-3 items-center text-sm text-slate-200 py-2 relative z-20"
    >
      <div className="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg flex-shrink-0 flex items-center justify-center">
        <span className="text-white font-bold text-sm">CP</span>
      </div>
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="font-semibold text-slate-200 whitespace-pre"
      >
        CriticalPixel
      </motion.span>
    </Link>
  );
};

const LogoIcon = () => {
  return (
    <Link
      href="/u/dashboard"
      className="font-normal flex space-x-2 items-center text-sm text-slate-200 py-2 relative z-20"
    >
      <div className="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg flex-shrink-0 flex items-center justify-center">
        <span className="text-white font-bold text-sm">CP</span>
      </div>
    </Link>
  );
};

// Animated Background Component
function AnimatedBackground({
  children,
  defaultValue,
  onValueChange,
  className,
  transition,
  enableHover = false,
}: AnimatedBackgroundProps) {
  const [activeId, setActiveId] = useState<string | null>(null);
  const uniqueId = React.useId();

  const handleSetActiveId = (id: string | null) => {
    setActiveId(id);

    if (onValueChange) {
      onValueChange(id);
    }
  };

  React.useEffect(() => {
    if (defaultValue !== undefined) {
      setActiveId(defaultValue);
    }
  }, [defaultValue]);

  return React.Children.map(children, (child: any, index) => {
    const id = child.props['data-id'];

    const interactionProps = enableHover
      ? {
          onMouseEnter: () => handleSetActiveId(id),
          onMouseLeave: () => handleSetActiveId(null),
        }
      : {
          onClick: () => handleSetActiveId(id),
        };

    return React.cloneElement(
      child,
      {
        key: index,
        className: cn('relative inline-flex', child.props.className),
        'aria-selected': activeId === id,
        'data-checked': activeId === id ? 'true' : 'false',
        ...interactionProps,
      },
      <>
        <AnimatePresence initial={false}>
          {activeId === id && (
            <motion.div
              layoutId={`background-${uniqueId}`}
              className={cn('absolute inset-0', className)}
              transition={transition}
              initial={{ opacity: defaultValue ? 1 : 0 }}
              animate={{
                opacity: 1,
              }}
              exit={{
                opacity: 0,
              }}
            />
          )}
        </AnimatePresence>
        <span className='z-10'>{child.props.children}</span>
      </>
    );
  });
}

// Quick Stats Component
const QuickStats = ({ stats }: { stats?: ModernDashboardLayoutProps['stats'] }) => {
  if (!stats) return null;

  const statItems = [
    {
      label: "Reviews",
      value: stats.totalReviews,
      icon: <FileText className="w-4 h-4" />,
      color: "text-purple-400"
    },
    {
      label: "Surveys",
      value: stats.totalSurveys,
      icon: <Gauge className="w-4 h-4" />,
      color: "text-green-400"
    },
    {
      label: "Avg Score",
      value: stats.averageScore > 0 ? stats.averageScore.toFixed(1) : "—",
      icon: <TrendingUp className="w-4 h-4" />,
      color: "text-purple-400"
    }
  ];

  return (
    <div className="grid grid-cols-3 gap-3 mb-6">
      {statItems.map((item, index) => (
        <motion.div
          key={item.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-3 backdrop-blur-sm hover:border-purple-500/50 transition-all duration-300"
        >
          <div className="flex items-center gap-2 mb-1">
            <span className={item.color}>{item.icon}</span>
            <span className="text-xs text-slate-400">{item.label}</span>
          </div>
          <div className="text-lg font-semibold text-slate-200">{item.value}</div>
        </motion.div>
      ))}
    </div>
  );
};

export {
  Sidebar,
  SidebarBody,
  SidebarLink,
  Logo,
  LogoIcon,
  AnimatedBackground,
  QuickStats,
  type ModernDashboardLayoutProps,
  type NavLink,
  type BentoItem,
  type AnimatedBackgroundProps
};
