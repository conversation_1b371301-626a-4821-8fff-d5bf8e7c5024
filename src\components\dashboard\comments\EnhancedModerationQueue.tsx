'use client';

// Enhanced Comment Moderation Queue Component
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import React, { useState } from 'react';
import { CommentModerationData } from '@/types/commentModeration';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { useAuthContext } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Check, 
  X, 
  Pin, 
  Flag, 
  ExternalLink,
  MessageSquare,
  Calendar,
  User,
  CheckSquare,
  Square,
  Zap,
  Filter,
  Search
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface EnhancedModerationQueueProps {
  comments: CommentModerationData[];
  isLoading: boolean;
}

export function EnhancedModerationQueue({ 
  comments, 
  isLoading 
}: EnhancedModerationQueueProps) {
  const { user } = useAuthContext();
  const [selectedComments, setSelectedComments] = useState<Set<string>>(new Set());
  const [bulkAction, setBulkAction] = useState<'approve' | 'reject' | 'delete' | ''>('');
  const [bulkReason, setBulkReason] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'flagged' | 'recent'>('all');
  const { moderateComment, bulkModerateComments, runAutoModeration } = useCommentModeration(user?.id || '');

  // Filter comments based on search and filter criteria
  const filteredComments = comments.filter(comment => {
    const matchesSearch = searchTerm === '' || 
      comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.author_username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      comment.review_title.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterBy === 'all' || 
      (filterBy === 'flagged' && comment.flag_count > 0) ||
      (filterBy === 'recent' && new Date(comment.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000));

    return matchesSearch && matchesFilter;
  });

  const handleSelectComment = (commentId: string, selected: boolean) => {
    const newSelected = new Set(selectedComments);
    if (selected) {
      newSelected.add(commentId);
    } else {
      newSelected.delete(commentId);
    }
    setSelectedComments(newSelected);
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedComments(new Set(filteredComments.map(c => c.id)));
    } else {
      setSelectedComments(new Set());
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedComments.size === 0) return;

    await bulkModerateComments.mutateAsync({
      commentIds: Array.from(selectedComments),
      action: bulkAction,
      reason: bulkReason,
    });

    setSelectedComments(new Set());
    setBulkAction('');
    setBulkReason('');
  };

  const handleAutoModeration = async () => {
    // Get unique review IDs from filtered comments
    const reviewIds = [...new Set(filteredComments.map(c => c.review_id))];
    
    for (const reviewId of reviewIds) {
      await runAutoModeration.mutateAsync(reviewId);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                <div className="h-4 bg-slate-700 rounded w-1/2"></div>
                <div className="h-20 bg-slate-700 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2">
            No pending comments
          </h3>
          <p className="text-slate-400">
            All comments have been reviewed. Great job!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Controls */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-slate-200">
            <Filter className="h-5 w-5" />
            Moderation Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search comments, authors, or reviews..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-900/50 border-slate-600 text-slate-200"
                />
              </div>
            </div>
            <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
              <SelectTrigger className="w-48 bg-slate-900/50 border-slate-600 text-slate-200">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Comments</SelectItem>
                <SelectItem value="flagged">Flagged Only</SelectItem>
                <SelectItem value="recent">Recent (24h)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          <div className="flex items-center gap-4 p-4 bg-slate-900/30 rounded-lg">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedComments.size === filteredComments.length && filteredComments.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-slate-300">
                {selectedComments.size > 0 ? `${selectedComments.size} selected` : 'Select all'}
              </span>
            </div>

            {selectedComments.size > 0 && (
              <>
                <Select value={bulkAction} onValueChange={(value: any) => setBulkAction(value)}>
                  <SelectTrigger className="w-40 bg-slate-800 border-slate-600">
                    <SelectValue placeholder="Bulk action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="approve">Approve</SelectItem>
                    <SelectItem value="reject">Reject</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Reason (optional)"
                  value={bulkReason}
                  onChange={(e) => setBulkReason(e.target.value)}
                  className="flex-1 bg-slate-800 border-slate-600 text-slate-200"
                />

                <Button
                  onClick={handleBulkAction}
                  disabled={!bulkAction || bulkModerateComments.isPending}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Apply to {selectedComments.size}
                </Button>
              </>
            )}

            <Button
              onClick={handleAutoModeration}
              disabled={runAutoModeration.isPending}
              variant="outline"
              className="border-green-600 text-green-400 hover:bg-green-600/20"
            >
              <Zap className="h-4 w-4 mr-2" />
              Auto-Moderate
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <div className="space-y-4">
        {filteredComments.map((comment) => (
          <Card key={comment.id} className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600/50 transition-colors">
            <CardContent className="p-6">
              {/* Selection and Header */}
              <div className="flex items-start gap-4 mb-4">
                <Checkbox
                  checked={selectedComments.has(comment.id)}
                  onCheckedChange={(checked) => handleSelectComment(comment.id, checked as boolean)}
                />
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-slate-400" />
                        <span className="font-medium text-slate-200">
                          {comment.author?.display_name || comment.author_name}
                        </span>
                        <span className="text-slate-400">@{comment.author_username}</span>
                      </div>
                      {comment.flag_count > 0 && (
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <Flag className="h-3 w-3" />
                          {comment.flag_count} reports
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-slate-400">
                      <Calendar className="h-4 w-4" />
                      {formatDistanceToNow(new Date(comment.created_at))} ago
                    </div>
                  </div>

                  {/* Review Context */}
                  <div className="mb-4 p-3 bg-slate-900/50 rounded-lg border border-slate-700/50">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-slate-400">Comment on:</p>
                        <p className="font-medium text-slate-200">{comment.review_title}</p>
                      </div>
                      <Link 
                        href={`/reviews/view/${comment.review_slug}`}
                        className="text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </div>
                  </div>

                  {/* Comment Content */}
                  <div className="mb-4 p-4 bg-slate-900/30 rounded-lg border border-slate-700/30">
                    <p className="text-slate-300 whitespace-pre-wrap leading-relaxed">
                      {comment.content}
                    </p>
                  </div>

                  {/* Individual Actions */}
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => moderateComment.mutateAsync({
                        commentId: comment.id,
                        action: 'approve'
                      })}
                      className="bg-green-600 hover:bg-green-700 text-white"
                      disabled={moderateComment.isPending}
                      size="sm"
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                    
                    <Button
                      onClick={() => moderateComment.mutateAsync({
                        commentId: comment.id,
                        action: 'reject'
                      })}
                      variant="destructive"
                      disabled={moderateComment.isPending}
                      size="sm"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                    
                    <Button
                      onClick={() => moderateComment.mutateAsync({
                        commentId: comment.id,
                        action: 'pin'
                      })}
                      variant="outline"
                      className="border-yellow-600 text-yellow-400 hover:bg-yellow-600/20"
                      disabled={moderateComment.isPending}
                      size="sm"
                    >
                      <Pin className="h-4 w-4 mr-2" />
                      Pin
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
