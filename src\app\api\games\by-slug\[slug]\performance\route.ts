import { NextRequest, NextResponse } from 'next/server';
import { getGameBySlug, getGamePerformanceData } from '@/lib/services/gameService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    
    const limit = parseInt(searchParams.get('limit') || '50');
    
    // Get game first
    const game = await getGameBySlug(slug);
    
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const performanceData = await getGamePerformanceData(game.id, limit);

    // Calculate aggregated performance metrics
    const performanceStats = {
      total_surveys: performanceData.length,
      average_fps: performanceData.length > 0 
        ? performanceData.reduce((sum, p) => sum + (p.fps_average || 0), 0) / performanceData.filter(p => p.fps_average).length
        : null,
      average_performance_rating: performanceData.length > 0
        ? performanceData.reduce((sum, p) => sum + (p.performance_rating || 0), 0) / performanceData.filter(p => p.performance_rating).length
        : null,
      platforms: [...new Set(performanceData.map(p => p.platform).filter(Boolean))],
      device_types: [...new Set(performanceData.map(p => p.device_type).filter(Boolean))],
      popular_gpus: getTopItems(performanceData.map(p => p.gpu).filter(Boolean), 5),
      popular_cpus: getTopItems(performanceData.map(p => p.cpu).filter(Boolean), 5),
    };

    return NextResponse.json({
      performance_data: performanceData,
      performance_stats: performanceStats
    });
  } catch (error) {
    console.error('Error fetching game performance data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to get top items by frequency
function getTopItems(items: string[], limit: number): Array<{ name: string; count: number }> {
  const counts = items.reduce((acc, item) => {
    acc[item] = (acc[item] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(counts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}