'use client';

import { useState, useEffect } from 'react';

export const useSteamGridDBIcon = (gameName: string) => {
  const [iconUrl, setIconUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!gameName?.trim()) {
      setIconUrl(null);
      return;
    }

    const fetchIcon = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log(`🔍 Searching SteamGridDB for: "${gameName}"`);
        
        // Clean up game name for better matching
        const cleanGameName = gameName
          .replace(/[^\w\s]/g, '') // Remove special characters
          .replace(/\s+/g, ' ') // Replace multiple spaces with single space
          .trim();
        
        console.log(`🧹 Cleaned game name: "${cleanGameName}"`);
        
        // Add a timeout to prevent hanging requests
        const timeoutController = new AbortController();
        const timeoutId = setTimeout(() => timeoutController.abort(), 10000); // 10 second timeout

        // Search for the game using API route
        const searchResponse = await fetch('/api/steamgriddb/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query: cleanGameName }),
          signal: timeoutController.signal
        });

        clearTimeout(timeoutId);

        if (!searchResponse.ok) {
          if (searchResponse.status === 404) {
            console.log(`❌ SteamGridDB API endpoint not found for: "${gameName}"`);
            setIconUrl(null);
            return;
          }
          throw new Error(`Search failed: ${searchResponse.status}`);
        }

        const searchResult = await searchResponse.json();
        const games = searchResult.data || searchResult;
        
        console.log(`🎮 Found ${games?.length || 0} games for "${gameName}":`, games);
        
        if (!games || !Array.isArray(games) || games.length === 0) {
          console.log(`❌ No games found for: "${gameName}"`);
          setIconUrl(null);
          return;
        }

        // Get icons for the first matching game using API route
        const firstGame = games[0];
        console.log(`🎯 Using first game:`, firstGame);
        
        if (!firstGame || (typeof firstGame.id !== 'number' && !firstGame.id)) {
          console.log(`❌ Invalid game ID for: "${gameName}"`);
          setIconUrl(null);
          return;
        }

        // Try to get icons without any style filter first (most likely to have results)
        const iconsTimeoutController = new AbortController();
        const iconsTimeoutId = setTimeout(() => iconsTimeoutController.abort(), 10000);

        const iconsResponse = await fetch('/api/steamgriddb/icons', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            gameId: firstGame.id,
            limit: 3 // Get a few options
          }),
          signal: iconsTimeoutController.signal
        });

        clearTimeout(iconsTimeoutId);

        if (!iconsResponse.ok) {
          console.log(`❌ Failed to fetch icons for game ID ${firstGame.id}`);
          setIconUrl(null);
          return;
        }

        const iconsResult = await iconsResponse.json();
        const icons = iconsResult.data || iconsResult;

        console.log(`🖼️ Found ${icons?.length || 0} icons for "${gameName}":`, icons);

        if (Array.isArray(icons) && icons.length > 0) {
          // Prefer white_logo style if available, otherwise use the first icon
          const preferredIcon = icons.find(icon => icon.style === 'white_logo') || icons[0];
          
          if (preferredIcon && (preferredIcon.thumb || preferredIcon.url)) {
            const iconUrl = preferredIcon.thumb || preferredIcon.url;
            console.log(`✅ Using icon for "${gameName}":`, iconUrl);
            setIconUrl(iconUrl);
            return;
          }
        }

        console.log(`❌ No usable icons found for: "${gameName}"`);
        setIconUrl(null);
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          console.log(`⏰ Request timeout for: "${gameName}"`);
          setIconUrl(null);
        } else {
          console.error(`💥 Error fetching icon for "${gameName}":`, err);
          setError(err instanceof Error ? err.message : 'Failed to fetch icon');
          setIconUrl(null);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchIcon();
  }, [gameName]);

  return { iconUrl, isLoading, error };
};