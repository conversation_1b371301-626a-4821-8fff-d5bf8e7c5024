import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "animate-pulse rounded-md bg-slate-700/40",
          className
        )}
        {...props}
      />
    );
  }
);
Skeleton.displayName = "Skeleton";

// Profile Header Skeleton
const ProfileHeaderSkeleton = ({ theme }: { theme?: any }) => (
  <div 
    className="relative overflow-hidden rounded-xl border border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur"
    style={{
      background: theme 
        ? `linear-gradient(135deg, ${theme.colors.primary}20, ${theme.colors.secondary}10)`
        : 'linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(147, 51, 234, 0.1))',
      borderColor: theme ? `${theme.colors.accent}30` : 'rgba(139, 92, 246, 0.3)'
    }}
  >
    {/* Banner Skeleton */}
    <Skeleton className="w-full h-32 sm:h-48 md:h-64 lg:h-80 xl:h-96 max-h-[450px] rounded-none" />
    
    {/* User Info Container */}
    <div className="relative p-4 sm:p-6">
      <div className="flex flex-col lg:flex-row items-center lg:items-center gap-4 lg:gap-6">
        {/* Avatar + Profile Info */}
        <div className="flex flex-col sm:flex-row items-center sm:items-center gap-4 sm:gap-6 flex-1">
          {/* Avatar Skeleton */}
          <Skeleton className="h-24 w-24 rounded-full -mt-12 mx-auto sm:mx-0 flex-shrink-0" />
          
          {/* Profile Info Skeleton */}
          <div className="flex-1 min-w-0 text-center sm:text-left flex flex-col justify-center">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
              <Skeleton className="h-8 w-48 mx-auto sm:mx-0" />
              <Skeleton className="h-4 w-16 mx-auto sm:mx-0" />
            </div>
            <div className="flex flex-wrap justify-center sm:justify-start items-center gap-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        </div>
        
        {/* Social Media Buttons Skeleton */}
        <div className="flex items-center gap-2">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-10 w-10 rounded-lg" />
          ))}
        </div>
      </div>
    </div>
  </div>
);

// YouTube Module Skeleton
const YouTubeModuleSkeleton = ({ theme }: { theme?: any }) => (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex items-center gap-3">
      <Skeleton 
        className="p-2 rounded-lg h-9 w-9"
        style={{ backgroundColor: theme ? `${theme.colors.primary}20` : 'rgba(139, 92, 246, 0.2)' }}
      />
      <div>
        <Skeleton className="h-6 w-32 mb-2" />
        <Skeleton className="h-4 w-48" />
      </div>
    </div>
    
    {/* Channel Header */}
    <div className="flex items-center gap-4 p-6 rounded-xl border border-gray-800 bg-gray-900/50">
      <Skeleton className="w-16 h-16 rounded-full" />
      <div className="flex-1">
        <Skeleton className="h-6 w-48 mb-2" />
        <Skeleton className="h-4 w-64 mb-2" />
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
      <Skeleton className="h-10 w-20 rounded-lg" />
    </div>
    
    {/* Videos Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="border rounded-lg overflow-hidden bg-gray-900/50 border-gray-800">
          <Skeleton className="w-full h-40" />
          <div className="p-4">
            <Skeleton className="h-4 w-full mb-2" />
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="h-3 w-12" />
                <Skeleton className="h-3 w-12" />
              </div>
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Gallery Module Skeleton
const GalleryModuleSkeleton = ({ theme }: { theme?: any }) => (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Skeleton 
          className="p-2 rounded-lg h-9 w-9"
          style={{ backgroundColor: theme ? `${theme.colors.primary}20` : 'rgba(139, 92, 246, 0.2)' }}
        />
        <div>
          <Skeleton className="h-6 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-8 w-16 rounded-lg" />
        <Skeleton className="h-8 w-20 rounded-lg" />
      </div>
    </div>
    
    {/* Media Grid */}
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="bg-slate-900/60 border-slate-700/50 rounded-lg overflow-hidden">
          <Skeleton className="w-full aspect-video" />
          <div className="p-3">
            <Skeleton className="h-4 w-full mb-2" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-3 w-8" />
              <Skeleton className="h-3 w-8" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Content Modules Skeleton
const ContentModulesSkeleton = ({ theme }: { theme?: any }) => (
  <div className="space-y-6">
    {/* Header */}
    <div className="flex items-center gap-3">
      <Skeleton 
        className="p-2 rounded-lg h-9 w-9"
        style={{ backgroundColor: theme ? `${theme.colors.primary}20` : 'rgba(139, 92, 246, 0.2)' }}
      />
      <div>
        <Skeleton className="h-6 w-40 mb-2" />
        <Skeleton className="h-4 w-56" />
      </div>
    </div>
    
    {/* Stats Grid */}
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50">
          <div className="flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-lg" />
            <div>
              <Skeleton className="h-6 w-12 mb-1" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
    
    {/* Tabs */}
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-1 bg-gray-900/50 rounded-lg p-1">
        {[...Array(3)].map((_, i) => (
          <Skeleton key={i} className="h-10 w-20 rounded-lg" />
        ))}
      </div>
      <Skeleton className="h-8 w-16 rounded-lg" />
    </div>
    
    {/* Content Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-gray-900/50 border-gray-800/50 rounded-lg overflow-hidden">
          <Skeleton className="w-full h-32" />
          <div className="p-4">
            <Skeleton className="h-5 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-3" />
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="h-3 w-8" />
                <Skeleton className="h-3 w-8" />
                <Skeleton className="h-3 w-8" />
              </div>
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export { 
  Skeleton, 
  ProfileHeaderSkeleton, 
  YouTubeModuleSkeleton, 
  GalleryModuleSkeleton, 
  ContentModulesSkeleton 
};