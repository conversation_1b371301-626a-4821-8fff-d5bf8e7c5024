// src/lib/utils/dateUtils.ts
export const formatDate = (dateInput: string | Date | any | undefined): string => {
  if (dateInput && typeof dateInput === 'string') {
    dateInput = dateInput.trim(); // Trim whitespace
  }

  if (!dateInput) return 'N/A';

  // Check if it's a Firestore Timestamp and convert
  if (typeof dateInput === 'object' && 'toDate' in dateInput) {
    try {
      return dateInput.toDate().toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
    } catch (e) {
      // console.error("Error formatting Firestore Timestamp:", e);
      return 'Invalid Timestamp Date';
    }
  }

  if (typeof dateInput === 'string') {
    const mmYYYYRegex = /^(\d{2})\/(\d{4})$/; // Matches MM/YYYY
    const match = dateInput.match(mmYYYYRegex);

    if (match) {
      const month = parseInt(match[1], 10);
      const year = parseInt(match[2], 10);

      if (month >= 1 && month <= 12 && year > 1000 && year < 3000) {
        const tempDate = new Date(year, month - 1, 1);
        const monthName = tempDate.toLocaleDateString(undefined, { month: 'long' });
        return `${monthName} ${year}`;
      } else {
        return 'Invalid MM/YYYY Date';
      }
    }
  }

  try {
    // Attempt to parse other string formats or use if it's a Date object
    const d = new Date(dateInput as string | Date);
    if (isNaN(d.getTime())) { // Check if date is invalid
        return 'Invalid Date';
    }
    // For full dates, display day, month, year.
    return d.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
  } catch (e) {
    // console.error("Error formatting date string or object:", e);
    return 'Invalid Date';
  }
};
