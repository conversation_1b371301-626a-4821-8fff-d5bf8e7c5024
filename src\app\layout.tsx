import type { Metadata } from 'next';
import { Geist, <PERSON><PERSON>st_Mono } from 'next/font/google';
import './globals.css';
import '@/components/review-form/style/lexical.css';
import '@/components/review-form/style/lexicaltoolbar.css';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/auth-context';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Providers } from './providers';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Critical Pixel',
  description: 'Video game reviews and discussions.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <AuthProvider>
          <Providers>
            <div className="min-h-screen">
              <Navbar />
              <main className="py-8 pt-24"> {/* pt-24 to offset fixed navbar */}
                {children}
              </main>
              <Footer />
            </div>
            <Toaster />
          </Providers>
        </AuthProvider>
      </body>
    </html>
  );
}