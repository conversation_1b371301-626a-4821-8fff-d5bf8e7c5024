'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Trophy, Medal, TrendingUp, Star } from 'lucide-react';
import '../style/trendingSidebar.css';

interface TrendingGame {
  id: string;
  title: string;
  coverImage: string;
  rating: number;
  reviewCount: number;
  category: 'masterpiece' | 'excellent' | 'popular';
  percentage: number;
}

const TrendingGames = () => {
  const [hoveredGame, setHoveredGame] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const trendingGames: TrendingGame[] = [
    { id: "game1", title: "Cyberpunk 2077", coverImage: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=300&h=400&fit=crop", rating: 4.8, reviewCount: 2847, category: 'masterpiece', percentage: 100 },
    { id: "game2", title: "The Witcher 3", coverImage: "https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=300&h=400&fit=crop", rating: 4.6, reviewCount: 2156, category: 'masterpiece', percentage: 76 },
    { id: "game3", title: "Elden Ring", coverImage: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop", rating: 4.4, reviewCount: 1923, category: 'excellent', percentage: 68 },
    { id: "game4", title: "Red Dead 2", coverImage: "https://images.unsplash.com/photo-1509198397868-475647b2a1e5?w=300&h=400&fit=crop", rating: 4.2, reviewCount: 1654, category: 'excellent', percentage: 58 },
    { id: "game5", title: "Hades", coverImage: "https://images.unsplash.com/photo-1556438064-2d7646166914?w=300&h=400&fit=crop", rating: 4.0, reviewCount: 1289, category: 'popular', percentage: 45 },
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'masterpiece': return Trophy;
      case 'excellent': return Medal;
      case 'popular': return TrendingUp;
      default: return Star;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'masterpiece': return 'text-yellow-400';
      case 'excellent': return 'text-purple-400';
      case 'popular': return 'text-cyan-400';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="trending-sidebar-container">
      <div className="trending-sidebar-header">
        <div className="trending-sidebar-header-flex">
          <div className="trending-sidebar-divider" />
          <span className="trending-sidebar-title trending-sidebar-title-green">
            <span className="trending-sidebar-title-accent">&gt;</span> Hot Games
          </span>
          <div className="trending-sidebar-divider" />
        </div>
      </div>

      <div className="trending-sidebar-main-container">
        <div className="trending-sidebar-list">
          {trendingGames.map((game, idx) => {
            const CategoryIcon = getCategoryIcon(game.category);
            const categoryColor = getCategoryColor(game.category);
            const animDelay = idx * 75;
            
            return (
              <Link
                key={game.id}
                href={`/games/${game.title.toLowerCase().replace(/\s+/g, '-')}`}
                className={`trending-sidebar-item ${mounted ? 'trending-sidebar-animate-fade-slide' : ''}`}
                onMouseEnter={() => setHoveredGame(game.id)}
                onMouseLeave={() => setHoveredGame(null)}
                style={{
                  animationDelay: `${animDelay}ms`
                }}
              >
                <div className="trending-sidebar-item-content">
                  <div className="trending-sidebar-item-left">
                    <div className="trending-sidebar-icon-container">
                      <img 
                        src={game.coverImage} 
                        alt={`${game.title} cover`}
                        className="trending-sidebar-cover"
                      />
                      <div className={`trending-sidebar-badge ${game.category === 'masterpiece' ? 'masterpiece-glow' : ''}`}>
                        <CategoryIcon 
                          size={5} 
                          className={categoryColor}
                        />
                      </div>
                    </div>
                    <span className="trending-sidebar-name">{game.title}</span>
                  </div>
                </div>
                <div className="trending-sidebar-progress-container">
                  <div
                    className="trending-sidebar-progress-bar"
                    style={{ 
                      width: hoveredGame === game.id ? `${game.percentage}%` : '0%'
                    }}
                  />
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TrendingGames;