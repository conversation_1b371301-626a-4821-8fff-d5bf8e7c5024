'use client';

// Comment Moderation Modal Component
// Date: 28/06/2025
// Purpose: Forum-style modal for moderating comments of a specific review within dashboard

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CommentModerationData } from '@/types/commentModeration';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { useAuthContext } from '@/contexts/auth-context';
import { useForumPosts } from '@/hooks/useForumPosts';
import { useForumThread } from '@/hooks/useForumMutations';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  MessageSquare,
  Search,
  Calendar,
  User,
  Flag,
  Clock,
  CheckCircle,
  XCircle,
  Pin,
  PinOff,
  Trash2,
  Loader2,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  ArrowLeft,
  TrendingUp
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ReviewSummary {
  id: string;
  title: string;
  slug: string;
  game_name: string;
  created_at: string;
  total_comments: number;
  flagged_comments: number;
  pending_comments: number;
  recent_activity: string | null;
  comments: CommentModerationData[];
}

interface CommentModerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  review: ReviewSummary | null;
  comments: CommentModerationData[];
}

type ViewMode = 'topics' | 'thread';

export function CommentModerationModal({
  isOpen,
  onClose,
  review,
  comments
}: CommentModerationModalProps) {
  const { user } = useAuthContext();
  const { moderateComment } = useCommentModeration(user?.uid || '');

  const [currentView, setCurrentView] = useState<ViewMode>('topics');
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<string>('newest');

  // Get forum posts for this review
  const { data: forumPosts, isLoading: postsLoading } = useForumPosts(review?.id || '');

  // Get thread data when viewing a specific topic
  const { data: threadData, isLoading: threadLoading } = useForumThread(selectedTopicId || '');

  // Filter forum posts for moderation
  const moderationPosts = useMemo(() => {
    if (!forumPosts) return [];
    return forumPosts.filter(post => {
      if (!searchQuery) return true;

      // For deleted posts, only search by title and author, not content
      const searchableContent = post.is_deleted ? '' : post.content.toLowerCase();
      const searchLower = searchQuery.toLowerCase();

      return (
        post.title.toLowerCase().includes(searchLower) ||
        searchableContent.includes(searchLower) ||
        post.author?.display_name?.toLowerCase().includes(searchLower)
      );
    });
  }, [forumPosts, searchQuery]);

  // Sort forum posts
  const sortedPosts = useMemo(() => {
    if (!moderationPosts) return [];

    return [...moderationPosts].sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'most_replies':
          return (b.reply_count || 0) - (a.reply_count || 0);
        case 'most_upvotes':
          return (b.upvotes || 0) - (a.upvotes || 0);
        default:
          return 0;
      }
    });
  }, [moderationPosts, sortBy]);

  const handleTopicClick = (topicId: string) => {
    setSelectedTopicId(topicId);
    setCurrentView('thread');
  };

  const handleBackToTopics = () => {
    setCurrentView('topics');
    setSelectedTopicId(null);
  };

  const handleGoToReview = () => {
    if (review) {
      window.open(`/reviews/view/${review.slug}#comments`, '_blank');
    }
  };

  const handleModeration = async (
    commentId: string,
    action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin'
  ) => {
    try {
      await moderateComment.mutateAsync({
        commentId,
        action,
        notes: '',
      });

      const actionText = action === 'approve' ? 'restored' : `${action}d`;
      toast({
        title: "Post moderated",
        description: `Post has been ${actionText} successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to moderate post. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!review) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 text-white">
        <DialogHeader className="border-b border-slate-700/40 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {currentView === 'thread' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToTopics}
                  className="text-slate-400 hover:text-white"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
              )}
              <div className="flex-1">
                <DialogTitle className="font-mono text-lg text-slate-200">
                  <span className="text-purple-400">//</span>
                  <span className="ml-2">
                    {currentView === 'topics' ? 'Discussion Topics' : 'Thread Moderation'}
                  </span>
                </DialogTitle>
                <DialogDescription className="text-slate-400 font-mono text-sm mt-1">
                  {review.game_name} • {review.title.length > 50 ? review.title.substring(0, 50) + '...' : review.title}
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGoToReview}
              className="border-slate-600/50 text-slate-300 hover:bg-slate-800/50"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Review
            </Button>
          </div>
        </DialogHeader>

        <div className="flex flex-col h-full max-h-[70vh]">
          <AnimatePresence mode="wait">
            {currentView === 'topics' && (
              <motion.div
                key="topics"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col h-full"
              >
                {/* Stats and Controls */}
                <div className="flex items-center justify-between mb-4 gap-4">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="font-mono text-xs border-slate-600/50">
                      {sortedPosts.length} topics
                    </Badge>
                    {review.flagged_comments > 0 && (
                      <Badge variant="outline" className="font-mono text-xs bg-red-500/10 text-red-300 border-red-500/30">
                        <Flag className="h-3 w-3 mr-1" />
                        {review.flagged_comments} flagged
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                      <Input
                        placeholder="Search topics..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 w-64 bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder-slate-400"
                      />
                    </div>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-40 bg-slate-800/50 border-slate-600/50 text-slate-200">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600/50">
                        <SelectItem value="newest">Newest First</SelectItem>
                        <SelectItem value="oldest">Oldest First</SelectItem>
                        <SelectItem value="most_replies">Most Replies</SelectItem>
                        <SelectItem value="most_upvotes">Most Upvotes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Topics List */}
                <div className="flex-1 overflow-y-auto space-y-3 pr-2">
                  {postsLoading ? (
                    <div className="text-center py-8 text-slate-400">
                      <Loader2 className="h-8 w-8 mx-auto mb-3 animate-spin" />
                      <p className="font-mono">Loading discussion topics...</p>
                    </div>
                  ) : sortedPosts.length === 0 ? (
                    <div className="text-center py-8 text-slate-400">
                      <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p className="font-mono">
                        {searchQuery ? 'No topics match your search.' : 'No discussion topics found.'}
                      </p>
                    </div>
                  ) : (
                    sortedPosts.map((post) => (
                      <TopicCard
                        key={post.id}
                        post={post}
                        onClick={() => handleTopicClick(post.id)}
                        onModerate={handleModeration}
                      />
                    ))
                  )}
                </div>
              </motion.div>
            )}

            {currentView === 'thread' && selectedTopicId && (
              <motion.div
                key="thread"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col h-full"
              >
                <ThreadModerationView
                  topicId={selectedTopicId}
                  threadData={threadData}
                  isLoading={threadLoading}
                  onModerate={handleModeration}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Topic Card Component - Shows individual discussion topics
interface TopicCardProps {
  post: any; // ForumPost type
  onClick: () => void;
  onModerate: (commentId: string, action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin') => Promise<void>;
}

function TopicCard({ post, onClick, onModerate }: TopicCardProps) {
  const score = post.upvotes - post.downvotes;
  const needsModeration = post.flag_count > 0 || post.is_deleted;

  return (
    <Card
      className={cn(
        "transition-all duration-200 cursor-pointer",
        needsModeration
          ? "bg-red-950/20 border-red-800/40 hover:bg-red-950/30 hover:border-red-700/50"
          : "bg-slate-800/50 border-slate-700/50 hover:bg-slate-800/70 hover:border-slate-600/50"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex gap-3 items-start">
          {/* Avatar */}
          <img
            src={post.author?.avatar_url || '/imgs/profile.svg'}
            alt={post.author?.display_name || 'User'}
            className="h-10 w-10 rounded-lg object-cover flex-shrink-0"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/imgs/profile.svg';
            }}
          />

          {/* Content */}
          <div className="flex-1 min-w-0">
            {/* Title and Badges */}
            <div className="flex items-center gap-2 mb-1 flex-wrap">
              <h3 className="font-semibold text-slate-200 hover:text-purple-400 transition-colors text-sm">
                {post.title}
              </h3>

              {post.is_hot && (
                <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30 text-xs px-1.5 py-0.5 h-5">
                  <TrendingUp className="h-2.5 w-2.5 mr-0.5" />
                  Hot
                </Badge>
              )}

              {post.is_pinned && (
                <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs px-1.5 py-0.5 h-5">
                  <Pin className="h-2.5 w-2.5 mr-0.5" />
                  Pinned
                </Badge>
              )}

              {post.is_deleted && (
                <Badge variant="outline" className="text-xs bg-gray-500/10 text-gray-300 border-gray-500/30">
                  Deleted
                </Badge>
              )}

              {post.flag_count > 0 && (
                <Badge variant="outline" className="text-xs bg-red-500/10 text-red-300 border-red-500/30">
                  <Flag className="h-2.5 w-2.5 mr-0.5" />
                  {post.flag_count}
                </Badge>
              )}
            </div>

            {/* Metadata */}
            <div className="flex items-center gap-3 text-xs text-slate-500">
              <div className="flex items-center gap-1">
                <User className="w-2.5 h-2.5" />
                <span className="truncate max-w-20">{post.author?.display_name || 'Anonymous'}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-2.5 h-2.5" />
                <span>{formatDistanceToNow(new Date(post.created_at))} ago</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageSquare className="w-2.5 h-2.5" />
                <span className="font-mono">{post.reply_count || 0}</span>
              </div>
            </div>
          </div>

          {/* Score Badge */}
          <div className="flex items-center gap-2">
            <div className="text-center">
              <div className={cn(
                "text-xs font-mono px-2 py-1 rounded",
                score > 0 ? "text-green-400 bg-green-500/10" :
                score < 0 ? "text-red-400 bg-red-500/10" :
                "text-slate-400 bg-slate-500/10"
              )}>
                {score > 0 ? `+${score}` : score}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Thread Moderation View Component
interface ThreadModerationViewProps {
  topicId: string;
  threadData: any; // ForumThread type
  isLoading: boolean;
  onModerate: (commentId: string, action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin') => Promise<void>;
}

function ThreadModerationView({ topicId, threadData, isLoading, onModerate }: ThreadModerationViewProps) {
  if (isLoading) {
    return (
      <div className="text-center py-8 text-slate-400">
        <Loader2 className="h-8 w-8 mx-auto mb-3 animate-spin" />
        <p className="font-mono">Loading thread...</p>
      </div>
    );
  }

  if (!threadData) {
    return (
      <div className="text-center py-8 text-slate-400">
        <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
        <p className="font-mono">Thread not found.</p>
      </div>
    );
  }

  const { post, replies } = threadData;

  return (
    <div className="flex-1 overflow-y-auto space-y-4 pr-2">
      {/* Main Post */}
      <ThreadPostCard post={post} onModerate={onModerate} isMainPost />

      {/* Replies */}
      {replies && replies.length > 0 && (
        <div className="space-y-3">
          <div className="text-sm font-mono text-slate-400 border-b border-slate-700/30 pb-2">
            <span className="text-slate-500">//</span> Replies ({replies.length})
          </div>
          {replies.map((reply: any) => (
            <ThreadPostCard
              key={reply.id}
              post={reply}
              onModerate={onModerate}
              isMainPost={false}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Thread Post Card Component
interface ThreadPostCardProps {
  post: any;
  onModerate: (commentId: string, action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin') => Promise<void>;
  isMainPost: boolean;
}

function ThreadPostCard({ post, onModerate, isMainPost }: ThreadPostCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const needsModeration = post.flag_count > 0 || post.is_deleted;

  // Handle deleted content immediately - don't show original content
  const displayContent = post.is_deleted
    ? "This comment was removed by moderation"
    : post.content;

  const contentPreview = post.is_deleted
    ? "This comment was removed by moderation"
    : (post.content.length > 200 ? post.content.substring(0, 200) + '...' : post.content);

  return (
    <Card
      className={cn(
        "transition-all duration-200",
        needsModeration
          ? "bg-red-950/20 border-red-800/40"
          : "bg-slate-800/50 border-slate-700/50",
        isMainPost && "border-l-4 border-l-purple-500/50"
      )}
    >
      <CardContent className="p-4">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <img
              src={post.author?.avatar_url || '/imgs/profile.svg'}
              alt={post.author?.display_name || 'User'}
              className="h-8 w-8 rounded-lg object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/imgs/profile.svg';
              }}
            />
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-slate-200 text-sm">
                  {post.author?.display_name || 'Anonymous'}
                </span>
                {isMainPost && (
                  <Badge variant="outline" className="text-xs bg-purple-500/10 text-purple-300 border-purple-500/30">
                    Topic
                  </Badge>
                )}
              </div>
              <div className="text-xs text-slate-400">
                {formatDistanceToNow(new Date(post.created_at))} ago
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {post.is_deleted && (
              <Badge variant="outline" className="text-xs bg-gray-500/10 text-gray-300 border-gray-500/30">
                Deleted
              </Badge>
            )}
            {post.is_pinned && (
              <Badge variant="outline" className="text-xs bg-blue-500/10 text-blue-300 border-blue-500/30">
                <Pin className="h-3 w-3 mr-1" />
                Pinned
              </Badge>
            )}
            {post.flag_count > 0 && (
              <Badge variant="outline" className="text-xs bg-red-500/10 text-red-300 border-red-500/30">
                <Flag className="h-3 w-3 mr-1" />
                {post.flag_count}
              </Badge>
            )}
          </div>
        </div>

        {/* Title (for main post) */}
        {isMainPost && post.title && (
          <h3 className="font-semibold text-slate-200 text-base mb-2">{post.title}</h3>
        )}

        {/* Content */}
        <div className="mb-3">
          <p className={cn(
            "text-sm leading-relaxed whitespace-pre-wrap",
            post.is_deleted ? "text-slate-400 italic" : "text-slate-300"
          )}>
            {isExpanded ? displayContent : contentPreview}
          </p>
          {!post.is_deleted && post.content.length > 200 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-purple-400 hover:text-purple-300 text-xs mt-1 p-0 h-auto"
            >
              {isExpanded ? 'Show less' : 'Show more'}
            </Button>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center gap-4 mb-3 text-xs text-slate-400">
          <div className="flex items-center gap-1">
            <ThumbsUp className="h-3 w-3" />
            <span>{post.upvotes || 0}</span>
          </div>
          <div className="flex items-center gap-1">
            <ThumbsDown className="h-3 w-3" />
            <span>{post.downvotes || 0}</span>
          </div>
        </div>

        {/* Moderation Actions */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onModerate(post.id, post.is_pinned ? 'unpin' : 'pin')}
            className="border-slate-600/50 text-slate-300 hover:bg-slate-800/50"
          >
            {post.is_pinned ? (
              <>
                <PinOff className="h-3 w-3 mr-1" />
                Unpin
              </>
            ) : (
              <>
                <Pin className="h-3 w-3 mr-1" />
                Pin
              </>
            )}
          </Button>

          {!post.is_deleted ? (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onModerate(post.id, 'delete')}
              className="border-red-600/50 text-red-300 hover:bg-red-600/20"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Delete
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onModerate(post.id, 'approve')}
              className="border-green-600/50 text-green-300 hover:bg-green-600/20"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Restore
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
