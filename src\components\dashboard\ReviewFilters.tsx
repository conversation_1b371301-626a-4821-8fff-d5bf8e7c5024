'use client';

import { useState } from 'react';
import {
  X,
  Calendar,
  Star,
  Monitor,
  Tag,
  FileText,
  SortAsc,
  SortDesc,
  RotateCcw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import type { DashboardFilters } from '@/types/dashboard';

export interface ReviewFiltersProps {
  filters: DashboardFilters['reviews'];
  availableOptions: {
    platforms: string[];
    genres: string[];
  };
  onFilterChange: (filters: Partial<DashboardFilters['reviews']>) => void;
  onClearFilters: () => void;
  onToggleSortOrder: () => void;
}

export function ReviewFilters({
  filters,
  availableOptions,
  onFilterChange,
  onClearFilters,
  onToggleSortOrder
}: ReviewFiltersProps) {
  const [scoreRange, setScoreRange] = useState<[number, number]>(
    filters.scoreRange || [0, 10]
  );
  const [dateRange, setDateRange] = useState<{
    start: string;
    end: string;
  }>({
    start: filters.dateRange?.[0]?.toISOString().split('T')[0] || '',
    end: filters.dateRange?.[1]?.toISOString().split('T')[0] || ''
  });

  const handleScoreRangeChange = (index: 0 | 1, value: number) => {
    const newRange: [number, number] = [...scoreRange];
    newRange[index] = value;
    
    // Ensure min <= max
    if (index === 0 && value > newRange[1]) {
      newRange[1] = value;
    } else if (index === 1 && value < newRange[0]) {
      newRange[0] = value;
    }
    
    setScoreRange(newRange);
    onFilterChange({ scoreRange: newRange });
  };

  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    const newDateRange = { ...dateRange, [field]: value };
    setDateRange(newDateRange);
    
    if (newDateRange.start && newDateRange.end) {
      onFilterChange({
        dateRange: [new Date(newDateRange.start), new Date(newDateRange.end)]
      });
    } else if (!newDateRange.start && !newDateRange.end) {
      onFilterChange({ dateRange: undefined });
    }
  };

  const clearDateRange = () => {
    setDateRange({ start: '', end: '' });
    onFilterChange({ dateRange: undefined });
  };

  const clearScoreRange = () => {
    setScoreRange([0, 10]);
    onFilterChange({ scoreRange: undefined });
  };

  const hasActiveFilters = !!(
    filters.platform ||
    filters.genre ||
    filters.scoreRange ||
    filters.dateRange ||
    (filters.status && filters.status !== 'all')
  );

  return (
    <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-slate-200 flex items-center gap-2">
          <FileText className="text-purple-400" size={20} />
          Filter Reviews
        </h3>
        
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-slate-400 hover:text-slate-200"
          >
            <RotateCcw size={16} className="mr-1" />
            Clear All
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
            <FileText size={16} className="text-slate-400" />
            Status
          </label>
          <select
            value={filters.status || 'all'}
            onChange={(e) => onFilterChange({ status: e.target.value as any })}
            className="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-3 py-2 text-slate-200 text-sm focus:border-purple-500/50 focus:outline-none"
          >
            <option value="all">All Reviews</option>
            <option value="published">Published</option>
            <option value="draft">Drafts</option>
            <option value="pending">Pending</option>
          </select>
        </div>

        {/* Platform Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
            <Monitor size={16} className="text-slate-400" />
            Platform
          </label>
          <select
            value={filters.platform || ''}
            onChange={(e) => onFilterChange({ platform: e.target.value || undefined })}
            className="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-3 py-2 text-slate-200 text-sm focus:border-purple-500/50 focus:outline-none"
          >
            <option value="">All Platforms</option>
            {availableOptions.platforms.map((platform) => (
              <option key={platform} value={platform}>
                {platform}
              </option>
            ))}
          </select>
        </div>

        {/* Genre Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
            <Tag size={16} className="text-slate-400" />
            Genre
          </label>
          <select
            value={filters.genre || ''}
            onChange={(e) => onFilterChange({ genre: e.target.value || undefined })}
            className="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg px-3 py-2 text-slate-200 text-sm focus:border-purple-500/50 focus:outline-none"
          >
            <option value="">All Genres</option>
            {availableOptions.genres.map((genre) => (
              <option key={genre} value={genre}>
                {genre}
              </option>
            ))}
          </select>
        </div>

        {/* Sort Options */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
            {filters.sortOrder === 'asc' ? (
              <SortAsc size={16} className="text-slate-400" />
            ) : (
              <SortDesc size={16} className="text-slate-400" />
            )}
            Sort By
          </label>
          <div className="flex gap-1">
            <select
              value={filters.sortBy || 'publishDate'}
              onChange={(e) => onFilterChange({ sortBy: e.target.value as any })}
              className="flex-1 bg-slate-800/50 border border-slate-700/50 rounded-lg px-3 py-2 text-slate-200 text-sm focus:border-purple-500/50 focus:outline-none"
            >
              <option value="publishDate">Publish Date</option>
              <option value="createdAt">Created Date</option>
              <option value="title">Title</option>
              <option value="overallScore">Score</option>
            </select>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleSortOrder}
              className="px-2"
            >
              {filters.sortOrder === 'asc' ? (
                <SortAsc size={16} />
              ) : (
                <SortDesc size={16} />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="mt-6 pt-4 border-t border-slate-700/50">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Score Range Filter */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
                <Star size={16} className="text-slate-400" />
                Score Range
              </label>
              {filters.scoreRange && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearScoreRange}
                  className="text-xs text-slate-400 hover:text-slate-200 p-1"
                >
                  <X size={12} />
                </Button>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="0"
                  max="10"
                  step="0.1"
                  value={scoreRange[0]}
                  onChange={(e) => handleScoreRangeChange(0, parseFloat(e.target.value) || 0)}
                  className="bg-slate-800/50 border-slate-700/50 text-slate-200 text-sm"
                  placeholder="Min"
                />
                <span className="text-slate-400">to</span>
                <Input
                  type="number"
                  min="0"
                  max="10"
                  step="0.1"
                  value={scoreRange[1]}
                  onChange={(e) => handleScoreRangeChange(1, parseFloat(e.target.value) || 10)}
                  className="bg-slate-800/50 border-slate-700/50 text-slate-200 text-sm"
                  placeholder="Max"
                />
              </div>
              
              <div className="text-xs text-slate-500">
                Showing reviews with scores between {scoreRange[0]} and {scoreRange[1]}
              </div>
            </div>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-slate-300 flex items-center gap-2">
                <Calendar size={16} className="text-slate-400" />
                Date Range
              </label>
              {filters.dateRange && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearDateRange}
                  className="text-xs text-slate-400 hover:text-slate-200 p-1"
                >
                  <X size={12} />
                </Button>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                  className="bg-slate-800/50 border-slate-700/50 text-slate-200 text-sm"
                />
                <span className="text-slate-400">to</span>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                  className="bg-slate-800/50 border-slate-700/50 text-slate-200 text-sm"
                />
              </div>
              
              {filters.dateRange && (
                <div className="text-xs text-slate-500">
                  Showing reviews from {filters.dateRange[0].toLocaleDateString()} to {filters.dateRange[1].toLocaleDateString()}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-slate-700/50">
          <div className="flex items-center gap-2 text-sm text-slate-400">
            <span>Active filters:</span>
            <div className="flex flex-wrap gap-1">
              {filters.status && filters.status !== 'all' && (
                <span className="bg-purple-600/20 text-purple-300 px-2 py-1 rounded text-xs">
                  {filters.status}
                </span>
              )}
              {filters.platform && (
                <span className="bg-blue-600/20 text-blue-300 px-2 py-1 rounded text-xs">
                  {filters.platform}
                </span>
              )}
              {filters.genre && (
                <span className="bg-green-600/20 text-green-300 px-2 py-1 rounded text-xs">
                  {filters.genre}
                </span>
              )}
              {filters.scoreRange && (
                <span className="bg-yellow-600/20 text-yellow-300 px-2 py-1 rounded text-xs">
                  Score: {filters.scoreRange[0]}-{filters.scoreRange[1]}
                </span>
              )}
              {filters.dateRange && (
                <span className="bg-cyan-600/20 text-cyan-300 px-2 py-1 rounded text-xs">
                  Date Range
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
