// src/lib/services/b2StorageService.ts
import { S3Client, PutObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import sharp from 'sharp';
import { lookup } from 'mime-types';
import { v4 as uuidv4 } from 'uuid';

// Disable AWS SDK checksums globally for B2 compatibility
process.env.AWS_DISABLE_REQUEST_COMPRESSION = 'true';
process.env.AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE = '1';
process.env.AWS_NODEJS_CONNECTION_REUSE_ENABLED = '1';
// CRITICAL: Disable all checksum calculations globally
process.env.AWS_DISABLE_S3_EXPRESS_SESSION_AUTH = 'true';
process.env.AWS_S3_DISABLE_EXPRESS_SESSION_AUTH = 'true';

// B2 Client Configuration optimized for Backblaze B2 compatibility
const b2Client = new S3Client({
  endpoint: `https://${process.env.B2_ENDPOINT}`,
  region: process.env.B2_REGION || 'us-east-005',
  credentials: {
    accessKeyId: process.env.B2_APPLICATION_KEY_ID!,
    secretAccessKey: process.env.B2_APPLICATION_KEY!,
  },
  forcePathStyle: true, // Required for B2
  // Disable AWS-specific features that B2 doesn't support
  useAccelerateEndpoint: false,
  useDualstackEndpoint: false,
  // Configure request handler for B2 compatibility
  requestHandler: {
    requestTimeout: 30000,
    httpsAgent: undefined,
  },
  // Disable automatic checksum validation
  disableS3ExpressSessionAuth: true,
  // CRITICAL: Disable all checksum calculations for B2 compatibility (AWS SDK v3.729.0+)
  checksumDisabled: true,
  // NEW: Disable default integrity protections for third-party services like B2
  requestChecksumCalculation: 'WHEN_REQUIRED',
  responseChecksumValidation: 'WHEN_REQUIRED',
});

// Add middleware to remove problematic headers
b2Client.middlewareStack.add(
  (next: any) => async (args: any) => {
    // Remove checksum headers and other AWS-specific headers before sending request
    if (args.request?.headers) {
      const headers = args.request.headers;
      // Remove all checksum-related and AWS-specific headers
      Object.keys(headers).forEach(key => {
        const lowerKey = key.toLowerCase();
        if (lowerKey.includes('checksum') ||
            lowerKey.includes('x-amz-sdk') ||
            lowerKey.includes('x-amz-content-sha256') ||
            lowerKey === 'x-amz-checksum-crc32' ||
            lowerKey === 'x-amz-checksum-crc32c' ||
            lowerKey === 'x-amz-checksum-sha1' ||
            lowerKey === 'x-amz-checksum-sha256' ||
            lowerKey === 'x-amz-trailer' ||
            lowerKey === 'x-amz-decoded-content-length') {
          delete headers[key];
        }
      });
    }
    
    // Also remove checksums from the input if present
    if (args.input) {
      delete args.input.ChecksumAlgorithm;
      delete args.input.ChecksumCRC32;
      delete args.input.ChecksumCRC32C;
      delete args.input.ChecksumSHA1;
      delete args.input.ChecksumSHA256;
    }
    
    return next(args);
  },
  {
    step: 'build',
    name: 'removeB2UnsupportedHeaders',
    priority: 'high',
  }
);

// Configuration Constants
const MAX_SIZE = parseInt(process.env.MAX_IMAGE_SIZE || '10485760'); // 10MB
const ALLOWED_TYPES = (process.env.ALLOWED_IMAGE_TYPES || 'image/jpeg,image/png,image/webp,image/gif').split(',');
const IMAGE_QUALITY = parseInt(process.env.IMAGE_QUALITY || '85');
const MAX_WIDTH = parseInt(process.env.MAX_IMAGE_WIDTH || '2048');
const MAX_HEIGHT = parseInt(process.env.MAX_IMAGE_HEIGHT || '2048');

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    width: number;
    height: number;
    format: string;
  };
}

export interface ProcessedImage {
  buffer: Buffer;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}

/**
 * Validate image file before processing
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > MAX_SIZE) {
    return { valid: false, error: `File size exceeds ${MAX_SIZE / 1024 / 1024}MB limit` };
  }

  // Check file type
  if (!ALLOWED_TYPES.includes(file.type)) {
    return { valid: false, error: `File type ${file.type} not allowed. Allowed types: ${ALLOWED_TYPES.join(', ')}` };
  }

  return { valid: true };
}

/**
 * Process and optimize image using Sharp
 */
export async function processImage(buffer: Buffer, originalName: string): Promise<ProcessedImage> {
  try {
    const image = sharp(buffer);
    const metadata = await image.metadata();

    // Determine if resizing is needed
    const needsResize = metadata.width! > MAX_WIDTH || metadata.height! > MAX_HEIGHT;

    let processedImage = image;

    if (needsResize) {
      processedImage = image.resize(MAX_WIDTH, MAX_HEIGHT, {
        fit: 'inside',
        withoutEnlargement: true,
      });
    }

    // Convert to WebP for better compression, except GIFs
    const isGif = metadata.format === 'gif';
    const outputBuffer = await processedImage
      .webp({ quality: IMAGE_QUALITY, effort: 4 })
      .toBuffer();

    const finalMetadata = await sharp(outputBuffer).metadata();

    return {
      buffer: outputBuffer,
      metadata: {
        width: finalMetadata.width!,
        height: finalMetadata.height!,
        format: finalMetadata.format!,
        size: outputBuffer.length,
      },
    };
  } catch (error) {
    throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate unique file key for B2 storage
 */
export function generateFileKey(originalName: string, userId?: string): string {
  const timestamp = Date.now();
  const uuid = uuidv4().substring(0, 8);
  const extension = originalName.split('.').pop()?.toLowerCase() || 'webp';
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  
  const prefix = userId ? `users/${userId}` : 'uploads';
  return `${prefix}/${timestamp}-${uuid}-${sanitizedName}.${extension}`;
}

/**
 * Upload image to Backblaze B2
 */
export async function uploadImageToB2(
  buffer: Buffer,
  key: string,
  contentType: string = 'image/webp',
  metadata?: Record<string, string>
): Promise<UploadResult> {
  try {
    const command = new PutObjectCommand({
      Bucket: process.env.B2_BUCKET_NAME!,
      Key: key,
      Body: buffer,
      ContentType: contentType,
      Metadata: metadata,
      CacheControl: 'public, max-age=31536000', // 1 year cache
      // Explicitly disable checksums for B2 compatibility
      ChecksumAlgorithm: undefined,
    });

    // Send command with explicit options to disable checksums
    await b2Client.send(command, {
      // Disable request compression and checksums
      requestChecksumCalculation: 'WHEN_REQUIRED',
      requestHandler: {
        requestTimeout: 30000,
      },
      // Explicitly disable checksums at the request level
      checksumDisabled: true,
    });

    // Generate public URL
    const url = `https://${process.env.B2_ENDPOINT}/${process.env.B2_BUCKET_NAME}/${key}`;

    return {
      success: true,
      url,
      key,
    };
  } catch (error) {
    console.error('B2 Upload Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

/**
 * Delete image from Backblaze B2
 */
export async function deleteImageFromB2(key: string): Promise<{ success: boolean; error?: string }> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: process.env.B2_BUCKET_NAME!,
      Key: key,
    });

    await b2Client.send(command);

    return { success: true };
  } catch (error) {
    console.error('B2 Delete Error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
    };
  }
}

/**
 * Check if image exists in B2
 */
export async function checkImageExists(key: string): Promise<boolean> {
  try {
    const command = new HeadObjectCommand({
      Bucket: process.env.B2_BUCKET_NAME!,
      Key: key,
    });

    await b2Client.send(command);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Generate presigned URL for direct uploads (alternative method)
 */
export async function generatePresignedUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const command = new PutObjectCommand({
      Bucket: process.env.B2_BUCKET_NAME!,
      Key: key,
      ContentType: contentType,
    });

    const url = await getSignedUrl(b2Client, command, { expiresIn });

    return { success: true, url };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate presigned URL',
    };
  }
}

/**
 * Complete upload workflow: validate, process, and upload
 */
export async function uploadImageWorkflow(
  file: File,
  userId?: string
): Promise<UploadResult> {
  try {
    // Step 1: Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    // Step 2: Convert File to Buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Step 3: Process image
    const processed = await processImage(buffer, file.name);

    // Step 4: Generate unique key
    const key = generateFileKey(file.name, userId);

    // Step 5: Upload to B2
    const uploadResult = await uploadImageToB2(
      processed.buffer,
      key,
      'image/webp',
      {
        originalName: file.name,
        uploadedBy: userId || 'anonymous',
        originalSize: file.size.toString(),
        processedSize: processed.metadata.size.toString(),
      }
    );

    if (uploadResult.success) {
      return {
        ...uploadResult,
        metadata: {
          originalName: file.name,
          size: processed.metadata.size,
          width: processed.metadata.width,
          height: processed.metadata.height,
          format: processed.metadata.format,
        },
      };
    }

    return uploadResult;
  } catch (error) {
    console.error('Upload workflow error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload workflow failed',
    };
  }
}
