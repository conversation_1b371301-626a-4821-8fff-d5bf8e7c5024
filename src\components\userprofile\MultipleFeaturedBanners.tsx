'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ExternalLink, Gamepad2, Star, Eye, Heart, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface StoreLink {
  id: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  color_gradient: string;
  display_order: number;
}

interface FeaturedBanner {
  id: string;
  user_id: string;
  review_id: string;
  display_order: number;
  is_active: boolean;
  review: {
    id: string;
    title: string;
    game_name: string;
    igdb_cover_url?: string;
    main_image_url?: string;
    overall_score: number;
    like_count: number;
    view_count: number;
    created_at: string;
  };
  store_links?: StoreLink[];
}

interface MultipleFeaturedBannersProps {
  userId: string;
}

export default function MultipleFeaturedBanners({ userId }: MultipleFeaturedBannersProps) {
  const [banners, setBanners] = useState<FeaturedBanner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBanners = async () => {
      try {
        setLoading(true);
        
        const response = await fetch('/api/u/featured-banners', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'getBanners', userId })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result && result.success && result.data) {
          // Load store links for each banner
          const bannersWithStoreLinks = await Promise.all(
            result.data.map(async (banner: FeaturedBanner) => {
              if (banner.id) {
                try {
                  const storeLinksResponse = await fetch('/api/u/featured-banners', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getStoreLinks', userId, bannerId: banner.id })
                  });
                  
                  if (storeLinksResponse.ok) {
                    const storeLinksResult = await storeLinksResponse.json();
                    if (storeLinksResult.success) {
                      banner.store_links = storeLinksResult.data;
                    }
                  }
                } catch (error) {
                  console.error('Error loading store links for banner:', banner.id, error);
                }
              }
              return banner;
            })
          );
          
          setBanners(bannersWithStoreLinks.sort((a, b) => a.display_order - b.display_order));
        } else {
          setBanners([]);
        }
      } catch (error) {
        console.error('Error loading featured banners:', error);
        setError(error instanceof Error ? error.message : 'Failed to load featured banners');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      loadBanners();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-gray-900/50 border border-gray-700 rounded-xl p-6 animate-pulse">
            <div className="flex gap-4">
              <div className="w-24 h-32 bg-gray-700 rounded-lg"></div>
              <div className="flex-1 space-y-3">
                <div className="h-6 bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-gray-700 rounded w-20"></div>
                  <div className="h-8 bg-gray-700 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-700 rounded-xl p-6">
        <p className="text-red-400">Error loading featured banners: {error}</p>
      </div>
    );
  }

  if (banners.length === 0) {
    return null; // Don't show anything if no banners
  }

  const getImageSource = (review: FeaturedBanner['review']) => {
    const isValidUrl = (url: any) => url && url !== 'undefined' && url !== 'null' && typeof url === 'string' && url.trim() !== '';
    return isValidUrl(review.igdb_cover_url) ? review.igdb_cover_url : 
           isValidUrl(review.main_image_url) ? review.main_image_url : null;
  };

  const getDiscountPercentage = (price: string, originalPrice?: string) => {
    if (!originalPrice || originalPrice === price) return null;
    
    const current = parseFloat(price.replace(/[^0-9.]/g, ''));
    const original = parseFloat(originalPrice.replace(/[^0-9.]/g, ''));
    
    if (isNaN(current) || isNaN(original) || original <= current) return null;
    
    return Math.round(((original - current) / original) * 100);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Star className="h-5 w-5 text-yellow-400" />
        <h2 className="text-xl font-bold text-white">Featured Reviews</h2>
      </div>

      {banners.map((banner) => {
        const imageSource = getImageSource(banner.review);
        
        return (
          <div
            key={banner.id}
            className="bg-gray-900/60 border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/30 transition-all duration-300"
          >
            <div className="flex gap-6">
              {/* Game Cover */}
              <div className="w-24 h-32 bg-gray-800/50 rounded-lg border border-gray-700/30 flex items-center justify-center overflow-hidden flex-shrink-0">
                {imageSource ? (
                  <Image
                    src={imageSource}
                    alt={banner.review.game_name}
                    width={96}
                    height={128}
                    className="w-full h-full object-cover"
                    unoptimized
                  />
                ) : (
                  <Gamepad2 className="h-8 w-8 text-gray-500" />
                )}
              </div>

              {/* Content */}
              <div className="flex-1 space-y-4">
                {/* Header */}
                <div>
                  <h3 className="text-xl font-bold text-white mb-1">{banner.review.game_name}</h3>
                  <p className="text-gray-300">{banner.review.title}</p>
                </div>

                {/* Stats */}
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400" />
                    <span className="text-yellow-400 font-semibold">
                      {(banner.review.overall_score / 10).toFixed(1)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4 text-red-400" />
                    <span className="text-gray-300">{banner.review.like_count}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4 text-blue-400" />
                    <span className="text-gray-300">{banner.review.view_count}</span>
                  </div>
                  <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30">
                    Featured #{banner.display_order}
                  </Badge>
                </div>

                {/* Store Links */}
                {banner.store_links && banner.store_links.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="h-4 w-4 text-green-400" />
                      <span className="text-sm font-medium text-gray-300">Available at:</span>
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {banner.store_links.map((link) => {
                        const discount = getDiscountPercentage(link.price, link.original_price);
                        
                        return (
                          <Button
                            key={link.id}
                            asChild
                            variant="outline"
                            size="sm"
                            className={`border-gray-600 hover:border-gray-500 bg-gradient-to-r ${link.color_gradient} text-white hover:scale-105 transition-all duration-200`}
                          >
                            <a
                              href={link.store_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-2"
                            >
                              <span className="font-medium">{link.store_name}</span>
                              <div className="flex items-center gap-1">
                                {link.original_price && link.original_price !== link.price && (
                                  <span className="text-xs line-through opacity-70">
                                    {link.original_price}
                                  </span>
                                )}
                                <span className="font-bold">{link.price}</span>
                                {discount && (
                                  <Badge variant="secondary" className="bg-green-500/20 text-green-300 text-xs">
                                    -{discount}%
                                  </Badge>
                                )}
                              </div>
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Read Review Link */}
                <div className="pt-2">
                  <Button
                    asChild
                    variant="ghost"
                    size="sm"
                    className="text-purple-400 hover:text-purple-300 hover:bg-purple-500/10"
                  >
                    <Link href={`/review/${banner.review.id}`}>
                      Read Full Review →
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
