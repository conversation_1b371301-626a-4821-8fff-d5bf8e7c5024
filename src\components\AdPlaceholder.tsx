import { cn } from "@/lib/utils";
import { Megaphone } from "lucide-react";

interface AdPlaceholderProps extends React.HTMLAttributes<HTMLDivElement> {
  type: "banner" | "skyscraper" | "square";
  hintText?: string;
}

export function AdPlaceholder({ type, className, hintText, ...props }: AdPlaceholderProps) {
  const typeClasses = {
    banner: "w-full h-24 md:h-32", // Example sizes
    skyscraper: "w-full md:w-40 h-[250px] md:h-[600px]", // Example sizes, full width on mobile
    square: "w-64 h-64", // Example sizes
  };

  return (
    <div
      className={cn(
        "bg-muted/30 border-2 border-dashed border-border/50 rounded-lg flex flex-col items-center justify-center text-muted-foreground p-4",
        typeClasses[type],
        className
      )}
      {...props}
    >
      <Megaphone className="h-8 w-8 mb-2 text-accent opacity-50" />
      <p className="text-sm font-semibold text-accent/80">Advertisement</p>
      {hintText && <p className="text-xs mt-1 opacity-70">{hintText}</p>}
      <p className="text-xs mt-1 opacity-70">(Placeholder for {type} ad)</p>
    </div>
  );
}
