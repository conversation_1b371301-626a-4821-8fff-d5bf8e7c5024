-- Featured Banners System Migration
-- Date: January 25, 2025
-- Purpose: Create database structure for multiple featured banners (up to 3 per user)

-- Create the main featured banners table
CREATE TABLE IF NOT EXISTS featured_banners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  review_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL CHECK (display_order >= 1 AND display_order <= 3),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique display order per user
  CONSTRAINT unique_user_display_order UNIQUE (user_id, display_order)
);

-- Create the store links table for featured banners
CREATE TABLE IF NOT EXISTS featured_review_store_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  banner_id UUID NOT NULL REFERENCES featured_banners(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  store_name VARCHAR(100) NOT NULL,
  price VARCHAR(20) NOT NULL,
  original_price VARCHAR(20),
  store_url TEXT NOT NULL,
  display_order INTEGER NOT NULL DEFAULT 1 CHECK (display_order >= 1 AND display_order <= 5),
  color_gradient VARCHAR(100) DEFAULT 'from-blue-500 to-purple-600',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure unique display order per banner
  CONSTRAINT unique_banner_display_order UNIQUE (banner_id, display_order)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_featured_banners_user_id ON featured_banners(user_id);
CREATE INDEX IF NOT EXISTS idx_featured_banners_review_id ON featured_banners(review_id);
CREATE INDEX IF NOT EXISTS idx_featured_banners_active ON featured_banners(is_active);
CREATE INDEX IF NOT EXISTS idx_featured_banners_display_order ON featured_banners(display_order);

CREATE INDEX IF NOT EXISTS idx_store_links_banner_id ON featured_review_store_links(banner_id);
CREATE INDEX IF NOT EXISTS idx_store_links_user_id ON featured_review_store_links(user_id);
CREATE INDEX IF NOT EXISTS idx_store_links_active ON featured_review_store_links(is_active);

-- Enable Row Level Security
ALTER TABLE featured_banners ENABLE ROW LEVEL SECURITY;
ALTER TABLE featured_review_store_links ENABLE ROW LEVEL SECURITY;

-- RLS Policies for featured_banners

-- Allow users to view all active featured banners (for public profile viewing)
CREATE POLICY featured_banners_public_view ON featured_banners
  FOR SELECT TO public USING (is_active = true);

-- Allow users to manage only their own featured banners
CREATE POLICY featured_banners_user_manage ON featured_banners
  FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for featured_review_store_links

-- Allow users to view all active store links (for public profile viewing)
CREATE POLICY store_links_public_view ON featured_review_store_links
  FOR SELECT TO public USING (is_active = true);

-- Allow users to manage only their own store links
CREATE POLICY store_links_user_manage ON featured_review_store_links
  FOR ALL USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_featured_banners_updated_at 
  BEFORE UPDATE ON featured_banners 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_store_links_updated_at 
  BEFORE UPDATE ON featured_review_store_links 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE featured_banners IS 'Stores up to 3 featured review banners per user for profile display';
COMMENT ON TABLE featured_review_store_links IS 'Store purchase links associated with featured banners';

COMMENT ON COLUMN featured_banners.display_order IS 'Order of banner display (1-3), unique per user';
COMMENT ON COLUMN featured_review_store_links.display_order IS 'Order of store link display (1-5), unique per banner';
COMMENT ON COLUMN featured_review_store_links.color_gradient IS 'CSS gradient class for store link button styling';
