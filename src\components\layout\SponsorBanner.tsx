'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Sparkles } from 'lucide-react';
import { getUserSponsorBanner, trackSponsorImpression, trackSponsorClick } from '@/lib/services/sponsorBannerService';

interface SponsorBannerProps {
  userId?: string;
  className?: string;
}

interface SponsorData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

const SponsorBanner: React.FC<SponsorBannerProps> = ({ 
  userId,
  className = ''
}) => {
  const [sponsorData, setSponsorData] = useState<SponsorData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (userId) {
      loadSponsorData();
    } else {
      setIsLoading(false);
    }
  }, [userId]);

  const loadSponsorData = async () => {
    try {
      setIsLoading(true);
      setImageError(false);
      
      // Make sure userId is defined before making API call
      if (!userId) {
        console.warn('SponsorBanner: userId is undefined');
        setIsLoading(false);
        return;
      }
      
      const data = await getUserSponsorBanner(userId);
      
      if (data && data.is_active) {
        setSponsorData(data);
        
        // Track impression after a short delay to ensure banner is actually viewed
        setTimeout(() => {
          if (data.id) {
            const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
            const referrer = typeof document !== 'undefined' ? document.referrer : undefined;

            trackSponsorImpression(data.id, userAgent, referrer)
              .then(success => {
                if (!success) console.warn('Failed to track impression for banner', data.id);
              })
              .catch(err => console.error('Error tracking impression:', err));
          }
        }, 1000);
      } else {
        setSponsorData(null);
      }
    } catch (error) {
      console.error('Error loading sponsor data:', error);
      setSponsorData(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render anything if there's no active sponsor data
  if (isLoading || !sponsorData || !sponsorData.is_active) {
    return null;
  }

  return (
    <div className={`w-full overflow-hidden ${className}`}>
      <a 
        href={sponsorData.url}
        target="_blank"
        rel="noopener noreferrer sponsored"
        className="block rounded-2xl overflow-hidden w-full"
        title="Sponsored Link"
        onClick={() => {
          if (sponsorData.id) {
            const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
            const referrer = typeof document !== 'undefined' ? document.referrer : undefined;

            trackSponsorClick(sponsorData.id, userAgent, referrer)
              .then(success => {
                if (!success) console.warn('Failed to track click for banner', sponsorData.id);
              })
              .catch(err => console.error('Error tracking click:', err));
          }
        }}
      >
        <div className="relative w-full aspect-square bg-slate-800/60 border border-slate-700/50 rounded-2xl overflow-hidden">
          {!imageError ? (
            <img
              src={sponsorData.img_url}
              alt="Sponsor"
              className="w-full h-full object-contain"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-xs text-slate-500">Sponsor</span>
            </div>
          )}
        </div>
      </a>
    </div>
  );
};

export default SponsorBanner;
