// src/components/ui/ScoreCircle.tsx
'use client';

import { useEffect, useRef, useState } from 'react';

interface ScoreCircleProps {
  score: number; // Score out of 100
  size?: number; // Diameter of the circle
  strokeWidth?: number; // Width of the circle's stroke
  className?: string;
}

const ScoreCircle: React.FC<ScoreCircleProps> = ({
  score,
  size = 100,
  strokeWidth = 8,
  className,
}) => {
  const circleRef = useRef<SVGCircleElement>(null);
  const [displayScore, setDisplayScore] = useState(0);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (score / 100) * circumference;

  useEffect(() => {
    if (circleRef.current) {
      circleRef.current.style.strokeDasharray = `${circumference} ${circumference}`;
      circleRef.current.style.strokeDashoffset = `${circumference}`; // Start empty
      // Trigger reflow to ensure animation starts
      circleRef.current.getBoundingClientRect(); 
      // Animate to the target offset
      circleRef.current.style.transition = 'stroke-dashoffset 1s ease-out';
      circleRef.current.style.strokeDashoffset = `${offset}`;
    }

    // Animate score text
    let startTimestamp: number | null = null;
    const duration = 1000; // 1 second

    const animateScore = (timestamp: number) => {
      if (!startTimestamp) startTimestamp = timestamp;
      const progress = Math.min((timestamp - startTimestamp) / duration, 1);
      setDisplayScore(Math.floor(progress * score));
      if (progress < 1) {
        requestAnimationFrame(animateScore);
      }
    };
    requestAnimationFrame(animateScore);

    return () => {
      // Cleanup if necessary
      startTimestamp = null; 
    };

  }, [score, circumference, offset]);

  let scoreColorClass = 'text-primary'; // Default
  if (score >= 85) scoreColorClass = 'text-green-500';
  else if (score >= 70) scoreColorClass = 'text-yellow-500';
  else if (score >= 50) scoreColorClass = 'text-orange-500';
  else scoreColorClass = 'text-red-500';


  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <svg className="w-full h-full" viewBox={`0 0 ${size} ${size}`}>
        {/* Background circle */}
        <circle
          className="text-muted-foreground/10 stroke-current"
          strokeWidth={strokeWidth}
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="transparent"
        />
        {/* Progress circle */}
        <circle
          ref={circleRef}
          className={`${scoreColorClass.replace('text-', 'stroke-')} stroke-current`} // Use color for stroke
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="transparent"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </svg>
      <div className={`absolute inset-0 flex flex-col items-center justify-center ${scoreColorClass}`}>
        <span className="text-3xl font-bold">
          {displayScore}
        </span>
        <span className="text-xs opacity-80">/ 100</span>
      </div>
    </div>
  );
};

export default ScoreCircle;
