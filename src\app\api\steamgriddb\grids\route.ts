import { NextRequest, NextResponse } from 'next/server';
import { 
  getSteamGridDBGrids, 
  getSteamGridDBGridsBySteamId,
  STEAMGRIDDB_STYLES,
  STEAMGRIDDB_DIMENSIONS
} from '@/lib/steamgriddb-api';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  const gameId = searchParams.get('gameId');
  const steamId = searchParams.get('steamId');
  const styles = searchParams.get('styles')?.split(',').filter(Boolean) || [];
  const dimensions = searchParams.get('dimensions')?.split(',').filter(Boolean) || [];
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

  // Validate that either gameId or steamId is provided
  if (!gameId && !steamId) {
    return NextResponse.json(
      { error: 'Either gameId or steamId parameter is required' },
      { status: 400 }
    );
  }

  // Validate gameId or steamId is numeric
  if (gameId && isNaN(parseInt(gameId))) {
    return NextResponse.json(
      { error: 'gameId must be a valid number' },
      { status: 400 }
    );
  }

  if (steamId && isNaN(parseInt(steamId))) {
    return NextResponse.json(
      { error: 'steamId must be a valid number' },
      { status: 400 }
    );
  }

  // Validate styles
  const validStyles = styles.filter(style => STEAMGRIDDB_STYLES.includes(style as any));
  if (styles.length > 0 && validStyles.length === 0) {
    return NextResponse.json(
      { 
        error: 'Invalid styles provided',
        validStyles: STEAMGRIDDB_STYLES
      },
      { status: 400 }
    );
  }

  // Validate dimensions
  const validDimensions = dimensions.filter(dim => STEAMGRIDDB_DIMENSIONS.includes(dim as any));
  if (dimensions.length > 0 && validDimensions.length === 0) {
    return NextResponse.json(
      { 
        error: 'Invalid dimensions provided',
        validDimensions: STEAMGRIDDB_DIMENSIONS
      },
      { status: 400 }
    );
  }

  try {
    let grids;
    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      dimensions: validDimensions.length > 0 ? validDimensions : undefined,
      limit
    };

    if (gameId) {
      grids = await getSteamGridDBGrids(parseInt(gameId), options);
    } else {
      grids = await getSteamGridDBGridsBySteamId(parseInt(steamId!), options);
    }

    return NextResponse.json({
      success: true,
      data: grids,
      metadata: {
        count: grids.length,
        gameId: gameId ? parseInt(gameId) : null,
        steamId: steamId ? parseInt(steamId!) : null,
        styles: validStyles,
        dimensions: validDimensions
      }
    });
  } catch (error) {
    console.error('SteamGridDB grids error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch grids from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gameId, steamId, styles, dimensions, limit } = body;

    // Validate that either gameId or steamId is provided
    if (!gameId && !steamId) {
      return NextResponse.json(
        { error: 'Either gameId or steamId is required' },
        { status: 400 }
      );
    }

    // Validate styles if provided
    const validStyles = styles ? styles.filter((style: string) => STEAMGRIDDB_STYLES.includes(style as any)) : [];
    
    // Validate dimensions if provided
    const validDimensions = dimensions ? dimensions.filter((dim: string) => STEAMGRIDDB_DIMENSIONS.includes(dim as any)) : [];

    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      dimensions: validDimensions.length > 0 ? validDimensions : undefined,
      limit
    };

    let grids;
    if (gameId) {
      grids = await getSteamGridDBGrids(gameId, options);
    } else {
      grids = await getSteamGridDBGridsBySteamId(steamId, options);
    }

    return NextResponse.json({
      success: true,
      data: grids,
      metadata: {
        count: grids.length,
        gameId: gameId || null,
        steamId: steamId || null,
        styles: validStyles,
        dimensions: validDimensions
      }
    });
  } catch (error) {
    console.error('SteamGridDB grids error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch grids from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}