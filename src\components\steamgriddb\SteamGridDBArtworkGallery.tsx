'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSteamGridDB, SteamGridDBArtwork, SteamGridDBGame } from '@/hooks/useSteamGridDB';
import { Image, User, Star, ExternalLink } from 'lucide-react';
import { STEAMGRIDDB_STYLES, STEAMGRIDDB_DIMENSIONS } from '@/lib/steamgriddb-api';

interface SteamGridDBArtworkGalleryProps {
  selectedGame: SteamGridDBGame | null;
  onArtworkSelect?: (artwork: SteamGridDBArtwork, type: 'grid' | 'hero' | 'logo' | 'icon') => void;
  className?: string;
}

export function SteamGridDBArtworkGallery({ 
  selectedGame, 
  onArtworkSelect, 
  className 
}: SteamGridDBArtworkGalleryProps) {
  const { 
    loading, 
    error, 
    grids, 
    heroes, 
    logos, 
    icons, 
    getGrids, 
    getHeroes, 
    getLogos, 
    getIcons 
  } = useSteamGridDB();

  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('grids');

  useEffect(() => {
    if (selectedGame) {
      const options = {
        styles: selectedStyles.length > 0 ? selectedStyles : undefined,
        dimensions: selectedDimensions.length > 0 ? selectedDimensions : undefined,
        limit: 20
      };

      getGrids(selectedGame.id, options);
      getHeroes(selectedGame.id, { styles: selectedStyles.length > 0 ? selectedStyles : undefined, limit: 20 });
      getLogos(selectedGame.id, { styles: selectedStyles.length > 0 ? selectedStyles : undefined, limit: 20 });
      getIcons(selectedGame.id, options);
    }
  }, [selectedGame, selectedStyles, selectedDimensions]);

  const handleStyleChange = (style: string) => {
    setSelectedStyles(prev => 
      prev.includes(style) 
        ? prev.filter(s => s !== style)
        : [...prev, style]
    );
  };

  const handleDimensionChange = (dimension: string) => {
    setSelectedDimensions(prev => 
      prev.includes(dimension) 
        ? prev.filter(d => d !== dimension)
        : [...prev, dimension]
    );
  };

  const renderArtworkGrid = (artworks: SteamGridDBArtwork[], type: 'grid' | 'hero' | 'logo' | 'icon') => {
    if (loading) {
      return (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="aspect-video w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
      );
    }

    if (artworks.length === 0) {
      return (
        <div className="text-center py-12 text-muted-foreground">
          <Image className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No {type} artwork found</p>
          <p className="text-sm">Try adjusting your filters</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {artworks.map((artwork) => (
          <Card key={artwork.id} className="overflow-hidden group cursor-pointer hover:shadow-lg transition-shadow">
            <div className="relative aspect-video">
              <img
                src={artwork.thumb || artwork.url}
                alt={`${type} artwork`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => onArtworkSelect?.(artwork, type)}
                >
                  Select
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => window.open(artwork.url, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
              {artwork.score > 0 && (
                <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                  <Star className="h-3 w-3 fill-current" />
                  {artwork.score}
                </div>
              )}
            </div>
            <CardContent className="p-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    {artwork.style}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {artwork.width}×{artwork.height}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <User className="h-3 w-3" />
                  <span className="truncate">{artwork.author.name}</span>
                </div>
                {artwork.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {artwork.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {artwork.tags.length > 2 && (
                      <Badge variant="secondary" className="text-xs">
                        +{artwork.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  if (!selectedGame) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center text-muted-foreground">
            <Image className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Select a game to view artwork</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Artwork for {selectedGame.name}</span>
            {selectedGame.verified && (
              <Badge variant="default" className="bg-green-500">
                Verified
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex flex-wrap gap-4 mt-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Styles</label>
              <div className="flex flex-wrap gap-2">
                {STEAMGRIDDB_STYLES.map((style) => (
                  <Button
                    key={style}
                    variant={selectedStyles.includes(style) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleStyleChange(style)}
                  >
                    {style}
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Dimensions</label>
              <div className="flex flex-wrap gap-2">
                {STEAMGRIDDB_DIMENSIONS.map((dimension) => (
                  <Button
                    key={dimension}
                    variant={selectedDimensions.includes(dimension) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleDimensionChange(dimension)}
                  >
                    {dimension}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="grids">
                Grids ({grids.length})
              </TabsTrigger>
              <TabsTrigger value="heroes">
                Heroes ({heroes.length})
              </TabsTrigger>
              <TabsTrigger value="logos">
                Logos ({logos.length})
              </TabsTrigger>
              <TabsTrigger value="icons">
                Icons ({icons.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="grids" className="mt-6">
              {renderArtworkGrid(grids, 'grid')}
            </TabsContent>

            <TabsContent value="heroes" className="mt-6">
              {renderArtworkGrid(heroes, 'hero')}
            </TabsContent>

            <TabsContent value="logos" className="mt-6">
              {renderArtworkGrid(logos, 'logo')}
            </TabsContent>

            <TabsContent value="icons" className="mt-6">
              {renderArtworkGrid(icons, 'icon')}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}