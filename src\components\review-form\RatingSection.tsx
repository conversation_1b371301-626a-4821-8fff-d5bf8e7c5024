import React, { forwardRef, useState, useEffect, memo, useMemo } from 'react';
import './style/ratingSection.css';
import { Button } from '@/components/ui/button';
import {
  ArrowRight,
  Target,
  Zap,
  Star,
  Trophy,
  Award,
  Gamepad2,
  Headphones,
  Eye,
  Palette,
  Users
} from 'lucide-react';

// Define the type for scoring criteria
interface ScoringCriterion {
  id: string;
  name: string;
  score: number;
  icon: React.ElementType;
}

// Define the props for the RatingSection component
interface RatingSectionProps {
  currentStep: number;
  scoringCriteria: ScoringCriterion[];
  handleCriterionScoreChange: (id: string, score: number) => void;
  overallScore: number;
  handleNextStep?: () => void; // Optional since parent may not have step navigation
}


// Helper function to get score tier description
const getScoreTier = (score: number): { name: string; color: string; gradient: string } => {
  if (score >= 95) return { name: 'Legendary', color: '#fbbf24', gradient: 'from-yellow-400 to-amber-500' };
  if (score >= 90) return { name: 'Epic', color: '#a855f7', gradient: 'from-purple-500 to-violet-600' };
  if (score >= 85) return { name: 'Excellent', color: '#8b5cf6', gradient: 'from-violet-500 to-purple-600' };
  if (score >= 80) return { name: 'Great', color: '#3b82f6', gradient: 'from-blue-500 to-indigo-600' };
  if (score >= 75) return { name: 'Good', color: '#06b6d4', gradient: 'from-cyan-500 to-blue-600' };
  if (score >= 70) return { name: 'Decent', color: '#22d3ee', gradient: 'from-cyan-400 to-blue-500' };
  if (score >= 65) return { name: 'Average', color: '#64748b', gradient: 'from-slate-500 to-gray-600' };
  if (score >= 60) return { name: 'Below Average', color: '#f97316', gradient: 'from-orange-500 to-red-500' };
  if (score >= 50) return { name: 'Mediocre', color: '#f59e0b', gradient: 'from-amber-500 to-orange-500' };
  if (score >= 40) return { name: 'Poor', color: '#ef4444', gradient: 'from-red-500 to-red-600' };
  return { name: 'Terrible', color: '#dc2626', gradient: 'from-red-600 to-red-700' };
};

// Get smooth color for score
const getScoreColor = (score: number): string => {
  const tier = getScoreTier(score);
  return tier.color;
};

// Elegant minimal criterion component
const CriterionSlider = memo(({
  criterion,
  onChange
}: {
  criterion: ScoringCriterion;
  onChange: (id: string, score: number) => void;
}) => {
  const CriterionIcon = criterion.icon;

  return (
    <div className="group flex items-center gap-4 py-2 px-2 rounded-lg transition-all duration-200 hover:bg-slate-800/30">
      {/* Label */}
      <div className="flex items-center gap-3 min-w-[140px]">
        <span className="text-sm text-slate-300 font-medium">{criterion.name}</span>
      </div>

      {/* Slider Container - Much Thinner */}
      <div className="flex-1 relative">
        <input
          type="range"
          min="0"
          max="100"
          step="1"
          value={criterion.score}
          onChange={(e) => onChange(criterion.id, parseInt(e.target.value))}
          className="w-full h-1 bg-slate-700/60 rounded-full appearance-none cursor-pointer slider-ultra-thin"
          style={{
            background: `linear-gradient(to right, ${getScoreColor(criterion.score)} ${criterion.score}%, rgb(51 65 85 / 0.6) ${criterion.score}%)`
          }}
          aria-label={`Rate ${criterion.name}`}
        />
      </div>

      {/* Score Value */}
      <div className="text-lg font-bold text-white font-mono min-w-[35px] text-center">
        {criterion.score}
      </div>
    </div>
  );
});

const RatingSection = forwardRef<HTMLElement, RatingSectionProps>(
  ({
    currentStep,
    scoringCriteria,
    handleCriterionScoreChange,
    overallScore,
    handleNextStep,
  },
    ref
  ) => {
    const [scoreChanged, setScoreChanged] = useState(false);

    const tier = getScoreTier(overallScore);

    // Show animation when score changes
    useEffect(() => {
      setScoreChanged(true);
      const timer = setTimeout(() => setScoreChanged(false), 500);
      return () => clearTimeout(timer);
    }, [overallScore]);

    // Check if we can proceed to next step (all criteria have scores > 0)
    const canProceed = useMemo(() => {
      return scoringCriteria.every(criterion => criterion.score > 0);
    }, [scoringCriteria]);

    return (
      <div className="space-y-6">
        {/* Compact Overall Score Display */}
        <div className="bg-slate-800/50 border border-white/10 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <span className="font-mono text-sm text-slate-300">// Overall Score</span>
            </div>
            <div className="text-right">
              <div className={`text-2xl font-bold font-mono text-white mb-1 transition-all duration-300 ${scoreChanged ? 'scale-110' : 'scale-100'}`}>{overallScore}</div>
              <div className="text-sm font-medium transition-colors duration-300" style={{ color: tier.color }}>{tier.name}</div>
            </div>
          </div>
          
          {/* Elegant animated progress bar */}
          <div className="w-full h-2 bg-slate-700/60 rounded-full overflow-hidden">
            <div 
              className={`h-full rounded-full bg-gradient-to-r ${tier.gradient} transition-all duration-500 ease-out ${scoreChanged ? 'animate-pulse scale-y-110' : ''}`}
              style={{ 
                width: `${overallScore}%`,
                transformOrigin: 'left center'
              }}
            />
          </div>
        </div>

        {/* Compact Criteria Sliders */}
        <div className="bg-slate-800/50 border border-white/10 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <span className="font-mono text-sm text-slate-300">// Rate Each Aspect</span>
          </div>
          
          <div className="space-y-4">
            {scoringCriteria.map((criterion) => (
              <CriterionSlider
                key={criterion.id}
                criterion={criterion}
                onChange={handleCriterionScoreChange}
              />
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-2 gap-4">
          <div className="flex items-center space-x-2 min-w-0">
            <div className="w-2 h-2 bg-violet-400/60 rounded-full animate-pulse flex-shrink-0" />
            <span className="text-sm text-slate-400/80 font-mono break-words">
              <span className="text-violet-400">&lt;</span>
              {canProceed ? '// Ratings configured' : '// Configure your game ratings'}
              <span className="text-violet-400">/&gt;</span>
            </span>
          </div>

          <div className="flex items-center justify-end flex-shrink-0">
            {canProceed && handleNextStep && (
              <Button
                onClick={handleNextStep}
                className="review-continue-button review-continue-ready"
              >
                <div className="review-button-content">
                  <span className="review-code-brackets">&lt;</span>
                  Continue
                  <span className="review-code-brackets">/&gt;</span>
                  <ArrowRight className="review-button-arrow" />
                </div>
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }
);

RatingSection.displayName = 'RatingSection';
CriterionSlider.displayName = 'CriterionSlider';

export default RatingSection;