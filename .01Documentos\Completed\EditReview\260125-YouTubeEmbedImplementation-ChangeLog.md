# YouTube Embed Implementation - Complete Change Log
**Date**: January 26, 2025
**Task**: Implement YouTube embed functionality for both toolbars with same styling as images
**Status**: CSS Loading Issue Occurred

## Summary
Implemented a complete YouTube embed system for the Lexical editor including new nodes, modals, toolbar buttons, and styling. The implementation was successful but caused CSS loading issues for the entire website.

## Files Created

### 1. `/src/components/review-form/lexical/nodes/YouTubeNode.tsx`
**Purpose**: Custom Lexical node for YouTube video embeds
**Lines**: 315 lines total
**Key Features**:
- Supports multiple YouTube URL formats (youtube.com/watch, youtu.be, embeds, shorts)
- Accepts video IDs directly
- Same figure/caption structure as ImageNode
- Responsive 16:9 aspect ratio iframe
- Export/import JSON serialization
- DOM conversion for YouTube iframes

**Critical Code**:
```typescript
export class YouTubeNode extends DecoratorNode<JSX.Element>
// Lines 62-207: Complete node implementation
// Lines 240-315: React component with same styling structure as images
```

### 2. `/src/components/review-form/lexical/plugins/YouTubeInsertModal.tsx`
**Purpose**: Modal for inserting YouTube videos
**Lines**: 273 lines total
**Key Features**:
- Smart URL parsing for various YouTube URL formats
- Live preview of YouTube videos
- Optional title and caption fields
- Same styling patterns as ImageInsertModal
- Error handling and validation

**Critical Code**:
```typescript
const extractVideoId = (urlString: string): string | null => {
  // Lines 25-50: URL parsing logic for multiple YouTube formats
}
```

### 3. `/src/app/test-youtube/page.tsx`
**Purpose**: Test page for YouTube functionality
**Lines**: 96 lines total
**Key Features**:
- Complete Lexical editor setup
- Premium toolbar for testing
- CSS imports for styling
- Sample YouTube URLs for testing

## Files Modified

### 4. `/src/components/review-form/lexical/nodes.ts`
**Lines Modified**: 11-12, 28-29
**Changes**:
```typescript
// Line 12: Added import
import { YouTubeNode } from './nodes/YouTubeNode';

// Line 29: Added to node array
YouTubeNode,
```

### 5. `/src/components/review-form/lexical/theme.ts`
**Lines Modified**: 34-35
**Changes**:
```typescript
// Added between lines 34-35
youtube: 'editor-youtube',
```

### 6. `/src/components/review-form/lexical/plugins/ToolbarPlugin.tsx`
**Lines Modified**: Multiple sections
**Changes**:

**Imports (Lines 27-32)**:
```typescript
import { $createYouTubeNode } from '../nodes/YouTubeNode';
import YouTubeInsertModal from './YouTubeInsertModal';
```

**State (Lines 72-75)**:
```typescript
const [isYouTubeModalOpen, setIsYouTubeModalOpen] = useState(false);
```

**Functions (Lines 378-393)**:
```typescript
const handleYouTubeInsert = useCallback((videoId: string, title?: string, caption?: string) => {
  editor.update(() => {
    const youtubeNode = $createYouTubeNode({
      videoId,
      title,
      caption,
      showCaption: !!caption,
    });
    $insertNodes([youtubeNode]);
  });
}, [editor]);

const insertYouTube = useCallback(() => {
  if (!mounted) return;
  setIsYouTubeModalOpen(true);
}, [mounted]);
```

**Button (Lines 671-681)**:
```typescript
<button
  className="toolbar-button"
  onClick={insertYouTube}
  title="Insert YouTube Video"
  type="button"
>
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="currentColor"/>
  </svg>
</button>
```

**Modal (Lines 725-729)**:
```typescript
<YouTubeInsertModal
  isOpen={isYouTubeModalOpen}
  onClose={() => setIsYouTubeModalOpen(false)}
  onInsert={handleYouTubeInsert}
/>
```

### 7. `/src/components/review-form/lexical/plugins/PremiumToolbarPlugin.tsx`
**Lines Modified**: Identical changes to ToolbarPlugin.tsx
**Changes**:

**Imports (Lines 27-32)**:
```typescript
import { $createYouTubeNode } from '../nodes/YouTubeNode';
import YouTubeInsertModal from './YouTubeInsertModal';
```

**State (Lines 77-80)**:
```typescript
const [isYouTubeModalOpen, setIsYouTubeModalOpen] = useState(false);
```

**Functions (Lines 385-400)**:
```typescript
const handleYouTubeInsert = useCallback((videoId: string, title?: string, caption?: string) => {
  editor.update(() => {
    const youtubeNode = $createYouTubeNode({
      videoId,
      title,
      caption,
      showCaption: !!caption,
    });
    $insertNodes([youtubeNode]);
  });
}, [editor]);

const insertYouTube = useCallback(() => {
  if (!mounted) return;
  setIsYouTubeModalOpen(true);
}, [mounted]);
```

**Button (Lines 712-722)**:
```typescript
<button
  className="toolbar-button"
  onClick={insertYouTube}
  title="Insert YouTube Video"
  type="button"
>
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="currentColor"/>
  </svg>
</button>
```

**Modal (Lines 775-779)**:
```typescript
<YouTubeInsertModal
  isOpen={isYouTubeModalOpen}
  onClose={() => setIsYouTubeModalOpen(false)}
  onInsert={handleYouTubeInsert}
/>
```

### 8. `/src/app/reviews/view/[slug]/ReviewPageClient.tsx`
**Lines Modified**: 86-87
**Changes**:
```typescript
// Line 86: Added YouTube theme
youtube: 'editor-youtube',

// Lines 6-7: Added CSS imports
import '@/components/review-form/style/lexical.css';
import '@/components/review-form/style/lexicaltoolbar.css';
```

### 9. `/src/components/review-form/style/lexical.css`
**Lines Modified**: 804-819
**Changes**:
```css
/* YouTube Video Styles - Uses same structure as images for consistency */
.editor-youtube {
  /* YouTube videos will use the same figure styles as images */
}

/* YouTube videos inherit all image figure styles for consistency */
.lexical-image-figure:has(iframe),
.lexical-image-container:has(iframe) {
  /* YouTube videos use same spacing and effects as images */
}

/* Special hover effects for YouTube videos */
.lexical-image-clickable .lexical-image-container:has(iframe):hover::after {
  content: '▶️';
  font-size: 16px;
}
```

## Critical CSS Loading Issue Changes

### 10. `/src/components/review-form/lexical/Editor.tsx`
**Lines Modified**: 8-9, 142-143 (REMOVED)
**Changes That Caused Issues**:

**Added CSS imports at top (Lines 8-9)**:
```typescript
import '@/components/review-form/style/lexical.css';
import '@/components/review-form/style/lexicaltoolbar.css';
```

**Removed from dynamic imports (Lines 142-143 DELETED)**:
```typescript
// REMOVED THESE LINES:
import('@/components/review-form/style/lexical.css'),
import('@/components/review-form/style/lexicaltoolbar.css'),
```

### 11. `/src/app/reviews/new/page.tsx`
**Lines Modified**: 56-57
**Changes That May Have Contributed**:
```typescript
// Added CSS imports
import '@/components/review-form/style/lexical.css';
import '@/components/review-form/style/lexicaltoolbar.css';
```

## Root Cause Analysis

### The Breaking Change
The primary issue occurred when modifying `/src/components/review-form/lexical/Editor.tsx`:

1. **Original State**: CSS files were imported using dynamic imports within a Promise.all()
2. **Problem**: CSS files cannot be dynamically imported like JavaScript modules
3. **My Change**: Moved CSS imports to the top of the file (static imports)
4. **Side Effect**: Removed the CSS imports from the Promise.all() but this disrupted the existing CSS loading mechanism

### Potential Issues Created

1. **Duplicate CSS Imports**: Multiple components now import the same CSS files
2. **Import Order Issues**: CSS imports may be conflicting with existing stylesheets
3. **Build Process Disruption**: The change may have affected Next.js CSS processing
4. **SSR Hydration Issues**: CSS loading timing may have changed

## Files Affected by CSS Loading Issue

- All pages and components that rely on Lexical editor styling
- Review creation page (`/reviews/new`)
- Review view pages (`/reviews/view/[slug]`)
- User profile pages with review components
- Test pages and development tools

## Immediate Rollback Steps Required

1. **Revert Editor.tsx changes**:
   - Remove static CSS imports from lines 8-9
   - Restore original dynamic import structure (if it was working)

2. **Revert new page CSS imports**:
   - Remove added CSS imports from `/src/app/reviews/new/page.tsx`
   - Remove added CSS imports from review view page

3. **Investigate original CSS loading mechanism**:
   - Check how CSS was originally loaded
   - Verify if dynamic CSS imports were actually working
   - Find the correct way to load CSS for the editor

## Notes for Future Implementation

1. **CSS Import Strategy**: Need to determine the correct way to import CSS for Lexical components
2. **Global vs Component CSS**: Consider if these styles should be global or component-scoped
3. **Build Process**: Ensure changes don't disrupt Next.js CSS processing
4. **Testing**: Always test CSS loading in development environment before major changes

## Files to Investigate for Original CSS Loading

- Check if CSS was imported in a layout file
- Look for CSS imports in the main app layout
- Verify if CSS was loaded through a different mechanism
- Check if there's a global CSS file that includes these styles

## Recommended Next Steps

1. Immediately revert CSS-related changes
2. Research the original CSS loading mechanism
3. Test YouTube functionality with original CSS loading intact
4. Find a non-disruptive way to ensure CSS is available for new components