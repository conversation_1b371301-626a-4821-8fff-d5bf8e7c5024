'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, RefreshCw, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

interface MFAChallengeProps {
  onSuccess: () => void;
  onCancel?: () => void;
  title?: string;
  description?: string;
}

export default function MFAChallenge({
  onSuccess,
  onCancel,
  title = "Verificação de Segurança Necessária",
  description = "Para acessar esta área administrativa, você precisa verificar sua identidade com autenticação de dois fatores."
}: MFAChallengeProps) {
  const [token, setToken] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [attempts, setAttempts] = useState(0);
  const maxAttempts = 3;

  // DEBUG: Log when component mounts
  useEffect(() => {
    console.log('🔐 MFAChallenge: Component mounted with props:', { title, description });
    return () => {
      console.log('🔐 MFAChallenge: Component unmounted');
    };
  }, []);

  // Auto-focus on mount
  useEffect(() => {
    const input = document.getElementById('mfa-code-input');
    if (input) {
      input.focus();
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token.trim() || token.length !== 6) {
      setError('Por favor, insira um código de 6 dígitos');
      return;
    }

    if (attempts >= maxAttempts) {
      setError('Muitas tentativas falharam. Tente novamente mais tarde.');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/admin/mfa-verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token.trim(),
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Falha na verificação MFA');
      }

      if (result.success) {
        toast.success('Verificação MFA bem-sucedida!');
        onSuccess();
      } else {
        throw new Error(result.error || 'Código inválido');
      }
    } catch (error: any) {
      console.error('MFA verification error:', error);
      setAttempts(prev => prev + 1);
      setError(error.message || 'Erro ao verificar código MFA');
      setToken(''); // Clear the input
      
      // Focus back on input for retry
      setTimeout(() => {
        const input = document.getElementById('mfa-code-input');
        if (input) {
          input.focus();
        }
      }, 100);
    } finally {
      setLoading(false);
    }
  };

  const handleCodeChange = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setToken(numericValue);
    setError(''); // Clear error when user types
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any);
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/80 flex items-center justify-center z-[9999] p-4 backdrop-blur-sm"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        zIndex: 99999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Card className="w-full max-w-md mx-auto shadow-2xl border-purple-500/30 bg-slate-900/95 backdrop-blur-md"
        style={{
          backgroundColor: 'rgba(15, 23, 42, 0.95)',
          border: '1px solid rgba(168, 85, 247, 0.3)',
          borderRadius: '8px'
        }}
      >
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <CardTitle className="text-xl font-bold">{title}</CardTitle>
          <CardDescription className="text-sm text-gray-600">
            {description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {attempts > 0 && attempts < maxAttempts && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Tentativa {attempts} de {maxAttempts}. {maxAttempts - attempts} tentativas restantes.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="mfa-code-input" className="block text-sm font-medium mb-2">
                Código de Autenticação (6 dígitos)
              </label>
              <Input
                id="mfa-code-input"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                placeholder="000000"
                value={token}
                onChange={(e) => handleCodeChange(e.target.value)}
                onKeyPress={handleKeyPress}
                maxLength={6}
                className="text-center text-lg tracking-widest font-mono"
                disabled={loading || attempts >= maxAttempts}
                autoComplete="one-time-code"
              />
              <p className="text-xs text-gray-500 mt-1">
                Digite o código de 6 dígitos do seu aplicativo autenticador
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={loading || token.length !== 6 || attempts >= maxAttempts}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Verificando...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Verificar
                  </>
                )}
              </Button>
              
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  Cancelar
                </Button>
              )}
            </div>
          </form>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Não consegue acessar seu autenticador?{' '}
              <button 
                type="button"
                className="text-blue-600 hover:underline"
                onClick={() => {
                  toast.info('Entre em contato com o administrador do sistema para assistência com códigos de backup.');
                }}
              >
                Use código de backup
              </button>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
