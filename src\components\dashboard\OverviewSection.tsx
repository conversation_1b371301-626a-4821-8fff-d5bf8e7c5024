'use client';

import { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Gauge,
  Star,
  Clock,
  BarChart3,
  TrendingUp,
  Calendar,
  Award,
  Target,
  Zap,
  Plus,
  ExternalLink,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { Review } from '@/lib/types';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';
import type { DashboardStats } from '@/types/dashboard';

export interface OverviewSectionProps {
  user: {
    uid: string;
    displayName?: string;
    userName?: string;
    slug?: string;
  } | null;
  reviews: Review[];
  surveys: PerformanceSurveyRecord[];
  stats: DashboardStats;
  isLoading?: boolean;
}

export function OverviewSection({
  user,
  reviews,
  surveys,
  stats,
  isLoading = false
}: OverviewSectionProps) {
  if (!user) {
    return null;
  }
  // Calculate insights and recommendations
  const insights = useMemo(() => {
    const isNewUser = stats.totalReviews === 0 && stats.totalSurveys === 0;
    // Ensure reviews is an array to prevent undefined errors
    const safeReviews = reviews || [];
    const recentReviews = safeReviews.filter(review => {
      const dateValue = review.publishDate || review.createdAt;
      const reviewDate = new Date(
        dateValue && typeof dateValue === 'object' && 'toDate' in dateValue
          ? dateValue.toDate()
          : dateValue || 0
      );
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return reviewDate > thirtyDaysAgo;
    }).length;

    const recentSurveys = surveys.filter(survey => {
      const surveyDate = new Date(survey.created_at || 0);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return surveyDate > thirtyDaysAgo;
    }).length;

    // Calculate next milestone
    let nextMilestone = '';
    if (stats.totalReviews < 5) {
      nextMilestone = `${5 - stats.totalReviews} more reviews to reach Rising Reviewer`;
    } else if (stats.totalReviews < 10) {
      nextMilestone = `${10 - stats.totalReviews} more reviews to reach Active Contributor`;
    } else if (stats.totalReviews < 20) {
      nextMilestone = `${20 - stats.totalReviews} more reviews to reach Expert Reviewer`;
    } else {
      nextMilestone = 'Expert Reviewer achieved!';
    }

    return {
      isNewUser,
      recentReviews,
      recentSurveys,
      nextMilestone
    };
  }, [reviews, surveys, stats]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6">
            <div className="h-6 bg-slate-700 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-slate-700 rounded w-3/4"></div>
              <div className="h-4 bg-slate-700 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Message for New Users */}
      {insights.isNewUser && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/30 rounded-lg p-6"
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-purple-600/20 rounded-full flex items-center justify-center">
              <Zap className="text-purple-400" size={24} />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-slate-200 mb-2">
                Welcome to CriticalPixel! 🎮
              </h2>
              <p className="text-slate-300 mb-4">
                Ready to start your gaming journey? Share your experiences and help the community discover amazing games.
              </p>
              <div className="flex flex-wrap gap-3">
                <Button asChild className="bg-purple-600 hover:bg-purple-700">
                  <Link href="/reviews/create">
                    <Plus size={16} className="mr-2" />
                    Write Your First Review
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/reviews">
                    <BarChart3 size={16} className="mr-2" />
                    Explore Reviews
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6"
      >
        <h2 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
          <Target className="text-purple-400" size={20} />
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Button asChild className="bg-purple-600 hover:bg-purple-700 h-auto p-4 justify-start">
            <Link href="/reviews/create">
              <div className="flex items-center gap-3">
                <Plus size={20} />
                <div className="text-left">
                  <div className="font-medium">Create Review</div>
                  <div className="text-xs opacity-80">Share your gaming experience</div>
                </div>
              </div>
            </Link>
          </Button>
          
          <Button variant="outline" asChild className="h-auto p-4 justify-start">
            <Link href={`/u/${user?.slug || ''}`}>
              <div className="flex items-center gap-3">
                <ExternalLink size={20} />
                <div className="text-left">
                  <div className="font-medium">View Profile</div>
                  <div className="text-xs opacity-80">See your public profile</div>
                </div>
              </div>
            </Link>
          </Button>

          <Button variant="outline" asChild className="h-auto p-4 justify-start">
            <Link href="/reviews">
              <div className="flex items-center gap-3">
                <BarChart3 size={20} />
                <div className="text-left">
                  <div className="font-medium">Browse Reviews</div>
                  <div className="text-xs opacity-80">Discover new games</div>
                </div>
              </div>
            </Link>
          </Button>
        </div>
      </motion.div>

      {/* Activity Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6"
      >
        <h2 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
          <TrendingUp className="text-green-400" size={20} />
          Activity Summary
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-slate-800/50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="text-blue-400" size={16} />
              <span className="text-slate-400 text-sm">This Month</span>
            </div>
            <div className="text-xl font-bold text-slate-200">
              {insights.recentReviews + insights.recentSurveys}
            </div>
            <div className="text-xs text-slate-500">
              {insights.recentReviews} reviews, {insights.recentSurveys} surveys
            </div>
          </div>

          <div className="bg-slate-800/50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Award className="text-purple-400" size={16} />
              <span className="text-slate-400 text-sm">Next Milestone</span>
            </div>
            <div className="text-sm font-medium text-slate-200">
              {insights.nextMilestone}
            </div>
          </div>

          <div className="bg-slate-800/50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Star className="text-yellow-400" size={16} />
              <span className="text-slate-400 text-sm">Avg Rating</span>
            </div>
            <div className="text-xl font-bold text-slate-200">
              {stats.averageScore > 0 ? `${stats.averageScore}/10` : '—'}
            </div>
            <div className="text-xs text-slate-500">
              {stats.totalReviews > 0 ? `From ${stats.totalReviews} reviews` : 'No reviews yet'}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-slate-200 flex items-center gap-2">
            <Clock className="text-cyan-400" size={20} />
            Recent Activity
          </h2>
          {((reviews || []).length > 3 || surveys.length > 2) && (
            <Button variant="ghost" size="sm" asChild>
              <Link href="#" onClick={() => {/* Switch to appropriate tab */}}>
                View All
                <ArrowRight size={14} className="ml-1" />
              </Link>
            </Button>
          )}
        </div>

        {(reviews || []).length === 0 && surveys.length === 0 ? (
          <div className="text-center py-8">
            <BarChart3 className="mx-auto text-slate-500 mb-4" size={48} />
            <p className="text-slate-400 mb-4">No activity yet</p>
            <p className="text-slate-500 text-sm mb-4">
              Start by creating your first review or completing a performance survey
            </p>
            <Button asChild className="bg-purple-600 hover:bg-purple-700">
              <Link href="/reviews/create">
                <Plus size={16} className="mr-2" />
                Get Started
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Recent Reviews */}
            {(reviews || []).slice(0, 3).map((review) => (
              <motion.div
                key={review.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-colors"
              >
                <FileText className="text-blue-400 flex-shrink-0" size={16} />
                <div className="flex-1 min-w-0">
                  <p className="text-slate-200 font-medium truncate">{review.title}</p>
                  <p className="text-slate-400 text-sm">
                    {review.publishDate
                      ? `Published ${new Date(review.publishDate as any).toLocaleDateString()}`
                      : 'Draft'
                    }
                  </p>
                </div>
                <div className="flex items-center gap-1 flex-shrink-0">
                  <Star className="text-yellow-400" size={14} />
                  <span className="text-slate-300 text-sm">{review.overallScore || 0}</span>
                </div>
              </motion.div>
            ))}

            {/* Recent Surveys */}
            {surveys.slice(0, 2).map((survey) => (
              <motion.div
                key={survey.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-colors"
              >
                <Gauge className="text-green-400 flex-shrink-0" size={16} />
                <div className="flex-1 min-w-0">
                  <p className="text-slate-200 font-medium truncate">
                    {survey.game_title || 'Performance Survey'}
                  </p>
                  <p className="text-slate-400 text-sm">
                    {survey.created_at
                      ? `Completed ${new Date(survey.created_at).toLocaleDateString()}`
                      : 'Recently completed'
                    }
                  </p>
                </div>
                <div className="text-slate-300 text-sm flex-shrink-0">
                  {survey.device_type}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </div>
  );
}
