import React from 'react';

const ExternalLinkIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M15 3h6v6" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M10 14L21 3" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="3" y="7" width="1" height="1"/>
    <rect x="3" y="18" width="1" height="1"/>
    <rect x="16" y="18" width="1" height="1"/>
    <circle cx="19" cy="5" r="0.5"/>
    <circle cx="17" cy="7" r="0.5"/>
  </svg>
);

export default ExternalLinkIcon;