'use client';

import { useEffect, useState, useMemo, useCallback } from 'react';
import Link from 'next/link';

import ReviewBanner from '@/components/review-new/reviewBanner';
import UserProfileCard from '@/components/review-new/UserProfileCard';
import C<PERSON>BannerTop from '@/components/review-new/creatorBannerTop';
import GameInfoCard from '@/components/review-new/GameInfoCard';
import CreatorBannerBottom from '@/components/review-new/creatorBannerBottom';
import LinearToggle from '@/components/review-new/LinearToggle';
import { ForumSystem } from '@/components/forum';
import GameStyleUserMenu from '@/components/layout/GameStyleUserMenu';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Gamepad2, Sun, Moon } from 'lucide-react';

import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import type { Review } from '@/lib/types';
import { useReview } from '@/hooks/useUserReviews';
import { useAuthContext } from '@/contexts/auth-context';
import EditorNodes from '@/components/review-form/lexical/nodes';
import { ReportButton } from '@/components/review/ReportButton';
import { SimpleViewTracker } from '@/components/analytics/ViewTracker';
import { AIDetectionVotingSimple } from '@/components/review/AIDetectionVotingSimple';

const editorConfig = {
  namespace: 'ReviewEditor',
  nodes: EditorNodes,
  onError: (error: Error) => {
    console.error('Lexical error:', error);
  },
  editable: false,
  theme: {
    paragraph: 'editor-paragraph',
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      underline: 'editor-text-underline',
      strikethrough: 'editor-text-strikethrough',
      underlineStrikethrough: 'editor-text-underlineStrikethrough',
      code: 'editor-text-code',
    },
    heading: {
      h1: 'editor-heading-h1',
      h2: 'editor-heading-h2',
      h3: 'editor-heading-h3',
      h4: 'editor-heading-h4',
      h5: 'editor-heading-h5',
      h6: 'editor-heading-h6',
    },
    link: 'editor-link',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    quote: 'editor-quote',
    code: 'editor-code',
    codeHighlight: {
      comment: 'editor-tokenComment',
      punctuation: 'editor-tokenPunctuation',
      property: 'editor-tokenProperty',
      selector: 'editor-tokenSelector',
      operator: 'editor-tokenOperator',
      attr: 'editor-tokenAttr',
      variable: 'editor-tokenVariable',
      function: 'editor-tokenFunction',
    },
    hashtag: 'editor-hashtag',
    table: 'editor-table',
    tableCell: 'editor-table-cell',
    tableCellHeader: 'editor-table-cell-header',
    youtube: 'editor-youtube',
  },
};

interface ModeToggleProps {
  isLight: boolean;
  onToggle: () => void;
  bubble: { show: boolean; message: string; type: string };
  onBubbleClose: () => void;
}

const ModeToggle: React.FC<ModeToggleProps> = ({ isLight, onToggle, bubble, onBubbleClose }) => (
  <div className="flex justify-start items-center gap-2 mb-4">
    <button
      type="button"
      onClick={onToggle}
      className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 border-2 ${
        isLight
          ? 'bg-amber-100 border-amber-300 text-amber-800 hover:bg-amber-200'
          : 'bg-slate-800 border-slate-600 text-slate-200 hover:bg-slate-700'
      }`}
    >
      {isLight ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
      <span className="text-xs font-mono font-bold">
        {isLight ? 'PSYCHOPATH' : 'NORMAL'}
      </span>
    </button>

    {bubble.show && (
      <div className="animate-in slide-in-from-left-4 fade-in duration-300">
        <div
          className={`relative px-3 py-2 rounded-lg border ${
            bubble.type === 'psychopath'
              ? 'bg-red-900 border-red-500 text-red-100'
              : 'bg-amber-900 border-amber-500 text-amber-100'
          }`}
        >
          <div className="text-xs font-mono font-bold">{bubble.message}</div>
          <button
            type="button"
            onClick={onBubbleClose}
            className={`absolute -top-1 -right-1 w-4 h-4 rounded-full text-xs font-bold leading-none flex items-center justify-center ${
              bubble.type === 'psychopath'
                ? 'bg-red-500 text-red-100 hover:bg-red-400'
                : 'bg-amber-500 text-amber-100 hover:bg-amber-400'
            }`}
          >
            ×
          </button>
        </div>
      </div>
    )}
  </div>
);

const NotFound = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center py-10 px-4">
      <h1 className="text-6xl font-bold mb-4 text-white">
        <span className="text-violet-400">&lt;</span>
        <span className="text-red-400">404</span>
        <span className="text-violet-400">/&gt;</span>
      </h1>
      <h2 className="text-2xl font-bold text-white mb-4">
        <span className="text-violet-400">&lt;</span>Review_Not_Found<span className="text-violet-400">/&gt;</span>
      </h2>
      <p className="text-slate-300 mb-8">Review not found or may have been moved.</p>
      <Link href="/reviews">
        <Button className="bg-violet-600 hover:bg-violet-700 text-sm px-6 py-3">
          <Gamepad2 className="w-4 h-4 mr-2" />Back_To_Reviews
        </Button>
      </Link>
    </div>
  </div>
);

interface LoadingSkeletonProps {
  windowWidth: number;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ windowWidth }) => (
  <div className="min-h-screen">
    {/* Banner area - uses banner width */}
    <div className="mx-auto responsive-banner-width">
      <div className="relative overflow-hidden h-[80vh] min-h-[600px] mx-4 my-8 rounded-2xl bg-slate-800 shadow-2xl">
        <div className="absolute bottom-8 left-8 z-10">
          <div className="bg-black/20 backdrop-blur-lg border border-white/30 rounded-xl p-4 max-w-sm">
            <Skeleton className="h-6 w-48 bg-slate-700 mb-2" />
            <Skeleton className="h-4 w-32 bg-slate-700 mb-4" />
            <Skeleton className="h-8 w-20 bg-slate-700" />
          </div>
        </div>
      </div>
    </div>
    {/* Content area - uses content width */}
    <div className="mx-auto responsive-content-width">
      <div className="p-8">
        <div className="space-y-6">
          <Skeleton className="h-4 w-full bg-slate-800" />
          <Skeleton className="h-4 w-full bg-slate-800" />
          <Skeleton className="h-4 w-3/4 bg-slate-800" />
        </div>
      </div>
    </div>
  </div>
);

interface ReviewPageClientProps {
  slug: string;
  initialReview?: Review | null;
}

export default function ReviewPageClient({ slug, initialReview }: ReviewPageClientProps) {
  const { user } = useAuthContext();
  
  // Add client-side check to prevent SSR issues
  const [mounted, setMounted] = useState(false);
  const [highlightedCommentId, setHighlightedCommentId] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
    
    // Check URL parameters for comment highlighting
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const highlight = urlParams.get('highlight');
      const scrollTo = urlParams.get('scrollTo');
      
      if (highlight === 'true' && scrollTo) {
        // Extract comment ID from scrollTo parameter (format: comment-{id})
        const commentId = scrollTo.replace('comment-', '');
        setHighlightedCommentId(commentId);
        
        // Robust auto-scroll with retry mechanism
        const attemptScroll = (attempt = 1, maxAttempts = 10) => {
          const element = document.getElementById(scrollTo);
          
          if (element) {
            // Element found, scroll to it
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Optional: Add a subtle flash effect to highlight the element
            element.style.transition = 'all 0.3s ease';
            element.style.transform = 'scale(1.02)';
            setTimeout(() => {
              element.style.transform = 'scale(1)';
            }, 300);
            
            return;
          }
          
          // Element not found, retry if we haven't exceeded max attempts
          if (attempt < maxAttempts) {
            const delay = Math.min(500 * attempt, 3000); // Progressive delay: 500ms, 1s, 1.5s, 2s, 2.5s, 3s...
            setTimeout(() => attemptScroll(attempt + 1, maxAttempts), delay);
          } else {
            console.warn(`Could not find element with ID: ${scrollTo} after ${maxAttempts} attempts`);
          }
        };
        
        // Start scrolling attempts after initial mount
        setTimeout(() => attemptScroll(), 500);
      }
    }
  }, []);

  // Use the review hook for data fetching only on client side
  const { data: fetchedReview, isLoading, error } = useReview(slug, mounted && typeof window !== 'undefined');

  // Use fetched review or initial review
  const review = initialReview || fetchedReview;
  const loading = isLoading && !initialReview;

  const [windowWidth, setWindowWidth] = useState(0);
  const [isLight, setIsLight] = useState(false);
  const [bubble, setBubble] = useState({ show: false, message: '', type: 'psychopath' });
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleMode = useCallback(() => {
    const newMode = !isLight;
    setIsLight(newMode);
    setBubble({
      show: true,
      message: newMode ? 'Psychopath Mode' : 'Normal People Mode',
      type: newMode ? 'psychopath' : 'normal',
    });
    setTimeout(() => setBubble((prev) => ({ ...prev, show: false })), 3000);
  }, [isLight]);

  if (loading) return <LoadingSkeleton windowWidth={windowWidth} />;
  if (error) {
    console.error('Error loading review:', error);
    return <NotFound />;
  }
  if (!review) return <NotFound />;

  // Memoize light mode styles to prevent recreation on every render
  const lightModeStyles = useMemo(() => `
    .light-mode-content * {
      color: rgb(41 37 36) !important; /* text-stone-800 - darker, closer to black */
    }
    
    /* Fix column issue in light mode */
    .light-mode-content .prose {
      max-width: none !important;
      width: 100% !important;
    }
  `, []);

  return (
    <>
      {isLight && <style dangerouslySetInnerHTML={{ __html: lightModeStyles }} />}
      <div className="min-h-screen">
        {/* Banner uses full banner width */}
        <ReviewBanner review={review} />

        {/* Content below banner gets content width (75%) */}
        <div className="mx-auto responsive-content-width relative">
          {/* View tracking for unique daily views */}
          <SimpleViewTracker reviewId={review.id} reviewSlug={slug} delay={2000} threshold={3000} />
          
          {/* User Profile Container - Extracted from ReviewBanner */}
          <UserProfileCard review={review} />
          
          {/* Creator Banner Top */}
          <CreatorBannerTop review={review} />
          
          {/* Game Info Container - Extracted from ReviewBanner */}
          <GameInfoCard review={review} />
          <div className="w-full max-w-full 2xl:max-w-[75%] mx-auto">
            <div className="mb-6">
              {/* Mode toggle positioned above the content container, aligned left */}
              <ModeToggle
                isLight={isLight}
                onToggle={toggleMode}
                bubble={bubble}
                onBubbleClose={useCallback(() => setBubble((prev) => ({ ...prev, show: false })), [])}
              />
              
              <div
                className={`relative border rounded-xl overflow-hidden transition-all duration-500 ${
                  isLight 
                    ? 'border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50' 
                    : 'border-slate-700 bg-gradient-to-br from-slate-900 to-slate-800'
                }`}
              >
                {review.contentLexical ? (
                  <LexicalComposer 
                    key={review.id} 
                    initialConfig={{
                      ...editorConfig,
                      editorState: review.contentLexical
                    }}
                  >
                    <div className={`relative ${isLight ? 'light-mode-content' : ''}`}>
                      <RichTextPlugin
                        contentEditable={
                          <ContentEditable
                            className={`lexical-content-editable min-h-[400px] outline-none transition-all duration-500 w-full ${
                              isLight
                                ? '!text-stone-800 prose prose-stone max-w-none !prose-headings:text-stone-800 !prose-p:text-stone-800 !prose-strong:text-stone-800 !prose-em:text-stone-800 !prose-li:text-stone-800 !prose-code:text-stone-800 !prose-a:text-stone-800 !prose-ul:text-stone-800 !prose-ol:text-stone-800 !prose-blockquote:text-stone-800'
                                : 'text-slate-100 prose-slate prose-invert max-w-none'
                            }`}
                            style={{ padding: '2rem' }}
                          />
                        }
                        placeholder={null}
                        ErrorBoundary={LexicalErrorBoundary}
                      />
                      <HistoryPlugin />
                      <ListPlugin />
                      <TablePlugin hasCellMerge={false} hasCellBackgroundColor={false} />
                    </div>
                  </LexicalComposer>
                ) : (
                  <div className="p-8 text-slate-400 text-center">
                    No content available for this review.
                  </div>
                )}
                {/* Action buttons aligned to the right */}
                <div className="flex justify-end items-center gap-4 mt-2 mr-4">
                  <AIDetectionVotingSimple reviewId={review.id} />
                  <ReportButton
                    reviewId={review.id}
                    reviewTitle={review.gameName || review.title || 'Review'}
                    triggerAsText
                  />
                </div>
              </div>
            </div>
            
            {/* LinearToggle component aligned to the left with minimal spacing */}
            <div className="w-full mt-4 mb-2">
              <LinearToggle 
                platforms={review.platforms || []}
                genres={review.genres || []}
                tags={review.tags || []}
              />
            </div>
          </div>
        </div>
        

        {/* Creator Banner Bottom - uses SAME responsive width as lexical content */}
        <div className="mx-auto responsive-content-width mt-0 mb-16">
          <CreatorBannerBottom review={review} />
        </div>
        
        {/* Forum System Component - positioned below Creator Banner with consistent width */}
        {/* Only render if comments are enabled for this review */}
        {(review.enable_comments ?? true) && (
          <div className="mx-auto responsive-content-width mt-0 mb-16">
            <div className="w-full max-w-full 2xl:max-w-[75%] mx-auto">
              <ForumSystem
                reviewId={review.id}
                reviewTitle={review.gameName || review.title || 'Review'}
                reviewAuthorId={review.authorId || review.creatorId || review.userId}
                onOpenSidebar={() => setSidebarOpen(true)}
                highlightedCommentId={highlightedCommentId}
              />
            </div>
          </div>
        )}
      </div>

      <GameStyleUserMenu open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
    </>
  );
}
