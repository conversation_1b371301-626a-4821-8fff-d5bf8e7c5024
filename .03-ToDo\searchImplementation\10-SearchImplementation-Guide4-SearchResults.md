#### Search Results Component

```typescript
// File: /src/components/search/SearchResults.tsx
import { <PERSON>, CardContent, CardHeader, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Loader2, Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { SearchResults as SearchResultsType } from './SearchInterface';

interface SearchResultsProps {
  results: SearchResultsType;
  searchType: 'all' | 'games' | 'reviews' | 'users' | 'hardware';
  isLoading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

/**
 * Component to display search results based on search type
 */
export function SearchResults({ 
  results, 
  searchType, 
  isLoading, 
  hasMore, 
  onLoadMore 
}: SearchResultsProps) {
  const hasResults = (
    (searchType === 'all' && 
      (results.games.length > 0 || 
       results.reviews.length > 0 || 
       results.users.length > 0 || 
       results.hardware.length > 0)
    ) ||
    (searchType === 'games' && results.games.length > 0) ||
    (searchType === 'reviews' && results.reviews.length > 0) ||
    (searchType === 'users' && results.users.length > 0) ||
    (searchType === 'hardware' && results.hardware.length > 0)
  );

  // If no results and not loading, show empty state
  if (!hasResults && !isLoading) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <h3 className="text-2xl font-semibold mb-3">No results found</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your search or filter criteria.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Determine which tab to show first based on search type
  const getDefaultTab = () => {
    if (searchType !== 'all') return searchType;
    
    // For 'all' search, show the tab with results first
    if (results.games.length > 0) return 'games';
    if (results.reviews.length > 0) return 'reviews';
    if (results.users.length > 0) return 'users';
    if (results.hardware.length > 0) return 'hardware';
    
    return 'games'; // Default
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Search Results</CardTitle>
          <CardDescription>
            {isLoading ? (
              <span className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching...
              </span>
            ) : (
              <span>Found {results.totalResults} results in {results.searchTime}ms</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {searchType === 'all' ? (
            <Tabs defaultValue={getDefaultTab()} className="w-full">
              <TabsList className="w-full">
                <TabsTrigger value="games" disabled={results.games.length === 0}>
                  Games ({results.games.length})
                </TabsTrigger>
                <TabsTrigger value="reviews" disabled={results.reviews.length === 0}>
                  Reviews ({results.reviews.length})
                </TabsTrigger>
                <TabsTrigger value="users" disabled={results.users.length === 0}>
                  Users ({results.users.length})
                </TabsTrigger>
                <TabsTrigger value="hardware" disabled={results.hardware.length === 0}>
                  Hardware ({results.hardware.length})
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="games" className="space-y-4 mt-4">
                {renderGameResults(results.games)}
              </TabsContent>
              
              <TabsContent value="reviews" className="space-y-4 mt-4">
                {renderReviewResults(results.reviews)}
              </TabsContent>
              
              <TabsContent value="users" className="space-y-4 mt-4">
                {renderUserResults(results.users)}
              </TabsContent>
              
              <TabsContent value="hardware" className="space-y-4 mt-4">
                {renderHardwareResults(results.hardware)}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="space-y-4">
              {searchType === 'games' && renderGameResults(results.games)}
              {searchType === 'reviews' && renderReviewResults(results.reviews)}
              {searchType === 'users' && renderUserResults(results.users)}
              {searchType === 'hardware' && renderHardwareResults(results.hardware)}
            </div>
          )}
        </CardContent>
        {hasMore && (
          <CardFooter>
            <Button 
              onClick={onLoadMore} 
              className="w-full" 
              variant="outline"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading more...
                </>
              ) : (
                'Load more results'
              )}
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}

/**
 * Render game search results
 */
function renderGameResults(games: any[]) {
  return games.map((game) => (
    <Link href={`/games/${game.slug}`} key={game.id}>
      <Card className="overflow-hidden hover:shadow-md transition-shadow">
        <div className="flex">
          <div className="w-24 h-32 relative">
            {game.cover ? (
              <Image
                src={game.cover}
                alt={game.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <span>No image</span>
              </div>
            )}
          </div>
          <div className="flex-1 p-4">
            <h3 className="font-semibold text-lg">{game.name}</h3>
            <div className="flex flex-wrap gap-1 my-2">
              {game.platforms?.slice(0, 3).map((platform: string) => (
                <Badge key={platform} variant="outline">{platform}</Badge>
              ))}
              {game.platforms?.length > 3 && (
                <Badge variant="outline">+{game.platforms.length - 3}</Badge>
              )}
            </div>
            <div className="flex items-center">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
              <span>{game.rating?.toFixed(1) || 'N/A'}</span>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
              {game.summary || 'No description available'}
            </p>
          </div>
        </div>
      </Card>
    </Link>
  ));
}

/**
 * Render review search results
 */
function renderReviewResults(reviews: any[]) {
  return reviews.map((review) => (
    <Link href={`/reviews/${review.slug}`} key={review.id}>
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="pt-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-semibold text-lg">{review.title}</h3>
              <p className="text-sm text-muted-foreground">
                Review of <span className="font-medium">{review.game_name}</span>
              </p>
            </div>
            <div className="bg-primary text-primary-foreground px-3 py-1 rounded-md font-bold">
              {review.score.toFixed(1)}
            </div>
          </div>
          <div className="flex items-center mt-3 mb-2">
            <Avatar className="h-6 w-6 mr-2">
              <AvatarImage src={review.author_avatar} />
              <AvatarFallback>{review.author_name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm">{review.author_name}</span>
            <span className="mx-2 text-muted-foreground">•</span>
            <span className="text-sm text-muted-foreground">
              {new Date(review.published_date).toLocaleDateString()}
            </span>
          </div>
          <p className="text-sm line-clamp-3 mt-2">
            {review.summary || 'No summary available'}
          </p>
        </CardContent>
      </Card>
    </Link>
  ));
}

/**
 * Render user search results
 */
function renderUserResults(users: any[]) {
  return users.map((user) => (
    <Link href={`/users/${user.username}`} key={user.id}>
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <Avatar className="h-12 w-12 mr-4">
              <AvatarImage src={user.avatar} />
              <AvatarFallback>{user.username.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold">{user.username}</h3>
              <p className="text-sm text-muted-foreground">{user.bio || 'No bio available'}</p>
              <div className="flex items-center gap-4 mt-2">
                <div className="text-sm">
                  <span className="font-medium">{user.reviews_count}</span> reviews
                </div>
                <div className="text-sm">
                  <span className="font-medium">{user.comments_count}</span> comments
                </div>
                <div className="text-sm">
                  <span className="font-medium">{user.followers_count}</span> followers
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  ));
}

/**
 * Render hardware search results
 */
function renderHardwareResults(hardware: any[]) {
  return hardware.map((item) => (
    <Link href={`/hardware/${item.slug}`} key={item.id}>
      <Card className="overflow-hidden hover:shadow-md transition-shadow">
        <div className="flex">
          <div className="w-32 h-32 relative">
            {item.image ? (
              <Image
                src={item.image}
                alt={item.name}
                fill
                className="object-contain p-2"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <span>No image</span>
              </div>
            )}
          </div>
          <div className="flex-1 p-4">
            <h3 className="font-semibold text-lg">{item.name}</h3>
            <div className="flex flex-wrap gap-1 my-2">
              <Badge>{item.category}</Badge>
              <Badge variant="outline">{item.manufacturer}</Badge>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
              {item.description || 'No description available'}
            </p>
            {item.price && (
              <div className="font-medium mt-2">
                ${item.price.toFixed(2)}
              </div>
            )}
          </div>
        </div>
      </Card>
    </Link>
  ));
}
```
