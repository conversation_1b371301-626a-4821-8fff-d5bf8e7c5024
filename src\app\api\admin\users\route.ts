// Admin Users List API - Supabase Implementation
// Date: 16/01/2025
// Task: adminSystemImpl002 - Fix 500 error for user listing

import { NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

// Helper function to verify admin permissions
async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createServerClient();

    // Get the user profile directly
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Admin verification profile error:', profileError);
      return false;
    }

    return profile?.is_admin === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Helper function to log admin actions
async function logAdminAction(adminId: string, action: string, data?: any): Promise<void> {
  console.log('ADMIN AUDIT LOG:', {
    timestamp: new Date().toISOString(),
    admin_id: adminId,
    action,
    data
  });
}

// Get users list (admin only)
export async function GET(request: Request) {
  try {
    const supabase = createServerClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify admin permissions
    const isAuthorized = await verifyAdminPermissions(user.id);
    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const search = url.searchParams.get('search') || '';
    const role = url.searchParams.get('role') || '';

    // Build query with filters
    let query = supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        avatar_url,
        banner_url,
        bio,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        suspended,
        created_at,
        updated_at
      `, { count: 'exact' });

    // Apply search filter
    if (search) {
      query = query.or(`username.ilike.%${search}%,display_name.ilike.%${search}%`);
    }

    // Apply role filter (admin status)
    if (role === 'admin') {
      query = query.eq('is_admin', true);
    } else if (role === 'user') {
      query = query.eq('is_admin', false);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      console.error('Admin API GET: Database error:', error);
      return NextResponse.json(
        { error: `Database error: ${error.message}` },
        { status: 500 }
      );
    }

    // Convert to UserProfile format
    const users = (data || []).map((profile: any) => ({
      uid: profile.id,
      email: profile.username || '', // Use username as email placeholder
      displayName: profile.display_name,
      photoURL: profile.avatar_url,
      username: profile.username,
      bio: profile.bio,
      isAdmin: profile.is_admin,
      isOnline: profile.is_online,
      lastSeen: profile.last_seen,
      level: profile.level,
      experience: profile.experience,
      reviewCount: profile.review_count,
      creationTime: profile.created_at,
      lastSignInTime: profile.last_seen, // Using last_seen as proxy
      role: profile.is_admin ? 'Admin' : 'User',
      disabled: profile.suspended || false
    }));

    const total = count || 0;
    const totalPages = Math.ceil(total / limit);

    // Log admin action
    await logAdminAction(user.id, 'getUsersList', { page, limit, search, role, total });

    return NextResponse.json({
      users,
      total,
      page,
      limit,
      totalPages
    });

  } catch (error) {
    console.error('Admin API GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle POST requests for server actions
export async function POST(request: Request) {
  try {
    const supabase = await createServerClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify admin permissions
    const isAuthorized = await verifyAdminPermissions(user.id);
    if (!isAuthorized) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action, ...params } = body;

    switch (action) {
      case 'getUsersList':
        // Redirect to GET method logic
        const searchParams = new URLSearchParams();
        if (params.page) searchParams.set('page', params.page.toString());
        if (params.limit) searchParams.set('limit', params.limit.toString());
        if (params.search) searchParams.set('search', params.search);
        if (params.role) searchParams.set('role', params.role);
        
        const getRequest = new Request(`${request.url}?${searchParams.toString()}`);
        return await GET(getRequest);

      default:
        return NextResponse.json(
          { error: 'Unknown action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Admin API POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
