# CriticalPixel - Correção Erro 400 API MFA - COMPLETA

**Data**: 16 de Junho de 2025  
**Status**: ✅ **100% CORRIGIDO**  
**Problema**: POST `/api/admin/mfa-verify` retorna 400 (Bad Request)  
**Causa**: Função SQL com nome de coluna incorreto  
**Solução**: Correção da função `get_user_mfa_settings`  

---

## 🚨 **ERRO IDENTIFICADO**

### **Erro no Console**
```
POST http://localhost:9003/api/admin/mfa-verify 400 (Bad Request)
handleVerifyMFA @ AdminMFAPrompt.tsx:31
```

### **Causa Raiz**
A API `/api/admin/mfa-verify` estava retornando erro 400 "MFA não configurado" porque:

1. **Função SQL incorreta**: `get_user_mfa_settings` tentava acessar coluna `secret_key`
2. **Coluna não existe**: A coluna real se chama `secret_encrypted`
3. **Erro SQL**: Função falhava e retornava erro, fazendo API retornar 400

### **Investigação Realizada**

**Erro SQL**:
```sql
ERROR: 42703: column user_mfa_settings.secret_key does not exist
QUERY: SELECT user_mfa_settings.is_enabled, user_mfa_settings.secret_key
FROM user_mfa_settings WHERE user_mfa_settings.user_id = p_user_id
```

**Estrutura Real da Tabela**:
```sql
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'user_mfa_settings';

-- Resultado:
secret_encrypted  ← Nome correto
backup_codes_encrypted
recovery_phrase_encrypted
is_enabled
admin_level
...
```

---

## 🔧 **CORREÇÃO IMPLEMENTADA**

### ✅ **1. Função SQL Corrigida**
**Migração**: `fix_mfa_helper_functions`

**ANTES** (Incorreto):
```sql
CREATE FUNCTION get_user_mfa_settings(p_user_id UUID)
RETURNS TABLE(is_enabled BOOLEAN, secret_key TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    user_mfa_settings.is_enabled,
    user_mfa_settings.secret_key  ← ERRO: Coluna não existe
  FROM user_mfa_settings
  WHERE user_mfa_settings.user_id = p_user_id;
END;
$$
```

**DEPOIS** (Corrigido):
```sql
CREATE FUNCTION get_user_mfa_settings(p_user_id UUID)
RETURNS TABLE(is_enabled BOOLEAN, secret_key TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    user_mfa_settings.is_enabled,
    user_mfa_settings.secret_encrypted as secret_key  ← CORRETO
  FROM user_mfa_settings
  WHERE user_mfa_settings.user_id = p_user_id;
END;
$$
```

### ✅ **2. Teste da Função Corrigida**
```sql
SELECT * FROM get_user_mfa_settings('25944d23-b788-4d16-8508-3d20b72510d1');

-- Resultado: ✅ SUCESSO
{
  "is_enabled": true,
  "secret_key": "316072b46d55b81133e77d32ae070cd3:1b192fe9ed698396e45d2b26897a73dc6e00dc58825786d837b79e212aa7386a"
}
```

---

## 🔄 **FLUXO CORRIGIDO**

### **ANTES** (Com Erro):
```
1. AdminMFAPrompt → POST /api/admin/mfa-verify
2. API chama get_user_mfa_settings(user_id)
3. 🚨 ERRO SQL: column secret_key does not exist
4. Função retorna erro
5. API retorna 400 "MFA não configurado"
6. ❌ Usuário vê erro no console
```

### **DEPOIS** (Corrigido):
```
1. AdminMFAPrompt → POST /api/admin/mfa-verify
2. API chama get_user_mfa_settings(user_id)
3. ✅ Função SQL funciona corretamente
4. Retorna { is_enabled: true, secret_key: "encrypted..." }
5. API descriptografa e verifica código TOTP
6. ✅ Retorna sucesso ou erro de código inválido
```

---

## 🛠️ **DETALHES TÉCNICOS**

### **Problema de Nomenclatura**
Durante a implementação inicial do sistema MFA, as colunas foram criadas com nomes específicos:
- `secret_encrypted` (nome real)
- `backup_codes_encrypted`
- `recovery_phrase_encrypted`

Mas a função SQL foi criada assumindo nome `secret_key`, causando incompatibilidade.

### **Solução Aplicada**
1. **Mapeamento correto**: `secret_encrypted as secret_key`
2. **Manter interface**: API continua recebendo `secret_key`
3. **Compatibilidade**: Não quebra código existente

### **Segurança Mantida**
- ✅ Dados continuam criptografados
- ✅ Função com SECURITY DEFINER
- ✅ Permissões apenas para authenticated
- ✅ Nenhuma exposição de dados sensíveis

---

## 🧪 **TESTES REALIZADOS**

### ✅ **Função SQL**
```sql
-- Teste direto da função
SELECT * FROM get_user_mfa_settings('user-id');
-- ✅ Retorna dados corretos
```

### ✅ **API Endpoint**
```bash
# Teste da API (após correção)
POST /api/admin/mfa-verify
Body: { "token": "123456" }
# ✅ Não mais erro 400 por "MFA não configurado"
# ✅ Agora retorna erro correto se código inválido
```

### ✅ **Fluxo Completo**
- ✅ AdminMFAPrompt carrega sem erro
- ✅ Usuário pode inserir código MFA
- ✅ API processa requisição corretamente
- ✅ Retorna resposta adequada (sucesso/erro)

---

## 📋 **ARQUIVOS AFETADOS**

### **Banco de Dados**
- **Migração**: `fix_mfa_helper_functions`
- **Função**: `get_user_mfa_settings(UUID)` - Corrigida

### **Código (Sem Alterações)**
- `src/app/api/admin/mfa-verify/route.ts` - Funciona com correção SQL
- `src/components/admin/AdminMFAPrompt.tsx` - Funciona normalmente

**Total**: 1 migração SQL (sem mudanças de código)

---

## 🎯 **RESULTADO FINAL**

### **✅ ERRO 400 RESOLVIDO**
- API `/api/admin/mfa-verify` funciona corretamente
- Não mais erro "MFA não configurado" para usuários com MFA
- Função SQL retorna dados corretos

### **✅ FUNCIONALIDADE RESTAURADA**
- AdminMFAPrompt pode verificar códigos MFA
- Usuários com MFA podem acessar admin após verificação
- Sistema MFA totalmente funcional

### **✅ COMPATIBILIDADE MANTIDA**
- Nenhuma mudança de código necessária
- Interface da API permanece igual
- Dados criptografados preservados

---

## 🚀 **STATUS FINAL**

**✅ CORREÇÃO 100% COMPLETA**  
- Erro 400 eliminado
- API MFA-verify funcional
- Sistema MFA totalmente operacional
- Usuários podem verificar códigos MFA

**🎯 PROBLEMA DE API RESOLVIDO!**

---

## 📝 **LIÇÕES APRENDIDAS**

### **Problema de Nomenclatura**
- Sempre verificar nomes reais das colunas no banco
- Usar `information_schema.columns` para confirmar estrutura
- Testar funções SQL antes de usar em APIs

### **Debugging de APIs**
- Erros 400 podem ser causados por problemas SQL subjacentes
- Verificar logs do banco de dados para erros SQL
- Testar funções SQL isoladamente

### **Mapeamento de Dados**
- Usar aliases (`as`) para compatibilidade de interface
- Manter nomes consistentes entre banco e API
- Documentar mapeamentos de colunas

---

*Correção implementada por AI Assistant em 16/06/2025*  
*Erro 400 da API MFA-verify completamente resolvido* ✅ 