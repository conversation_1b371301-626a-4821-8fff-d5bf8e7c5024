/**
 * Admin Analytics Service
 * Provides comprehensive analytics data for the admin dashboard
 * Created: 18/01/2025 - Sprint 3 Milestone 3.2
 */

import { createClient } from '@/lib/supabase/client';
import { logAdminAction, AdminAction } from '@/lib/audit/adminActions';

// Analytics interfaces
export interface SiteAnalytics {
  totalUsers: number;
  activeUsers: number;
  totalReviews: number;
  publishedReviews: number;
  draftReviews: number;
  totalComments: number;
  totalLikes: number;
  totalViews: number;
  userGrowth: GrowthData[];
  contentGrowth: GrowthData[];
  topReviews: TopReview[];
  topUsers: TopUser[];
  engagementMetrics: EngagementMetrics;
}

export interface GrowthData {
  date: string;
  count: number;
  cumulative: number;
}

export interface TopReview {
  id: string;
  title: string;
  author_name: string;
  view_count: number;
  like_count: number;
  comment_count: number;
  overall_score: number;
  created_at: string;
  slug: string;
}

export interface TopUser {
  id: string;
  username: string;
  display_name: string;
  review_count: number;
  total_views: number;
  total_likes: number;
  level: number;
  created_at: string;
  slug: string;
}

export interface EngagementMetrics {
  averageReviewsPerUser: number;
  averageViewsPerReview: number;
  averageLikesPerReview: number;
  averageCommentsPerReview: number;
  userRetentionRate: number;
  contentCreationRate: number;
}

export interface PerformanceMetrics {
  averagePageLoadTime: number;
  databaseResponseTime: number;
  userEngagementRate: number;
  contentCreationRate: number;
  systemUptime: number;
  errorRates: Record<string, number>;
}

// Verify admin permissions before analytics operations
async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .rpc('is_admin', { user_id: userId });
    
    if (error) {
      console.error('Error verifying admin permissions:', error);
      return false;
    }
    
    return data === true;
  } catch (error) {
    console.error('Error in verifyAdminPermissions:', error);
    return false;
  }
}

/**
 * Get comprehensive site analytics
 */
export async function getSiteAnalytics(
  adminId: string,
  startDate: Date,
  endDate: Date
): Promise<SiteAnalytics | null> {
  try {
    // Verify admin permissions
    const isAuthorized = await verifyAdminPermissions(adminId);
    if (!isAuthorized) {
      throw new Error('Unauthorized admin access');
    }

    const supabase = createClient();
    
    // Get basic counts
    const [
      { count: totalUsers },
      { count: totalReviews },
      { count: totalComments },
      { count: totalLikes },
    ] = await Promise.all([
      supabase.from('profiles').select('*', { count: 'exact', head: true }),
      supabase.from('reviews').select('*', { count: 'exact', head: true }),
      supabase.from('comments').select('*', { count: 'exact', head: true }),
      supabase.from('review_likes').select('*', { count: 'exact', head: true }),
    ]);

    // Get published vs draft reviews
    const { data: reviewStats } = await supabase
      .from('reviews')
      .select('status, view_count')
      .not('status', 'is', null);

    const publishedReviews = reviewStats?.filter(r => r.status === 'published').length || 0;
    const draftReviews = reviewStats?.filter(r => r.status === 'draft').length || 0;
    const totalViews = reviewStats?.reduce((sum, r) => sum + (r.view_count || 0), 0) || 0;

    // Get active users (users who have been active in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { count: activeUsers } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('last_seen', thirtyDaysAgo.toISOString());

    // Get user growth data
    const userGrowth = await getUserGrowthData(supabase, startDate, endDate);
    
    // Get content growth data
    const contentGrowth = await getContentGrowthData(supabase, startDate, endDate);
    
    // Get top reviews
    const topReviews = await getTopReviews(supabase);
    
    // Get top users
    const topUsers = await getTopUsers(supabase);
    
    // Calculate engagement metrics
    const engagementMetrics = await getEngagementMetrics(supabase);

    // Log admin action
    await logAdminAction(
      adminId,
      AdminAction.VIEW_ANALYTICS,
      'analytics',
      'site_analytics',
      { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
    );

    return {
      totalUsers: totalUsers || 0,
      activeUsers: activeUsers || 0,
      totalReviews: totalReviews || 0,
      publishedReviews,
      draftReviews,
      totalComments: totalComments || 0,
      totalLikes: totalLikes || 0,
      totalViews,
      userGrowth,
      contentGrowth,
      topReviews,
      topUsers,
      engagementMetrics,
    };

  } catch (error) {
    console.error('Error getting site analytics:', error);
    
    // Log failed admin action
    await logAdminAction(
      adminId,
      AdminAction.VIEW_ANALYTICS,
      'analytics',
      'site_analytics',
      { error: error instanceof Error ? error.message : 'Unknown error' },
      false,
      error instanceof Error ? error.message : 'Unknown error'
    );
    
    return null;
  }
}

/**
 * Get user growth data over time
 */
async function getUserGrowthData(supabase: any, startDate: Date, endDate: Date): Promise<GrowthData[]> {
  try {
    const { data: users } = await supabase
      .from('profiles')
      .select('created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at');

    if (!users) return [];

    // Group by date and calculate cumulative growth
    const growthMap = new Map<string, number>();
    users.forEach(user => {
      const date = new Date(user.created_at).toISOString().split('T')[0];
      growthMap.set(date, (growthMap.get(date) || 0) + 1);
    });

    const growthData: GrowthData[] = [];
    let cumulative = 0;
    
    for (const [date, count] of growthMap.entries()) {
      cumulative += count;
      growthData.push({ date, count, cumulative });
    }

    return growthData.sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting user growth data:', error);
    return [];
  }
}

/**
 * Get content growth data over time
 */
async function getContentGrowthData(supabase: any, startDate: Date, endDate: Date): Promise<GrowthData[]> {
  try {
    const { data: reviews } = await supabase
      .from('reviews')
      .select('created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at');

    if (!reviews) return [];

    // Group by date and calculate cumulative growth
    const growthMap = new Map<string, number>();
    reviews.forEach(review => {
      const date = new Date(review.created_at).toISOString().split('T')[0];
      growthMap.set(date, (growthMap.get(date) || 0) + 1);
    });

    const growthData: GrowthData[] = [];
    let cumulative = 0;
    
    for (const [date, count] of growthMap.entries()) {
      cumulative += count;
      growthData.push({ date, count, cumulative });
    }

    return growthData.sort((a, b) => a.date.localeCompare(b.date));
  } catch (error) {
    console.error('Error getting content growth data:', error);
    return [];
  }
}

/**
 * Get top performing reviews
 */
async function getTopReviews(supabase: any): Promise<TopReview[]> {
  try {
    const { data: reviews } = await supabase
      .from('reviews')
      .select(`
        id, title, author_name, slug, overall_score, created_at,
        view_count, like_count, comment_count
      `)
      .eq('status', 'published')
      .order('view_count', { ascending: false })
      .limit(10);

    return reviews || [];
  } catch (error) {
    console.error('Error getting top reviews:', error);
    return [];
  }
}

/**
 * Get top performing users
 */
async function getTopUsers(supabase: any): Promise<TopUser[]> {
  try {
    const { data: users } = await supabase
      .from('profiles')
      .select(`
        id, username, display_name, slug, review_count, level, created_at
      `)
      .order('review_count', { ascending: false })
      .limit(10);

    if (!users) return [];

    // Calculate total views and likes for each user
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const { data: userReviews } = await supabase
          .from('reviews')
          .select('view_count, like_count')
          .eq('author_id', user.id);

        const total_views = userReviews?.reduce((sum, r) => sum + (r.view_count || 0), 0) || 0;
        const total_likes = userReviews?.reduce((sum, r) => sum + (r.like_count || 0), 0) || 0;

        return {
          ...user,
          total_views,
          total_likes,
        };
      })
    );

    return usersWithStats;
  } catch (error) {
    console.error('Error getting top users:', error);
    return [];
  }
}

/**
 * Calculate engagement metrics
 */
async function getEngagementMetrics(supabase: any): Promise<EngagementMetrics> {
  try {
    const [
      { count: totalUsers },
      { count: totalReviews },
      { data: reviewStats }
    ] = await Promise.all([
      supabase.from('profiles').select('*', { count: 'exact', head: true }),
      supabase.from('reviews').select('*', { count: 'exact', head: true }),
      supabase.from('reviews').select('view_count, like_count, comment_count')
    ]);

    const averageReviewsPerUser = totalUsers > 0 ? (totalReviews || 0) / totalUsers : 0;
    
    const totalViews = reviewStats?.reduce((sum, r) => sum + (r.view_count || 0), 0) || 0;
    const totalLikes = reviewStats?.reduce((sum, r) => sum + (r.like_count || 0), 0) || 0;
    const totalComments = reviewStats?.reduce((sum, r) => sum + (r.comment_count || 0), 0) || 0;
    
    const averageViewsPerReview = totalReviews > 0 ? totalViews / totalReviews : 0;
    const averageLikesPerReview = totalReviews > 0 ? totalLikes / totalReviews : 0;
    const averageCommentsPerReview = totalReviews > 0 ? totalComments / totalReviews : 0;

    // Calculate user retention rate (simplified - users active in last 30 days vs total)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { count: activeUsers } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('last_seen', thirtyDaysAgo.toISOString());

    const userRetentionRate = totalUsers > 0 ? (activeUsers || 0) / totalUsers * 100 : 0;

    // Calculate content creation rate (reviews per day in last 30 days)
    const { count: recentReviews } = await supabase
      .from('reviews')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', thirtyDaysAgo.toISOString());

    const contentCreationRate = (recentReviews || 0) / 30; // reviews per day

    return {
      averageReviewsPerUser,
      averageViewsPerReview,
      averageLikesPerReview,
      averageCommentsPerReview,
      userRetentionRate,
      contentCreationRate,
    };
  } catch (error) {
    console.error('Error calculating engagement metrics:', error);
    return {
      averageReviewsPerUser: 0,
      averageViewsPerReview: 0,
      averageLikesPerReview: 0,
      averageCommentsPerReview: 0,
      userRetentionRate: 0,
      contentCreationRate: 0,
    };
  }
}
