'use client';

// Comment Analytics Component
// Date: 22/06/2025
// Task: Comment Analytics Implementation - Redesigned to match dashboard style

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  MessageSquare, 
  Users, 
  Calendar,
  Activity,
  Zap,
  Award,
  Loader2
} from 'lucide-react';

interface CommentAnalyticsProps {
  userId: string;
}

export function CommentAnalytics({ userId }: CommentAnalyticsProps) {
  // Mock data for now - in real implementation, this would fetch from API
  const analyticsData = {
    totalComments: 147,
    totalReplies: 89,
    averageResponseTime: '2.4 hours',
    mostActiveReview: 'Cyberpunk 2077 Review',
    topCommenter: 'GamerPro42',
    engagement: {
      thisWeek: 23,
      lastWeek: 18,
      growth: '+27.8%'
    },
    moderation: {
      approved: 89,
      pending: 12,
      flagged: 3,
      approvalRate: 94.7
    }
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-600/20 rounded border border-blue-600/30">
                  <MessageSquare className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-slate-200 font-mono">
                    {analyticsData.totalComments}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    Total Comments
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-600/20 rounded border border-green-600/30">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-slate-200 font-mono">
                    {analyticsData.engagement.thisWeek}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    This Week
                  </div>
                  <Badge variant="outline" className="mt-1 font-mono text-xs border-green-600/30 text-green-400">
                    {analyticsData.engagement.growth}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-600/20 rounded border border-purple-600/30">
                  <Users className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-slate-200 font-mono">
                    {analyticsData.totalReplies}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    Your Replies
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-600/20 rounded border border-yellow-600/30">
                  <Zap className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <div className="text-lg font-bold text-slate-200 font-mono">
                    {analyticsData.averageResponseTime}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    Avg Response Time
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Moderation Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-mono text-slate-200">
                <span className="text-purple-400 mr-2">//</span>
                Moderation Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-slate-800/30 rounded-lg border border-slate-700/30">
                  <div className="text-2xl font-bold text-green-400 font-mono">
                    {analyticsData.moderation.approved}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    Approved
                  </div>
                </div>
                <div className="text-center p-3 bg-slate-800/30 rounded-lg border border-slate-700/30">
                  <div className="text-2xl font-bold text-yellow-400 font-mono">
                    {analyticsData.moderation.pending}
                  </div>
                  <div className="text-xs text-slate-400 font-mono uppercase tracking-wide">
                    Pending
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-slate-800/30 rounded-lg border border-slate-700/30">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-purple-400" />
                  <span className="font-mono text-sm text-slate-300">Approval Rate</span>
                </div>
                <div className="text-lg font-bold text-purple-400 font-mono">
                  {analyticsData.moderation.approvalRate}%
                </div>
              </div>

              {analyticsData.moderation.flagged > 0 && (
                <div className="flex items-center justify-between p-3 bg-red-600/10 rounded-lg border border-red-600/30">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-red-400" />
                    <span className="font-mono text-sm text-slate-300">Flagged Content</span>
                  </div>
                  <Badge variant="destructive" className="font-mono text-xs">
                    {analyticsData.moderation.flagged}
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Top Performers */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-mono text-slate-200">
                <span className="text-purple-400 mr-2">//</span>
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-700/30">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-1.5 bg-blue-600/20 rounded border border-blue-600/30">
                    <BarChart3 className="h-4 w-4 text-blue-400" />
                  </div>
                  <div>
                    <div className="font-mono text-sm text-slate-200 font-semibold">
                      Most Active Review
                    </div>
                    <div className="text-xs text-slate-400 font-mono">
                      Highest comment engagement
                    </div>
                  </div>
                </div>
                <div className="font-mono text-sm text-purple-400">
                  {analyticsData.mostActiveReview}
                </div>
              </div>

              <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-700/30">
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-1.5 bg-green-600/20 rounded border border-green-600/30">
                    <Users className="h-4 w-4 text-green-400" />
                  </div>
                  <div>
                    <div className="font-mono text-sm text-slate-200 font-semibold">
                      Top Commenter
                    </div>
                    <div className="text-xs text-slate-400 font-mono">
                      Most engaged community member
                    </div>
                  </div>
                </div>
                <div className="font-mono text-sm text-green-400">
                  @{analyticsData.topCommenter}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Engagement Trends */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-mono text-slate-200">
              <span className="text-purple-400 mr-2">//</span>
              Engagement Trends
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              Comment activity over the past 7 days
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-32 bg-slate-800/30 rounded-lg border border-slate-700/30 flex items-center justify-center">
              <div className="text-center">
                <BarChart3 className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                <div className="font-mono text-sm text-slate-400">
                  Chart visualization would go here
                </div>
                <div className="font-mono text-xs text-slate-500 mt-1">
                  Integration with charts library needed
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardContent className="p-4">
            <div className="text-center text-slate-400">
              <Calendar className="h-8 w-8 mx-auto mb-2" />
              <div className="font-mono text-sm">
                Advanced analytics features coming soon
              </div>
              <div className="font-mono text-xs text-slate-500 mt-1">
                Real-time metrics, detailed reports, and exportable data
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
