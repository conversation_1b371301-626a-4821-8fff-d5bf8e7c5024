import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { MFAService } from '@/lib/security/mfa';

// GET - Obter status do MFA
export async function GET() {
  try {
    // Para verificação de status MFA, usar verificação básica sem exigir MFA
    const supabase = await createServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se é admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin, admin_level')
      .eq('id', user.id as any)
      .single();

    if (profileError || !profile || !(profile as any).is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const status = await MFAService.getMFAStatus(user.id);

    return NextResponse.json({ status });
  } catch (error: any) {
    console.error('Error getting MFA status:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Configurar ou verificar MFA
export async function POST(request: NextRequest) {
  try {
    // Para operações MFA, usar verificação básica sem exigir MFA
    const supabase = await createServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se é admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin, admin_level')
      .eq('id', user.id as any)
      .single();

    if (profileError || !profile || !(profile as any).is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { action, token, userId } = body;
    const targetUserId = userId || user.id;

    // Verificar se admin tem permissão para gerenciar MFA de outros usuários
    if (targetUserId !== user.id) {
      if ((profile as any).admin_level !== 'SUPER_ADMIN') {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
      }
    }

    switch (action) {
      case 'setup':
        const setupResult = await MFAService.setupMFA(targetUserId);
        return NextResponse.json({
          success: true,
          setupResult: setupResult
        });

      case 'verify':
        if (!token) {
          return NextResponse.json({ error: 'Token required' }, { status: 400 });
        }
        
        const verifyResult = await MFAService.verifyMFAToken(
          targetUserId,
          token,
          'MFA_SETUP'
        );

        return NextResponse.json({
          success: true,
          result: verifyResult
        });

      case 'regenerate-backup-codes':
        const newCodes = await MFAService.regenerateBackupCodes(targetUserId);
        return NextResponse.json({ 
          success: true, 
          backupCodes: newCodes 
        });

      case 'disable':
        const { reason } = body;
        if (!reason) {
          return NextResponse.json({ error: 'Reason required' }, { status: 400 });
        }
        
        await MFAService.disableMFA(targetUserId, reason, user.id);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error: any) {
    console.error('Error in MFA API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar configurações MFA
export async function PUT(request: NextRequest) {
  try {
    // Para operações MFA, usar verificação básica sem exigir MFA
    const supabase = await createServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar se é admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin, admin_level')
      .eq('id', user.id as any)
      .single();

    if (profileError || !profile || !(profile as any).is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { userId } = body;
    const targetUserId = userId || user.id;

    // Apenas SUPER_ADMIN pode atualizar configurações de outros usuários
    if (targetUserId !== user.id && (profile as any).admin_level !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Implementar atualizações de configuração conforme necessário
    // Por enquanto, apenas retornar sucesso
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error updating MFA settings:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
} 