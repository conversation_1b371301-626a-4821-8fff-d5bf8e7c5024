// Comment Notification System
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import React from 'react';
import { createClient } from '@/lib/supabase/client';

export interface CommentNotification {
  id: string;
  type: 'new_comment' | 'comment_flagged' | 'comment_approved' | 'comment_rejected';
  title: string;
  message: string;
  comment_id: string;
  review_id: string;
  created_at: string;
  read: boolean;
  data?: any;
}

export class CommentNotificationService {
  private supabase = createClient();

  // Subscribe to real-time comment notifications
  subscribeToCommentNotifications(
    userId: string,
    onNotification: (notification: CommentNotification) => void
  ) {
    // Subscribe to new comments on user's reviews
    const commentsChannel = this.supabase
      .channel('comment-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'comments',
          filter: `review_id=in.(${this.getUserReviewIds(userId)})`,
        },
        async (payload) => {
          const comment = payload.new;
          
          // Get review details
          const { data: review } = await this.supabase
            .from('reviews')
            .select('title, author_id')
            .eq('id', comment.review_id)
            .single();

          if (review && review.author_id === userId) {
            const notification: CommentNotification = {
              id: `comment-${comment.id}`,
              type: 'new_comment',
              title: 'New Comment',
              message: `New comment on "${review.title}"`,
              comment_id: comment.id,
              review_id: comment.review_id,
              created_at: comment.created_at,
              read: false,
              data: { comment, review },
            };
            
            onNotification(notification);
          }
        }
      )
      .subscribe();

    // Subscribe to comment flags
    const flagsChannel = this.supabase
      .channel('flag-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'content_flags',
          filter: `content_type=eq.comment`,
        },
        async (payload) => {
          const flag = payload.new;
          
          // Get comment and review details
          const { data: comment } = await this.supabase
            .from('comments')
            .select(`
              *,
              review:reviews!review_id(id, title, author_id)
            `)
            .eq('id', flag.content_id)
            .single();

          if (comment?.review?.author_id === userId) {
            const notification: CommentNotification = {
              id: `flag-${flag.id}`,
              type: 'comment_flagged',
              title: 'Comment Flagged',
              message: `Comment flagged on "${comment.review.title}": ${flag.reason}`,
              comment_id: comment.id,
              review_id: comment.review_id,
              created_at: flag.created_at,
              read: false,
              data: { comment, flag },
            };
            
            onNotification(notification);
          }
        }
      )
      .subscribe();

    return () => {
      commentsChannel.unsubscribe();
      flagsChannel.unsubscribe();
    };
  }

  // Get user's review IDs for filtering
  private async getUserReviewIds(userId: string): Promise<string> {
    const { data: reviews } = await this.supabase
      .from('reviews')
      .select('id')
      .eq('author_id', userId);

    return reviews?.map(r => r.id).join(',') || '';
  }

  // Send notification for moderation actions
  async sendModerationNotification(
    commentId: string,
    action: 'approved' | 'rejected' | 'deleted',
    moderatorId: string
  ) {
    try {
      // Get comment details
      const { data: comment } = await this.supabase
        .from('comments')
        .select(`
          *,
          author:profiles!author_id(id, username, display_name),
          review:reviews!review_id(id, title, author_id)
        `)
        .eq('id', commentId)
        .single();

      if (!comment) return;

      // Create notification for comment author (if different from moderator)
      if (comment.author_id !== moderatorId) {
        const notification = {
          user_id: comment.author_id,
          type: `comment_${action}`,
          title: `Comment ${action.charAt(0).toUpperCase() + action.slice(1)}`,
          message: `Your comment on "${comment.review.title}" has been ${action}`,
          data: {
            comment_id: commentId,
            review_id: comment.review_id,
            action,
          },
        };

        // In a real implementation, you'd store this in a notifications table
        console.log('Notification sent:', notification);
      }
    } catch (error) {
      console.error('Error sending moderation notification:', error);
    }
  }

  // Get notification preferences for a user
  async getNotificationPreferences(userId: string) {
    const { data } = await this.supabase
      .from('user_notification_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    return data || {
      new_comments: true,
      comment_flags: true,
      moderation_updates: true,
      email_notifications: false,
      push_notifications: true,
    };
  }

  // Update notification preferences
  async updateNotificationPreferences(
    userId: string,
    preferences: {
      new_comments?: boolean;
      comment_flags?: boolean;
      moderation_updates?: boolean;
      email_notifications?: boolean;
      push_notifications?: boolean;
    }
  ) {
    const { error } = await this.supabase
      .from('user_notification_settings')
      .upsert({
        user_id: userId,
        ...preferences,
        updated_at: new Date().toISOString(),
      });

    if (error) throw error;
  }
}

// React hook for comment notifications
export function useCommentNotifications(userId: string) {
  const [notifications, setNotifications] = React.useState<CommentNotification[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);

  React.useEffect(() => {
    if (!userId) return;

    const notificationService = new CommentNotificationService();
    
    const unsubscribe = notificationService.subscribeToCommentNotifications(
      userId,
      (notification) => {
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show browser notification if permissions granted
        if (Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico',
          });
        }
      }
    );

    return unsubscribe;
  }, [userId]);

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
  };

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
  };
}

// Request notification permissions
export async function requestNotificationPermission(): Promise<boolean> {
  if (!('Notification' in window)) {
    console.warn('This browser does not support notifications');
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
}
