// src/components/ui/UploadProgress.tsx
import React from 'react';
import { CheckCircle, AlertCircle, Loader2, X } from 'lucide-react';
import { UploadProgress as UploadProgressType } from '@/hooks/useB2ImageUpload';

interface UploadProgressProps {
  uploads: UploadProgressType[];
  onCancel?: () => void;
  onRemove?: (id: string) => void;
  showDetails?: boolean;
  className?: string;
}

export default function UploadProgress({
  uploads,
  onCancel,
  onRemove,
  showDetails = true,
  className = '',
}: UploadProgressProps) {
  const stats = uploads.reduce(
    (acc, upload) => {
      acc.total++;
      if (upload.status === 'completed') acc.completed++;
      if (upload.status === 'error') acc.failed++;
      if (upload.status === 'uploading' || upload.status === 'pending') acc.pending++;
      return acc;
    },
    { total: 0, completed: 0, failed: 0, pending: 0 }
  );

  const isActive = stats.pending > 0;

  if (uploads.length === 0) return null;

  return (
    <div className={`bg-slate-800 rounded-lg border border-slate-700 p-3 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {isActive && <Loader2 className="w-4 h-4 animate-spin text-blue-400" />}
          <span className="text-slate-300 text-sm font-medium">
            Upload Progress
          </span>
        </div>
        {isActive && onCancel && (
          <button
            onClick={onCancel}
            className="text-slate-400 hover:text-red-400 text-xs transition-colors"
          >
            Cancel All
          </button>
        )}
      </div>

      {/* Stats */}
      <div className="text-xs text-slate-400 mb-2">
        Total: {stats.total} | Completed: {stats.completed} | Failed: {stats.failed} | Pending: {stats.pending}
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-slate-700 rounded-full h-2 mb-3">
        <div
          className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
          style={{
            width: `${stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}%`,
          }}
        />
      </div>

      {/* Detailed Progress */}
      {showDetails && (
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {uploads.map((upload) => (
            <div
              key={upload.id}
              className="flex items-center gap-2 text-xs p-2 rounded bg-slate-700/50"
            >
              {/* Status Icon */}
              <div className="flex-shrink-0">
                {upload.status === 'uploading' && (
                  <Loader2 className="w-3 h-3 animate-spin text-blue-400" />
                )}
                {upload.status === 'completed' && (
                  <CheckCircle className="w-3 h-3 text-green-400" />
                )}
                {upload.status === 'error' && (
                  <AlertCircle className="w-3 h-3 text-red-400" />
                )}
                {upload.status === 'pending' && (
                  <div className="w-3 h-3 rounded-full border border-slate-500" />
                )}
              </div>

              {/* File Name */}
              <div className="flex-1 truncate text-slate-300">
                {upload.file.name}
              </div>

              {/* Progress/Status */}
              <div className="flex-shrink-0">
                {upload.status === 'uploading' && (
                  <span className="text-blue-400">{upload.progress}%</span>
                )}
                {upload.status === 'completed' && (
                  <span className="text-green-400">Done</span>
                )}
                {upload.status === 'error' && (
                  <span className="text-red-400" title={upload.error}>
                    Error
                  </span>
                )}
                {upload.status === 'pending' && (
                  <span className="text-slate-400">Waiting</span>
                )}
              </div>

              {/* Remove Button */}
              {onRemove && upload.status !== 'uploading' && (
                <button
                  onClick={() => onRemove(upload.id)}
                  className="flex-shrink-0 text-slate-400 hover:text-red-400 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
