// src/lib/utils/searchOptimization.ts
/**
 * Search optimization utilities for CriticalPixel
 * Implements caching, rate limiting, and circuit breaker patterns
 * to prevent expensive Firebase API calls
 */

export interface SearchCacheEntry<T = any> {
  results: T[];
  timestamp: number;
  query: string;
}

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  minQueryLength: number;
  debounceMs: number;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeoutMs: number;
  monitorWindowMs: number;
}

/**
 * Search cache with automatic cleanup
 */
export class SearchCache<T = any> {
  private cache = new Map<string, SearchCacheEntry<T>>();
  private maxSize: number;
  private ttlMs: number;

  constructor(maxSize = 50, ttlMs = 5 * 60 * 1000) { // 5 minutes default TTL
    this.maxSize = maxSize;
    this.ttlMs = ttlMs;
  }

  get(key: string): SearchCacheEntry<T> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry is expired
    if (Date.now() - entry.timestamp > this.ttlMs) {
      this.cache.delete(key);
      return null;
    }

    return entry;
  }

  set(key: string, results: T[], query: string): void {
    // Clean up old entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.keys())[0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      results,
      timestamp: Date.now(),
      query
    });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttlMs) {
        this.cache.delete(key);
      }
    }
  }
}

/**
 * Rate limiter for search requests
 */
export class SearchRateLimiter {
  private requests: number[] = [];
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  canMakeRequest(): boolean {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(
      timestamp => now - timestamp < this.config.windowMs
    );

    // Check if we're under the limit
    if (this.requests.length >= this.config.maxRequests) {
      return false;
    }

    // Record this request
    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest(): number {
    if (this.requests.length < this.config.maxRequests) {
      return 0;
    }

    const oldestRequest = Math.min(...this.requests);
    const timeUntilReset = this.config.windowMs - (Date.now() - oldestRequest);
    return Math.max(0, timeUntilReset);
  }

  reset(): void {
    this.requests = [];
  }
}

/**
 * Circuit breaker for search operations
 */
export class SearchCircuitBreaker {
  private failures: number[] = [];
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private lastFailureTime = 0;
  private config: CircuitBreakerConfig;

  constructor(config: CircuitBreakerConfig) {
    this.config = config;
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeoutMs) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = [];
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    const now = Date.now();
    this.failures.push(now);
    this.lastFailureTime = now;

    // Remove old failures outside the monitor window
    this.failures = this.failures.filter(
      timestamp => now - timestamp < this.config.monitorWindowMs
    );

    // Check if we should open the circuit
    if (this.failures.length >= this.config.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = [];
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

/**
 * Comprehensive search optimizer
 */
export class SearchOptimizer<T = any> {
  private cache: SearchCache<T>;
  private rateLimiter: SearchRateLimiter;
  private circuitBreaker: SearchCircuitBreaker;

  constructor(
    cacheConfig = { maxSize: 50, ttlMs: 5 * 60 * 1000 },
    rateLimitConfig: RateLimitConfig = {
      maxRequests: 10,
      windowMs: 60 * 1000, // 1 minute
      minQueryLength: 3,
      debounceMs: 800
    },
    circuitBreakerConfig: CircuitBreakerConfig = {
      failureThreshold: 3,
      resetTimeoutMs: 30 * 1000, // 30 seconds
      monitorWindowMs: 5 * 60 * 1000 // 5 minutes
    }
  ) {
    this.cache = new SearchCache<T>(cacheConfig.maxSize, cacheConfig.ttlMs);
    this.rateLimiter = new SearchRateLimiter(rateLimitConfig);
    this.circuitBreaker = new SearchCircuitBreaker(circuitBreakerConfig);
  }

  async search(
    query: string,
    searchFunction: (query: string) => Promise<T[]>
  ): Promise<{ results: T[]; fromCache: boolean; error?: string; circuitBreakerOpen?: boolean }> {
    // Validate query length
    if (query.length < 3) {
      return { results: [], fromCache: false, error: 'Query too short' };
    }

    const cacheKey = `search_${query.toLowerCase()}`;

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached) {
      return { results: cached.results, fromCache: true };
    }

    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      return {
        results: [],
        fromCache: false,
        error: `Rate limited. Try again in ${Math.ceil(waitTime / 1000)} seconds.`
      };
    }

    try {
      // Execute search with circuit breaker
      const results = await this.circuitBreaker.execute(() => searchFunction(query));

      // Cache the results
      this.cache.set(cacheKey, results, query);

      return { results, fromCache: false };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Search failed';

      // Check if circuit breaker is open
      if (errorMessage.includes('Circuit breaker is OPEN')) {
        return {
          results: [],
          fromCache: false,
          error: 'Search temporarily unavailable. Please try again in a few seconds.',
          circuitBreakerOpen: true
        };
      }

      return {
        results: [],
        fromCache: false,
        error: errorMessage
      };
    }
  }

  // Manual reset for circuit breaker issues
  forceReset(): void {
    this.resetRateLimit();
    this.resetCircuitBreaker();
    this.clearCache();
  }

  // Utility methods
  clearCache(): void {
    this.cache.clear();
  }

  resetRateLimit(): void {
    this.rateLimiter.reset();
  }

  resetCircuitBreaker(): void {
    this.circuitBreaker.reset();
  }

  getStats() {
    return {
      cacheSize: this.cache.size(),
      circuitBreakerState: this.circuitBreaker.getState(),
      timeUntilNextRequest: this.rateLimiter.getTimeUntilNextRequest()
    };
  }
}

// Default instance for user search
export const userSearchOptimizer = new SearchOptimizer({
  maxSize: 100,
  ttlMs: 5 * 60 * 1000 // 5 minutes
}, {
  maxRequests: 10, // More reasonable for user search
  windowMs: 60 * 1000, // 1 minute
  minQueryLength: 3,
  debounceMs: 800
}, {
  failureThreshold: 5, // More forgiving - open circuit after 5 failures
  resetTimeoutMs: 15 * 1000, // Faster recovery - 15 seconds
  monitorWindowMs: 2 * 60 * 1000 // Shorter monitoring window - 2 minutes
});
