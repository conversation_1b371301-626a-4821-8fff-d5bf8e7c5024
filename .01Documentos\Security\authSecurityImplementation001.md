# Critical Pixel Authentication Security Implementation Guide
**Date:** June 10, 2025  
**Implementation Guide Version:** 1.0  
**Target:** Critical and High Priority Security Fixes  
**AI Execution Ready:** ✅ Checkbox-based task tracking  

---

## 🎯 IMPLEMENTATION OVERVIEW

This guide provides step-by-step instructions for implementing critical security fixes identified in the security assessment. Each task includes detailed instructions, code examples, verification steps, and specific commenting requirements for AI completion tracking.

### Priority Implementation Schedule:
- **Phase 1:** Critical Fixes (24-48 hours) - 4 tasks
- **Phase 2:** High Priority (2-3 days) - 5 tasks  
- **Phase 3:** Security Hardening (1 week) - 4 tasks

---

## 📋 TASK COMPLETION TRACKING

### Progress Overview:
- [ ] **Phase 1 Complete:** 0/4 Critical fixes
- [ ] **Phase 2 Complete:** 0/5 High priority fixes
- [ ] **Phase 3 Complete:** 0/4 Security hardening tasks

---

# 🚨 PHASE 1: CRITICAL SECURITY FIXES (24-48 hours)

## Task 1: Fix AuthProvider Integration (CVE-CP-001)
**Priority:** P0 - CRITICAL  
**Estimated Time:** 30 minutes  
**Risk:** Authentication bypass vulnerability

### Implementation Steps:

#### ☐ 1.1: Analyze Current Provider Structure
**Instructions:**
- Read the current `src/app/providers.tsx` file
- Identify the React Query provider wrapper
- Locate the missing AuthProvider integration

**Verification Command:**
```bash
# Check current provider structure
grep -n "AuthProvider\|QueryClientProvider" src/app/providers.tsx
```

**Expected Finding:** Only QueryClientProvider present, AuthProvider missing

#### ☐ 1.2: Import Required Dependencies
**Instructions:**
- Add AuthProvider import from auth context
- Ensure proper import path resolution

**Code to Add:**
```tsx
import { AuthProvider } from '@/contexts/auth-context'
```

**Comment to Add:** `// Added AuthProvider import for authentication state management`

#### ☐ 1.3: Wrap AuthProvider Around Application
**Instructions:**
- Modify the Providers component to wrap AuthProvider around React Query
- Ensure proper nesting order: AuthProvider > QueryClientProvider > children

**Implementation:**
```tsx
export function Providers({ children }: ProvidersProps) {
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </AuthProvider>
  );
}
```

**Comment to Add:** `// Fixed critical security vulnerability: AuthProvider now wraps entire application for proper authentication state management`

#### ☐ 1.4: Test Authentication State Access
**Instructions:**
- Create a test component that uses useAuthContext
- Verify authentication state is available throughout the application
- Test user login/logout state persistence

**Verification Steps:**
1. Start development server: `npm run dev`
2. Navigate to login page
3. Attempt authentication
4. Verify user state is available in all components
5. Test logout functionality

**Expected Result:** Authentication state properly shared across all components

#### ☐ 1.5: Verify Fix Implementation
**Instructions:**
- Run type checking: `npm run typecheck`
- Ensure no TypeScript errors
- Test authentication flow end-to-end

**Final Comment to Add:** `// SECURITY FIX COMPLETED: AuthProvider integration restored - authentication state now properly managed application-wide`

---

## Task 2: Implement Next.js Middleware (CVE-CP-003)
**Priority:** P0 - CRITICAL  
**Estimated Time:** 45 minutes  
**Risk:** Session hijacking and unauthorized access

### Implementation Steps:

#### ☐ 2.1: Create Middleware File
**Instructions:**
- Create new file `middleware.ts` in project root (same level as package.json)
- Set up basic file structure with proper imports

**File Path:** `/mnt/f/Sites/CriticalPixel/middleware.ts`

**Initial Code:**
```typescript
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Middleware implementation will be added in next steps
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - Public assets
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

**Comment to Add:** `// Created Next.js middleware for authentication session management and route protection`

#### ☐ 2.2: Implement Supabase Server Client
**Instructions:**
- Add Supabase server client creation with proper cookie handling
- Implement session refresh logic

**Code to Add:**
```typescript
export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // CRITICAL: Do not run code between createServerClient and supabase.auth.getUser()
  const { data: { user } } = await supabase.auth.getUser()

  return supabaseResponse
}
```

**Comment to Add:** `// Implemented Supabase server client with proper cookie handling for session management`

#### ☐ 2.3: Add Route Protection Logic
**Instructions:**
- Implement authentication checks for protected routes
- Add redirect logic for unauthenticated users

**Code to Add (within middleware function, after getUser call):**
```typescript
// Protect authenticated routes
if (
  !user &&
  !request.nextUrl.pathname.startsWith('/login') &&
  !request.nextUrl.pathname.startsWith('/auth') &&
  !request.nextUrl.pathname.startsWith('/register') &&
  !request.nextUrl.pathname.startsWith('/forgot-password') &&
  request.nextUrl.pathname !== '/'
) {
  // Redirect unauthenticated users to login
  const url = request.nextUrl.clone()
  url.pathname = '/login'
  url.searchParams.set('redirectTo', request.nextUrl.pathname)
  return NextResponse.redirect(url)
}

// Protect admin routes
if (
  request.nextUrl.pathname.startsWith('/admin') &&
  (!user || !user.user_metadata?.role || user.user_metadata.role !== 'admin')
) {
  const url = request.nextUrl.clone()
  url.pathname = '/unauthorized'
  return NextResponse.redirect(url)
}
```

**Comment to Add:** `// Added route protection logic to prevent unauthorized access to protected and admin routes`

#### ☐ 2.4: Test Middleware Functionality
**Instructions:**
- Test route protection for unauthenticated users
- Verify session refresh works properly
- Test admin route protection

**Testing Steps:**
1. Clear browser cookies
2. Navigate to protected route (e.g., `/dashboard`)
3. Verify redirect to login page
4. Login and verify redirect back to intended page
5. Test admin route access
6. Verify session persistence across page refreshes

**Expected Results:**
- Unauthenticated users redirected to login
- Authenticated users can access protected routes
- Admin routes properly protected
- Sessions automatically refreshed

#### ☐ 2.5: Verify Security Implementation
**Instructions:**
- Run development server: `npm run dev`
- Test all authentication flows
- Verify no console errors

**Final Comment to Add:** `// SECURITY FIX COMPLETED: Next.js middleware implemented for comprehensive session management and route protection`

---

## Task 3: Restore Admin Functionality (CVE-CP-002)
**Priority:** P0 - CRITICAL  
**Estimated Time:** 60 minutes  
**Risk:** Complete inability to manage user accounts

### Implementation Steps:

#### ☐ 3.1: Analyze Current Admin API Routes
**Instructions:**
- Examine `src/app/api/admin/users/[uid]/route.ts`
- Identify disabled endpoints returning 503 errors
- Document current admin functionality gaps

**Verification Command:**
```bash
# Check admin API routes
find src/app/api/admin -name "*.ts" -exec grep -l "503\|disabled" {} \;
```

**Expected Finding:** Admin routes returning "temporarily disabled" messages

#### ☐ 3.2: Create Admin Role Verification Utility
**Instructions:**
- Create helper function to verify admin roles
- Implement JWT admin role checking

**File Path:** `src/lib/auth/admin.ts`

**Code to Create:**
```typescript
import { createClient } from '@/lib/supabase/server'
import { NextRequest } from 'next/server'

export async function verifyAdminRole(request: NextRequest): Promise<{
  isAdmin: boolean
  user: any
  error?: string
}> {
  try {
    const supabase = await createClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return { isAdmin: false, user: null, error: 'Authentication required' }
    }

    // Check for admin role in user metadata or app_metadata
    const isAdmin = user.app_metadata?.role === 'admin' || 
                   user.user_metadata?.role === 'admin' ||
                   user.app_metadata?.claims_admin === true

    if (!isAdmin) {
      return { isAdmin: false, user, error: 'Admin role required' }
    }

    return { isAdmin: true, user }
  } catch (error) {
    return { isAdmin: false, user: null, error: 'Admin verification failed' }
  }
}

export async function requireAdminRole(request: NextRequest) {
  const { isAdmin, user, error } = await verifyAdminRole(request)
  
  if (!isAdmin) {
    throw new Error(error || 'Admin access denied')
  }
  
  return user
}
```

**Comment to Add:** `// Created admin role verification utility for secure admin endpoint access`

#### ☐ 3.3: Implement Admin User Management API
**Instructions:**
- Replace disabled admin user route with functional implementation
- Add proper authentication and authorization

**File to Modify:** `src/app/api/admin/users/[uid]/route.ts`

**Implementation:**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { requireAdminRole } from '@/lib/auth/admin'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { uid: string } }
) {
  try {
    // Verify admin role
    await requireAdminRole(request)
    
    const supabase = await createClient()
    const { uid } = params

    // Fetch user profile with admin privileges
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', uid)
      .single()

    if (error) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ user: profile })
  } catch (error) {
    return NextResponse.json(
      { error: error.message || 'Admin access denied' },
      { status: 403 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { uid: string } }
) {
  try {
    // Verify admin role
    await requireAdminRole(request)
    
    const supabase = await createClient()
    const { uid } = params
    const updateData = await request.json()

    // Update user profile with admin privileges
    const { data: updatedProfile, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', uid)
      .select()
      .single()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 400 }
      )
    }

    return NextResponse.json({ user: updatedProfile })
  } catch (error) {
    return NextResponse.json(
      { error: error.message || 'Admin access denied' },
      { status: 403 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { uid: string } }
) {
  try {
    // Verify admin role
    await requireAdminRole(request)
    
    const supabase = await createClient()
    const { uid } = params

    // Delete user profile (this doesn't delete auth.users - that requires service role)
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', uid)

    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete user profile' },
        { status: 400 }
      )
    }

    return NextResponse.json({ message: 'User profile deleted successfully' })
  } catch (error) {
    return NextResponse.json(
      { error: error.message || 'Admin access denied' },
      { status: 403 }
    )
  }
}
```

**Comment to Add:** `// Restored admin user management API with proper role-based access control`

#### ☐ 3.4: Create Admin User List Endpoint
**Instructions:**
- Create endpoint for listing all users with admin privileges
- Implement pagination and filtering

**File Path:** `src/app/api/admin/users/route.ts`

**Code to Create:**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { requireAdminRole } from '@/lib/auth/admin'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    // Verify admin role
    await requireAdminRole(request)
    
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100)
    const offset = (page - 1) * limit

    // Fetch users with pagination
    const { data: profiles, error, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      users: profiles,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    return NextResponse.json(
      { error: error.message || 'Admin access denied' },
      { status: 403 }
    )
  }
}
```

**Comment to Add:** `// Created admin user list endpoint with pagination and proper access control`

#### ☐ 3.5: Test Admin Functionality
**Instructions:**
- Create test admin user in database
- Test admin API endpoints
- Verify proper authentication and authorization

**Testing Steps:**
1. Create admin user in Supabase dashboard:
   ```sql
   UPDATE auth.users 
   SET raw_app_meta_data = raw_app_meta_data || '{"role": "admin"}'::jsonb 
   WHERE email = '<EMAIL>';
   ```
2. Test admin login
3. Test GET `/api/admin/users` - should return user list
4. Test GET `/api/admin/users/[uid]` - should return specific user
5. Test PUT `/api/admin/users/[uid]` - should update user
6. Test with non-admin user - should return 403

**Expected Results:**
- Admin users can access all endpoints
- Non-admin users receive 403 errors
- Proper data returned for all operations

#### ☐ 3.6: Update Admin Frontend Components
**Instructions:**
- Update admin pages to use restored API endpoints
- Remove hardcoded disabled messages

**File to Modify:** `src/app/admin/users/page.tsx`

**Key Changes:**
- Remove any "disabled" or "503" error handling
- Implement proper API calls to `/api/admin/users`
- Add loading states and error handling

**Comment to Add:** `// Updated admin frontend to use restored API endpoints`

**Final Comment to Add:** `// SECURITY FIX COMPLETED: Admin functionality fully restored with proper role-based access control and comprehensive user management capabilities`

---

## Task 4: Implement CSRF Protection (CVE-CP-004)
**Priority:** P0 - CRITICAL  
**Estimated Time:** 45 minutes  
**Risk:** Cross-site request forgery attacks

### Implementation Steps:

#### ☐ 4.1: Install CSRF Protection Library
**Instructions:**
- Install a Next.js compatible CSRF protection library
- Choose between edge-csrf or next-csrf

**Command to Run:**
```bash
npm install edge-csrf
```

**Comment to Add:** `// Added CSRF protection library for securing sensitive operations`

#### ☐ 4.2: Create CSRF Utility Functions
**Instructions:**
- Create CSRF token generation and validation utilities
- Implement middleware integration

**File Path:** `src/lib/csrf.ts`

**Code to Create:**
```typescript
import { createCsrfProtect } from 'edge-csrf'
import { NextRequest, NextResponse } from 'next/server'

// Initialize CSRF protection
const csrfProtect = createCsrfProtect({
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    name: '__Host-csrf-token',
    httpOnly: true,
  },
})

export async function validateCsrfToken(request: NextRequest): Promise<boolean> {
  try {
    const response = NextResponse.next()
    await csrfProtect(request, response)
    return true
  } catch (error) {
    console.error('CSRF validation failed:', error)
    return false
  }
}

export async function generateCsrfToken(request: NextRequest): Promise<string> {
  const response = NextResponse.next()
  await csrfProtect(request, response)
  
  // Extract token from response headers or cookies
  const token = response.headers.get('X-CSRF-Token') || 
                request.cookies.get('__Host-csrf-token')?.value || ''
  
  return token
}

export function createCsrfResponse(request: NextRequest, response: NextResponse): NextResponse {
  // Apply CSRF protection to response
  csrfProtect(request, response)
  return response
}
```

**Comment to Add:** `// Created CSRF token generation and validation utilities for protecting sensitive operations`

#### ☐ 4.3: Add CSRF Protection to Middleware
**Instructions:**
- Integrate CSRF protection into existing middleware
- Add CSRF token generation for protected routes

**File to Modify:** `middleware.ts`

**Code to Add (after user authentication check):**
```typescript
import { validateCsrfToken, createCsrfResponse } from '@/lib/csrf'

// Add after user authentication check in middleware
if (request.method !== 'GET' && request.method !== 'HEAD') {
  // Validate CSRF token for non-GET requests
  const isValidCsrf = await validateCsrfToken(request)
  
  if (!isValidCsrf) {
    return NextResponse.json(
      { error: 'CSRF token validation failed' },
      { status: 403 }
    )
  }
}

// Before returning supabaseResponse, apply CSRF protection
supabaseResponse = createCsrfResponse(request, supabaseResponse)
```

**Comment to Add:** `// Added CSRF protection to middleware for all non-GET requests`

#### ☐ 4.4: Create CSRF Token Hook
**Instructions:**
- Create React hook for accessing CSRF tokens in components
- Implement automatic token inclusion in forms

**File Path:** `src/hooks/useCsrfToken.ts`

**Code to Create:**
```typescript
import { useEffect, useState } from 'react'

export function useCsrfToken() {
  const [token, setToken] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchToken = async () => {
      try {
        const response = await fetch('/api/csrf-token', {
          method: 'GET',
          credentials: 'include',
        })
        
        if (response.ok) {
          const data = await response.json()
          setToken(data.token)
        }
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchToken()
  }, [])

  return { token, loading }
}
```

**Comment to Add:** `// Created CSRF token hook for automatic token management in React components`

#### ☐ 4.5: Create CSRF Token API Endpoint
**Instructions:**
- Create API endpoint for fetching CSRF tokens
- Ensure proper token generation and validation

**File Path:** `src/app/api/csrf-token/route.ts`

**Code to Create:**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { generateCsrfToken } from '@/lib/csrf'

export async function GET(request: NextRequest) {
  try {
    const token = await generateCsrfToken(request)
    
    return NextResponse.json({ token })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}
```

**Comment to Add:** `// Created CSRF token API endpoint for client-side token access`

#### ☐ 4.6: Update Form Components with CSRF Protection
**Instructions:**
- Update authentication forms to include CSRF tokens
- Add CSRF protection to profile edit forms

**Files to Modify:**
- `src/app/auth/reset-password/page.tsx`
- `src/components/userprofile/EditProfileModal.tsx`
- Any other forms handling sensitive operations

**Implementation Example:**
```tsx
import { useCsrfToken } from '@/hooks/useCsrfToken'

function SecureForm() {
  const { token, loading } = useCsrfToken()
  
  const handleSubmit = async (formData: FormData) => {
    if (!token) return
    
    formData.append('csrf-token', token)
    
    // Submit form with CSRF token
    const response = await fetch('/api/secure-endpoint', {
      method: 'POST',
      body: formData,
    })
  }

  if (loading) return <div>Loading...</div>

  return (
    <form action={handleSubmit}>
      <input type="hidden" name="csrf-token" value={token} />
      {/* Other form fields */}
    </form>
  )
}
```

**Comment to Add:** `// Added CSRF token protection to all sensitive forms and operations`

#### ☐ 4.7: Test CSRF Protection
**Instructions:**
- Test form submissions with valid CSRF tokens
- Test rejection of requests without CSRF tokens
- Verify protection across all sensitive operations

**Testing Steps:**
1. Submit forms with valid CSRF tokens - should succeed
2. Attempt form submission without CSRF token - should fail with 403
3. Test with expired/invalid CSRF tokens - should fail
4. Verify admin operations require CSRF tokens
5. Test profile updates require CSRF tokens

**Expected Results:**
- All legitimate form submissions work correctly
- Requests without CSRF tokens are rejected
- Forged cross-site requests are blocked

**Final Comment to Add:** `// SECURITY FIX COMPLETED: CSRF protection implemented across all sensitive operations, preventing cross-site request forgery attacks`

---

# 🔶 PHASE 2: HIGH PRIORITY SECURITY FIXES (2-3 days)

## Task 5: Implement Rate Limiting (VUL-CP-008)
**Priority:** P1 - HIGH  
**Estimated Time:** 60 minutes  
**Risk:** Brute force and credential stuffing attacks

### Implementation Steps:

#### ☐ 5.1: Install Rate Limiting Library
**Instructions:**
- Install Upstash Redis rate limiting library
- Set up Redis connection configuration

**Command to Run:**
```bash
npm install @upstash/ratelimit @upstash/redis
```

**Environment Variables to Add:**
```env
UPSTASH_REDIS_REST_URL=your_redis_url
UPSTASH_REDIS_REST_TOKEN=your_redis_token
```

**Comment to Add:** `// Added rate limiting dependencies and Redis configuration`

#### ☐ 5.2: Create Rate Limiting Utility
**Instructions:**
- Create configurable rate limiting utilities
- Implement different limits for different operations

**File Path:** `src/lib/rateLimit.ts`

**Code to Create:**
```typescript
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

// Redis instance
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
})

// Rate limiters for different operations
export const authRateLimit = new Ratelimit({
  redis: redis,
  limiter: Ratelimit.slidingWindow(5, '1m'), // 5 attempts per minute
  analytics: true,
})

export const apiRateLimit = new Ratelimit({
  redis: redis,
  limiter: Ratelimit.slidingWindow(100, '1m'), // 100 requests per minute
  analytics: true,
})

export const passwordResetRateLimit = new Ratelimit({
  redis: redis,
  limiter: Ratelimit.slidingWindow(3, '1h'), // 3 password resets per hour
  analytics: true,
})

export async function checkRateLimit(
  rateLimit: Ratelimit,
  identifier: string
): Promise<{ success: boolean; limit: number; remaining: number; reset: Date }> {
  const result = await rateLimit.limit(identifier)
  
  return {
    success: result.success,
    limit: result.limit,
    remaining: result.remaining,
    reset: new Date(result.reset),
  }
}
```

**Comment to Add:** `// Created rate limiting utilities for different types of operations with configurable limits`

#### ☐ 5.3: Add Rate Limiting to Authentication Endpoints
**Instructions:**
- Apply rate limiting to login endpoints
- Add rate limiting to registration endpoints

**File to Modify:** `src/app/auth/callback/route.ts`

**Code to Add:**
```typescript
import { authRateLimit, checkRateLimit } from '@/lib/rateLimit'

// Add at the beginning of authentication handlers
export async function POST(request: NextRequest) {
  const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'anonymous'
  
  // Check rate limit
  const { success, remaining } = await checkRateLimit(authRateLimit, clientIP)
  
  if (!success) {
    return NextResponse.json(
      { 
        error: 'Too many authentication attempts. Please try again later.',
        remaining: 0,
        reset: new Date(Date.now() + 60000) // 1 minute
      },
      { status: 429 }
    )
  }
  
  // Continue with authentication logic...
}
```

**Comment to Add:** `// Added rate limiting to authentication endpoints to prevent brute force attacks`

#### ☐ 5.4: Implement Rate Limiting Middleware
**Instructions:**
- Add rate limiting to the existing middleware
- Apply different limits based on route patterns

**File to Modify:** `middleware.ts`

**Code to Add:**
```typescript
import { apiRateLimit, authRateLimit, checkRateLimit } from '@/lib/rateLimit'

// Add after CSRF protection, before return
const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'anonymous'

// Apply different rate limits based on route
let rateLimit = apiRateLimit
if (request.nextUrl.pathname.startsWith('/auth') || 
    request.nextUrl.pathname.startsWith('/api/auth')) {
  rateLimit = authRateLimit
}

const { success, remaining, reset } = await checkRateLimit(rateLimit, clientIP)

if (!success) {
  return NextResponse.json(
    {
      error: 'Rate limit exceeded',
      remaining: 0,
      reset: reset.toISOString(),
    },
    { 
      status: 429,
      headers: {
        'X-RateLimit-Limit': rateLimit.limit.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': reset.getTime().toString(),
      }
    }
  )
}

// Add rate limit headers to successful responses
supabaseResponse.headers.set('X-RateLimit-Limit', rateLimit.limit.toString())
supabaseResponse.headers.set('X-RateLimit-Remaining', remaining.toString())
supabaseResponse.headers.set('X-RateLimit-Reset', reset.getTime().toString())
```

**Comment to Add:** `// Added comprehensive rate limiting middleware with different limits for auth and API routes`

#### ☐ 5.5: Test Rate Limiting
**Instructions:**
- Test rate limits on authentication endpoints
- Verify proper error responses and headers
- Test rate limit reset functionality

**Testing Steps:**
1. Make multiple rapid login attempts - should hit rate limit
2. Verify 429 status code and proper error message
3. Wait for rate limit reset - should allow requests again
4. Test different IP addresses get separate limits
5. Verify rate limit headers are present

**Expected Results:**
- Rate limits properly enforced
- Clear error messages for rate limit exceeded
- Proper headers indicating rate limit status

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Comprehensive rate limiting implemented to prevent brute force and abuse attacks`

---

## Task 6: Add Security Headers (VUL-CP-012)
**Priority:** P1 - HIGH  
**Estimated Time:** 45 minutes  
**Risk:** XSS, clickjacking, and other client-side attacks

### Implementation Steps:

#### ☐ 6.1: Configure Security Headers in Next.js
**Instructions:**
- Update Next.js configuration with security headers
- Implement Content Security Policy (CSP)

**File to Modify:** `next.config.ts`

**Code to Add:**
```typescript
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  },
  {
    key: 'Content-Security-Policy',
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "img-src 'self' blob: data: https:",
      "font-src 'self' https://fonts.gstatic.com",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
      "frame-src 'none'",
    ].join('; ')
  }
]

const nextConfig: NextConfig = {
  // ... existing config
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

**Comment to Add:** `// Added comprehensive security headers including CSP, HSTS, and XSS protection`

#### ☐ 6.2: Configure CORS Policies
**Instructions:**
- Add proper CORS configuration for API routes
- Restrict origins to allowed domains

**File Path:** `src/lib/cors.ts`

**Code to Create:**
```typescript
import { NextRequest, NextResponse } from 'next/server'

const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:9003',
  'https://criticalPixel.com',
  'https://www.criticalPixel.com',
]

export function corsMiddleware(request: NextRequest, response: NextResponse): NextResponse {
  const origin = request.headers.get('origin')
  
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  response.headers.set(
    'Access-Control-Allow-Methods', 
    'GET,DELETE,PATCH,POST,PUT,OPTIONS'
  )
  response.headers.set(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  )
  
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, { status: 200, headers: response.headers })
  }
  
  return response
}
```

**Comment to Add:** `// Created CORS middleware with proper origin restrictions and security headers`

#### ☐ 6.3: Add Security Headers to API Routes
**Instructions:**
- Update API routes to include security headers
- Implement consistent header application

**Implementation for API routes:**
```typescript
// Add to all API route handlers
export async function GET/POST/PUT/DELETE(request: NextRequest) {
  // ... existing logic
  
  const response = NextResponse.json(data)
  
  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  
  return response
}
```

**Comment to Add:** `// Added security headers to all API routes for consistent protection`

#### ☐ 6.4: Test Security Headers
**Instructions:**
- Verify security headers are present in responses
- Test CSP policy effectiveness
- Validate CORS restrictions

**Testing Commands:**
```bash
# Test security headers
curl -I http://localhost:9003/

# Test CORS
curl -H "Origin: https://malicious-site.com" \
     -H "Access-Control-Request-Method: POST" \
     -X OPTIONS \
     http://localhost:9003/api/test

# Test CSP
# Open browser developer tools and check for CSP violations
```

**Expected Results:**
- All security headers present in responses
- CORS requests from unauthorized origins rejected
- CSP policy prevents XSS attempts

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Comprehensive security headers implemented to prevent XSS, clickjacking, and other client-side attacks`

---

## Task 7: Enhanced Error Handling (VUL-CP-007)
**Priority:** P1 - HIGH  
**Estimated Time:** 30 minutes  
**Risk:** Information disclosure through error messages

### Implementation Steps:

#### ☐ 7.1: Create Secure Error Handler
**Instructions:**
- Create utility for sanitizing error messages
- Implement different error handling for production vs development

**File Path:** `src/lib/errorHandler.ts`

**Code to Create:**
```typescript
interface SecureError {
  message: string
  code?: string
  status: number
}

export function sanitizeError(error: any, isDevelopment: boolean = false): SecureError {
  // In development, show detailed errors
  if (isDevelopment && process.env.NODE_ENV === 'development') {
    return {
      message: error.message || 'An error occurred',
      code: error.code,
      status: error.status || 500
    }
  }
  
  // In production, sanitize error messages
  const sanitizedErrors: Record<string, SecureError> = {
    'auth/user-not-found': {
      message: 'Invalid credentials',
      code: 'AUTH_ERROR',
      status: 401
    },
    'auth/wrong-password': {
      message: 'Invalid credentials',
      code: 'AUTH_ERROR', 
      status: 401
    },
    'auth/invalid-email': {
      message: 'Invalid email format',
      code: 'VALIDATION_ERROR',
      status: 400
    },
    'database-error': {
      message: 'A database error occurred',
      code: 'DATABASE_ERROR',
      status: 500
    },
    'permission-denied': {
      message: 'Access denied',
      code: 'PERMISSION_ERROR',
      status: 403
    }
  }
  
  // Return sanitized error or generic error
  return sanitizedErrors[error.code] || {
    message: 'An unexpected error occurred',
    code: 'INTERNAL_ERROR',
    status: 500
  }
}

export function logError(error: any, context: string, userId?: string) {
  // Log full error details securely (not to client)
  console.error(`[${context}] Error for user ${userId || 'anonymous'}:`, {
    message: error.message,
    stack: error.stack,
    code: error.code,
    timestamp: new Date().toISOString(),
  })
}
```

**Comment to Add:** `// Created secure error handler to prevent information disclosure while maintaining debugging capabilities`

#### ☐ 7.2: Update Authentication Error Handling
**Instructions:**
- Apply secure error handling to authentication flows
- Sanitize database and Supabase errors

**Files to Modify:**
- `src/contexts/auth-context.tsx`
- `src/app/auth/callback/route.ts`

**Implementation Example:**
```typescript
import { sanitizeError, logError } from '@/lib/errorHandler'

// In authentication functions
try {
  // ... authentication logic
} catch (error) {
  // Log full error securely
  logError(error, 'authentication', user?.id)
  
  // Return sanitized error to client
  const secureError = sanitizeError(error)
  throw new Error(secureError.message)
}
```

**Comment to Add:** `// Applied secure error handling to authentication flows to prevent information leakage`

#### ☐ 7.3: Update API Route Error Handling
**Instructions:**
- Apply secure error handling to all API routes
- Ensure consistent error response format

**Implementation Pattern:**
```typescript
export async function POST(request: NextRequest) {
  try {
    // ... API logic
  } catch (error) {
    logError(error, `API:${request.nextUrl.pathname}`, userId)
    const secureError = sanitizeError(error)
    
    return NextResponse.json(
      { error: secureError.message, code: secureError.code },
      { status: secureError.status }
    )
  }
}
```

**Comment to Add:** `// Updated all API routes with secure error handling to prevent information disclosure`

#### ☐ 7.4: Remove Console.log Statements
**Instructions:**
- Remove or replace console.log statements with proper logging
- Ensure no sensitive data is logged to client console

**Command to Find Console Logs:**
```bash
# Find console.log statements
grep -r "console\." src/ --exclude-dir=node_modules
```

**Comment to Add:** `// Removed console.log statements and replaced with secure logging where appropriate`

#### ☐ 7.5: Test Error Handling
**Instructions:**
- Test error responses don't expose sensitive information
- Verify proper error logging in development
- Ensure user-friendly error messages in production

**Testing Steps:**
1. Trigger various error conditions (invalid login, database errors, etc.)
2. Verify error responses are sanitized
3. Check server logs contain full error details
4. Test in both development and production modes

**Expected Results:**
- Production errors are sanitized and user-friendly
- Development errors provide debugging information
- No sensitive information exposed to clients
- All errors properly logged server-side

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Secure error handling implemented to prevent information disclosure while maintaining debugging capabilities`

---

## Task 8: Simplify Profile Type System (VUL-CP-009)
**Priority:** P1 - HIGH  
**Estimated Time:** 60 minutes  
**Risk:** Type confusion and potential security bypasses

### Implementation Steps:

#### ☐ 8.1: Analyze Current Profile Types
**Instructions:**
- Examine all profile type definitions
- Identify overlapping and conflicting types

**Files to Examine:**
- `src/lib/types.ts`
- `src/lib/supabase/types.ts`
- `src/lib/types/profile.ts`
- `src/utils/profile-conversion.ts`

**Command to Find Profile Types:**
```bash
grep -r "interface.*Profile\|type.*Profile" src/ --include="*.ts" --include="*.tsx"
```

**Comment to Add:** `// Analyzed current profile type system and identified consolidation opportunities`

#### ☐ 8.2: Create Unified Profile Type
**Instructions:**
- Design single comprehensive profile type
- Include all necessary fields with proper typing

**File Path:** `src/lib/types/unifiedProfile.ts`

**Code to Create:**
```typescript
// Unified Profile Type - Single source of truth
export interface Profile {
  // Core identification
  id: string
  email: string
  username: string | null
  
  // Personal information
  full_name: string | null
  display_name: string | null
  bio: string | null
  avatar_url: string | null
  banner_url: string | null
  
  // Gaming profiles
  gaming_profiles: {
    steam?: string | null
    xbox?: string | null
    playstation?: string | null
    nintendo?: string | null
  }
  
  // Social media profiles
  social_profiles: {
    youtube?: string | null
    twitch?: string | null
    twitter?: string | null
    instagram?: string | null
    tiktok?: string | null
    discord?: string | null
    reddit?: string | null
    github?: string | null
  }
  
  // Privacy settings
  privacy_settings: {
    profile_visibility: 'public' | 'private' | 'friends'
    show_email: boolean
    show_gaming_profiles: boolean
    show_social_profiles: boolean
    show_reviews: boolean
    show_performance_data: boolean
  }
  
  // Theme and customization
  theme_settings: {
    color_scheme: 'light' | 'dark' | 'auto'
    primary_color: string
    accent_color: string
    banner_style: string
  }
  
  // Metadata
  created_at: string
  updated_at: string
  last_active_at: string | null
  is_verified: boolean
  role: 'user' | 'admin' | 'moderator'
}

// Database row type (matches Supabase table structure)
export interface ProfileRow {
  id: string
  email: string
  username: string | null
  full_name: string | null
  display_name: string | null
  bio: string | null
  avatar_url: string | null
  banner_url: string | null
  
  // Gaming profiles (stored as JSON)
  steam_profile: string | null
  xbox_profile: string | null
  playstation_profile: string | null
  nintendo_profile: string | null
  
  // Social profiles (stored as JSON)
  youtube_profile: string | null
  twitch_profile: string | null
  twitter_profile: string | null
  instagram_profile: string | null
  tiktok_profile: string | null
  discord_profile: string | null
  reddit_profile: string | null
  github_profile: string | null
  
  // Privacy settings (stored as JSON)
  privacy_settings: Json | null
  
  // Theme settings (stored as JSON)  
  theme_settings: Json | null
  
  // Metadata
  created_at: string
  updated_at: string
  last_active_at: string | null
  is_verified: boolean
  role: string
}

// Type conversion utilities
export function profileRowToProfile(row: ProfileRow): Profile {
  return {
    ...row,
    gaming_profiles: {
      steam: row.steam_profile,
      xbox: row.xbox_profile,
      playstation: row.playstation_profile,
      nintendo: row.nintendo_profile,
    },
    social_profiles: {
      youtube: row.youtube_profile,
      twitch: row.twitch_profile,
      twitter: row.twitter_profile,
      instagram: row.instagram_profile,
      tiktok: row.tiktok_profile,
      discord: row.discord_profile,
      reddit: row.reddit_profile,
      github: row.github_profile,
    },
    privacy_settings: row.privacy_settings as Profile['privacy_settings'] || {
      profile_visibility: 'public',
      show_email: false,
      show_gaming_profiles: true,
      show_social_profiles: true,
      show_reviews: true,
      show_performance_data: true,
    },
    theme_settings: row.theme_settings as Profile['theme_settings'] || {
      color_scheme: 'auto',
      primary_color: '#3b82f6',
      accent_color: '#8b5cf6',
      banner_style: 'default',
    },
    role: row.role as Profile['role'] || 'user',
  }
}

export function profileToProfileRow(profile: Profile): Partial<ProfileRow> {
  return {
    ...profile,
    steam_profile: profile.gaming_profiles.steam,
    xbox_profile: profile.gaming_profiles.xbox,
    playstation_profile: profile.gaming_profiles.playstation,
    nintendo_profile: profile.gaming_profiles.nintendo,
    youtube_profile: profile.social_profiles.youtube,
    twitch_profile: profile.social_profiles.twitch,
    twitter_profile: profile.social_profiles.twitter,
    instagram_profile: profile.social_profiles.instagram,
    tiktok_profile: profile.social_profiles.tiktok,
    discord_profile: profile.social_profiles.discord,
    reddit_profile: profile.social_profiles.reddit,
    github_profile: profile.social_profiles.github,
    privacy_settings: profile.privacy_settings as Json,
    theme_settings: profile.theme_settings as Json,
  }
}
```

**Comment to Add:** `// Created unified profile type system to eliminate type confusion and improve security`

#### ☐ 8.3: Update Components to Use Unified Types
**Instructions:**
- Replace all profile type usage with unified types
- Update import statements throughout codebase

**Files to Update:**
- `src/contexts/auth-context.tsx`
- `src/components/userprofile/EditProfileModal.tsx`
- `src/components/userprofile/GamerCard.tsx`
- All other profile-related components

**Implementation Pattern:**
```typescript
// Replace old imports
// import { UserProfile, ExtendedUserProfile } from '@/lib/types'

// With unified import
import { Profile, profileRowToProfile, profileToProfileRow } from '@/lib/types/unifiedProfile'
```

**Comment to Add:** `// Updated all components to use unified profile types for consistency and security`

#### ☐ 8.4: Remove Legacy Profile Types
**Instructions:**
- Remove old profile type definitions
- Remove profile conversion utilities
- Clean up unused type files

**Files to Remove/Modify:**
- Remove legacy types from `src/lib/types.ts`
- Clean up `src/utils/profile-conversion.ts`
- Remove unused profile type definitions

**Comment to Add:** `// Removed legacy profile types and conversion utilities to eliminate type confusion`

#### ☐ 8.5: Test Profile Type System
**Instructions:**
- Test profile creation and updates
- Verify type safety throughout application
- Test all profile-related functionality

**Testing Steps:**
1. Run TypeScript compilation: `npm run typecheck`
2. Test profile creation in auth flow
3. Test profile editing functionality
4. Verify gaming and social profile management
5. Test privacy settings functionality

**Expected Results:**
- No TypeScript errors
- All profile functionality works correctly
- Consistent type handling throughout application

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Unified profile type system implemented to eliminate type confusion and improve overall application security`

---

## Task 9: Enhanced Privacy Controls (VUL-CP-006)
**Priority:** P1 - HIGH  
**Estimated Time:** 45 minutes  
**Risk:** Unintended data exposure and privacy violations

### Implementation Steps:

#### ☐ 9.1: Update RLS Policies for Privacy
**Instructions:**
- Enhance Row Level Security policies to respect privacy settings
- Add privacy-aware query filters

**SQL to Execute (in Supabase SQL editor):**
```sql
-- Update profiles RLS policy to respect privacy settings
DROP POLICY IF EXISTS "Public profiles are viewable by everyone." ON profiles;

CREATE POLICY "Profiles visible based on privacy settings"
ON profiles FOR SELECT
USING (
  -- Owner can always see their own profile
  (SELECT auth.uid()) = id
  OR
  -- Public profiles are visible to everyone
  (privacy_settings->>'profile_visibility' = 'public')
  OR
  -- Private profiles only visible to authenticated users (could add friends logic later)
  (
    privacy_settings->>'profile_visibility' = 'private' 
    AND auth.role() = 'authenticated'
  )
);

-- Add privacy-aware policy for gaming profiles
CREATE POLICY "Gaming profiles respect privacy settings"
ON profiles FOR SELECT
USING (
  (SELECT auth.uid()) = id
  OR
  (
    (privacy_settings->>'profile_visibility' = 'public')
    AND
    (privacy_settings->>'show_gaming_profiles' = 'true')
  )
);

-- Add privacy-aware policy for social profiles  
CREATE POLICY "Social profiles respect privacy settings"
ON profiles FOR SELECT
USING (
  (SELECT auth.uid()) = id
  OR
  (
    (privacy_settings->>'profile_visibility' = 'public')
    AND
    (privacy_settings->>'show_social_profiles' = 'true')
  )
);
```

**Comment to Add:** `// Updated RLS policies to respect user privacy settings and prevent unauthorized data exposure`

#### ☐ 9.2: Implement Privacy-Aware Queries
**Instructions:**
- Create utility functions for privacy-aware data fetching
- Ensure all profile queries respect privacy settings

**File Path:** `src/lib/privacy/queries.ts`

**Code to Create:**
```typescript
import { createClient } from '@/lib/supabase/client'
import { Profile } from '@/lib/types/unifiedProfile'

export async function getPublicProfile(
  userId: string, 
  viewerId?: string
): Promise<Profile | null> {
  const supabase = createClient()
  
  // If viewer is the profile owner, return full profile
  if (viewerId === userId) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error || !data) return null
    return profileRowToProfile(data)
  }
  
  // For other viewers, apply privacy filters
  const { data, error } = await supabase
    .from('profiles')
    .select(`
      id,
      username,
      display_name,
      bio,
      avatar_url,
      banner_url,
      privacy_settings,
      theme_settings,
      created_at,
      is_verified,
      role,
      ${viewerId ? `
        CASE 
          WHEN privacy_settings->>'show_gaming_profiles' = 'true' 
          THEN steam_profile 
          ELSE null 
        END as steam_profile,
        CASE 
          WHEN privacy_settings->>'show_gaming_profiles' = 'true' 
          THEN xbox_profile 
          ELSE null 
        END as xbox_profile,
        CASE 
          WHEN privacy_settings->>'show_social_profiles' = 'true' 
          THEN youtube_profile 
          ELSE null 
        END as youtube_profile
      ` : ''}
    `)
    .eq('id', userId)
    .single()
  
  if (error || !data) return null
  
  // Filter out private information based on privacy settings
  const privacySettings = data.privacy_settings as any
  
  if (privacySettings?.profile_visibility === 'private' && !viewerId) {
    return null
  }
  
  return profileRowToProfile(data)
}

export async function searchPublicProfiles(
  query: string,
  limit: number = 20
): Promise<Profile[]> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
    .eq('privacy_settings->profile_visibility', 'public')
    .limit(limit)
  
  if (error || !data) return []
  
  return data.map(profileRowToProfile)
}
```

**Comment to Add:** `// Created privacy-aware query utilities to ensure data access respects user privacy settings`

#### ☐ 9.3: Update Profile Components for Privacy
**Instructions:**
- Update profile display components to respect privacy settings
- Hide sensitive information based on privacy preferences

**Files to Modify:**
- `src/components/userprofile/GamerCard.tsx`
- `src/app/u/[slug]/ProfilePageClient.tsx`

**Implementation Example:**
```tsx
function GamerCard({ profile, isOwnProfile }: { profile: Profile, isOwnProfile: boolean }) {
  const showGamingProfiles = isOwnProfile || profile.privacy_settings.show_gaming_profiles
  const showSocialProfiles = isOwnProfile || profile.privacy_settings.show_social_profiles
  const showEmail = isOwnProfile || profile.privacy_settings.show_email
  
  return (
    <div>
      {/* Always show basic info if profile is visible */}
      <h1>{profile.display_name || profile.username}</h1>
      
      {/* Conditionally show email */}
      {showEmail && profile.email && (
        <p>{profile.email}</p>
      )}
      
      {/* Conditionally show gaming profiles */}
      {showGamingProfiles && (
        <div>
          {profile.gaming_profiles.steam && (
            <a href={profile.gaming_profiles.steam}>Steam</a>
          )}
          {/* Other gaming profiles */}
        </div>
      )}
      
      {/* Conditionally show social profiles */}
      {showSocialProfiles && (
        <div>
          {profile.social_profiles.youtube && (
            <a href={profile.social_profiles.youtube}>YouTube</a>
          )}
          {/* Other social profiles */}
        </div>
      )}
    </div>
  )
}
```

**Comment to Add:** `// Updated profile components to respect privacy settings and hide sensitive information`

#### ☐ 9.4: Add Privacy Setting Controls
**Instructions:**
- Update profile edit modal with comprehensive privacy controls
- Allow users to control visibility of different data types

**File to Modify:** `src/components/userprofile/EditProfileModal.tsx`

**Code to Add:**
```tsx
function PrivacySettingsSection({ profile, onUpdate }: { 
  profile: Profile, 
  onUpdate: (settings: Profile['privacy_settings']) => void 
}) {
  return (
    <div className="privacy-settings">
      <h3>Privacy Settings</h3>
      
      <div>
        <label>Profile Visibility</label>
        <select 
          value={profile.privacy_settings.profile_visibility}
          onChange={(e) => onUpdate({
            ...profile.privacy_settings,
            profile_visibility: e.target.value as 'public' | 'private' | 'friends'
          })}
        >
          <option value="public">Public</option>
          <option value="private">Private</option>
          <option value="friends">Friends Only</option>
        </select>
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={profile.privacy_settings.show_email}
            onChange={(e) => onUpdate({
              ...profile.privacy_settings,
              show_email: e.target.checked
            })}
          />
          Show email address
        </label>
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={profile.privacy_settings.show_gaming_profiles}
            onChange={(e) => onUpdate({
              ...profile.privacy_settings,
              show_gaming_profiles: e.target.checked
            })}
          />
          Show gaming profiles
        </label>
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={profile.privacy_settings.show_social_profiles}
            onChange={(e) => onUpdate({
              ...profile.privacy_settings,
              show_social_profiles: e.target.checked
            })}
          />
          Show social media profiles
        </label>
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={profile.privacy_settings.show_reviews}
            onChange={(e) => onUpdate({
              ...profile.privacy_settings,
              show_reviews: e.target.checked
            })}
          />
          Show my reviews
        </label>
      </div>
    </div>
  )
}
```

**Comment to Add:** `// Added comprehensive privacy controls allowing users to control visibility of their data`

#### ☐ 9.5: Test Privacy Controls
**Instructions:**
- Test privacy settings with different user combinations
- Verify data is properly hidden based on settings
- Test RLS policy enforcement

**Testing Steps:**
1. Create test users with different privacy settings
2. View profiles while logged out - should respect public/private settings
3. View profiles while logged in as different user
4. Test each privacy setting individually
5. Verify database queries respect RLS policies

**Expected Results:**
- Private profiles not visible to unauthorized users
- Gaming/social profiles hidden when privacy setting disabled
- Email addresses only shown when permitted
- RLS policies prevent unauthorized data access

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Comprehensive privacy controls implemented with RLS enforcement to prevent unauthorized data exposure`

---

# 🔸 PHASE 3: SECURITY HARDENING (1 week)

## Task 10: Implement Multi-Factor Authentication (Future Enhancement)
**Priority:** P2 - MEDIUM  
**Estimated Time:** 120 minutes  
**Risk:** Single factor authentication vulnerability

### Implementation Steps:

#### ☐ 10.1: Enable MFA in Supabase
**Instructions:**
- Enable MFA in Supabase project settings
- Configure TOTP (Time-based One-Time Password) support

**Supabase Dashboard Steps:**
1. Go to Authentication > Settings
2. Enable "Enable Multi-Factor Authentication"
3. Configure TOTP settings
4. Save configuration

**Comment to Add:** `// Enabled MFA support in Supabase project configuration`

#### ☐ 10.2: Create MFA Setup Component
**Instructions:**
- Create component for users to set up MFA
- Include QR code generation for authenticator apps

**File Path:** `src/components/auth/MFASetup.tsx`

**Code to Create:**
```tsx
import { useState } from 'react'
import { useSupabaseClient, useUser } from '@supabase/auth-helpers-react'
import QRCode from 'qrcode'

export function MFASetup() {
  const supabase = useSupabaseClient()
  const user = useUser()
  const [qrCode, setQRCode] = useState<string>('')
  const [verificationCode, setVerificationCode] = useState('')
  const [factorId, setFactorId] = useState('')
  
  const setupMFA = async () => {
    const { data, error } = await supabase.auth.mfa.enroll({
      factorType: 'totp',
      friendlyName: 'Critical Pixel Account'
    })
    
    if (error) {
      console.error('MFA setup error:', error)
      return
    }
    
    setFactorId(data.id)
    
    // Generate QR code
    const qrCodeUrl = data.totp.qr_code
    const qrCodeDataUrl = await QRCode.toDataURL(qrCodeUrl)
    setQRCode(qrCodeDataUrl)
  }
  
  const verifyMFA = async () => {
    const { error } = await supabase.auth.mfa.challengeAndVerify({
      factorId,
      code: verificationCode
    })
    
    if (error) {
      console.error('MFA verification error:', error)
      return
    }
    
    alert('MFA setup successful!')
  }
  
  return (
    <div className="mfa-setup">
      <h2>Set Up Multi-Factor Authentication</h2>
      
      {!qrCode ? (
        <button onClick={setupMFA}>Enable MFA</button>
      ) : (
        <div>
          <p>Scan this QR code with your authenticator app:</p>
          <img src={qrCode} alt="MFA QR Code" />
          
          <div>
            <input
              type="text"
              placeholder="Enter verification code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
            />
            <button onClick={verifyMFA}>Verify</button>
          </div>
        </div>
      )}
    </div>
  )
}
```

**Comment to Add:** `// Created MFA setup component with QR code generation for authenticator app integration`

#### ☐ 10.3: Add MFA Challenge Component
**Instructions:**
- Create component for MFA challenges during login
- Handle MFA verification in authentication flow

**File Path:** `src/components/auth/MFAChallenge.tsx`

**Code to Create:**
```tsx
import { useState } from 'react'
import { useSupabaseClient } from '@supabase/auth-helpers-react'

interface MFAChallengeProps {
  onSuccess: () => void
  onCancel: () => void
}

export function MFAChallenge({ onSuccess, onCancel }: MFAChallengeProps) {
  const supabase = useSupabaseClient()
  const [code, setCode] = useState('')
  const [loading, setLoading] = useState(false)
  
  const verifyCode = async () => {
    setLoading(true)
    
    const { error } = await supabase.auth.mfa.verify({
      factorId: 'current', // Use current factor
      code
    })
    
    if (error) {
      console.error('MFA verification failed:', error)
      alert('Invalid verification code')
    } else {
      onSuccess()
    }
    
    setLoading(false)
  }
  
  return (
    <div className="mfa-challenge">
      <h2>Multi-Factor Authentication Required</h2>
      <p>Enter the verification code from your authenticator app:</p>
      
      <div>
        <input
          type="text"
          placeholder="000000"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          maxLength={6}
        />
        <button onClick={verifyCode} disabled={loading || code.length !== 6}>
          {loading ? 'Verifying...' : 'Verify'}
        </button>
        <button onClick={onCancel}>Cancel</button>
      </div>
    </div>
  )
}
```

**Comment to Add:** `// Created MFA challenge component for secure login verification`

#### ☐ 10.4: Update Authentication Flow for MFA
**Instructions:**
- Integrate MFA challenges into login process
- Update authentication context to handle MFA states

**File to Modify:** `src/contexts/auth-context.tsx`

**Code to Add:**
```tsx
// Add MFA state management
const [mfaRequired, setMfaRequired] = useState(false)
const [mfaFactorId, setMfaFactorId] = useState<string | null>(null)

// Update login function
const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  
  if (error) {
    throw error
  }
  
  // Check if MFA is required
  if (data.user && !data.session) {
    // MFA challenge required
    const factors = await supabase.auth.mfa.listFactors()
    if (factors.data && factors.data.totp.length > 0) {
      setMfaRequired(true)
      setMfaFactorId(factors.data.totp[0].id)
    }
  }
}
```

**Comment to Add:** `// Integrated MFA challenges into authentication flow for enhanced security`

#### ☐ 10.5: Add MFA Enforcement RLS Policy
**Instructions:**
- Create RLS policy requiring MFA for sensitive operations
- Apply MFA requirement to admin functions

**SQL to Execute:**
```sql
-- Create policy requiring MFA for admin operations
CREATE POLICY "Admin operations require MFA"
ON profiles
FOR UPDATE
TO authenticated
USING (
  -- Allow if user is updating their own profile (basic updates)
  (SELECT auth.uid()) = id
  OR
  -- For admin operations, require MFA (aal2)
  (
    (SELECT auth.jwt()->>'role') = 'admin'
    AND
    (SELECT auth.jwt()->>'aal') = 'aal2'
  )
);

-- Create policy for sensitive data access requiring MFA
CREATE POLICY "Sensitive data requires MFA"
ON user_sensitive_data
FOR ALL
TO authenticated
USING ((SELECT auth.jwt()->>'aal') = 'aal2');
```

**Comment to Add:** `// Added RLS policies requiring MFA for sensitive operations and admin functions`

#### ☐ 10.6: Test MFA Implementation
**Instructions:**
- Test MFA setup process
- Verify MFA challenges work correctly
- Test RLS enforcement with MFA

**Testing Steps:**
1. Set up MFA for test user
2. Test login with MFA challenge
3. Verify admin operations require MFA
4. Test MFA recovery scenarios
5. Verify RLS policies enforce MFA requirements

**Expected Results:**
- MFA setup works smoothly
- Login requires MFA verification when enabled
- Admin operations blocked without MFA
- Sensitive data access requires MFA

**Final Comment to Add:** `// SECURITY ENHANCEMENT COMPLETED: Multi-Factor Authentication implemented with RLS enforcement for enhanced account security`

---

## 📊 IMPLEMENTATION TRACKING

### Phase 1 Progress: Critical Fixes
- [ ] Task 1: Fix AuthProvider Integration
- [ ] Task 2: Implement Next.js Middleware  
- [ ] Task 3: Restore Admin Functionality
- [ ] Task 4: Implement CSRF Protection

### Phase 2 Progress: High Priority
- [ ] Task 5: Implement Rate Limiting
- [ ] Task 6: Add Security Headers
- [ ] Task 7: Enhanced Error Handling
- [ ] Task 8: Simplify Profile Type System
- [ ] Task 9: Enhanced Privacy Controls

### Phase 3 Progress: Security Hardening
- [ ] Task 10: Implement Multi-Factor Authentication
- [ ] Task 11: Enhanced Audit Logging (Optional)
- [ ] Task 12: Security Testing Integration (Optional)
- [ ] Task 13: Performance Security Optimization (Optional)

---

## 🧪 VERIFICATION CHECKLIST

After completing all tasks, verify the following:

### Security Verification:
- [ ] Authentication state properly managed app-wide
- [ ] Session management working with automatic refresh
- [ ] Admin functionality restored with proper access control
- [ ] CSRF protection preventing cross-site attacks
- [ ] Rate limiting protecting against brute force
- [ ] Security headers preventing XSS and clickjacking
- [ ] Error messages sanitized in production
- [ ] Profile types simplified and secure
- [ ] Privacy controls enforcing data protection
- [ ] MFA providing additional security layer

### Functional Verification:
- [ ] All authentication flows working correctly
- [ ] Profile creation and editing functional
- [ ] Admin user management operational
- [ ] Privacy settings properly applied
- [ ] Gaming and social profiles secure
- [ ] Review system unaffected by changes
- [ ] Performance maintained or improved

### Testing Commands:
```bash
# Run all checks
npm run typecheck
npm run lint
npm run build

# Test development server
npm run dev

# Security header testing
curl -I http://localhost:9003/

# Rate limiting testing (repeat rapidly)
curl -X POST http://localhost:9003/api/auth/login
```

---

## 🚨 ROLLBACK PROCEDURES

If any implementation causes issues:

### Emergency Rollback Steps:
1. **Revert Git Changes:**
   ```bash
   git stash
   git checkout main
   ```

2. **Restore Database:**
   - Revert any RLS policy changes
   - Restore original table permissions

3. **Environment Reset:**
   - Remove new environment variables
   - Restart development server

### Specific Task Rollbacks:
- **AuthProvider Issues:** Comment out AuthProvider wrapper temporarily
- **Middleware Problems:** Rename `middleware.ts` to `middleware.ts.bak`
- **Admin API Errors:** Restore 503 responses temporarily
- **CSRF Problems:** Disable CSRF validation in middleware

---

**IMPLEMENTATION GUIDE COMPLETE**

This comprehensive guide provides step-by-step instructions for implementing all critical and high-priority security fixes identified in the security assessment. Each task includes detailed implementation steps, verification procedures, and rollback options to ensure safe and successful security enhancement of the Critical Pixel authentication system.

Follow the phases in order, complete all checkboxes, and add the specified comments for proper tracking. The result will be a significantly more secure authentication system that protects user accounts and prevents the identified vulnerabilities.

---

*This implementation guide follows Microsoft-level security standards and provides enterprise-grade security enhancements for the Critical Pixel platform.*