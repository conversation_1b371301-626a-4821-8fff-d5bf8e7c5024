'use client';

import { useState } from 'react';
import '@/components/review-form/style/lexical.css';
import '@/components/review-form/style/lexicaltoolbar.css';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import EditorNodes from '@/components/review-form/lexical/nodes';
import PremiumToolbarPlugin from '@/components/review-form/lexical/plugins/PremiumToolbarPlugin';

const editorConfig = {
  namespace: 'YouTubeTestEditor',
  nodes: EditorNodes,
  onError: (error: Error) => {
    console.error('Lexical error:', error);
  },
  editable: true,
  theme: {
    paragraph: 'editor-paragraph',
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      underline: 'editor-text-underline',
      strikethrough: 'editor-text-strikethrough',
      underlineStrikethrough: 'editor-text-underlineStrikethrough',
      code: 'editor-text-code',
    },
    heading: {
      h1: 'editor-heading-h1',
      h2: 'editor-heading-h2',
      h3: 'editor-heading-h3',
      h4: 'editor-heading-h4',
      h5: 'editor-heading-h5',
      h6: 'editor-heading-h6',
    },
    link: 'editor-link',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    quote: 'editor-quote',
    code: 'editor-code',
    youtube: 'editor-youtube',
    hashtag: 'editor-hashtag',
    table: 'editor-table',
    tableCell: 'editor-table-cell',
    tableCellHeader: 'editor-table-cell-header',
  },
};

export default function YouTubeTestPage() {
  return (
    <div className="min-h-screen bg-slate-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-white text-2xl font-bold mb-6">YouTube Embed Test</h1>
        <div className="bg-slate-800 rounded-lg p-4">
          <LexicalComposer initialConfig={editorConfig}>
            <div className="lexical-editor-container">
              <div className="lexical-editor-wrapper">
                <PremiumToolbarPlugin />
                <div className="lexical-editor-content">
                  <RichTextPlugin
                    contentEditable={
                      <ContentEditable
                        className="lexical-content-editable min-h-[400px] outline-none p-8 text-white"
                      />
                    }
                    placeholder={
                      <div className="lexical-placeholder">
                        Start typing or click the YouTube button to insert a video...
                      </div>
                    }
                    ErrorBoundary={LexicalErrorBoundary}
                  />
                  <HistoryPlugin />
                  <ListPlugin />
                  <TablePlugin hasCellMerge={false} hasCellBackgroundColor={false} />
                </div>
              </div>
            </div>
          </LexicalComposer>
        </div>
        <div className="mt-6 text-slate-400 text-sm">
          <p>Test the YouTube button in the toolbar above. Try these video URLs:</p>
          <ul className="mt-2 space-y-1">
            <li>• https://www.youtube.com/watch?v=dQw4w9WgXcQ</li>
            <li>• https://youtu.be/dQw4w9WgXcQ</li>
            <li>• dQw4w9WgXcQ (just the video ID)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}