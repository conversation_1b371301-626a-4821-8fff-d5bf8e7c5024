// src/hooks/useB2ImageUpload.ts
import { useState, useCallback, useRef } from 'react';

export interface UploadProgress {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  result?: UploadResult;
  error?: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    width: number;
    height: number;
    format: string;
  };
}

export interface UseB2ImageUploadOptions {
  maxFiles?: number;
  maxFileSize?: number;
  allowedTypes?: string[];
  onUploadComplete?: (results: UploadResult[]) => void;
  onUploadError?: (error: string) => void;
  onProgress?: (progress: UploadProgress[]) => void;
}

export function useB2ImageUpload(options: UseB2ImageUploadOptions = {}) {
  const {
    maxFiles = 10,
    maxFileSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    onUploadComplete,
    onUploadError,
    onProgress,
  } = options;

  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Generate unique ID for upload tracking
  const generateUploadId = () => Math.random().toString(36).substr(2, 9);

  // Validate files before upload
  const validateFiles = useCallback((files: File[]): { valid: File[]; errors: string[] } => {
    const errors: string[] = [];
    const valid: File[] = [];

    if (files.length > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed`);
      return { valid: [], errors };
    }

    files.forEach((file) => {
      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: File type not allowed`);
        return;
      }

      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File size exceeds ${maxFileSize / 1024 / 1024}MB limit`);
        return;
      }

      valid.push(file);
    });

    return { valid, errors };
  }, [maxFiles, maxFileSize, allowedTypes]);

  // Update progress for specific upload
  const updateUploadProgress = useCallback((id: string, updates: Partial<UploadProgress>) => {
    setUploads(prev => {
      const updated = prev.map(upload => 
        upload.id === id ? { ...upload, ...updates } : upload
      );
      onProgress?.(updated);
      return updated;
    });
  }, [onProgress]);

  // Upload single file with progress tracking
  const uploadSingleFile = useCallback(async (file: File, uploadId: string): Promise<UploadResult> => {
    const formData = new FormData();
    formData.append('images', file);

    try {
      updateUploadProgress(uploadId, { status: 'uploading', progress: 0 });

      const response = await fetch('/api/b2/upload', {
        method: 'POST',
        body: formData,
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      
      if (data.results && data.results[0]) {
        const result = data.results[0];
        updateUploadProgress(uploadId, { 
          status: result.success ? 'completed' : 'error',
          progress: 100,
          result,
          error: result.success ? undefined : result.error,
        });
        return result;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      updateUploadProgress(uploadId, { 
        status: 'error',
        error: errorMessage,
      });
      throw error;
    }
  }, [updateUploadProgress]);

  // Main upload function
  const uploadFiles = useCallback(async (files: File[]) => {
    const { valid, errors } = validateFiles(files);

    if (errors.length > 0) {
      onUploadError?.(errors.join(', '));
      return;
    }

    if (valid.length === 0) {
      return;
    }

    // Create upload progress entries
    const newUploads: UploadProgress[] = valid.map(file => ({
      id: generateUploadId(),
      file,
      progress: 0,
      status: 'pending' as const,
    }));

    setUploads(newUploads);
    setIsUploading(true);
    
    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController();

    try {
      // Upload files in parallel with limited concurrency
      const maxConcurrent = 3;
      const results: UploadResult[] = [];
      
      for (let i = 0; i < newUploads.length; i += maxConcurrent) {
        const batch = newUploads.slice(i, i + maxConcurrent);
        const batchPromises = batch.map(upload => 
          uploadSingleFile(upload.file, upload.id)
        );
        
        try {
          const batchResults = await Promise.allSettled(batchPromises);
          batchResults.forEach((result) => {
            if (result.status === 'fulfilled') {
              results.push(result.value);
            }
          });
        } catch (error) {
          console.error('Batch upload error:', error);
        }
      }

      // Filter successful uploads
      const successful = results.filter(result => result.success);
      
      if (successful.length > 0) {
        onUploadComplete?.(successful);
      }

      // Check for any errors
      const failed = results.filter(result => !result.success);
      if (failed.length > 0) {
        const errorMessages = failed.map(f => f.error || 'Unknown error').join(', ');
        onUploadError?.(errorMessages);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload process failed';
      onUploadError?.(errorMessage);
    } finally {
      setIsUploading(false);
      abortControllerRef.current = null;
    }
  }, [validateFiles, uploadSingleFile, onUploadComplete, onUploadError]);

  // Cancel ongoing uploads
  const cancelUploads = useCallback(() => {
    abortControllerRef.current?.abort();
    setIsUploading(false);
    setUploads([]);
  }, []);

  // Clear completed uploads
  const clearUploads = useCallback(() => {
    setUploads([]);
  }, []);

  // Get upload statistics
  const getUploadStats = useCallback(() => {
    const total = uploads.length;
    const completed = uploads.filter(u => u.status === 'completed').length;
    const failed = uploads.filter(u => u.status === 'error').length;
    const pending = uploads.filter(u => u.status === 'pending' || u.status === 'uploading').length;

    return { total, completed, failed, pending };
  }, [uploads]);

  return {
    uploads,
    isUploading,
    uploadFiles,
    cancelUploads,
    clearUploads,
    getUploadStats,
  };
}
