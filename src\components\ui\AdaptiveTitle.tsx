'use client';

import { ReactNode } from 'react';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from '@/lib/utils';

// Import CSS for adaptive text classes
import '@/components/review-form/style/NewReview.css';

interface AdaptiveTitleProps {
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'title' | 'subtitle' | 'content';
  codeStyle?: boolean;
}

const sizeClasses = {
  sm: 'text-lg',
  md: 'text-2xl', 
  lg: 'text-3xl',
  xl: 'text-4xl'
};

const variantClasses = {
  title: 'adaptive-text-title',
  subtitle: 'adaptive-text-subtitle', 
  content: 'adaptive-text-content'
};

/**
 * AdaptiveTitle Component
 * 
 * A centralized component that automatically adapts text color based on background brightness.
 * This ensures all titles across the application use consistent adaptive styling without
 * needing to manually add useBackgroundBrightness hook and conditional classes.
 * 
 * Features:
 * - Automatic background brightness detection
 * - Consistent text color adaptation
 * - Multiple size and variant options
 * - Optional code-style bracket wrapping
 * - Smooth transitions
 * 
 * Usage:
 * <AdaptiveTitle size="lg" variant="title" codeStyle>
 *   My Dashboard Section
 * </AdaptiveTitle>
 * 
 * This replaces the pattern of:
 * const isDarkBackground = useBackgroundBrightness();
 * <span className={`adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
 */
export function AdaptiveTitle({
  children,
  className,
  size = 'lg',
  variant = 'title',
  codeStyle = false
}: AdaptiveTitleProps) {
  const isDarkBackground = useBackgroundBrightness();
  
  const baseClasses = cn(
    'font-mono font-bold transition-all duration-300',
    sizeClasses[size],
    variantClasses[variant],
    isDarkBackground ? 'dark-background' : 'light-background',
    className
  );

  if (codeStyle) {
    return (
      <span className={baseClasses}>
        <span className="text-violet-400/60">&lt;</span>
        <span className="px-2">{children}</span>
        <span className="text-violet-400/60">/&gt;</span>
      </span>
    );
  }

  return (
    <span className={baseClasses}>
      {children}
    </span>
  );
}

/**
 * Pre-configured title components for common use cases
 */
export function DashboardSectionTitle({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn("border-l-4 border-purple-500 pl-4", className)}>
      <AdaptiveTitle size="lg" variant="title" codeStyle>
        {children}
      </AdaptiveTitle>
    </div>
  );
}

export function DashboardSubtitle({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <AdaptiveTitle size="md" variant="subtitle" className={className}>
      {children}
    </AdaptiveTitle>
  );
}

export function DashboardCardTitle({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <AdaptiveTitle size="sm" variant="title" className={className}>
      {children}
    </AdaptiveTitle>
  );
}