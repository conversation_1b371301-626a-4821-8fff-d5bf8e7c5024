export interface GPUSpec {
  id: string;
  name: string;
  manufacturer: string;
  model: string;
}

export const GPU_DATABASE: GPUSpec[] = [
  // ======== NVIDIA ======== (Newest First)
  // RTX 50 Series (2025)
  { id: 'nvidia-rtx-5090', name: 'NVIDIA RTX 5090', manufacturer: 'NVIDIA', model: 'RTX 5090' },
  { id: 'nvidia-rtx-5080', name: 'NVIDIA RTX 5080', manufacturer: 'NVIDIA', model: 'RTX 5080' },
  { id: 'nvidia-rtx-5070-ti', name: 'NVIDIA RTX 5070 Ti', manufacturer: 'NVIDIA', model: 'RTX 5070 Ti' },
  { id: 'nvidia-rtx-5070', name: 'NVIDIA RTX 5070', manufacturer: 'NVIDIA', model: 'RTX 5070' },
  { id: 'nvidia-rtx-5060-ti', name: 'NVIDIA RTX 5060 Ti', manufacturer: 'NVIDIA', model: 'RTX 5060 Ti' },
  { id: 'nvidia-rtx-5060', name: 'NVIDIA RTX 5060', manufacturer: 'NVIDIA', model: 'RTX 5060' },
  
  // RTX 40 Series (2022-2023)
  { id: 'nvidia-rtx-4090', name: 'NVIDIA RTX 4090', manufacturer: 'NVIDIA', model: 'RTX 4090' },
  { id: 'nvidia-rtx-4080-super', name: 'NVIDIA RTX 4080 SUPER', manufacturer: 'NVIDIA', model: 'RTX 4080 SUPER' },
  { id: 'nvidia-rtx-4080', name: 'NVIDIA RTX 4080', manufacturer: 'NVIDIA', model: 'RTX 4080' },
  { id: 'nvidia-rtx-4070-ti-super', name: 'NVIDIA RTX 4070 Ti SUPER', manufacturer: 'NVIDIA', model: 'RTX 4070 Ti SUPER' },
  { id: 'nvidia-rtx-4070-ti', name: 'NVIDIA RTX 4070 Ti', manufacturer: 'NVIDIA', model: 'RTX 4070 Ti' },
  { id: 'nvidia-rtx-4070-super', name: 'NVIDIA RTX 4070 SUPER', manufacturer: 'NVIDIA', model: 'RTX 4070 SUPER' },
  { id: 'nvidia-rtx-4070', name: 'NVIDIA RTX 4070', manufacturer: 'NVIDIA', model: 'RTX 4070' },
  { id: 'nvidia-rtx-4060-ti', name: 'NVIDIA RTX 4060 Ti', manufacturer: 'NVIDIA', model: 'RTX 4060 Ti' },
  { id: 'nvidia-rtx-4060', name: 'NVIDIA RTX 4060', manufacturer: 'NVIDIA', model: 'RTX 4060' },
  
  // RTX 30 Series (2020-2021)
  { id: 'nvidia-rtx-3090-ti', name: 'NVIDIA RTX 3090 Ti', manufacturer: 'NVIDIA', model: 'RTX 3090 Ti' },
  { id: 'nvidia-rtx-3090', name: 'NVIDIA RTX 3090', manufacturer: 'NVIDIA', model: 'RTX 3090' },
  { id: 'nvidia-rtx-3080-ti', name: 'NVIDIA RTX 3080 Ti', manufacturer: 'NVIDIA', model: 'RTX 3080 Ti' },
  { id: 'nvidia-rtx-3080', name: 'NVIDIA RTX 3080', manufacturer: 'NVIDIA', model: 'RTX 3080' },
  { id: 'nvidia-rtx-3070-ti', name: 'NVIDIA RTX 3070 Ti', manufacturer: 'NVIDIA', model: 'RTX 3070 Ti' },
  { id: 'nvidia-rtx-3070', name: 'NVIDIA RTX 3070', manufacturer: 'NVIDIA', model: 'RTX 3070' },
  { id: 'nvidia-rtx-3060-ti', name: 'NVIDIA RTX 3060 Ti', manufacturer: 'NVIDIA', model: 'RTX 3060 Ti' },
  { id: 'nvidia-rtx-3060', name: 'NVIDIA RTX 3060', manufacturer: 'NVIDIA', model: 'RTX 3060' },
  { id: 'nvidia-rtx-3050', name: 'NVIDIA RTX 3050', manufacturer: 'NVIDIA', model: 'RTX 3050' },
  
  // GTX 16 Series (2019)
  { id: 'nvidia-gtx-1660-super', name: 'NVIDIA GTX 1660 SUPER', manufacturer: 'NVIDIA', model: 'GTX 1660 SUPER' },
  { id: 'nvidia-gtx-1660-ti', name: 'NVIDIA GTX 1660 Ti', manufacturer: 'NVIDIA', model: 'GTX 1660 Ti' },
  { id: 'nvidia-gtx-1660', name: 'NVIDIA GTX 1660', manufacturer: 'NVIDIA', model: 'GTX 1660' },
  { id: 'nvidia-gtx-1650-super', name: 'NVIDIA GTX 1650 SUPER', manufacturer: 'NVIDIA', model: 'GTX 1650 SUPER' },
  { id: 'nvidia-gtx-1650', name: 'NVIDIA GTX 1650', manufacturer: 'NVIDIA', model: 'GTX 1650' },
  
  // RTX 20 Series (2018)
  { id: 'nvidia-rtx-2080-ti', name: 'NVIDIA RTX 2080 Ti', manufacturer: 'NVIDIA', model: 'RTX 2080 Ti' },
  { id: 'nvidia-rtx-2080-super', name: 'NVIDIA RTX 2080 SUPER', manufacturer: 'NVIDIA', model: 'RTX 2080 SUPER' },
  { id: 'nvidia-rtx-2080', name: 'NVIDIA RTX 2080', manufacturer: 'NVIDIA', model: 'RTX 2080' },
  { id: 'nvidia-rtx-2070-super', name: 'NVIDIA RTX 2070 SUPER', manufacturer: 'NVIDIA', model: 'RTX 2070 SUPER' },
  { id: 'nvidia-rtx-2070', name: 'NVIDIA RTX 2070', manufacturer: 'NVIDIA', model: 'RTX 2070' },
  { id: 'nvidia-rtx-2060-super', name: 'NVIDIA RTX 2060 SUPER', manufacturer: 'NVIDIA', model: 'RTX 2060 SUPER' },
  { id: 'nvidia-rtx-2060', name: 'NVIDIA RTX 2060', manufacturer: 'NVIDIA', model: 'RTX 2060' },
  
  // GTX 10 Series (2016)
  { id: 'nvidia-gtx-1080-ti', name: 'NVIDIA GTX 1080 Ti', manufacturer: 'NVIDIA', model: 'GTX 1080 Ti' },
  { id: 'nvidia-gtx-1080', name: 'NVIDIA GTX 1080', manufacturer: 'NVIDIA', model: 'GTX 1080' },
  { id: 'nvidia-gtx-1070-ti', name: 'NVIDIA GTX 1070 Ti', manufacturer: 'NVIDIA', model: 'GTX 1070 Ti' },
  { id: 'nvidia-gtx-1070', name: 'NVIDIA GTX 1070', manufacturer: 'NVIDIA', model: 'GTX 1070' },
  { id: 'nvidia-gtx-1060', name: 'NVIDIA GTX 1060', manufacturer: 'NVIDIA', model: 'GTX 1060' },
  { id: 'nvidia-gtx-1050-ti', name: 'NVIDIA GTX 1050 Ti', manufacturer: 'NVIDIA', model: 'GTX 1050 Ti' },
  { id: 'nvidia-gtx-1050', name: 'NVIDIA GTX 1050', manufacturer: 'NVIDIA', model: 'GTX 1050' },
  { id: 'nvidia-gtx-1030', name: 'NVIDIA GTX 1030', manufacturer: 'NVIDIA', model: 'GTX 1030' },
  
  // 900 Series (2014)
  { id: 'nvidia-gtx-980-ti', name: 'NVIDIA GTX 980 Ti', manufacturer: 'NVIDIA', model: 'GTX 980 Ti' },
  { id: 'nvidia-gtx-980', name: 'NVIDIA GTX 980', manufacturer: 'NVIDIA', model: 'GTX 980' },
  { id: 'nvidia-gtx-970', name: 'NVIDIA GTX 970', manufacturer: 'NVIDIA', model: 'GTX 970' },
  { id: 'nvidia-gtx-960', name: 'NVIDIA GTX 960', manufacturer: 'NVIDIA', model: 'GTX 960' },
  { id: 'nvidia-gtx-950', name: 'NVIDIA GTX 950', manufacturer: 'NVIDIA', model: 'GTX 950' },
  
  // 700 Series (2013)
  { id: 'nvidia-gtx-780-ti', name: 'NVIDIA GTX 780 Ti', manufacturer: 'NVIDIA', model: 'GTX 780 Ti' },
  { id: 'nvidia-gtx-780', name: 'NVIDIA GTX 780', manufacturer: 'NVIDIA', model: 'GTX 780' },
  { id: 'nvidia-gtx-770', name: 'NVIDIA GTX 770', manufacturer: 'NVIDIA', model: 'GTX 770' },
  { id: 'nvidia-gtx-760', name: 'NVIDIA GTX 760', manufacturer: 'NVIDIA', model: 'GTX 760' },
  { id: 'nvidia-gtx-750-ti', name: 'NVIDIA GTX 750 Ti', manufacturer: 'NVIDIA', model: 'GTX 750 Ti' },
  { id: 'nvidia-gtx-750', name: 'NVIDIA GTX 750', manufacturer: 'NVIDIA', model: 'GTX 750' },
  { id: 'nvidia-gtx-745', name: 'NVIDIA GTX 745', manufacturer: 'NVIDIA', model: 'GTX 745' },
  
  // 600 Series (2012)
  { id: 'nvidia-gtx-690', name: 'NVIDIA GTX 690', manufacturer: 'NVIDIA', model: 'GTX 690' },
  { id: 'nvidia-gtx-680', name: 'NVIDIA GTX 680', manufacturer: 'NVIDIA', model: 'GTX 680' },
  { id: 'nvidia-gtx-670', name: 'NVIDIA GTX 670', manufacturer: 'NVIDIA', model: 'GTX 670' },
  { id: 'nvidia-gtx-660-ti', name: 'NVIDIA GTX 660 Ti', manufacturer: 'NVIDIA', model: 'GTX 660 Ti' },
  { id: 'nvidia-gtx-660', name: 'NVIDIA GTX 660', manufacturer: 'NVIDIA', model: 'GTX 660' },
  { id: 'nvidia-gtx-650-ti', name: 'NVIDIA GTX 650 Ti', manufacturer: 'NVIDIA', model: 'GTX 650 Ti' },
  { id: 'nvidia-gtx-650', name: 'NVIDIA GTX 650', manufacturer: 'NVIDIA', model: 'GTX 650' },
  
  // 500 Series (2010)
  { id: 'nvidia-gtx-590', name: 'NVIDIA GTX 590', manufacturer: 'NVIDIA', model: 'GTX 590' },
  { id: 'nvidia-gtx-580', name: 'NVIDIA GTX 580', manufacturer: 'NVIDIA', model: 'GTX 580' },
  { id: 'nvidia-gtx-570', name: 'NVIDIA GTX 570', manufacturer: 'NVIDIA', model: 'GTX 570' },
  { id: 'nvidia-gtx-560-ti', name: 'NVIDIA GTX 560 Ti', manufacturer: 'NVIDIA', model: 'GTX 560 Ti' },
  { id: 'nvidia-gtx-560', name: 'NVIDIA GTX 560', manufacturer: 'NVIDIA', model: 'GTX 560' },
  { id: 'nvidia-gtx-550-ti', name: 'NVIDIA GTX 550 Ti', manufacturer: 'NVIDIA', model: 'GTX 550 Ti' },
  
  // 400 Series (2010)
  { id: 'nvidia-gtx-480', name: 'NVIDIA GTX 480', manufacturer: 'NVIDIA', model: 'GTX 480' },
  { id: 'nvidia-gtx-470', name: 'NVIDIA GTX 470', manufacturer: 'NVIDIA', model: 'GTX 470' },
  { id: 'nvidia-gtx-465', name: 'NVIDIA GTX 465', manufacturer: 'NVIDIA', model: 'GTX 465' },
  { id: 'nvidia-gtx-460', name: 'NVIDIA GTX 460', manufacturer: 'NVIDIA', model: 'GTX 460' },
  { id: 'nvidia-gts-450', name: 'NVIDIA GTS 450', manufacturer: 'NVIDIA', model: 'GTS 450' },
  
  // 200 Series (2008)
  { id: 'nvidia-gtx-295', name: 'NVIDIA GTX 295', manufacturer: 'NVIDIA', model: 'GTX 295' },
  { id: 'nvidia-gtx-285', name: 'NVIDIA GTX 285', manufacturer: 'NVIDIA', model: 'GTX 285' },
  { id: 'nvidia-gtx-280', name: 'NVIDIA GTX 280', manufacturer: 'NVIDIA', model: 'GTX 280' },
  { id: 'nvidia-gtx-260', name: 'NVIDIA GTX 260', manufacturer: 'NVIDIA', model: 'GTX 260' },
  
  // 9000 Series (2007)
  { id: 'nvidia-9800-gtx', name: 'NVIDIA 9800 GTX', manufacturer: 'NVIDIA', model: '9800 GTX' },
  { id: 'nvidia-9800-gt', name: 'NVIDIA 9800 GT', manufacturer: 'NVIDIA', model: '9800 GT' },
  { id: 'nvidia-9600-gt', name: 'NVIDIA 9600 GT', manufacturer: 'NVIDIA', model: '9600 GT' },
  { id: 'nvidia-9500-gt', name: 'NVIDIA 9500 GT', manufacturer: 'NVIDIA', model: '9500 GT' },
  
  // 8000 Series (2006)
  { id: 'nvidia-8800-ultra', name: 'NVIDIA 8800 Ultra', manufacturer: 'NVIDIA', model: '8800 Ultra' },
  { id: 'nvidia-8800-gtx', name: 'NVIDIA 8800 GTX', manufacturer: 'NVIDIA', model: '8800 GTX' },
  { id: 'nvidia-8800-gts', name: 'NVIDIA 8800 GTS', manufacturer: 'NVIDIA', model: '8800 GTS' },
  { id: 'nvidia-8600-gts', name: 'NVIDIA 8600 GTS', manufacturer: 'NVIDIA', model: '8600 GTS' },
  { id: 'nvidia-8500-gt', name: 'NVIDIA 8500 GT', manufacturer: 'NVIDIA', model: '8500 GT' },
  
  // 7000 Series (2005)
  { id: 'nvidia-7800-gtx', name: 'NVIDIA 7800 GTX', manufacturer: 'NVIDIA', model: '7800 GTX' },
  { id: 'nvidia-7600-gt', name: 'NVIDIA 7600 GT', manufacturer: 'NVIDIA', model: '7600 GT' },
  { id: 'nvidia-7300-gt', name: 'NVIDIA 7300 GT', manufacturer: 'NVIDIA', model: '7300 GT' },
  
  // 6000 Series (2004)
  { id: 'nvidia-6800-ultra', name: 'NVIDIA 6800 Ultra', manufacturer: 'NVIDIA', model: '6800 Ultra' },
  { id: 'nvidia-6800-gt', name: 'NVIDIA 6800 GT', manufacturer: 'NVIDIA', model: '6800 GT' },
  { id: 'nvidia-6600-gt', name: 'NVIDIA 6600 GT', manufacturer: 'NVIDIA', model: '6600 GT' },
  
  // FX Series (2003)
  { id: 'nvidia-fx-5950-ultra', name: 'NVIDIA FX 5950 Ultra', manufacturer: 'NVIDIA', model: 'FX 5950 Ultra' },
  { id: 'nvidia-fx-5900', name: 'NVIDIA FX 5900', manufacturer: 'NVIDIA', model: 'FX 5900' },
  { id: 'nvidia-fx-5700', name: 'NVIDIA FX 5700', manufacturer: 'NVIDIA', model: 'FX 5700' },
  { id: 'nvidia-fx-5200', name: 'NVIDIA FX 5200', manufacturer: 'NVIDIA', model: 'FX 5200' },
  
  // GeForce 4 Series (2002)
  { id: 'nvidia-ti-4600', name: 'NVIDIA GeForce4 Ti 4600', manufacturer: 'NVIDIA', model: 'GeForce4 Ti 4600' },
  { id: 'nvidia-ti-4200', name: 'NVIDIA GeForce4 Ti 4200', manufacturer: 'NVIDIA', model: 'GeForce4 Ti 4200' },
  { id: 'nvidia-mx-440', name: 'NVIDIA GeForce4 MX 440', manufacturer: 'NVIDIA', model: 'GeForce4 MX 440' },
  
  // GeForce 3 Series (2001)
  { id: 'nvidia-ti-500', name: 'NVIDIA GeForce3 Ti 500', manufacturer: 'NVIDIA', model: 'GeForce3 Ti 500' },
  { id: 'nvidia-ti-200', name: 'NVIDIA GeForce3 Ti 200', manufacturer: 'NVIDIA', model: 'GeForce3 Ti 200' },
  
  // GeForce 2 Series (2000)
  { id: 'nvidia-ultra', name: 'NVIDIA GeForce2 Ultra', manufacturer: 'NVIDIA', model: 'GeForce2 Ultra' },
  { id: 'nvidia-gts', name: 'NVIDIA GeForce2 GTS', manufacturer: 'NVIDIA', model: 'GeForce2 GTS' },
  { id: 'nvidia-mx', name: 'NVIDIA GeForce2 MX', manufacturer: 'NVIDIA', model: 'GeForce2 MX' },
  
  // ======== AMD ======== (Newest First)
  // RX 9000 Series (2025)
  { id: 'amd-rx-9070-xt', name: 'AMD Radeon RX 9070 XT', manufacturer: 'AMD', model: 'RX 9070 XT' },
  { id: 'amd-rx-9070', name: 'AMD Radeon RX 9070', manufacturer: 'AMD', model: 'RX 9070' },
  { id: 'amd-rx-9060-xt', name: 'AMD Radeon RX 9060 XT', manufacturer: 'AMD', model: 'RX 9060 XT' },
  
  // RX 7000 Series (2022-2023)
  { id: 'amd-rx-7900-xtx', name: 'AMD Radeon RX 7900 XTX', manufacturer: 'AMD', model: 'RX 7900 XTX' },
  { id: 'amd-rx-7900-xt', name: 'AMD Radeon RX 7900 XT', manufacturer: 'AMD', model: 'RX 7900 XT' },
  { id: 'amd-rx-7800-xt', name: 'AMD Radeon RX 7800 XT', manufacturer: 'AMD', model: 'RX 7800 XT' },
  { id: 'amd-rx-7700-xt', name: 'AMD Radeon RX 7700 XT', manufacturer: 'AMD', model: 'RX 7700 XT' },
  { id: 'amd-rx-7600-xt', name: 'AMD Radeon RX 7600 XT', manufacturer: 'AMD', model: 'RX 7600 XT' },
  { id: 'amd-rx-7600', name: 'AMD Radeon RX 7600', manufacturer: 'AMD', model: 'RX 7600' },
  
  // RX 6000 Series (2020-2021)
  { id: 'amd-rx-6950-xt', name: 'AMD Radeon RX 6950 XT', manufacturer: 'AMD', model: 'RX 6950 XT' },
  { id: 'amd-rx-6900-xt', name: 'AMD Radeon RX 6900 XT', manufacturer: 'AMD', model: 'RX 6900 XT' },
  { id: 'amd-rx-6800-xt', name: 'AMD Radeon RX 6800 XT', manufacturer: 'AMD', model: 'RX 6800 XT' },
  { id: 'amd-rx-6800', name: 'AMD Radeon RX 6800', manufacturer: 'AMD', model: 'RX 6800' },
  { id: 'amd-rx-6750-xt', name: 'AMD Radeon RX 6750 XT', manufacturer: 'AMD', model: 'RX 6750 XT' },
  { id: 'amd-rx-6700-xt', name: 'AMD Radeon RX 6700 XT', manufacturer: 'AMD', model: 'RX 6700 XT' },
  { id: 'amd-rx-6650-xt', name: 'AMD Radeon RX 6650 XT', manufacturer: 'AMD', model: 'RX 6650 XT' },
  { id: 'amd-rx-6600-xt', name: 'AMD Radeon RX 6600 XT', manufacturer: 'AMD', model: 'RX 6600 XT' },
  { id: 'amd-rx-6600', name: 'AMD Radeon RX 6600', manufacturer: 'AMD', model: 'RX 6600' },
  { id: 'amd-rx-6500-xt', name: 'AMD Radeon RX 6500 XT', manufacturer: 'AMD', model: 'RX 6500 XT' },
  
  // RX 5000 Series (2019)
  { id: 'amd-rx-5700-xt', name: 'AMD Radeon RX 5700 XT', manufacturer: 'AMD', model: 'RX 5700 XT' },
  { id: 'amd-rx-5700', name: 'AMD Radeon RX 5700', manufacturer: 'AMD', model: 'RX 5700' },
  { id: 'amd-rx-5600-xt', name: 'AMD Radeon RX 5600 XT', manufacturer: 'AMD', model: 'RX 5600 XT' },
  { id: 'amd-rx-5500-xt', name: 'AMD Radeon RX 5500 XT', manufacturer: 'AMD', model: 'RX 5500 XT' },
  
  // Vega Series (2017)
  { id: 'amd-vega-64', name: 'AMD Radeon Vega 64', manufacturer: 'AMD', model: 'Vega 64' },
  { id: 'amd-vega-56', name: 'AMD Radeon Vega 56', manufacturer: 'AMD', model: 'Vega 56' },
  
  // RX 500 Series (2017)
  { id: 'amd-rx-590', name: 'AMD Radeon RX 590', manufacturer: 'AMD', model: 'RX 590' },
  { id: 'amd-rx-580', name: 'AMD Radeon RX 580', manufacturer: 'AMD', model: 'RX 580' },
  { id: 'amd-rx-570', name: 'AMD Radeon RX 570', manufacturer: 'AMD', model: 'RX 570' },
  { id: 'amd-rx-560', name: 'AMD Radeon RX 560', manufacturer: 'AMD', model: 'RX 560' },
  { id: 'amd-rx-550', name: 'AMD Radeon RX 550', manufacturer: 'AMD', model: 'RX 550' },
  
  // RX 400 Series (2016)
  { id: 'amd-rx-480', name: 'AMD Radeon RX 480', manufacturer: 'AMD', model: 'RX 480' },
  { id: 'amd-rx-470', name: 'AMD Radeon RX 470', manufacturer: 'AMD', model: 'RX 470' },
  { id: 'amd-rx-460', name: 'AMD Radeon RX 460', manufacturer: 'AMD', model: 'RX 460' },
  
  // R9 300 Series (2015)
  { id: 'amd-r9-390x', name: 'AMD Radeon R9 390X', manufacturer: 'AMD', model: 'R9 390X' },
  { id: 'amd-r9-390', name: 'AMD Radeon R9 390', manufacturer: 'AMD', model: 'R9 390' },
  { id: 'amd-r9-380x', name: 'AMD Radeon R9 380X', manufacturer: 'AMD', model: 'R9 380X' },
  { id: 'amd-r9-380', name: 'AMD Radeon R9 380', manufacturer: 'AMD', model: 'R9 380' },
  { id: 'amd-r9-370', name: 'AMD Radeon R9 370', manufacturer: 'AMD', model: 'R9 370' },
  
  // R9 200 Series (2013)
  { id: 'amd-r9-290x', name: 'AMD Radeon R9 290X', manufacturer: 'AMD', model: 'R9 290X' },
  { id: 'amd-r9-290', name: 'AMD Radeon R9 290', manufacturer: 'AMD', model: 'R9 290' },
  { id: 'amd-r9-280x', name: 'AMD Radeon R9 280X', manufacturer: 'AMD', model: 'R9 280X' },
  { id: 'amd-r9-270x', name: 'AMD Radeon R9 270X', manufacturer: 'AMD', model: 'R9 270X' },
  
  // HD 7000 Series (2012)
  { id: 'amd-hd-7970', name: 'AMD Radeon HD 7970', manufacturer: 'AMD', model: 'HD 7970' },
  { id: 'amd-hd-7950', name: 'AMD Radeon HD 7950', manufacturer: 'AMD', model: 'HD 7950' },
  { id: 'amd-hd-7870', name: 'AMD Radeon HD 7870', manufacturer: 'AMD', model: 'HD 7870' },
  { id: 'amd-hd-7850', name: 'AMD Radeon HD 7850', manufacturer: 'AMD', model: 'HD 7850' },
  { id: 'amd-hd-7790', name: 'AMD Radeon HD 7790', manufacturer: 'AMD', model: 'HD 7790' },
  
  // HD 6000 Series (2010)
  { id: 'amd-hd-6970', name: 'AMD Radeon HD 6970', manufacturer: 'AMD', model: 'HD 6970' },
  { id: 'amd-hd-6950', name: 'AMD Radeon HD 6950', manufacturer: 'AMD', model: 'HD 6950' },
  { id: 'amd-hd-6870', name: 'AMD Radeon HD 6870', manufacturer: 'AMD', model: 'HD 6870' },
  { id: 'amd-hd-6850', name: 'AMD Radeon HD 6850', manufacturer: 'AMD', model: 'HD 6850' },
  { id: 'amd-hd-6790', name: 'AMD Radeon HD 6790', manufacturer: 'AMD', model: 'HD 6790' },
  
  // HD 5000 Series (2009)
  { id: 'amd-hd-5870', name: 'AMD Radeon HD 5870', manufacturer: 'AMD', model: 'HD 5870' },
  { id: 'amd-hd-5850', name: 'AMD Radeon HD 5850', manufacturer: 'AMD', model: 'HD 5850' },
  { id: 'amd-hd-5770', name: 'AMD Radeon HD 5770', manufacturer: 'AMD', model: 'HD 5770' },
  { id: 'amd-hd-5750', name: 'AMD Radeon HD 5750', manufacturer: 'AMD', model: 'HD 5750' },
  { id: 'amd-hd-5670', name: 'AMD Radeon HD 5670', manufacturer: 'AMD', model: 'HD 5670' },
  
  // HD 4000 Series (2008)
  { id: 'amd-hd-4890', name: 'AMD Radeon HD 4890', manufacturer: 'AMD', model: 'HD 4890' },
  { id: 'amd-hd-4870', name: 'AMD Radeon HD 4870', manufacturer: 'AMD', model: 'HD 4870' },
  { id: 'amd-hd-4850', name: 'AMD Radeon HD 4850', manufacturer: 'AMD', model: 'HD 4850' },
  { id: 'amd-hd-4830', name: 'AMD Radeon HD 4830', manufacturer: 'AMD', model: 'HD 4830' },
  
  // HD 3000 Series (2007)
  { id: 'amd-hd-3870', name: 'AMD Radeon HD 3870', manufacturer: 'AMD', model: 'HD 3870' },
  { id: 'amd-hd-3850', name: 'AMD Radeon HD 3850', manufacturer: 'AMD', model: 'HD 3850' },
  
  // HD 2000 Series (2007)
  { id: 'amd-hd-2900-xt', name: 'AMD Radeon HD 2900 XT', manufacturer: 'AMD', model: 'HD 2900 XT' },
  { id: 'amd-hd-2600-xt', name: 'AMD Radeon HD 2600 XT', manufacturer: 'AMD', model: 'HD 2600 XT' },
  { id: 'amd-hd-2400-xt', name: 'AMD Radeon HD 2400 XT', manufacturer: 'AMD', model: 'HD 2400 XT' },
  
  // X1000 Series (2005)
  { id: 'amd-x1950-xtx', name: 'AMD Radeon X1950 XTX', manufacturer: 'AMD', model: 'X1950 XTX' },
  { id: 'amd-x1900-xt', name: 'AMD Radeon X1900 XT', manufacturer: 'AMD', model: 'X1900 XT' },
  { id: 'amd-x1800-xt', name: 'AMD Radeon X1800 XT', manufacturer: 'AMD', model: 'X1800 XT' },
  { id: 'amd-x1600-xt', name: 'AMD Radeon X1600 XT', manufacturer: 'AMD', model: 'X1600 XT' },
  
  // X Series (2003)
  { id: 'amd-x850-xt', name: 'AMD Radeon X850 XT', manufacturer: 'AMD', model: 'X850 XT' },
  { id: 'amd-x800-xt', name: 'AMD Radeon X800 XT', manufacturer: 'AMD', model: 'X800 XT' },
  { id: 'amd-x700-pro', name: 'AMD Radeon X700 Pro', manufacturer: 'AMD', model: 'X700 Pro' },
  { id: 'amd-x600-pro', name: 'AMD Radeon X600 Pro', manufacturer: 'AMD', model: 'X600 Pro' },
  
  // 9000 Series (2002)
  { id: 'amd-9800-pro', name: 'AMD Radeon 9800 Pro', manufacturer: 'AMD', model: '9800 Pro' },
  { id: 'amd-9700-pro', name: 'AMD Radeon 9700 Pro', manufacturer: 'AMD', model: '9700 Pro' },
  { id: 'amd-9600-pro', name: 'AMD Radeon 9600 Pro', manufacturer: 'AMD', model: '9600 Pro' },
  { id: 'amd-9500-pro', name: 'AMD Radeon 9500 Pro', manufacturer: 'AMD', model: '9500 Pro' },
  
  // 8000 Series (2001)
  { id: 'amd-8500', name: 'AMD Radeon 8500', manufacturer: 'AMD', model: '8500' },
  { id: 'amd-7500', name: 'AMD Radeon 7500', manufacturer: 'AMD', model: '7500' },
  
  // 7000 Series (2000)
  { id: 'amd-7200', name: 'AMD Radeon 7200', manufacturer: 'AMD', model: '7200' },
  
  // ======== Intel ======== (Newest First)
  // Arc B-Series (2024)
  { id: 'intel-arc-b580', name: 'Intel Arc B580', manufacturer: 'Intel', model: 'Arc B580' },
  { id: 'intel-arc-b570', name: 'Intel Arc B570', manufacturer: 'Intel', model: 'Arc B570' },
  
  // Arc Series (2022)
  { id: 'intel-arc-a770', name: 'Intel Arc A770', manufacturer: 'Intel', model: 'Arc A770' },
  { id: 'intel-arc-a750', name: 'Intel Arc A750', manufacturer: 'Intel', model: 'Arc A750' },
  { id: 'intel-arc-a580', name: 'Intel Arc A580', manufacturer: 'Intel', model: 'Arc A580' },
  { id: 'intel-arc-a380', name: 'Intel Arc A380', manufacturer: 'Intel', model: 'Arc A380' },
  
  // DG1 Series (2021)
  { id: 'intel-dg1', name: 'Intel Iris Xe DG1', manufacturer: 'Intel', model: 'Iris Xe DG1' },
  
  // GMA Series (2004-2010)
  { id: 'intel-gma-x4500', name: 'Intel GMA X4500', manufacturer: 'Intel', model: 'GMA X4500' },
  { id: 'intel-gma-4500', name: 'Intel GMA 4500', manufacturer: 'Intel', model: 'GMA 4500' },
  { id: 'intel-gma-3100', name: 'Intel GMA 3100', manufacturer: 'Intel', model: 'GMA 3100' },
  { id: 'intel-gma-3000', name: 'Intel GMA 3000', manufacturer: 'Intel', model: 'GMA 3000' },
  { id: 'intel-gma-950', name: 'Intel GMA 950', manufacturer: 'Intel', model: 'GMA 950' },
  
  // Extreme Graphics (2002)
  { id: 'intel-extreme-graphics-2', name: 'Intel Extreme Graphics 2', manufacturer: 'Intel', model: 'Extreme Graphics 2' },
  
  // ======== Professional GPUs ========
  // NVIDIA Pro Series
  { id: 'nvidia-rtx-a6000', name: 'NVIDIA RTX A6000', manufacturer: 'NVIDIA', model: 'RTX A6000' },
  { id: 'nvidia-rtx-a5000', name: 'NVIDIA RTX A5000', manufacturer: 'NVIDIA', model: 'RTX A5000' },
  { id: 'nvidia-rtx-a4000', name: 'NVIDIA RTX A4000', manufacturer: 'NVIDIA', model: 'RTX A4000' },
  { id: 'nvidia-quadro-fx-5800', name: 'NVIDIA Quadro FX 5800', manufacturer: 'NVIDIA', model: 'Quadro FX 5800' },
  
  // AMD Pro Series
  { id: 'amd-radeon-pro-r9700', name: 'AMD Radeon AI PRO R9700', manufacturer: 'AMD', model: 'AI PRO R9700' },
  { id: 'amd-radeon-pro-w7900', name: 'AMD Radeon PRO W7900', manufacturer: 'AMD', model: 'PRO W7900' },
  { id: 'amd-radeon-pro-w6800', name: 'AMD Radeon PRO W6800', manufacturer: 'AMD', model: 'PRO W6800' },
  { id: 'amd-firepro-w9100', name: 'AMD FirePro W9100', manufacturer: 'AMD', model: 'FirePro W9100' },
  
  // Intel Pro Series
  { id: 'intel-arc-pro-a60', name: 'Intel Arc Pro A60', manufacturer: 'Intel', model: 'Arc Pro A60' },
];

// Search functions
export function searchGPUs(query: string): GPUSpec[] {
  if (!query.trim()) return [];

  const searchTerm = query.toLowerCase();
  return GPU_DATABASE.filter(gpu =>
    gpu.name.toLowerCase().includes(searchTerm) ||
    gpu.manufacturer.toLowerCase().includes(searchTerm) ||
    gpu.model.toLowerCase().includes(searchTerm)
  );
}

export function getGPUById(id: string): GPUSpec | undefined {
  return GPU_DATABASE.find(gpu => gpu.id === id);
}

export function getTopGPUs(count: number = 10): GPUSpec[] {
  return GPU_DATABASE.slice(0, count);
}

export function getGPUsByManufacturer(manufacturer: string): GPUSpec[] {
  return GPU_DATABASE.filter(gpu =>
    gpu.manufacturer.toLowerCase() === manufacturer.toLowerCase()
  );
}