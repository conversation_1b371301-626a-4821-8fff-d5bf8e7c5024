'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Shield, QrCode, Key, AlertTriangle, CheckCircle, Copy, RefreshCw } from 'lucide-react';
import { useAuthContext } from '@/hooks/use-auth-context';
// Tipos MFA (movidos para evitar importação do servidor)
interface MFASetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
  recoveryPhrase: string;
}

interface MFAStatus {
  isEnabled: boolean;
  isRequired: boolean;
  hasBackupCodes: boolean;
  lastVerified?: string;
  enrolledAt?: string;
}
import { toast } from 'sonner';

interface MFASetupProps {
  userId?: string;
  showTitle?: boolean;
  compact?: boolean;
}

export function MFASetup({ userId, showTitle = true, compact = false }: MFASetupProps) {
  const { user } = useAuthContext();
  const targetUserId = userId || user?.id;

  // Estados
  const [mfaStatus, setMfaStatus] = useState<MFAStatus | null>(null);
  const [setupData, setSetupData] = useState<MFASetupResult | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'status' | 'setup' | 'verify' | 'backup'>('status');
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [newBackupCodes, setNewBackupCodes] = useState<string[]>([]);

  // Carregar status do MFA
  useEffect(() => {
    if (targetUserId) {
      loadMFAStatus();
    }
  }, [targetUserId]);

  const loadMFAStatus = async () => {
    if (!targetUserId) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/admin/mfa', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (!response.ok) {
        throw new Error('Failed to load MFA status');
      }
      
      const { status } = await response.json();
      setMfaStatus(status);
    } catch (error) {
      console.error('Error loading MFA status:', error);
      toast.error('Error loading MFA status');
    } finally {
      setLoading(false);
    }
  };

  const startMFASetup = async () => {
    if (!targetUserId) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/admin/mfa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup', userId: targetUserId }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to setup MFA');
      }
      
      const { setupResult } = await response.json();
      setSetupData(setupResult);
      setStep('setup');
      toast.success('MFA setup initiated');
    } catch (error: any) {
      console.error('Error setting up MFA:', error);
      toast.error(error.message || 'Error setting up MFA');
    } finally {
      setLoading(false);
    }
  };

  const verifyMFACode = async () => {
    if (!targetUserId || !verificationCode.trim()) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/admin/mfa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'verify', 
          userId: targetUserId,
          token: verificationCode.trim(),
          operation: 'MFA_SETUP'
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to verify MFA');
      }
      
      const { result } = await response.json();
      
      if (result.valid) {
        setStep('backup');
        await loadMFAStatus();
        toast.success('MFA activated successfully!');
      } else {
        toast.error('Invalid code. Please check and try again.');
      }
    } catch (error: any) {
      console.error('Error verifying MFA:', error);
      toast.error(error.message || 'Error verifying code');
    } finally {
      setLoading(false);
    }
  };

  const regenerateBackupCodes = async () => {
    if (!targetUserId) return;
    
    try {
      setLoading(true);
      const response = await fetch('/api/admin/mfa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'regenerate-backup-codes', 
          userId: targetUserId 
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to regenerate backup codes');
      }
      
      const { backupCodes } = await response.json();
      setNewBackupCodes(backupCodes);
      setShowBackupCodes(true);
      toast.success('Backup codes regenerated');
    } catch (error: any) {
      console.error('Error regenerating backup codes:', error);
      toast.error(error.message || 'Error regenerating codes');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (error) {
      toast.error('Error copying');
    }
  };

  const copyBackupCodes = () => {
    const codes = setupData?.backupCodes || newBackupCodes;
    const text = codes.join('\n');
    copyToClipboard(text);
  };

  if (loading && !mfaStatus) {
    return (
      <Card className={`bg-slate-900/60 border-slate-700/50 rounded-lg backdrop-blur-sm ${compact ? 'p-4' : ''}`}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-violet-400" />
            <p className="text-slate-300">Loading MFA settings...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!mfaStatus) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Error loading MFA settings
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {showTitle && (
        <div className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-violet-400" />
          <h2 className="text-2xl font-bold font-mono">
            <span className="text-violet-400">&lt;</span>
            <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Multi-Factor Authentication</span>
            <span className="text-violet-400">/&gt;</span>
          </h2>
        </div>
      )}

      {/* Status Atual */}
      <Card className="bg-slate-900/60 border-slate-700/50 rounded-lg backdrop-blur-sm hover:border-violet-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 font-mono text-lg">
                <span className="text-violet-400">&lt;</span>
                <span className="text-white">MFA Status</span>
                <span className="text-violet-400">/&gt;</span>
                {mfaStatus.isEnabled ? (
                  <Badge variant="default" className="bg-green-500/20 text-green-400 border-green-500/30">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="bg-slate-700/50 text-slate-300 border-slate-600/50">
                    Inactive
                  </Badge>
                )}
              </CardTitle>
              <CardDescription className="text-slate-400 text-sm">
                {mfaStatus.isRequired 
                  ? 'MFA is required for this user' 
                  : 'MFA is recommended for enhanced security'
                }
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-sm">
              <p className="font-medium text-slate-300 font-mono uppercase tracking-wide text-xs mb-1">Status:</p>
              <p className={mfaStatus.isEnabled ? 'text-green-400' : 'text-orange-400'}>
                {mfaStatus.isEnabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-sm">
              <p className="font-medium text-slate-300 font-mono uppercase tracking-wide text-xs mb-1">Backup Codes:</p>
              <p className={mfaStatus.hasBackupCodes ? 'text-green-400' : 'text-orange-400'}>
                {mfaStatus.hasBackupCodes ? 'Available' : 'Not configured'}
              </p>
            </div>
            <div className="text-sm">
              <p className="font-medium text-slate-300 font-mono uppercase tracking-wide text-xs mb-1">Last Used:</p>
              <p className="text-slate-400">
                {mfaStatus.lastVerified 
                  ? new Date(mfaStatus.lastVerified).toLocaleDateString()
                  : 'Never used'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuração MFA */}
      {!mfaStatus.isEnabled && step === 'status' && (
        <Card className="bg-slate-900/60 border-slate-700/50 rounded-lg backdrop-blur-sm hover:border-violet-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10">
          <CardHeader>
            <CardTitle className="font-mono text-lg">
              <span className="text-violet-400">&lt;</span>
              <span className="text-white">Setup MFA</span>
              <span className="text-violet-400">/&gt;</span>
            </CardTitle>
            <CardDescription className="text-slate-400 text-sm">
              Configure multi-factor authentication to add an extra layer of security
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={startMFASetup} disabled={loading} className="w-full">
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Setup MFA
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Setup Flow */}
      {step === 'setup' && setupData && (
        <Card className="bg-slate-900/60 border-slate-700/50 rounded-lg backdrop-blur-sm hover:border-violet-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10">
          <CardHeader>
            <CardTitle className="font-mono text-lg">
              <span className="text-violet-400">&lt;</span>
              <span className="text-white">Setup Authenticator App</span>
              <span className="text-violet-400">/&gt;</span>
            </CardTitle>
            <CardDescription className="text-slate-400 text-sm">
              Scan the QR code with your authenticator app
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="qr" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="qr">QR Code</TabsTrigger>
                <TabsTrigger value="manual">Manual Setup</TabsTrigger>
              </TabsList>
              
              <TabsContent value="qr" className="space-y-4">
                <div className="flex justify-center">
                  <img 
                    src={setupData.qrCodeUrl} 
                    alt="QR Code for MFA" 
                    className="border rounded-lg"
                  />
                </div>
                <p className="text-sm text-gray-600 text-center">
                  Scan this code with Google Authenticator, Authy or similar
                </p>
              </TabsContent>
              
              <TabsContent value="manual" className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-300 font-mono uppercase tracking-wide">Secret Key:</label>
                  <div className="flex gap-2 mt-1">
                    <Input value={setupData.secret} readOnly className="font-mono bg-slate-800/50 border-slate-600/50 text-slate-200" />
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => copyToClipboard(setupData.secret)}
                      className="border-slate-600/50 hover:border-violet-500/50 hover:bg-violet-500/10"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-slate-400 space-y-1">
                  <p className="font-mono"><span className="text-violet-400">Account Name:</span> CriticalPixel Admin</p>
                  <p className="font-mono"><span className="text-violet-400">Type:</span> TOTP (Time-based)</p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="mt-6 space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-300 font-mono uppercase tracking-wide">Verification Code:</label>
                <Input
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  maxLength={6}
                  className="mt-1 bg-slate-800/50 border-slate-600/50 text-slate-200 font-mono text-center text-lg tracking-widest"
                />
              </div>
              <Button 
                onClick={verifyMFACode} 
                disabled={loading || verificationCode.length !== 6}
                className="w-full"
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  'Verify and Activate MFA'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Backup Codes */}
      {(step === 'backup' || (mfaStatus.isEnabled && setupData)) && (
        <Card className="bg-slate-900/60 border-slate-700/50 rounded-lg backdrop-blur-sm hover:border-violet-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-violet-500/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-mono text-lg">
              <Key className="h-5 w-5 text-violet-400" />
              <span className="text-violet-400">&lt;</span>
              <span className="text-white">Backup Codes</span>
              <span className="text-violet-400">/&gt;</span>
            </CardTitle>
            <CardDescription className="text-slate-400 text-sm">
              Save these codes in a secure location. Each code can only be used once.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> These codes allow access to your account if you lose 
                access to your authenticator app. Keep them in a secure location.
              </AlertDescription>
            </Alert>

            {setupData?.backupCodes && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-2 p-4 bg-slate-800/50 rounded-lg border border-slate-700/50">
                  {setupData.backupCodes.map((code, index) => (
                    <div key={index} className="font-mono text-sm p-2 bg-slate-900/50 rounded border border-slate-600/50 text-slate-200">
                      {code}
                    </div>
                  ))}
                </div>
                <Button variant="outline" onClick={copyBackupCodes} className="w-full">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Codes
                </Button>
              </div>
            )}

            <div className="flex gap-2 mt-4">
              <Button 
                variant="outline" 
                onClick={() => setStep('status')}
                className="flex-1"
              >
                Complete
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

    </div>
  );
} 