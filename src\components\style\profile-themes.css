/* ------------ Component-Focused Theme System ------------ */
/* This new theme system applies colors to specific components rather than backgrounds */

:root {
  /* Default theme (Cosmic) - matching the site's purple aesthetic */
  --theme-primary: #8b5cf6;
  --theme-secondary: #7c3aed;
  --theme-accent: #a78bfa;
  --theme-primary-rgb: 139, 92, 246;
  --theme-secondary-rgb: 124, 58, 237;
  --theme-accent-rgb: 167, 139, 250;
  
  /* Base colors - remain constant across themes */
  --base-background: #030712; /* gray-950 */
  --base-surface: rgba(17, 24, 39, 0.5); /* gray-900 with transparency */
  --base-border: rgba(31, 41, 55, 0.5); /* gray-800 */
  --base-text-primary: #f9fafb; /* gray-50 */
  --base-text-secondary: #d1d5db; /* gray-300 */
  --base-text-muted: #6b7280; /* gray-500 */
}

/* ------------ Theme: Cosmic (Default) ------------ */
.theme-cosmic,
.theme-default {
  --theme-primary: #8b5cf6;
  --theme-secondary: #7c3aed;
  --theme-accent: #a78bfa;
  --theme-primary-rgb: 139, 92, 246;
  --theme-secondary-rgb: 124, 58, 237;
  --theme-accent-rgb: 167, 139, 250;
}

/* ------------ Theme: Ocean ------------ */
.theme-ocean {
  --theme-primary: #3b82f6;
  --theme-secondary: #2563eb;
  --theme-accent: #60a5fa;
  --theme-primary-rgb: 59, 130, 246;
  --theme-secondary-rgb: 37, 99, 235;
  --theme-accent-rgb: 96, 165, 250;
}

/* ------------ Theme: Forest ------------ */
.theme-forest {
  --theme-primary: #10b981;
  --theme-secondary: #059669;
  --theme-accent: #34d399;
  --theme-primary-rgb: 16, 185, 129;
  --theme-secondary-rgb: 5, 150, 105;
  --theme-accent-rgb: 52, 211, 153;
}

/* ------------ Theme: Crimson ------------ */
.theme-crimson {
  --theme-primary: #ef4444;
  --theme-secondary: #dc2626;
  --theme-accent: #f87171;
  --theme-primary-rgb: 239, 68, 68;
  --theme-secondary-rgb: 220, 38, 38;
  --theme-accent-rgb: 248, 113, 113;
}

/* ------------ Theme: Silver ------------ */
.theme-silver {
  --theme-primary: #6b7280;
  --theme-secondary: #4b5563;
  --theme-accent: #9ca3af;
  --theme-primary-rgb: 107, 114, 128;
  --theme-secondary-rgb: 75, 85, 99;
  --theme-accent-rgb: 156, 163, 175;
}

/* ------------ Theme: Amber ------------ */
.theme-amber {
  --theme-primary: #f59e0b;
  --theme-secondary: #d97706;
  --theme-accent: #fbbf24;
  --theme-primary-rgb: 245, 158, 11;
  --theme-secondary-rgb: 217, 119, 6;
  --theme-accent-rgb: 251, 191, 36;
}

/* ------------ Component Theme Applications ------------ */

/* Avatar Ring */
.theme-avatar-ring {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 4px rgba(var(--theme-primary-rgb), 0.1);
}

/* Buttons with theme accent */
.theme-button-accent {
  border-color: rgba(var(--theme-primary-rgb), 0.3);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  color: var(--theme-accent);
}

.theme-button-accent:hover {
  border-color: rgba(var(--theme-primary-rgb), 0.5);
  background-color: rgba(var(--theme-primary-rgb), 0.2);
  box-shadow: 0 0 20px rgba(var(--theme-primary-rgb), 0.3);
}

/* Cards with subtle theme glow */
.theme-card-glow {
  box-shadow: 
    0 0 0 1px rgba(var(--theme-primary-rgb), 0.2),
    0 0 30px rgba(var(--theme-primary-rgb), 0.1);
}

.theme-card-glow:hover {
  box-shadow: 
    0 0 0 1px rgba(var(--theme-primary-rgb), 0.3),
    0 0 40px rgba(var(--theme-primary-rgb), 0.15);
}

/* Badges and tags */
.theme-badge {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  border: 1px solid rgba(var(--theme-primary-rgb), 0.3);
  color: var(--theme-accent);
}

.theme-badge:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.15);
  border-color: rgba(var(--theme-primary-rgb), 0.4);
}

/* Text accents */
.theme-text-accent {
  color: var(--theme-accent);
}

.theme-text-primary {
  color: var(--theme-primary);
}

.theme-text-secondary {
  color: var(--theme-secondary);
}

/* Icons with theme colors */
.theme-icon-accent {
  color: var(--theme-accent);
}

.theme-icon-primary {
  color: var(--theme-primary);
}

/* Borders with theme colors */
.theme-border-accent {
  border-color: rgba(var(--theme-primary-rgb), 0.3);
}

.theme-border-primary {
  border-color: var(--theme-primary);
}

/* Focus states */
.theme-focus:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.2);
}

/* Hover overlays */
.theme-hover-overlay {
  position: relative;
  overflow: hidden;
}

.theme-hover-overlay::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(var(--theme-primary-rgb), 0.1), 
    rgba(var(--theme-secondary-rgb), 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-hover-overlay:hover::before {
  opacity: 1;
}

/* Progress indicators */
.theme-progress {
  background: linear-gradient(90deg, 
    var(--theme-secondary), 
    var(--theme-primary), 
    var(--theme-accent));
  background-size: 200% 100%;
  animation: theme-gradient-shift 3s ease infinite;
}

@keyframes theme-gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Subtle animations */
.theme-pulse {
  animation: theme-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes theme-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--theme-primary-rgb), 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(var(--theme-primary-rgb), 0);
  }
}

/* Glow effects for active states */
.theme-active-glow {
  box-shadow: 
    0 0 20px rgba(var(--theme-primary-rgb), 0.4),
    inset 0 0 20px rgba(var(--theme-primary-rgb), 0.1);
}

/* Selection colors */
.theme-selection::selection {
  background-color: rgba(var(--theme-primary-rgb), 0.3);
  color: var(--base-text-primary);
}

/* Scrollbar theming */
.theme-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.theme-scrollbar::-webkit-scrollbar-track {
  background: rgba(var(--theme-primary-rgb), 0.05);
  border-radius: 4px;
}

.theme-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(var(--theme-primary-rgb), 0.3);
  border-radius: 4px;
}

.theme-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--theme-primary-rgb), 0.5);
}

/* Component-specific theme applications */

/* GamerCard themed elements */
.gamer-card-container.themed {
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}

.gamer-card-container.themed:hover {
  border-color: rgba(var(--theme-primary-rgb), 0.3);
  box-shadow: 0 0 30px rgba(var(--theme-primary-rgb), 0.15);
}

.gamer-card-expand-icon.themed {
  color: var(--theme-accent);
}

.gamer-card-terminal-icon.themed {
  color: var(--theme-primary);
  animation: theme-pulse 2s infinite;
}

.gamer-card-section-icon.themed {
  color: var(--theme-accent);
}

.gamer-card-genre-tag.themed {
  background: rgba(var(--theme-primary-rgb), 0.1);
  border-color: rgba(var(--theme-primary-rgb), 0.3);
}

.gamer-card-genre-tag.themed:hover {
  background: rgba(var(--theme-primary-rgb), 0.2);
  border-color: var(--theme-primary);
  color: var(--base-text-primary);
}

.gamer-card-console-icon.themed {
  border-color: rgba(var(--theme-secondary-rgb), 0.3);
}

.gamer-card-console-icon.themed:hover {
  border-color: var(--theme-secondary);
  background: rgba(var(--theme-secondary-rgb), 0.1);
  box-shadow: 0 0 15px rgba(var(--theme-secondary-rgb), 0.3);
}

/* Profile info themed elements */
.profile-stat-icon {
  color: var(--theme-accent);
}

.profile-edit-button {
  border-color: rgba(var(--theme-primary-rgb), 0.3);
}

.profile-edit-button:hover {
  border-color: rgba(var(--theme-primary-rgb), 0.5);
  background: rgba(var(--theme-primary-rgb), 0.1);
}

/* Theme transition effects */
* {
  transition: 
    border-color 0.3s ease,
    box-shadow 0.3s ease,
    background-color 0.3s ease,
    color 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-card-glow {
    box-shadow: 
      0 0 0 1px rgba(var(--theme-primary-rgb), 0.15),
      0 0 20px rgba(var(--theme-primary-rgb), 0.08);
  }
  
  .theme-button-accent {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .theme-card-glow {
    box-shadow: 
      0 0 0 1px rgba(var(--theme-primary-rgb), 0.25),
      0 0 40px rgba(var(--theme-primary-rgb), 0.12);
  }
}

/* Utility classes for quick theme applications */
.theme-glow-sm {
  box-shadow: 0 0 10px rgba(var(--theme-primary-rgb), 0.3);
}

.theme-glow-md {
  box-shadow: 0 0 20px rgba(var(--theme-primary-rgb), 0.3);
}

.theme-glow-lg {
  box-shadow: 0 0 30px rgba(var(--theme-primary-rgb), 0.3);
}

.theme-text-glow {
  text-shadow: 0 0 10px rgba(var(--theme-primary-rgb), 0.5);
}