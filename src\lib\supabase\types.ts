export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_sponsor_banners: {
        Row: {
          id: string
          user_id: string
          img_url: string
          url: string
          is_active: boolean
          impression_count: number
          click_count: number
          last_impression_at: string | null
          last_click_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          img_url: string
          url: string
          is_active?: boolean
          impression_count?: number
          click_count?: number
          last_impression_at?: string | null
          last_click_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          img_url?: string
          url?: string
          is_active?: boolean
          impression_count?: number
          click_count?: number
          last_impression_at?: string | null
          last_click_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_sponsor_banners_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      user_content_banners: {
        Row: {
          id: string
          user_id: string
          img_url: string
          url: string
          is_active: boolean
          impression_count: number
          click_count: number
          last_impression_at: string | null
          last_click_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          img_url: string
          url: string
          is_active?: boolean
          impression_count?: number
          click_count?: number
          last_impression_at?: string | null
          last_click_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          img_url?: string
          url?: string
          is_active?: boolean
          impression_count?: number
          click_count?: number
          last_impression_at?: string | null
          last_click_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_content_banners_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      },
      content_banner_analytics: {
        Row: {
          id: string
          banner_id: string
          event_type: string
          user_agent: string | null
          ip_address: string | null
          referrer: string | null
          created_at: string
        }
        Insert: {
          id?: string
          banner_id: string
          event_type: string
          user_agent?: string | null
          ip_address?: string | null
          referrer?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          banner_id?: string
          event_type?: string
          user_agent?: string | null
          ip_address?: string | null
          referrer?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_banner_analytics_banner_id_fkey"
            columns: ["banner_id"]
            referencedRelation: "user_content_banners"
            referencedColumns: ["id"]
          }
        ]
      },
      sponsor_banner_analytics: {
        Row: {
          id: string
          banner_id: string
          event_type: string
          user_agent: string | null
          ip_address: string | null
          referrer: string | null
          created_at: string
        }
        Insert: {
          id?: string
          banner_id: string
          event_type: string
          user_agent?: string | null
          ip_address?: string | null
          referrer?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          banner_id?: string
          event_type?: string
          user_agent?: string | null
          ip_address?: string | null
          referrer?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sponsor_banner_analytics_banner_id_fkey"
            columns: ["banner_id"]
            referencedRelation: "user_sponsor_banners"
            referencedColumns: ["id"]
          }
        ]
      },
      tags: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          category: string | null
          usage_count: number
          recent_usage_count: number
          view_count: number
          click_count: number
          search_count: number
          trend_score: number
          popularity_rank: number | null
          is_featured: boolean
          is_trending: boolean
          status: string
          created_by: string | null
          created_at: string
          updated_at: string
          last_used_at: string | null
          meta_title: string | null
          meta_description: string | null
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          category?: string | null
          usage_count?: number
          recent_usage_count?: number
          view_count?: number
          click_count?: number
          search_count?: number
          trend_score?: number
          popularity_rank?: number | null
          is_featured?: boolean
          is_trending?: boolean
          status?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
          last_used_at?: string | null
          meta_title?: string | null
          meta_description?: string | null
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          category?: string | null
          usage_count?: number
          recent_usage_count?: number
          view_count?: number
          click_count?: number
          search_count?: number
          trend_score?: number
          popularity_rank?: number | null
          is_featured?: boolean
          is_trending?: boolean
          status?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
          last_used_at?: string | null
          meta_title?: string | null
          meta_description?: string | null
        }
      }
      review_tags: {
        Row: {
          id: string
          review_id: string
          tag_id: string
          created_at: string
          clicks_from_tag: number
          conversions_from_tag: number
        }
        Insert: {
          id?: string
          review_id: string
          tag_id: string
          created_at?: string
          clicks_from_tag?: number
          conversions_from_tag?: number
        }
        Update: {
          id?: string
          review_id?: string
          tag_id?: string
          created_at?: string
          clicks_from_tag?: number
          conversions_from_tag?: number
        }
      }
      tag_analytics: {
        Row: {
          id: string
          tag_id: string
          date: string
          views: number
          clicks: number
          searches: number
          new_usages: number
          avg_time_on_page: number
          bounce_rate: number
          created_at: string
        }
        Insert: {
          id?: string
          tag_id: string
          date?: string
          views?: number
          clicks?: number
          searches?: number
          new_usages?: number
          avg_time_on_page?: number
          bounce_rate?: number
          created_at?: string
        }
        Update: {
          id?: string
          tag_id?: string
          date?: string
          views?: number
          clicks?: number
          searches?: number
          new_usages?: number
          avg_time_on_page?: number
          bounce_rate?: number
          created_at?: string
        }
      }
      profiles: {
        Row: {
          id: string
          username: string
          display_name: string | null
          slug: string
          slug_lower: string
          avatar_url: string | null
          banner_url: string | null
          bio: string | null
          preferred_genres: string[] | null
          favorite_consoles: string[] | null
          theme: string
          custom_colors: Json | null
          is_admin: boolean
          is_online: boolean
          last_seen: string | null
          level: number
          experience: number
          review_count: number
          privacy_settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username: string
          display_name?: string | null
          slug: string
          slug_lower: string
          avatar_url?: string | null
          banner_url?: string | null
          bio?: string | null
          preferred_genres?: string[] | null
          favorite_consoles?: string[] | null
          theme?: string
          custom_colors?: Json | null
          is_admin?: boolean
          is_online?: boolean
          last_seen?: string | null
          level?: number
          experience?: number
          review_count?: number
          privacy_settings?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string
          display_name?: string | null
          slug?: string
          slug_lower?: string
          avatar_url?: string | null
          banner_url?: string | null
          bio?: string | null
          preferred_genres?: string[] | null
          favorite_consoles?: string[] | null
          theme?: string
          custom_colors?: Json | null
          is_admin?: boolean
          is_online?: boolean
          last_seen?: string | null
          level?: number
          experience?: number
          review_count?: number
          privacy_settings?: Json
          created_at?: string
          updated_at?: string
        }
      }
      review_likes: {
        Row: {
          id: string
          review_id: string
          user_id: string
          created_at: string | null
        }
        Insert: {
          id?: string
          review_id: string
          user_id: string
          created_at?: string | null
        }
        Update: {
          id?: string
          review_id?: string
          user_id?: string
          created_at?: string | null
        }
      }
      review_analytics: {
        Row: {
          id: string
          review_id: string
          date: string
          views: number | null
          unique_views: number | null
          likes: number | null
          comments: number | null
          shares: number | null
          avg_reading_time: number | null
          bounce_rate: number | null
          created_at: string | null
        }
        Insert: {
          id?: string
          review_id: string
          date: string
          views?: number | null
          unique_views?: number | null
          likes?: number | null
          comments?: number | null
          shares?: number | null
          avg_reading_time?: number | null
          bounce_rate?: number | null
          created_at?: string | null
        }
        Update: {
          id?: string
          review_id?: string
          date?: string
          views?: number | null
          unique_views?: number | null
          likes?: number | null
          comments?: number | null
          shares?: number | null
          avg_reading_time?: number | null
          bounce_rate?: number | null
          created_at?: string | null
        }
      }
      hardware_configs: {
        Row: {
          id: string
          user_id: string
          name: string
          cpu_model: string | null
          gpu_model: string | null
          ram_amount: number | null
          storage_type: string | null
          is_active: boolean | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          cpu_model?: string | null
          gpu_model?: string | null
          ram_amount?: number | null
          storage_type?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          cpu_model?: string | null
          gpu_model?: string | null
          ram_amount?: number | null
          storage_type?: string | null
          is_active?: boolean | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      reviews: {
        Row: {
          id: string
          title: string
          slug: string
          game_id: string | null
          game_name: string
          author_id: string | null
          author_name: string
          author_slug: string | null
          content_lexical: Json
          overall_score: number
          scoring_criteria: Json
          platforms: string[] | null
          genres: string[] | null
          language: string | null
          played_on: string | null
          date_played: string | null
          tags: string[] | null
          main_image_url: string | null
          main_image_position: string | null
          gallery_image_urls: string[] | null
          video_url: string | null
          meta_title: string | null
          meta_description: string | null
          focus_keyword: string | null
          status: string | null
          featured_homepage: boolean | null
          publish_date: string | null
          monetization_blocks: Json | null
          created_at: string | null
          updated_at: string | null
          view_count: number | null
          like_count: number | null
          comment_count: number | null
          flag_count: number | null
          is_featured: boolean | null
          igdb_cover_url: string | null
          official_game_link: string | null
          is_private: boolean | null
        }
        Insert: {
          id?: string
          title: string
          slug: string
          game_id?: string | null
          game_name: string
          author_id?: string | null
          author_name: string
          author_slug?: string | null
          content_lexical: Json
          overall_score: number
          scoring_criteria: Json
          platforms?: string[] | null
          genres?: string[] | null
          language?: string | null
          played_on?: string | null
          date_played?: string | null
          tags?: string[] | null
          main_image_url?: string | null
          main_image_position?: string | null
          gallery_image_urls?: string[] | null
          video_url?: string | null
          meta_title?: string | null
          meta_description?: string | null
          focus_keyword?: string | null
          status?: string | null
          featured_homepage?: boolean | null
          publish_date?: string | null
          monetization_blocks?: Json | null
          created_at?: string | null
          updated_at?: string | null
          view_count?: number | null
          like_count?: number | null
          comment_count?: number | null
          flag_count?: number | null
          is_featured?: boolean | null
          igdb_cover_url?: string | null
          official_game_link?: string | null
          is_private?: boolean | null
        }
        Update: {
          id?: string
          title?: string
          slug?: string
          game_id?: string | null
          game_name?: string
          author_id?: string | null
          author_name?: string
          author_slug?: string | null
          content_lexical?: Json
          overall_score?: number
          scoring_criteria?: Json
          platforms?: string[] | null
          genres?: string[] | null
          language?: string | null
          played_on?: string | null
          date_played?: string | null
          tags?: string[] | null
          main_image_url?: string | null
          main_image_position?: string | null
          gallery_image_urls?: string[] | null
          video_url?: string | null
          meta_title?: string | null
          meta_description?: string | null
          focus_keyword?: string | null
          status?: string | null
          featured_homepage?: boolean | null
          publish_date?: string | null
          monetization_blocks?: Json | null
          created_at?: string | null
          updated_at?: string | null
          view_count?: number | null
          like_count?: number | null
          comment_count?: number | null
          flag_count?: number | null
          is_featured?: boolean | null
          igdb_cover_url?: string | null
          official_game_link?: string | null
          is_private?: boolean | null
        }
      }
      games: {
        Row: {
          id: string
          igdb_id: number | null
          name: string
          slug: string
          summary: string | null
          release_date: string | null
          cover_url: string | null
          aggregated_rating: number | null
          aggregated_rating_count: number | null
          developers: string[] | null
          publishers: string[] | null
          genres: string[] | null
          platforms: string[] | null
          game_engines: string[] | null
          player_perspectives: string[] | null
          time_to_beat_normally: number | null
          time_to_beat_completely: number | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          igdb_id?: number | null
          name: string
          slug: string
          summary?: string | null
          release_date?: string | null
          cover_url?: string | null
          aggregated_rating?: number | null
          aggregated_rating_count?: number | null
          developers?: string[] | null
          publishers?: string[] | null
          genres?: string[] | null
          platforms?: string[] | null
          game_engines?: string[] | null
          player_perspectives?: string[] | null
          time_to_beat_normally?: number | null
          time_to_beat_completely?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          igdb_id?: number | null
          name?: string
          slug?: string
          summary?: string | null
          release_date?: string | null
          cover_url?: string | null
          aggregated_rating?: number | null
          aggregated_rating_count?: number | null
          developers?: string[] | null
          publishers?: string[] | null
          genres?: string[] | null
          platforms?: string[] | null
          game_engines?: string[] | null
          player_perspectives?: string[] | null
          time_to_beat_normally?: number | null
          time_to_beat_completely?: number | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      content_flags: {
        Row: {
          id: string
          content_id: string
          content_type: 'review' | 'comment'
          reporter_id: string
          reason: string
          description: string | null
          status: 'pending' | 'resolved' | 'dismissed'
          resolved_by: string | null
          resolved_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          content_id: string
          content_type: 'review' | 'comment'
          reporter_id: string
          reason: string
          description?: string | null
          status?: 'pending' | 'resolved' | 'dismissed'
          resolved_by?: string | null
          resolved_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          content_id?: string
          content_type?: 'review' | 'comment'
          reporter_id?: string
          reason?: string
          description?: string | null
          status?: 'pending' | 'resolved' | 'dismissed'
          resolved_by?: string | null
          resolved_at?: string | null
          created_at?: string
        }
      }
      user_blocks: {
        Row: {
          id: string
          blocker_id: string
          blocked_id: string
          reason: string | null
          created_at: string
        }
        Insert: {
          id?: string
          blocker_id: string
          blocked_id: string
          reason?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          blocker_id?: string
          blocked_id?: string
          reason?: string | null
          created_at?: string
        }
      }
      comments: {
        Row: {
          id: string
          review_id: string
          author_id: string
          author_name: string
          content: string
          is_approved: boolean
          is_deleted: boolean
          flag_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          review_id: string
          author_id: string
          author_name: string
          content: string
          is_approved?: boolean
          is_deleted?: boolean
          flag_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          review_id?: string
          author_id?: string
          author_name?: string
          content?: string
          is_approved?: boolean
          is_deleted?: boolean
          flag_count?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      trending_tags: {
        Row: {
          id: string
          name: string
          slug: string
          category: string | null
          usage_count: number
          recent_usage_count: number
          trend_score: number
          popularity_rank: number | null
          is_trending: boolean
          is_featured: boolean
          review_count: number
          avg_review_score: number
        }
      }
      tag_suggestions: {
        Row: {
          category: string | null
          suggested_tags: string[]
        }
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
