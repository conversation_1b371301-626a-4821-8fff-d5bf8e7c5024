'use client';

import React, { useState, useEffect } from 'react';
import { getTotalReviewLines, formatLineCount, type LineCountStats } from '@/lib/services/lineCounterService';

interface LineCounterProps {
  className?: string;
}

const LineCounter: React.FC<LineCounterProps> = ({ className = "" }) => {
  const [lineStats, setLineStats] = useState<LineCountStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const fetchLineCount = async () => {
      try {
        const stats = await getTotalReviewLines();
        setLineStats(stats);
      } catch (error) {
        console.error('Error fetching line count:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLineCount();
  }, []);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-4 h-4 bg-slate-700 rounded animate-pulse"></div>
        <div className="w-20 h-3 bg-slate-700 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!lineStats) {
    return null;
  }

  return (
    <div 
      className={`group flex items-center space-x-2 transition-all duration-300 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Icon */}
      <div className="relative">
        <svg 
          className="w-4 h-4 text-violet-400/70 group-hover:text-violet-400 transition-colors duration-300" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
          />
        </svg>
        
        {/* Glow effect on hover */}
        <div className={`absolute inset-0 bg-violet-400/20 rounded-full blur-sm transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`} />
      </div>

      {/* Counter text */}
      <div className="flex flex-col">
        <div className="flex items-baseline space-x-1">
          <span className="text-xs text-slate-400 font-mono">
            <span className="text-violet-400/60">//</span> lines written:
          </span>
          <span className={`font-mono font-bold transition-all duration-300 ${
            isHovered 
              ? 'text-violet-400 scale-105' 
              : 'text-slate-300'
          }`}>
            {formatLineCount(lineStats.totalLines)}
          </span>
        </div>
        
        {/* Tooltip on hover */}
        <div className={`absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 transition-all duration-200 pointer-events-none ${
          isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
        }`}>
          <div className="bg-slate-900/95 backdrop-blur-sm text-white text-xs px-3 py-2 rounded-lg border border-white/10 whitespace-nowrap">
            <div className="font-semibold text-violet-400">
              {lineStats.totalLines.toLocaleString()} total lines
            </div>
            <div className="text-slate-400 text-[10px] mt-1">
              Written by our community across all reviews
            </div>
          </div>
          {/* Arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-900/95"></div>
        </div>
      </div>
    </div>
  );
};

export default LineCounter;
