// Server-only cover utility functions
// This file contains functions that require server-side modules and should not be imported on the client

import { 
  CoverSize, 
  getOptimalCoverUrl as serverGetOptimalCoverUrl, 
  getCoverUrlForSize as serverGetCoverUrlForSize,
  shouldRecacheCover,
  downloadAndCacheGameCover,
  getHighResIgdbUrl,
  generateCoverPath,
  COVER_SIZES
} from '@/lib/services/gameCoverService';

// Re-export server functions for use in API routes and server components
export {
  shouldRecacheCover,
  downloadAndCacheGameCover,
  getHighResIgdbUrl,
  generateCoverPath,
  COVER_SIZES
};

export type { CoverSize };

// Game interface for server utilities
export interface GameWithCoverServer {
  id?: string;
  igdb_id?: number | string;
  cover_url?: string;
  supabase_cover_url?: string;
  cover_cache_status?: string;
  igdb_cover_cached_at?: string;
}

/**
 * Server-side version of getBestCoverUrl with full Supabase client access
 */
export function getBestCoverUrlServer(
  game: GameWithCoverServer | null | undefined,
  size: CoverSize = 'big',
  includeDefault: boolean = true
): string {
  if (!game) {
    const defaultCovers = {
      thumb: '/images/default-game-thumb.svg',
      small: '/images/default-game-small.svg',
      big: '/images/default-game-big.svg'
    };
    return includeDefault ? defaultCovers[size] : '';
  }

  // Try to get cached cover first
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url) {
    return serverGetCoverUrlForSize(game, size, game.igdb_id?.toString());
  }

  // Fallback to IGDB cover
  if (game.cover_url) {
    return serverGetOptimalCoverUrl(game, size);
  }

  // Final fallback to default image
  const defaultCovers = {
    thumb: '/images/default-game-thumb.svg',
    small: '/images/default-game-small.svg',
    big: '/images/default-game-big.svg'
  };
  return includeDefault ? defaultCovers[size] : '';
}

/**
 * Server-side function to extract IGDB ID with enhanced logic
 */
export function extractIgdbIdServer(game: GameWithCoverServer | null | undefined): string | null {
  if (!game) return null;
  
  if (game.igdb_id) {
    return game.igdb_id.toString();
  }
  
  // Try to extract from IGDB cover URL
  if (game.cover_url) {
    const match = game.cover_url.match(/\/(\d+)\.jpg/);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Server-side function to validate cover processing requirements
 */
export function validateCoverProcessingServer(game: GameWithCoverServer): {
  canProcess: boolean;
  reason?: string;
  igdbId?: string;
} {
  if (!game.cover_url) {
    return {
      canProcess: false,
      reason: 'No cover URL available'
    };
  }

  const igdbId = extractIgdbIdServer(game);
  if (!igdbId) {
    return {
      canProcess: false,
      reason: 'Could not determine IGDB ID'
    };
  }

  if (game.cover_cache_status === 'processing') {
    return {
      canProcess: false,
      reason: 'Cover is already being processed'
    };
  }

  return {
    canProcess: true,
    igdbId
  };
}

/**
 * Server-side function to get cover processing priority
 */
export function getCoverProcessingPriorityServer(game: GameWithCoverServer): number {
  let priority = 1;
  
  // Higher priority for games with reviews
  if ((game as any).review_count > 0) {
    priority += 2;
  }
  
  // Higher priority for featured games
  if ((game as any).is_featured) {
    priority += 3;
  }
  
  // Higher priority for recently added games
  if ((game as any).created_at) {
    const createdAt = new Date((game as any).created_at);
    const daysSinceCreated = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 7) {
      priority += 1;
    }
  }
  
  return priority;
}

/**
 * Server-side function to batch process multiple covers
 */
export async function batchProcessCoversServer(
  games: GameWithCoverServer[],
  options: {
    maxConcurrent?: number;
    delayBetweenBatches?: number;
    onProgress?: (processed: number, total: number, current: GameWithCoverServer) => void;
  } = {}
): Promise<{
  successful: number;
  failed: number;
  results: Array<{
    game: GameWithCoverServer;
    success: boolean;
    error?: string;
  }>;
}> {
  const { maxConcurrent = 3, delayBetweenBatches = 1000, onProgress } = options;
  const results: Array<{ game: GameWithCoverServer; success: boolean; error?: string }> = [];
  let successful = 0;
  let failed = 0;

  // Filter games that can be processed
  const processableGames = games.filter(game => {
    const validation = validateCoverProcessingServer(game);
    if (!validation.canProcess) {
      results.push({
        game,
        success: false,
        error: validation.reason
      });
      failed++;
      return false;
    }
    return true;
  });

  // Sort by priority
  processableGames.sort((a, b) => 
    getCoverProcessingPriorityServer(b) - getCoverProcessingPriorityServer(a)
  );

  // Process in batches
  for (let i = 0; i < processableGames.length; i += maxConcurrent) {
    const batch = processableGames.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(async (game) => {
      try {
        const validation = validateCoverProcessingServer(game);
        if (!validation.canProcess || !validation.igdbId) {
          throw new Error(validation.reason || 'Validation failed');
        }

        const result = await downloadAndCacheGameCover(
          game.id!,
          validation.igdbId,
          game.cover_url!
        );

        if (result.success) {
          successful++;
          return { game, success: true };
        } else {
          failed++;
          return { game, success: false, error: result.error };
        }
      } catch (error) {
        failed++;
        return {
          game,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Report progress
    if (onProgress) {
      batchResults.forEach((result) => {
        onProgress(successful + failed, games.length, result.game);
      });
    }

    // Delay between batches to avoid overwhelming the system
    if (i + maxConcurrent < processableGames.length && delayBetweenBatches > 0) {
      await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
    }
  }

  return {
    successful,
    failed,
    results
  };
}

/**
 * Server-side function to cleanup orphaned cover files
 */
export async function cleanupOrphanedCoversServer(): Promise<{
  deletedFiles: number;
  errors: string[];
}> {
  // This would require implementing storage cleanup logic
  // For now, return a placeholder
  return {
    deletedFiles: 0,
    errors: ['Cleanup not yet implemented']
  };
}
