'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface ViewTrackingResponse {
  success: boolean;
  newView: boolean; // Whether this was a new unique view
  totalViews: number;
  error?: string;
}

/**
 * Track a review view with daily unique user limitation
 * Only counts one view per user per review per day
 */
export async function trackReviewView(
  reviewId: string,
  viewerIp?: string
): Promise<ViewTrackingResponse> {
  try {
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user if authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    // Create unique identifier for the viewer
    // For anonymous users, use IP or generate a random ID for testing
    let viewerIdentifier: string;
    if (user?.id) {
      viewerIdentifier = user.id;
    } else if (viewerIp && viewerIp !== '127.0.0.1' && viewerIp !== 'localhost') {
      viewerIdentifier = viewerIp;
    } else {
      // For localhost/development or when no IP is available
      // Use the passed IP or create a fallback identifier
      viewerIdentifier = viewerIp || `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Debug logging for tracking
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 View Tracking Debug:', {
        reviewId,
        user: user ? 'authenticated' : 'anonymous',
        userId: user?.id,
        viewerIp,
        viewerIdentifier,
        today
      });
    }

    // Check if this user/IP has already viewed this review today
    const { data: existingView, error: checkError } = await supabase
      .from('review_view_tracking')
      .select('id')
      .eq('review_id', reviewId)
      .eq('viewer_identifier', viewerIdentifier)
      .eq('view_date', today)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing view:', checkError);
      return { success: false, newView: false, totalViews: 0, error: 'Failed to check existing view' };
    }

    let newView = false;

    // If no view exists for today, record it
    if (!existingView) {
      const { error: insertError } = await supabase
        .from('review_view_tracking')
        .insert({
          review_id: reviewId,
          viewer_identifier: viewerIdentifier,
          viewer_user_id: user?.id || null,
          viewer_ip: viewerIp || null,
          view_date: today,
          is_authenticated: !!user,
          user_agent: null, // Could be added if needed
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error inserting view tracking:', insertError);
        return { success: false, newView: false, totalViews: 0, error: 'Failed to record view' };
      }

      newView = true;

      // Update the review's view count
      const { error: updateError } = await supabase
        .rpc('increment_review_view_count', { review_uuid: reviewId });

      if (updateError) {
        console.error('Error updating view count:', updateError);
        // Don't fail the whole operation if the increment fails
      }
    }

    // Get current total view count
    const { data: reviewData, error: reviewError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('id', reviewId)
      .single();

    const totalViews = reviewData?.view_count || 0;

    return {
      success: true,
      newView,
      totalViews,
    };

  } catch (error) {
    console.error('Error tracking review view:', error);
    return { 
      success: false, 
      newView: false, 
      totalViews: 0, 
      error: 'An unexpected error occurred' 
    };
  }
}

/**
 * Get view analytics for a review
 */
export async function getReviewViewAnalytics(reviewId: string): Promise<{
  success: boolean;
  data?: {
    totalViews: number;
    uniqueViews: number;
    authenticatedViews: number;
    anonymousViews: number;
    dailyViews: Array<{
      date: string;
      views: number;
      uniqueViews: number;
    }>;
  };
  error?: string;
}> {
  try {
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get total view count from reviews table
    const { data: reviewData, error: reviewError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('id', reviewId)
      .single();

    if (reviewError) {
      return { success: false, error: 'Review not found' };
    }

    // Get detailed view tracking data
    const { data: viewData, error: viewError } = await supabase
      .from('review_view_tracking')
      .select('*')
      .eq('review_id', reviewId)
      .order('view_date', { ascending: false });

    if (viewError) {
      console.error('Error fetching view analytics:', viewError);
      return { success: false, error: 'Failed to fetch view analytics' };
    }

    const totalViews = reviewData.view_count || 0;
    const uniqueViews = viewData?.length || 0;
    const authenticatedViews = viewData?.filter(v => v.is_authenticated).length || 0;
    const anonymousViews = uniqueViews - authenticatedViews;

    // Group by date for daily analytics
    const dailyViewsMap = new Map<string, number>();
    viewData?.forEach(view => {
      const date = view.view_date;
      dailyViewsMap.set(date, (dailyViewsMap.get(date) || 0) + 1);
    });

    const dailyViews = Array.from(dailyViewsMap.entries())
      .map(([date, views]) => ({
        date,
        views,
        uniqueViews: views // In this case, each tracking record is unique
      }))
      .sort((a, b) => b.date.localeCompare(a.date))
      .slice(0, 30); // Last 30 days

    return {
      success: true,
      data: {
        totalViews,
        uniqueViews,
        authenticatedViews,
        anonymousViews,
        dailyViews
      }
    };

  } catch (error) {
    console.error('Error getting review analytics:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Clean up old view tracking data (run periodically)
 * Keeps last 90 days of data
 */
export async function cleanupOldViewData(): Promise<{
  success: boolean;
  deletedCount?: number;
  error?: string;
}> {
  try {
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90);
    const cutoffDateString = cutoffDate.toISOString().split('T')[0];

    const { error, count } = await supabase
      .from('review_view_tracking')
      .delete()
      .lt('view_date', cutoffDateString);

    if (error) {
      console.error('Error cleaning up old view data:', error);
      return { success: false, error: 'Failed to cleanup old data' };
    }

    return {
      success: true,
      deletedCount: count || 0
    };

  } catch (error) {
    console.error('Error during cleanup:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}