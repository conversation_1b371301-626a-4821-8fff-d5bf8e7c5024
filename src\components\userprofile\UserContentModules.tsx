'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Star, 
  Trophy, 
  TrendingUp, 
  Eye, 
  MessageSquare, 
  Heart, 
  Gamepad2,
  Calendar,
  Clock,
  Target,
  Zap,
  Award,
  ChevronRight,
  Filter,
  Grid,
  List,
  Play,
  Share2,
  Bookmark,
  MoreHorizontal,
  Search,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUserContent } from '@/hooks/useUserContent';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Tipos para os dados de conteúdo
interface UserReview {
  id: string;
  game_name: string;
  game_image?: string;
  igdb_cover_url?: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
}

interface UserSurvey {
  id: string;
  game_name: string;
  game_image?: string;
  performance_score: number;
  fps_average: number;
  resolution: string;
  graphics_settings: string;
  created_at: string;
  hardware_used: string;
  notes?: string;
  is_public: boolean;
}

interface UserActivity {
  id: string;
  type: 'review' | 'survey' | 'achievement' | 'milestone' | 'comment' | 'like' | 'follow';
  title: string;
  description: string;
  created_at: string;
  metadata?: Record<string, any>;
}

interface UserContentModulesProps {
  userId: string;
  currentUserId?: string;
  isOwnProfile?: boolean;
  theme?: any;
}

// Componente para exibir estatísticas gamificadas
const UserStats = ({ stats, theme }: { stats: any; theme: any }) => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
    {[
      { label: 'Surveys', value: stats.surveysCount, icon: Target, color: 'text-blue-400' },
      { label: 'Activities', value: stats.activitiesCount, icon: TrendingUp, color: 'text-purple-400' },
      { label: 'Total Views', value: stats.totalViews, icon: Eye, color: 'text-green-400' },
      { label: 'Likes', value: stats.totalLikes, icon: Heart, color: 'text-red-400' },
    ].map((stat) => (
      <motion.div
        key={stat.label}
        whileHover={{ scale: 1.02 }}
        className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50 backdrop-blur-sm"
      >
        <div className="flex items-center gap-3">
          <div 
            className="p-2 rounded-lg"
            style={{ backgroundColor: `${theme?.colors?.primary}20` }}
          >
            <stat.icon className={cn("h-4 w-4", stat.color)} />
          </div>
          <div>
            <p className="text-2xl font-bold text-white">{stat.value}</p>
            <p className="text-xs text-gray-400">{stat.label}</p>
          </div>
        </div>
      </motion.div>
    ))}
  </div>
);

// Componente para exibir reviews em grid moderno
const ReviewsGrid = ({ reviews, theme }: { reviews: UserReview[]; theme: any }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    {reviews.map((review) => (
      <motion.div
        key={review.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5 }}
        className="group"
      >
        <Card className="bg-gray-900/50 border-gray-800/50 backdrop-blur-sm overflow-hidden h-full">
          <CardContent className="p-0">
            {/* Header com imagem do jogo */}
            <div className="relative">
              {review.game_image ? (
                <img 
                  src={review.game_image} 
                  alt={review.game_name}
                  className="w-full h-32 object-cover"
                />
              ) : (
                <div 
                  className="w-full h-32 flex items-center justify-center"
                  style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                >
                  <Gamepad2 className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="absolute top-2 right-2 flex gap-2">
                {review.is_featured && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    <Trophy className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                <Badge variant="secondary" className="bg-gray-900/80">
                  {review.rating}/5 ⭐
                </Badge>
              </div>
            </div>

            {/* Conteúdo */}
            <div className="p-4 space-y-3">
              <div>
                <h3 className="font-semibold text-white truncate">{review.game_name}</h3>
                {review.platform && (
                  <Badge variant="outline" className="text-xs mt-1">
                    {review.platform}
                  </Badge>
                )}
              </div>

              <p className="text-sm text-gray-300 line-clamp-3">
                {review.review_text}
              </p>

              {/* Tags */}
              {review.tags && review.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {review.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Estatísticas */}
              <div className="flex items-center justify-between text-xs text-gray-400">
                <div className="flex items-center gap-3">
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {review.views_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {review.likes_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    {review.comments_count}
                  </span>
                </div>
                <span>{new Date(review.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    ))}
  </div>
);

// Componente para exibir surveys de performance
const SurveysGrid = ({ surveys, theme }: { surveys: UserSurvey[]; theme: any }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {surveys.map((survey) => (
      <motion.div
        key={survey.id}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        className="group"
      >
        <Card className="bg-gray-900/50 border-gray-800/50 backdrop-blur-sm overflow-hidden">
          <CardContent className="p-4 space-y-3">
            {/* Header */}
            <div className="flex items-center gap-3">
              {survey.game_image ? (
                <img 
                  src={survey.game_image} 
                  alt={survey.game_name}
                  className="w-10 h-10 rounded object-cover"
                />
              ) : (
                <div 
                  className="w-10 h-10 rounded flex items-center justify-center"
                  style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                >
                  <Target className="h-5 w-5 text-gray-400" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-white truncate">{survey.game_name}</h3>
                <p className="text-xs text-gray-400">{survey.resolution}</p>
              </div>
            </div>

            {/* Score de Performance */}
            <div className="relative">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-300">Performance Score</span>
                <span className="text-lg font-bold text-white">{survey.performance_score}%</span>
              </div>
              <div className="w-full bg-gray-800 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${survey.performance_score}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                  className="h-2 rounded-full"
                  style={{ backgroundColor: theme?.colors?.primary }}
                />
              </div>
            </div>

            {/* Métricas */}
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="bg-gray-800/50 rounded p-2">
                <div className="text-gray-400">FPS Médio</div>
                <div className="text-white font-semibold">{survey.fps_average}</div>
              </div>
              <div className="bg-gray-800/50 rounded p-2">
                <div className="text-gray-400">Gráficos</div>
                <div className="text-white font-semibold">{survey.graphics_settings}</div>
              </div>
            </div>

            {/* Hardware */}
            <div className="text-xs text-gray-400">
              <Zap className="h-3 w-3 inline mr-1" />
              {survey.hardware_used}
            </div>

            {/* Data */}
            <div className="text-xs text-gray-500">
              {new Date(survey.created_at).toLocaleDateString()}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    ))}
  </div>
);

// Componente para atividades recentes
const RecentActivity = ({ activities, theme }: { activities: UserActivity[]; theme: any }) => (
  <div className="space-y-3">
    {activities.map((activity) => (
      <motion.div
        key={activity.id}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex items-center gap-3 p-3 bg-gray-900/30 rounded-lg border border-gray-800/30"
      >
        <div 
          className="p-2 rounded-full"
          style={{ backgroundColor: `${theme?.colors?.primary}20` }}
        >
          {activity.type === 'review' && <Star className="h-4 w-4 text-yellow-400" />}
          {activity.type === 'survey' && <Target className="h-4 w-4 text-blue-400" />}
          {activity.type === 'achievement' && <Trophy className="h-4 w-4 text-green-400" />}
          {activity.type === 'milestone' && <Award className="h-4 w-4 text-purple-400" />}
        </div>
        <div className="flex-1">
          <p className="text-sm text-white font-medium">{activity.title}</p>
          <p className="text-xs text-gray-400">{activity.description}</p>
        </div>
        <span className="text-xs text-gray-500">
          {new Date(activity.created_at).toLocaleDateString()}
        </span>
      </motion.div>
    ))}
  </div>
);

// Componente principal dos módulos de conteúdo
export default function UserContentModules({
  userId,
  currentUserId,
  isOwnProfile = false,
  theme
}: UserContentModulesProps) {
  // ALL HOOKS MUST BE AT THE TOP - DO NOT MOVE BELOW
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'rating' | 'views'>('date');

  // Use the real useUserContent hook (without YouTube channel URL)
  const {
    data,
    isLoading,
    error,
    stats
  } = useUserContent(userId, currentUserId);

  // Filter and sort content based on search and sort criteria - HOOKS MUST BE BEFORE CONDITIONAL RETURNS
  const filteredReviews = useMemo(() => {
    if (!data?.reviews) return [];
    
    let filtered = data.reviews.filter(review => 
      !searchTerm || 
      review.game_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.review_text.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'views':
          return b.views_count - a.views_count;
        case 'date':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [data?.reviews, searchTerm, sortBy]);



  const filteredActivities = useMemo(() => {
    if (!data?.activities) return [];
    
    let filtered = data.activities.filter(activity => 
      !searchTerm || 
      activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filtered.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [data?.activities, searchTerm]);

  // Prepare stats for display
  const displayStats = useMemo(() => ({
    activitiesCount: filteredActivities.length,
    totalViews: stats?.totalEngagement || 0,
    totalLikes: stats?.totalContent || 0
  }), [filteredActivities.length, stats?.totalEngagement, stats?.totalContent]);

  // Handle loading and error states AFTER all hooks
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 text-red-400">
          <p>Error loading content: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Título da Seção */}
      <div className="flex items-center gap-3">
        <div 
          className="p-2 rounded-lg"
          style={{ backgroundColor: `${theme?.colors?.primary}20` }}
        >
          <Trophy className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-white font-mono">
            <span style={{ color: theme?.colors?.accent }}>&lt;</span>
            {isOwnProfile ? 'Meu Conteúdo' : 'Conteúdo do Usuário'}
            <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
          </h2>
          <p className="text-sm text-gray-400">
            Atividades recentes do usuário
          </p>
        </div>
      </div>

      {/* Estatísticas Gamificadas */}
      <UserStats stats={displayStats} theme={theme} />

      {/* Search and Filter Controls */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
            />
          </div>
          <Select value={sortBy} onValueChange={(value: 'date' | 'rating' | 'views') => setSortBy(value)}>
            <SelectTrigger className="w-full sm:w-48 bg-gray-800/50 border-gray-700 text-white">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Latest</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
              <SelectItem value="views">Views</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Header for Activities */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-purple-400" />
          <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
        </div>
      </div>

      {/* Activities Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {filteredActivities.length > 0 ? (
          <RecentActivity activities={filteredActivities} theme={theme} />
        ) : (
          <div className="text-center py-12 text-gray-400">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{searchTerm ? 'No activities match your search' : 'Nenhuma atividade recente'}</p>
            {searchTerm && (
              <button
                type="button"
                onClick={() => setSearchTerm('')}
                className="mt-2 text-purple-400 hover:text-purple-300 text-sm"
              >
                Clear search
              </button>
            )}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}