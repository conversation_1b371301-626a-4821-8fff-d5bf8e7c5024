'use client';

import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { X, Monitor, Cpu, HardDrive, Zap, Target, Gauge, CheckCircle, ArrowRight, ArrowLeft, Search, Clock, Laptop, Gamepad2, Settings } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { HardwareSearchInput } from '@/components/ui/HardwareSearchInput';
import { CPUSpec } from '@/lib/cpu-data';
import { GPUSpec } from '@/lib/gpu-data';
import { HANDHELD_DATABASE } from '@/lib/handheld-data';
import { NOTEBOOK_DATABASE } from '@/lib/notebook-data';
import type { UserProfile } from '@/lib/types';
import './style/performanceSurvey.css';

interface PerformanceSurveyProps {
  isOpen: boolean;
  onClose: (wasSkipped?: boolean) => void;
  onSubmit: (data: PerformanceSurveyData) => void;
  platform: string;
  user: UserProfile | null;
  gameTitle?: string;
}

export interface PerformanceSurveyData {
  deviceType: string;
  laptopModel?: string;
  handheldModel?: string;
  cpu: string;
  gpu: string;
  totalMemory: string;
  memoryGen: string;
  memorySpeed: string;
  graphicsPreset: string;
  frameGen: string;
  frameGenType?: string;
  upscale: string;
  upscaleType?: string;
  upscalePreset?: string;
  resolution: string;
  ultrawide: string;
  fpsAverage: string;
  smoothness: string;
}

const PerformanceSurvey: React.FC<PerformanceSurveyProps> = ({
  isOpen,
  onClose,
  onSubmit,
  platform,
  user,
  gameTitle
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showLaterInfo, setShowLaterInfo] = useState(false);

  // Step interaction tracking - tracks when user first meaningfully interacts with each step
  const [stepInteractions, setStepInteractions] = useState<boolean[]>(new Array(14).fill(false));

  // Track highest step reached to allow navigation back to previously visited steps
  const [highestStepReached, setHighestStepReached] = useState(1);
  const [formData, setFormData] = useState<PerformanceSurveyData>({
    deviceType: '',
    laptopModel: '',
    handheldModel: '',
    cpu: '',
    gpu: '',
    totalMemory: '',
    memoryGen: '',
    memorySpeed: '',
    graphicsPreset: '',
    frameGen: '',
    frameGenType: '',
    upscale: '',
    upscaleType: '',
    upscalePreset: '',
    resolution: '',
    ultrawide: '',
    fpsAverage: '',
    smoothness: ''
  });

  const totalSteps = 14; // Updated to account for thank you step as final step

  // Track user interactions with steps
  const markStepInteraction = useCallback((step: number) => {
    setStepInteractions(prev => {
      const newInteractions = [...prev];
      newInteractions[step - 1] = true; // Convert to 0-based index
      return newInteractions;
    });
  }, []);

  // Update highest step reached
  const updateHighestStep = useCallback((step: number) => {
    setHighestStepReached(prev => Math.max(prev, step));
  }, []);

  // Step completion tracking - determines if a step has all required data
  const getStepCompletion = useCallback(() => {
    return [
      true, // Step 1: Welcome (always completed when reached)
      !!formData.deviceType, // Step 2: Device type selected
      formData.deviceType === 'desktop' ? !!formData.cpu : true, // Step 3: CPU (desktop only)
      formData.deviceType === 'desktop' ? !!formData.gpu : true, // Step 4: GPU (desktop only)
      formData.deviceType === 'desktop' ? !!formData.totalMemory : true, // Step 5: Memory (desktop only)
      formData.deviceType === 'desktop' ? !!(formData.memoryGen && formData.memorySpeed) : true, // Step 6: Memory details (desktop only)
      !!formData.frameGen, // Step 7: Frame generation
      !!formData.upscale, // Step 8: Upscaling
      !!(formData.resolution && formData.ultrawide), // Step 9: Display setup
      !!(formData.fpsAverage && formData.smoothness), // Step 10: Performance results
      !!formData.laptopModel, // Step 11: Laptop model (laptop only)
      !!formData.handheldModel, // Step 12: Handheld model (handheld only)
      !!formData.graphicsPreset, // Step 13: Graphics preset (all device types)
      true // Step 14: Thank you (always completed when reached)
    ];
  }, [formData]);

  const updateField = useCallback((field: keyof PerformanceSurveyData, value: string) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Reset dependent fields in the same update
      if (field === 'memoryGen') {
        newData.memorySpeed = '';
      }
      if (field === 'upscale' && value === 'no') {
        newData.upscaleType = '';
        newData.upscalePreset = '';
      }
      if (field === 'frameGen' && value === 'no') {
        newData.frameGenType = '';
      }
      if (field === 'gpu') {
        // Clear DLSS selections if switching to AMD GPU
        const isAmdGpu = value && value.toLowerCase().includes('amd');
        if (isAmdGpu) {
          if (newData.frameGenType === 'dlss 3') {
            newData.frameGenType = '';
          }
          if (newData.upscaleType === 'dlss') {
            newData.upscaleType = '';
            newData.upscalePreset = '';
          }
        }
      }
      if (field === 'deviceType') {
        // Reset model fields when device type changes
        if (value !== 'laptop') {
          newData.laptopModel = '';
        }
        if (value !== 'handheld') {
          newData.handheldModel = '';
        }
        // Reset CPU/GPU for laptop and handheld
        if (value === 'laptop' || value === 'handheld') {
          newData.cpu = '';
          newData.gpu = '';
        }
        // Auto-select display native for handheld devices
        if (value === 'handheld') {
          newData.ultrawide = 'display-native';
        } else {
          // Reset ultrawide selection when switching away from handheld
          newData.ultrawide = '';
        }
      }

      return newData;
    });
  }, []);

  // Create stable callback functions for input fields
  const handleCpuChange = useCallback((value: string) => updateField('cpu', value), [updateField]);
  const handleGpuChange = useCallback((value: string) => updateField('gpu', value), [updateField]);
  const handleLaptopModelChange = useCallback((value: string) => updateField('laptopModel', value), [updateField]);
  const handleHandheldModelChange = useCallback((value: string) => updateField('handheldModel', value), [updateField]);

  const handleNext = useCallback(() => {
    // Mark current step as interacted with
    markStepInteraction(currentStep);

    setCurrentStep(prev => {
      const nextStep = prev + 1;

      // Handle flow logic based on device type
      if (prev === 2 && formData.deviceType) { // From device type step
        let targetStep;
        if (formData.deviceType === 'desktop') {
          targetStep = 3; // Go to CPU step
        } else if (formData.deviceType === 'laptop') {
          targetStep = 11; // Go to laptop model step
        } else if (formData.deviceType === 'handheld') {
          targetStep = 12; // Go to handheld model step
        } else {
          targetStep = nextStep;
        }
        updateHighestStep(targetStep);
        markStepInteraction(targetStep);
        return targetStep;
      }

      // From memory speed step (desktop), go to graphics preset
      if (prev === 6) {
        const targetStep = 13; // Go to graphics preset step
        updateHighestStep(targetStep);
        markStepInteraction(targetStep);
        return targetStep;
      }

      // From laptop model step, go to graphics preset
      if (prev === 11) {
        const targetStep = 13; // Go to graphics preset step
        updateHighestStep(targetStep);
        markStepInteraction(targetStep);
        return targetStep;
      }

      // From handheld model step, go to graphics preset
      if (prev === 12) {
        const targetStep = 13; // Go to graphics preset step
        updateHighestStep(targetStep);
        markStepInteraction(targetStep);
        return targetStep;
      }

      // From graphics preset step, go to frame generation
      if (prev === 13) {
        const targetStep = 7; // Go to frame generation step
        updateHighestStep(targetStep);
        markStepInteraction(targetStep);
        return targetStep;
      }

      // Regular progression
      updateHighestStep(nextStep);
      markStepInteraction(nextStep);
      return nextStep;
    });
  }, [formData.deviceType, currentStep, markStepInteraction, updateHighestStep]);

  const handleBack = useCallback(() => {
    setCurrentStep(prev => {
      // Handle flow logic for going back
      if (prev === 7) {
        // From frame generation back to graphics preset
        return 13;
      }
      if (prev === 13 && formData.deviceType === 'desktop') {
        // From graphics preset back to memory speed for desktop
        return 6;
      }
      if (prev === 13 && formData.deviceType === 'laptop') {
        // From graphics preset back to laptop model
        return 11;
      }
      if (prev === 13 && formData.deviceType === 'handheld') {
        // From graphics preset back to handheld model
        return 12;
      }
      if (prev === 3 && formData.deviceType === 'desktop') {
        // From CPU back to device type
        return 2;
      }
      if (prev === 11 || prev === 12) {
        // From model selection back to device type
        return 2;
      }
      
      return prev - 1;
    });
  }, [formData.deviceType]);

  const handleSkip = useCallback((field: keyof PerformanceSurveyData, value: string) => {
    updateField(field, value);
    markStepInteraction(currentStep); // Mark current step as interacted
    handleNext();
  }, [updateField, handleNext, currentStep, markStepInteraction]);

  // Hardware selection handlers
  const handleCpuSelect = useCallback((hardware: CPUSpec | GPUSpec) => {
    const cpu = hardware as CPUSpec;
    updateField('cpu', cpu.name);
    // Manual navigation - user must click Next button
  }, [updateField]);

  const handleGpuSelect = useCallback((hardware: CPUSpec | GPUSpec) => {
    const gpu = hardware as GPUSpec;
    updateField('gpu', gpu.name);
    // Manual navigation - user must click Next button
  }, [updateField]);

  const handleSubmit = useCallback(() => {
    // Don't call onSubmit yet - wait until user closes the survey
    setCurrentStep(14); // Go to thank you step (step 14)
  }, [formData]);

  const handleCancel = useCallback(() => {
    if (currentStep > 1) {
      setShowCancelConfirm(true);
    } else {
      onClose(true); // User is skipping the survey
    }
  }, [currentStep, onClose]);

  const handleMaybeLater = useCallback(() => {
    setShowLaterInfo(true);
  }, []);

  const confirmCancel = useCallback(() => {
    onClose(true); // User is skipping the survey
  }, [onClose]);

  const getMemorySpeedOptions = useMemo(() => {
    switch (formData.memoryGen) {
      case 'ddr3':
        return [
          { value: '1066', label: '1066 MT/s', category: 'Entry Level' },
          { value: '1333', label: '1333 MT/s', category: 'Budget' },
          { value: '1600', label: '1600 MT/s', category: 'Average' },
          { value: '2133+', label: '2133+ MT/s', category: 'Enthusiast' }
        ];
      case 'ddr4':
        return [
          { value: '2133', label: '2133 MT/s', category: 'Entry Level' },
          { value: '2666', label: '2666 MT/s', category: 'Budget' },
          { value: '3600', label: '3600 MT/s', category: 'Average' },
          { value: '4000+', label: '4000+ MT/s', category: 'Enthusiast' }
        ];
      case 'ddr5':
        return [
          { value: '4800', label: '4800 MT/s', category: 'Entry Level' },
          { value: '5200', label: '5200 MT/s', category: 'Budget' },
          { value: '6000', label: '6000 MT/s', category: 'Average' },
          { value: '8000+', label: '8000+ MT/s', category: 'Enthusiast' }
        ];
      default:
        return [];
    }
  }, [formData.memoryGen]);

  const OptionCard = useCallback(({ 
    icon, 
    title, 
    subtitle, 
    value, 
    onClick, 
    selected = false,
    disabled = false 
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    value: string;
    onClick: () => void;
    selected?: boolean;
    disabled?: boolean;
  }) => (
    <motion.button
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      onClick={disabled ? undefined : onClick}
      className={`perf-survey__option-card ${selected ? 'perf-survey__option-card--selected' : ''} ${disabled ? 'perf-survey__option-card--disabled' : ''}`}
    >
      <div className="perf-survey__option-content">
        <div className={`perf-survey__option-icon ${selected ? 'perf-survey__option-icon--selected' : ''}`}>
          {icon}
        </div>
        <div className="perf-survey__option-text">
          <div className="perf-survey__option-title">
            {title}
          </div>
          {subtitle && (
            <div className="perf-survey__option-subtitle">
              {subtitle}
            </div>
          )}
        </div>
        {selected && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="perf-survey__option-check"
          >
            <CheckCircle size={16} className="perf-survey__performance-purple" />
          </motion.div>
        )}
      </div>
    </motion.button>
  ), []);

  const SearchableInput = useCallback(({
    value,
    onChange,
    placeholder,
    suggestions = []
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    suggestions?: string[];
  }) => {
    const [focused, setFocused] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);
    
    const filteredSuggestions = useMemo(() => {
      if (!value.trim()) {
        // Show all suggestions when input is empty
        const result = suggestions.slice(0, 5);
        console.log('Empty input, showing suggestions:', result);
        return result;
      }
      // Filter suggestions based on input value
      const result = suggestions.filter(s =>
        s.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 3);
      console.log('Filtered suggestions for "' + value + '":', result);
      return result;
    }, [suggestions, value]);

    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    }, [onChange]);

    const handleSuggestionClick = useCallback((suggestion: string) => {
      onChange(suggestion);
      setFocused(false);
    }, [onChange]);

    const handleFocus = useCallback(() => {
      console.log('Input focused');
      setFocused(true);
    }, []);

    const handleBlur = useCallback(() => {
      console.log('Input blurred');
      // Use a longer timeout to prevent premature closing when clicking suggestions
      setTimeout(() => setFocused(false), 300);
    }, []);

    // Calculate position when suggestions should show
    const getSuggestionsStyle = useCallback(() => {
      return {
        position: 'absolute' as const,
        top: '100%',
        left: 0,
        right: 0,
        marginTop: '4px'
        // z-index is handled by CSS class
      };
    }, []);

    return (
      <div className="perf-survey__search-container">
        <div className="perf-survey__search-wrapper">
          <Search size={16} className="perf-survey__search-icon" />
          <Input
            ref={inputRef}
            value={value}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            className="perf-survey__search-input"
            autoComplete="off"
          />
        </div>

        {(() => {
          const shouldShow = focused && filteredSuggestions.length > 0;
          console.log('Should show suggestions:', shouldShow, 'focused:', focused, 'suggestions count:', filteredSuggestions.length);
          return shouldShow;
        })() && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="perf-survey__search-suggestions"
            style={getSuggestionsStyle()}
          >
            {filteredSuggestions.map((suggestion, index) => (
              <button
                key={`suggestion-${index}-${suggestion}`}
                onMouseDown={(e) => {
                  e.preventDefault();
                  handleSuggestionClick(suggestion);
                }}
                className="perf-survey__search-suggestion"
                type="button"
              >
                {suggestion}
              </button>
            ))}
          </motion.div>
        )}
      </div>
    );
  }, []);

  const SectionTitle = useCallback(({ children }: { children: string }) => (
    <div className="perf-survey__section-title">
      <h3>
        {'<'}{children}{'/>'}
      </h3>
    </div>
  ), []);

  const renderCancelConfirmation = useCallback(() => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="perf-survey__text-center perf-survey__space-y-6"
    >
      <div>
        <SectionTitle>Cancel Survey?</SectionTitle>
        <p className="perf-survey__mb-4 perf-survey__text-slate-400">
          Your progress will be lost, but you can always start a fresh performance survey later.
        </p>
        <div className="perf-survey__info-box">
          <Target size={16} />
          Find the survey on this game's page or in your reviews dashboard
        </div>
      </div>
      
      <div className="perf-survey__flex-gap-3">
        <Button
          onClick={() => setShowCancelConfirm(false)}
          className="perf-survey__button perf-survey__button--secondary perf-survey__button--flex-1"
        >
          Continue Survey
        </Button>
        <Button
          onClick={confirmCancel}
          className="perf-survey__button perf-survey__button--danger perf-survey__button--flex-1"
        >
          Yes, Cancel
        </Button>
      </div>
    </motion.div>
  ), [SectionTitle, confirmCancel]);

  const renderLaterInfo = useCallback(() => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="perf-survey__text-center perf-survey__space-y-6"
    >
      <div>
        <SectionTitle>Complete Anytime</SectionTitle>
        <p className="perf-survey__mb-4 perf-survey__text-slate-400">
          No worries! You can complete performance surveys whenever you're ready.
        </p>
        <div className="perf-survey__space-y-3">
          <div className="perf-survey__info-box">
            <Target size={16} />
            Find surveys on individual game pages
          </div>
          <div className="perf-survey__info-box">
            <Gauge size={16} />
            Access all surveys in your reviews dashboard
          </div>
        </div>
      </div>
      
      <Button
        onClick={confirmCancel}
        className="perf-survey__button perf-survey__button--primary perf-survey__button--full perf-survey__py-3"
      >
        Got it!
      </Button>
    </motion.div>
  ), [SectionTitle, confirmCancel]);

  // Memoize suggestions to prevent re-renders
  const cpuSuggestions = useMemo(() => [
    // Intel 14th Gen (Raptor Lake Refresh)
    'Intel Core i9-14900K', 'Intel Core i9-14900KF', 'Intel Core i7-14700K', 'Intel Core i7-14700KF',
    'Intel Core i5-14600K', 'Intel Core i5-14600KF', 'Intel Core i5-14400F', 'Intel Core i3-14100F',

    // Intel 13th Gen (Raptor Lake)
    'Intel Core i9-13900K', 'Intel Core i9-13900KF', 'Intel Core i7-13700K', 'Intel Core i7-13700KF',
    'Intel Core i5-13600K', 'Intel Core i5-13600KF', 'Intel Core i5-13400F', 'Intel Core i3-13100F',

    // Intel 12th Gen (Alder Lake)
    'Intel Core i9-12900K', 'Intel Core i9-12900KF', 'Intel Core i7-12700K', 'Intel Core i7-12700KF',
    'Intel Core i5-12600K', 'Intel Core i5-12600KF', 'Intel Core i5-12400F', 'Intel Core i3-12100F',

    // AMD Ryzen 7000 Series (Zen 4)
    'AMD Ryzen 9 7950X', 'AMD Ryzen 9 7950X3D', 'AMD Ryzen 9 7900X', 'AMD Ryzen 9 7900X3D',
    'AMD Ryzen 7 7800X3D', 'AMD Ryzen 7 7700X', 'AMD Ryzen 5 7600X', 'AMD Ryzen 5 7600',

    // AMD Ryzen 5000 Series (Zen 3)
    'AMD Ryzen 9 5950X', 'AMD Ryzen 9 5900X', 'AMD Ryzen 7 5800X3D', 'AMD Ryzen 7 5800X',
    'AMD Ryzen 7 5700X', 'AMD Ryzen 5 5600X', 'AMD Ryzen 5 5600', 'AMD Ryzen 5 5500',

    // Popular older options
    'Intel Core i7-11700K', 'Intel Core i5-11600K', 'AMD Ryzen 7 3700X', 'AMD Ryzen 5 3600'
  ], []);

  const gpuSuggestions = useMemo(() => [
    // NVIDIA RTX 40 Series (Ada Lovelace)
    'NVIDIA RTX 4090', 'NVIDIA RTX 4080 SUPER', 'NVIDIA RTX 4080', 'NVIDIA RTX 4070 Ti SUPER',
    'NVIDIA RTX 4070 Ti', 'NVIDIA RTX 4070 SUPER', 'NVIDIA RTX 4070', 'NVIDIA RTX 4060 Ti',
    'NVIDIA RTX 4060', 'NVIDIA RTX 4050',

    // NVIDIA RTX 30 Series (Ampere)
    'NVIDIA RTX 3090 Ti', 'NVIDIA RTX 3090', 'NVIDIA RTX 3080 Ti', 'NVIDIA RTX 3080',
    'NVIDIA RTX 3070 Ti', 'NVIDIA RTX 3070', 'NVIDIA RTX 3060 Ti', 'NVIDIA RTX 3060',
    'NVIDIA RTX 3050',

    // AMD RX 7000 Series (RDNA 3)
    'AMD RX 7900 XTX', 'AMD RX 7900 XT', 'AMD RX 7900 GRE', 'AMD RX 7800 XT',
    'AMD RX 7700 XT', 'AMD RX 7600 XT', 'AMD RX 7600',

    // AMD RX 6000 Series (RDNA 2)
    'AMD RX 6950 XT', 'AMD RX 6900 XT', 'AMD RX 6800 XT', 'AMD RX 6800',
    'AMD RX 6750 XT', 'AMD RX 6700 XT', 'AMD RX 6650 XT', 'AMD RX 6600 XT',
    'AMD RX 6600', 'AMD RX 6500 XT', 'AMD RX 6400',

    // Intel Arc (Alchemist)
    'Intel Arc A770', 'Intel Arc A750', 'Intel Arc A580', 'Intel Arc A380',

    // Popular older options
    'NVIDIA GTX 1660 SUPER', 'NVIDIA GTX 1660 Ti', 'NVIDIA GTX 1660', 'AMD RX 5700 XT',
    'AMD RX 5600 XT', 'NVIDIA RTX 2070 SUPER', 'NVIDIA RTX 2060 SUPER'
  ], []);

  const laptopSuggestions = useMemo(() =>
    NOTEBOOK_DATABASE.map(notebook => notebook.name), []
  );

  const handheldSuggestions = useMemo(() =>
    HANDHELD_DATABASE.map(handheld => handheld.name), []
  );

  // Initialize step tracking on component mount
  useEffect(() => {
    markStepInteraction(1); // Mark welcome step as interacted
  }, [markStepInteraction]);

  // Track interactions when user fills form fields
  useEffect(() => {
    if (formData.deviceType && currentStep === 2) {
      markStepInteraction(2);
    }
  }, [formData.deviceType, currentStep, markStepInteraction]);

  useEffect(() => {
    if (formData.cpu && currentStep === 3) {
      markStepInteraction(3);
    }
  }, [formData.cpu, currentStep, markStepInteraction]);

  useEffect(() => {
    if (formData.gpu && currentStep === 4) {
      markStepInteraction(4);
    }
  }, [formData.gpu, currentStep, markStepInteraction]);

  useEffect(() => {
    if (formData.laptopModel && currentStep === 11) {
      markStepInteraction(11);
    }
  }, [formData.laptopModel, currentStep, markStepInteraction]);

  useEffect(() => {
    if (formData.handheldModel && currentStep === 12) {
      markStepInteraction(12);
    }
  }, [formData.handheldModel, currentStep, markStepInteraction]);

  useEffect(() => {
    if (formData.graphicsPreset && currentStep === 13) {
      markStepInteraction(13);
    }
  }, [formData.graphicsPreset, currentStep, markStepInteraction]);

  // Calculate step number for progress display
  const getProgressStep = useCallback(() => {
    if (currentStep === 11 || currentStep === 12) {
      // Laptop/handheld model steps should show as step 3 in progress
      return 3;
    }
    if (currentStep === 13) {
      // Graphics preset step should show as step 4 for laptop/handheld, or step 7 for desktop
      return formData.deviceType === 'desktop' ? 7 : 4;
    }
    if (currentStep > 13) {
      // Steps after graphics preset need to be adjusted
      const adjustment = formData.deviceType === 'desktop' ? 6 : 9; // Adjust for the skipped steps
      return currentStep - adjustment;
    }
    return currentStep;
  }, [currentStep, formData.deviceType]);

  const getProgressTotal = useCallback(() => {
    if (formData.deviceType === 'desktop') {
      return 10; // All steps including thank you (device, cpu, gpu, memory, memory speed, graphics preset, frame gen, upscale, display, performance)
    } else {
      return 6; // Fewer steps for laptop/handheld (device, model, graphics preset, frame gen, upscale, display, performance)
    }
  }, [formData.deviceType]);

  // Calculate actual completion percentage based on interactions and data
  const getCompletionPercentage = useCallback(() => {
    const stepCompletion = getStepCompletion();
    const relevantSteps = formData.deviceType === 'desktop' ?
      [1, 2, 3, 4, 5, 6, 13, 7, 8, 9, 10] : // Desktop flow (includes graphics preset)
      formData.deviceType === 'laptop' ?
      [1, 2, 11, 13, 7, 8, 9, 10] : // Laptop flow (includes graphics preset)
      formData.deviceType === 'handheld' ?
      [1, 2, 12, 13, 7, 8, 9, 10] : // Handheld flow (includes graphics preset)
      [1, 2]; // Default minimal flow

    const completedSteps = relevantSteps.filter(step => stepCompletion[step - 1]).length;
    return Math.round((completedSteps / relevantSteps.length) * 100);
  }, [formData.deviceType, getStepCompletion]);

  // Get step status for visual indicators
  const getStepStatus = useCallback((step: number) => {
    const stepCompletion = getStepCompletion();
    if (stepCompletion[step - 1]) return 'completed';
    if (step === currentStep) return 'active';
    if (step <= highestStepReached) return 'visited';
    return 'upcoming';
  }, [getStepCompletion, currentStep, highestStepReached]);

  // Get relevant steps for current device type
  const getRelevantSteps = useCallback(() => {
    if (formData.deviceType === 'desktop') {
      return [1, 2, 3, 4, 5, 6, 13, 7, 8, 9, 10];
    } else if (formData.deviceType === 'laptop') {
      return [1, 2, 11, 13, 7, 8, 9, 10];
    } else if (formData.deviceType === 'handheld') {
      return [1, 2, 12, 13, 7, 8, 9, 10];
    }
    return [1, 2]; // Default minimal flow
  }, [formData.deviceType]);

  const renderStep = useCallback(() => {
    switch (currentStep) {
      case 1: // Welcome
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="perf-survey__text-center perf-survey__space-y-6"
          >
            <div className="perf-survey__space-y-4">
              <motion.div
                animate={{ 
                  rotate: [0, 5, -5, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                className="perf-survey__icon-container perf-survey__icon-container--primary"
              >
                <Gauge size={32} className="perf-survey__performance-purple" />
              </motion.div>
              
              <SectionTitle>Performance Survey</SectionTitle>
              <p className="perf-survey__text-slate-400">
                How was your performance experience?
                {gameTitle && (
                  <span className="block mt-1 text-sm">
                     <span className="text-white font-medium">{gameTitle}</span>
                  </span>
                )}
              </p>
              <div className="perf-survey__info-box perf-survey__info-box--gray">
                <Clock size={16} />
                It takes less than a minute.
              </div>

              {!user && (
                <div className="perf-survey__info-box perf-survey__info-box--warning">
                  <Target size={16} />
                  Please log in to save your performance data
                </div>
              )}
            </div>

            <div className="perf-survey__space-y-3">
              <Button
                onClick={handleNext}
                className="perf-survey__button perf-survey__button--primary perf-survey__button--full perf-survey__button--large"
              >
                Start Survey <ArrowRight size={16} className="perf-survey__ml-2" />
              </Button>
              
              <Button
                onClick={handleMaybeLater}
                className="perf-survey__button perf-survey__button--secondary perf-survey__button--full perf-survey__py-2"
              >
                Maybe later
              </Button>
            </div>
          </motion.div>
        );

      case 2: // Device Type Selection
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Monitor size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What's your gaming device?</SectionTitle>
              <p className="perf-survey__text-sm perf-survey__text-slate-400">
                Different devices have different performance characteristics
              </p>
            </div>

            <div className="perf-survey__space-y-3">
              <OptionCard
                icon={<Monitor size={20} />}
                title="Desktop"
                subtitle="Custom built or pre-built"
                value="desktop"
                onClick={() => updateField('deviceType', 'desktop')}
                selected={formData.deviceType === 'desktop'}
              />
              <OptionCard
                icon={<Laptop size={20} />}
                title="Laptop"
                subtitle="Gaming notebook or ultrabook"
                value="laptop"
                onClick={() => updateField('deviceType', 'laptop')}
                selected={formData.deviceType === 'laptop'}
              />
              <OptionCard
                icon={<Gamepad2 size={20} />}
                title="Handheld"
                subtitle="Steam Deck, ROG Ally, etc."
                value="handheld"
                onClick={() => updateField('deviceType', 'handheld')}
                selected={formData.deviceType === 'handheld'}
              />
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.deviceType && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 11: // Laptop Model Selection
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Laptop size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What's your laptop model?</SectionTitle>

            </div>

            <SearchableInput
              value={formData.laptopModel || ''}
              onChange={handleLaptopModelChange}
              placeholder="e.g. ASUS ROG Zephyrus G14"
              suggestions={laptopSuggestions}
            />

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!formData.laptopModel}
                className={`perf-survey__button ${formData.laptopModel ? 'perf-survey__button--primary' : ''} perf-survey__button--flex-1 perf-survey__py-3`}
              >
                Next <ArrowRight size={16} className="perf-survey__ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case 12: // Handheld Model Selection
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Gamepad2 size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What's your handheld device?</SectionTitle>

            </div>

            <SearchableInput
              value={formData.handheldModel || ''}
              onChange={handleHandheldModelChange}
              placeholder="e.g. Steam Deck OLED 512GB"
              suggestions={handheldSuggestions}
            />

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!formData.handheldModel}
                className={`perf-survey__button ${formData.handheldModel ? 'perf-survey__button--primary' : ''} perf-survey__button--flex-1 perf-survey__py-3`}
              >
                Next <ArrowRight size={16} className="perf-survey__ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case 3: // CPU (Desktop only)
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Cpu size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What's your CPU?</SectionTitle>

            </div>

            <HardwareSearchInput
              value={formData.cpu}
              onChange={handleCpuChange}
              placeholder="e.g. Intel i7-13700K"
              type="cpu"
              onHardwareSelect={handleCpuSelect}
            />

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!formData.cpu}
                className={`perf-survey__button ${formData.cpu ? 'perf-survey__button--primary' : ''} perf-survey__button--flex-1 perf-survey__py-3`}
              >
                Next <ArrowRight size={16} className="perf-survey__ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case 4: // GPU (Desktop only)
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Monitor size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What about your GPU?</SectionTitle>

            </div>

            <HardwareSearchInput
              value={formData.gpu}
              onChange={handleGpuChange}
              placeholder="e.g. RTX 4070"
              type="gpu"
              onHardwareSelect={handleGpuSelect}
            />

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!formData.gpu}
                className={`perf-survey__button ${formData.gpu ? 'perf-survey__button--primary' : ''} perf-survey__button--flex-1 perf-survey__py-3`}
              >
                Next <ArrowRight size={16} className="perf-survey__ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case 5: // Memory Amount (Desktop only)
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <HardDrive size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>How much RAM?</SectionTitle>

            </div>

            <div className="perf-survey__grid-cols-2">
              {[
                { value: '8gb', label: '8GB', subtitle: 'Minimum' },
                { value: '16gb', label: '16GB', subtitle: 'Sweet spot' },
                { value: '32gb', label: '32GB', subtitle: 'Future-proof' },
                { value: '64gb+', label: '64GB+', subtitle: 'Enthusiast' }
              ].map((option) => (
                <OptionCard
                  key={option.value}
                  icon={<HardDrive size={20} />}
                  title={option.label}
                  subtitle={option.subtitle}
                  value={option.value}
                  onClick={() => updateField('totalMemory', option.value)}
                  selected={formData.totalMemory === option.value}
                />
              ))}
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.totalMemory && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 6: // Memory Type & Speed (Desktop only)
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Zap size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>What type of RAM?</SectionTitle>

            </div>

            <div className="perf-survey__space-y-3">
              {[
                { type: 'DDR3', subtitle: 'Legacy but functional' },
                { type: 'DDR4', subtitle: 'Still great for gaming' },
                { type: 'DDR5', subtitle: 'Latest & fastest' }
              ].map((memory) => (
                <div key={memory.type} className="perf-survey__space-y-2">
                  <OptionCard
                    icon={<Zap size={20} />}
                    title={memory.type}
                    subtitle={memory.subtitle}
                    value={memory.type.toLowerCase()}
                    onClick={() => updateField('memoryGen', memory.type.toLowerCase())}
                    selected={formData.memoryGen === memory.type.toLowerCase()}
                  />
                  
                  <AnimatePresence>
                    {formData.memoryGen === memory.type.toLowerCase() && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                        className="perf-survey__grid-cols-2--small-gap"
                      >
                      {getMemorySpeedOptions.map((speed) => (
                        <OptionCard
                          key={speed.value}
                          icon={<div className="perf-survey__memory-speed-indicator" />}
                          title={speed.label}
                          subtitle={speed.category}
                          value={speed.value}
                          onClick={() => updateField('memorySpeed', speed.value)}
                          selected={formData.memorySpeed === speed.value}
                        />
                      ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.memoryGen && formData.memorySpeed && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 13: // Graphics Preset Selection (All device types)
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-4"
          >
            <div className="perf-survey__text-center">
              <Settings size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-2" />
              <SectionTitle>Graphics Settings?</SectionTitle>
              <p className="perf-survey__text-sm perf-survey__text-slate-400">
                What preset did you use in the game?
              </p>
            </div>

            <div className="perf-survey__grid-cols-2">
              {(() => {
                const basePresets = [
                  { preset: 'Low', subtitle: 'Low quality', level: 1 },
                  { preset: 'Medium', subtitle: 'Balanced quality', level: 2 },
                  { preset: 'High', subtitle: 'High quality', level: 3 },
                  { preset: 'Ultra', subtitle: 'Maximum quality', level: 4 }
                ];

                // Add handheld-specific preset
                if (formData.deviceType === 'handheld') {
                  basePresets.unshift({ preset: 'Steam Deck Optimized', subtitle: 'Verified settings', level: 0 });
                }

                return basePresets.map((option) => (
                  <OptionCard
                    key={option.preset}
                    icon={
                      <div className="perf-survey__preset-indicator">
                        {option.level === 0 ? (
                          <Zap size={16} className="perf-survey__performance-purple" />
                        ) : (
                          <div className="perf-survey__preset-bars">
                            {[1, 2, 3, 4].map((bar) => (
                              <div
                                key={bar}
                                className={`perf-survey__preset-bar ${
                                  bar <= option.level ? 'perf-survey__preset-bar--active' : ''
                                }`}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    }
                    title={option.preset}
                    subtitle={option.subtitle}
                    value={option.preset.toLowerCase().replace(' ', '-')}
                    onClick={() => updateField('graphicsPreset', option.preset.toLowerCase().replace(' ', '-'))}
                    selected={formData.graphicsPreset === option.preset.toLowerCase().replace(' ', '-')}
                  />
                ));
              })()}
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.graphicsPreset && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 7: // Frame Generation
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Target size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>Frame Generation?</SectionTitle>

            </div>

            <div className="perf-survey__space-y-4">
              <div className="perf-survey__grid-cols-2">
                <OptionCard
                  icon={<Zap size={16} />}
                  title="Yes"
                  subtitle="Higher FPS"
                  value="yes"
                  onClick={() => updateField('frameGen', 'yes')}
                  selected={formData.frameGen === 'yes'}
                />
                <OptionCard
                  icon={<X size={16} />}
                  title="No"
                  subtitle="Native frames"
                  value="no"
                  onClick={() => updateField('frameGen', 'no')}
                  selected={formData.frameGen === 'no'}
                />
              </div>

              <AnimatePresence>
                {formData.frameGen === 'yes' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="perf-survey__space-y-2"
                  >
                  <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__text-slate-400">
                    Which technology?
                  </p>
                  <div className="perf-survey__flex-wrap-gap">
                    {(() => {
                      const allTechnologies = [
                        { name: 'DLSS 3', subtitle: 'NVIDIA', isNvidia: true },
                        { name: 'FSR 3', subtitle: 'AMD', isNvidia: false },
                        { name: 'XeSS', subtitle: 'Intel', isNvidia: false },
                        { name: 'Other', subtitle: 'Custom', isNvidia: false }
                      ];

                      // Filter out DLSS if AMD GPU is selected or handheld device
                      const isAmdGpu = formData.gpu && formData.gpu.toLowerCase().includes('amd');
                      const isHandheld = formData.deviceType === 'handheld';
                      const availableTechnologies = (isAmdGpu || isHandheld)
                        ? allTechnologies.filter(tech => !tech.isNvidia)
                        : allTechnologies;

                      return availableTechnologies.map((tech) => (
                        <OptionCard
                          key={tech.name}
                          icon={<Target size={16} />}
                          title={tech.name}
                          subtitle={tech.subtitle}
                          value={tech.name.toLowerCase()}
                          onClick={() => updateField('frameGenType', tech.name.toLowerCase())}
                          selected={formData.frameGenType === tech.name.toLowerCase()}
                        />
                      ));
                    })()}
                  </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.frameGen && (formData.frameGen === 'no' || formData.frameGenType) && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 8: // Upscaling
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Target size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>Upscaling Technology?</SectionTitle>

            </div>

            <div className="perf-survey__space-y-4">
              <div className="perf-survey__grid-cols-2">
                <OptionCard
                  icon={<Target size={16} />}
                  title="Yes"
                  subtitle="AI magic"
                  value="yes"
                  onClick={() => updateField('upscale', 'yes')}
                  selected={formData.upscale === 'yes'}
                />
                <OptionCard
                  icon={<X size={16} />}
                  title="No"
                  subtitle="Native res"
                  value="no"
                  onClick={() => updateField('upscale', 'no')}
                  selected={formData.upscale === 'no'}
                />
              </div>

              <AnimatePresence>
                {formData.upscale === 'yes' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="perf-survey__space-y-3"
                  >
                  <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__text-slate-400">
                    Which technology?
                  </p>
                  <div className="perf-survey__flex-wrap-gap">
                    {(() => {
                      const allTechnologies = [
                        { name: 'DLSS', subtitle: 'NVIDIA', isNvidia: true },
                        { name: 'FSR', subtitle: 'AMD', isNvidia: false },
                        { name: 'XeSS', subtitle: 'Intel', isNvidia: false },
                        { name: 'Other', subtitle: 'Custom', isNvidia: false }
                      ];

                      // Filter out DLSS if AMD GPU is selected or handheld device
                      const isAmdGpu = formData.gpu && formData.gpu.toLowerCase().includes('amd');
                      const isHandheld = formData.deviceType === 'handheld';
                      const availableTechnologies = (isAmdGpu || isHandheld)
                        ? allTechnologies.filter(tech => !tech.isNvidia)
                        : allTechnologies;

                      return availableTechnologies.map((tech) => (
                        <OptionCard
                          key={tech.name}
                          icon={<Target size={16} />}
                          title={tech.name}
                          subtitle={tech.subtitle}
                          value={tech.name.toLowerCase()}
                          onClick={() => {
                            updateField('upscaleType', tech.name.toLowerCase());
                            updateField('upscalePreset', 'balanced');
                          }}
                          selected={formData.upscaleType === tech.name.toLowerCase()}
                        />
                      ));
                    })()}
                  </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
              {formData.upscale && (formData.upscale === 'no' || formData.upscaleType) && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--flex-1"
                >
                  Next <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>
          </motion.div>
        );

      case 9: // Display Setup
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Monitor size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>Display Setup?</SectionTitle>

            </div>

            <div className="perf-survey__space-y-4">
              <div>
                <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__mb-3 perf-survey__text-slate-400">
                  Resolution
                </p>
                <div className="perf-survey__space-y-2">
                  {[
                    // Handheld-specific options (only show for handheld devices)
                    ...(formData.deviceType === 'handheld' ? [
                      { res: 'Display Native', subtitle: 'Device optimized', popular: false, handheldOnly: true },
                      { res: 'Below Native', subtitle: 'Better performance', popular: false, handheldOnly: true }
                    ] : [
                      // Standard options (only show for desktop/laptop)
                      { res: '1080p', subtitle: '1920×1080', popular: true },
                      { res: '1440p', subtitle: '2560×1440', popular: true },
                      { res: '4K', subtitle: '3840×2160', popular: false }
                    ])
                  ].map((resolution) => (
                    <OptionCard
                      key={resolution.res}
                      icon={<Monitor size={16} />}
                      title={resolution.res}
                      subtitle={`${resolution.subtitle}${resolution.popular ? ' • Popular' : ''}`}
                      value={resolution.res.toLowerCase().replace(' ', '-')}
                      onClick={() => updateField('resolution', resolution.res.toLowerCase().replace(' ', '-'))}
                      selected={formData.resolution === resolution.res.toLowerCase().replace(' ', '-')}
                    />
                  ))}
                </div>
              </div>

              <div>
                <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__mb-3 perf-survey__text-slate-400">
                  {formData.deviceType === 'handheld' ? 'Aspect Ratio' : 'Ultrawide monitor?'}
                </p>
                <div className="perf-survey__grid-cols-2">
                  <OptionCard
                    icon={<Monitor size={16} />}
                    title={formData.deviceType === 'handheld' ? 'Display Native' : 'Yes'}
                    subtitle={formData.deviceType === 'handheld' ? 'Device default' : '21:9 or wider'}
                    value={formData.deviceType === 'handheld' ? 'display-native' : 'yes'}
                    onClick={() => updateField('ultrawide', formData.deviceType === 'handheld' ? 'display-native' : 'yes')}
                    selected={formData.ultrawide === (formData.deviceType === 'handheld' ? 'display-native' : 'yes')}
                  />
                  <OptionCard
                    icon={<Monitor size={16} />}
                    title={formData.deviceType === 'handheld' ? 'Custom' : 'No'}
                    subtitle={formData.deviceType === 'handheld' ? 'Override default' : 'Standard 16:9'}
                    value={formData.deviceType === 'handheld' ? 'custom' : 'no'}
                    onClick={() => updateField('ultrawide', formData.deviceType === 'handheld' ? 'custom' : 'no')}
                    selected={formData.ultrawide === (formData.deviceType === 'handheld' ? 'custom' : 'no')}
                  />
                </div>
                {formData.deviceType === 'handheld' && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="perf-survey__mt-1"
                  >
                    <div className="perf-survey__info-box">
                      <Target size={16} />
                      Display Native automatically selected for handheld devices
                    </div>
                  </motion.div>
                )}
              </div>

              {formData.resolution && formData.ultrawide && (
                <Button
                  onClick={handleNext}
                  className="perf-survey__button perf-survey__button--primary perf-survey__button--full perf-survey__py-3"
                >
                  Final step! <ArrowRight size={16} className="perf-survey__ml-2" />
                </Button>
              )}
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
            </div>
          </motion.div>
        );

      case 10: // Performance Results
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="perf-survey__space-y-6"
          >
            <div className="perf-survey__text-center">
              <Gauge size={24} className="perf-survey__performance-purple perf-survey__mx-auto perf-survey__mb-3" />
              <SectionTitle>How did it perform?</SectionTitle>

            </div>

            <div className="perf-survey__space-y-3">
              <div>
                <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__mb-2 perf-survey__text-slate-400">
                  Average FPS
                </p>
                <div className="perf-survey__grid-cols-2--small-gap">
                  {[
                    { fps: 'Under 30', indicator: '●', subtitle: 'Struggling', color: 'perf-survey__performance-red' },
                    { fps: '30', indicator: '●', subtitle: 'Playable', color: 'perf-survey__performance-red' },
                    { fps: '45', indicator: '●', subtitle: 'Decent', color: 'perf-survey__performance-orange' },
                    { fps: '60', indicator: '●', subtitle: 'Smooth', color: 'perf-survey__performance-orange' },
                    { fps: '120', indicator: '●', subtitle: 'Excellent', color: 'perf-survey__performance-green' },
                    { fps: '144+', indicator: '●', subtitle: 'Perfect', color: 'perf-survey__performance-purple' }
                  ].map((performance) => (
                    <OptionCard
                      key={performance.fps}
                      icon={<span className={`${performance.color} perf-survey__text-xl`}>{performance.indicator}</span>}
                      title={`${performance.fps} FPS`}
                      subtitle={performance.subtitle}
                      value={performance.fps.toLowerCase().replace(' ', '-')}
                      onClick={() => updateField('fpsAverage', performance.fps.toLowerCase().replace(' ', '-'))}
                      selected={formData.fpsAverage === performance.fps.toLowerCase().replace(' ', '-')}
                    />
                  ))}
                </div>
              </div>

              <div>
                <p className="perf-survey__text-sm perf-survey__font-medium perf-survey__mb-2 perf-survey__text-slate-400">
                  Overall experience
                </p>
                <div className="perf-survey__space-y-1">
                  {[
                    { exp: 'Smooth', indicator: '●', subtitle: 'Great 1%', color: 'perf-survey__performance-green' },
                    { exp: 'Minor stutters', indicator: '●', subtitle: 'Poor 1%', color: 'perf-survey__performance-orange' },
                    { exp: 'Choppy', indicator: '●', subtitle: 'Send Help', color: 'perf-survey__performance-red' }
                  ].map((experience) => (
                    <OptionCard
                      key={experience.exp}
                      icon={<span className={`${experience.color} perf-survey__text-xl`}>{experience.indicator}</span>}
                      title={experience.exp}
                      subtitle={experience.subtitle}
                      value={experience.exp === 'Smooth' ? 'yes' : experience.exp === 'Minor stutters' ? 'traversal-stuttering' : 'no'}
                      onClick={() => {
                        const value = experience.exp === 'Smooth' ? 'yes' : experience.exp === 'Minor stutters' ? 'traversal-stuttering' : 'no';
                        updateField('smoothness', value);
                      }}
                      selected={formData.smoothness === (experience.exp === 'Smooth' ? 'yes' : experience.exp === 'Minor stutters' ? 'traversal-stuttering' : 'no')}
                    />
                  ))}
                </div>
              </div>

              {/* Complete Survey Button */}
              {formData.fpsAverage && formData.smoothness && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="perf-survey__space-y-3"
                >
                  <div className="perf-survey__info-box">
                    <CheckCircle size={16} />
                    {user ?
                      'Ready to help the community with your performance data!' :
                      'Survey complete! Data will not be saved without login.'
                    }
                  </div>
                  <Button
                    onClick={handleSubmit}
                    className="perf-survey__button perf-survey__button--primary perf-survey__button--full perf-survey__py-3"
                  >
                    <CheckCircle size={16} className="perf-survey__mr-2" />
                    {user ? 'Save & Complete Survey' : 'Complete Survey (No Save)'}
                  </Button>
                </motion.div>
              )}
            </div>

            <div className="perf-survey__flex-gap-3">
              <Button
                onClick={handleBack}
                className="perf-survey__button perf-survey__button--secondary"
              >
                <ArrowLeft size={16} className="perf-survey__mr-2" />
                Back
              </Button>
            </div>
          </motion.div>
        );

      case 14: // Thank You
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="perf-survey__text-center perf-survey__space-y-6 perf-survey__py-8"
          >
            <motion.div
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{ duration: 1.5 }}
              className="perf-survey__icon-container perf-survey__icon-container--success"
            >
              <CheckCircle size={48} className="perf-survey__performance-green" />
            </motion.div>

            <div className="perf-survey__space-y-4">
              <SectionTitle>{user ? 'Thank You, Community Hero!' : 'Survey Complete!'}</SectionTitle>
              <div className="perf-survey__space-y-3">
                <p className="perf-survey__text-slate-400">
                  {user ?
                    '🎮 Your performance data is now live and helping fellow gamers make informed decisions' :
                    '📊 Thank you for completing the survey! To save your data and help the community, please log in next time.'
                  }
                </p>
                {user ? (
                  <div className="perf-survey__space-y-2">
                    <div className="perf-survey__info-box perf-survey__info-box--gray">
                      <Target size={16} />
                      Helping others find their perfect settings
                    </div>
                    <div className="perf-survey__info-box perf-survey__info-box--gray">
                      <Gauge size={16} />
                      Contributing to our community performance database
                    </div>
                  </div>
                ) : (
                  <div className="perf-survey__info-box perf-survey__info-box--warning">
                    <Target size={16} />
                    Log in to save your performance data and help the community
                  </div>
                )}
                <p className="perf-survey__text-sm perf-survey__text-slate-500">
                  {user ?
                    'Every data point matters - you\'re making gaming better for everyone! 🚀' :
                    'Your feedback is valuable - consider creating an account to contribute! 🚀'
                  }
                </p>
              </div>
            </div>

            <Button
              onClick={() => {
                onSubmit(formData); // Submit the data when user closes
                onClose(false);
              }}
              className="perf-survey__button perf-survey__button--primary perf-survey__px-8 perf-survey__py-3"
            >
              <CheckCircle size={16} className="perf-survey__mr-2" />
              Close Survey
            </Button>
          </motion.div>
        );



      default:
        return null;
    }
  }, [currentStep, formData, handleNext, handleBack, handleSkip, handleSubmit, updateField, SectionTitle, OptionCard, SearchableInput, handleCpuChange, handleGpuChange, handleLaptopModelChange, handleHandheldModelChange, handleCpuSelect, handleGpuSelect, cpuSuggestions, gpuSuggestions, laptopSuggestions, handheldSuggestions, getMemorySpeedOptions, platform, onClose, onSubmit, totalSteps]);

  if (!isOpen) return null;

  // Create the modal content
  const modalContent = (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="perf-survey__overlay"
        onClick={(e) => {
          // Prevent any clicks from bubbling to components below
          e.stopPropagation();
        }}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="perf-survey__modal"
          onClick={(e) => {
            // Prevent clicks within modal from bubbling to overlay
            e.stopPropagation();
          }}
        >
          {/* Progress Header */}
          {currentStep < 14 && !showCancelConfirm && !showLaterInfo && (
            <motion.div 
              className="perf-survey__progress-container"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="perf-survey__progress-header">
                <div className="perf-survey__progress-info">
                  <div className="perf-survey__progress-text">
                    {getProgressStep()}/{getProgressTotal()}
                  </div>
                  <div className="perf-survey__progress-completion">
                    {getCompletionPercentage()}% complete
                  </div>
                </div>

                {/* Mini step indicators */}
                <div className="perf-survey__mini-steps">
                  {getRelevantSteps().slice(0, 6).map((step, index) => {
                    const status = getStepStatus(step);
                    return (
                      <div
                        key={step}
                        className={`perf-survey__mini-step ${status}`}
                        title={`Step ${index + 1}`}
                      />
                    );
                  })}
                </div>

                <div className="perf-survey__flex-gap-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="perf-survey__button perf-survey__button--close"
                    title="Close Performance Survey"
                    aria-label="Close Performance Survey"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
              
              <div className="perf-survey__progress-bar">
                <motion.div
                  className="perf-survey__progress-fill"
                  initial={{ width: 0 }}
                  animate={{ width: `${getCompletionPercentage()}%` }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                />
              </div>
            </motion.div>
          )}

          {/* Main Content */}
          <div className="perf-survey__content">
            <AnimatePresence mode="wait">
              <motion.div
                key={showCancelConfirm ? 'cancel' : showLaterInfo ? 'later' : currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {showCancelConfirm ? renderCancelConfirmation() : 
                 showLaterInfo ? renderLaterInfo() : 
                 renderStep()}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );

  // Render modal via portal to document.body to escape parent container constraints
  return typeof window !== 'undefined' ? createPortal(modalContent, document.body) : null;
};

export default PerformanceSurvey;