// src/app/auth/callback/route.ts
// PHASE 3 IMPLEMENTATION: OAuth Callback Handler
// Updated: January 2025 - Supabase OAuth callback processing

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/lib/supabase/types';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          },
        },
      }
    );

    try {
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('OAuth callback error:', error);
        // Redirect to home with error
        return NextResponse.redirect(
          `${requestUrl.origin}/?auth_error=${encodeURIComponent(error.message)}`
        );
      }

      if (data.user) {
        // Check if user profile exists, create if not
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.user.id)
          .single();

        if (profileError && profileError.code === 'PGRST116') {
          // Profile doesn't exist - create with all required fields
          const username = data.user.user_metadata?.username || 
                          data.user.email?.split('@')[0]?.replace(/[^a-z0-9]/gi, '') || 
                          `user_${data.user.id.slice(0, 8)}`;
          
          const slug = username.toLowerCase().replace(/[^a-z0-9]/g, '');
          
          console.log(`🔐 Creating profile for user ${data.user.email} with username: ${username}`);
          
          const { error: createError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              username,
              display_name: data.user.user_metadata?.display_name || data.user.user_metadata?.full_name || username,
              slug,
              slug_lower: slug,
              avatar_url: data.user.user_metadata?.avatar_url || null,
              theme: 'muted-dark',
              is_admin: false,
              is_online: true,
              level: 1,
              experience: 0,
              review_count: 0,
              privacy_settings: {
                profile_visibility: 'public',
                show_online_status: true,
                show_gaming_profiles: true,
                show_achievements: true,
                allow_contact: true,
                allow_friend_requests: true
              }
            });

          if (createError) {
            console.error('🚨 Profile creation error:', createError);
            // Log error but continue - don't block authentication
          } else {
            console.log(`✅ Profile created successfully for user: ${username}`);
          }
        }

        // Successful authentication - redirect to home
        return NextResponse.redirect(`${requestUrl.origin}/?auth_success=true`);
      }
    } catch (error) {
      console.error('OAuth callback processing error:', error);
      return NextResponse.redirect(
        `${requestUrl.origin}/?auth_error=${encodeURIComponent('Authentication failed')}`
      );
    }
  }

  // No code provided or other error
  return NextResponse.redirect(`${requestUrl.origin}/?auth_error=invalid_request`);
}
