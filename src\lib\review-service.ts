// src/lib/review-service.ts
// Complete Supabase Review Service Implementation
// Handles CRUD operations, IGDB integration, and content processing

import { createClient } from '@/lib/supabase/client';
import type { Review, MonetizationBlock } from '@/lib/types';
import type { Database } from '@/lib/supabase/types';
import { withSuspensionProtection } from '@/lib/middleware/suspensionCheck';

// Helper function to normalize IGDB image URLs
function normalizeIGDBImageUrl(url: string | null | undefined): string | undefined {
  if (!url) return undefined;
  
  // Handle double protocol issue in database
  if (url.startsWith('https:https://')) {
    return url.replace('https:https://', 'https://');
  }
  
  // If URL already has protocol, return as is
  if (url.startsWith('https://') || url.startsWith('http://')) {
    return url;
  }
  
  // If URL starts with //, add https:
  if (url.startsWith('//')) {
    return `https:${url}`;
  }
  
  // Default: assume it needs https:// prefix
  return `https://${url}`;
}

// Types for review operations
export interface ReviewFormData {
  title: string;
  gameName: string;
  slug?: string;
  contentLexical: any;
  contentMarkdown?: string;
  overallScore: number;
  detailedScores: Record<string, number>;
  platforms: string[];
  genres: string[];
  tags: string[];
  language: string;
  playedOn: string;
  datePlayed?: string;
  mainImageUrl?: string;
  mainImagePosition?: string;
  videoUrl?: string;
  galleryImageUrls?: string[];
  status: 'draft' | 'published' | 'archived';
  metaTitle?: string;
  metaDescription?: string;
  focusKeyword?: string;
  igdbId?: number;
  igdbData?: any;
  // Missing fields identified in QA report
  authorName?: string;
  monetizationBlocks?: MonetizationBlock[];
  // Comment settings
  enableComments?: boolean;
  // Privacy settings
  isPrivate?: boolean;
}

export interface ReviewFilters {
  platforms?: string[];
  genres?: string[];
  tags?: string[];
  scoreRange?: [number, number];
  dateRange?: [Date, Date];
  status?: string;
  authorId?: string;
}

export interface ReviewValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

// Validation function
export function validateReview(formData: ReviewFormData): ReviewValidationResult {
  const errors: Record<string, string> = {};
  const warnings: Record<string, string> = {};

  // Required fields validation
  if (!formData.title?.trim()) {
    errors.title = 'Review title is required';
  } else if (formData.title.length < 5) {
    errors.title = 'Review title must be at least 5 characters';
  } else if (formData.title.length > 200) {
    errors.title = 'Review title must be less than 200 characters';
  }

  if (!formData.igdbId) {
    errors.igdbId = 'Game selection is required';
  }

  if (!formData.contentLexical) {
    errors.content = 'Review content is required';
  }

  if (formData.overallScore < 0 || formData.overallScore > 100) {
    errors.overallScore = 'Overall score must be between 0 and 100';
  }

  if (!formData.language?.trim()) {
    errors.language = 'Language is required';
  }

  if (!formData.playedOn?.trim()) {
    errors.playedOn = 'Platform played on is required';
  }

  // Warnings for optional but recommended fields
  if (!formData.metaDescription) {
    warnings.metaDescription = 'Meta description recommended for SEO';
  }

  if (!formData.tags || formData.tags.length === 0) {
    warnings.tags = 'Tags recommended for better discoverability';
  }

  if (!formData.mainImageUrl) {
    warnings.mainImage = 'Banner image recommended for better presentation';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
}

// Helper function to generate unique slug
async function generateUniqueSlug(title: string, gameName: string): Promise<string> {
  const supabase = createClient();

  // Create base slug from title and game name
  const baseSlug = `${title}-${gameName}`
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
    .substring(0, 100);

  let slug = baseSlug;
  let counter = 1;

  // Check for uniqueness and append counter if needed
  while (true) {
    const { data, error } = await supabase
      .from('reviews')
      .select('id')
      .eq('slug', slug)
      .single();

    if (error && error.code === 'PGRST116') {
      // No matching record found, slug is unique
      break;
    }

    if (error) {
      console.error('Error checking slug uniqueness:', error);
      throw new Error('Failed to generate unique slug');
    }

    // Slug exists, try with counter
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

// Helper function to ensure game exists in database
async function ensureGameExistsLocal(igdbId: number, igdbData: any): Promise<string> {
  const supabase = createClient();
  
  try {
    // Check if game already exists
    const { data: existingGame } = await supabase
      .from('games')
      .select('id')
      .eq('igdb_id', igdbId)
      .single();
    
    if (existingGame) {
      return existingGame.id;
    }
    
    // Create new game if it doesn't exist
    const { data: newGame, error } = await supabase
      .from('games')
      .insert({
        name: igdbData.name || 'Unknown Game',
        igdb_id: igdbId,
        slug: igdbData.slug || igdbData.name?.toLowerCase().replace(/\s+/g, '-') || 'unknown-game',
        summary: igdbData.summary || null,
        cover_url: igdbData.cover?.url || null,
        release_date: igdbData.first_release_date ? new Date(igdbData.first_release_date * 1000).toISOString().split('T')[0] : null,
        platforms: igdbData.platforms || [],
        genres: igdbData.genres || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();
    
    if (error) {
      console.error('Error creating game:', error);
      throw new Error(`Failed to create game: ${error.message}`);
    }
    
    return newGame.id;
  } catch (error) {
    console.error('Error ensuring game exists:', error);
    throw error;
  }
}


// Helper function to process content (Lexical to Markdown)
export async function processReviewContent(contentLexical: any, metadata?: any): Promise<{
  contentMarkdown: string;
  seoMetadata: any;
  socialMetadata: any;
}> {
  // Basic Lexical to Markdown conversion
  // This is a simplified version - you might want to use a proper Lexical serializer
  let contentMarkdown = '';

  try {
    if (contentLexical && contentLexical.root && contentLexical.root.children) {
      contentMarkdown = extractTextFromLexical(contentLexical.root.children);
    }
  } catch (error) {
    console.error('Error processing Lexical content:', error);
    contentMarkdown = 'Content processing error';
  }

  // Generate SEO metadata
  const seoMetadata = {
    title: metadata?.title || '',
    description: metadata?.metaDescription || contentMarkdown.substring(0, 160),
    keywords: metadata?.tags?.join(', ') || '',
    focusKeyword: metadata?.focusKeyword || '',
    canonicalUrl: metadata?.slug ? `/reviews/view/${metadata.slug}` : '',
    ogTitle: metadata?.title || '',
    ogDescription: metadata?.metaDescription || contentMarkdown.substring(0, 160),
    ogImage: metadata?.mainImageUrl || '',
    twitterCard: 'summary_large_image',
    twitterTitle: metadata?.title || '',
    twitterDescription: metadata?.metaDescription || contentMarkdown.substring(0, 160),
    twitterImage: metadata?.mainImageUrl || ''
  };

  // Generate social metadata
  const socialMetadata = {
    openGraph: {
      title: metadata?.title || '',
      description: metadata?.metaDescription || contentMarkdown.substring(0, 160),
      image: metadata?.mainImageUrl || '',
      url: metadata?.slug ? `/reviews/view/${metadata.slug}` : '',
      type: 'article',
      siteName: 'CriticalPixel'
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata?.title || '',
      description: metadata?.metaDescription || contentMarkdown.substring(0, 160),
      image: metadata?.mainImageUrl || ''
    }
  };

  return {
    contentMarkdown,
    seoMetadata,
    socialMetadata
  };
}

// Helper function to extract text from Lexical JSON
function extractTextFromLexical(children: any[]): string {
  let text = '';

  for (const child of children) {
    if (child.type === 'text') {
      text += child.text || '';
    } else if (child.type === 'paragraph' || child.type === 'heading') {
      if (child.children) {
        text += extractTextFromLexical(child.children) + '\n\n';
      }
    } else if (child.type === 'list') {
      if (child.children) {
        text += extractTextFromLexical(child.children) + '\n';
      }
    } else if (child.type === 'listitem') {
      if (child.children) {
        text += '- ' + extractTextFromLexical(child.children) + '\n';
      }
    } else if (child.children) {
      text += extractTextFromLexical(child.children);
    }
  }

  return text.trim();
}

// Helper function to safely convert date_played string to valid date
function parseAndFormatDatePlayed(datePlayed?: string): string | null {
  if (!datePlayed) return null;
  
  // Check if it's in MM/YYYY format (like "12/2024")
  const mmYyyyMatch = datePlayed.match(/^(\d{1,2})\/(\d{4})$/);
  if (mmYyyyMatch) {
    const [, month, year] = mmYyyyMatch;
    // Create a valid date using the first day of the month
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    
    // Validate the created date
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date created from datePlayed: ${datePlayed}`);
      return null;
    }
    
    return date.toISOString().split('T')[0];
  }
  
  // Try parsing as a regular date string
  try {
    const date = new Date(datePlayed);
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date string in datePlayed: ${datePlayed}`);
      return null;
    }
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.warn(`Error parsing datePlayed: ${datePlayed}`, error);
    return null;
  }
}

// Create new review with suspension protection
export const createReview = withSuspensionProtection(async function createReviewInternal(formData: ReviewFormData, authorId: string): Promise<{ success: boolean; review?: Review; error?: string }> {
  try {
    const supabase = createClient();

    // Debug logging for input parameters
    console.log('CreateReview called with:', {
      authorId,
      title: formData.title,
      gameName: formData.gameName,
      hasContent: !!formData.contentLexical,
      overallScore: formData.overallScore,
      detailedScoresCount: Object.keys(formData.detailedScores || {}).length
    });

    // Enhanced input validation
    if (!authorId || typeof authorId !== 'string') {
      console.error('Invalid authorId provided:', authorId);
      return {
        success: false,
        error: 'Valid author ID is required'
      };
    }

    if (!formData.title || typeof formData.title !== 'string') {
      console.error('Invalid title provided:', formData.title);
      return {
        success: false,
        error: 'Valid title is required'
      };
    }

    // Validate form data
    const validation = validateReview(formData);
    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors);
      return {
        success: false,
        error: `Validation failed: ${Object.values(validation.errors).join(', ')}`
      };
    }

    // Generate unique slug
    const slug = formData.slug || await generateUniqueSlug(formData.title, formData.gameName);

    // Ensure game exists in database if IGDB data provided
    let gameId: string | null = null;
    if (formData.igdbId && formData.igdbData) {
      try {
        console.log('Attempting to create/find game with IGDB ID:', formData.igdbId);
        gameId = await ensureGameExistsLocal(formData.igdbId, formData.igdbData);
        console.log('Game created/found with ID:', gameId);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn('Failed to create/find game:', {
          error: errorMessage,
          igdbId: formData.igdbId,
          gameName: formData.igdbData.name
        });
        
        // Check if it's a permissions issue
        if (errorMessage.includes('permissions') || errorMessage.includes('policy')) {
          console.warn('Game creation failed due to insufficient permissions (RLS policy)');
          console.warn('This may require admin intervention to create game records');
        }
        
        // Continue without game_id - review can still be created
        gameId = null;
      }
    }

    // Process content
    const processedContent = await processReviewContent(formData.contentLexical, {
      title: formData.title,
      metaDescription: formData.metaDescription,
      tags: formData.tags,
      focusKeyword: formData.focusKeyword,
      mainImageUrl: formData.mainImageUrl,
      slug
    });

    // Prepare review data for database - match actual schema
    // Convert 0-100 scale to 0-10 scale for database constraint compatibility
    const dbOverallScore = formData.overallScore / 10;
    const dbScoringCriteria = Object.fromEntries(
      Object.entries(formData.detailedScores || {}).map(([key, value]) => [key, value / 10])
    );

    const reviewData = {
      author_id: authorId,
      game_id: gameId,
      game_name: formData.gameName,
      author_name: formData.authorName || 'Anonymous',
      slug,
      title: formData.title,
      content_lexical: formData.contentLexical,
      overall_score: dbOverallScore,
      scoring_criteria: dbScoringCriteria,
      played_on: formData.playedOn,
      language: formData.language || 'en',
      date_played: formData.datePlayed ? parseAndFormatDatePlayed(formData.datePlayed) : null,
      platforms: formData.platforms || [],
      genres: formData.genres || [],
      tags: formData.tags || [],
      main_image_url: formData.mainImageUrl,
      main_image_position: formData.mainImagePosition,
      gallery_image_urls: formData.galleryImageUrls || [],
      video_url: formData.videoUrl,
      meta_title: formData.metaTitle || processedContent.seoMetadata.title,
      meta_description: formData.metaDescription || processedContent.seoMetadata.description,
      focus_keyword: formData.focusKeyword || processedContent.seoMetadata.focusKeyword,
      status: formData.status || 'draft',
      featured_homepage: false,
      is_featured: false,
      publish_date: formData.status === 'published' ? new Date().toISOString() : null,
      monetization_blocks: formData.monetizationBlocks || [],
      view_count: 0,
      like_count: 0,
      comment_count: 0,
      // Add direct IGDB cover URL for fallback when game relationship is missing
      igdb_cover_url: formData.igdbData?.cover?.url ? normalizeIGDBImageUrl(formData.igdbData.cover.url) : null,
      official_game_link: formData.igdbData?.official_game_link || null,
      // Comment settings
      enable_comments: formData.enableComments !== undefined ? formData.enableComments : true,
      // Privacy settings
      is_private: formData.isPrivate !== undefined ? formData.isPrivate : false
    };

    // Debug logging for IGDB data storage
    if (process.env.NODE_ENV === 'development') {
      console.log('Creating review with IGDB data:', {
        gameId,
        igdbId: formData.igdbId,
        igdbCoverUrl: reviewData.igdb_cover_url,
        platforms: reviewData.platforms,
        genres: reviewData.genres,
        igdbDataPresent: !!formData.igdbData
      });
    }

    // Verify authentication before database insert
    const { data: authData, error: authError } = await supabase.auth.getUser();
    console.log('Authentication check:', {
      authenticated: !!authData.user,
      userId: authData.user?.id,
      providedAuthorId: authorId,
      match: authData.user?.id === authorId
    });

    if (authError) {
      console.error('Authentication error:', authError);
      return {
        success: false,
        error: 'Authentication failed. Please log in again.'
      };
    }

    if (!authData.user) {
      console.error('No authenticated user found');
      return {
        success: false,
        error: 'You must be logged in to create a review.'
      };
    }

    if (authData.user.id !== authorId) {
      console.error('Author ID mismatch:', {
        authUserId: authData.user.id,
        providedAuthorId: authorId
      });
      return {
        success: false,
        error: 'Author ID does not match authenticated user.'
      };
    }

    // Log the data being inserted
    console.log('About to insert review data:', {
      author_id: reviewData.author_id,
      title: reviewData.title,
      game_name: reviewData.game_name,
      overall_score: reviewData.overall_score,
      status: reviewData.status,
      content_length: JSON.stringify(reviewData.content_lexical).length
    });

    // Insert review into database
    const { data: review, error } = await supabase
      .from('reviews')
      .insert([reviewData])
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .single();

    if (error) {
      // Enhanced error logging for debugging
      console.error('Supabase insert error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      console.error('Review data that failed insert:', JSON.stringify(reviewData, null, 2));
      console.error('Author ID used:', authorId);
      
      return {
        success: false,
        error: `Database error: ${error.message || 'Unknown database error'}`
      };
    }

    // Convert database result to Review interface
    const reviewResult: Review = {
      id: review.id,
      title: review.title,
      gameName: review.games?.name || formData.gameName,
      slug: review.slug,
      releaseDate: formData.igdbData?.first_release_date ? new Date(formData.igdbData.first_release_date * 1000) : undefined,
      publishDate: review.publish_date ? new Date(review.publish_date) : undefined,
      scoringCriteria: Object.entries(review.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: (score as number) * 10  // Convert 0-10 back to 0-100 scale
      })),
      overallScore: review.overall_score * 10,  // Convert 0-10 back to 0-100 scale
      platforms: review.platforms || [],
      genres: formData.genres || [],
      language: review.language || formData.language,
      playedOn: review.played_on || formData.playedOn,
      datePlayed: review.date_played || formData.datePlayed,
      contentLexical: review.content_lexical || null,
      metaTitle: review.meta_title || formData.metaTitle,
      metaDescription: review.meta_description || formData.metaDescription,
      focusKeyword: review.focus_keyword || formData.focusKeyword,
      tags: review.tags || [],
      mainImageUrl: review.main_image_url || formData.mainImageUrl,
      mainImagePosition: review.main_image_position || formData.mainImagePosition,
      igdbCoverUrl: normalizeIGDBImageUrl(review.igdb_cover_url || review.games?.cover_url),
      galleryImageUrls: review.gallery_image_urls || formData.galleryImageUrls,
      videoUrl: review.video_url || formData.videoUrl,
      status: review.status as 'draft' | 'pending' | 'published',
      featuredHomepage: review.is_featured,
      authorId: review.author_id,
      authorName: review.profiles?.display_name || review.profiles?.username || 'Anonymous',
      authorPhotoURL: review.profiles?.avatar_url,
      authorSlug: review.profiles?.slug,
      createdAt: new Date(review.created_at),
      igdbId: formData.igdbId,
      summary: formData.igdbData?.summary,
      aggregatedRating: formData.igdbData?.aggregated_rating,
      aggregatedRatingCount: formData.igdbData?.aggregated_rating_count,
      developers: formData.igdbData?.developers?.map((d: any) => d.name) || [],
      publishers: formData.igdbData?.publishers?.map((p: any) => p.name) || [],
      gameEngines: formData.igdbData?.game_engines || [],
      playerPerspectives: formData.igdbData?.player_perspectives || [],
      timeToBeatNormally: formData.igdbData?.time_to_beat?.normally,
      timeToBeatCompletely: formData.igdbData?.time_to_beat?.completely,
      monetizationBlocks: review.monetization_blocks || formData.monetizationBlocks || []
    };

    return {
      success: true,
      review: reviewResult
    };

  } catch (error) {
    // Enhanced error logging for debugging
    console.error('Unexpected error in createReview:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      formData: JSON.stringify(formData, null, 2),
      authorId
    });
    
    return {
      success: false,
      error: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
});

// Get review by ID for editing (user must own the review)
export async function getReviewById(reviewId: string, userId: string): Promise<{ success: boolean; review?: any; error?: string }> {
  try {
    const supabase = createClient();

    // Fetch review with all necessary data for editing
    const { data: review, error } = await supabase
      .from('reviews')
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .eq('id', reviewId)
      .single();

    if (error) {
      console.error('Error fetching review for editing:', error);
      return {
        success: false,
        error: 'Review not found'
      };
    }

    // Check if user owns the review
    if (review.author_id !== userId) {
      return {
        success: false,
        error: 'You do not have permission to edit this review'
      };
    }

    return {
      success: true,
      review
    };

  } catch (error) {
    console.error('Unexpected error fetching review for editing:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

// Update existing review
export async function updateReview(reviewId: string, formData: Partial<ReviewFormData>, userId: string): Promise<{ success: boolean; review?: Review; error?: string }> {
  try {
    const supabase = createClient();

    // First check if review exists and user has permission
    const { data: existingReview, error: fetchError } = await supabase
      .from('reviews')
      .select('author_id, status')
      .eq('id', reviewId)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: 'Review not found'
      };
    }

    if (existingReview.author_id !== userId) {
      return {
        success: false,
        error: 'You do not have permission to edit this review'
      };
    }

    // Prepare update data
    const updateData: any = {};

    if (formData.title) updateData.title = formData.title;
    if (formData.gameName) updateData.game_name = formData.gameName;
    if (formData.contentLexical) {
      updateData.content_lexical = formData.contentLexical;
      // Note: content_markdown, seo_metadata, and social_metadata columns don't exist in current schema
      // Content processing is skipped to avoid database errors
    }
    if (formData.overallScore !== undefined) updateData.overall_score = formData.overallScore / 10;  // Convert 0-100 to 0-10 scale
    if (formData.detailedScores) {
      updateData.scoring_criteria = Object.fromEntries(
        Object.entries(formData.detailedScores).map(([key, value]) => [key, value / 10])
      );
    }
    if (formData.platforms) updateData.platforms = formData.platforms;
    if (formData.tags) updateData.tags = formData.tags;
    if (formData.status) {
      updateData.status = formData.status;
      if (formData.status === 'published' && existingReview.status !== 'published') {
        updateData.publish_date = new Date().toISOString();
      }
    }
    
    // Additional fields that might be edited (only include fields that exist in schema)
    if (formData.language) updateData.language = formData.language;
    if (formData.playedOn) updateData.played_on = formData.playedOn;
    if (formData.datePlayed) updateData.date_played = formData.datePlayed;
    if (formData.mainImageUrl) updateData.main_image_url = formData.mainImageUrl;
    if (formData.mainImagePosition) updateData.main_image_position = formData.mainImagePosition;
    if (formData.galleryImageUrls) updateData.gallery_image_urls = formData.galleryImageUrls;
    if (formData.videoUrl) updateData.video_url = formData.videoUrl;
    if (formData.metaTitle) updateData.meta_title = formData.metaTitle;
    if (formData.metaDescription) updateData.meta_description = formData.metaDescription;
    if (formData.focusKeyword) updateData.focus_keyword = formData.focusKeyword;
    if (formData.monetizationBlocks) updateData.monetization_blocks = formData.monetizationBlocks;
    
    // Comment settings - now supported in the database
    if (formData.enableComments !== undefined) updateData.enable_comments = formData.enableComments;

    // Privacy settings
    if (formData.isPrivate !== undefined) updateData.is_private = formData.isPrivate;

    updateData.updated_at = new Date().toISOString();

    // Update review in database
    const { data: updatedReview, error: updateError } = await supabase
      .from('reviews')
      .update(updateData)
      .eq('id', reviewId)
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .single();

    if (updateError) {
      console.error('Error updating review details:', {
        message: updateError.message,
        details: updateError.details,
        hint: updateError.hint,
        code: updateError.code,
        fullError: updateError
      });
      return {
        success: false,
        error: `Failed to update review: ${updateError.message || 'Unknown database error'}`
      };
    }

    // Convert to Review interface (similar to createReview)
    const reviewResult: Review = {
      id: updatedReview.id,
      title: updatedReview.title,
      gameName: updatedReview.games?.name || '',
      slug: updatedReview.slug,
      publishDate: updatedReview.publish_date ? new Date(updatedReview.publish_date) : undefined,
      scoringCriteria: Object.entries(updatedReview.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: (score as number) * 10  // Convert 0-10 back to 0-100 scale
      })),
      overallScore: updatedReview.overall_score * 10,  // Convert 0-10 back to 0-100 scale
      platforms: updatedReview.platforms || [],
      genres: [], // Would need to be stored separately or derived
      language: formData.language || '',
      playedOn: updatedReview.played_on || '',
      contentLexical: JSON.stringify(updatedReview.content_lexical),
      tags: updatedReview.tags || [],
      status: updatedReview.status as 'draft' | 'pending' | 'published',
      featuredHomepage: updatedReview.is_featured,
      authorId: updatedReview.author_id,
      authorName: updatedReview.profiles?.display_name || updatedReview.profiles?.username || 'Anonymous',
      authorPhotoURL: updatedReview.profiles?.avatar_url,
      authorSlug: updatedReview.profiles?.slug,
      createdAt: new Date(updatedReview.created_at),
      igdbCoverUrl: updatedReview.games?.cover_url || updatedReview.igdb_cover_url
    };

    return {
      success: true,
      review: reviewResult
    };

  } catch (error) {
    console.error('Error in updateReview:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while updating the review.'
    };
  }
}

// Get review by slug with analytics tracking
export async function getReviewBySlug(slug: string, trackView: boolean = true): Promise<Review | null> {
  try {
    const supabase = createClient();

    // Get current user to check ownership
    const { data: { user } } = await supabase.auth.getUser();

    // Adicionado filtro is_blocked para garantir que reviews bloqueados não sejam exibidos publicamente
    // Modified to allow owners to view their private reviews
    let query = supabase
      .from('reviews')
      .select('*')
      .eq('slug', slug)
      .eq('is_blocked', false);

    // If user is authenticated, allow them to see their own private reviews and drafts
    if (user) {
      // Allow: 1) published non-private reviews, 2) any review owned by the current user
      query = query.or(`and(status.eq.published,or(is_private.is.null,is_private.eq.false)),author_id.eq.${user.id}`);
    } else {
      // For non-authenticated users, only show published, non-private reviews
      query = query.eq('status', 'published').or('is_private.is.null,is_private.eq.false');
    }

    const { data: review, error: reviewError } = await query.single();

    if (reviewError || !review) {
      console.error('Error fetching review:', reviewError);
      return null;
    }

    // Manually fetch related data to avoid nested query issues
    const [profileResult, gameResult] = await Promise.all([
      supabase
        .from('profiles')
        .select('username, display_name, avatar_url, slug')
        .eq('id', review.author_id)
        .single(),
      supabase
        .from('games')
        .select('*')
        .eq('id', review.game_id)
        .single()
    ]);

    // Debug the manual queries
    if (process.env.NODE_ENV === 'development') {
      console.log('=== MANUAL QUERY RESULTS ===');
      console.log('profileResult.error:', profileResult.error);
      console.log('profileResult.data:', profileResult.data);
      console.log('gameResult.error:', gameResult.error);
      console.log('gameResult.data:', gameResult.data);
      console.log('review.game_id:', review.game_id);
    }

    // Combine the data manually
    const combinedReview = {
      ...review,
      profiles: profileResult.data,
      games: gameResult.data
    };

    // OLD VIEW TRACKING DISABLED - Now using unique daily view tracking system
    // The new system is implemented in ReviewPageClient with SimpleViewTracker component
    // This prevents double-counting and conflicts with the new tracking system
    if (false && trackView) {
      // Old tracking code disabled
    }

    // Debug logging for IGDB data retrieval
    if (process.env.NODE_ENV === 'development') {
      console.log('=== RAW REVIEW DATA FROM DATABASE ===');
      console.log('Full combinedReview object keys:', Object.keys(combinedReview));
      console.log('combinedReview.games type:', typeof combinedReview.games);
      console.log('combinedReview.games value:', combinedReview.games);
      console.log('combinedReview.game_id:', combinedReview.game_id);
      console.log('combinedReview.igdb_cover_url:', combinedReview.igdb_cover_url);
      console.log('combinedReview.official_game_link:', combinedReview.official_game_link);

      console.log('Retrieved review data from database:', {
        reviewId: combinedReview.id,
        gameId: combinedReview.game_id,
        igdbCoverUrl: combinedReview.games?.cover_url || combinedReview.igdb_cover_url,
        gamesCoverUrl: combinedReview.games?.cover_url,
        platforms: combinedReview.platforms,
        genres: combinedReview.games?.genres,
        developers: combinedReview.games?.developers,
        publishers: combinedReview.games?.publishers,
        finalIgdbCoverUrl: normalizeIGDBImageUrl(combinedReview.games?.cover_url || combinedReview.igdb_cover_url)
      });
    }

    // Convert to Review interface
    const reviewResult: Review = {
      id: combinedReview.id,
      title: combinedReview.title,
      gameName: combinedReview.games?.name || combinedReview.game_name,
      slug: combinedReview.slug,
      publishDate: combinedReview.publish_date ? new Date(combinedReview.publish_date) : undefined,
      scoringCriteria: Object.entries(combinedReview.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: (score as number) * 10  // Convert 0-10 back to 0-100 scale
      })),
      overallScore: combinedReview.overall_score * 10,  // Convert 0-10 back to 0-100 scale
      platforms: combinedReview.platforms || combinedReview.games?.platforms || [],
      genres: combinedReview.games?.genres || [],
      language: combinedReview.language || 'en',
      playedOn: combinedReview.played_on || '',
      datePlayed: combinedReview.date_played || undefined,
      contentLexical: combinedReview.content_lexical,
      tags: combinedReview.tags || [],
      mainImageUrl: combinedReview.main_image_url || undefined,
      mainImagePosition: combinedReview.main_image_position || undefined,
      videoUrl: combinedReview.video_url || undefined,
      galleryImageUrls: combinedReview.gallery_image_urls || [],
      metaTitle: combinedReview.meta_title || undefined,
      metaDescription: combinedReview.meta_description || undefined,
      focusKeyword: combinedReview.focus_keyword || undefined,
      status: combinedReview.status as 'draft' | 'pending' | 'published',
      featuredHomepage: combinedReview.is_featured,
      authorId: combinedReview.author_id,
      authorName: combinedReview.profiles?.display_name || combinedReview.profiles?.username || 'Anonymous',
      authorPhotoURL: combinedReview.profiles?.avatar_url,
      authorSlug: combinedReview.profiles?.slug,
      createdAt: new Date(combinedReview.created_at),
      igdbCoverUrl: normalizeIGDBImageUrl(combinedReview.igdb_cover_url || combinedReview.games?.cover_url) || undefined,
      igdbId: combinedReview.games?.igdb_id,
      summary: combinedReview.games?.summary,
      aggregatedRating: combinedReview.games?.aggregated_rating,
      aggregatedRatingCount: combinedReview.games?.aggregated_rating_count,
      developers: combinedReview.games?.developers || [],
      publishers: combinedReview.games?.publishers || [],
      gameEngines: combinedReview.games?.game_engines || [],
      playerPerspectives: combinedReview.games?.player_perspectives || [],
      timeToBeatNormally: combinedReview.games?.time_to_beat_normally,
      timeToBeatCompletely: combinedReview.games?.time_to_beat_completely,
      releaseDate: combinedReview.games?.release_date ? new Date(combinedReview.games.release_date) : undefined,
      officialGameLink: combinedReview.official_game_link || undefined,
      discussionLink: `/reviews/view/${combinedReview.slug}#discussion`,
      monetizationBlocks: combinedReview.monetization_blocks || [],
      // Analytics fields
      view_count: combinedReview.view_count || 0,
      likes_count: combinedReview.like_count || 0,
      // Comment settings
      enable_comments: combinedReview.enable_comments ?? true,
      // Privacy settings
      is_private: combinedReview.is_private || false
    };

    // ADDED: Additional debug logging for component data verification
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Review Service - Final object for components:', {
        reviewId: reviewResult.id,
        title: reviewResult.title,
        fromDatabase_view_count: combinedReview.view_count,
        fromDatabase_like_count: combinedReview.like_count,
        mapped_view_count: reviewResult.view_count,
        mapped_likes_count: reviewResult.likes_count,
        timestamp: new Date().toISOString()
      });
    }

    return reviewResult;

  } catch (error) {
    console.error('Error in getReviewBySlug:', error);
    return null;
  }
}


// Get reviews for user dashboard
export async function getUserReviews(userId: string, status?: string): Promise<Review[]> {
  try {
    const supabase = createClient();

    let query = supabase
      .from('reviews')
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .eq('author_id', userId)
      .eq('is_blocked', false)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: reviews, error } = await query;

    if (error) {
      console.error('Error fetching user reviews:', error);
      return [];
    }

    // Convert to Review interface
    return reviews.map(review => ({
      id: review.id,
      title: review.title,
      gameName: review.games?.name || '',
      slug: review.slug,
      publishDate: review.publish_date ? new Date(review.publish_date) : undefined,
      scoringCriteria: Object.entries(review.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: (score as number) * 10  // Convert 0-10 back to 0-100 scale
      })),
      overallScore: review.overall_score * 10,  // Convert 0-10 back to 0-100 scale
      platforms: review.platforms || [],
      genres: [], // Would need to be derived from game data
      language: review.language || 'en',
      playedOn: review.played_on || '',
      contentLexical: review.content_lexical || null,
      tags: review.tags || [],
      status: review.status as 'draft' | 'pending' | 'published',
      featuredHomepage: review.is_featured,
      authorId: review.author_id,
      authorName: review.profiles?.display_name || review.profiles?.username || 'Anonymous',
      authorPhotoURL: review.profiles?.avatar_url,
      authorSlug: review.profiles?.slug,
      createdAt: new Date(review.created_at),
      igdbCoverUrl: normalizeIGDBImageUrl(review.igdb_cover_url || review.games?.cover_url),
      igdbId: review.games?.igdb_id,
      is_private: review.is_private || false
    }));

  } catch (error) {
    console.error('Error in getUserReviews:', error);
    return [];
  }
}

// Delete review (soft delete)
export async function deleteReview(reviewId: string, userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient();

    // Check if review exists and user has permission
    const { data: existingReview, error: fetchError } = await supabase
      .from('reviews')
      .select('author_id')
      .eq('id', reviewId)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: 'Review not found'
      };
    }

    if (existingReview.author_id !== userId) {
      return {
        success: false,
        error: 'You do not have permission to delete this review'
      };
    }

    // Soft delete by updating status
    const { error: deleteError } = await supabase
      .from('reviews')
      .update({
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId);

    if (deleteError) {
      console.error('Error deleting review:', deleteError);
      return {
        success: false,
        error: 'Failed to delete review. Please try again.'
      };
    }

    return { success: true };

  } catch (error) {
    console.error('Error in deleteReview:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while deleting the review.'
    };
  }
}

// Search reviews with filters
export async function searchReviews(query: string, filters?: ReviewFilters): Promise<Review[]> {
  try {
    const supabase = createClient();

    let dbQuery = supabase
      .from('reviews')
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .eq('status', 'published')
      .eq('is_blocked', false)
      .neq('is_private', true);

    // Text search on title and game name (content_markdown doesn't exist in schema)
    if (query.trim()) {
      dbQuery = dbQuery.or(`title.ilike.%${query}%,game_name.ilike.%${query}%`);
    }

    // Apply filters
    if (filters?.platforms && filters.platforms.length > 0) {
      dbQuery = dbQuery.overlaps('platforms', filters.platforms);
    }

    if (filters?.tags && filters.tags.length > 0) {
      dbQuery = dbQuery.overlaps('tags', filters.tags);
    }

    if (filters?.scoreRange) {
      // Convert 0-100 scale filter to 0-10 scale for database query
      dbQuery = dbQuery
        .gte('overall_score', filters.scoreRange[0] / 10)
        .lte('overall_score', filters.scoreRange[1] / 10);
    }

    if (filters?.dateRange) {
      dbQuery = dbQuery
        .gte('publish_date', filters.dateRange[0].toISOString())
        .lte('publish_date', filters.dateRange[1].toISOString());
    }

    if (filters?.authorId) {
      dbQuery = dbQuery.eq('author_id', filters.authorId);
    }

    // Order by relevance (view count and score)
    dbQuery = dbQuery.order('view_count', { ascending: false });

    const { data: reviews, error } = await dbQuery.limit(50);

    if (error) {
      console.error('Error searching reviews:', error);
      return [];
    }

    // Convert to Review interface
    return reviews.map(review => ({
      id: review.id,
      title: review.title,
      gameName: review.games?.name || '',
      slug: review.slug,
      publishDate: review.publish_date ? new Date(review.publish_date) : undefined,
      scoringCriteria: Object.entries(review.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: (score as number) * 10  // Convert 0-10 back to 0-100 scale
      })),
      overallScore: review.overall_score * 10,  // Convert 0-10 back to 0-100 scale
      platforms: review.platforms || [],
      genres: [], // Would need to be derived from game data
      language: review.language || 'en',
      playedOn: review.played_on || '',
      contentLexical: review.content_lexical || null,
      tags: review.tags || [],
      status: review.status as 'draft' | 'pending' | 'published',
      featuredHomepage: review.is_featured,
      authorId: review.author_id,
      authorName: review.profiles?.display_name || review.profiles?.username || 'Anonymous',
      authorPhotoURL: review.profiles?.avatar_url,
      authorSlug: review.profiles?.slug,
      createdAt: new Date(review.created_at),
      igdbCoverUrl: normalizeIGDBImageUrl(review.igdb_cover_url || review.games?.cover_url),
      igdbId: review.games?.igdb_id,
      is_private: review.is_private || false
    }));

  } catch (error) {
    console.error('Error in searchReviews:', error);
    return [];
  }
}

// Analytics tracking functions
export async function trackReviewView(reviewId: string, userId?: string): Promise<void> {
  try {
    const supabase = createClient();
    const today = new Date().toISOString().split('T')[0];

    // Update daily analytics
    await supabase
      .from('review_analytics')
      .upsert([{
        review_id: reviewId,
        date: today,
        views: 1,
        unique_views: userId ? 1 : 0
      }], {
        onConflict: 'review_id,date'
      });

  } catch (error) {
    console.error('Error tracking review view:', error);
    // Don't throw error for analytics failures
  }
}

export async function getReviewAnalytics(reviewId: string, dateRange?: { start: Date; end: Date }): Promise<any> {
  try {
    const supabase = createClient();

    let query = supabase
      .from('review_analytics')
      .select('*')
      .eq('review_id', reviewId);

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { data: analytics, error } = await query;

    if (error) {
      console.error('Error fetching review analytics:', error);
      return null;
    }

    // Process analytics data
    const totalViews = analytics.reduce((sum, a) => sum + (a.views || 0), 0);
    const totalUniqueViews = analytics.reduce((sum, a) => sum + (a.unique_views || 0), 0);

    return {
      totalViews,
      uniqueViews: totalUniqueViews,
      analytics
    };

  } catch (error) {
    console.error('Error in getReviewAnalytics:', error);
    return null;
  }
}

// Test function to verify authentication and database connectivity
export async function testReviewCreation(): Promise<{ success: boolean; details: any; error?: string }> {
  try {
    const supabase = createClient();
    
    // Test authentication
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    const testResults = {
      authentication: {
        isAuthenticated: !!authData.user,
        userId: authData.user?.id,
        error: authError?.message
      },
      database: {
        connected: false,
        error: null as string | null
      },
      rls: {
        canInsert: false,
        error: null as string | null
      }
    };

    // Test database connectivity
    try {
      const { data, error } = await supabase.from('reviews').select('count').limit(1);
      testResults.database.connected = !error;
      testResults.database.error = error?.message || null;
    } catch (dbError) {
      testResults.database.error = dbError instanceof Error ? dbError.message : 'Unknown database error';
    }

    // Test RLS permissions (if authenticated)
    if (authData.user) {
      try {
        // Try a dry-run insert to test RLS (this will fail but show us the RLS error)
        const testReview = {
          title: 'Test Review',
          slug: 'test-review-' + Date.now(),
          game_name: 'Test Game',
          author_id: authData.user.id,
          author_name: 'Test Author',
          content_lexical: {},
          overall_score: 7.5,  // Use 0-10 scale for database (75/10)
          scoring_criteria: {},
          status: 'draft'
        };
        
        const { error: insertError } = await supabase
          .from('reviews')
          .insert([testReview])
          .select('id')
          .single();
          
        testResults.rls.canInsert = !insertError;
        testResults.rls.error = insertError?.message || null;
        
        // If successful, delete the test review
        if (!insertError) {
          await supabase.from('reviews').delete().eq('slug', testReview.slug);
        }
      } catch (rlsError) {
        testResults.rls.error = rlsError instanceof Error ? rlsError.message : 'Unknown RLS error';
      }
    }

    return {
      success: true,
      details: testResults
    };
  } catch (error) {
    return {
      success: false,
      details: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Legacy function for compatibility - wrapper to match old signature
export async function saveReview(reviewData: any): Promise<{ success: boolean; review?: Review; error?: string; message?: string }> {
  try {
    // Extract authorId from reviewData
    const { authorId, scoringCriteria, ...formData } = reviewData;

    if (!authorId) {
      return {
        success: false,
        error: 'Author ID is required',
        message: 'Author ID is required'
      };
    }

    // Transform scoringCriteria array to detailedScores object
    let detailedScores: Record<string, number> = {};
    if (scoringCriteria && Array.isArray(scoringCriteria)) {
      detailedScores = scoringCriteria.reduce((acc: Record<string, number>, criterion: any) => {
        if (criterion.id && typeof criterion.score === 'number') {
          acc[criterion.id] = criterion.score;
        }
        return acc;
      }, {});
    }

    // Prepare the form data with correct structure
    const transformedFormData: ReviewFormData = {
      ...formData,
      detailedScores
    };

    // Call createReview with proper parameters
    const result = await createReview(transformedFormData, authorId);

    // Add message field for compatibility
    return {
      ...result,
      message: result.error || (result.success ? 'Review saved successfully' : 'Failed to save review')
    };
  } catch (error) {
    // Enhanced error logging for debugging
    console.error('Error in saveReview wrapper:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      reviewData: JSON.stringify(reviewData, null, 2)
    });
    
    return {
      success: false,
      error: `SaveReview error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      message: `SaveReview error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
