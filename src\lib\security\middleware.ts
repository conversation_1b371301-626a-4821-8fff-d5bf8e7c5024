// Security Middleware for Comment System
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import { NextRequest, NextResponse } from 'next/server';
import { checkCommentRateLimit } from './rateLimiting';
import { SpamDetector } from './spamDetection';
import { ContentFilter } from './contentFilter';
import { createClient } from '@/lib/supabase/server';

export interface SecurityCheckResult {
  allowed: boolean;
  reason?: string;
  filteredContent?: string;
  retryAfter?: number;
}

export class CommentSecurityMiddleware {
  private spamDetector = new SpamDetector();
  private contentFilter = new ContentFilter();

  async validateCommentSubmission(
    content: string,
    userId?: string,
    ip?: string
  ): Promise<SecurityCheckResult> {
    try {
      // 1. Rate limiting check
      if (userId || ip) {
        const identifier = userId || ip || 'anonymous';
        const rateLimitResult = await checkCommentRateLimit(identifier, 'COMMENT_CREATE');
        
        if (!rateLimitResult.allowed) {
          return {
            allowed: false,
            reason: `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`,
            retryAfter: rateLimitResult.retryAfter,
          };
        }
      }

      // 2. Content length validation
      if (content.length < 3) {
        return {
          allowed: false,
          reason: 'Comment is too short. Minimum 3 characters required.',
        };
      }

      if (content.length > 2000) {
        return {
          allowed: false,
          reason: 'Comment is too long. Maximum 2000 characters allowed.',
        };
      }

      // 3. Spam detection
      const spamResult = await this.spamDetector.checkSpam(content, userId, ip);
      
      if (spamResult.action === 'block') {
        return {
          allowed: false,
          reason: `Comment blocked: ${spamResult.reasons.join(', ')}`,
        };
      }

      // 4. Content filtering
      const filterResult = await this.contentFilter.filterContent(content);
      
      if (!filterResult.isAllowed) {
        return {
          allowed: false,
          reason: `Content not allowed: ${filterResult.warnings.join(', ')}`,
        };
      }

      return {
        allowed: true,
        filteredContent: filterResult.filteredContent,
      };
    } catch (error) {
      console.error('Security validation error:', error);
      // Fail open for system errors
      return {
        allowed: true,
        filteredContent: content,
      };
    }
  }

  async validateModerationAction(
    action: string,
    userId: string,
    commentId: string,
    ip?: string
  ): Promise<SecurityCheckResult> {
    try {
      // Rate limiting for moderation actions
      const rateLimitResult = await checkCommentRateLimit(userId, 'COMMENT_VOTE');
      
      if (!rateLimitResult.allowed) {
        return {
          allowed: false,
          reason: `Rate limit exceeded for moderation actions.`,
          retryAfter: rateLimitResult.retryAfter,
        };
      }

      // Verify user has permission to moderate this comment
      const supabase = createClient();
      const { data: comment } = await supabase
        .from('comments')
        .select(`
          review_id,
          review:reviews!review_id(author_id)
        `)
        .eq('id', commentId)
        .single();

      if (!comment) {
        return {
          allowed: false,
          reason: 'Comment not found.',
        };
      }

      // Check if user owns the review
      if (comment.review.author_id !== userId) {
        return {
          allowed: false,
          reason: 'You can only moderate comments on your own reviews.',
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Moderation validation error:', error);
      return {
        allowed: false,
        reason: 'Validation error occurred.',
      };
    }
  }
}

// Middleware function for Next.js API routes
export function withCommentSecurity(
  handler: (req: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (req: NextRequest, context: any) => {
    const middleware = new CommentSecurityMiddleware();
    
    // Get client IP
    const ip = req.ip || 
      req.headers.get('x-forwarded-for')?.split(',')[0] || 
      req.headers.get('x-real-ip') || 
      'unknown';

    // Add security context to request
    (req as any).security = {
      ip,
      middleware,
    };

    return handler(req, context);
  };
}

// Helper function to extract user ID from request
export async function getUserFromRequest(req: NextRequest): Promise<string | null> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();
    return user?.id || null;
  } catch (error) {
    console.error('Error getting user from request:', error);
    return null;
  }
}

// CSRF protection for comment actions
export function validateCSRFToken(req: NextRequest): boolean {
  const token = req.headers.get('x-csrf-token');
  const sessionToken = req.cookies.get('csrf-token')?.value;
  
  if (!token || !sessionToken || token !== sessionToken) {
    return false;
  }
  
  return true;
}

// IP-based blocking for known bad actors
const BLOCKED_IPS = new Set([
  // Add known malicious IPs here
]);

export function isIPBlocked(ip: string): boolean {
  return BLOCKED_IPS.has(ip);
}

// Honeypot field validation
export function validateHoneypot(req: NextRequest): boolean {
  const honeypot = req.headers.get('x-honeypot') || '';
  // Honeypot field should be empty
  return honeypot === '';
}

// Complete security validation for comment endpoints
export async function validateCommentRequest(
  req: NextRequest,
  action: 'create' | 'moderate' | 'vote' | 'report'
): Promise<SecurityCheckResult> {
  const middleware = new CommentSecurityMiddleware();
  const ip = (req as any).security?.ip || 'unknown';
  
  // Check if IP is blocked
  if (isIPBlocked(ip)) {
    return {
      allowed: false,
      reason: 'Access denied.',
    };
  }

  // Validate honeypot
  if (!validateHoneypot(req)) {
    return {
      allowed: false,
      reason: 'Security validation failed.',
    };
  }

  // Get user ID
  const userId = await getUserFromRequest(req);
  
  // Action-specific validation
  switch (action) {
    case 'create':
      const body = await req.json();
      return middleware.validateCommentSubmission(
        body.content,
        userId || undefined,
        ip
      );
      
    case 'moderate':
      const moderateBody = await req.json();
      return middleware.validateModerationAction(
        moderateBody.action,
        userId || '',
        moderateBody.commentId,
        ip
      );
      
    case 'vote':
    case 'report':
      // Basic rate limiting for these actions
      const identifier = userId || ip;
      const rateLimitResult = await checkCommentRateLimit(
        identifier, 
        action === 'vote' ? 'COMMENT_VOTE' : 'COMMENT_REPORT'
      );
      
      return {
        allowed: rateLimitResult.allowed,
        reason: rateLimitResult.allowed ? undefined : 'Rate limit exceeded.',
        retryAfter: rateLimitResult.retryAfter,
      };
      
    default:
      return { allowed: true };
  }
}
