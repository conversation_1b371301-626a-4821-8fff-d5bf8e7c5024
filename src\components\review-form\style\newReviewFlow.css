/* ═══════════════════════════════════════════════════════════════════════════
   🎮 TITLE YOUR QUEST COMPONENT - CLEAN FLOW CSS (FIXED)
   ═══════════════════════════════════════════════════════════════════════════ */

/* Core Variables for TitleYourQuest - Toned Down */
:root {
    /* TYQ Color Palette - Subdued */
    --tyq-bg-primary: #0f172a;
    --tyq-bg-secondary: rgba(30, 41, 59, 0.9);
    --tyq-bg-tertiary: rgba(51, 65, 85, 0.7);
    --tyq-bg-card: rgba(30, 41, 59, 0.5);
    --tyq-bg-solid: #1e293b;
    --tyq-gradient-subtle: linear-gradient(135deg, rgba(100, 116, 139, 0.1) 0%, rgba(71, 85, 105, 0.05) 100%);
    
    /* TYQ Text Colors - Less Aggressive Blue */
    --tyq-text-primary: #f8fafc;
    --tyq-text-secondary: #cbd5e1;
    --tyq-text-muted: #94a3b8;
    --tyq-text-accent: #6b7280; /* Much more subtle gray-blue */
    --tyq-text-accent-hover: #9ca3af; /* Slightly lighter on hover */
    --tyq-text-success: #10b981;
    --tyq-text-warning: #f59e0b;
    --tyq-text-error: #ef4444;
    
    /* TYQ Borders & Effects - Very Subtle */
    --tyq-border-primary: rgba(100, 116, 139, 0.2);
    --tyq-border-secondary: rgba(100, 116, 139, 0.15);
    --tyq-border-accent: rgba(156, 163, 175, 0.3); /* Subtle gray instead of blue */
    --tyq-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    --tyq-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    --tyq-shadow-lg: 0 8px 12px -3px rgba(0, 0, 0, 0.25);
    
    /* TYQ Spacing & Sizing */
    --tyq-spacing-xs: 0.25rem;
    --tyq-spacing-sm: 0.5rem;
    --tyq-spacing-md: 0.75rem;
    --tyq-spacing-lg: 1rem;
    --tyq-spacing-xl: 1.5rem;
    --tyq-spacing-2xl: 2rem;
    
    /* TYQ Transitions - Smoother */
    --tyq-transition: 0.15s ease-out;
    --tyq-transition-spring: 0.2s ease-out;
  }
  
  /* ═══════════════════════════════════════════════════════════════════════════
     🏗️ MAIN CONTAINERS
     ═══════════════════════════════════════════════════════════════════════════ */
  
  .tyq-container {
    width: 100%;
    margin: 0 auto;
    padding: var(--tyq-spacing-lg) var(--tyq-spacing-lg) 0 var(--tyq-spacing-lg);
  }

  .tyq-main-content {
    width: 100%;
    min-height: 600px;
    position: relative;
    z-index: 1;
  }
  
  .tyq-main-card {
    background: var(--tyq-bg-primary);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: var(--tyq-transition);
  }
  
  .tyq-card-content {
    padding: 0;
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     📦 STEP PANELS
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-step-panel {
    background: rgba(51, 65, 85, 0.4); /* Lighter than previous dark blue */
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: 0;
    transition: var(--tyq-transition);
    min-height: 500px;
    display: flex;
    flex-direction: column;
    width: 100%;
    margin: 0 auto;
    backdrop-filter: blur(8px);
    box-shadow: var(--tyq-shadow-md);
  }

  .tyq-step-panel:hover {
    border-color: var(--tyq-border-accent);
    background: rgba(17, 22, 30, 0.2);
    transform: translateY(-1px);
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     📋 SUBSECTIONS
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-subsection {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.2) 0%, rgba(30, 41, 59, 0.15) 100%);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.375rem;
    padding: var(--tyq-spacing-lg);
    margin-bottom: var(--tyq-spacing-md);
    transition: var(--tyq-transition);
    backdrop-filter: blur(4px);
  }

  .tyq-subsection:hover {
    border-color: var(--tyq-border-accent);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
  }

  .tyq-subsection-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--tyq-text-accent);
    margin-bottom: var(--tyq-spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: 'Fira Code', monospace;
    padding-bottom: var(--tyq-spacing-sm);
    border-bottom: 1px solid var(--tyq-border-secondary);
    position: relative;
  }

  .tyq-subsection-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 20%;
    height: 1px;
    background: var(--tyq-text-accent);
    transition: var(--tyq-transition);
  }

  .tyq-subsection:hover .tyq-subsection-title::after {
    width: 30%;
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🎮 GAME DISPLAY - CLEANER STYLING
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-game-card {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-primary);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: var(--tyq-transition);
    backdrop-filter: blur(12px);
    box-shadow: var(--tyq-shadow-md);
    margin-top: var(--tyq-spacing-lg);
  }

  .tyq-game-card:hover {
    border-color: var(--tyq-border-accent);
    box-shadow: var(--tyq-shadow-lg);
    transform: translateY(-2px);
  }

  .tyq-game-header {
    padding: var(--tyq-spacing-xl);
    border-bottom: 1px solid var(--tyq-border-secondary);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.4) 0%, rgba(30, 41, 59, 0.6) 100%);
    backdrop-filter: blur(8px);
  }

  .tyq-game-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--tyq-text-primary);
    margin-bottom: var(--tyq-spacing-sm);
    font-family: 'JetBrains Mono', monospace;
    letter-spacing: -0.02em;
  }

  .tyq-game-meta {
    display: flex;
    align-items: center;
    gap: var(--tyq-spacing-lg);
    font-size: 0.875rem;
    color: var(--tyq-text-secondary);
    font-family: 'Fira Code', monospace;
    flex-wrap: wrap;
  }

  .tyq-game-meta span {
    background: rgba(100, 116, 139, 0.15);
    padding: var(--tyq-spacing-sm) var(--tyq-spacing-md);
    border-radius: 0.375rem;
    border: 1px solid var(--tyq-border-secondary);
    font-weight: 500;
    transition: var(--tyq-transition);
  }

  .tyq-game-meta span:hover {
    background: rgba(100, 116, 139, 0.25);
  }

  .tyq-game-body {
    display: flex;
    gap: var(--tyq-spacing-2xl);
    padding: var(--tyq-spacing-2xl);
    min-height: 240px;
    background: var(--tyq-bg-solid);
  }

  .tyq-game-cover {
    width: 180px;
    height: 240px;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--tyq-border-secondary);
    background: var(--tyq-bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--tyq-shadow-md);
    transition: var(--tyq-transition);
  }

  .tyq-game-cover:hover {
    transform: scale(1.02);
    border-color: var(--tyq-border-accent);
  }

  .tyq-game-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--tyq-transition);
  }

  .tyq-game-info {
    display: flex;
    flex-direction: column;
    gap: var(--tyq-spacing-lg);
    flex: 1;
    min-width: 0;
  }

  .tyq-info-section {
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: var(--tyq-spacing-lg);
    transition: var(--tyq-transition);
    backdrop-filter: blur(8px);
  }

  .tyq-info-section:hover {
    border-color: var(--tyq-border-accent);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.4) 0%, rgba(30, 41, 59, 0.3) 100%);
  }

  .tyq-info-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--tyq-text-accent);
    margin-bottom: var(--tyq-spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: 'Fira Code', monospace;
    border-bottom: 1px solid var(--tyq-border-secondary);
    padding-bottom: var(--tyq-spacing-xs);
  }

  .tyq-info-content {
    font-size: 0.875rem;
    color: var(--tyq-text-secondary);
    line-height: 1.6;
    font-family: 'JetBrains Mono', monospace;
  }

  .tyq-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--tyq-spacing-sm);
  }

  .tyq-badge {
    background: rgba(100, 116, 139, 0.15);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.375rem;
    padding: var(--tyq-spacing-sm) var(--tyq-spacing-md);
    font-size: 0.8rem;
    color: var(--tyq-text-secondary);
    cursor: pointer;
    transition: var(--tyq-transition);
    font-family: 'Fira Code', monospace;
    font-weight: 500;
  }

  .tyq-badge:hover {
    background: rgba(100, 116, 139, 0.25);
    border-color: var(--tyq-border-accent);
    color: var(--tyq-text-primary);
  }

  .tyq-badge.selected {
    background: rgba(156, 163, 175, 0.2);
    color: var(--tyq-text-primary);
    border-color: var(--tyq-text-accent-hover);
    font-weight: 600;
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     📝 FORM ELEMENTS
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--tyq-spacing-lg);
  }

  @media (min-width: 768px) {
    .tyq-form-grid {
      grid-template-columns: 1fr 1fr;
    }
  }

  .tyq-field-group {
    display: grid;
    gap: var(--tyq-spacing-sm);
  }

  .tyq-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--tyq-text-secondary);
    font-family: 'Fira Code', monospace;
    transition: var(--tyq-transition);
  }

  .tyq-label-required::after {
    content: " *";
    color: var(--tyq-text-error);
    font-weight: bold;
  }

  .tyq-input {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: var(--tyq-spacing-md) var(--tyq-spacing-lg);
    color: var(--tyq-text-primary);
    font-size: 0.875rem;
    transition: var(--tyq-transition);
    font-family: 'Fira Code', monospace;
    backdrop-filter: blur(4px);
    max-width: 100%;
    box-sizing: border-box;
  }

  .tyq-input:focus {
    outline: none;
    border-color: var(--tyq-text-accent-hover);
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.1);
    background: var(--tyq-bg-solid);
  }

  .tyq-input:hover:not(:focus) {
    border-color: var(--tyq-border-accent);
    background: var(--tyq-bg-solid);
  }

  .tyq-input::placeholder {
    color: var(--tyq-text-muted);
    font-style: normal;
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .tyq-select {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: var(--tyq-spacing-md) var(--tyq-spacing-lg);
    color: var(--tyq-text-primary);
    font-size: 0.875rem;
    font-family: 'Fira Code', monospace;
    cursor: pointer;
    transition: var(--tyq-transition);
    backdrop-filter: blur(4px);
    max-width: 100%;
    box-sizing: border-box;
  }

  .tyq-select:focus {
    outline: none;
    border-color: var(--tyq-text-accent-hover);
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.1);
  }

  .tyq-select:hover:not(:focus) {
    border-color: var(--tyq-border-accent);
    background: var(--tyq-bg-solid);
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🔍 FIXED IGDB SEARCH COMPONENT - SUBTLE & CLEAN
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-input-container {
    position: relative;
    width: 100%;
    cursor: default;
    overflow: visible;
    border-radius: 0.5rem;
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-secondary);
    transition: var(--tyq-transition);
  }

  .tyq-input-container:focus-within {
    border-color: var(--tyq-text-accent-hover);
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.1);
  }

  /* CLEAN IGDB Dropdown Styles - FIXED DOUBLE SCROLL */
  .igdb-dropdown {
    margin-top: 4px;
    border-radius: 0.5rem;
    background: var(--tyq-bg-solid) !important;
    border: 1px solid var(--tyq-border-secondary) !important;
    box-shadow: var(--tyq-shadow-lg) !important;
    max-height: 280px;
    overflow: hidden !important; /* Prevent parent scroll */
    z-index: 10000 !important;
    position: absolute !important;
    width: 100%;
    backdrop-filter: none !important;
    left: 0;
    right: 0;
  }

  .igdb-dropdown::before {
    display: none !important;
  }

  .igdb-dropdown-loading,
  .igdb-dropdown-error,
  .igdb-dropdown-empty {
    padding: var(--tyq-spacing-lg);
    font-size: 0.875rem;
    font-family: 'Fira Code', monospace;
    background: var(--tyq-bg-solid) !important;
    font-weight: 500;
    overflow: hidden !important;
  }

  .igdb-dropdown-loading {
    color: var(--tyq-text-secondary);
  }

  .igdb-dropdown-error {
    color: var(--tyq-text-error);
  }

  .igdb-dropdown-empty {
    color: var(--tyq-text-muted);
  }

  .igdb-dropdown-results {
    padding: 0; /* Remove padding to prevent scroll issues */
    background: var(--tyq-bg-solid) !important;
    max-height: 280px;
    overflow-y: auto !important; /* Only this container should scroll */
    overflow-x: hidden !important;
  }

  /* IGDB Option Styles - FIXED FONTS */
  .igdb-option {
    position: relative;
    cursor: pointer;
    user-select: none;
    padding: var(--tyq-spacing-md) var(--tyq-spacing-lg);
    color: var(--tyq-text-secondary) !important;
    transition: var(--tyq-transition);
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    border-bottom: 1px solid var(--tyq-border-secondary);
    background: var(--tyq-bg-solid) !important;
    font-size: 0.875rem;
    font-weight: 400 !important;
    font-style: normal !important;
  }

  .igdb-option:last-child {
    border-bottom: none;
  }

  .igdb-option:hover,
  .igdb-option-active {
    background: rgba(100, 116, 139, 0.15) !important;
    color: var(--tyq-text-primary) !important;
    border-left: 2px solid var(--tyq-text-accent-hover) !important;
    padding-left: calc(var(--tyq-spacing-lg) - 2px) !important;
  }

  .igdb-year-badge {
    margin-left: var(--tyq-spacing-md);
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 0.25rem;
    background: rgba(100, 116, 139, 0.2) !important;
    color: var(--tyq-text-muted) !important;
    border: 1px solid var(--tyq-border-secondary);
    font-weight: 600;
  }

  .igdb-meta {
    font-size: 0.75rem;
    color: var(--tyq-text-muted) !important;
    margin-top: 4px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-weight: 400 !important;
    font-style: normal !important;
  }

  .igdb-option-active .igdb-meta {
    color: var(--tyq-text-secondary) !important;
  }

  /* Portal-based dropdown that escapes all parent containers - FIXED POSITIONING & FONTS */
  .igdb-dropdown-portal {
    margin-top: 0.25rem !important; /* Reduced gap to input */
    border-radius: 0.5rem;
    background: var(--tyq-bg-solid) !important;
    border: 1px solid var(--tyq-border-secondary) !important;
    box-shadow: 0 10px 25px -8px rgba(0, 0, 0, 0.3) !important;
    -webkit-backdrop-filter: blur(12px) !important;
    backdrop-filter: blur(12px) !important;
    padding: 0 !important;
    font-size: 0.875rem;
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-weight: 400 !important;
    font-style: normal !important;
    color: var(--tyq-text-secondary) !important;
    overflow: hidden !important;
    z-index: 50000 !important;
    max-height: 280px !important;
  }

  /* Remove all overflow overrides that conflict with FixedSizeList */
  .igdb-dropdown-portal > *,
  .igdb-dropdown-portal [role="listbox"] {
    overflow: visible !important;
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Only allow the FixedSizeList scroll container to handle scrolling */
  .igdb-dropdown-portal [role="listbox"] > div[style*="overflow"] {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.5rem !important;
  }

  /* Style only the FixedSizeList scrollbar */
  .igdb-dropdown-portal [role="listbox"] > div[style*="overflow"]::-webkit-scrollbar {
    width: 8px;
  }

  .igdb-dropdown-portal [role="listbox"] > div[style*="overflow"]::-webkit-scrollbar-track {
    background: rgba(51, 65, 85, 0.3);
    border-radius: 4px;
  }

  .igdb-dropdown-portal [role="listbox"] > div[style*="overflow"]::-webkit-scrollbar-thumb {
    background: rgba(100, 116, 139, 0.6);
    border-radius: 4px;
  }

  .igdb-dropdown-portal [role="listbox"] > div[style*="overflow"]::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.8);
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🏷️ COMPACT TAGS SYSTEM - LESS BRIGHT, SHORTER
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-tags-section-simple {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: var(--tyq-spacing-lg);
    backdrop-filter: blur(8px);
    transition: var(--tyq-transition);
  }

  .tyq-tags-section-simple:hover {
    border-color: var(--tyq-border-accent);
  }

  .tyq-tags-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--tyq-spacing-md);
    padding-bottom: var(--tyq-spacing-sm);
    border-bottom: 1px solid var(--tyq-border-secondary);
  }

  .tyq-tags-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--tyq-text-accent);
    font-family: 'Fira Code', monospace;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  .tyq-tags-counter {
    font-size: 0.75rem;
    color: var(--tyq-text-muted);
    font-family: 'JetBrains Mono', monospace;
    background: rgba(100, 116, 139, 0.1);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.25rem;
    padding: 2px 6px;
    font-weight: 600;
  }

  .tyq-tags-display-simple {
    min-height: 3rem; /* Reduced from 5rem */
    padding: var(--tyq-spacing-md); /* Reduced padding */
    background: rgba(30, 41, 59, 0.2); /* Less bright */
    border: 1px dashed var(--tyq-border-secondary);
    border-radius: 0.375rem;
    margin-bottom: var(--tyq-spacing-md);
    transition: var(--tyq-transition);
  }

  .tyq-tags-display-simple:hover {
    border-color: var(--tyq-border-accent);
    background: rgba(30, 41, 59, 0.3);
  }

  .tyq-tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--tyq-spacing-sm);
  }

  .tyq-tag-simple {
    background: rgba(100, 116, 139, 0.3); /* Much less bright */
    color: var(--tyq-text-primary);
    border-radius: 0.25rem;
    padding: 0.125rem var(--tyq-spacing-sm); /* Even smaller padding */
    font-size: 0.75rem; /* Smaller font */
    display: flex;
    align-items: center;
    gap: var(--tyq-spacing-xs);
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
    border: 1px solid var(--tyq-border-secondary);
    transition: var(--tyq-transition);
    box-shadow: none; /* No glow */
    line-height: 1.2;
  }

  .tyq-tag-simple:hover {
    background: rgba(100, 116, 139, 0.4);
    transform: translateY(-1px); /* Subtle movement */
  }

  .tyq-tag-remove-simple {
    width: 14px; /* Smaller */
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: var(--tyq-transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .tyq-tag-remove-simple:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  /* COMPACT Suggestions Dropdown - FIXED SCROLL & RESET */
  .tyq-tags-suggestions-fixed {
    background: var(--tyq-bg-solid) !important;
    border: 1px solid var(--tyq-border-secondary) !important;
    border-radius: 0.5rem;
    margin-top: var(--tyq-spacing-xs);
    max-height: 8rem; /* Much shorter */
    overflow: hidden !important; /* Prevent parent scroll */
    box-shadow: var(--tyq-shadow-lg) !important;
    position: absolute !important;
    left: 0;
    right: 0;
    z-index: 40000 !important; /* Lower than IGDB dropdown */
    backdrop-filter: none !important;
  }

  .tyq-tags-suggestions-fixed::before {
    display: none !important;
  }

  .tyq-tags-suggestions-fixed .tyq-suggestions-container {
    max-height: 8rem;
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }

  .tyq-suggestion {
    padding: var(--tyq-spacing-sm) var(--tyq-spacing-md); /* Smaller padding */
    cursor: pointer;
    font-size: 0.8rem; /* Smaller font */
    color: var(--tyq-text-secondary) !important;
    border-bottom: 1px solid var(--tyq-border-secondary);
    font-family: 'JetBrains Mono', monospace;
    transition: var(--tyq-transition);
    background: var(--tyq-bg-solid) !important;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tyq-suggestion:hover,
  .tyq-suggestion.active {
    background: rgba(100, 116, 139, 0.15) !important;
    color: var(--tyq-text-primary) !important;
    border-left: 2px solid var(--tyq-text-accent-hover) !important;
    padding-left: calc(var(--tyq-spacing-md) - 2px) !important;
  }

  .tyq-suggestion:last-child {
    border-bottom: none;
  }

  /* GAME SEARCH RESET HELPERS */
  .tyq-input-container.tyq-reset {
    border-color: var(--tyq-border-secondary) !important;
    box-shadow: none !important;
  }

  .tyq-input-container.tyq-reset .igdb-dropdown {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }

  .tyq-input-container.tyq-reset input {
    background: var(--tyq-bg-solid) !important;
    border: none !important;
    color: var(--tyq-text-primary) !important;
  }

  /* Hide suggestions when parent is marked as reset */
  .tyq-game-search-reset .igdb-dropdown,
  .tyq-game-search-reset .tyq-tags-suggestions-fixed {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     📊 SUMMARY & EDITING - SUBTLE
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-summary-section {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.5rem;
    padding: var(--tyq-spacing-sm) var(--tyq-spacing-md);
    cursor: pointer;
    transition: var(--tyq-transition);
    position: relative;
    backdrop-filter: blur(8px);
    overflow: hidden;
    min-height: auto;
  }

  .tyq-summary-section:hover {
    border-color: var(--tyq-border-accent);
    background: var(--tyq-bg-solid);
    transform: translateY(-1px);
  }

  .tyq-summary-section::after {
    content: "✎ edit";
    position: absolute;
    top: var(--tyq-spacing-sm);
    right: var(--tyq-spacing-sm);
    font-size: 0.65rem;
    color: var(--tyq-text-muted);
    opacity: 0;
    transition: var(--tyq-transition);
    font-family: 'Fira Code', monospace;
    background: rgba(100, 116, 139, 0.1);
    padding: 2px 6px;
    border-radius: 0.25rem;
    border: 1px solid var(--tyq-border-secondary);
    font-weight: 600;
  }

  .tyq-summary-section:hover::after {
    opacity: 1;
  }

  .tyq-summary-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--tyq-text-accent);
    margin-bottom: var(--tyq-spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: 'Fira Code', monospace;
    position: relative;
  }

  .tyq-summary-section-content {
    font-size: 0.875rem;
    color: var(--tyq-text-primary);
    line-height: 1.3;
    font-family: 'JetBrains Mono', monospace;
  }

  /* Compact summary sections - remove extra spacing */
  .tyq-summary-section .font-medium {
    margin: 0;
    padding: 0;
  }

  .tyq-summary-section .text-xs {
    margin-top: 0.125rem;
    margin-bottom: 0;
  }

  .tyq-inline-edit {
    background: var(--tyq-bg-solid);
    border: 1px solid var(--tyq-text-accent-hover);
    border-radius: 0.375rem;
    padding: var(--tyq-spacing-sm) var(--tyq-spacing-md);
    color: var(--tyq-text-primary);
    font-size: 0.875rem;
    width: 100%;
    font-family: 'Fira Code', monospace;
    transition: var(--tyq-transition);
    backdrop-filter: blur(8px);
    max-width: 100%;
    box-sizing: border-box;
  }

  .tyq-inline-edit:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.1);
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🎯 BUTTONS AND NAVIGATION - FIXED ALIGNMENT
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-button {
    background: rgba(100, 116, 139, 0.3);
    color: var(--tyq-text-primary);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.375rem;
    padding: var(--tyq-spacing-md) var(--tyq-spacing-xl);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--tyq-transition);
    font-family: 'Fira Code', monospace;
    box-shadow: var(--tyq-shadow-sm);
    position: relative;
    overflow: hidden;
  }

  .tyq-button:hover:not(:disabled) {
    background: rgba(100, 116, 139, 0.4);
    border-color: var(--tyq-border-accent);
    transform: translateY(-1px);
    box-shadow: var(--tyq-shadow-md);
  }

  .tyq-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--tyq-shadow-sm);
  }

  .tyq-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: var(--tyq-bg-tertiary);
  }

  .tyq-button-secondary {
    background: var(--tyq-bg-solid);
    color: var(--tyq-text-secondary);
    border: 1px solid var(--tyq-border-secondary);
  }

  .tyq-button-secondary:hover:not(:disabled) {
    background: var(--tyq-bg-secondary);
    border-color: var(--tyq-border-accent);
    color: var(--tyq-text-primary);
  }

  .tyq-button-primary {
    background: rgba(100, 116, 139, 0.4);
    color: var(--tyq-text-primary);
    border: 1px solid var(--tyq-text-accent-hover);
  }

  .tyq-button-primary:hover:not(:disabled) {
    background: rgba(100, 116, 139, 0.5);
    border-color: var(--tyq-text-accent-hover);
  }

  /* FIXED: Back Button Alignment - Right Side */
  .tyq-back-button {
    display: flex;
    align-items: center;
    padding: var(--tyq-spacing-md) var(--tyq-spacing-lg);
    background: rgba(51, 65, 85, 0.4);
    color: var(--tyq-text-secondary);
    border: 1px solid var(--tyq-border-secondary);
    border-radius: 0.375rem;
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--tyq-transition);
    text-decoration: none;
  }

  .tyq-back-button:hover {
    background: rgba(51, 65, 85, 0.6);
    border-color: var(--tyq-border-accent);
    color: var(--tyq-text-primary);
  }

  /* FIXED: Button Container Alignment */
  .tyq-button-container-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--tyq-spacing-md);
    width: 100%;
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     📱 RESPONSIVE DESIGN
     ═══════════════════════════════════════════════════════════════════════════ */

  @media (max-width: 1023px) {
    .tyq-game-body {
      gap: var(--tyq-spacing-lg);
      padding: var(--tyq-spacing-lg);
    }
    
    .tyq-game-cover {
      width: 160px;
      height: 210px;
    }
    
    .tyq-tags-section-simple {
      padding: var(--tyq-spacing-md);
    }
  }

  @media (max-width: 768px) {
    .tyq-game-body {
      flex-direction: column;
      text-align: center;
      gap: var(--tyq-spacing-lg);
    }

    .tyq-game-cover {
      margin: 0 auto;
      width: 140px;
      height: 180px;
    }

    .tyq-form-grid {
      grid-template-columns: 1fr;
    }

    .tyq-tags-section-simple {
      padding: var(--tyq-spacing-md);
    }
    
    .tyq-tags-header {
      flex-direction: column;
      gap: var(--tyq-spacing-xs);
      text-align: center;
    }
    
    .tyq-tags-display-simple {
      min-height: 2.5rem;
      padding: var(--tyq-spacing-sm);
    }
    
    .tyq-tag-simple {
      font-size: 0.75rem;
      padding: 2px var(--tyq-spacing-sm);
    }

    .tyq-subsection {
      padding: var(--tyq-spacing-md);
    }

    /* Button alignment on mobile - stack vertically */
    .tyq-button-container-right {
      flex-direction: column-reverse;
      gap: var(--tyq-spacing-sm);
    }

    .tyq-back-button {
      padding: var(--tyq-spacing-sm) var(--tyq-spacing-md);
      font-size: 0.8rem;
      width: 100%;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .tyq-tags-grid {
      justify-content: center;
    }
    
    .tyq-game-header {
      padding: var(--tyq-spacing-lg);
    }
    
    .tyq-game-title {
      font-size: 1.25rem;
    }

    /* Ensure no horizontal scroll */
    .tyq-input,
    .tyq-select,
    .tyq-inline-edit {
      min-width: 0;
      width: 100%;
    }
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🔧 UTILITIES & FIXES - ENHANCED
     ═══════════════════════════════════════════════════════════════════════════ */

  .tyq-hidden {
    display: none !important;
  }

  .tyq-text-error {
    color: var(--tyq-text-error);
  }

  .tyq-text-success {
    color: var(--tyq-text-success);
  }

  .tyq-text-warning {
    color: var(--tyq-text-warning);
  }

  .tyq-text-muted {
    color: var(--tyq-text-muted);
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Prevent horizontal scroll */
  * {
    box-sizing: border-box;
    max-width: 100%;
  }

  /* SCROLL FIXES */
  .tyq-scroll-container {
    overflow: hidden !important;
  }

  .tyq-scroll-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
  }

  /* Ensure no double scroll bars */
  .tyq-input-container,
  .tyq-tags-section-simple {
    overflow: visible !important;
  }

  /* RESET STATE UTILITIES */
  .tyq-field-reset {
    opacity: 1 !important;
    transform: none !important;
    transition: var(--tyq-transition) !important;
  }

  .tyq-dropdown-hidden {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    transform: translateY(-10px) !important;
  }

  /* INPUT FOCUS MANAGEMENT */
  .tyq-input-clean {
    background: var(--tyq-bg-solid) !important;
    border: 1px solid var(--tyq-border-secondary) !important;
    box-shadow: none !important;
  }

  .tyq-input-clean:focus {
    border-color: var(--tyq-text-accent-hover) !important;
    box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.1) !important;
  }

  /* ANIMATION CLEANUP */
  .tyq-fade-out {
    opacity: 0 !important;
    transform: translateY(-5px) !important;
    pointer-events: none !important;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out !important;
  }

  .tyq-fade-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    transition: opacity 0.2s ease-out, transform 0.2s ease-out !important;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .tyq-tags-section-simple {
      border-width: 2px;
    }
    
    .tyq-tag-simple {
      border-width: 2px;
      font-weight: 700;
    }
    
    .tyq-button {
      border-width: 2px;
      font-weight: 700;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* ═══════════════════════════════════════════════════════════════════════════
     🎯 IGDB DROPDOWN SCOPED FIXES - FONT OVERRIDE
     ═══════════════════════════════════════════════════════════════════════════ */

  /* Force Lato font family on all IGDB dropdown elements with maximum specificity */
  .igdb-dropdown-portal,
  .igdb-dropdown-portal *,
  .igdb-option,
  .igdb-option *,
  .igdb-meta,
  .igdb-year-badge,
  [class*="igdb-"],
  [class*="igdb-"] *,
  .game-search-combobox *,
  .game-search-combobox input,
  .game-search-combobox input::placeholder {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-style: normal !important;
    font-weight: 400 !important;
  }

  /* Specific overrides for selected elements */
  .igdb-option span[class*="font-medium"] {
    font-weight: 500 !important;
  }

  .igdb-option span[class*="font-semibold"] {
    font-weight: 600 !important;
  }

  /* Container background fix for better contrast */
  .bg-slate-900\/60 {
    background: rgba(51, 65, 85, 0.4) !important;
  }
  
  /* Additional IGDB input and combobox font fixes */
  .game-search-combobox .tyq-input,
  .game-search-combobox .tyq-input::placeholder,
  .tyq-input-container input,
  .tyq-input-container input::placeholder {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-style: normal !important;
    font-weight: 400 !important;
  }
  
  /* Headless UI Combobox specific overrides */
  [role="combobox"],
  [role="combobox"] *,
  [role="listbox"],
  [role="listbox"] *,
  [role="option"],
  [role="option"] * {
    font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-style: normal !important;
  }

/* MAXIMUM PRIORITY IGDB FONT OVERRIDES - NUCLEAR OPTION */
.game-search-combobox,
.game-search-combobox *,
.game-search-combobox input,
.game-search-combobox input::placeholder,
.game-search-combobox [role="combobox"],
.game-search-combobox [role="listbox"],
.game-search-combobox [role="option"],
.tyq-input-container,
.tyq-input-container *,
.tyq-input-container input,
.tyq-input-container input::placeholder {
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif \!important;
  font-style: normal \!important;
  font-weight: 400 \!important;
}
