# 🎮 GUIA EXECUTIVO: IMPLEMENTAÇÃO TWITCH - 5 PASSOS
**CriticalPixel - Implementação Rápida e Prática**

*Documento Versão: 1.0*  
*Criado: 17 de Janeiro, 2025*  
*Baseado na pesquisa completa da equipe*

---

## 🚀 IMPLEMENTAÇÃO RÁPIDA - 5 PASSOS

### 📋 PRÉ-REQUISITOS
- [ ] Twitch Developer Account criado
- [ ] Supabase configurado e rodando
- [ ] Next.js environment ativo

---

## 🏗️ PASSO 1: DATABASE SETUP (30 minutos)

### 1.1 Criar Aplicativo Twitch
```bash
# 1. Vá para https://dev.twitch.tv/console
# 2. Create Application:
#    - Name: CriticalPixel
#    - Redirect URL: http://localhost:9003/api/auth/twitch/callback
#    - Category: Website Integration
# 3. Copie Client ID e Client Secret
```

### 1.2 Environment Variables
```bash
# Adicionar ao .env.local:
TWITCH_CLIENT_ID=your_client_id_here
TWITCH_CLIENT_SECRET=your_client_secret_here
TWITCH_REDIRECT_URI=http://localhost:9003/api/auth/twitch/callback
```

### 1.3 Database Migration
- [ ] **Executar SQL Schema**
```sql
-- Cole todo o conteúdo do arquivo: 160125-TwitchIntegration-DatabaseSchema.sql
-- no Supabase SQL Editor e execute
```

### 1.4 Update TypeScript Types
- [ ] **Adicionar ao `src/types/user-content.ts`:**
```typescript
// Interfaces Twitch - cole no final do arquivo
export interface UserTwitchData {
  userId: string;
  twitchUserId: string;
  username: string;
  displayName: string;
  profileImageUrl?: string;
  description?: string;
  broadcasterType: 'partner' | 'affiliate' | '';
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: string;
  scopes: string[];
  createdAt: string;Error: Invalid easing type 'cubic-bezier(0.4, 0, 0.2, 1)'
    at invariant (webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs:16:19)
    at easingDefinitionToFunction (webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/easing/utils/map.mjs:47:63)
    at keyframes (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/keyframes.mjs:27:83)
    at JSAnimation.initAnimation (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/JSAnimation.mjs:90:27)
    at new JSAnimation (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/JSAnimation.mjs:70:14)
    at AsyncMotionValueAnimation.onKeyframesResolved (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs:115:15)
    at DOMKeyframesResolver.eval [as onComplete] (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs:61:118)
    at DOMKeyframesResolver.complete (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs:140:14)
    at eval (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs:56:46)
    at Set.forEach (<anonymous>)
    at measureAllKeyframes (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs:56:15)
    at triggerCallback (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs:37:9)
    at Set.forEach (<anonymous>)
    at Object.process (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/render-step.mjs:76:23)
    at processBatch (webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/batcher.mjs:42:26)
  updatedAt: string;
  lastFetched: string;
  isValid: boolean;
  error?: string;
}

export interface UserTwitchClip {
  id: string;
  title: string;
  viewCount: number;
  createdAt: string;
  thumbnailUrl: string;
  embedUrl: string;
  url: string;
  duration: number;
  gameId?: string;
  gameName?: string;
  language: string;
  creatorId: string;
  creatorName: string;
}

export interface UserTwitchStreamStatus {
  userId: string;
  isLive: boolean;
  streamId?: string;
  streamTitle?: string;
  gameId?: string;
  gameName?: string;
  viewerCount: number;
  language: string;
  thumbnailUrl?: string;
  startedAt?: string;
  lastChecked: string;
}

export interface TwitchModuleSettings {
  enabled: boolean;
  visibility: 'public' | 'friends' | 'private';
  maxClips: number;
  showStats: boolean;
  showStreamStatus: boolean;
  autoRefresh: boolean;
  refreshInterval: number;
}
```

---

## 🔐 PASSO 2: BACKEND IMPLEMENTATION (60 minutos)

### 2.1 OAuth Class
- [ ] **Criar `src/lib/twitch/oauth.ts`:**
```typescript
interface TwitchTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  scope: string[];
  token_type: string;
}

interface TwitchUserResponse {
  data: Array<{
    id: string;
    login: string;
    display_name: string;
    type: string;
    broadcaster_type: string;
    description: string;
    profile_image_url: string;
    created_at: string;
  }>;
}

export class TwitchOAuth {
  private static readonly BASE_URL = 'https://api.twitch.tv/helix';
  private static readonly AUTH_URL = 'https://id.twitch.tv/oauth2';
  
  static generateAuthUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: process.env.TWITCH_CLIENT_ID!,
      redirect_uri: process.env.TWITCH_REDIRECT_URI!,
      response_type: 'code',
      scope: 'user:read:email clips:read',
      state,
    });
    
    return `${this.AUTH_URL}/authorize?${params.toString()}`;
  }

  static async exchangeCodeForTokens(code: string): Promise<TwitchTokenResponse> {
    const response = await fetch(`${this.AUTH_URL}/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.TWITCH_CLIENT_ID!,
        client_secret: process.env.TWITCH_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.TWITCH_REDIRECT_URI!,
      }),
    });

    if (!response.ok) throw new Error(`Token exchange failed: ${response.statusText}`);
    return response.json();
  }

  static async refreshAccessToken(refreshToken: string): Promise<TwitchTokenResponse> {
    const response = await fetch(`${this.AUTH_URL}/token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: process.env.TWITCH_CLIENT_ID!,
        client_secret: process.env.TWITCH_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      }),
    });

    if (!response.ok) throw new Error(`Token refresh failed: ${response.statusText}`);
    return response.json();
  }

  static async getUserData(accessToken: string): Promise<TwitchUserResponse> {
    const response = await fetch(`${this.BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Client-Id': process.env.TWITCH_CLIENT_ID!,
      },
    });

    if (!response.ok) throw new Error(`Failed to fetch user data: ${response.statusText}`);
    return response.json();
  }
}
```

### 2.2 API Service
- [ ] **Criar `src/lib/twitch/api.ts`:**
```typescript
import type { UserTwitchClip, UserTwitchStreamStatus } from '@/types/user-content';

interface TwitchClipsResponse {
  data: Array<{
    id: string;
    url: string;
    embed_url: string;
    broadcaster_id: string;
    broadcaster_name: string;
    creator_id: string;
    creator_name: string;
    title: string;
    view_count: number;
    created_at: string;
    thumbnail_url: string;
    duration: number;
    game_id: string;
    language: string;
  }>;
}

export class TwitchAPI {
  private static readonly BASE_URL = 'https://api.twitch.tv/helix';

  private static async makeRequest(endpoint: string, accessToken: string, params?: Record<string, string>) {
    const url = new URL(`${this.BASE_URL}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => url.searchParams.append(key, value));
    }

    return fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Client-Id': process.env.TWITCH_CLIENT_ID!,
      },
    });
  }

  static async getUserClips(accessToken: string, broadcasterId: string, limit: number = 20): Promise<UserTwitchClip[]> {
    try {
      const response = await this.makeRequest('/clips', accessToken, {
        broadcaster_id: broadcasterId,
        first: limit.toString(),
        started_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      });

      if (!response.ok) throw new Error(`Failed to fetch clips: ${response.statusText}`);

      const data: TwitchClipsResponse = await response.json();
      return data.data.map(clip => ({
        id: clip.id,
        title: clip.title,
        viewCount: clip.view_count,
        createdAt: clip.created_at,
        thumbnailUrl: clip.thumbnail_url,
        embedUrl: clip.embed_url,
        url: clip.url,
        duration: clip.duration,
        gameId: clip.game_id,
        gameName: '',
        language: clip.language,
        creatorId: clip.creator_id,
        creatorName: clip.creator_name,
      }));
    } catch (error) {
      console.error('Error fetching clips:', error);
      return [];
    }
  }

  static async getStreamStatus(accessToken: string, userId: string): Promise<UserTwitchStreamStatus | null> {
    try {
      const response = await this.makeRequest('/streams', accessToken, { user_id: userId });
      if (!response.ok) throw new Error(`Failed to fetch stream status: ${response.statusText}`);

      const data = await response.json();
      
      if (data.data.length === 0) {
        return {
          userId,
          isLive: false,
          viewerCount: 0,
          language: 'en',
          lastChecked: new Date().toISOString(),
        };
      }

      const stream = data.data[0];
      return {
        userId,
        isLive: true,
        streamId: stream.id,
        streamTitle: stream.title,
        gameId: stream.game_id,
        gameName: stream.game_name,
        viewerCount: stream.viewer_count,
        language: stream.language,
        thumbnailUrl: stream.thumbnail_url,
        startedAt: stream.started_at,
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching stream status:', error);
      return null;
    }
  }
}
```

### 2.3 Server Actions
- [ ] **Criar `src/app/u/actions-twitch.ts`:**
```typescript
'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { TwitchOAuth } from '@/lib/twitch/oauth';
import { TwitchAPI } from '@/lib/twitch/api';
import type { UserTwitchData, TwitchModuleSettings } from '@/types/user-content';

async function getSupabaseClient() {
  const cookieStore = await cookies();
  return createServerClient(cookieStore);
}

export async function initiateTwitchConnection(userId: string) {
  const state = `${userId}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  const authUrl = TwitchOAuth.generateAuthUrl(state);
  
  const cookieStore = await cookies();
  cookieStore.set('twitch_oauth_state', state, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 600,
  });
  
  return { authUrl, state };
}

export async function completeTwitchConnection(userId: string, code: string, state: string) {
  try {
    const cookieStore = await cookies();
    const storedState = cookieStore.get('twitch_oauth_state')?.value;
    
    if (!storedState || storedState !== state) {
      return { success: false, error: 'Invalid OAuth state' };
    }

    const tokens = await TwitchOAuth.exchangeCodeForTokens(code);
    const userData = await TwitchOAuth.getUserData(tokens.access_token);
    const user = userData.data[0];

    const expiresAt = new Date(Date.now() + tokens.expires_in * 1000);
    const supabase = await getSupabaseClient();
    
    const twitchData = {
      user_id: userId,
      twitch_user_id: user.id,
      username: user.login,
      display_name: user.display_name,
      profile_image_url: user.profile_image_url,
      description: user.description,
      broadcaster_type: user.broadcaster_type,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      token_expires_at: expiresAt.toISOString(),
      scopes: tokens.scope,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase
      .from('user_twitch_data')
      .upsert(twitchData, { onConflict: 'user_id' });

    if (error) {
      console.error('Error saving Twitch data:', error);
      return { success: false, error: 'Failed to save Twitch connection' };
    }

    cookieStore.delete('twitch_oauth_state');
    return { success: true, data: twitchData };
  } catch (error) {
    console.error('Error completing connection:', error);
    return { success: false, error: 'Failed to connect Twitch account' };
  }
}

export async function getUserTwitchData(userId: string) {
  try {
    const supabase = await getSupabaseClient();
    const { data: twitchData, error } = await supabase
      .from('user_twitch_data')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      return { success: false, error: 'Failed to fetch Twitch data' };
    }

    if (!twitchData) {
      return { success: false, error: 'No Twitch account connected' };
    }

    return { success: true, data: twitchData };
  } catch (error) {
    console.error('Error getting Twitch data:', error);
    return { success: false, error: 'Failed to get Twitch data' };
  }
}

export async function disconnectTwitchAccount(userId: string) {
  try {
    const supabase = await getSupabaseClient();

    await Promise.all([
      supabase.from('user_twitch_data').delete().eq('user_id', userId),
      supabase.from('user_twitch_clips').delete().eq('user_id', userId),
      supabase.from('user_twitch_stream_status').delete().eq('user_id', userId),
    ]);

    return { success: true };
  } catch (error) {
    console.error('Error disconnecting:', error);
    return { success: false, error: 'Failed to disconnect Twitch account' };
  }
}
```

### 2.4 OAuth Callback Route
- [ ] **Criar `src/app/api/auth/twitch/callback/route.ts`:**
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { completeTwitchConnection } from '@/app/u/actions-twitch';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  
  if (error) {
    return NextResponse.redirect(new URL('/u/dashboard?twitch_error=access_denied', request.url));
  }
  
  if (!code || !state) {
    return NextResponse.redirect(new URL('/u/dashboard?twitch_error=invalid_request', request.url));
  }
  
  try {
    const userId = state.split('-')[0];
    const result = await completeTwitchConnection(userId, code, state);
    
    if (result.success) {
      return NextResponse.redirect(new URL('/u/dashboard?twitch_success=connected', request.url));
    } else {
      return NextResponse.redirect(new URL(`/u/dashboard?twitch_error=${encodeURIComponent(result.error || 'connection_failed')}`, request.url));
    }
  } catch (error) {
    console.error('Callback error:', error);
    return NextResponse.redirect(new URL('/u/dashboard?twitch_error=server_error', request.url));
  }
}
```

---

## 🎛️ PASSO 3: DASHBOARD COMPONENT (45 minutos)

### 3.1 TwitchChannelConfig Component
- [ ] **Criar `src/components/dashboard/TwitchChannelConfig.tsx`:**
```typescript
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Loader2, ExternalLink, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  getUserTwitchData, 
  disconnectTwitchAccount,
  initiateTwitchConnection 
} from '@/app/u/actions-twitch';
import type { UserTwitchData } from '@/types/user-content';

// Criar ícone Twitch inline por simplicidade
const TwitchIcon = ({ className = "h-5 w-5" }) => (
  <svg viewBox="0 0 24 24" className={className} fill="currentColor">
    <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z"/>
  </svg>
);

interface TwitchChannelConfigProps {
  userId: string;
}

const TwitchChannelConfig: React.FC<TwitchChannelConfigProps> = ({ userId }) => {
  const [twitchData, setTwitchData] = useState<UserTwitchData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadTwitchData();
  }, [userId]);

  const loadTwitchData = async () => {
    setIsLoading(true);
    try {
      const response = await getUserTwitchData(userId);
      if (response.success) {
        setTwitchData(response.data || null);
      }
    } catch (error) {
      console.error('Error loading Twitch data:', error);
      toast({
        title: 'Erro',
        description: 'Falha ao carregar configuração Twitch',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      const { authUrl } = await initiateTwitchConnection(userId);
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error connecting:', error);
      toast({
        title: 'Erro de Conexão',
        description: 'Falha ao conectar com Twitch. Tente novamente.',
        variant: 'destructive',
      });
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!confirm('Tem certeza que deseja desconectar sua conta Twitch?')) return;

    try {
      const result = await disconnectTwitchAccount(userId);
      if (result.success) {
        setTwitchData(null);
        toast({
          title: 'Desconectado',
          description: 'Sua conta Twitch foi desconectada com sucesso.',
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
      toast({
        title: 'Erro',
        description: 'Falha ao desconectar conta Twitch.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TwitchIcon className="h-5 w-5 text-purple-500" />
            Integração Twitch
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TwitchIcon className="h-5 w-5 text-purple-500" />
          Integração Twitch
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {!twitchData ? (
          <div className="text-center py-6">
            <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-6 mb-4">
              <TwitchIcon className="h-12 w-12 text-purple-500 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">
                Conectar Conta Twitch
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                Mostre seu status de streaming e clips no seu perfil
              </p>
              <Button
                onClick={handleConnect}
                disabled={isConnecting}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Conectando...
                  </>
                ) : (
                  <>
                    <TwitchIcon className="h-4 w-4 mr-2" />
                    Conectar Twitch
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={twitchData.profileImageUrl}
                alt={twitchData.displayName}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-white">{twitchData.displayName}</span>
                  <Badge variant="outline" className="text-green-400 border-green-400">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Conectado
                  </Badge>
                </div>
                <p className="text-sm text-gray-400">@{twitchData.username}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`https://twitch.tv/${twitchData.username}`, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Ver Canal
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDisconnect}
                className="text-red-400 border-red-400 hover:bg-red-400/10"
              >
                Desconectar
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TwitchChannelConfig;
```

### 3.2 Integrar no Dashboard
- [ ] **Adicionar ao `src/app/u/dashboard/page.tsx`:**
```typescript
// Importar no topo
import TwitchChannelConfig from '@/components/dashboard/TwitchChannelConfig';

// Adicionar no JSX do dashboard (procurar outras configurações similares)
<TwitchChannelConfig userId={user.id} />
```

---

## 👤 PASSO 4: PROFILE DISPLAY (60 minutos)

### 4.1 TwitchModule Component
- [ ] **Criar `src/components/userprofile/TwitchModule.tsx`:**
```typescript
'use client';

import React, { useState } from 'react';
import { Play, ExternalLink, Eye, Calendar, Twitch } from 'lucide-react';
import { UserTwitchData, UserTwitchClip } from '@/types/user-content';
import { MagicContainer, FloatingParticles } from './MagicUIIntegration';

interface TwitchModuleProps {
  twitchData: UserTwitchData | null;
  clips: UserTwitchClip[];
  isLoading?: boolean;
  theme?: string;
  className?: string;
}

const TwitchModule: React.FC<TwitchModuleProps> = ({
  twitchData,
  clips,
  isLoading = false,
  theme = 'cosmic',
  className = '',
}) => {
  const [selectedClip, setSelectedClip] = useState<UserTwitchClip | null>(null);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  };

  if (isLoading) {
    return (
      <MagicContainer theme={theme} className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-700 rounded w-48"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-800 rounded-lg h-48"></div>
            ))}
          </div>
        </div>
      </MagicContainer>
    );
  }

  if (!twitchData || clips.length === 0) {
    return (
      <MagicContainer theme={theme} className={`p-6 ${className}`}>
        <div className="text-center py-8">
          <Twitch className="mx-auto mb-4 h-12 w-12 text-purple-400 opacity-50" />
          <h3 className="text-lg font-semibold text-gray-300 mb-2">
            Nenhum clip encontrado
          </h3>
          <p className="text-gray-400 text-sm">
            Este usuário ainda não possui clips do Twitch para exibir.
          </p>
        </div>
      </MagicContainer>
    );
  }

  return (
    <>
      <MagicContainer theme={theme} className={`p-6 ${className}`}>
        <FloatingParticles theme={theme} count={8} />
        
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <img
            src={twitchData.profileImageUrl}
            alt={twitchData.displayName}
            className="w-16 h-16 rounded-full border-2 border-purple-500"
          />
          <div className="flex-1">
            <h2 className="text-xl font-bold text-white mb-1">
              {twitchData.displayName}
            </h2>
            <p className="text-gray-400 text-sm mb-2 line-clamp-2">
              {twitchData.description}
            </p>
            <div className="flex items-center gap-4 text-sm text-gray-400">
              <span>{clips.length} clips</span>
            </div>
          </div>
          <a
            href={`https://twitch.tv/${twitchData.username}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-white transition-colors flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Canal
          </a>
        </div>

        {/* Clips Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {clips.slice(0, 6).map((clip) => (
            <div
              key={clip.id}
              className="bg-gray-900/50 border border-purple-500/20 rounded-lg overflow-hidden hover:border-purple-500/40 transition-all cursor-pointer group"
              onClick={() => setSelectedClip(clip)}
            >
              <div className="relative">
                <img
                  src={clip.thumbnailUrl}
                  alt={clip.title}
                  className="w-full h-40 object-cover group-hover:scale-105 transition-transform"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors flex items-center justify-center">
                  <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {Math.floor(clip.duration / 60)}:{(clip.duration % 60).toString().padStart(2, '0')}
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-gray-100 text-sm line-clamp-2 mb-2">
                  {clip.title}
                </h3>
                
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{formatNumber(clip.viewCount)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(clip.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </MagicContainer>

      {/* Modal */}
      {selectedClip && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-bold text-white">{selectedClip.title}</h2>
                <button
                  onClick={() => setSelectedClip(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ✕
                </button>
              </div>
              
              <div className="aspect-video mb-4 rounded-lg overflow-hidden">
                <iframe
                  src={selectedClip.embedUrl}
                  title={selectedClip.title}
                  className="w-full h-full"
                  allowFullScreen
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Eye className="h-5 w-5 text-purple-400 mx-auto mb-1" />
                  <div className="text-sm text-gray-400">Visualizações</div>
                  <div className="font-semibold text-white">{formatNumber(selectedClip.viewCount)}</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Calendar className="h-5 w-5 text-purple-400 mx-auto mb-1" />
                  <div className="text-sm text-gray-400">Criado em</div>
                  <div className="font-semibold text-white">{formatDate(selectedClip.createdAt)}</div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Twitch className="h-5 w-5 text-purple-400 mx-auto mb-1" />
                  <div className="text-sm text-gray-400">Criador</div>
                  <div className="font-semibold text-white">{selectedClip.creatorName}</div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <a
                  href={selectedClip.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-purple-600 hover:bg-purple-700 px-6 py-3 rounded-lg text-white transition-colors flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Ver no Twitch
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TwitchModule;
```

### 4.2 Integrar no Profile
- [ ] **No `src/app/u/[slug]/ProfilePageClient.tsx`, adicionar:**
```typescript
// Importar no topo
import TwitchModule from '@/components/userprofile/TwitchModule';
import { getUserTwitchData } from '@/app/u/actions-twitch';
import { TwitchAPI } from '@/lib/twitch/api';

// Adicionar no componente (onde carrega outros módulos)
const [twitchData, setTwitchData] = useState(null);
const [twitchClips, setTwitchClips] = useState([]);

// Na função de carregamento de dados
const loadTwitchData = async () => {
  try {
    const response = await getUserTwitchData(profile.id);
    if (response.success && response.data) {
      setTwitchData(response.data);
      
      // Carregar clips
      const clips = await TwitchAPI.getUserClips(
        response.data.accessToken,
        response.data.twitchUserId,
        12
      );
      setTwitchClips(clips);
    }
  } catch (error) {
    console.error('Error loading Twitch data:', error);
  }
};

// No JSX, adicionar o módulo
{twitchData && (
  <TwitchModule
    twitchData={twitchData}
    clips={twitchClips}
    theme={theme}
    className="mb-6"
  />
)}
```

---

## 🧪 PASSO 5: TESTES E FINALIZAÇÃO (30 minutos)

### 5.1 Testes Funcionais
- [ ] **Testar OAuth completo:**
  1. Conectar conta Twitch via dashboard
  2. Verificar redirecionamento
  3. Confirmar dados salvos no banco
  4. Verificar exibição no perfil

### 5.2 Testes Visuais
- [ ] **Testar responsividade:**
  1. Desktop (1920x1080)
  2. Tablet (768px)
  3. Mobile (375px)

### 5.3 Validação Final
- [ ] **Checklist de validação:**
  - [ ] OAuth flow funciona
  - [ ] Dashboard permite conectar/desconectar
  - [ ] Clips aparecem no perfil
  - [ ] Modal de clips funciona
  - [ ] Responsivo em mobile
  - [ ] Sem erros no console

### 5.4 Otimizações
- [ ] **Performance:**
  - [ ] Imagens com lazy loading
  - [ ] Componentes memoizados
  - [ ] APIs com cache

---

## ✅ CHECKLIST FINAL

### Implementação Básica
- [ ] ✅ Database schema executado
- [ ] ✅ Environment variables configuradas
- [ ] ✅ OAuth flow implementado
- [ ] ✅ Dashboard component criado
- [ ] ✅ Profile module implementado

### Testes Essenciais
- [ ] ✅ Conexão Twitch funciona
- [ ] ✅ Clips carregam corretamente
- [ ] ✅ Modal responsivo
- [ ] ✅ Mobile compatibility

### Próximos Passos
- [ ] Stream status em tempo real
- [ ] Cache de clips otimizado
- [ ] Analytics de engajamento
- [ ] Admin panel para moderação

---

**🎮 IMPLEMENTAÇÃO COMPLETA!**
*Seguindo este guia, você terá uma integração Twitch funcional em aproximadamente 3-4 horas.*

**Status**: ⏳ Pronto para execução  
**Tempo estimado**: 3-4 horas  
**Dificuldade**: Intermediária 