'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthContext } from '@/contexts/auth-context';

interface LikeStatus {
  liked: boolean;
  likeCount: number;
}

interface LikeResponse {
  success: boolean;
  liked: boolean;
  likeCount: number;
  error?: string;
}

/**
 * Hook to manage like functionality for reviews
 */
export function useLikes(reviewId: string | null) {
  const { user } = useAuthContext();
  const queryClient = useQueryClient();
  const [optimisticState, setOptimisticState] = useState<LikeStatus | null>(null);

  // Query to get current like status
  const {
    data: likeStatus,
    isLoading,
    error
  } = useQuery({
    queryKey: ['likes', reviewId],
    queryFn: async (): Promise<LikeStatus> => {
      if (!reviewId) throw new Error('Review ID is required');
      
      const response = await fetch(`/api/reviews/${reviewId}/like`);
      if (!response.ok) {
        throw new Error(`Failed to fetch like status: ${response.statusText}`);
      }
      
      const data: LikeResponse = await response.json();
      return {
        liked: data.liked,
        likeCount: data.likeCount
      };
    },
    enabled: !!reviewId,
    staleTime: 30000, // Consider data fresh for 30 seconds
    gcTime: 300000,   // Keep in cache for 5 minutes
  });

  // Mutation to toggle like status
  const likeMutation = useMutation({
    mutationFn: async (): Promise<LikeResponse> => {
      if (!reviewId) throw new Error('Review ID is required');
      if (!user) throw new Error('Authentication required');

      const response = await fetch(`/api/reviews/${reviewId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to toggle like: ${response.statusText}`);
      }

      return response.json();
    },
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['likes', reviewId] });

      // Snapshot the previous value
      const previousLikes = queryClient.getQueryData<LikeStatus>(['likes', reviewId]);

      // Optimistically update the like status
      if (previousLikes && reviewId) {
        const newStatus: LikeStatus = {
          liked: !previousLikes.liked,
          likeCount: previousLikes.liked 
            ? previousLikes.likeCount - 1 
            : previousLikes.likeCount + 1
        };
        
        queryClient.setQueryData(['likes', reviewId], newStatus);
        setOptimisticState(newStatus);
      }

      // Return a context object with the snapshotted value
      return { previousLikes };
    },
    onError: (err, newLike, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousLikes && reviewId) {
        queryClient.setQueryData(['likes', reviewId], context.previousLikes);
        setOptimisticState(null);
      }
      console.error('Like mutation failed:', err);
    },
    onSuccess: (data) => {
      // Update the cache with the server response
      if (reviewId) {
        queryClient.setQueryData(['likes', reviewId], {
          liked: data.liked,
          likeCount: data.likeCount
        });
        setOptimisticState(null);
        
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['review', reviewId] });
        queryClient.invalidateQueries({ queryKey: ['userReviews'] });
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      if (reviewId) {
        queryClient.invalidateQueries({ queryKey: ['likes', reviewId] });
      }
    },
  });

  // Clear optimistic state when component unmounts or reviewId changes
  useEffect(() => {
    setOptimisticState(null);
  }, [reviewId]);

  const currentStatus = optimisticState || likeStatus || { liked: false, likeCount: 0 };

  return {
    liked: currentStatus.liked,
    likeCount: currentStatus.likeCount,
    isLoading,
    error,
    toggleLike: likeMutation.mutate,
    isToggling: likeMutation.isPending,
    canLike: !!user, // User must be authenticated to like
  };
}