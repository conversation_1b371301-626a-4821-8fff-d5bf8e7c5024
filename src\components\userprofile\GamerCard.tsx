// src/components/userprofile/GamerCard.tsx
'use client';

import { useState, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import type { UserProfile } from '@/lib/types/profile';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { calculateProfilePermissions } from '@/utils/profile-permissions';

// Icon imports
import FacebookIcon from '@/components/ui/icons/socials/FacebookIcon';
import GithubIcon from '@/components/ui/icons/socials/GithubIcon';
import InstagramIcon from '@/components/ui/icons/socials/InstagramIcon';
import RedditIcon from '@/components/ui/icons/socials/RedditIcon';
import TikTokIcon from '@/components/ui/icons/socials/TikTokIcon';
import TwitchIcon from '@/components/ui/icons/socials/TwitchIcon';
import YouTubeIcon from '@/components/ui/icons/socials/YouTubeIcon';
import TwitterIcon from '@/components/ui/icons/socials/TwitterIcon';
import ChatSquareLikeIcon from '@/components/ui/icons/socials/ChatSquareLikeIcon';

import {
  Gamepad2,
  Pencil,
  ChevronDown,
  Terminal,
  User,
  Users,
  Zap,
  Cpu,
  ExternalLink,
  Heart
} from 'lucide-react';

// Theme configuration
const themes = {
  default: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa' },
  ocean: { primary: '#3b82f6', secondary: '#2563eb', accent: '#60a5fa' },
  forest: { primary: '#10b981', secondary: '#059669', accent: '#34d399' },
  crimson: { primary: '#ef4444', secondary: '#dc2626', accent: '#f87171' },
  silver: { primary: '#6b7280', secondary: '#4b5563', accent: '#9ca3af' },
  amber: { primary: '#f59e0b', secondary: '#d97706', accent: '#fbbf24' }
};

interface GamerCardProps {
  profileData: UserProfile | null;
  isOwnProfile: boolean;
  isAdmin?: boolean;
  onEdit?: () => void;
  loading?: boolean;
  error?: string | null;
  viewerId?: string;
}

// Loading skeleton components
const SkeletonLine = ({ width = "100%", height = "1rem" }: { width?: string; height?: string }) => (
  <div
    className="bg-gray-700 rounded animate-pulse"
    style={{ width, height }}
  />
);

const SkeletonCard = ({ children }: { children: React.ReactNode }) => (
  <div className="p-3 rounded-xl bg-gray-800 border border-gray-700 animate-pulse">
    {children}
  </div>
);

const SkeletonSection = ({ title }: { title: string }) => (
  <div>
    <div className="flex items-center gap-2 mb-4">
      <SkeletonLine width="1.25rem" height="1.25rem" />
      <SkeletonLine width="8rem" height="1rem" />
    </div>
    <div className="space-y-3">
      <SkeletonCard>
        <div className="flex items-center gap-3">
          <SkeletonLine width="2rem" height="2rem" />
          <div className="flex-1 space-y-2">
            <SkeletonLine width="60%" height="0.875rem" />
            <SkeletonLine width="40%" height="0.75rem" />
          </div>
        </div>
      </SkeletonCard>
      <SkeletonCard>
        <div className="flex items-center gap-3">
          <SkeletonLine width="2rem" height="2rem" />
          <div className="flex-1 space-y-2">
            <SkeletonLine width="70%" height="0.875rem" />
            <SkeletonLine width="30%" height="0.75rem" />
          </div>
        </div>
      </SkeletonCard>
    </div>
  </div>
);



// Social media icon mapping
const SocialIconDisplay = ({ platform, className, color }: { platform: string; className?: string; color?: string }) => {
  const key = platform.toLowerCase();
  const iconClass = cn('h-5 w-5', className);
  const style = color ? { color } : {};
  
  if (key.includes('twitch')) return <TwitchIcon className={iconClass} style={style} />;
  if (key.includes('youtube')) return <YouTubeIcon className={iconClass} style={style} />;
  if (key.includes('twitter') || key.includes('x')) return <TwitterIcon className={iconClass} style={style} />;
  if (key.includes('facebook')) return <FacebookIcon className={iconClass} style={style} />;
  if (key.includes('instagram')) return <InstagramIcon className={iconClass} style={style} />;
  if (key.includes('github')) return <GithubIcon className={iconClass} style={style} />;
  if (key.includes('reddit')) return <RedditIcon className={iconClass} style={style} />;
  if (key.includes('tiktok')) return <TikTokIcon className={iconClass} style={style} />;
  return <ChatSquareLikeIcon className={iconClass} style={style} />;
};

// Section Header Component with theme accent
const SectionHeader: React.FC<{ title: string; icon: React.ElementType; color?: string }> = ({ title, icon: Icon, color }) => (
  <div className="flex items-center gap-2 mb-4">
    <Icon className="h-5 w-5" style={{ color: color || '#a78bfa' }} />
    <span className="font-mono text-sm font-semibold text-gray-300 uppercase tracking-wider">
      {title}
    </span>
  </div>
);

const GamerCard: React.FC<GamerCardProps> = memo(({
  profileData,
  isOwnProfile,
  isAdmin = false,
  onEdit,
  loading = false,
  error = null,
  viewerId
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Calculate privacy permissions
  const permissions = useMemo(() => {
    if (!profileData) return null;
    return calculateProfilePermissions(profileData, viewerId);
  }, [profileData, viewerId]);

  // Extract profile data with privacy filtering
  const {
    username: userName = 'anonymous_user',
    display_name: displayName = 'Anonymous User',
    bio = '',
    preferred_genres: preferredGenres = [],
    theme = 'default'
  } = profileData || {};

  // Get social media from separate tables
  const socialMedia = useMemo(() => {
    if (!profileData) return [];
    
    // Access data from the separate social_profiles table
    return profileData.social_profiles || [];
  }, [profileData]);

  const themeColors = useMemo(() =>
    themes[theme as keyof typeof themes] || themes.default,
    [theme]
  );

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: 'auto',
      transition: { duration: 0.4 }
    },
    exit: {
      opacity: 0,
      height: 0,
      transition: { duration: 0.3 }
    }
  };

  // Error state
  if (error) {
    return (
      <motion.div
        className="relative w-full rounded-xl bg-red-900 border border-red-800 overflow-hidden"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="p-6 text-center">
          <div className="text-red-400 mb-2">
            <Terminal className="h-8 w-8 mx-auto" />
          </div>
          <h3 className="text-lg font-mono font-semibold text-red-300 mb-2">
            Profile Load Error
          </h3>
          <p className="text-sm text-red-400 mb-4 font-mono">
            {error}
          </p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-800 hover:bg-red-700 text-red-200 rounded-lg font-mono text-sm transition-colors"
          >
            Retry Connection
          </button>
        </div>
      </motion.div>
    );
  }

  // Loading state
  if (loading || !profileData) {
    return (
      <motion.div
        className="relative w-full rounded-xl bg-gray-900 border border-gray-800 overflow-hidden"
        style={{
          borderColor: `${themeColors.primary}30`,
          boxShadow: `0 0 20px ${themeColors.primary}15`
        }}
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Loading Header */}
        <div className="relative px-6 py-4 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <SkeletonLine width="2rem" height="2rem" />
              <div className="flex items-center gap-2">
                <SkeletonLine width="1.25rem" height="1.25rem" />
                <SkeletonLine width="8rem" height="1.125rem" />
              </div>
            </div>
            <SkeletonLine width="2.5rem" height="2.5rem" />
          </div>
        </div>

        {/* Loading Content */}
        <div className="p-6 space-y-6">
          <SkeletonSection title="About Me" />
          <SkeletonSection title="Social Profiles" />
          <SkeletonSection title="Gaming Profiles" />

          {/* Gaming DNA Skeleton */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <SkeletonLine width="1.25rem" height="1.25rem" />
              <SkeletonLine width="6rem" height="1rem" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 rounded-xl bg-gray-800 border border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                  <SkeletonLine width="1rem" height="1rem" />
                  <SkeletonLine width="8rem" height="0.75rem" />
                </div>
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3].map((i) => (
                    <SkeletonLine key={i} width="4rem" height="1.5rem" />
                  ))}
                </div>
              </div>
              <div className="p-4 rounded-xl bg-gray-800 border border-gray-700">
                <div className="flex items-center gap-2 mb-3">
                  <SkeletonLine width="1rem" height="1rem" />
                  <SkeletonLine width="9rem" height="0.75rem" />
                </div>
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3, 4].map((i) => (
                    <SkeletonLine key={i} width="2.5rem" height="2.5rem" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Loading Footer */}
        <div className="px-6 py-3 bg-gray-800 border-t border-gray-700">
          <div className="text-center">
            <SkeletonLine width="12rem" height="0.75rem" />
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="relative w-full rounded-xl bg-gray-900 border border-gray-800 overflow-hidden"
      style={{
        borderColor: `${themeColors.primary}30`,
        boxShadow: `0 0 20px ${themeColors.primary}15`
      }}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <div 
        className="relative px-6 py-4 bg-gray-800 border-b border-gray-700 cursor-pointer transition-all duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700"
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown className="h-5 w-5" style={{ color: themeColors.accent }} />
            </motion.div>
            
            <div className="flex items-center gap-2">
              <Terminal className="h-5 w-5" style={{ color: themeColors.primary }} />
              <span className="font-mono text-lg font-semibold text-white">
                Profile
              </span>
            </div>
          </div>

          {isOwnProfile && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <motion.button
                    className="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-all duration-200"
                    style={{
                      borderColor: `${themeColors.primary}50`,
                      borderWidth: '1px',
                      borderStyle: 'solid'
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit?.();
                    }}
                  >
                    <Pencil className="h-4 w-4" style={{ color: themeColors.accent }} />
                  </motion.button>
                </TooltipTrigger>
                <TooltipContent>Edit Profile</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>

      {/* Expandable Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            variants={contentVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="overflow-hidden"
          >
            <div className="p-6 space-y-6">
              
              {/* About Me Section */}
              <div>
                <SectionHeader title="About Me" icon={User} color={themeColors.accent} />
                <div className="p-4 rounded-xl bg-gray-800 border border-gray-700">
                  {permissions?.canViewFullProfile ? (
                    bio ? (
                      bio.split('\n').filter(line => line.trim()).map((line, index) => (
                        <motion.p
                          key={index}
                          className="text-gray-300 text-sm mb-2 last:mb-0 font-mono"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <span style={{ color: themeColors.accent }}>//</span> {line}
                        </motion.p>
                      ))
                    ) : (
                      <p className="text-gray-500 text-sm font-mono italic">
                        <span style={{ color: themeColors.accent }}>//</span> This user prefers to remain mysterious...
                      </p>
                    )
                  ) : (
                    <p className="text-gray-500 text-sm font-mono italic">
                      <span style={{ color: themeColors.accent }}>//</span> This profile is private
                    </p>
                  )}
                </div>
              </div>

              {/* Social Media Section - Hidden for now */}
              {false && (
                <div>
                                      <SectionHeader title="Social Profiles" icon={Heart} color={themeColors.accent} />
                  {permissions?.canViewSocialProfiles ? (
                    socialMedia.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {socialMedia.map((item: any, index: number) => (
                          <motion.a
                            key={`${item.platform}-${item.username}-${index}`}
                            href={item.url || '#'}
                            target={item.url ? '_blank' : undefined}
                            rel="noopener noreferrer"
                            className="flex items-center gap-3 p-3 rounded-xl bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-200"
                            style={{
                              borderColor: `${themeColors.primary}20`,
                            }}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="p-2 rounded-md bg-gray-900">
                              <SocialIconDisplay platform={item.platform} color={themeColors.accent} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium text-white truncate">
                                {item.username}
                              </div>
                              <div className="text-xs text-gray-500 font-mono">
                                {item.platform}
                              </div>
                            </div>
                            {item.url && (
                              <ExternalLink className="h-4 w-4 text-gray-500" />
                            )}
                          </motion.a>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500 text-sm font-mono">
                        {'{ '}No social profiles linked{' }'}
                        {isOwnProfile && (
                          <button
                            type="button"
                            className="ml-2 text-sm hover:underline"
                            style={{ color: themeColors.accent }}
                            onClick={() => onEdit?.()}
                          >
                            Add Profile
                          </button>
                        )}
                      </div>
                    )
                  ) : (
                    <div className="text-center py-8 text-gray-500 text-sm font-mono">
                      {'{ '}Social profiles are private{' }'}
                    </div>
                  )}
                </div>
              )}



              {/* Gaming DNA Section */}
              <div>
                <SectionHeader title="Gaming DNA" icon={Cpu} color={themeColors.accent} />
                
                {/* Genres */}
                <div className="p-4 rounded-xl bg-gray-800 border border-gray-700">
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="h-4 w-4" style={{ color: themeColors.secondary }} />
                    <span className="text-xs font-mono text-gray-400">preferred genres[]:</span>
                  </div>
                  {permissions?.canViewFullProfile ? (
                    preferredGenres && preferredGenres.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {preferredGenres.map((genre, index) => (
                          <motion.span
                            key={genre}
                            className="px-3 py-1 text-xs font-medium rounded-full bg-gray-900 border"
                            style={{
                              borderColor: `${themeColors.primary}40`,
                              color: themeColors.accent
                            }}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            {genre}
                          </motion.span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-xs text-gray-500 font-mono">// No genres defined</p>
                    )
                  ) : (
                    <p className="text-xs text-gray-500 font-mono">// Private</p>
                  )}
                </div>
              </div>
            </div>


          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

GamerCard.displayName = 'GamerCard';

export default GamerCard;
