'use client';

import React, { forwardRef, useEffect } from 'react';
import Link from 'next/link';
import { LogOut, LayoutDashboard, Settings, Pencil, User, Terminal, Shield, FileText, Gauge, ChevronRight, Link2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '@/components/layout/Logo';
import TrendingTagsSidebar from '@/components/layout/TrendingTagsSidebar';
import TrendingCreators from '@/components/layout/TrendingCreators';
import TrendingGames from '@/components/layout/TrendingGames';
import AuthModal from '@/components/auth/AuthModal';
import { useAuthContext } from '@/contexts/auth-context';
import '@/components/style/navColors.css';

interface GameStyleUserMenuProps {
  open: boolean;
  onClose: () => void;
}

// Define MenuItemType for better type safety and reusability
interface MenuItemType {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  action?: () => void;
  subItems?: MenuItemType[];
}

// ItemPresentation: Compact gaming-style presentational component (hover effects removed)
const ItemPresentation = ({ item }: { item: MenuItemType }) => (
  <div className="relative flex items-center px-4 py-3 gaming-menu-item">
    <div className="relative z-10 flex items-center w-full">
      <div className="mr-3 p-1.5 rounded-md bg-slate-800/50 gaming-menu-icon">
        {item.icon}
      </div>
      <div className="flex-1">
        <div className="font-mono text-sm font-medium text-slate-200">
          <span className="ml-1.5">{item.label}</span>
        </div>
      </div>
    </div>
  </div>
);

// UserProfileIcon: User profile picture that fits in the icon container
const UserProfileIcon = ({ user }: { user: any }) => (
  <div className="relative">
    <img
      src={user?.avatarUrl || "/imgs/profile.svg"}
      alt="User Profile Picture"
      className="rounded-md w-4 h-4"
    />
    <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-cyan-400 rounded-full border border-slate-800"></div>
  </div>
);

// UserProfileItem: User profile presentation with glitch hover effect
const UserProfileItem = ({ user, onClose }: { user: any; onClose: () => void }) => {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <Link
      href={`/u/${user?.slug || '#'}`}
      className="block"
      onClick={onClose}
    >
      <div 
        className="relative flex items-center px-4 py-3 gaming-menu-item"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <style jsx>{`
          .glitch-text {
            position: relative;
            display: inline-block;
          }
          
          .glitch-text.active::before,
          .glitch-text.active::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
          
          .glitch-text.active::before {
            animation: glitch-1 0.3s infinite;
            color: #ff00ff;
            z-index: -1;
          }
          
          .glitch-text.active::after {
            animation: glitch-2 0.3s infinite;
            color: #00ffff;
            z-index: -2;
          }
          
          @keyframes glitch-1 {
            0%, 14%, 15%, 49%, 50%, 99%, 100% {
              transform: translate(0);
            }
            15%, 49% {
              transform: translate(-2px, -1px);
            }
          }
          
          @keyframes glitch-2 {
            0%, 20%, 21%, 62%, 63%, 99%, 100% {
              transform: translate(0);
            }
            21%, 62% {
              transform: translate(2px, 1px);
            }
          }
        `}</style>
        
        <div className="relative z-10 flex items-center w-full">
          <div className="mr-3 w-7 h-7 rounded-md bg-slate-800/50 gaming-menu-icon overflow-hidden">
            <img
              src={user?.avatarUrl || "/imgs/profile.svg"}
              alt="User Profile Picture"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <div className="font-mono text-sm font-medium text-slate-200">
              <span
                className={`mx-1 glitch-text ${isHovered ? 'active' : ''}`}
                data-text={isHovered ? "Profile" : (user?.displayName || user?.userName || "User")}
              >
                {isHovered ? "Profile" : (user?.displayName || user?.userName || "User")}
              </span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

// MenuItem component: Gaming aesthetics with submenu support
const MenuItem = ({ item, onClose }: { item: MenuItemType; onClose: () => void; }) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Handle items with submenus
  if (item.subItems && item.subItems.length > 0) {
    return (
      <div>
        <div
          className="cursor-pointer"
          onClick={() => setIsExpanded(!isExpanded)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setIsExpanded(!isExpanded);
            }
          }}
        >
          <div className="relative flex items-center px-4 py-3 gaming-menu-item">
            <div className="relative z-10 flex items-center w-full">
              <div className="mr-3 p-1.5 rounded-md bg-slate-800/50 gaming-menu-icon">
                {item.icon}
              </div>
              <div className="flex-1">
                <div className="font-mono text-sm font-medium text-slate-200">
                  <span className="ml-1.5">{item.label}</span>
                </div>
              </div>
              <ChevronRight
                size={14}
                className={`text-slate-400 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
              />
            </div>
          </div>
        </div>

        {/* Submenu */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="ml-6 border-l border-slate-700/50">
                {item.subItems.map((subItem) => (
                  <div key={subItem.id} className="pl-4">
                    <MenuItem item={subItem} onClose={onClose} />
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Handle regular items with href
  if (item.href) {
    return (
      <Link
        href={item.href}
        className="block"
        onClick={() => {
          if (item.action) {
            item.action(); // Execute action if item has both href and action
          }
          onClose(); // Close menu on navigation
        }}
      >
        <ItemPresentation item={item} />
      </Link>
    );
  } else if (item.action) {
    const commonProps = {
      onClick: () => {
        if (item.action) item.action();
        onClose(); // Close menu after action
      },
      onKeyDown: (e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault(); // Prevent page scroll on Space key
          if (item.action) item.action();
          onClose();
        }
      },
      role: "button",
      tabIndex: 0,
    };

    if (item.id === 'login-register') {
      return (
        <div className="px-4 py-3">
          <div
            {...commonProps}
            className="elegant-login-button relative bg-slate-800/60 text-slate-200 flex items-center justify-center px-4 py-3 rounded-lg cursor-pointer border border-slate-600/40"
          >
            <div className="flex items-center">
              {item.icon && <div className="mr-3 opacity-70">{item.icon}</div>}
              <div>
                <div className="font-mono text-sm font-medium">{item.label}</div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Default rendering for other action items
    return (
      <div
        className="cursor-pointer"
        {...commonProps}
      >
        <ItemPresentation item={item} />
      </div>
    );
  }
  // Fallback for items with no href and no action (renders nothing)
  return null;
};

const GameStyleUserMenu = forwardRef<HTMLDivElement, GameStyleUserMenuProps>(
  ({ open, onClose }, ref) => {
    const { user, loading, isAdmin, authVersion, signOut } = useAuthContext();
    const [isAuthModalOpen, setIsAuthModalOpen] = React.useState(false);



    useEffect(() => {
      const handleEscKey = (e: KeyboardEvent) => {
        if (e.key === 'Escape' && open) {
          onClose();
        }
      };
      window.addEventListener('keydown', handleEscKey);
      return () => window.removeEventListener('keydown', handleEscKey);
    }, [open, onClose]);

    const handleLogout = async () => {
      try {
        await signOut();
        onClose();
      } catch (error) {
        console.error('Logout failed:', error);
      }
    };



    // visibleMenuItems dynamically generated based on user auth state
    const visibleMenuItems: MenuItemType[] = loading
      ? [
          {
            id: 'loading',
            label: 'Loading...',
            icon: <Terminal size={16} />,
            action: () => {},
          },
        ]
      : user
      ? [
          {
            id: 'user-profile',
            label: user.displayName || user.userName || "User",
            icon: <UserProfileIcon user={user} />,
            href: `/u/${user.slug || '#'}`
          },
          { 
            id: 'new-review', 
            label: "New Review", 
            icon: <Pencil size={16} />, 
            href: '/reviews/new'
          },
          {
            id: 'dashboard',
            label: "Dashboard",
            icon: <LayoutDashboard size={16} />,
            href: '/u/dashboard',
            subItems: [
              {
                id: 'dashboard-profile',
                label: 'Profile',
                icon: <Settings size={14} />,
                href: '/u/dashboard?tab=settings'
              },
              {
                id: 'dashboard-privacy',
                label: 'Privacy',
                icon: <Shield size={14} />,
                href: '/u/dashboard?tab=privacy'
              },
              {
                id: 'dashboard-reviews',
                label: 'My Reviews',
                icon: <FileText size={14} />,
                href: '/u/dashboard?tab=reviews'
              },
              {
                id: 'dashboard-performance',
                label: 'Performance',
                icon: <Gauge size={14} />,
                href: '/u/dashboard?tab=performance'
              },
              {
                id: 'dashboard-connections',
                label: 'Connections',
                icon: <Link2 size={14} />,
                href: '/u/dashboard?tab=connections'
              }
            ]
          },
          { 
            id: 'settings', 
            label: "Settings", 
            icon: <Settings size={16} />, 
            href: '/settings'
          },
          ...(isAdmin ? [{
            id: 'admin-panel',
            label: 'Admin Panel',
            icon: <Shield size={16} />,
            href: '/admin',
          }] : []),
          {
            id: 'logout',
            label: 'Logout',
            icon: <LogOut size={16} />,
            action: handleLogout,
          },
        ]
      : [ // Menu items for logged-out users
          {
            id: 'login-register',
            label: "Login/Register",
            icon: <Terminal size={16} />,
            action: () => setIsAuthModalOpen(true),
          },
        ];



    return (
      <> {/* Use a Fragment to return multiple top-level elements */}
        <div ref={ref}> {/* This div is for the ref (e.g., for click-outside detection) */}
          {/* Overlay */}
          {open && (
            <div
              className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40"
              onClick={onClose} // Clicking overlay closes the menu
            />
          )}

          {/* Sidebar Menu */}
          <AnimatePresence>
            {open && (
              <motion.div
                initial={{ x: "-100%" }}
                animate={{ x: 0 }}
                exit={{ x: "-100%" }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="fixed top-0 left-0 h-screen z-50 w-72 gaming-menu-container overflow-y-auto flex flex-col"
                style={{ boxShadow: 'none' }}
                role="dialog"
                aria-modal="true"
                aria-labelledby="user-menu-header-label"
              >
                <style jsx>{`
                  .gaming-menu-container {
                    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(20, 23, 38, 0.92) 100%);
                    backdrop-filter: blur(20px);
                    box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
                    scrollbar-width: thin;
                    scrollbar-color: rgba(139, 92, 246, 0.4) transparent;
                  }

                  .gaming-menu-container::-webkit-scrollbar {
                    width: 8px;
                  }

                  .gaming-menu-container::-webkit-scrollbar-track {
                    background: rgba(15, 23, 42, 0.3);
                    border-radius: 4px;
                  }

                  .gaming-menu-container::-webkit-scrollbar-thumb {
                    background: linear-gradient(to bottom, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.4));
                    border-radius: 4px;
                    border: 1px solid rgba(139, 92, 246, 0.2);
                  }

                  .gaming-menu-container::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: 
                      radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 80% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
                    pointer-events: none;
                  }

                  .elegant-login-button {
                    backdrop-filter: blur(10px);
                  }

                  .sidebar-section {
                    padding: 0 0px;
                  }
                `}</style>

                {/* Logo Header Section */}
                <div className="h-14 flex items-center px-4 flex-shrink-0">
                  <motion.div
                    initial={{
                      opacity: 0,
                      scale: 0.95,
                      x: -10
                    }}
                    animate={{
                      opacity: 1,
                      scale: 1,
                      x: 0
                    }}
                    exit={{
                      opacity: 0,
                      scale: 0.95,
                      x: -10
                    }}
                    transition={{
                      duration: 0.3,
                      delay: 0.15,
                      ease: [0.34, 1.56, 0.64, 1]
                    }}
                  >
                    <Logo size="sm" noLink />
                  </motion.div>
                </div>

                {/* Scrollable Content Container */}
                <div className="flex-1 flex flex-col min-h-0">
                  {/* User Profile Section (if authenticated) */}
                  {user && (
                    <UserProfileItem key={`profile-${authVersion}`} user={user} onClose={onClose} />
                  )}

                  {/* Compact Navigation Section - No Title */}
                  <nav className="relative z-10 pb-4 flex-shrink-0 sidebar-section" aria-label="User menu actions" key={`nav-${authVersion}`}>
                    {visibleMenuItems.filter(item => item.id !== 'user-profile').map((item) => (
                      <MenuItem
                        key={`${item.id}-${authVersion}`}
                        item={item}
                        onClose={onClose}
                      />
                    ))}
                  </nav>

                  {/* Trending Games Section */}
                  <div className="flex-shrink-0 text-sm sidebar-section">
                    <TrendingGames />
                  </div>

                  {/* Trending Creators Section */}
                  <div className="flex-shrink-0 text-sm sidebar-section">
                    <TrendingCreators />
                  </div>

                  {/* Trending Tags Section */}
                  <div className="flex-shrink-0 text-sm sidebar-section">
                  <div className="pb-12">
  <TrendingTagsSidebar />
</div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* AuthModal is rendered as a sibling, managed by its own state */}
        <AuthModal isOpen={isAuthModalOpen} onClose={() => setIsAuthModalOpen(false)} />
      </>
    );
  }
);

GameStyleUserMenu.displayName = 'GameStyleUserMenu';

export default GameStyleUserMenu;