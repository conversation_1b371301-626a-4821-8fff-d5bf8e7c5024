'use client';

import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  MoreHorizontal,
  CheckCircle,
  Trash2,
  Pin,
  PinOff,
  Flag,
  AlertTriangle,
  ExternalLink,
} from 'lucide-react';
import Link from 'next/link';
import type { CommentModerationData, CommentModerationAction } from '@/lib/admin/commentService';

interface CommentActionsDropdownProps {
  comment: CommentModerationData;
  onModerate: (commentId: string, action: CommentModerationAction) => Promise<void>;
  compact?: boolean;
  disabled?: boolean;
}

export function CommentActionsDropdown({
  comment,
  onModerate,
  compact = false,
  disabled = false,
}: CommentActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    action: CommentModerationAction | null;
    title: string;
    description: string;
    variant: 'default' | 'destructive';
  }>({
    isOpen: false,
    action: null,
    title: '',
    description: '',
    variant: 'default',
  });

  const handleAction = async (action: CommentModerationAction) => {
    setIsOpen(false);
    
    // Show confirmation for destructive actions
    if (action.action === 'delete') {
      setConfirmDialog({
        isOpen: true,
        action,
        title: 'Delete Comment',
        description: 'This will permanently delete this comment. This action cannot be undone.',
        variant: 'destructive',
      });
      return;
    }

    if (action.action === 'warn_user') {
      setConfirmDialog({
        isOpen: true,
        action,
        title: 'Warn User',
        description: 'This will send a warning notification to the user about their comment behavior.',
        variant: 'default',
      });
      return;
    }

    // Execute action immediately for non-destructive actions
    await onModerate(comment.id, action);
  };

  const handleConfirmAction = async () => {
    if (confirmDialog.action) {
      await onModerate(comment.id, confirmDialog.action);
    }
    setConfirmDialog({
      isOpen: false,
      action: null,
      title: '',
      description: '',
      variant: 'default',
    });
  };

  return (
    <TooltipProvider>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size={compact ? "sm" : "icon"}
                className="hover:bg-muted/50 transition-colors"
                disabled={disabled}
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open comment actions menu</span>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Comment Actions</p>
          </TooltipContent>
        </Tooltip>
        
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="font-semibold text-primary">
            Comment Moderation
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Review Link */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem asChild>
                <Link
                  href={`/reviews/${comment.review_slug}`}
                  target="_blank"
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Review
                </Link>
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Open the review this comment belongs to</p>
            </TooltipContent>
          </Tooltip>
          
          <DropdownMenuSeparator />
          
          {/* Primary Actions */}
          {!comment.is_deleted && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction({ action: 'approve' })}
                  className="text-green-600 hover:text-green-700 hover:bg-green-50 cursor-pointer"
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve Comment
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Approve this comment and clear any flags</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          {/* Pin/Unpin Action */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({
                  action: comment.is_pinned ? 'unpin' : 'pin'
                })}
                className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50 cursor-pointer"
              >
                {comment.is_pinned ? (
                  <>
                    <PinOff className="mr-2 h-4 w-4" />
                    Unpin Comment
                  </>
                ) : (
                  <>
                    <Pin className="mr-2 h-4 w-4" />
                    Pin Comment
                  </>
                )}
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>
                {comment.is_pinned 
                  ? 'Remove pin from this comment' 
                  : 'Pin this comment to the top'}
              </p>
            </TooltipContent>
          </Tooltip>
          
          <DropdownMenuSeparator />
          
          {/* Warning and Flag Actions */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({ action: 'flag' })}
                className="text-orange-600 hover:text-orange-700 hover:bg-orange-50 cursor-pointer"
              >
                <Flag className="mr-2 h-4 w-4" />
                Flag Comment
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Flag this comment for manual review</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({ action: 'warn_user' })}
                className="text-amber-600 hover:text-amber-700 hover:bg-amber-50 cursor-pointer"
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                Warn User
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Send a warning to the comment author</p>
            </TooltipContent>
          </Tooltip>
          
          <DropdownMenuSeparator />
          
          {/* Destructive Actions */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction({ action: 'delete' })}
                className="text-destructive hover:text-destructive/80 hover:bg-destructive/10 cursor-pointer"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Comment
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Permanently delete this comment</p>
            </TooltipContent>
          </Tooltip>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => !open && setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              {confirmDialog.title}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAction}
              className={confirmDialog.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </TooltipProvider>
  );
} 