-- AI Detection Voting System Migration
-- This creates tables for users to vote on whether reviews are AI-generated

-- Create ai_detection_votes table
CREATE TABLE IF NOT EXISTS ai_detection_votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vote_type TEXT NOT NULL CHECK (vote_type IN ('human', 'ai', 'unsure')),
    confidence_score INTEGER DEFAULT NULL CHECK (confidence_score >= 1 AND confidence_score <= 10),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Ensure one vote per user per review
    UNIQUE(review_id, user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_detection_votes_review_id ON ai_detection_votes(review_id);
CREATE INDEX IF NOT EXISTS idx_ai_detection_votes_user_id ON ai_detection_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_detection_votes_vote_type ON ai_detection_votes(vote_type);
CREATE INDEX IF NOT EXISTS idx_ai_detection_votes_created_at ON ai_detection_votes(created_at);

-- Create user_trust_scores table for tracking user accuracy over time
CREATE TABLE IF NOT EXISTS user_trust_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    accuracy_score DECIMAL(5,2) DEFAULT 50.00 CHECK (accuracy_score >= 0 AND accuracy_score <= 100),
    total_votes INTEGER DEFAULT 0,
    correct_votes INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create index for user trust scores
CREATE INDEX IF NOT EXISTS idx_user_trust_scores_user_id ON user_trust_scores(user_id);
CREATE INDEX IF NOT EXISTS idx_user_trust_scores_accuracy ON user_trust_scores(accuracy_score);

-- Add AI detection stats to reviews table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reviews' AND column_name = 'ai_human_confidence') THEN
        ALTER TABLE reviews ADD COLUMN ai_human_confidence INTEGER DEFAULT 50 CHECK (ai_human_confidence >= 0 AND ai_human_confidence <= 100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reviews' AND column_name = 'ai_vote_count') THEN
        ALTER TABLE reviews ADD COLUMN ai_vote_count INTEGER DEFAULT 0;
    END IF;
END $$;

-- Function to update review AI confidence score
CREATE OR REPLACE FUNCTION update_review_ai_confidence(review_uuid UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE reviews 
    SET 
        ai_human_confidence = (
            SELECT CASE 
                WHEN COUNT(*) = 0 THEN 50
                ELSE ROUND(
                    ((COUNT(*) FILTER (WHERE vote_type = 'human') + 
                      COUNT(*) FILTER (WHERE vote_type = 'unsure') * 0.5) / COUNT(*)) * 100
                )::INTEGER
            END
            FROM ai_detection_votes 
            WHERE review_id = review_uuid
        ),
        ai_vote_count = (
            SELECT COUNT(*) 
            FROM ai_detection_votes 
            WHERE review_id = review_uuid
        )
    WHERE id = review_uuid;
END;
$$;

-- Function to update user trust score (for future accuracy tracking)
CREATE OR REPLACE FUNCTION update_user_trust_score(user_uuid UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO user_trust_scores (user_id, total_votes, last_updated)
    VALUES (
        user_uuid,
        (SELECT COUNT(*) FROM ai_detection_votes WHERE user_id = user_uuid),
        timezone('utc'::text, now())
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        total_votes = (SELECT COUNT(*) FROM ai_detection_votes WHERE user_id = user_uuid),
        last_updated = timezone('utc'::text, now());
END;
$$;

-- Trigger to automatically update review AI confidence when votes change
CREATE OR REPLACE FUNCTION trigger_update_ai_confidence()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update the review's AI confidence score
    PERFORM update_review_ai_confidence(
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.review_id
            ELSE NEW.review_id
        END
    );
    
    -- Update user trust score
    PERFORM update_user_trust_score(
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.user_id
            ELSE NEW.user_id
        END
    );
    
    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$;

-- Create trigger for AI detection votes
DROP TRIGGER IF EXISTS trigger_ai_detection_vote_update ON ai_detection_votes;
CREATE TRIGGER trigger_ai_detection_vote_update
    AFTER INSERT OR UPDATE OR DELETE ON ai_detection_votes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_ai_confidence();

-- Row Level Security (RLS) policies
ALTER TABLE ai_detection_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_trust_scores ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view all AI detection votes (for stats)
CREATE POLICY "Allow read access to ai_detection_votes" ON ai_detection_votes
    FOR SELECT USING (true);

-- Policy: Users can only insert/update their own votes
CREATE POLICY "Allow users to manage their own ai_detection_votes" ON ai_detection_votes
    FOR ALL USING (auth.uid() = user_id);

-- Policy: Users can view all trust scores
CREATE POLICY "Allow read access to user_trust_scores" ON user_trust_scores
    FOR SELECT USING (true);

-- Policy: Users can only update their own trust scores (via functions)
CREATE POLICY "Allow users to update own trust_scores" ON user_trust_scores
    FOR ALL USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT SELECT ON ai_detection_votes TO authenticated;
GRANT INSERT, UPDATE, DELETE ON ai_detection_votes TO authenticated;
GRANT SELECT ON user_trust_scores TO authenticated;
GRANT UPDATE ON user_trust_scores TO authenticated;

-- Comments for documentation
COMMENT ON TABLE ai_detection_votes IS 'Stores user votes on whether reviews are AI-generated or human-written';
COMMENT ON TABLE user_trust_scores IS 'Tracks user accuracy in AI detection for reputation system';
COMMENT ON COLUMN reviews.ai_human_confidence IS 'Percentage confidence that review is human-written (0-100)';
COMMENT ON COLUMN reviews.ai_vote_count IS 'Total number of AI detection votes received';

-- Insert some example data for testing (optional)
-- Note: This would only work if the referenced users and reviews exist
-- INSERT INTO ai_detection_votes (review_id, user_id, vote_type) VALUES
-- ('sample-review-uuid', 'sample-user-uuid', 'human');

-- Verification queries (uncomment to run diagnostics)
-- SELECT 'ai_detection_votes table created' as status, COUNT(*) as count FROM ai_detection_votes;
-- SELECT 'user_trust_scores table created' as status, COUNT(*) as count FROM user_trust_scores;