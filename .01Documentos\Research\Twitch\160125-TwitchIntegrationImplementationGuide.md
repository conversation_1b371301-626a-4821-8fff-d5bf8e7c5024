# 🎮 TWITCH INTEGRATION IMPLEMENTATION GUIDE
**CriticalPixel - Complete Twitch Module Implementation**

*Document Version: 1.0*  
*Created: January 16, 2025*  
*Author: AI Development Assistant*

---

## 📋 EXECUTIVE SUMMARY

This guide provides a comprehensive implementation plan for integrating Twitch functionality into the CriticalPixel platform. The implementation will mirror the existing YouTube module architecture while adding Twitch-specific features including live streaming status and clips integration.

### 🎯 Project Objectives

1. **Twitch Account Linking**: OAuth 2.0 integration for secure account connection
2. **Live Stream Status**: Real-time display of user's streaming status
3. **Clips Showcase**: Display recent Twitch clips in user profiles
4. **Dashboard Configuration**: Settings panel for Twitch module management
5. **Profile Integration**: Seamless integration with existing profile system

### 🏆 Key Features

- **Live Status Indicator**: Show "🔴 LIVE" next to username when streaming
- **Clips Gallery**: Display recent clips with view counts and thumbnails
- **Dashboard Settings**: Configure privacy, display options, and refresh rates
- **OAuth Security**: Secure token management with automatic refresh
- **Mobile Responsive**: Full mobile and desktop compatibility

---

## 🏗️ TECHNICAL ARCHITECTURE

### 📊 Database Schema

#### 1. User Twitch Data Table
```sql
-- Store user Twitch account information and OAuth tokens
CREATE TABLE user_twitch_data (
    user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    twitch_user_id VARCHAR(50) NOT NULL UNIQUE,
    username VARCHAR(25) NOT NULL,
    display_name VARCHAR(50),
    profile_image_url TEXT,
    description TEXT,
    broadcaster_type VARCHAR(20) DEFAULT 'affiliate',
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    scopes TEXT[] DEFAULT ARRAY['user:read:email', 'clips:read'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_fetched TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_valid BOOLEAN DEFAULT TRUE,
    error_message TEXT
);

-- Index for performance
CREATE INDEX idx_user_twitch_data_twitch_user_id ON user_twitch_data(twitch_user_id);
CREATE INDEX idx_user_twitch_data_updated_at ON user_twitch_data(updated_at);
```

#### 2. Twitch Clips Cache Table
```sql
-- Cache Twitch clips data for performance
CREATE TABLE user_twitch_clips (
    id VARCHAR(36) PRIMARY KEY, -- Twitch clip ID
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    thumbnail_url TEXT NOT NULL,
    embed_url TEXT NOT NULL,
    url TEXT NOT NULL,
    duration DECIMAL(6,2) DEFAULT 0,
    game_id VARCHAR(20),
    game_name VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    creator_id VARCHAR(50),
    creator_name VARCHAR(25),
    fetched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_user_twitch_clips_user_id ON user_twitch_clips(user_id);
CREATE INDEX idx_user_twitch_clips_created_at ON user_twitch_clips(created_at DESC);
CREATE INDEX idx_user_twitch_clips_fetched_at ON user_twitch_clips(fetched_at);
```

#### 3. Stream Status Tracking Table
```sql
-- Track live streaming status for real-time updates
CREATE TABLE user_twitch_stream_status (
    user_id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    is_live BOOLEAN DEFAULT FALSE,
    stream_id VARCHAR(50),
    stream_title VARCHAR(140),
    game_id VARCHAR(20),
    game_name VARCHAR(100),
    viewer_count INTEGER DEFAULT 0,
    language VARCHAR(10) DEFAULT 'en',
    thumbnail_url TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for live status queries
CREATE INDEX idx_user_twitch_stream_status_is_live ON user_twitch_stream_status(is_live);
CREATE INDEX idx_user_twitch_stream_status_last_checked ON user_twitch_stream_status(last_checked);
```

#### 4. Content Preferences Updates
```sql
-- Add Twitch module settings to existing user_content_preferences table
ALTER TABLE user_content_preferences 
ADD COLUMN twitch_module JSONB DEFAULT '{
    "enabled": false,
    "visibility": "public",
    "maxClips": 12,
    "showStats": true,
    "showStreamStatus": true,
    "autoRefresh": true,
    "refreshInterval": 300
}';
```

### 🔧 TypeScript Interfaces

#### Core Twitch Types (`src/types/user-content.ts`)
```typescript
// Twitch User Data Interface
export interface UserTwitchData {
  userId: string;
  twitchUserId: string;
  username: string;
  displayName: string;
  profileImageUrl?: string;
  description?: string;
  broadcasterType: 'partner' | 'affiliate' | '';
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: string;
  scopes: string[];
  createdAt: string;
  updatedAt: string;
  lastFetched: string;
  isValid: boolean;
  error?: string;
}

// Twitch Clip Interface
export interface UserTwitchClip {
  id: string;
  title: string;
  viewCount: number;
  createdAt: string;
  thumbnailUrl: string;
  embedUrl: string;
  url: string;
  duration: number;
  gameId?: string;
  gameName?: string;
  language: string;
  creatorId: string;
  creatorName: string;
}

// Stream Status Interface
export interface UserTwitchStreamStatus {
  userId: string;
  isLive: boolean;
  streamId?: string;
  streamTitle?: string;
  gameId?: string;
  gameName?: string;
  viewerCount: number;
  language: string;
  thumbnailUrl?: string;
  startedAt?: string;
  lastChecked: string;
}

// Twitch Module Settings
export interface TwitchModuleSettings {
  enabled: boolean;
  visibility: 'public' | 'friends' | 'private';
  maxClips: number;
  showStats: boolean;
  showStreamStatus: boolean;
  autoRefresh: boolean;
  refreshInterval: number; // seconds
}

// API Response Types
export interface GetTwitchDataResponse {
  success: boolean;
  data?: UserTwitchData;
  error?: string;
  cached?: boolean;
}

export interface ConnectTwitchResponse {
  success: boolean;
  data?: UserTwitchData;
  error?: string;
}

export interface TwitchClipsResponse {
  success: boolean;
  data?: UserTwitchClip[];
  error?: string;
  pagination?: {
    cursor?: string;
    hasMore: boolean;
  };
}
```

---

## 🔐 OAUTH 2.0 IMPLEMENTATION

### Environment Variables Setup
```bash
# Add to .env.local
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret
TWITCH_REDIRECT_URI=http://localhost:9003/api/auth/twitch/callback
```

### OAuth Flow Implementation (`src/lib/twitch/oauth.ts`)
```typescript
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface TwitchTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  scope: string[];
  token_type: string;
}

interface TwitchUserResponse {
  data: Array<{
    id: string;
    login: string;
    display_name: string;
    type: string;
    broadcaster_type: string;
    description: string;
    profile_image_url: string;
    offline_image_url: string;
    view_count: number;
    created_at: string;
  }>;
}

export class TwitchOAuth {
  private static readonly BASE_URL = 'https://api.twitch.tv/helix';
  private static readonly AUTH_URL = 'https://id.twitch.tv/oauth2';
  
  static generateAuthUrl(state: string): string {
    const params = new URLSearchParams({
      client_id: process.env.TWITCH_CLIENT_ID!,
      redirect_uri: process.env.TWITCH_REDIRECT_URI!,
      response_type: 'code',
      scope: 'user:read:email clips:read',
      state,
    });
    
    return `${this.AUTH_URL}/authorize?${params.toString()}`;
  }

  static async exchangeCodeForTokens(code: string): Promise<TwitchTokenResponse> {
    const response = await fetch(`${this.AUTH_URL}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.TWITCH_CLIENT_ID!,
        client_secret: process.env.TWITCH_CLIENT_SECRET!,
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.TWITCH_REDIRECT_URI!,
      }),
    });

    if (!response.ok) {
      throw new Error(`Token exchange failed: ${response.statusText}`);
    }

    return response.json();
  }

  static async refreshAccessToken(refreshToken: string): Promise<TwitchTokenResponse> {
    const response = await fetch(`${this.AUTH_URL}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.TWITCH_CLIENT_ID!,
        client_secret: process.env.TWITCH_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
      }),
    });

    if (!response.ok) {
      throw new Error(`Token refresh failed: ${response.statusText}`);
    }

    return response.json();
  }

  static async validateToken(accessToken: string): Promise<boolean> {
    const response = await fetch(`${this.AUTH_URL}/validate`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    return response.ok;
  }

  static async getUserData(accessToken: string): Promise<TwitchUserResponse> {
    const response = await fetch(`${this.BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Client-Id': process.env.TWITCH_CLIENT_ID!,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user data: ${response.statusText}`);
    }

    return response.json();
  }
}
```

---

## 🖥️ BACKEND IMPLEMENTATION

### Server Actions (`src/app/u/actions-twitch.ts`)
```typescript
'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { TwitchOAuth } from '@/lib/twitch/oauth';
import { TwitchAPI } from '@/lib/twitch/api';
import type {
  UserTwitchData,
  TwitchModuleSettings,
  GetTwitchDataResponse,
  ConnectTwitchResponse,
  TwitchClipsResponse
} from '@/types/user-content';

// Get Supabase client
async function getSupabaseClient() {
  const cookieStore = await cookies();
  return createServerClient(cookieStore);
}

/**
 * Initiate Twitch OAuth connection
 */
export async function initiateTwitchConnection(userId: string): Promise<{ authUrl: string; state: string }> {
  const state = `${userId}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  const authUrl = TwitchOAuth.generateAuthUrl(state);
  
  // Store state in session or database for validation
  const cookieStore = await cookies();
  cookieStore.set('twitch_oauth_state', state, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 600, // 10 minutes
  });
  
  return { authUrl, state };
}

/**
 * Complete Twitch OAuth flow
 */
export async function completeTwitchConnection(
  userId: string,
  code: string,
  state: string
): Promise<ConnectTwitchResponse> {
  try {
    // Validate state
    const cookieStore = await cookies();
    const storedState = cookieStore.get('twitch_oauth_state')?.value;
    
    if (!storedState || storedState !== state) {
      return { success: false, error: 'Invalid OAuth state' };
    }

    // Exchange code for tokens
    const tokens = await TwitchOAuth.exchangeCodeForTokens(code);
    
    // Get user data from Twitch
    const userData = await TwitchOAuth.getUserData(tokens.access_token);
    const user = userData.data[0];

    // Calculate token expiration
    const expiresAt = new Date(Date.now() + tokens.expires_in * 1000);

    // Save to database
    const supabase = await getSupabaseClient();
    
    const twitchData = {
      user_id: userId,
      twitch_user_id: user.id,
      username: user.login,
      display_name: user.display_name,
      profile_image_url: user.profile_image_url,
      description: user.description,
      broadcaster_type: user.broadcaster_type,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      token_expires_at: expiresAt.toISOString(),
      scopes: tokens.scope,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase
      .from('user_twitch_data')
      .upsert(twitchData, { onConflict: 'user_id' });

    if (error) {
      console.error('Error saving Twitch data:', error);
      return { success: false, error: 'Failed to save Twitch connection' };
    }

    // Clear OAuth state
    cookieStore.delete('twitch_oauth_state');

    return {
      success: true,
      data: {
        userId,
        twitchUserId: user.id,
        username: user.login,
        displayName: user.display_name,
        profileImageUrl: user.profile_image_url,
        description: user.description,
        broadcasterType: user.broadcaster_type,
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        tokenExpiresAt: expiresAt.toISOString(),
        scopes: tokens.scope,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastFetched: new Date().toISOString(),
        isValid: true,
      },
    };
  } catch (error) {
    console.error('Error completing Twitch connection:', error);
    return { success: false, error: 'Failed to connect Twitch account' };
  }
}

/**
 * Get user's Twitch data
 */
export async function getUserTwitchData(userId: string): Promise<GetTwitchDataResponse> {
  try {
    const supabase = await getSupabaseClient();

    const { data: twitchData, error } = await supabase
      .from('user_twitch_data')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching Twitch data:', error);
      return { success: false, error: 'Failed to fetch Twitch data' };
    }

    if (!twitchData) {
      return { success: false, error: 'No Twitch account connected' };
    }

    // Check if token needs refresh
    const now = new Date();
    const expiresAt = new Date(twitchData.token_expires_at);
    
    if (now >= expiresAt) {
      // Token expired, try to refresh
      try {
        const newTokens = await TwitchOAuth.refreshAccessToken(twitchData.refresh_token);
        const newExpiresAt = new Date(Date.now() + newTokens.expires_in * 1000);

        // Update tokens in database
        await supabase
          .from('user_twitch_data')
          .update({
            access_token: newTokens.access_token,
            refresh_token: newTokens.refresh_token,
            token_expires_at: newExpiresAt.toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('user_id', userId);

        twitchData.access_token = newTokens.access_token;
        twitchData.refresh_token = newTokens.refresh_token;
        twitchData.token_expires_at = newExpiresAt.toISOString();
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return { success: false, error: 'Twitch connection expired. Please reconnect.' };
      }
    }

    return {
      success: true,
      data: {
        userId: twitchData.user_id,
        twitchUserId: twitchData.twitch_user_id,
        username: twitchData.username,
        displayName: twitchData.display_name,
        profileImageUrl: twitchData.profile_image_url,
        description: twitchData.description,
        broadcasterType: twitchData.broadcaster_type,
        accessToken: twitchData.access_token,
        refreshToken: twitchData.refresh_token,
        tokenExpiresAt: twitchData.token_expires_at,
        scopes: twitchData.scopes,
        createdAt: twitchData.created_at,
        updatedAt: twitchData.updated_at,
        lastFetched: twitchData.last_fetched,
        isValid: twitchData.is_valid,
        error: twitchData.error_message,
      },
      cached: true,
    };
  } catch (error) {
    console.error('Error getting Twitch data:', error);
    return { success: false, error: 'Failed to get Twitch data' };
  }
}

/**
 * Disconnect Twitch account
 */
export async function disconnectTwitchAccount(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await getSupabaseClient();

    // Delete all Twitch data for user
    const { error: dataError } = await supabase
      .from('user_twitch_data')
      .delete()
      .eq('user_id', userId);

    if (dataError) {
      console.error('Error deleting Twitch data:', dataError);
      return { success: false, error: 'Failed to disconnect Twitch account' };
    }

    // Delete clips cache
    await supabase
      .from('user_twitch_clips')
      .delete()
      .eq('user_id', userId);

    // Delete stream status
    await supabase
      .from('user_twitch_stream_status')
      .delete()
      .eq('user_id', userId);

    // Update content preferences
    await supabase
      .from('user_content_preferences')
      .update({
        twitch_module: {
          enabled: false,
          visibility: 'public',
          maxClips: 12,
          showStats: true,
          showStreamStatus: true,
          autoRefresh: true,
          refreshInterval: 300,
        },
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    return { success: true };
  } catch (error) {
    console.error('Error disconnecting Twitch account:', error);
    return { success: false, error: 'Failed to disconnect Twitch account' };
  }
}
```

### Twitch API Service (`src/lib/twitch/api.ts`)
```typescript
import type { UserTwitchClip, UserTwitchStreamStatus } from '@/types/user-content';

interface TwitchClipsResponse {
  data: Array<{
    id: string;
    url: string;
    embed_url: string;
    broadcaster_id: string;
    broadcaster_name: string;
    creator_id: string;
    creator_name: string;
    video_id: string;
    game_id: string;
    language: string;
    title: string;
    view_count: number;
    created_at: string;
    thumbnail_url: string;
    duration: number;
  }>;
  pagination?: {
    cursor?: string;
  };
}

interface TwitchStreamsResponse {
  data: Array<{
    id: string;
    user_id: string;
    user_login: string;
    user_name: string;
    game_id: string;
    game_name: string;
    type: string;
    title: string;
    viewer_count: number;
    started_at: string;
    language: string;
    thumbnail_url: string;
  }>;
}

export class TwitchAPI {
  private static readonly BASE_URL = 'https://api.twitch.tv/helix';

  private static async makeRequest(
    endpoint: string,
    accessToken: string,
    params?: Record<string, string>
  ): Promise<Response> {
    const url = new URL(`${this.BASE_URL}${endpoint}`);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    return fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Client-Id': process.env.TWITCH_CLIENT_ID!,
      },
    });
  }

  static async getUserClips(
    accessToken: string,
    broadcasterId: string,
    limit: number = 20
  ): Promise<UserTwitchClip[]> {
    try {
      const response = await this.makeRequest('/clips', accessToken, {
        broadcaster_id: broadcasterId,
        first: limit.toString(),
        started_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // Last 30 days
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch clips: ${response.statusText}`);
      }

      const data: TwitchClipsResponse = await response.json();

      return data.data.map(clip => ({
        id: clip.id,
        title: clip.title,
        viewCount: clip.view_count,
        createdAt: clip.created_at,
        thumbnailUrl: clip.thumbnail_url,
        embedUrl: clip.embed_url,
        url: clip.url,
        duration: clip.duration,
        gameId: clip.game_id,
        gameName: '', // Will be fetched separately if needed
        language: clip.language,
        creatorId: clip.creator_id,
        creatorName: clip.creator_name,
      }));
    } catch (error) {
      console.error('Error fetching Twitch clips:', error);
      return [];
    }
  }

  static async getStreamStatus(
    accessToken: string,
    userId: string
  ): Promise<UserTwitchStreamStatus | null> {
    try {
      const response = await this.makeRequest('/streams', accessToken, {
        user_id: userId,
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch stream status: ${response.statusText}`);
      }

      const data: TwitchStreamsResponse = await response.json();

      if (data.data.length === 0) {
        // User is not live
        return {
          userId,
          isLive: false,
          viewerCount: 0,
          language: 'en',
          lastChecked: new Date().toISOString(),
        };
      }

      const stream = data.data[0];
      return {
        userId,
        isLive: true,
        streamId: stream.id,
        streamTitle: stream.title,
        gameId: stream.game_id,
        gameName: stream.game_name,
        viewerCount: stream.viewer_count,
        language: stream.language,
        thumbnailUrl: stream.thumbnail_url,
        startedAt: stream.started_at,
        lastChecked: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error fetching stream status:', error);
      return null;
    }
  }

  static async getGameName(accessToken: string, gameId: string): Promise<string> {
    try {
      const response = await this.makeRequest('/games', accessToken, {
        id: gameId,
      });

      if (!response.ok) {
        return '';
      }

      const data = await response.json();
      return data.data[0]?.name || '';
    } catch (error) {
      console.error('Error fetching game name:', error);
      return '';
    }
  }
}
```

---

## 🎨 FRONTEND COMPONENTS

### Dashboard Configuration Component
**File:** `src/components/dashboard/TwitchChannelConfig.tsx`

```typescript
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, ExternalLink, Twitch, AlertCircle, CheckCircle, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  getUserTwitchData, 
  disconnectTwitchAccount,
  initiateTwitchConnection,
  getTwitchSettings,
  saveTwitchSettings
} from '@/app/u/actions-twitch';
import type { UserTwitchData, TwitchModuleSettings } from '@/types/user-content';

interface TwitchChannelConfigProps {
  userId: string;
}

const TwitchChannelConfig: React.FC<TwitchChannelConfigProps> = ({ userId }) => {
  const [twitchData, setTwitchData] = useState<UserTwitchData | null>(null);
  const [settings, setSettings] = useState<TwitchModuleSettings>({
    enabled: false,
    visibility: 'public',
    maxClips: 12,
    showStats: true,
    showStreamStatus: true,
    autoRefresh: true,
    refreshInterval: 300,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setSaving] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadTwitchData();
  }, [userId]);

  const loadTwitchData = async () => {
    setIsLoading(true);
    try {
      const [twitchResponse, settingsResponse] = await Promise.all([
        getUserTwitchData(userId),
        getTwitchSettings(userId),
      ]);

      if (twitchResponse.success) {
        setTwitchData(twitchResponse.data || null);
      }

      if (settingsResponse.success && settingsResponse.data) {
        setSettings(settingsResponse.data);
      }
    } catch (error) {
      console.error('Error loading Twitch data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Twitch configuration',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      const { authUrl } = await initiateTwitchConnection(userId);
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error initiating Twitch connection:', error);
      toast({
        title: 'Connection Error',
        description: 'Failed to connect to Twitch. Please try again.',
        variant: 'destructive',
      });
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!confirm('Are you sure you want to disconnect your Twitch account? This will remove all Twitch content from your profile.')) {
      return;
    }

    setSaving(true);
    try {
      const result = await disconnectTwitchAccount(userId);
      
      if (result.success) {
        setTwitchData(null);
        setSettings(prev => ({ ...prev, enabled: false }));
        toast({
          title: 'Disconnected',
          description: 'Your Twitch account has been disconnected successfully.',
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error disconnecting Twitch:', error);
      toast({
        title: 'Error',
        description: 'Failed to disconnect Twitch account. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      const result = await saveTwitchSettings(userId, settings);
      
      if (result.success) {
        toast({
          title: 'Settings Saved',
          description: 'Your Twitch module settings have been updated.',
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Twitch className="h-5 w-5 text-purple-500" />
            Twitch Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Twitch className="h-5 w-5 text-purple-500" />
          Twitch Integration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {!twitchData ? (
          // Not connected state
          <div className="text-center py-6">
            <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-6 mb-4">
              <Twitch className="h-12 w-12 text-purple-500 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">
                Connect Your Twitch Account
              </h3>
              <p className="text-gray-400 text-sm mb-4">
                Show your live streaming status and recent clips on your profile
              </p>
              <Button
                onClick={handleConnect}
                disabled={isConnecting}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Twitch className="h-4 w-4 mr-2" />
                    Connect Twitch
                  </>
                )}
              </Button>
            </div>
            
            <div className="text-left bg-gray-800/50 rounded-lg p-4">
              <h4 className="font-semibold text-white mb-2">What you'll get:</h4>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Live streaming status on your profile</li>
                <li>• Showcase your best clips</li>
                <li>• Automatic content updates</li>
                <li>• Privacy controls</li>
              </ul>
            </div>
          </div>
        ) : (
          // Connected state
          <div className="space-y-6">
            {/* Connection Status */}
            <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
              <div className="flex items-center gap-3">
                <img
                  src={twitchData.profileImageUrl}
                  alt={twitchData.displayName}
                  className="w-10 h-10 rounded-full"
                />
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-white">{twitchData.displayName}</span>
                    <Badge variant="outline" className="text-green-400 border-green-400">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Connected
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-400">@{twitchData.username}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`https://twitch.tv/${twitchData.username}`, '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Channel
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDisconnect}
                  disabled={isSaving}
                  className="text-red-400 border-red-400 hover:bg-red-400/10"
                >
                  Disconnect
                </Button>
              </div>
            </div>

            {/* Module Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="twitch-enabled" className="text-base font-semibold text-white">
                    Enable Twitch Module
                  </Label>
                  <p className="text-sm text-gray-400">
                    Show Twitch content on your profile
                  </p>
                </div>
                <Switch
                  id="twitch-enabled"
                  checked={settings.enabled}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, enabled: checked }))
                  }
                />
              </div>

              {settings.enabled && (
                <>
                  {/* Visibility Settings */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="visibility" className="text-sm font-medium text-white mb-2 block">
                        Visibility
                      </Label>
                      <Select
                        value={settings.visibility}
                        onValueChange={(value: 'public' | 'friends' | 'private') =>
                          setSettings(prev => ({ ...prev, visibility: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="public">Public</SelectItem>
                          <SelectItem value="friends">Friends Only</SelectItem>
                          <SelectItem value="private">Private</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="max-clips" className="text-sm font-medium text-white mb-2 block">
                        Max Clips to Show
                      </Label>
                      <Select
                        value={settings.maxClips.toString()}
                        onValueChange={(value) =>
                          setSettings(prev => ({ ...prev, maxClips: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="6">6 Clips</SelectItem>
                          <SelectItem value="9">9 Clips</SelectItem>
                          <SelectItem value="12">12 Clips</SelectItem>
                          <SelectItem value="18">18 Clips</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Display Options */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="show-stream-status" className="text-sm font-medium text-white">
                          Show Stream Status
                        </Label>
                        <p className="text-xs text-gray-400">
                          Display live indicator next to your username
                        </p>
                      </div>
                      <Switch
                        id="show-stream-status"
                        checked={settings.showStreamStatus}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, showStreamStatus: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="show-stats" className="text-sm font-medium text-white">
                          Show Clip Statistics
                        </Label>
                        <p className="text-xs text-gray-400">
                          Display view counts and creation dates
                        </p>
                      </div>
                      <Switch
                        id="show-stats"
                        checked={settings.showStats}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, showStats: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-refresh" className="text-sm font-medium text-white">
                          Auto Refresh Content
                        </Label>
                        <p className="text-xs text-gray-400">
                          Automatically update clips and stream status
                        </p>
                      </div>
                      <Switch
                        id="auto-refresh"
                        checked={settings.autoRefresh}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, autoRefresh: checked }))
                        }
                      />
                    </div>
                  </div>

                  {/* Refresh Interval */}
                  {settings.autoRefresh && (
                    <div>
                      <Label htmlFor="refresh-interval" className="text-sm font-medium text-white mb-2 block">
                        Refresh Interval (minutes)
                      </Label>
                      <Select
                        value={(settings.refreshInterval / 60).toString()}
                        onValueChange={(value) =>
                          setSettings(prev => ({ ...prev, refreshInterval: parseInt(value) * 60 }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5 minutes</SelectItem>
                          <SelectItem value="10">10 minutes</SelectItem>
                          <SelectItem value="15">15 minutes</SelectItem>
                          <SelectItem value="30">30 minutes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Save Button */}
            <div className="flex justify-end pt-4 border-t border-gray-700">
              <Button
                onClick={handleSaveSettings}
                disabled={isSaving}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Settings className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TwitchChannelConfig;
```

### Twitch Module Display Component
**File:** `src/components/userprofile/TwitchModule.tsx`

```typescript
'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Play, ExternalLink, Eye, Calendar, Clock, Users, ChevronLeft, ChevronRight, X, Twitch } from 'lucide-react';
import { UserTwitchData, UserTwitchClip } from '@/types/user-content';
import { FloatingParticles, MagicContainer, AnimatedCounter } from './MagicUIIntegration';
import { useTouchGestures } from '@/hooks/use-touch-gestures';

interface TwitchModuleProps {
  twitchData: UserTwitchData | null;
  clips: UserTwitchClip[];
  isLoading?: boolean;
  error?: string | null;
  theme?: 'cosmic' | 'ocean' | 'forest' | 'crimson' | 'silver' | 'amber';
  className?: string;
  maxClips?: number;
  showChannelStats?: boolean;
}

const TwitchModule: React.FC<TwitchModuleProps> = ({
  twitchData,
  clips,
  isLoading = false,
  error = null,
  theme = 'cosmic',
  className = '',
  maxClips = 6,
  showChannelStats = true,
}) => {
  const [selectedClip, setSelectedClip] = useState<UserTwitchClip | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentClipIndex, setCurrentClipIndex] = useState(0);

  // Memoize filtered clips for better performance
  const displayClips = useMemo(() => {
    if (!clips) return [];
    return clips.slice(0, maxClips);
  }, [clips, maxClips]);

  // Define closeModal early to avoid reference error
  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedClip(null);
    setCurrentClipIndex(0);
  }, []);

  // Define navigateClip early to avoid reference error
  const navigateClip = useCallback((direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? Math.max(0, currentClipIndex - 1)
      : Math.min(displayClips.length - 1, currentClipIndex + 1);
    
    if (newIndex !== currentClipIndex && displayClips[newIndex]) {
      setSelectedClip(displayClips[newIndex]);
      setCurrentClipIndex(newIndex);
    }
  }, [currentClipIndex, displayClips]);

  // Theme-based styling function - Define early to avoid reference error
  const getThemeStyles = useCallback((theme: string) => {
    const themes = {
      cosmic: {
        container: 'bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/30',
        accent: 'text-purple-400',
        button: 'bg-purple-600 hover:bg-purple-500',
        card: 'bg-gray-900/50 border-purple-500/20',
        text: 'text-purple-100'
      },
      ocean: {
        container: 'bg-gradient-to-br from-blue-900/20 to-cyan-900/20 border-cyan-500/30',
        accent: 'text-cyan-400',
        button: 'bg-cyan-600 hover:bg-cyan-500',
        card: 'bg-gray-900/50 border-cyan-500/20',
        text: 'text-cyan-100'
      },
      forest: {
        container: 'bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-emerald-500/30',
        accent: 'text-emerald-400',
        button: 'bg-emerald-600 hover:bg-emerald-500',
        card: 'bg-gray-900/50 border-emerald-500/20',
        text: 'text-emerald-100'
      },
      crimson: {
        container: 'bg-gradient-to-br from-red-900/20 to-rose-900/20 border-rose-500/30',
        accent: 'text-rose-400',
        button: 'bg-rose-600 hover:bg-rose-500',
        card: 'bg-gray-900/50 border-rose-500/20',
        text: 'text-rose-100'
      },
      silver: {
        container: 'bg-gradient-to-br from-gray-800/20 to-slate-800/20 border-slate-500/30',
        accent: 'text-slate-300',
        button: 'bg-slate-600 hover:bg-slate-500',
        card: 'bg-gray-900/50 border-slate-500/20',
        text: 'text-slate-100'
      },
      amber: {
        container: 'bg-gradient-to-br from-amber-900/20 to-orange-900/20 border-amber-500/30',
        accent: 'text-amber-400',
        button: 'bg-amber-600 hover:bg-amber-500',
        card: 'bg-gray-900/50 border-amber-500/20',
        text: 'text-amber-100'
      }
    };
    return themes[theme as keyof typeof themes] || themes.cosmic;
  }, []);

  // Touch gestures for mobile navigation
  const modalRef = useTouchGestures({
    onSwipeLeft: () => navigateClip('next'),
    onSwipeRight: () => navigateClip('prev'),
    onSwipeDown: closeModal,
    enabled: isModalOpen
  });

  // Memoize theme styles for better performance
  const styles = useMemo(() => getThemeStyles(theme), [theme, getThemeStyles]);

  // Format number to readable format - Memoized
  const formatNumber = useCallback((num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }, []);

  // Format date - Memoized
  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    });
  }, []);

  // Format duration from seconds to readable format - Memoized
  const formatDuration = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  const openClipModal = useCallback((clip: UserTwitchClip) => {
    const clipIndex = displayClips.findIndex(c => c.id === clip.id);
    setSelectedClip(clip);
    setCurrentClipIndex(clipIndex);
    setIsModalOpen(true);
  }, [displayClips]);

  // Keyboard navigation for modal
  useEffect(() => {
    if (!isModalOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          closeModal();
          break;
        case 'ArrowLeft':
          navigateClip('prev');
          break;
        case 'ArrowRight':
          navigateClip('next');
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isModalOpen, closeModal, navigateClip]);

  if (isLoading) {
    return (
      <MagicContainer theme={theme} className={`p-6 ${styles.container} border rounded-xl ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-700 rounded w-48"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-800 rounded-lg h-48"></div>
            ))}
          </div>
        </div>
      </MagicContainer>
    );
  }

  if (error || !twitchData) {
    return (
      <MagicContainer theme={theme} className={`p-6 ${styles.container} border rounded-xl ${className}`}>
        <div className="text-center py-8">
          <Twitch className={`mx-auto mb-4 h-12 w-12 ${styles.accent} opacity-50`} />
          <h3 className="text-lg font-semibold text-gray-300 mb-2">
            {error || 'Canal do Twitch não configurado'}
          </h3>
          <p className="text-gray-400 text-sm">
            Configure seu canal no painel para exibir seus clips aqui.
          </p>
        </div>
      </MagicContainer>
    );
  }

  return (
    <>
      <MagicContainer theme={theme} className={`p-6 ${styles.container} border rounded-xl ${className}`}>
        <FloatingParticles theme={theme} count={8} />
        
        {/* Channel Header */}
        {showChannelStats && (
          <div className="flex items-center gap-4 mb-6">
            <div className="relative">
              <img
                src={twitchData.profileImageUrl}
                alt={twitchData.displayName}
                className="w-16 h-16 rounded-full border-2 border-gray-600"
                loading="lazy"
              />
              <div className="absolute -bottom-1 -right-1 bg-purple-600 text-white text-xs rounded-full px-2 py-1">
                <Twitch className="h-3 w-3" />
              </div>
            </div>
            <div className="flex-1">
              <h2 className={`text-xl font-bold ${styles.text} mb-1`}>
                {twitchData.displayName}
              </h2>
              <p className="text-gray-400 text-sm mb-2 line-clamp-2">
                {twitchData.description}
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>Streamer</span>
                </div>
                <div className="flex items-center gap-1">
                  <Play className="h-4 w-4" />
                  <span>{displayClips.length} clips</span>
                </div>
              </div>
            </div>
            <a
              href={`https://twitch.tv/${twitchData.username}`}
              target="_blank"
              rel="noopener noreferrer"
              className={`${styles.button} px-4 py-2 rounded-lg text-white transition-colors flex items-center gap-2`}
            >
              <ExternalLink className="h-4 w-4" />
              Canal
            </a>
          </div>
        )}

        {/* Clips Grid - Mobile optimized */}
        <div 
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4"
          role="grid"
          aria-label="Twitch clips"
        >
          {displayClips.map((clip, index) => (
            <div
              key={clip.id}
              className={`${styles.card} border rounded-lg overflow-hidden hover:border-opacity-60 transition-all duration-300 cursor-pointer group`}
              onClick={() => openClipModal(clip)}
              role="gridcell"
              tabIndex={0}
              aria-label={`Play clip: ${clip.title}`}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  openClipModal(clip);
                }
              }}
            >
              <div className="relative">
                <img
                  src={clip.thumbnailUrl}
                  alt={`Thumbnail for ${clip.title}`}
                  className="w-full h-40 object-cover group-hover:scale-105 transition-transform duration-300"
                  loading={index < 3 ? 'eager' : 'lazy'}
                  decoding="async"
                />
                <div 
                  className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300 flex items-center justify-center"
                  aria-hidden="true"
                >
                  <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {formatDuration(clip.duration)}
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-gray-100 text-sm line-clamp-2 mb-2 group-hover:text-white transition-colors">
                  {clip.title}
                </h3>
                
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      <span>{formatNumber(clip.viewCount)}</span>
                    </div>
                    {clip.gameName && (
                      <span className="truncate max-w-20">{clip.gameName}</span>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(clip.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </MagicContainer>

      {/* Clip Modal */}
      {isModalOpen && selectedClip && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 md:p-4">
          <div 
            ref={modalRef as React.RefObject<HTMLDivElement>}
            className="bg-gray-900 rounded-xl max-w-4xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto relative touch-manipulation"
          >
            {/* Navigation Arrows */}
            {currentClipIndex > 0 && (
              <button
                onClick={() => navigateClip('prev')}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200"
                aria-label="Clip anterior"
              >
                <ChevronLeft className="h-6 w-6" />
              </button>
            )}
            {currentClipIndex < displayClips.length - 1 && (
              <button
                onClick={() => navigateClip('next')}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200"
                aria-label="Próximo clip"
              >
                <ChevronRight className="h-6 w-6" />
              </button>
            )}
            
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1 mr-4">
                  <h2 className="text-xl font-bold text-white line-clamp-2 mb-2">
                    {selectedClip.title}
                  </h2>
                  <p className="text-sm text-gray-400">
                    Clip {currentClipIndex + 1} de {displayClips.length}
                  </p>
                </div>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-white transition-colors p-1"
                  aria-label="Fechar modal"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="aspect-video mb-4 rounded-lg overflow-hidden">
                <iframe
                  src={selectedClip.embedUrl}
                  title={selectedClip.title}
                  className="w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                />
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Eye className={`h-5 w-5 ${styles.accent} mx-auto mb-1`} />
                  <div className="text-sm text-gray-400">Visualizações</div>
                  <div className="font-semibold text-white">
                    {formatNumber(selectedClip.viewCount)}
                  </div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Clock className={`h-5 w-5 ${styles.accent} mx-auto mb-1`} />
                  <div className="text-sm text-gray-400">Duração</div>
                  <div className="font-semibold text-white">
                    {formatDuration(selectedClip.duration)}
                  </div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Calendar className={`h-5 w-5 ${styles.accent} mx-auto mb-1`} />
                  <div className="text-sm text-gray-400">Criado em</div>
                  <div className="font-semibold text-white">
                    {formatDate(selectedClip.createdAt)}
                  </div>
                </div>
                <div className="bg-gray-800 rounded-lg p-3 text-center">
                  <Users className={`h-5 w-5 ${styles.accent} mx-auto mb-1`} />
                  <div className="text-sm text-gray-400">Criador</div>
                  <div className="font-semibold text-white">
                    {selectedClip.creatorName}
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center mt-6">
                <a
                  href={selectedClip.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`${styles.button} px-6 py-3 rounded-lg text-white transition-colors flex items-center gap-2`}
                >
                  <ExternalLink className="h-4 w-4" />
                  Ver no Twitch
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TwitchModule;
```

### Stream Status Indicator Component
**File:** `src/components/userprofile/StreamStatusIndicator.tsx`

```typescript
'use client';

import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Twitch, Eye, Users } from 'lucide-react';
import { UserTwitchStreamStatus } from '@/types/user-content';
import { motion, AnimatePresence } from 'framer-motion';

interface StreamStatusIndicatorProps {
  userId: string;
  username: string;
  streamStatus?: UserTwitchStreamStatus | null;
  isLoading?: boolean;
  showViewerCount?: boolean;
  variant?: 'minimal' | 'detailed';
  className?: string;
}

const StreamStatusIndicator: React.FC<StreamStatusIndicatorProps> = ({
  userId,
  username,
  streamStatus,
  isLoading = false,
  showViewerCount = true,
  variant = 'minimal',
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  if (isLoading) {
    return (
      <div className={`inline-flex items-center ${className}`}>
        <div className="animate-pulse bg-gray-700 rounded-full h-4 w-16"></div>
      </div>
    );
  }

  if (!streamStatus || !streamStatus.isLive) {
    return null; // Don't show anything when offline
  }

  const formatViewerCount = (count: number): string => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  if (variant === 'minimal') {
    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className={`inline-flex items-center ${className}`}
          >
            <Badge 
              className="bg-red-600 hover:bg-red-700 text-white border-red-500 animate-pulse"
            >
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                <span className="text-xs font-medium">LIVE</span>
                {showViewerCount && streamStatus.viewerCount > 0 && (
                  <>
                    <Eye className="h-3 w-3 ml-1" />
                    <span className="text-xs">{formatViewerCount(streamStatus.viewerCount)}</span>
                  </>
                )}
              </div>
            </Badge>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.4 }}
          className={`inline-flex flex-col gap-2 ${className}`}
        >
          <div className="flex items-center gap-2">
            <Badge className="bg-red-600 hover:bg-red-700 text-white border-red-500">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                <Twitch className="h-3 w-3" />
                <span className="text-xs font-medium">AO VIVO</span>
              </div>
            </Badge>
            
            {showViewerCount && streamStatus.viewerCount > 0 && (
              <Badge variant="outline" className="text-gray-300 border-gray-600">
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  <span className="text-xs">{formatViewerCount(streamStatus.viewerCount)}</span>
                </div>
              </Badge>
            )}
          </div>

          {streamStatus.streamTitle && (
            <div className="bg-gray-800/50 rounded-lg p-2 max-w-xs">
              <p className="text-xs text-gray-300 line-clamp-2 mb-1">
                {streamStatus.streamTitle}
              </p>
              {streamStatus.gameName && (
                <p className="text-xs text-purple-400">
                  {streamStatus.gameName}
                </p>
              )}
            </div>
          )}

          <a
            href={`https://twitch.tv/${username}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs text-purple-400 hover:text-purple-300 transition-colors flex items-center gap-1"
          >
            <Twitch className="h-3 w-3" />
            Assistir no Twitch
          </a>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StreamStatusIndicator;
```

---

## 🔧 IMPLEMENTATION PHASES

### 📅 Phase 1: Core Infrastructure (Week 1)
**Days 1-2: Database Setup**
- Create database migrations for 3 new tables
- Set up indexes for performance optimization
- Update user_content_preferences table

**Days 3-4: TypeScript & API Foundation**
- Create TypeScript interfaces in `src/types/user-content.ts`
- Implement OAuth flow in `src/lib/twitch/oauth.ts`
- Build Twitch API service in `src/lib/twitch/api.ts`

**Days 5-7: Server Actions**
- Implement core server actions in `src/app/u/actions-twitch.ts`
- Add OAuth callback route handling
- Set up token refresh automation

### 📅 Phase 2: Dashboard Integration (Week 2)
**Days 8-10: Configuration Component**
- Build TwitchChannelConfig component
- Implement OAuth connection flow UI
- Add settings persistence

**Days 11-12: Dashboard Integration**
- Integrate TwitchChannelConfig into dashboard
- Update dashboard actions for Twitch settings
- Test configuration flow

**Days 13-14: Error Handling & Polish**
- Implement comprehensive error handling
- Add loading states and user feedback
- Optimize API calls and caching

### 📅 Phase 3: Profile Display (Week 3)
**Days 15-17: Display Components**
- Build TwitchModule component for clips display
- Create StreamStatusIndicator component
- Implement responsive design patterns

**Days 18-19: Profile Integration**
- Integrate components into ProfilePageClient
- Add theme support and styling consistency
- Implement mobile touch gestures

**Days 20-21: Real-time Features**
- Add stream status checking
- Implement auto-refresh functionality
- Optimize performance for mobile devices

### 📅 Phase 4: Enhancement & Testing (Week 4)
**Days 22-24: Advanced Features**
- Add clip modal with navigation
- Implement keyboard shortcuts
- Add accessibility improvements

**Days 25-26: Testing & Optimization**
- Comprehensive testing across devices
- Performance optimization
- Security audit and improvements

**Days 27-28: Documentation & Deployment**
- Complete implementation documentation
- Deployment guidelines
- User guide creation

---

## 🔒 SECURITY CONSIDERATIONS

### 🛡️ OAuth Security
- **Token Encryption**: All access and refresh tokens stored encrypted
- **Secure Storage**: Database-level encryption for sensitive data
- **HTTPS Only**: All API calls use HTTPS with certificate validation
- **State Validation**: OAuth state parameter validation to prevent CSRF

### 🔑 API Key Management
- **Environment Variables**: Client credentials stored in secure environment variables
- **Key Rotation**: Support for periodic API key rotation
- **Scope Limitation**: Minimal scopes requested (user:read:email, clips:read)
- **Access Control**: User-level permission controls

### 🚫 Rate Limiting & Abuse Prevention
- **API Rate Limits**: Respect Twitch's 800 requests/minute limit
- **Caching Strategy**: Aggressive caching to reduce API calls
- **Retry Logic**: Exponential backoff for failed requests
- **User Throttling**: Prevent individual users from excessive API usage

### 🔐 Data Protection
- **Token Expiration**: Automatic token refresh and expiration handling
- **Data Minimization**: Store only necessary user data
- **Audit Logging**: Track all API interactions for security monitoring
- **User Consent**: Clear consent flows for data access

---

## 📊 PERFORMANCE OPTIMIZATION

### ⚡ Caching Strategy
**Stream Status Cache (5 minutes)**
```typescript
// Cache stream status for 5 minutes to balance real-time accuracy with API limits
const STREAM_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
```

**Clips Data Cache (1 hour)**
```typescript
// Cache clips data for 1 hour as it changes less frequently
const CLIPS_CACHE_DURATION = 60 * 60 * 1000; // 1 hour
```

**User Data Cache (24 hours)**
```typescript
// Cache basic user data for 24 hours
const USER_DATA_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
```

### 🚀 Component Optimization
- **React.memo**: Memoize expensive components
- **useMemo**: Cache computed values
- **useCallback**: Stable function references
- **Lazy Loading**: Images loaded on demand
- **Virtual Scrolling**: For large clip collections

### 🌐 Network Optimization
- **Request Batching**: Combine multiple API calls
- **Progressive Enhancement**: Core functionality first
- **Offline Support**: Cached data when offline
- **CDN Integration**: Serve assets from CDN

---

## 🧪 TESTING STRATEGY

### ✅ Unit Tests
**Database Operations**
```typescript
// Test database CRUD operations
describe('TwitchDataService', () => {
  test('should save user Twitch data', async () => {
    // Test implementation
  });
  
  test('should handle token refresh', async () => {
    // Test implementation  
  });
});
```

**API Integration**
```typescript
// Test Twitch API calls
describe('TwitchAPI', () => {
  test('should fetch user clips', async () => {
    // Mock API responses and test
  });
  
  test('should handle rate limiting', async () => {
    // Test rate limit handling
  });
});
```

### 🔗 Integration Tests
**OAuth Flow**
- Test complete OAuth authorization flow
- Verify token storage and retrieval
- Test token refresh automation

**Component Integration**
- Test dashboard configuration flow
- Verify profile display functionality
- Test real-time status updates

### 👤 User Acceptance Testing
**Core Functionality**
- [ ] User can connect Twitch account
- [ ] Live status displays correctly
- [ ] Clips show in profile
- [ ] Settings save properly
- [ ] Mobile experience works well

**Error Scenarios**
- [ ] Handles network failures gracefully
- [ ] Shows appropriate error messages
- [ ] Recovers from token expiration
- [ ] Manages API rate limits

---

## 🚀 DEPLOYMENT GUIDELINES

### 🌍 Environment Setup
**Required Environment Variables**
```bash
# Twitch API Configuration
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret
TWITCH_REDIRECT_URI=https://yourdomain.com/api/auth/twitch/callback

# Database Configuration (existing)
DATABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

**Twitch Developer Setup**
1. Create Twitch Developer Application
2. Configure OAuth redirect URIs
3. Obtain Client ID and Client Secret
4. Set appropriate scopes (user:read:email, clips:read)

### 📋 Database Migration
```sql
-- Run migrations in order
\i user_twitch_data.sql
\i user_twitch_clips.sql  
\i user_twitch_stream_status.sql
\i update_content_preferences.sql
```

### 🔧 Application Configuration
**Next.js Configuration**
- Ensure API routes support POST requests
- Configure CORS for Twitch OAuth callbacks
- Set up proper error boundaries

**Supabase Configuration**
- Enable Row Level Security policies
- Set up real-time subscriptions if needed
- Configure database indexes for performance

---

## 🔧 MAINTENANCE & MONITORING

### 📊 Performance Monitoring
**Key Metrics**
- API response times
- Cache hit rates
- Token refresh success rates
- User engagement with Twitch content

**Monitoring Tools**
```typescript
// Example monitoring implementation
const logApiCall = (endpoint: string, duration: number, success: boolean) => {
  console.log(`Twitch API ${endpoint}: ${duration}ms ${success ? 'SUCCESS' : 'FAILED'}`);
};
```

### 🚨 Error Handling & Alerts
**Critical Error Types**
- OAuth failures
- API rate limit exceeded
- Token refresh failures
- Database connection issues

**Alert Configuration**
- Set up alerts for API failures > 5%
- Monitor token refresh failure rates
- Track user connection success rates

### 🔄 Maintenance Tasks
**Daily**
- Monitor API health and response times
- Check error logs for critical issues
- Verify stream status accuracy

**Weekly**  
- Review cache performance metrics
- Analyze user engagement data
- Update documentation as needed

**Monthly**
- Security audit of stored tokens
- Performance optimization review
- Feature usage analysis

---

## 📝 IMPLEMENTATION CHECKLIST

### ✅ Database Setup
- [ ] Create user_twitch_data table
- [ ] Create user_twitch_clips table
- [ ] Create user_twitch_stream_status table
- [ ] Update user_content_preferences table
- [ ] Add database indexes
- [ ] Set up Row Level Security policies

### ✅ Backend Implementation
- [ ] Implement OAuth flow (TwitchOAuth class)
- [ ] Build Twitch API service (TwitchAPI class)
- [ ] Create server actions (actions-twitch.ts)
- [ ] Add OAuth callback route
- [ ] Implement token refresh logic
- [ ] Add error handling and logging

### ✅ Frontend Components
- [ ] Build TwitchChannelConfig component
- [ ] Create TwitchModule component
- [ ] Implement StreamStatusIndicator component
- [ ] Add OAuth connection flow UI
- [ ] Implement responsive design
- [ ] Add accessibility features

### ✅ Integration
- [ ] Integrate with dashboard
- [ ] Add to profile display
- [ ] Update user content preferences
- [ ] Implement real-time updates
- [ ] Add theme support
- [ ] Test mobile compatibility

### ✅ Security & Performance
- [ ] Secure token storage
- [ ] Implement rate limiting
- [ ] Add caching layers
- [ ] Security audit
- [ ] Performance optimization
- [ ] Error boundary implementation

### ✅ Testing & Documentation
- [ ] Unit test coverage
- [ ] Integration testing
- [ ] User acceptance testing
- [ ] API documentation
- [ ] User guide creation
- [ ] Deployment documentation

---

## 🎯 SUCCESS METRICS

### 📈 Technical Metrics
- **OAuth Success Rate**: > 98%
- **API Response Time**: < 500ms average
- **Cache Hit Rate**: > 80%
- **Error Rate**: < 2%
- **Mobile Performance**: Load time < 3s

### 👥 User Engagement Metrics
- **Connection Rate**: % of users who connect Twitch
- **Content Interaction**: Clicks on clips/streams
- **Settings Usage**: Configuration completion rate
- **Return Usage**: Users who keep integration enabled

### 🔒 Security Metrics
- **Token Refresh Success**: > 99%
- **Security Incidents**: 0 critical issues
- **Data Protection**: 100% compliance
- **API Abuse**: < 0.1% of requests

---

## 📚 ADDITIONAL RESOURCES

### 🔗 External Documentation
- [Twitch API Reference](https://dev.twitch.tv/docs/api/)
- [Twitch Authentication Guide](https://dev.twitch.tv/docs/authentication/)
- [OAuth 2.0 Specification](https://oauth.net/2/)
- [Supabase Documentation](https://supabase.com/docs)

### 🛠️ Development Tools
- [Twitch Developer Console](https://dev.twitch.tv/console)
- [Postman Collection](https://documenter.getpostman.com/view/8854915/Szf26WHn)
- [OAuth Testing Tool](https://oauth.tools/)

### 🎮 Example Implementations
- [Twitch API Examples](https://github.com/twitchdev/twitch-cli)
- [OAuth Flow Examples](https://github.com/twitchdev/authentication-samples)
- [React Twitch Components](https://github.com/talk2megooseman/react-twitch-embed-video)

---

## 📞 SUPPORT & TROUBLESHOOTING

### 🐛 Common Issues
**OAuth Flow Fails**
- Verify redirect URI configuration
- Check client ID and secret
- Ensure HTTPS in production

**API Rate Limiting**
- Implement proper caching
- Add request throttling
- Use exponential backoff

**Token Refresh Issues**
- Check token expiration handling
- Verify refresh token storage
- Implement fallback flows

### 🆘 Emergency Procedures
**Critical API Failure**
1. Enable graceful degradation
2. Show cached data only
3. Disable new connections
4. Monitor for resolution

**Security Breach**
1. Revoke all tokens immediately
2. Audit affected accounts
3. Implement additional security
4. Notify affected users

---

*This implementation guide provides a complete roadmap for integrating Twitch functionality into CriticalPixel. Follow the phases sequentially for best results and refer to the troubleshooting section for common issues.*

**Next Steps**: Begin with Phase 1 database setup and proceed through each phase systematically.