'use server';

import { createServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import { cookies } from 'next/headers';

export interface ReportSubmission {
  contentId: string;
  contentType: 'review' | 'comment';
  reporterId: string;
  reason: string;
  description?: string;
}

export async function submitReportAction(report: ReportSubmission): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    console.log('🚨 Report submission started:', { contentId: report.contentId, contentType: report.contentType, reporterId: report.reporterId, reason: report.reason });

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Validate input
    if (!report.contentId || !report.reporterId || !report.reason) {
      console.error('Missing required fields:', { contentId: !!report.contentId, reporterId: !!report.reporterId, reason: !!report.reason });
      return { success: false, error: 'Campos obrigatórios faltando' };
    }

    // Check authentication with more detailed logging
    console.log('🔐 Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('🔐 Auth result:', { user: user ? { id: user.id, email: user.email } : null, authError });

    if (authError) {
      console.error('Authentication error:', authError);
      return { success: false, error: 'Erro de autenticação' };
    }

    if (!user) {
      console.error('No user found in session');
      return { success: false, error: 'Usuário não autenticado' };
    }

    // Verify reporter ID matches authenticated user
    if (user.id !== report.reporterId) {
      console.error('Reporter ID mismatch:', { userId: user.id, reporterId: report.reporterId });
      return { success: false, error: 'ID do usuário não confere' };
    }

    console.log('✅ Authentication successful for user:', user.id);

    // Check for existing report (use maybeSingle to avoid errors)
    const { data: existingReport, error: duplicateError } = await supabase
      .from('content_flags')
      .select('id')
      .eq('content_id', report.contentId)
      .eq('reporter_id', report.reporterId)
      .eq('content_type', report.contentType)
      .maybeSingle();

    if (duplicateError) {
      console.error('Error checking for duplicate report:', duplicateError);
      return { success: false, error: 'Erro ao verificar denúncia existente' };
    }

    if (existingReport) {
      return { success: false, error: 'Você já denunciou este conteúdo' };
    }

    // Verify content exists (use maybeSingle to avoid errors)
    const tableName = report.contentType === 'review' ? 'reviews' : 'comments';
    const { data: content, error: contentError } = await supabase
      .from(tableName)
      .select('id')
      .eq('id', report.contentId)
      .maybeSingle();

    if (contentError) {
      console.error('Error checking content existence:', contentError);
      return { success: false, error: 'Erro ao verificar conteúdo' };
    }

    if (!content) {
      console.error('Content not found:', { contentId: report.contentId, contentType: report.contentType });
      return { success: false, error: 'Conteúdo não encontrado' };
    }

    // Insert the report
    const insertData = {
      content_id: report.contentId,
      content_type: report.contentType,
      reporter_id: report.reporterId,
      reason: report.reason,
      description: report.description,
      status: 'pending',
      created_at: new Date().toISOString(),
    };

    console.log('📝 Attempting to insert report with data:', insertData);

    const { error: insertError } = await supabase
      .from('content_flags')
      .insert(insertData);

    if (insertError) {
      console.error('Error inserting report:', insertError);
      return { success: false, error: 'Erro ao registrar denúncia' };
    }

    console.log('✅ Report inserted successfully');
    revalidatePath('/admin/reviews/reports');
    return { success: true };
  } catch (error) {
    console.error('Unexpected error in submitReportAction:', error);
    return { success: false, error: 'Erro inesperado ao denunciar' };
  }
}

export async function getReportCountForContent(contentId: string): Promise<number> {
  try {
    const supabase = await createServerClient();
    const { count, error } = await supabase
      .from('content_flags')
      .select('*', { count: 'exact', head: true })
      .eq('content_id', contentId)
      .eq('status', 'pending');
    if (error) return 0;
    return count || 0;
  } catch {
    return 0;
  }
} 