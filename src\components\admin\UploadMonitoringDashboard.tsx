// src/components/admin/UploadMonitoringDashboard.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { 
  Upload, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  HardDrive, 
  Users,
  TrendingUp,
  Shield
} from 'lucide-react';
import { UploadAnalytics, B2HealthCheck } from '@/lib/monitoring/uploadAnalytics';

interface DashboardProps {
  className?: string;
}

export default function UploadMonitoringDashboard({ className = '' }: DashboardProps) {
  const [metrics, setMetrics] = useState(UploadAnalytics.getMetrics('day'));
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [timeRange]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load metrics
      const newMetrics = UploadAnalytics.getMetrics(timeRange);
      setMetrics(newMetrics);

      // Check B2 health
      const health = await B2HealthCheck.checkHealth();
      setHealthStatus(health);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'degraded': return 'text-yellow-400';
      case 'unhealthy': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  const successRate = metrics.totalUploads > 0 
    ? ((metrics.successfulUploads / metrics.totalUploads) * 100).toFixed(1)
    : '0';

  const pieData = [
    { name: 'Successful', value: metrics.successfulUploads, color: '#10B981' },
    { name: 'Failed', value: metrics.failedUploads, color: '#EF4444' },
    { name: 'Security Issues', value: metrics.securityIssues, color: '#F59E0B' },
  ];

  const performanceMetrics = UploadAnalytics.getPerformanceMetrics();
  const topUsers = UploadAnalytics.getTopUsers(5);
  const errorAnalysis = UploadAnalytics.getErrorAnalysis();

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Upload Monitoring Dashboard</h2>
        <div className="flex items-center gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-slate-800 border border-slate-600 rounded text-white focus:outline-none focus:border-purple-400"
          >
            <option value="hour">Last Hour</option>
            <option value="day">Last 24 Hours</option>
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
          </select>
          {healthStatus && (
            <div className={`flex items-center gap-2 ${getHealthStatusColor(healthStatus.status)}`}>
              <div className={`w-2 h-2 rounded-full ${
                healthStatus.status === 'healthy' ? 'bg-green-400' :
                healthStatus.status === 'degraded' ? 'bg-yellow-400' : 'bg-red-400'
              }`} />
              <span className="text-sm font-medium">
                B2 {healthStatus.status} ({healthStatus.latency}ms)
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Uploads</p>
              <p className="text-2xl font-bold text-white">{metrics.totalUploads}</p>
            </div>
            <Upload className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-green-400">{successRate}%</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Total Size</p>
              <p className="text-2xl font-bold text-white">{formatBytes(metrics.totalSize)}</p>
            </div>
            <HardDrive className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-400 text-sm">Avg Processing</p>
              <p className="text-2xl font-bold text-white">
                {performanceMetrics.averageProcessingTime.toFixed(0)}ms
              </p>
            </div>
            <Clock className="w-8 h-8 text-yellow-400" />
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Status Distribution */}
        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <h3 className="text-white font-medium mb-4">Upload Status Distribution</h3>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Security Issues */}
        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <h3 className="text-white font-medium mb-4">Security & Quota Issues</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-red-400" />
                <span className="text-slate-300">Security Issues</span>
              </div>
              <span className="text-red-400 font-medium">{metrics.securityIssues}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-yellow-400" />
                <span className="text-slate-300">Quota Exceeded</span>
              </div>
              <span className="text-yellow-400 font-medium">{metrics.quotaExceeded}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-orange-400" />
                <span className="text-slate-300">Rate Limited</span>
              </div>
              <span className="text-orange-400 font-medium">{metrics.rateLimitHits}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-blue-400" />
                <span className="text-slate-300">Duplicates Detected</span>
              </div>
              <span className="text-blue-400 font-medium">{metrics.duplicatesDetected}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Users and Error Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Users */}
        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <h3 className="text-white font-medium mb-4">Top Uploaders</h3>
          <div className="space-y-2">
            {topUsers.map((user, index) => (
              <div key={user.userId} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-xs text-white">
                    {index + 1}
                  </div>
                  <span className="text-slate-300">{user.userId.slice(0, 8)}...</span>
                </div>
                <span className="text-white font-medium">{user.uploads} uploads</span>
              </div>
            ))}
          </div>
        </div>

        {/* Error Analysis */}
        <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
          <h3 className="text-white font-medium mb-4">Common Errors</h3>
          <div className="space-y-2">
            {errorAnalysis.slice(0, 5).map((error, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-slate-300 text-sm truncate flex-1 mr-2">
                  {error.error}
                </span>
                <span className="text-red-400 font-medium">{error.count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-white font-medium mb-4">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-slate-400 text-sm">Average Processing Time</p>
            <p className="text-xl font-bold text-white">
              {performanceMetrics.averageProcessingTime.toFixed(0)}ms
            </p>
          </div>
          <div>
            <p className="text-slate-400 text-sm">95th Percentile</p>
            <p className="text-xl font-bold text-white">
              {performanceMetrics.p95ProcessingTime.toFixed(0)}ms
            </p>
          </div>
          <div>
            <p className="text-slate-400 text-sm">Compression Savings</p>
            <p className="text-xl font-bold text-green-400">
              {formatBytes(metrics.compressionSavings)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
