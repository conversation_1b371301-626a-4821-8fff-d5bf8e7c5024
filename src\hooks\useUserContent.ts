'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  incrementContentViews,
  toggleContentLike,
  getUserYouTubeData
} from '@/app/u/actions-content';
import type { 
  UserContentData, 
  UserReview, 
  UserSurvey, 
  UserActivity, 
  UserAchievement, 
  UserMedia, 
  UserStats,
  UserYouTubeData,
  ContentFilters 
} from '@/types/user-content';

interface UseUserContentReturn {
  data: UserContentData | null;
  featuredReview: UserReview | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  incrementViews: (contentType: 'review' | 'survey' | 'media', contentId: string) => Promise<void>;
  toggleLike: (contentType: 'review' | 'survey' | 'media', contentId: string) => Promise<boolean>;
  stats: {
    totalContent: number;
    totalEngagement: number;
    averageRating: number;
    topGame: string | null;
  };
  youtubeData: UserYouTubeData | null;
  refetchYouTube: () => Promise<void>;
}

// Cache simples para evitar requests desnecessários
const contentCache = new Map<string, { data: UserContentData; timestamp: number; featured?: UserReview | null }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

export function useUserContent(userId: string, currentUserId?: string, channelUrl?: string): UseUserContentReturn {
  const [data, setData] = useState<UserContentData | null>(null);
  const [featuredReview, setFeaturedReview] = useState<UserReview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [youtubeData, setYoutubeData] = useState<UserYouTubeData | null>(null);

  // Verificar cache
  const getCachedData = useCallback((userId: string) => {
    const cached = contentCache.get(userId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached;
    }
    return null;
  }, []);

  // Buscar dados do usuário usando client-side Supabase
  const fetchUserContent = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Verificar cache primeiro
      const cached = getCachedData(userId);
      if (cached) {
        setData(cached.data);
        setFeaturedReview(cached.featured || null);
        setIsLoading(false);
        return;
      }

      // Use client-side Supabase to avoid auth issues
      const { createClient } = await import('@/lib/supabase/client');
      const supabase = createClient();

      // Fetch reviews directly from client
      const { data: reviewsData, error: reviewsError } = await supabase
        .from('reviews')
        .select(`
          id,
          title,
          slug,
          game_name,
          author_id,
          author_name,
          overall_score,
          platforms,
          played_on,
          date_played,
          tags,
          main_image_url,
          igdb_cover_url,
          status,
          is_featured,
          view_count,
          like_count,
          comment_count,
          created_at,
          updated_at,
          content_lexical,
          games (
            cover_url
          )
        `)
        .eq('author_id', userId)
        .eq('status', 'published')
        .eq('is_blocked', false)
        .order('created_at', { ascending: false })
        .limit(20);

      // Fetch surveys directly from client
      const { data: surveysData, error: surveysError } = await supabase
        .from('performance_surveys')
        .select(`
          id,
          user_id,
          game_title,
          platform,
          device_type,
          cpu,
          gpu,
          total_memory,
          fps_average,
          smoothness,
          resolution,
          created_at,
          updated_at
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20);

      // Transform reviews data
      const transformedReviews = (reviewsData || []).map((review: any) => ({
        id: review.id,
        user_id: review.author_id,
        game_name: review.game_name,
        game_image: review.main_image_url,
        igdb_cover_url: review.igdb_cover_url,
        games: review.games,
        rating: review.overall_score,
        overallScore: review.overall_score,
        review_text: typeof review.content_lexical === 'object' ? JSON.stringify(review.content_lexical) : review.content_lexical,
        content_lexical: review.content_lexical,
        contentLexical: review.content_lexical,
        created_at: review.created_at,
        updated_at: review.updated_at,
        // Map database fields to component expected names
        view_count: review.view_count || 0,
        like_count: review.like_count || 0,
        comment_count: review.comment_count || 0,
        // Keep legacy field names for backward compatibility
        likes_count: review.like_count || 0,
        views_count: review.view_count || 0,
        comments_count: review.comment_count || 0,
        is_featured: review.is_featured || false,
        is_public: true,
        platform: review.platforms?.[0] || '',
        played_on: review.played_on || '',
        playtime_hours: undefined,
        tags: review.tags || [],
        title: review.title,
        slug: review.slug
      }));

      // Transform surveys data
      const transformedSurveys = (surveysData || []).map((survey: any) => ({
        id: survey.id,
        user_id: survey.user_id,
        game_name: survey.game_title,
        game_image: undefined,
        performance_score: survey.smoothness || 0,
        fps_average: survey.fps_average || 0,
        resolution: survey.resolution || '',
        graphics_settings: `${survey.device_type} - ${survey.cpu} / ${survey.gpu}`,
        created_at: survey.created_at,
        hardware_used: `${survey.cpu} / ${survey.gpu} / ${survey.total_memory}GB RAM`,
        notes: undefined,
        is_public: true,
        is_verified: false
      }));

      // Find featured review
      const featuredReview = transformedReviews.find(review => review.is_featured) || null;

      // Create content data
      const contentData = {
        reviews: transformedReviews,
        surveys: transformedSurveys,
        activities: [], // Empty for now
        achievements: [], // Empty for now
        media: [], // Empty for now
        stats: {
          user_id: userId,
          reviews_count: transformedReviews.length,
          surveys_count: transformedSurveys.length,
          achievements_count: 0,
          media_count: 0,
          total_likes_received: transformedReviews.reduce((sum, r) => sum + r.likes_count, 0),
          total_views_received: transformedReviews.reduce((sum, r) => sum + r.views_count, 0),
          total_comments_received: transformedReviews.reduce((sum, r) => sum + r.comments_count, 0),
          total_points: transformedReviews.reduce((sum, r) => sum + r.likes_count + r.views_count, 0),
          level: 1,
          rank: 'Newcomer',
          join_date: new Date().toISOString(),
          last_activity: new Date().toISOString(),
          featured_content_count: transformedReviews.filter(r => r.is_featured).length,
          verified_content_count: 0
        },
        preferences: {
          user_id: userId,
          enabled_modules: [],
          youtubeModule: {
            enabled: false,
            visibility: 'public' as const,
            maxVideos: 6,
            showStats: true,
            showDescription: true,
            autoplay: false
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };

      setData(contentData);
      setFeaturedReview(featuredReview);

      // Cache the data
      contentCache.set(userId, {
        data: contentData,
        featured: featuredReview,
        timestamp: Date.now()
      });

    } catch (err) {
      console.error('Error in fetchUserContent:', err);
      setError('Failed to load user content');
    } finally {
      setIsLoading(false);
    }
  }, [userId, getCachedData]);

  // Função para refetch dos dados
  const refetch = useCallback(async () => {
    // Limpar cache para forçar atualização
    contentCache.delete(userId);
    await fetchUserContent();
  }, [userId, fetchUserContent]);

  // Incrementar views com otimização
  const incrementViews = useCallback(async (
    contentType: 'review' | 'survey' | 'media', 
    contentId: string
  ) => {
    try {
      await incrementContentViews(contentType, contentId);
      
      // Atualizar estado local otimisticamente
      if (data) {
        const updatedData = { ...data };
        
        if (contentType === 'review') {
          updatedData.reviews = updatedData.reviews.map(review => 
            review.id === contentId 
              ? { ...review, views_count: review.views_count + 1 }
              : review
          );
        } else if (contentType === 'survey') {
          updatedData.surveys = updatedData.surveys.map(survey => 
            survey.id === contentId 
              ? { ...survey, views_count: (survey as any).views_count + 1 }
              : survey
          );
        } else if (contentType === 'media') {
          updatedData.media = updatedData.media.map(media => 
            media.id === contentId 
              ? { ...media, views_count: media.views_count + 1 }
              : media
          );
        }
        
        setData(updatedData);
        
        // Atualizar cache
        const cached = contentCache.get(userId);
        if (cached) {
          contentCache.set(userId, {
            ...cached,
            data: updatedData
          });
        }
      }
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  }, [data, userId]);

  // Toggle like com otimização
  const toggleLike = useCallback(async (
    contentType: 'review' | 'survey' | 'media', 
    contentId: string
  ): Promise<boolean> => {
    if (!currentUserId) {
      throw new Error('User must be logged in to like content');
    }

    try {
      const result = await toggleContentLike(contentType, contentId, currentUserId);
      
      if (result.success) {
        const isLiked = result.data!;
        
        // Atualizar estado local otimisticamente
        if (data) {
          const updatedData = { ...data };
          
          if (contentType === 'review') {
            updatedData.reviews = updatedData.reviews.map(review => 
              review.id === contentId 
                ? { 
                    ...review, 
                    likes_count: isLiked 
                      ? review.likes_count + 1 
                      : Math.max(0, review.likes_count - 1)
                  }
                : review
            );
          } else if (contentType === 'media') {
            updatedData.media = updatedData.media.map(media => 
              media.id === contentId 
                ? { 
                    ...media, 
                    likes_count: isLiked 
                      ? media.likes_count + 1 
                      : Math.max(0, media.likes_count - 1)
                  }
                : media
            );
          }
          
          setData(updatedData);
          
          // Atualizar featured review se for o caso
          if (featuredReview && featuredReview.id === contentId && contentType === 'review') {
            setFeaturedReview({
              ...featuredReview,
              likes_count: isLiked 
                ? featuredReview.likes_count + 1 
                : Math.max(0, featuredReview.likes_count - 1)
            });
          }
          
          // Atualizar cache
          const cached = contentCache.get(userId);
          if (cached) {
            contentCache.set(userId, {
              ...cached,
              data: updatedData,
              featured: featuredReview && featuredReview.id === contentId && contentType === 'review'
                ? {
                    ...featuredReview,
                    likes_count: isLiked 
                      ? featuredReview.likes_count + 1 
                      : Math.max(0, featuredReview.likes_count - 1)
                  }
                : cached.featured
            });
          }
        }
        
        return isLiked;
      } else {
        throw new Error(result.error || 'Failed to toggle like');
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  }, [data, featuredReview, currentUserId, userId]);

  // Estatísticas computadas
  const stats = useMemo(() => {
    if (!data) {
      return {
        totalContent: 0,
        totalEngagement: 0,
        averageRating: 0,
        topGame: null
      };
    }

    const totalContent = data.reviews.length + data.surveys.length + data.media.length;
    const totalEngagement = data.stats.total_likes_received + data.stats.total_views_received + data.stats.total_comments_received;
    
    // Calcular rating médio
    const averageRating = data.reviews.length > 0 
      ? data.reviews.reduce((sum, review) => sum + review.rating, 0) / data.reviews.length 
      : 0;

    // Encontrar jogo mais avaliado
    const gameCount = new Map<string, number>();
    data.reviews.forEach(review => {
      gameCount.set(review.game_name, (gameCount.get(review.game_name) || 0) + 1);
    });
    data.surveys.forEach(survey => {
      gameCount.set(survey.game_name, (gameCount.get(survey.game_name) || 0) + 1);
    });

    const topGame = gameCount.size > 0 
      ? Array.from(gameCount.entries()).reduce((a, b) => a[1] > b[1] ? a : b)[0]
      : null;

    return {
      totalContent,
      totalEngagement,
      averageRating: Math.round(averageRating * 10) / 10,
      topGame
    };
  }, [data]);

  const computedStats = useMemo(() => {
    const baseViews = stats?.totalEngagement || 0;
    const baseLikes = stats?.totalContent || 0;
    const youtubeViews = youtubeData?.videos.reduce((sum, video) => sum + video.viewCount, 0) || 0;
    const youtubeLikes = youtubeData?.videos.reduce((sum, video) => sum + video.likeCount, 0) || 0;

    return {
      reviewsCount: data?.reviews.length || 0,
      surveysCount: data?.surveys.length || 0,
      activitiesCount: data?.activities.length || 0,
      achievementsCount: data?.achievements.length || 0,
      mediaCount: data?.media.length || 0,
      youtubeVideosCount: youtubeData?.videos.length || 0,
      totalViews: baseViews + youtubeViews,
      totalLikes: baseLikes + youtubeLikes,
      subscribersCount: youtubeData?.channel.subscriberCount || 0
    };
  }, [data, youtubeData, stats]);

  // Effect para carregar dados iniciais
  useEffect(() => {
    if (userId) {
      fetchUserContent();
    }
  }, [userId, fetchUserContent]);

  const fetchYouTubeData = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      const response = await getUserYouTubeData(userId, channelUrl);
      
      if (response.success && response.data) {
        setYoutubeData(response.data);
        
        // Cache the data
        const cacheKey = `youtube_${userId}`;
        localStorage.setItem(cacheKey, JSON.stringify({
          data: response.data,
          timestamp: Date.now(),
          ttl: CACHE_DURATION
        }));
      } else {
        setError(response.error || 'Failed to fetch YouTube data');
      }
    } catch (err) {
      setError('Error fetching YouTube data');
      console.error('YouTube fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [userId, channelUrl]);

  // YouTube data effect - only if channelUrl is provided
  useEffect(() => {
    if (!userId || !channelUrl) return;

    // Check cache first
    const cacheKey = `youtube_${userId}`;
    const cached = localStorage.getItem(cacheKey);
    
    if (cached) {
      try {
        const { data, timestamp, ttl } = JSON.parse(cached);
        const isExpired = Date.now() - timestamp > ttl;
        
        if (!isExpired) {
          setYoutubeData(data);
          return;
        }
      } catch (error) {
        console.error('Cache parse error:', error);
      }
    }

    fetchYouTubeData();
  }, [fetchYouTubeData, channelUrl]);

  const refetchYouTube = useCallback(async () => {
    // Clear cache and refetch
    const cacheKey = `youtube_${userId}`;
    localStorage.removeItem(cacheKey);
    await fetchYouTubeData();
  }, [fetchYouTubeData, userId]);

  return {
    data,
    featuredReview,
    isLoading,
    error,
    refetch,
    incrementViews,
    toggleLike,
    stats,
    youtubeData,
    refetchYouTube
  };
}

// Hook para gerenciar filtros de conteúdo
export function useContentFilters() {
  const [filters, setFilters] = useState<ContentFilters>({
    sort_by: 'created_at',
    sort_order: 'desc',
    limit: 20
  });

  const updateFilter = useCallback((key: keyof ContentFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      sort_by: 'created_at',
      sort_order: 'desc',
      limit: 20
    });
  }, []);

  return {
    filters,
    updateFilter,
    resetFilters
  };
}

// Hook para debounce de busca
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
} 