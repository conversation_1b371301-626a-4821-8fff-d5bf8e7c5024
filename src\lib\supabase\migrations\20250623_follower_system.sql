-- CriticalPixel Follower System - Complete Implementation
-- Date: June 23, 2025
-- Purpose: Implement comprehensive follower system with real-time capabilities
-- Safe to run on production - includes all safety checks

BEGIN;

-- =====================================================
-- PHASE 1: CORE TABLES CREATION
-- =====================================================

-- Create user_follows table with composite primary key for optimal performance
CREATE TABLE IF NOT EXISTS public.user_follows (
    follower_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    following_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Privacy and interaction tracking
    is_mutual BOOLEAN DEFAULT FALSE,
    notification_sent BOOLEAN DEFAULT FALSE,
    last_interaction_at TIMESTAMP WITH TIME ZONE,
    
    -- Composite primary key for optimal performance
    PRIMARY KEY (follower_id, following_id),
    
    -- Prevent self-following
    CONSTRAINT no_self_follow CHECK (follower_id != following_id)
);

-- Add follower metrics to existing profiles table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'follower_count') THEN
        ALTER TABLE public.profiles ADD COLUMN follower_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'following_count') THEN
        ALTER TABLE public.profiles ADD COLUMN following_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'mutual_follow_count') THEN
        ALTER TABLE public.profiles ADD COLUMN mutual_follow_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'last_follow_activity_at') THEN
        ALTER TABLE public.profiles ADD COLUMN last_follow_activity_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create follow notifications table for real-time notifications
CREATE TABLE IF NOT EXISTS public.follow_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    recipient_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    actor_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    notification_type VARCHAR(20) NOT NULL DEFAULT 'follow',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_notification_type CHECK (
        notification_type IN ('follow', 'unfollow', 'mutual')
    )
);

-- =====================================================
-- PHASE 2: PERFORMANCE OPTIMIZATION INDEXES
-- =====================================================

-- Critical indexes for optimal performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_follows_follower_id 
ON public.user_follows(follower_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_follows_following_id 
ON public.user_follows(following_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_follows_created_at 
ON public.user_follows(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_follows_mutual 
ON public.user_follows(is_mutual) WHERE is_mutual = true;

-- Notification indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_notifications_recipient 
ON public.follow_notifications(recipient_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_notifications_unread 
ON public.follow_notifications(recipient_id, is_read) WHERE is_read = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_notifications_created_at 
ON public.follow_notifications(created_at DESC);

-- Profile optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_follower_count 
ON public.profiles(follower_count DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_following_count 
ON public.profiles(following_count DESC);

-- =====================================================
-- PHASE 3: ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE public.user_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.follow_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_follows table
CREATE POLICY "Users can view public follow relationships" ON public.user_follows
    FOR SELECT USING (
        -- Allow viewing if either user has public followers/following
        EXISTS (
            SELECT 1 FROM public.profiles p1 
            WHERE p1.id = user_follows.following_id 
            AND (p1.privacy_settings->>'show_followers' != 'none'
                 OR p1.privacy_settings->>'show_following' != 'none')
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles p2 
            WHERE p2.id = user_follows.follower_id 
            AND p2.privacy_settings->>'show_following' != 'none'
        )
        -- Or if user is involved in the relationship
        OR user_follows.follower_id = auth.uid()
        OR user_follows.following_id = auth.uid()
    );

CREATE POLICY "Users can manage their own follows" ON public.user_follows
    FOR ALL USING (follower_id = auth.uid());

-- RLS Policies for follow_notifications table
CREATE POLICY "Users can access their own notifications" ON public.follow_notifications
    FOR ALL USING (recipient_id = auth.uid());

-- =====================================================
-- PHASE 4: TRIGGER FUNCTIONS FOR AUTOMATIC COUNT UPDATES
-- =====================================================

-- Function to update follower/following counts and handle mutual follows
CREATE OR REPLACE FUNCTION update_follow_counts()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment follower count for the followed user
        UPDATE public.profiles
        SET follower_count = follower_count + 1,
            last_follow_activity_at = NOW()
        WHERE id = NEW.following_id;

        -- Increment following count for the follower
        UPDATE public.profiles
        SET following_count = following_count + 1,
            last_follow_activity_at = NOW()
        WHERE id = NEW.follower_id;

        -- Check for mutual follow and update both records
        IF EXISTS (
            SELECT 1 FROM public.user_follows
            WHERE follower_id = NEW.following_id
            AND following_id = NEW.follower_id
        ) THEN
            -- Update mutual status for both relationships
            UPDATE public.user_follows
            SET is_mutual = TRUE
            WHERE (follower_id = NEW.follower_id AND following_id = NEW.following_id)
               OR (follower_id = NEW.following_id AND following_id = NEW.follower_id);

            -- Update mutual counts
            UPDATE public.profiles
            SET mutual_follow_count = mutual_follow_count + 1
            WHERE id IN (NEW.follower_id, NEW.following_id);
        END IF;

        RETURN NEW;

    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement follower count
        UPDATE public.profiles
        SET follower_count = GREATEST(follower_count - 1, 0)
        WHERE id = OLD.following_id;

        -- Decrement following count
        UPDATE public.profiles
        SET following_count = GREATEST(following_count - 1, 0)
        WHERE id = OLD.follower_id;

        -- Handle mutual follow removal
        IF OLD.is_mutual THEN
            -- Update remaining relationship to non-mutual
            UPDATE public.user_follows
            SET is_mutual = FALSE
            WHERE follower_id = OLD.following_id
            AND following_id = OLD.follower_id;

            -- Update mutual counts
            UPDATE public.profiles
            SET mutual_follow_count = GREATEST(mutual_follow_count - 1, 0)
            WHERE id IN (OLD.follower_id, OLD.following_id);
        END IF;

        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ language 'plpgsql';

-- Apply trigger
CREATE TRIGGER update_follow_counts_trigger
    AFTER INSERT OR DELETE ON public.user_follows
    FOR EACH ROW
    EXECUTE FUNCTION update_follow_counts();

-- =====================================================
-- PHASE 5: OPTIMIZED VIEWS AND FUNCTIONS
-- =====================================================

-- Optimized follower list view with privacy filtering
CREATE OR REPLACE VIEW public.user_followers_view AS
SELECT
    uf.following_id as user_id,
    uf.follower_id,
    p.username,
    p.display_name,
    p.avatar_url,
    p.slug,
    uf.created_at as followed_at,
    uf.is_mutual,
    -- Privacy check based on user settings
    CASE
        WHEN p.privacy_settings->>'show_followers' = 'everyone' THEN true
        WHEN p.privacy_settings->>'show_followers' = 'followers'
             AND EXISTS (
                 SELECT 1 FROM public.user_follows uf2
                 WHERE uf2.follower_id = uf.following_id
                 AND uf2.following_id = auth.uid()
             ) THEN true
        WHEN p.privacy_settings->>'show_followers' = 'none' THEN false
        ELSE true -- Default to public
    END as can_view
FROM public.user_follows uf
JOIN public.profiles p ON uf.follower_id = p.id
WHERE p.id NOT IN (
    SELECT blocked_id FROM public.user_blocks
    WHERE blocker_id = uf.following_id
);

-- Optimized following list view with privacy filtering
CREATE OR REPLACE VIEW public.user_following_view AS
SELECT
    uf.follower_id as user_id,
    uf.following_id,
    p.username,
    p.display_name,
    p.avatar_url,
    p.slug,
    uf.created_at as followed_at,
    uf.is_mutual,
    -- Privacy check based on user settings
    CASE
        WHEN p.privacy_settings->>'show_following' = 'everyone' THEN true
        WHEN p.privacy_settings->>'show_following' = 'followers'
             AND EXISTS (
                 SELECT 1 FROM public.user_follows uf2
                 WHERE uf2.follower_id = uf.follower_id
                 AND uf2.following_id = auth.uid()
             ) THEN true
        WHEN p.privacy_settings->>'show_following' = 'none' THEN false
        ELSE true -- Default to public
    END as can_view
FROM public.user_follows uf
JOIN public.profiles p ON uf.following_id = p.id
WHERE p.id NOT IN (
    SELECT blocked_id FROM public.user_blocks
    WHERE blocker_id = uf.follower_id
);

COMMIT;
