'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Play, AlertCircle, Loader2 } from 'lucide-react';

interface YouTubeInsertModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (videoId: string, title?: string, caption?: string) => void;
}

export default function YouTubeInsertModal({
  isOpen,
  onClose,
  onInsert,
}: YouTubeInsertModalProps) {
  const [url, setUrl] = useState('');
  const [videoId, setVideoId] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setUrl('');
      setVideoId('');
      setError('');
    }
  }, [isOpen]);

  // Extract YouTube video ID from URL
  const extractVideoId = (urlString: string): string | null => {
    try {
      // Remove whitespace
      urlString = urlString.trim();
      
      // If it's just a video ID, return it
      if (/^[a-zA-Z0-9_-]{11}$/.test(urlString)) {
        return urlString;
      }

      // Handle various YouTube URL formats
      const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
        /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
        /youtube\.com\/v\/([a-zA-Z0-9_-]{11})/,
        /youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})/,
      ];

      for (const pattern of patterns) {
        const match = urlString.match(pattern);
        if (match) {
          return match[1];
        }
      }

      return null;
    } catch {
      return null;
    }
  };

  const handleUrlChange = (value: string) => {
    setUrl(value);
    setError('');
    
    const extractedId = extractVideoId(value);
    if (extractedId) {
      setVideoId(extractedId);
    } else {
      setVideoId('');
    }
  };

  const handleInsert = () => {
    if (!url.trim()) {
      setError('YouTube URL is required');
      return;
    }

    const extractedId = extractVideoId(url.trim());

    if (!extractedId) {
      setError('Please enter a valid YouTube URL or video ID');
      return;
    }

    setIsLoading(true);

    // Simulate processing delay for better UX
    setTimeout(() => {
      onInsert(extractedId);
      setIsLoading(false);
      onClose();
    }, 300);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleInsert();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const showPreview = videoId && videoId.length === 11;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900/95 border border-slate-700/50 backdrop-blur-md max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-slate-200 font-mono flex items-center gap-2">
            <Play className="h-4 w-4 text-red-400" />
            <span className="text-red-400">//</span>
            Insert YouTube Video
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* YouTube URL Input */}
          <div className="space-y-2">
            <Label htmlFor="url" className="text-slate-300 font-mono text-sm">
              YouTube URL or Video ID *
            </Label>
            <Input
              id="url"
              type="text"
              placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ or dQw4w9WgXcQ"
              value={url}
              onChange={(e) => handleUrlChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono"
              autoFocus
            />
            <p className="text-xs text-slate-500 font-mono">
              Supports: youtube.com/watch, youtu.be, youtube.com/embed, youtube.com/shorts, or just the video ID
            </p>
            {videoId && (
              <p className="text-xs text-green-400 font-mono">
                ✓ Video ID detected: {videoId}
              </p>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm font-mono bg-red-900/20 border border-red-700/30 rounded-lg p-3">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              {error}
            </div>
          )}

          {/* Video Preview */}
          {showPreview && (
            <div className="bg-slate-800/30 border border-slate-700/30 rounded-lg p-4">
              <p className="text-xs text-slate-500 font-mono mb-3">Preview:</p>
              <div className="relative">
                <div className="aspect-video bg-slate-700/30 rounded-lg overflow-hidden">
                  <iframe
                    src={`https://www.youtube.com/embed/${videoId}`}
                    title="YouTube video preview"
                    className="w-full h-full"
                    frameBorder="0"
                    allowFullScreen
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-slate-800/50 border-slate-600/50 text-slate-300 hover:bg-slate-700/50 font-mono"
          >
            Cancel
          </Button>
          <Button
            onClick={handleInsert}
            disabled={!url.trim() || !videoId || isLoading}
            className="bg-red-600 hover:bg-red-700 text-white font-mono"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                Inserting...
              </div>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Insert Video
              </>
            )}
          </Button>
        </DialogFooter>

        <div className="text-xs text-slate-500 font-mono text-center border-t border-slate-700/30 pt-3">
          Tip: Press Ctrl+Enter to insert, Esc to cancel
        </div>
      </DialogContent>
    </Dialog>
  );
}