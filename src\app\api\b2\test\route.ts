// src/app/api/b2/test/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';

export async function GET(request: NextRequest) {
  try {
    // Simple B2 configuration test
    const requiredEnvVars = [
      'B2_APPLICATION_KEY_ID',
      'B2_APPLICATION_KEY',
      'B2_BUCKET_NAME',
      'B2_ENDPOINT',
      'B2_REGION'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Missing environment variables',
        missingVars,
      });
    }

    // Test actual B2 connection
    try {
      const b2Client = new S3Client({
        endpoint: `https://${process.env.B2_ENDPOINT}`,
        region: process.env.B2_REGION || 'us-east-005',
        credentials: {
          accessKeyId: process.env.B2_APPLICATION_KEY_ID!,
          secretAccessKey: process.env.B2_APPLICATION_KEY!,
        },
        forcePathStyle: true,
      });

      // Try to list objects (this will test the connection)
      const command = new ListObjectsV2Command({
        Bucket: process.env.B2_BUCKET_NAME,
        MaxKeys: 1,
      });

      await b2Client.send(command);

      return NextResponse.json({
        success: true,
        message: 'B2 connection successful',
        config: {
          endpoint: process.env.B2_ENDPOINT,
          region: process.env.B2_REGION,
          bucket: process.env.B2_BUCKET_NAME,
          hasKeyId: !!process.env.B2_APPLICATION_KEY_ID,
          hasKey: !!process.env.B2_APPLICATION_KEY,
        },
        timestamp: new Date().toISOString(),
      });

    } catch (b2Error) {
      console.error('B2 Connection Error:', b2Error);
      return NextResponse.json({
        success: false,
        error: 'B2 connection failed',
        details: b2Error instanceof Error ? b2Error.message : 'Unknown error',
      });
    }

  } catch (error) {
    console.error('Test API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return NextResponse.json({}, { status: 200 });
}
