import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get IP from various possible headers
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const cfConnectingIp = request.headers.get('cf-connecting-ip'); // Cloudflare
    
    let ip = 'unknown';
    
    if (forwarded) {
      // x-forwarded-for can contain multiple IPs, take the first one
      ip = forwarded.split(',')[0].trim();
    } else if (realIp) {
      ip = realIp;
    } else if (cfConnectingIp) {
      ip = cfConnectingIp;
    } else {
      // Fallback to request IP (might not work in all deployments)
      ip = request.ip || 'unknown';
    }

    return NextResponse.json({ 
      ip,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting IP:', error);
    return NextResponse.json(
      { error: 'Failed to get IP' },
      { status: 500 }
    );
  }
}

export async function POST() {
  // Also support POST if needed
  return GET({} as NextRequest);
}