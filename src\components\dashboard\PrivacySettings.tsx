'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

// Import the CSS that contains adaptive-text-title styles
import '@/components/review-form/style/NewReview.css';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Shield,
  Eye,
  EyeOff,
  Users,
  Lock,
  Globe,
  Save,
  AlertCircle,
  CheckCircle,
  Loader2,
  Info,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  getPrivacySettings,
  savePrivacySettings
} from '@/app/u/dashboard/actions';
import {
  bulkUpdateReviewsPrivacy,
  bulkUpdateSurveysPrivacy
} from '@/lib/services/privacyService';

interface PrivacySettingsProps {
  userId: string;
  className?: string;
  isDarkBackground?: boolean;
}

interface PrivacySettings {
  profile_visibility: 'public' | 'private';
  show_reviews: 'public' | 'private';
  show_activity: 'public' | 'private';
  show_favorites: 'public' | 'private';
  show_performance_data: 'public' | 'private';
  show_gaming_profiles: 'public' | 'private';
  allow_friend_requests: boolean;
  show_online_status: boolean;
}

const privacyOptions = [
  { value: 'public', label: 'Public', icon: <Globe className="h-4 w-4" />, description: 'Visible to everyone' },
  { value: 'private', label: 'Private', icon: <Lock className="h-4 w-4" />, description: 'Only visible to you' }
];

const PrivacySettings: React.FC<PrivacySettingsProps> = ({
  userId,
  className = '',
  isDarkBackground: propIsDarkBackground
}) => {
  const hookIsDarkBackground = useBackgroundBrightness();
  const isDarkBackground = propIsDarkBackground ?? hookIsDarkBackground;
  const [settings, setSettings] = useState<PrivacySettings>({
    profile_visibility: 'public',
    show_reviews: 'public',
    show_activity: 'public',
    show_favorites: 'public',
    show_performance_data: 'public',
    show_gaming_profiles: 'public',
    allow_friend_requests: true,
    show_online_status: true
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    type: 'reviews' | 'surveys' | null;
    newValue: 'public' | 'private';
    title: string;
    description: string;
  }>({
    isOpen: false,
    type: null,
    newValue: 'public',
    title: '',
    description: ''
  });
  const { toast } = useToast();

  // Load existing privacy settings on mount
  useEffect(() => {
    loadPrivacySettings();
  }, [userId]);

  const loadPrivacySettings = async () => {
    try {
      setIsLoading(true);
      
      const result = await getPrivacySettings(userId);
      
      if (result.success && result.data) {
        setSettings(result.data);
      } else {
        toast({
          title: "Load Failed",
          description: result.error || "Could not load privacy settings",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      toast({
        title: "Load Failed",
        description: "Could not load privacy settings",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = (key: keyof PrivacySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleBulkPrivacyChange = (type: 'reviews' | 'surveys', newValue: 'public' | 'private') => {
    const isPrivate = newValue === 'private';
    const contentType = type === 'reviews' ? 'reviews' : 'performance data';

    setConfirmDialog({
      isOpen: true,
      type,
      newValue,
      title: `Make All ${type === 'reviews' ? 'Reviews' : 'Performance Data'} ${isPrivate ? 'Private' : 'Public'}`,
      description: `This will make ALL of your ${contentType} ${isPrivate ? 'private' : 'public'}. ${isPrivate ? 'Only you will be able to see them.' : 'They will be visible to everyone.'} This action will affect all your existing ${contentType}. Are you sure you want to continue?`
    });
  };

  const handleConfirmBulkChange = async () => {
    if (!confirmDialog.type) return;

    const isPrivate = confirmDialog.newValue === 'private';

    try {
      setIsSaving(true);

      let result;
      if (confirmDialog.type === 'reviews') {
        result = await bulkUpdateReviewsPrivacy(userId, isPrivate);
      } else {
        result = await bulkUpdateSurveysPrivacy(userId, isPrivate);
      }

      if (result.success) {
        // Update the profile setting to match
        const settingKey = confirmDialog.type === 'reviews' ? 'show_reviews' : 'show_performance_data';
        setSettings(prev => ({
          ...prev,
          [settingKey]: confirmDialog.newValue
        }));
        setHasChanges(true);

        toast({
          title: "Privacy Updated",
          description: `${result.updatedCount || 0} ${confirmDialog.type} updated successfully.`,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: `Failed to update ${confirmDialog.type} privacy. Please try again.`,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
      setConfirmDialog({
        isOpen: false,
        type: null,
        newValue: 'public',
        title: '',
        description: ''
      });
    }
  };

  const savePrivacySettingsHandler = async () => {
    if (!hasChanges) return;
    
    setIsSaving(true);
    try {
      const result = await savePrivacySettings(userId, settings);
      
      if (result.success) {
        toast({
          title: "Privacy Settings Saved",
          description: "Your privacy preferences have been updated successfully!",
        });
        setHasChanges(false);
      } else {
        toast({
          title: "Save Failed",
          description: result.error || "Could not save privacy settings. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Could not save privacy settings. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public': return <Globe className="h-4 w-4 text-green-400" />;
      case 'private': return <Lock className="h-4 w-4 text-red-400" />;
      default: return <Eye className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-slate-300">
                <Loader2 className="animate-spin" size={24} />
                <span>Loading privacy settings...</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Privacy Settings Header */}
      <div className="space-y-4">
        <div className="border-l-4 border-purple-500 pl-4">
          <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            <span className="text-violet-400/60">&lt;</span>
            <span className="px-2">Privacy & Visibility Controls</span>
            <span className="text-violet-400/60">/&gt;</span>
          </span>
        </div>
      </div>

      {/* Profile Visibility */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader>
          <CardTitle className="text-lg text-white font-mono">
            <span className="text-purple-400 mr-1">//</span>
            Profile Visibility
          </CardTitle>
          <p className="text-xs text-gray-400 mt-1 font-mono">
            Control who can access and view your profile page
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
              Who can view your profile
            </label>
            <Select 
              value={settings.profile_visibility} 
              onValueChange={(value: 'public' | 'private') => 
                handleSettingChange('profile_visibility', value)
              }
            >
              <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-700">
                {privacyOptions.map(option => (
                  <SelectItem key={option.value} value={option.value} className="font-mono text-xs">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Content Visibility */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader>
          <CardTitle className="text-lg text-white font-mono">
            <span className="text-purple-400 mr-1">//</span>
            Content Visibility
          </CardTitle>
          <p className="text-xs text-gray-400 mt-1 font-mono">
            Configure visibility settings for your content and data
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* All Reviews Privacy */}
          <div className="space-y-2">
            <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
              All Reviews Privacy
            </label>
            <p className="text-xs text-slate-400 font-mono">
              Controls privacy for ALL your reviews. Changing this will affect every review you've published.
            </p>
            <Select
              value={settings.show_reviews}
              onValueChange={(value: 'public' | 'private') =>
                handleBulkPrivacyChange('reviews', value)
              }
              disabled={isSaving}
            >
              <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-700">
                {privacyOptions.map(option => (
                  <SelectItem key={option.value} value={option.value} className="font-mono text-xs">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* All Performance Data Privacy */}
          <div className="space-y-2">
            <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
              All Performance Data Privacy
            </label>
            <p className="text-xs text-slate-400 font-mono">
              Controls privacy for ALL your performance surveys. Changing this will affect every survey you've submitted.
            </p>
            <Select
              value={settings.show_performance_data}
              onValueChange={(value: 'public' | 'private') =>
                handleBulkPrivacyChange('surveys', value)
              }
              disabled={isSaving}
            >
              <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-700">
                {privacyOptions.map(option => (
                  <SelectItem key={option.value} value={option.value} className="font-mono text-xs">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Activity Visibility - Disabled */}
          <div className="space-y-2 opacity-50">
            <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
              Activity Feed Visibility <span className="text-purple-400">(Soon)</span>
            </label>
            <Select disabled>
              <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                <SelectValue placeholder="Coming Soon" />
              </SelectTrigger>
            </Select>
          </div>

          {/* Favorites Visibility - Disabled */}
          <div className="space-y-2 opacity-50">
            <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
              Favorites & Wishlist Visibility <span className="text-purple-400">(Soon)</span>
            </label>
            <Select disabled>
              <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
                <SelectValue placeholder="Coming Soon" />
              </SelectTrigger>
            </Select>
          </div>

          {/* Privacy Notice */}
          <Alert className="border-blue-500/50 bg-blue-500/10">
            <Info className="h-4 w-4 text-blue-400" />
            <AlertDescription className="text-blue-300 font-mono text-xs">
              Privacy settings take effect immediately. Some cached data may take a few minutes to update across the platform.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Save Button */}
      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="sticky bottom-6 z-10"
        >
          <Card className="border-green-500/50 bg-gradient-to-r from-green-900/90 to-emerald-900/90 backdrop-blur">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-green-400" />
                  <span className="font-mono text-xs text-green-300">You have unsaved changes</span>
                </div>
                <Button 
                  onClick={savePrivacySettingsHandler}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Privacy Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => !open && setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <AlertDialogContent className="bg-slate-900/95 border border-slate-700/50 backdrop-blur-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-slate-200 font-mono">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              {confirmDialog.title}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-slate-400 font-mono text-sm">
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-slate-800/50 border-slate-600/50 text-slate-300 hover:bg-slate-700/50 font-mono"
              disabled={isSaving}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmBulkChange}
              disabled={isSaving}
              className="bg-purple-600 hover:bg-purple-700 text-white font-mono"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                'Confirm'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default PrivacySettings;