'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Play, ExternalLink, Eye, ThumbsUp, MessageCircle, Calendar, Clock, Users, ChevronLeft, ChevronRight, X, Youtube, Code } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserYouTubeData, UserYouTubeVideo } from '@/types/user-content';
import { FloatingParticles, MagicContainer, AnimatedCounter } from './MagicUIIntegration';
import { useTouchGestures } from '@/hooks/use-touch-gestures';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';

// Import CSS for adaptive text classes
import '@/components/review-form/style/NewReview.css';

interface YouTubeModuleProps {
  youtubeData: UserYouTubeData | null;
  isLoading?: boolean;
  error?: string | null;
  theme?: 'cosmic' | 'Cosmic' | 'ocean' | 'Ocean' | 'forest' | 'Forest' | 'crimson' | 'Crimson' | 'silver' | 'Silver' | 'amber' | 'Amber';
  className?: string;
  maxVideos?: number;
  showChannelStats?: boolean;
  enableVideoPreload?: boolean;
}

const YouTubeModule: React.FC<YouTubeModuleProps> = ({
  youtubeData,
  isLoading = false,
  error = null,
  theme = 'cosmic',
  className = '',
  maxVideos = 4,
  showChannelStats = true,
  enableVideoPreload = true
}) => {
  const [selectedVideo, setSelectedVideo] = useState<UserYouTubeVideo | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [preloadedThumbnails, setPreloadedThumbnails] = useState<Set<string>>(new Set());
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  
  // Background brightness detection for adaptive text
  const isDarkBackground = useBackgroundBrightness();

  // Memoize filtered videos for better performance
  const displayVideos = useMemo(() => {
    if (!youtubeData?.videos) return [];
    return youtubeData.videos.slice(0, maxVideos);
  }, [youtubeData?.videos, maxVideos]);

  // Define closeModal early to avoid reference error
  const closeModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedVideo(null);
    setCurrentVideoIndex(0);
  }, []);

  // Define navigateVideo early to avoid reference error
  const navigateVideo = useCallback((direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? Math.max(0, currentVideoIndex - 1)
      : Math.min(displayVideos.length - 1, currentVideoIndex + 1);
    
    if (newIndex !== currentVideoIndex && displayVideos[newIndex]) {
      setSelectedVideo(displayVideos[newIndex]);
      setCurrentVideoIndex(newIndex);
    }
  }, [currentVideoIndex, displayVideos]);

  // Theme-based styling function adapted for profile themes
  const getThemeStyles = useCallback((theme: string) => {
    // Normalize theme name to lowercase
    const normalizedTheme = theme?.toLowerCase() || 'cosmic';
    
    const themeStyles = {
      cosmic: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-violet-400/20',
        accent: 'text-violet-400',
        accentRgb: '139, 92, 246', // violet-500 RGB
        button: 'bg-violet-400 hover:bg-violet-400/80 text-black',
        card: 'bg-slate-800/60 border-violet-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(139,92,246,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-violet-400/10 border-violet-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      },
      ocean: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-blue-400/20',
        accent: 'text-blue-400',
        accentRgb: '59, 130, 246', // blue-500 RGB
        button: 'bg-blue-400 hover:bg-blue-400/80 text-white',
        card: 'bg-slate-800/60 border-blue-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(59,130,246,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-blue-400/10 border-blue-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      },
      forest: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-emerald-400/20',
        accent: 'text-emerald-400',
        accentRgb: '52, 211, 153', // emerald-400 RGB
        button: 'bg-emerald-400 hover:bg-emerald-400/80 text-black',
        card: 'bg-slate-800/60 border-emerald-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(52,211,153,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-emerald-400/10 border-emerald-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      },
      crimson: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-red-400/20',
        accent: 'text-red-400',
        accentRgb: '248, 113, 113', // red-400 RGB
        button: 'bg-red-400 hover:bg-red-400/80 text-white',
        card: 'bg-slate-800/60 border-red-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(248,113,113,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-red-400/10 border-red-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      },
      silver: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-slate-400/20',
        accent: 'text-slate-400',
        accentRgb: '148, 163, 184', // slate-400 RGB
        button: 'bg-slate-400 hover:bg-slate-400/80 text-black',
        card: 'bg-slate-800/60 border-slate-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(148,163,184,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-slate-400/10 border-slate-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      },
      amber: {
        container: 'bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border-amber-400/20',
        accent: 'text-amber-400',
        accentRgb: '251, 191, 36', // amber-400 RGB
        button: 'bg-amber-400 hover:bg-amber-400/80 text-black',
        card: 'bg-slate-800/60 border-amber-400/10 backdrop-blur-sm',
        text: 'text-slate-100',
        glow: 'shadow-[0_0_20px_rgba(251,191,36,0.15)]',
        neural: 'shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm',
        codeAccent: 'bg-amber-400/10 border-amber-400/20',
        overlay: 'bg-gradient-to-t from-black/80 via-black/40 to-transparent'
      }
    };
    return themeStyles[normalizedTheme as keyof typeof themeStyles] || themeStyles.cosmic;
  }, []);

  // Touch gestures for mobile navigation
  const modalRef = useTouchGestures({
    onSwipeLeft: () => navigateVideo('next'),
    onSwipeRight: () => navigateVideo('prev'),
    onSwipeDown: closeModal,
    enabled: isModalOpen
  });

  // Memoize theme styles for better performance
  const styles = useMemo(() => getThemeStyles(theme), [theme, getThemeStyles]);

  // Format duration from ISO 8601 to readable format - Memoized
  const formatDuration = useCallback((duration: string): string => {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return '0:00';
    
    const hours = parseInt(match[1]?.replace('H', '') || '0');
    const minutes = parseInt(match[2]?.replace('M', '') || '0');
    const seconds = parseInt(match[3]?.replace('S', '') || '0');
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Format number to readable format - Memoized
  const formatNumber = useCallback((num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }, []);

  // Format date - Memoized
  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric' 
    });
  }, []);

  // Preload thumbnails for better UX
  useEffect(() => {
    if (enableVideoPreload && displayVideos.length > 0) {
      const preloadThumbnails = async () => {
        const promises = displayVideos.slice(0, 4).map(video => {
          return new Promise<string>((resolve) => {
            const img = new Image();
            img.onload = () => resolve(video.id);
            img.onerror = () => resolve(video.id);
            img.src = video.thumbnail;
          });
        });
        
        const loaded = await Promise.all(promises);
        setPreloadedThumbnails(new Set(loaded));
      };
      
      preloadThumbnails();
    }
  }, [displayVideos, enableVideoPreload]);

  const openVideoModal = useCallback((video: UserYouTubeVideo) => {
    const videoIndex = displayVideos.findIndex(v => v.id === video.id);
    setSelectedVideo(video);
    setCurrentVideoIndex(videoIndex);
    setIsModalOpen(true);
  }, [displayVideos]);

  // Keyboard navigation for modal
  useEffect(() => {
    if (!isModalOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          closeModal();
          break;
        case 'ArrowLeft':
          navigateVideo('prev');
          break;
        case 'ArrowRight':
          navigateVideo('next');
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isModalOpen, closeModal, navigateVideo]);

  if (isLoading) {
    return (
      <div className={`w-full mb-8 ${className}`}>
        
        {/* Gaming header with code accent */}
        <div className="flex items-center gap-3 mb-6">
          <div className={`p-2 rounded-lg ${styles.codeAccent} border`}>
            <Youtube className={`h-5 w-5 ${styles.accent}`} />
          </div>
          <div className="space-y-2">
            <div className="h-6 bg-[#1A1A1C] rounded w-48 animate-pulse"></div>
            <div className="h-4 bg-[#1A1A1C]/50 rounded w-32 animate-pulse"></div>
          </div>
        </div>
        
        {/* 2x2 Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <motion.div 
              key={i} 
              className="bg-[#1A1A1C]/60 rounded-xl overflow-hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: i * 0.1 }}
            >
              <div className="aspect-video bg-[#1A1A1C] animate-pulse"></div>
              <div className="p-3 space-y-2">
                <div className="h-4 bg-[#1A1A1C] rounded animate-pulse"></div>
                <div className="h-3 bg-[#1A1A1C]/50 rounded w-3/4 animate-pulse"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !youtubeData) {
    return (
      <div className={`w-full mb-8 ${className}`}>
        
        <div className="text-center">
          <motion.div 
            className={`mx-auto mb-4 p-4 rounded-2xl ${styles.codeAccent} border w-fit`}
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Code className={`h-8 w-8 ${styles.accent}`} />
          </motion.div>
          
          <h3 className="text-lg font-bold text-slate-100 mb-2">
            // YouTube Module Not Configured
          </h3>
          <p className="text-slate-400 text-sm max-w-xs mx-auto leading-relaxed">
            Connect your YouTube channel in the dashboard to showcase your latest content.
          </p>
          
          <motion.div 
            className={`mt-4 text-xs ${styles.accent} font-mono`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {"</> "}developer.configure(youtube_api)
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`w-full mb-8 ${className}`}>
        
        {/* Neural Channel Header with Gaming Aesthetics */}
        {showChannelStats && (
          <motion.div 
            className="flex items-center gap-4 mb-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="relative">
              <a
                href={youtubeData.channelUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <motion.img
                  src={youtubeData.channel.thumbnail}
                  alt={youtubeData.channel.title}
                  className="w-16 h-16 rounded-2xl border-2 border-[#A1A1AA]/20 hover:border-[#A1A1AA]/40 transition-colors cursor-pointer"
                  loading="lazy"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                />
              </a>
              <motion.div 
                className="absolute -bottom-1 -right-1 bg-red-600 text-white text-xs rounded-full px-2 py-1 font-bold"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, type: "spring" }}
              >
                YT
              </motion.div>
            </div>
            
            <div className="flex-1">
              <h2 className={`adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'} text-xl font-bold mb-1 flex items-center gap-2`}>
                {youtubeData.channel.title}
                <motion.span 
                  className={`text-xs ${styles.accent} font-mono opacity-60`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.6 }}
                  transition={{ delay: 0.6 }}
                >
                  // verified
                </motion.span>
              </h2>
              
              
              <div className={`flex items-center gap-4 text-sm adaptive-text-subtitle ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
                <motion.div 
                  className="flex items-center gap-1"
                  whileHover={{ scale: 1.05 }}
                >
                  <Users className="h-4 w-4" />
                  <AnimatedCounter value={youtubeData.channel.subscriberCount} />
                  <span>subs</span>
                </motion.div>
                <motion.div 
                  className="flex items-center gap-1"
                  whileHover={{ scale: 1.05 }}
                >
                  <Play className="h-4 w-4" />
                  <AnimatedCounter value={youtubeData.channel.videoCount} />
                  <span>videos</span>
                </motion.div>
              </div>
            </div>
            
          </motion.div>
        )}

        {/* Neural 2x2 Video Grid - Optimized for 4 videos */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-full"
          role="grid"
          aria-label="Latest YouTube videos"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          {displayVideos.map((video, index) => (
            <motion.div
              key={video.id}
              className={`relative ${styles.card} border rounded-xl overflow-hidden transition-all duration-300 cursor-pointer group min-w-0 max-w-full`}
              onClick={() => openVideoModal(video)}
              role="gridcell"
              tabIndex={0}
              aria-label={`Play video: ${video.title}`}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  openVideoModal(video);
                }
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              whileTap={{ scale: 0.98 }}
              style={{
                boxShadow: `0 0 15px rgba(${styles.accentRgb}, 0.1)`
              }}
            >
              <div className="relative">
                <img
                  src={video.thumbnail}
                  alt={`Thumbnail for ${video.title}`}
                  className="w-full aspect-video object-cover transition-transform duration-300"
                  loading={index < 4 ? 'eager' : 'lazy'}
                  decoding="async"
                />
                
                {/* Text Overlay with Gradient */}
                <div className={`absolute inset-0 ${styles.overlay}`}>
                  {/* Video Info Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <h3 className="text-white text-sm font-semibold line-clamp-2 mb-2 leading-tight">
                      {video.title}
                    </h3>
                    
                    <div className="flex items-center justify-between text-xs text-white/80">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          <span className="font-mono">{formatNumber(video.viewCount)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          <span className="font-mono">{formatNumber(video.likeCount)}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span className="font-mono">{formatDate(video.publishedAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Neural Play Overlay */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center bg-black/20">
                  <div
                    className={`p-3 rounded-full ${styles.codeAccent} border backdrop-blur-sm`}
                  >
                    <Play className={`h-6 w-6 ${styles.accent} fill-current`} />
                  </div>
                </div>
                
                {/* Duration Badge */}
                <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded-md font-mono">
                  {formatDuration(video.duration)}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
        
      </div>

      {/* Enhanced Video Modal */}
      <AnimatePresence>
        {isModalOpen && selectedVideo && (
          <motion.div 
            className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-2 md:p-4 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeModal}
          >
            <motion.div 
              ref={modalRef as React.RefObject<HTMLDivElement>}
              className={`${styles.container} border ${styles.neural} rounded-2xl max-w-4xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto relative touch-manipulation`}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Navigation Arrows */}
              {currentVideoIndex > 0 && (
                <motion.button
                  onClick={() => navigateVideo('prev')}
                  className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 ${styles.codeAccent} border hover:border-opacity-60 p-3 rounded-full transition-all duration-200`}
                  aria-label="Previous video"
                  whileHover={{ scale: 1.1, x: -2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ChevronLeft className={`h-6 w-6 ${styles.accent}`} />
                </motion.button>
              )}
              {currentVideoIndex < displayVideos.length - 1 && (
                <motion.button
                  onClick={() => navigateVideo('next')}
                  className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 ${styles.codeAccent} border hover:border-opacity-60 p-3 rounded-full transition-all duration-200`}
                  aria-label="Next video"
                  whileHover={{ scale: 1.1, x: 2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <ChevronRight className={`h-6 w-6 ${styles.accent}`} />
                </motion.button>
              )}
              
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1 mr-4">
                    <h2 className={`text-xl font-bold ${styles.text} line-clamp-2 mb-2`}>
                      {selectedVideo.title}
                    </h2>
                    <p className={`text-sm ${styles.accent} font-mono`}>
                      // video {currentVideoIndex + 1} of {displayVideos.length}
                    </p>
                  </div>
                  <motion.button
                    onClick={closeModal}
                    className="text-[#A1A1AA] hover:text-white transition-colors p-2 rounded-lg hover:bg-[#1A1A1C]"
                    aria-label="Close modal"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="h-6 w-6" />
                  </motion.button>
                </div>
                
                <motion.div 
                  className="aspect-video mb-4 rounded-xl overflow-hidden"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <iframe
                    src={`https://www.youtube.com/embed/${selectedVideo.videoId}?autoplay=1`}
                    title={selectedVideo.title}
                    className="w-full h-full"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                </motion.div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  {[
                    { icon: Eye, label: 'Views', value: selectedVideo.viewCount },
                    { icon: ThumbsUp, label: 'Likes', value: selectedVideo.likeCount },
                    { icon: MessageCircle, label: 'Comments', value: selectedVideo.commentCount },
                    { icon: Clock, label: 'Duration', value: formatDuration(selectedVideo.duration), isText: true }
                  ].map((stat, index) => (
                    <motion.div 
                      key={stat.label}
                      className={`${styles.card} border rounded-xl p-3 text-center`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <stat.icon className={`h-5 w-5 ${styles.accent} mx-auto mb-1`} />
                      <div className="text-xs text-[#A1A1AA] mb-1">{stat.label}</div>
                      <div className={`text-sm font-bold ${styles.text} font-mono`}>
                        {stat.isText ? stat.value : formatNumber(stat.value as number)}
                      </div>
                    </motion.div>
                  ))}
                </div>
                
                {selectedVideo.description && (
                  <motion.div 
                    className={`${styles.card} border rounded-xl p-4`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    <h3 className={`font-semibold ${styles.text} mb-2 flex items-center gap-2`}>
                      Description
                      <span className={`text-xs ${styles.accent} font-mono opacity-60`}>// details</span>
                    </h3>
                    <p className="text-[#A1A1AA] text-sm whitespace-pre-line line-clamp-4 leading-relaxed">
                      {selectedVideo.description}
                    </p>
                  </motion.div>
                )}
                
                <motion.div 
                  className="flex justify-center mt-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <motion.a
                    href={selectedVideo.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`${styles.button} px-6 py-3 rounded-xl font-medium transition-all flex items-center gap-2 ${styles.glow}`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ExternalLink className="h-4 w-4" />
                    Watch on YouTube
                  </motion.a>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default YouTubeModule;