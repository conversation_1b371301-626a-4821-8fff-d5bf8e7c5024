import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import Image from 'next/image';
import Link from "next/link";
import { <PERSON>, Filter, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Sample data structure for a review card, similar to homepage but might have more details
interface ReviewListItem {
  id: string;
  title: string;
  game: string;
  platform: string;
  score: number;
  imageUrl: string;
  author: string;
  date: string;
  excerpt: string;
  slug: string;
  tags: string[];
  dataAiHint?: string;
  is_blocked: boolean;
}

// Mock data for reviews list
const allReviews: ReviewListItem[] = [
  { id: '1', title: "Epic Adventure in Eldoria", game: "Chronicles of Eldoria", platform: "PC", score: 9.2, imageUrl: "https://picsum.photos/seed/game1/600/400", author: "<PERSON>", date: "Oct 26, 2023", slug: "epic-adventure-eldoria", excerpt: "A sprawling open-world RPG that sets a new standard for the genre. Eldoria is a must-play for fantasy enthusiasts.", tags: ["RPG", "Open World", "Fantasy"], dataAiHint: "fantasy landscape", is_blocked: false },
  { id: '2', title: "Cybernetic Mayhem Unleashed", game: "Neon City Takedown", platform: "PS5", score: 8.8, imageUrl: "https://picsum.photos/seed/game2/600/400", author: "John Smith", date: "Oct 24, 2023", slug: "cybernetic-mayhem", excerpt: "Fast-paced, stylish, and brutally challenging. Neon City Takedown is a cyberpunk dream come true for action junkies.", tags: ["Action", "Cyberpunk", "Shooter"], dataAiHint: "cyberpunk city", is_blocked: false },
  { id: '3', title: "A Puzzling Masterpiece", game: "The Enigma Box", platform: "Switch", score: 9.5, imageUrl: "https://picsum.photos/seed/game3/600/400", author: "Alex Green", date: "Oct 22, 2023", slug: "puzzling-masterpiece", excerpt: "Mind-bending puzzles and a captivating story make The Enigma Box an unforgettable experience.", tags: ["Puzzle", "Indie", "Story-Rich"], dataAiHint: "abstract puzzle", is_blocked: false },
  { id: '4', title: "Indie Gem: Whispering Woods", game: "Whispering Woods", platform: "PC", score: 8.5, imageUrl: "https://picsum.photos/seed/article1/600/400", author: "Chris Lee", date: "Oct 25, 2023", slug: "indie-gem-whispering-woods", excerpt: "A charming and heartfelt adventure through a beautifully crafted world. Whispering Woods is pure magic.", tags: ["Adventure", "Indie", "Exploration"], dataAiHint: "mystical forest", is_blocked: false },
  { id: '5', title: "Retro Rewind: Pixel Warriors", game: "Pixel Warriors", platform: "Multi", score: 7.9, imageUrl: "https://picsum.photos/seed/article2/600/400", author: "Pat Kim", date: "Oct 23, 2023", slug: "retro-rewind-pixel-warriors", excerpt: "A nostalgic trip back to the golden age of arcade gaming, Pixel Warriors offers simple fun with a modern twist.", tags: ["Arcade", "Retro", "Pixel Art"], dataAiHint: "pixel art", is_blocked: false },
  { id: '6', title: "VR Breakthrough: Reality Shift", game: "Reality Shift", platform: "VR", score: 9.0, imageUrl: "https://picsum.photos/seed/article3/600/400", author: "Samira Khan", date: "Oct 21, 2023", slug: "vr-breakthrough-reality-shift", excerpt: "Immersive and innovative, Reality Shift pushes the boundaries of what's possible in virtual reality gaming.", tags: ["VR", "Sci-Fi", "Immersive Sim"], dataAiHint: "virtual reality", is_blocked: false },
   { id: '7', title: "Racing Thrills: Nitro Boost", game: "Nitro Boost Circuit", platform: "Xbox Series X", score: 8.2, imageUrl: "https://picsum.photos/seed/game4/600/400", author: "Mike Brown", date: "Oct 20, 2023", slug: "racing-thrills-nitro-boost", excerpt: "High-octane racing with stunning visuals and tight controls. A must for speed demons.", tags: ["Racing", "Sports", "Simulation"], dataAiHint: "race car", is_blocked: false },
  { id: '8', title: "Strategic Depth: Galactic Empires", game: "Galactic Empires IV", platform: "PC", score: 9.1, imageUrl: "https://picsum.photos/seed/game5/600/400", author: "Sarah Miller", date: "Oct 18, 2023", slug: "strategic-depth-galactic-empires", excerpt: "Command vast fleets and conquer the galaxy in this deep and rewarding 4X strategy game.", tags: ["Strategy", "4X", "Sci-Fi"], dataAiHint: "space battle", is_blocked: false },
];

export default function ReviewsPage() {
  // In a real app, these would be stateful and trigger fetching/filtering
  const searchTerm = ""; 
  const platformFilter = "all";
  const genreFilter = "all";
  const sortBy = "date_desc";

  return (
    <div className="space-y-8">
      <header className="text-center">
        <h1 className="text-4xl md:text-5xl font-bold text-primary mb-2">Game Reviews</h1>
        <p className="text-lg text-muted-foreground">Find your next favorite game with our expert reviews.</p>
      </header>

      {/* Filters Section */}
      <Card className="bg-card/70 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-xl flex items-center"><Filter className="mr-2 h-5 w-5 text-accent" /> Filter Reviews</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search reviews..." className="pl-10" defaultValue={searchTerm} />
          </div>
          <Select defaultValue={platformFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="pc">PC</SelectItem>
              <SelectItem value="ps5">PlayStation 5</SelectItem>
              <SelectItem value="xbox-series-x">Xbox Series X</SelectItem>
              <SelectItem value="switch">Nintendo Switch</SelectItem>
              <SelectItem value="vr">VR</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue={genreFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Genre" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Genres</SelectItem>
              <SelectItem value="action">Action</SelectItem>
              <SelectItem value="rpg">RPG</SelectItem>
              <SelectItem value="strategy">Strategy</SelectItem>
              <SelectItem value="puzzle">Puzzle</SelectItem>
              <SelectItem value="sports">Sports</SelectItem>
              <SelectItem value="adventure">Adventure</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue={sortBy}>
            <SelectTrigger>
              <SelectValue placeholder="Sort By" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date_desc">Newest First</SelectItem>
              <SelectItem value="date_asc">Oldest First</SelectItem>
              <SelectItem value="score_desc">Highest Score</SelectItem>
              <SelectItem value="score_asc">Lowest Score</SelectItem>
              <SelectItem value="title_asc">Title (A-Z)</SelectItem>
              <SelectItem value="title_desc">Title (Z-A)</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Reviews Grid */}
      {allReviews.filter(r => !r.is_blocked).length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {allReviews.filter(r => !r.is_blocked).map((review) => (
            <ReviewListItemCard key={review.id} review={review} />
          ))}
        </div>
      ) : (
        <Card className="col-span-full text-center p-10 bg-card/70 backdrop-blur-sm">
          <CardTitle className="text-2xl text-primary mb-2">No Reviews Found</CardTitle>
          <CardDescription className="text-muted-foreground">
            Try adjusting your search or filter criteria.
          </CardDescription>
        </Card>
      )}

      {/* Pagination (Placeholder) */}
      <div className="flex justify-center mt-8">
        <Button variant="outline" className="mr-2">Previous</Button>
        <Button>Next</Button>
      </div>
    </div>
  );
}

const ReviewListItemCard = ({ review }: { review: ReviewListItem }) => (
  <Card className="overflow-hidden hover:shadow-xl transition-shadow duration-300 ease-in-out transform hover:-translate-y-1 glow-purple h-full flex flex-col">
    <Link href={`/reviews/${review.slug}`} className="block flex flex-col flex-grow">
      <Image 
        src={review.imageUrl} 
        alt={`Cover art for ${review.game}`} 
        width={600} 
        height={400} 
        className="w-full h-48 object-cover"
        data-ai-hint={review.dataAiHint || "game cover"}
      />
      <CardHeader className="flex-grow">
        <CardTitle className="text-lg md:text-xl text-primary group-hover:text-accent transition-colors">{review.title}</CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          {review.game} - <span className="font-semibold text-accent">{review.platform}</span>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-2xl font-bold text-accent">{review.score.toFixed(1)}</span>
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className={`h-4 w-4 ${i < Math.round(review.score / 2) ? 'text-yellow-400 fill-yellow-400' : 'text-muted-foreground'}`} />
            ))}
          </div>
        </div>
        <p className="text-sm text-muted-foreground line-clamp-3">{review.excerpt}</p>
        <div className="text-xs text-muted-foreground pt-2">
          By {review.author} on {review.date}
        </div>
        <div className="flex flex-wrap gap-1 pt-1">
          {review.tags.map(tag => (
            <span key={tag} className="text-xs bg-secondary text-secondary-foreground px-2 py-0.5 rounded-full">{tag}</span>
          ))}
        </div>
      </CardContent>
    </Link>
  </Card>
);
