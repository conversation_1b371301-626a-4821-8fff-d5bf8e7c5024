'use client';

/**
 * Admin Global Settings Page
 * Provides comprehensive site settings management interface for administrators
 * Created: 20/01/2025 - Admin System Completion
 */

import React, { useState, useEffect } from 'react';
import { useAuthContext } from '@/hooks/use-auth-context';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import {
  getSiteSettings,
  updateSiteSettings,
  resetSettingsToDefaults,
  exportSettings,
  getSettingsCategories,
  type SiteSettings,
  type SettingsCategory
} from '@/lib/admin/settingsService';
import {
  Settings,
  TrendingUp,
  FileText,
  Shield,
  Bell,
  Plug,
  Save,
  RotateCcw,
  Download,
  AlertTriangle,
  Globe,
  Mail,
  Lock,
  Database
} from 'lucide-react';

export default function GlobalSettingsPage() {
  const { user, isLoading } = useAuthContext();
  const { toast } = useToast();

  // State management
  const [settings, setSettings] = useState<SiteSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeCategory, setActiveCategory] = useState<keyof SiteSettings>('general');

  // Form state for each category
  const [formData, setFormData] = useState<SiteSettings | null>(null);

  const categories = getSettingsCategories();

  // Load settings data
  const loadSettings = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const settingsData = await getSiteSettings(user.id);
      setSettings(settingsData);
      setFormData(settingsData);
    } catch (error) {
      console.error('Error loading settings:', error);
      toast({
        title: "Error loading settings",
        description: "Failed to load site settings",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (user?.id) {
      loadSettings();
    }
  }, [user?.id]);

  // Handle form field changes
  const handleFieldChange = (
    category: keyof SiteSettings,
    field: string,
    value: any
  ) => {
    if (!formData) return;

    setFormData({
      ...formData,
      [category]: {
        ...formData[category],
        [field]: value
      }
    });
  };

  // Save settings for a category
  const handleSaveCategory = async (category: keyof SiteSettings) => {
    if (!user?.id || !formData) return;

    try {
      setSaving(true);
      const result = await updateSiteSettings(user.id, category, formData[category]);

      if (result.success) {
        toast({
          title: "Settings saved",
          description: `${categories.find(c => c.id === category)?.name} settings have been updated successfully`,
        });
        setSettings(formData);
      } else {
        toast({
          title: "Save failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Save error",
        description: "Failed to save settings",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset category to defaults
  const handleResetCategory = async (category: keyof SiteSettings) => {
    if (!user?.id) return;

    try {
      setSaving(true);
      const result = await resetSettingsToDefaults(user.id, category);

      if (result.success) {
        toast({
          title: "Settings reset",
          description: `${categories.find(c => c.id === category)?.name} settings have been reset to defaults`,
        });
        await loadSettings(); // Reload to get defaults
      } else {
        toast({
          title: "Reset failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Reset error",
        description: "Failed to reset settings",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // Export settings
  const handleExportSettings = async () => {
    if (!user?.id) return;

    try {
      const result = await exportSettings(user.id);

      if (result.success && result.data) {
        const dataStr = JSON.stringify(result.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `criticalpixel-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        toast({
          title: "Settings exported",
          description: "Settings have been exported successfully",
        });
      } else {
        toast({
          title: "Export failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Export error",
        description: "Failed to export settings",
        variant: "destructive"
      });
    }
  };

  // Get category icon
  const getCategoryIcon = (iconName: string) => {
    switch (iconName) {
      case 'Settings': return <Settings className="h-5 w-5" />;
      case 'TrendingUp': return <TrendingUp className="h-5 w-5" />;
      case 'FileText': return <FileText className="h-5 w-5" />;
      case 'Shield': return <Shield className="h-5 w-5" />;
      case 'Bell': return <Bell className="h-5 w-5" />;
      case 'Plug': return <Plug className="h-5 w-5" />;
      default: return <Settings className="h-5 w-5" />;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Global Settings" description="Loading settings interface...">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading settings...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!user?.isAdmin) {
    return (
      <AdminLayout title="Access Denied" description="Admin access required">
        <Card className="max-w-md mx-auto mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You need administrator privileges to access global settings.
            </CardDescription>
          </CardHeader>
        </Card>
      </AdminLayout>
    );
  }

  if (!formData) {
    return (
      <AdminLayout title="Global Settings" description="Loading...">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-accent"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Global Settings"
      description="Configure site-wide settings and preferences"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Global Settings' }
      ]}
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Site Configuration</CardTitle>
                <CardDescription>Manage global settings for your CriticalPixel site</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleExportSettings}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Settings
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Settings Categories */}
        <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as keyof SiteSettings)}>
          <TabsList className="grid w-full grid-cols-6">
            {categories.map((category) => (
              <TabsTrigger key={category.id} value={category.id} className="flex items-center gap-2">
                {getCategoryIcon(category.icon)}
                <span className="hidden sm:inline">{category.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-5 w-5" />
                      General Settings
                    </CardTitle>
                    <CardDescription>Basic site configuration and information</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset General Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all general settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('general')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('general')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Site Name</label>
                    <Input
                      value={formData.general.site_name}
                      onChange={(e) => handleFieldChange('general', 'site_name', e.target.value)}
                      placeholder="Your site name"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Site URL</label>
                    <Input
                      value={formData.general.site_url}
                      onChange={(e) => handleFieldChange('general', 'site_url', e.target.value)}
                      placeholder="https://yoursite.com"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Site Description</label>
                  <Textarea
                    value={formData.general.site_description}
                    onChange={(e) => handleFieldChange('general', 'site_description', e.target.value)}
                    placeholder="Brief description of your site"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Admin Email</label>
                    <Input
                      type="email"
                      value={formData.general.admin_email}
                      onChange={(e) => handleFieldChange('general', 'admin_email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Timezone</label>
                    <Select
                      value={formData.general.timezone}
                      onValueChange={(value) => handleFieldChange('general', 'timezone', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">Eastern Time</SelectItem>
                        <SelectItem value="America/Chicago">Central Time</SelectItem>
                        <SelectItem value="America/Denver">Mountain Time</SelectItem>
                        <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                        <SelectItem value="Europe/London">London</SelectItem>
                        <SelectItem value="Europe/Paris">Paris</SelectItem>
                        <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Language</label>
                    <Select
                      value={formData.general.language}
                      onValueChange={(value) => handleFieldChange('general', 'language', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="es">Spanish</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                        <SelectItem value="de">German</SelectItem>
                        <SelectItem value="pt">Portuguese</SelectItem>
                        <SelectItem value="ja">Japanese</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4 border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Maintenance Mode</h4>
                      <p className="text-sm text-muted-foreground">
                        Enable maintenance mode to temporarily disable public access
                      </p>
                    </div>
                    <Switch
                      checked={formData.general.maintenance_mode}
                      onCheckedChange={(checked) => handleFieldChange('general', 'maintenance_mode', checked)}
                    />
                  </div>
                  {formData.general.maintenance_mode && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Maintenance Message</label>
                      <Textarea
                        value={formData.general.maintenance_message}
                        onChange={(e) => handleFieldChange('general', 'maintenance_message', e.target.value)}
                        placeholder="Message to display during maintenance"
                        rows={2}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SEO & Analytics Settings */}
          <TabsContent value="seo" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      SEO & Analytics
                    </CardTitle>
                    <CardDescription>Search engine optimization and tracking configuration</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset SEO Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all SEO settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('seo')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('seo')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Meta Title</label>
                  <Input
                    value={formData.seo.meta_title}
                    onChange={(e) => handleFieldChange('seo', 'meta_title', e.target.value)}
                    placeholder="Default meta title for your site"
                    maxLength={60}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {formData.seo.meta_title.length}/60 characters
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Meta Description</label>
                  <Textarea
                    value={formData.seo.meta_description}
                    onChange={(e) => handleFieldChange('seo', 'meta_description', e.target.value)}
                    placeholder="Default meta description for your site"
                    rows={3}
                    maxLength={160}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {formData.seo.meta_description.length}/160 characters
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Meta Keywords</label>
                  <Input
                    value={formData.seo.meta_keywords}
                    onChange={(e) => handleFieldChange('seo', 'meta_keywords', e.target.value)}
                    placeholder="gaming, reviews, community (comma-separated)"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Open Graph Image</label>
                    <Input
                      value={formData.seo.og_image}
                      onChange={(e) => handleFieldChange('seo', 'og_image', e.target.value)}
                      placeholder="https://yoursite.com/og-image.jpg"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Twitter Card Type</label>
                    <Select
                      value={formData.seo.twitter_card}
                      onValueChange={(value) => handleFieldChange('seo', 'twitter_card', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="summary">Summary</SelectItem>
                        <SelectItem value="summary_large_image">Summary Large Image</SelectItem>
                        <SelectItem value="app">App</SelectItem>
                        <SelectItem value="player">Player</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Google Analytics ID</label>
                    <Input
                      value={formData.seo.google_analytics_id}
                      onChange={(e) => handleFieldChange('seo', 'google_analytics_id', e.target.value)}
                      placeholder="GA-XXXXXXXXX"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Google Search Console</label>
                    <Input
                      value={formData.seo.google_search_console}
                      onChange={(e) => handleFieldChange('seo', 'google_search_console', e.target.value)}
                      placeholder="GSC-XXXXXXXXX"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Management Settings */}
          <TabsContent value="content" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Content Management
                    </CardTitle>
                    <CardDescription>User-generated content settings and moderation policies</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset Content Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all content settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('content')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('content')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Allow User Registration</h4>
                      <p className="text-sm text-muted-foreground">
                        Allow new users to create accounts
                      </p>
                    </div>
                    <Switch
                      checked={formData.content.allow_user_registration}
                      onCheckedChange={(checked) => handleFieldChange('content', 'allow_user_registration', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Require Email Verification</h4>
                      <p className="text-sm text-muted-foreground">
                        Require users to verify their email before accessing the site
                      </p>
                    </div>
                    <Switch
                      checked={formData.content.require_email_verification}
                      onCheckedChange={(checked) => handleFieldChange('content', 'require_email_verification', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Allow Anonymous Comments</h4>
                      <p className="text-sm text-muted-foreground">
                        Allow non-registered users to post comments
                      </p>
                    </div>
                    <Switch
                      checked={formData.content.allow_anonymous_comments}
                      onCheckedChange={(checked) => handleFieldChange('content', 'allow_anonymous_comments', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Moderate Comments</h4>
                      <p className="text-sm text-muted-foreground">
                        Require admin approval for all comments
                      </p>
                    </div>
                    <Switch
                      checked={formData.content.moderate_comments}
                      onCheckedChange={(checked) => handleFieldChange('content', 'moderate_comments', checked)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border-t pt-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Max Review Length</label>
                    <Input
                      type="number"
                      min="100"
                      max="50000"
                      value={formData.content.max_review_length}
                      onChange={(e) => handleFieldChange('content', 'max_review_length', parseInt(e.target.value) || 10000)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Characters (100-50,000)</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Max Comment Length</label>
                    <Input
                      type="number"
                      min="10"
                      max="5000"
                      value={formData.content.max_comment_length}
                      onChange={(e) => handleFieldChange('content', 'max_comment_length', parseInt(e.target.value) || 1000)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Characters (10-5,000)</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Featured Reviews Count</label>
                    <Input
                      type="number"
                      min="1"
                      max="20"
                      value={formData.content.featured_reviews_count}
                      onChange={(e) => handleFieldChange('content', 'featured_reviews_count', parseInt(e.target.value) || 6)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Number to display (1-20)</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Settings */}
          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Lock className="h-5 w-5" />
                      Security Settings
                    </CardTitle>
                    <CardDescription>Authentication and security policies</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset Security Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all security settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('security')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('security')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Enable Rate Limiting</h4>
                      <p className="text-sm text-muted-foreground">
                        Protect against abuse with request rate limiting
                      </p>
                    </div>
                    <Switch
                      checked={formData.security.enable_rate_limiting}
                      onCheckedChange={(checked) => handleFieldChange('security', 'enable_rate_limiting', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Require Strong Passwords</h4>
                      <p className="text-sm text-muted-foreground">
                        Enforce strong password requirements
                      </p>
                    </div>
                    <Switch
                      checked={formData.security.require_strong_passwords}
                      onCheckedChange={(checked) => handleFieldChange('security', 'require_strong_passwords', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Enable Two-Factor Authentication</h4>
                      <p className="text-sm text-muted-foreground">
                        Allow users to enable 2FA for their accounts
                      </p>
                    </div>
                    <Switch
                      checked={formData.security.enable_two_factor}
                      onCheckedChange={(checked) => handleFieldChange('security', 'enable_two_factor', checked)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border-t pt-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Max Login Attempts</label>
                    <Input
                      type="number"
                      min="3"
                      max="10"
                      value={formData.security.max_login_attempts}
                      onChange={(e) => handleFieldChange('security', 'max_login_attempts', parseInt(e.target.value) || 5)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Attempts before lockout (3-10)</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Session Timeout</label>
                    <Input
                      type="number"
                      min="300"
                      max="86400"
                      value={formData.security.session_timeout}
                      onChange={(e) => handleFieldChange('security', 'session_timeout', parseInt(e.target.value) || 3600)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Seconds (300-86400)</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Max File Size</label>
                    <Input
                      type="number"
                      min="1048576"
                      max="52428800"
                      value={formData.security.max_file_size}
                      onChange={(e) => handleFieldChange('security', 'max_file_size', parseInt(e.target.value) || 5242880)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">Bytes (1MB-50MB)</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications Settings */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Notifications
                    </CardTitle>
                    <CardDescription>Email and notification settings</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset Notification Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all notification settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('notifications')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('notifications')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Email Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Enable email notifications system
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.email_notifications}
                      onCheckedChange={(checked) => handleFieldChange('notifications', 'email_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Admin Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Send notifications to administrators
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.admin_notifications}
                      onCheckedChange={(checked) => handleFieldChange('notifications', 'admin_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">User Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Send notifications to users
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.user_notifications}
                      onCheckedChange={(checked) => handleFieldChange('notifications', 'user_notifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Newsletter</h4>
                      <p className="text-sm text-muted-foreground">
                        Enable newsletter subscription system
                      </p>
                    </div>
                    <Switch
                      checked={formData.notifications.newsletter_enabled}
                      onCheckedChange={(checked) => handleFieldChange('notifications', 'newsletter_enabled', checked)}
                    />
                  </div>
                </div>

                {formData.notifications.email_notifications && (
                  <div className="border-t pt-4 space-y-4">
                    <h4 className="font-medium">SMTP Configuration</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">SMTP Host</label>
                        <Input
                          value={formData.notifications.smtp_host}
                          onChange={(e) => handleFieldChange('notifications', 'smtp_host', e.target.value)}
                          placeholder="smtp.gmail.com"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">SMTP Port</label>
                        <Input
                          type="number"
                          min="1"
                          max="65535"
                          value={formData.notifications.smtp_port}
                          onChange={(e) => handleFieldChange('notifications', 'smtp_port', parseInt(e.target.value) || 587)}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">SMTP Username</label>
                        <Input
                          value={formData.notifications.smtp_username}
                          onChange={(e) => handleFieldChange('notifications', 'smtp_username', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">SMTP Password</label>
                        <Input
                          type="password"
                          value={formData.notifications.smtp_password}
                          onChange={(e) => handleFieldChange('notifications', 'smtp_password', e.target.value)}
                          placeholder="Your app password"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Integrations Settings */}
          <TabsContent value="integrations" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      Integrations
                    </CardTitle>
                    <CardDescription>Third-party services and API configurations</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Reset Integration Settings</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will reset all integration settings to their default values. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleResetCategory('integrations')}>
                            Reset to Defaults
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                    <Button onClick={() => handleSaveCategory('integrations')} disabled={saving}>
                      <Save className="mr-2 h-4 w-4" />
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="text-sm font-medium mb-2 block">IGDB API Key</label>
                  <Input
                    type="password"
                    value={formData.integrations.igdb_api_key}
                    onChange={(e) => handleFieldChange('integrations', 'igdb_api_key', e.target.value)}
                    placeholder="Your IGDB API key"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Required for game data integration
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Discord Webhook</label>
                    <Input
                      value={formData.integrations.discord_webhook}
                      onChange={(e) => handleFieldChange('integrations', 'discord_webhook', e.target.value)}
                      placeholder="https://discord.com/api/webhooks/..."
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">Slack Webhook</label>
                    <Input
                      value={formData.integrations.slack_webhook}
                      onChange={(e) => handleFieldChange('integrations', 'slack_webhook', e.target.value)}
                      placeholder="https://hooks.slack.com/services/..."
                    />
                  </div>
                </div>

                <div className="border-t pt-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Automatic Backups</h4>
                      <p className="text-sm text-muted-foreground">
                        Enable automatic database backups
                      </p>
                    </div>
                    <Switch
                      checked={formData.integrations.backup_enabled}
                      onCheckedChange={(checked) => handleFieldChange('integrations', 'backup_enabled', checked)}
                    />
                  </div>

                  {formData.integrations.backup_enabled && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Backup Frequency</label>
                        <Select
                          value={formData.integrations.backup_frequency}
                          onValueChange={(value) => handleFieldChange('integrations', 'backup_frequency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="hourly">Hourly</SelectItem>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Backup Retention (Days)</label>
                        <Input
                          type="number"
                          min="1"
                          max="365"
                          value={formData.integrations.backup_retention}
                          onChange={(e) => handleFieldChange('integrations', 'backup_retention', parseInt(e.target.value) || 30)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
