/**
 * Content Analysis Service for CriticalPixel
 * Analyzes Lexical editor content and extracts SEO insights
 */

export interface ContentAnalysis {
  textContent: string;
  wordCount: number;
  lineCount: number;
  readingTime: number; // in minutes
  keyPhrases: string[];
  gameAspects: string[];
  sentimentScore: number; // 0-100
  readabilityScore: number; // 0-100
  seoScore: number; // 0-100
  suggestions: string[];
}

export interface LexicalNode {
  type: string;
  text?: string;
  children?: LexicalNode[];
  format?: number;
  indent?: number;
  direction?: string | null;
  version?: number;
}

export interface LexicalRoot {
  root: LexicalNode;
}

export class ContentAnalyzer {
  private readonly gameAspectKeywords = [
    'gameplay', 'mechanics', 'controls', 'difficulty',
    'graphics', 'visuals', 'art style', 'animation',
    'story', 'narrative', 'plot', 'characters', 'dialogue',
    'sound', 'music', 'audio', 'soundtrack', 'voice acting',
    'performance', 'fps', 'loading', 'bugs', 'optimization',
    'multiplayer', 'online', 'co-op', 'competitive',
    'replayability', 'content', 'length', 'value'
  ];

  private readonly positiveWords = [
    'excellent', 'amazing', 'fantastic', 'outstanding', 'brilliant',
    'great', 'good', 'solid', 'impressive', 'beautiful',
    'smooth', 'polished', 'engaging', 'immersive', 'addictive',
    'fun', 'enjoyable', 'satisfying', 'rewarding', 'compelling'
  ];

  private readonly negativeWords = [
    'terrible', 'awful', 'horrible', 'disappointing', 'frustrating',
    'bad', 'poor', 'weak', 'boring', 'tedious',
    'buggy', 'broken', 'clunky', 'repetitive', 'shallow',
    'confusing', 'annoying', 'outdated', 'generic', 'uninspired'
  ];

  /**
   * Analyze Lexical editor content and return comprehensive insights
   */
  analyzeContent(lexicalJson: string): ContentAnalysis {
    try {
      const lexicalData: LexicalRoot = JSON.parse(lexicalJson);
      const textContent = this.extractTextFromLexical(lexicalData);
      
      if (!textContent.trim()) {
        return this.getEmptyAnalysis();
      }

      const wordCount = this.countWords(textContent);
      const lineCount = this.countLines(textContent);
      const readingTime = this.calculateReadingTime(wordCount);
      const keyPhrases = this.extractKeyPhrases(textContent);
      const gameAspects = this.extractGameAspects(textContent);
      const sentimentScore = this.analyzeSentiment(textContent);
      const readabilityScore = this.calculateReadability(textContent);
      const seoScore = this.calculateSEOScore(textContent, wordCount);
      const suggestions = this.generateSuggestions(textContent, wordCount, gameAspects);

      return {
        textContent,
        wordCount,
        lineCount,
        readingTime,
        keyPhrases,
        gameAspects,
        sentimentScore,
        readabilityScore,
        seoScore,
        suggestions
      };
    } catch (error) {
      console.error('[ContentAnalyzer] Error analyzing content:', error);
      return this.getEmptyAnalysis();
    }
  }

  /**
   * Extract plain text from Lexical JSON structure
   */
  private extractTextFromLexical(lexicalData: LexicalRoot): string {
    const extractText = (node: LexicalNode): string => {
      if (!node) return '';
      
      // Handle text nodes
      if (node.text) {
        return node.text;
      }
      
      // Handle nodes with children
      if (node.children && Array.isArray(node.children)) {
        const childTexts = node.children.map(extractText);
        
        // Add spacing based on node type
        if (node.type === 'paragraph' || node.type === 'heading') {
          return childTexts.join('') + '\n\n';
        } else if (node.type === 'listitem') {
          return '• ' + childTexts.join('') + '\n';
        } else {
          return childTexts.join(' ');
        }
      }
      
      return '';
    };

    try {
      const text = extractText(lexicalData.root);
      return text.replace(/\n{3,}/g, '\n\n').trim(); // Clean up excessive newlines
    } catch (error) {
      console.warn('[ContentAnalyzer] Error extracting text from Lexical:', error);
      return '';
    }
  }

  /**
   * Count words in text content
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Count lines of text content (non-empty lines only)
   */
  private countLines(text: string): number {
    return text.trim().split(/\n/).filter(line => line.trim().length > 0).length;
  }

  /**
   * Calculate estimated reading time in minutes
   */
  private calculateReadingTime(wordCount: number): number {
    const wordsPerMinute = 200; // Average reading speed
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Extract key phrases from content using frequency analysis
   */
  private extractKeyPhrases(text: string): string[] {
    const cleanText = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Extract 2-3 word phrases
    const words = cleanText.split(' ');
    const phrases: { [key: string]: number } = {};

    // Extract 2-word phrases
    for (let i = 0; i < words.length - 1; i++) {
      const phrase = `${words[i]} ${words[i + 1]}`;
      if (phrase.length > 5 && !this.isStopPhrase(phrase)) {
        phrases[phrase] = (phrases[phrase] || 0) + 1;
      }
    }

    // Extract 3-word phrases
    for (let i = 0; i < words.length - 2; i++) {
      const phrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
      if (phrase.length > 8 && !this.isStopPhrase(phrase)) {
        phrases[phrase] = (phrases[phrase] || 0) + 1;
      }
    }

    // Sort by frequency and return top phrases
    return Object.entries(phrases)
      .filter(([phrase, count]) => count >= 2) // Must appear at least twice
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([phrase]) => phrase);
  }

  /**
   * Check if a phrase contains stop words
   */
  private isStopPhrase(phrase: string): boolean {
    const stopWords = [
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
      'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
      'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves',
      'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his',
      'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself',
      'they', 'them', 'their', 'theirs', 'themselves'
    ];

    const words = phrase.split(' ');
    return words.some(word => stopWords.includes(word));
  }

  /**
   * Extract game-related aspects mentioned in the content
   */
  private extractGameAspects(text: string): string[] {
    const lowerText = text.toLowerCase();
    const foundAspects: string[] = [];

    this.gameAspectKeywords.forEach(aspect => {
      if (lowerText.includes(aspect)) {
        foundAspects.push(aspect);
      }
    });

    // Remove duplicates and return
    return [...new Set(foundAspects)];
  }

  /**
   * Analyze sentiment of the content
   */
  private analyzeSentiment(text: string): number {
    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (this.positiveWords.some(pos => word.includes(pos))) {
        positiveCount++;
      }
      if (this.negativeWords.some(neg => word.includes(neg))) {
        negativeCount++;
      }
    });

    const totalSentimentWords = positiveCount + negativeCount;
    if (totalSentimentWords === 0) return 50; // Neutral

    // Calculate sentiment score (0-100)
    const sentimentRatio = positiveCount / totalSentimentWords;
    return Math.round(sentimentRatio * 100);
  }

  /**
   * Calculate readability score based on sentence structure
   */
  private calculateReadability(text: string): number {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = this.calculateAvgSyllables(words);

    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    
    // Convert to 0-100 scale where higher is better
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Calculate average syllables per word (simplified)
   */
  private calculateAvgSyllables(words: string[]): number {
    const totalSyllables = words.reduce((sum, word) => {
      return sum + this.countSyllables(word);
    }, 0);

    return totalSyllables / words.length;
  }

  /**
   * Count syllables in a word (simplified)
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = 'aeiouy';
    let syllableCount = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        syllableCount++;
      }
      previousWasVowel = isVowel;
    }

    // Handle silent 'e'
    if (word.endsWith('e')) {
      syllableCount--;
    }

    return Math.max(1, syllableCount);
  }

  /**
   * Calculate overall SEO score based on content quality
   */
  private calculateSEOScore(text: string, wordCount: number): number {
    let score = 0;

    // Word count scoring (optimal range: 300-2000 words)
    if (wordCount >= 300 && wordCount <= 2000) {
      score += 30;
    } else if (wordCount >= 150) {
      score += 20;
    } else if (wordCount >= 50) {
      score += 10;
    }

    // Content structure scoring
    const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);
    if (paragraphs.length >= 3) score += 20;
    else if (paragraphs.length >= 2) score += 15;
    else if (paragraphs.length >= 1) score += 10;

    // Heading detection (simplified)
    const hasHeadings = text.includes('\n') && paragraphs.some(p => p.length < 100);
    if (hasHeadings) score += 15;

    // Game aspect coverage
    const gameAspects = this.extractGameAspects(text);
    if (gameAspects.length >= 5) score += 20;
    else if (gameAspects.length >= 3) score += 15;
    else if (gameAspects.length >= 1) score += 10;

    // Readability bonus
    const readability = this.calculateReadability(text);
    if (readability >= 60) score += 15;
    else if (readability >= 40) score += 10;
    else if (readability >= 20) score += 5;

    return Math.min(100, score);
  }

  /**
   * Generate content improvement suggestions
   */
  private generateSuggestions(text: string, wordCount: number, gameAspects: string[]): string[] {
    const suggestions: string[] = [];

    if (wordCount < 150) {
      suggestions.push('Add more detailed content (aim for 300+ words for better SEO)');
    } else if (wordCount < 300) {
      suggestions.push('Consider expanding your review for better search visibility');
    }

    if (gameAspects.length < 3) {
      suggestions.push('Cover more game aspects (graphics, gameplay, story, sound, etc.)');
    }

    const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);
    if (paragraphs.length < 2) {
      suggestions.push('Break content into multiple paragraphs for better readability');
    }

    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgWordsPerSentence = wordCount / sentences.length;
    if (avgWordsPerSentence > 25) {
      suggestions.push('Consider shorter sentences for better readability');
    }

    const readability = this.calculateReadability(text);
    if (readability < 40) {
      suggestions.push('Simplify language and sentence structure for better readability');
    }

    if (!text.toLowerCase().includes('recommend')) {
      suggestions.push('Consider adding a clear recommendation or conclusion');
    }

    return suggestions;
  }

  /**
   * Return empty analysis for cases with no content
   */
  private getEmptyAnalysis(): ContentAnalysis {
    return {
      textContent: '',
      wordCount: 0,
      lineCount: 0,
      readingTime: 0,
      keyPhrases: [],
      gameAspects: [],
      sentimentScore: 50,
      readabilityScore: 0,
      seoScore: 0,
      suggestions: ['Add content to your review to get SEO insights']
    };
  }
}

// Export singleton instance
export const contentAnalyzer = new ContentAnalyzer();
