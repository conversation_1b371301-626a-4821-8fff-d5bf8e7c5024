# 🏗️ Guia de Implementação Completo: Sistema de Configurações Administrativas

## 📋 Visão Geral

Este documento fornece um plano de implementação completo para o sistema de configurações administrativas usando Next.js, Supabase, TypeScript e Zod.

### 🎯 Objetivos

- Criar um sistema de configurações administrativas funcional e escalável
- Implementar validação robusta com Zod
- Usar as melhores práticas do Next.js e Supabase
- Garantir segurança através de RLS e verificações de admin
- Criar uma interface de usuário intuitiva e responsiva

### 🛠️ Stack Tecnológico

- **Frontend**: Next.js 14, React Server Components, TypeScript
- **Backend**: Supabase (PostgreSQL + Auth + RLS)
- **Validação**: Zod
- **UI**: shadcn/ui, Tailwind CSS
- **Estado**: React hooks + Server Actions

### 🎯 Recursos Implementados vs. Omitidos

**✅ IMPLEMENTAREMOS:**
- Configurações gerais (nome do site, URL, descrição, timezone, idioma, modo de manutenção)
- Configurações de SEO (meta tags, Google Analytics, Twitter cards)
- Gerenciamento de conteúdo (registro de usuários, moderação de comentários, limites)
- Configurações básicas de segurança (políticas de senha, timeouts de sessão)
- Configurações de notificação (flags de email, configuração SMTP)
- Integrações básicas (chaves de API, webhooks)

**❌ OMITIREMOS (muito complexos):**
- Sistema completo de backup/restore
- Implementação completa de 2FA com TOTP
- Rate limiting avançado com Redis
- Integrações complexas de analytics

---

## 🗃️ FASE 1: Estrutura da Base de Dados

### 1.1 Schema da Base de Dados

Criar arquivo: `apps/web/supabase/migrations/[timestamp]_create_admin_settings.sql`

```sql
-- Migration: Create admin settings system

-- Create enum for setting categories
CREATE TYPE admin_setting_category AS ENUM (
  'general',
  'seo',
  'content', 
  'security',
  'notifications',
  'integrations'
);

-- Create admin settings table
CREATE TABLE admin_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category admin_setting_category NOT NULL,
  key TEXT NOT NULL,
  value JSONB NOT NULL,
  schema JSONB, -- For validation metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(category, key)
);

-- Create indexes for performance
CREATE INDEX idx_admin_settings_category ON admin_settings(category);
CREATE INDEX idx_admin_settings_key ON admin_settings(key);

-- Enable RLS
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (admin only)
CREATE POLICY "Admin can manage settings" ON admin_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'isAdmin' = 'true'
    )
  );

-- Grant permissions
GRANT ALL ON admin_settings TO authenticated;

-- Create function to update timestamp
CREATE OR REPLACE FUNCTION update_admin_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_admin_settings_updated_at
  BEFORE UPDATE ON admin_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_admin_settings_updated_at();
```

### 1.2 Dados Padrão

```sql
-- Insert default settings
INSERT INTO admin_settings (category, key, value) VALUES
-- General settings
('general', 'site_name', '"CriticalPixel"'),
('general', 'site_url', '"https://criticalpixel.com"'),
('general', 'site_description', '"Gaming reviews and community"'),
('general', 'admin_email', '"<EMAIL>"'),
('general', 'timezone', '"UTC"'),
('general', 'language', '"en"'),
('general', 'maintenance_mode', 'false'),
('general', 'maintenance_message', '"Site under maintenance. Please check back later."'),

-- SEO settings
('seo', 'meta_title', '"CriticalPixel - Gaming Reviews & Community"'),
('seo', 'meta_description', '"The ultimate destination for gaming reviews, news, and community discussions."'),
('seo', 'meta_keywords', '"gaming, reviews, community, esports"'),
('seo', 'og_image', '""'),
('seo', 'twitter_card', '"summary_large_image"'),
('seo', 'google_analytics_id', '""'),
('seo', 'google_search_console', '""'),

-- Content settings
('content', 'allow_user_registration', 'true'),
('content', 'require_email_verification', 'true'),
('content', 'allow_anonymous_comments', 'false'),
('content', 'moderate_comments', 'true'),
('content', 'max_review_length', '10000'),
('content', 'max_comment_length', '1000'),
('content', 'featured_reviews_count', '6'),

-- Security settings
('security', 'enable_rate_limiting', 'true'),
('security', 'require_strong_passwords', 'true'),
('security', 'enable_two_factor', 'false'),
('security', 'max_login_attempts', '5'),
('security', 'session_timeout', '3600'),
('security', 'max_file_size', '5242880'),

-- Notifications settings
('notifications', 'email_notifications', 'true'),
('notifications', 'admin_notifications', 'true'),
('notifications', 'user_notifications', 'true'),
('notifications', 'newsletter_enabled', 'true'),
('notifications', 'smtp_host', '""'),
('notifications', 'smtp_port', '587'),
('notifications', 'smtp_username', '""'),
('notifications', 'smtp_password', '""'),

-- Integrations settings
('integrations', 'igdb_api_key', '""'),
('integrations', 'discord_webhook', '""'),
('integrations', 'slack_webhook', '""'),
('integrations', 'backup_enabled', 'false'),
('integrations', 'backup_frequency', '"daily"'),
('integrations', 'backup_retention', '30');
```

---

## 🔧 FASE 2: Tipos TypeScript e Validação Zod

### 2.1 Esquemas Zod e Tipos

Criar arquivo: `src/lib/admin/settings-schemas.ts`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

---

## ⚙️ FASE 3: Camada de Serviço

### 3.1 Serviço de Configurações

Criar arquivo: `src/lib/admin/settingsService.ts`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

---

## 🎛️ FASE 4: Server Actions

### 4.1 Server Actions para Mutações

Criar arquivo: `src/lib/admin/settings-actions.ts`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

---

## 🖼️ FASE 5: Componentes da Interface

### 5.1 Hook de Context de Autenticação

Atualizar arquivo: `src/hooks/use-auth-context.ts`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

### 5.2 Página Principal de Configurações

Atualizar arquivo: `src/app/admin/settings/page.tsx`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

### 5.3 Componente de Conteúdo das Configurações

Criar arquivo: `src/app/admin/settings/settings-content.tsx`

[Ver conteúdo completo nos arquivos detalhados abaixo...]

### 5.4 Formulários das Categorias

Criar arquivos de formulário para cada categoria:
- `src/app/admin/settings/forms/general-settings-form.tsx`
- `src/app/admin/settings/forms/seo-settings-form.tsx`
- `src/app/admin/settings/forms/content-settings-form.tsx`
- `src/app/admin/settings/forms/security-settings-form.tsx`
- `src/app/admin/settings/forms/notifications-settings-form.tsx`
- `src/app/admin/settings/forms/integrations-settings-form.tsx`

[Ver exemplos de implementação nos arquivos detalhados abaixo...]

---

## 🧪 FASE 6: Testes e Validação

### 6.1 Comandos para Executar

```bash
# Execute as migrações
npx supabase migration up

# Gere os tipos
npx supabase gen types typescript --local > src/types/database.ts

# Inicie o projeto
npm run dev
```

### 6.2 Checklist de Validação

- [ ] RLS ativado para admin_settings
- [ ] Verificação de admin em todas as operações
- [ ] Validação de dados com Zod
- [ ] Sanitização de entradas
- [ ] Tratamento de erros adequado
- [ ] Interface responsiva
- [ ] Feedback visual para usuário
- [ ] Reset para configurações padrão
- [ ] Export/import de configurações

---

## 📋 Resumo da Implementação

### ✅ O que foi implementado:

1. **Estrutura de Base de Dados**
   - Tabela `admin_settings` com JSONB flexível
   - RLS para segurança de admin
   - Triggers para timestamps automáticos

2. **Camada de Tipos e Validação**
   - Esquemas Zod para cada categoria
   - Tipos TypeScript inferidos
   - Validação em tempo de execução

3. **Camada de Serviço**
   - Operações CRUD completas
   - Verificação de autenticação/autorização
   - Tratamento de erros robusto

4. **Interface de Usuário**
   - Componentes reutilizáveis
   - Formulários validados
   - Feedback visual apropriado

5. **Server Actions**
   - Mutações seguras
   - Revalidação de cache
   - Tratamento de transições

### 🎯 Próximos Passos Recomendados:

1. **Implementar os demais formulários** (SEO, Segurança, etc.)
2. **Adicionar testes unitários**
3. **Implementar funcionalidade de export/import**
4. **Adicionar logs de auditoria**
5. **Otimizar performance com cache**
6. **Implementar backup automático das configurações**

---

## 📚 Arquivos Detalhados

Veja os arquivos individuais com implementações completas:

- [settings-schemas.ts](./settings-schemas.ts) - Esquemas Zod e tipos
- [settingsService.ts](./settingsService.ts) - Camada de serviço
- [settings-actions.ts](./settings-actions.ts) - Server Actions
- [use-auth-context.ts](./use-auth-context.ts) - Hook de autenticação
- [page.tsx](./admin-settings-page.tsx) - Página principal
- [settings-content.tsx](./settings-content.tsx) - Componente principal
- [general-settings-form.tsx](./general-settings-form.tsx) - Formulário exemplo

## 🔗 Recursos e Referências

- [Documentação do Supabase](https://supabase.com/docs)
- [Documentação do Next.js](https://nextjs.org/docs)
- [Documentação do Zod](https://zod.dev/)
- [shadcn/ui Components](https://ui.shadcn.com/)
- [Melhores Práticas de RLS](https://supabase.com/docs/guides/auth/row-level-security)

---

**Este guia fornece uma base sólida para implementar um sistema completo de configurações administrativas. Adapte conforme necessário para suas necessidades específicas.** 