# 📖 Guide 4: Server Actions and Form Handling

## 🎯 Objective
Implement Next.js Server Actions that bridge the gap between client-side forms and the service layer, providing secure, validated form handling with proper error management and cache invalidation.

## 🚀 Implementation Steps

### Step 1: Create the Server Actions File

Create the file: `src/lib/admin/settings-actions.ts`

```typescript
// ============================================================================
// ADMIN SETTINGS SERVER ACTIONS
// ============================================================================
// Description: Next.js Server Actions for admin settings form handling
//              Bridges client forms with the service layer securely
// Author: AI Assistant
// Date: [Current Date]
// Version: 1.0.0
// Dependencies: Next.js 14+, settings service layer
// ============================================================================

'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { createServerActionClient } from '@supabase/auth-helpers-nextjs';
import { 
  getSiteSettings, 
  updateSiteSettings, 
  resetSettingsToDefaults, 
  exportSettings, 
  importSettings,
  batchUpdateSettings,
  updateSettingValue,
  healthCheck
} from './settingsService';
import { SiteSettings, validateCategorySettings, safeValidateCategorySettings } from './settings-schemas';

// ============================================================================
// AUTHENTICATION UTILITIES
// ============================================================================

/**
 * Get Current Admin User ID
 * 
 * Retrieves the current user's ID from the authentication context.
 * Validates that the user is authenticated and has admin privileges.
 * 
 * @returns Promise<string> - The authenticated admin user's ID
 * @throws Error if user is not authenticated or not an admin
 */
async function getCurrentAdminUserId(): Promise<string> {
  try {
    // Create Supabase client for server actions
    const supabase = createServerActionClient({ cookies });
    
    // Get the current user from the session
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('Authentication error in server action:', error);
      throw new Error('Authentication failed - please log in again');
    }
    
    if (!user) {
      throw new Error('No authenticated user found - please log in');
    }
    
    // Check if user has admin privileges
    const isAdmin = user.user_metadata?.isAdmin === true;
    
    if (!isAdmin) {
      console.warn(`Non-admin user ${user.id} attempted to access admin settings`);
      throw new Error('Administrative privileges required to perform this action');
    }
    
    return user.id;
  } catch (error) {
    console.error('Error getting current admin user:', error);
    throw error;
  }
}

// Comment: Centralized auth checking ensures consistent security across
// all server actions and provides clear error messages for debugging.

// ============================================================================
// FORM DATA PARSING UTILITIES
// ============================================================================

/**
 * Parse Form Data by Category
 * 
 * Parses FormData objects based on the settings category, handling
 * different data types (strings, booleans, numbers) appropriately.
 * 
 * @param category - The settings category being parsed
 * @param formData - The FormData object from the form submission
 * @returns Parsed and typed data object
 */
function parseFormDataByCategory(category: keyof SiteSettings, formData: FormData): any {
  switch (category) {
    case 'general':
      return parseGeneralSettings(formData);
    case 'seo':
      return parseSEOSettings(formData);
    case 'content':
      return parseContentSettings(formData);
    case 'security':
      return parseSecuritySettings(formData);
    case 'notifications':
      return parseNotificationSettings(formData);
    case 'integrations':
      return parseIntegrationSettings(formData);
    default:
      throw new Error(`Unknown settings category: ${category}`);
  }
}

// Comment: Category-specific parsing ensures each form's data is handled
// according to its specific field types and validation requirements.

/**
 * Parse General Settings from FormData
 * 
 * Handles the general settings form data, converting strings and checkboxes
 * to appropriate JavaScript types for validation and storage.
 */
function parseGeneralSettings(formData: FormData) {
  return {
    // Text fields - trim whitespace and ensure proper encoding
    site_name: (formData.get('site_name') as string)?.trim() || '',
    site_url: (formData.get('site_url') as string)?.trim() || '',
    site_description: (formData.get('site_description') as string)?.trim() || '',
    admin_email: (formData.get('admin_email') as string)?.toLowerCase().trim() || '',
    timezone: (formData.get('timezone') as string)?.trim() || 'UTC',
    language: (formData.get('language') as string)?.toLowerCase().trim() || 'en',
    
    // Boolean fields - handle checkbox states properly
    maintenance_mode: formData.get('maintenance_mode') === 'on' || formData.get('maintenance_mode') === 'true',
    
    // Conditional text field - only required when maintenance mode is enabled
    maintenance_message: (formData.get('maintenance_message') as string)?.trim() || '',
  };
}

// Comment: Proper handling of form data types is crucial for validation
// and prevents common issues with checkbox and text field parsing.

/**
 * Parse SEO Settings from FormData
 * 
 * Handles SEO-specific form fields with proper trimming and formatting.
 */
function parseSEOSettings(formData: FormData) {
  return {
    // Meta tags - trim and validate length
    meta_title: (formData.get('meta_title') as string)?.trim() || '',
    meta_description: (formData.get('meta_description') as string)?.trim() || '',
    meta_keywords: (formData.get('meta_keywords') as string)?.toLowerCase().trim() || '',
    
    // Social media settings
    og_image: (formData.get('og_image') as string)?.trim() || '',
    twitter_card: (formData.get('twitter_card') as string) || 'summary_large_image',
    
    // Analytics integration
    google_analytics_id: (formData.get('google_analytics_id') as string)?.trim() || '',
    google_search_console: (formData.get('google_search_console') as string)?.trim() || '',
  };
}

/**
 * Parse Content Settings from FormData
 * 
 * Handles content management settings including boolean flags and numeric limits.
 */
function parseContentSettings(formData: FormData) {
  return {
    // Boolean flags for content policies
    allow_user_registration: formData.get('allow_user_registration') === 'on',
    require_email_verification: formData.get('require_email_verification') === 'on',
    allow_anonymous_comments: formData.get('allow_anonymous_comments') === 'on',
    moderate_comments: formData.get('moderate_comments') === 'on',
    
    // Numeric limits - parse and validate ranges
    max_review_length: parseInt(formData.get('max_review_length') as string, 10) || 10000,
    max_comment_length: parseInt(formData.get('max_comment_length') as string, 10) || 1000,
    featured_reviews_count: parseInt(formData.get('featured_reviews_count') as string, 10) || 6,
  };
}

/**
 * Parse Security Settings from FormData
 * 
 * Handles security configuration including timeout values and file size limits.
 */
function parseSecuritySettings(formData: FormData) {
  return {
    // Security feature toggles
    enable_rate_limiting: formData.get('enable_rate_limiting') === 'on',
    require_strong_passwords: formData.get('require_strong_passwords') === 'on',
    enable_two_factor: formData.get('enable_two_factor') === 'on',
    
    // Numeric security limits
    max_login_attempts: parseInt(formData.get('max_login_attempts') as string, 10) || 5,
    session_timeout: parseInt(formData.get('session_timeout') as string, 10) || 3600,
    max_file_size: parseInt(formData.get('max_file_size') as string, 10) || 5242880,
  };
}

/**
 * Parse Notification Settings from FormData
 * 
 * Handles email and notification configuration including SMTP settings.
 */
function parseNotificationSettings(formData: FormData) {
  return {
    // Notification toggles
    email_notifications: formData.get('email_notifications') === 'on',
    admin_notifications: formData.get('admin_notifications') === 'on',
    user_notifications: formData.get('user_notifications') === 'on',
    newsletter_enabled: formData.get('newsletter_enabled') === 'on',
    
    // SMTP configuration - handle sensitive data carefully
    smtp_host: (formData.get('smtp_host') as string)?.trim() || '',
    smtp_port: parseInt(formData.get('smtp_port') as string, 10) || 587,
    smtp_username: (formData.get('smtp_username') as string)?.trim() || '',
    smtp_password: (formData.get('smtp_password') as string) || '', // Don't trim passwords
  };
}

/**
 * Parse Integration Settings from FormData
 * 
 * Handles third-party service integrations and backup configuration.
 */
function parseIntegrationSettings(formData: FormData) {
  return {
    // API keys and webhooks - handle sensitive data
    igdb_api_key: (formData.get('igdb_api_key') as string)?.trim() || '',
    discord_webhook: (formData.get('discord_webhook') as string)?.trim() || '',
    slack_webhook: (formData.get('slack_webhook') as string)?.trim() || '',
    
    // Backup configuration
    backup_enabled: formData.get('backup_enabled') === 'on',
    backup_frequency: (formData.get('backup_frequency') as string) || 'daily',
    backup_retention: parseInt(formData.get('backup_retention') as string, 10) || 30,
  };
}

// Comment: Each parsing function handles the specific data types and
// validation requirements for its category while maintaining consistency.

// ============================================================================
// CORE SETTINGS ACTIONS
// ============================================================================

/**
 * Update Settings Action
 * 
 * Generic server action for updating settings in any category.
 * Handles form data parsing, validation, and database updates.
 * 
 * @param category - The settings category to update
 * @param formData - Form data from the client
 * @returns Promise<{success: boolean, error?: string, data?: any}>
 */
export async function updateSettingsAction(
  category: keyof SiteSettings,
  formData: FormData
): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Parse form data according to category
    const parsedData = parseFormDataByCategory(category, formData);
    
    // Validate the parsed data using the appropriate schema
    const validationResult = safeValidateCategorySettings(category, parsedData);
    
    if (!validationResult.success) {
      // Return validation errors to the client
      return { 
        success: false, 
        error: `Validation failed: ${validationResult.errors?.map(e => e.message).join(', ')}` 
      };
    }
    
    // Update settings using the service layer
    const result = await updateSiteSettings(userId, category, validationResult.data);
    
    if (result.success) {
      // Revalidate the admin settings page to reflect changes
      revalidatePath('/admin/settings');
      
      // Also revalidate any pages that might use these settings
      if (category === 'general') {
        revalidatePath('/'); // Homepage might use site name/description
      }
      
      console.info(`Successfully updated ${category} settings via server action`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`Error in updateSettingsAction for ${category}:`, error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred while updating settings' 
    };
  }
}

// Comment: Generic update action provides consistent behavior across all
// categories while handling category-specific parsing and validation.

/**
 * Reset Settings to Defaults Action
 * 
 * Server action to reset settings for a specific category or all categories
 * back to their default values. Useful for troubleshooting and cleanup.
 * 
 * @param category - Optional category to reset (resets all if undefined)
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function resetSettingsAction(
  category?: keyof SiteSettings
): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Reset settings using the service layer
    const result = await resetSettingsToDefaults(userId, category);
    
    if (result.success) {
      // Revalidate relevant pages
      revalidatePath('/admin/settings');
      
      if (!category || category === 'general') {
        revalidatePath('/'); // Homepage might use general settings
      }
      
      console.info(`Successfully reset ${category || 'all'} settings to defaults`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`Error resetting ${category || 'all'} settings:`, error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to reset settings to defaults' 
    };
  }
}

// Comment: Reset functionality provides a safety net for administrators
// when settings changes cause issues or when starting fresh is needed.

// ============================================================================
// CATEGORY-SPECIFIC ACTIONS
// ============================================================================

/**
 * Update General Settings Action
 * 
 * Dedicated server action for updating general settings.
 * Provides a clean API for the general settings form.
 */
export async function updateGeneralSettingsAction(formData: FormData) {
  return await updateSettingsAction('general', formData);
}

/**
 * Update SEO Settings Action
 * 
 * Dedicated server action for updating SEO and analytics settings.
 * Handles meta tags, social media, and tracking configuration.
 */
export async function updateSEOSettingsAction(formData: FormData) {
  return await updateSettingsAction('seo', formData);
}

/**
 * Update Content Settings Action
 * 
 * Dedicated server action for updating content management settings.
 * Handles user registration, moderation, and content limits.
 */
export async function updateContentSettingsAction(formData: FormData) {
  return await updateSettingsAction('content', formData);
}

/**
 * Update Security Settings Action
 * 
 * Dedicated server action for updating security policies.
 * Handles authentication, session, and file upload security.
 */
export async function updateSecuritySettingsAction(formData: FormData) {
  return await updateSettingsAction('security', formData);
}

/**
 * Update Notification Settings Action
 * 
 * Dedicated server action for updating notification and email settings.
 * Handles SMTP configuration and notification preferences.
 */
export async function updateNotificationSettingsAction(formData: FormData) {
  return await updateSettingsAction('notifications', formData);
}

/**
 * Update Integration Settings Action
 * 
 * Dedicated server action for updating third-party integrations.
 * Handles API keys, webhooks, and backup configuration.
 */
export async function updateIntegrationSettingsAction(formData: FormData) {
  return await updateSettingsAction('integrations', formData);
}

// Comment: Category-specific actions provide clean, typed APIs for forms
// while leveraging the shared validation and update logic.

// ============================================================================
// BULK OPERATIONS
// ============================================================================

/**
 * Bulk Update Settings Action
 * 
 * Server action for updating multiple categories of settings in a single operation.
 * More efficient than individual updates when changing many settings.
 * 
 * @param updates - Partial SiteSettings object with categories to update
 * @returns Promise<{success: boolean, errors?: string[]}>
 */
export async function bulkUpdateSettingsAction(
  updates: Partial<SiteSettings>
): Promise<{ success: boolean; errors?: string[] }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Use the service layer's batch update functionality
    const result = await batchUpdateSettings(userId, updates);
    
    if (result.success) {
      // Revalidate all relevant pages after bulk update
      revalidatePath('/admin/settings');
      revalidatePath('/'); // Homepage might use various settings
      
      console.info(`Successfully performed bulk update of ${Object.keys(updates).length} categories`);
    }
    
    return result;
    
  } catch (error) {
    console.error('Error in bulk update settings action:', error);
    
    return { 
      success: false, 
      errors: [error instanceof Error ? error.message : 'Unknown error in bulk update'] 
    };
  }
}

// Comment: Bulk operations improve performance and provide atomicity
// when updating multiple related settings simultaneously.

// ============================================================================
// IMPORT/EXPORT ACTIONS
// ============================================================================

/**
 * Export Settings Action
 * 
 * Server action to export all current settings as a JSON backup.
 * Returns the complete settings configuration for download or storage.
 * 
 * @returns Promise<{success: boolean, data?: SiteSettings, error?: string}>
 */
export async function exportSettingsAction(): Promise<{ success: boolean; data?: SiteSettings; error?: string }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Export settings using the service layer
    const result = await exportSettings(userId);
    
    if (result.success) {
      console.info('Settings exported successfully via server action');
    }
    
    return result;
    
  } catch (error) {
    console.error('Error exporting settings:', error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to export settings' 
    };
  }
}

/**
 * Import Settings Action
 * 
 * Server action to import a complete settings configuration from a backup.
 * Validates all data before applying changes and provides restore functionality.
 * 
 * @param settings - Complete SiteSettings object to import
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function importSettingsAction(
  settings: SiteSettings
): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Import settings using the service layer
    const result = await importSettings(userId, settings);
    
    if (result.success) {
      // Revalidate all pages after import
      revalidatePath('/admin/settings');
      revalidatePath('/');
      
      console.info('Settings imported successfully via server action');
    }
    
    return result;
    
  } catch (error) {
    console.error('Error importing settings:', error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to import settings' 
    };
  }
}

// Comment: Import/export actions enable configuration backup and restore
// while maintaining data integrity through validation.

// ============================================================================
// UTILITY ACTIONS
// ============================================================================

/**
 * Update Single Setting Action
 * 
 * Server action for updating a single setting value without affecting
 * other settings in the category. Useful for quick toggles and adjustments.
 * 
 * @param category - The settings category
 * @param key - The specific setting key
 * @param value - The new value to set
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function updateSingleSettingAction(
  category: keyof SiteSettings,
  key: string,
  value: any
): Promise<{ success: boolean; error?: string }> {
  try {
    // Get the current admin user
    const userId = await getCurrentAdminUserId();
    
    // Update the single setting using the service layer
    const result = await updateSettingValue(userId, category, key, value);
    
    if (result.success) {
      // Revalidate relevant pages
      revalidatePath('/admin/settings');
      
      if (category === 'general') {
        revalidatePath('/');
      }
      
      console.info(`Successfully updated single setting ${category}.${key}`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`Error updating single setting ${category}.${key}:`, error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update setting' 
    };
  }
}

/**
 * Health Check Action
 * 
 * Server action to perform a health check on the settings system.
 * Useful for monitoring and troubleshooting.
 * 
 * @returns Promise<{healthy: boolean, issues?: string[]}>
 */
export async function healthCheckAction(): Promise<{ healthy: boolean; issues?: string[] }> {
  try {
    // Perform health check using the service layer
    const result = await healthCheck();
    
    console.info(`Settings system health check: ${result.healthy ? 'HEALTHY' : 'ISSUES FOUND'}`);
    
    return result;
    
  } catch (error) {
    console.error('Error performing health check:', error);
    
    return { 
      healthy: false, 
      issues: [error instanceof Error ? error.message : 'Health check failed with unknown error'] 
    };
  }
}

// Comment: Utility actions provide administrative tools for monitoring
// and fine-tuned control over the settings system.

// ============================================================================
// BACKUP AND RESTORE ACTIONS
// ============================================================================

/**
 * Create Settings Backup Action
 * 
 * Server action to create a timestamped backup of current settings.
 * Returns a filename and backup data for storage or download.
 * 
 * @returns Promise<{success: boolean, fileName?: string, data?: SiteSettings, error?: string}>
 */
export async function createBackupAction(): Promise<{ 
  success: boolean; 
  fileName?: string; 
  data?: SiteSettings; 
  error?: string 
}> {
  try {
    // Export current settings
    const exportResult = await exportSettingsAction();
    
    if (!exportResult.success || !exportResult.data) {
      return { success: false, error: exportResult.error || 'Export failed during backup creation' };
    }
    
    // Generate backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `criticalpixel-settings-backup-${timestamp}.json`;
    
    console.info(`Created settings backup: ${fileName}`);
    
    return { 
      success: true, 
      fileName, 
      data: exportResult.data 
    };
    
  } catch (error) {
    console.error('Error creating settings backup:', error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Backup creation failed' 
    };
  }
}

/**
 * Restore Settings from Backup Action
 * 
 * Server action to restore settings from a backup file.
 * Validates backup data before applying changes.
 * 
 * @param backupData - JSON string containing the backup data
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function restoreFromBackupAction(
  backupData: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Parse the backup data
    let settings: SiteSettings;
    
    try {
      settings = JSON.parse(backupData) as SiteSettings;
    } catch (parseError) {
      return { 
        success: false, 
        error: 'Invalid backup file format - unable to parse JSON data' 
      };
    }
    
    // Import the settings
    const result = await importSettingsAction(settings);
    
    if (result.success) {
      console.info('Settings restored from backup successfully');
    }
    
    return result;
    
  } catch (error) {
    console.error('Error restoring settings from backup:', error);
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Settings restore failed' 
    };
  }
}

// Comment: Backup and restore actions provide data protection and
// disaster recovery capabilities for the settings system.

// ============================================================================
// DEVELOPMENT AND TESTING ACTIONS
// ============================================================================

/**
 * Test Settings System Action
 * 
 * Server action for testing the settings system functionality.
 * Useful during development and for automated health checks.
 * 
 * @returns Promise<{success: boolean, tests: Array<{name: string, passed: boolean, error?: string}>}>
 */
export async function testSettingsSystemAction(): Promise<{ 
  success: boolean; 
  tests: Array<{ name: string; passed: boolean; error?: string }> 
}> {
  const tests: Array<{ name: string; passed: boolean; error?: string }> = [];
  
  try {
    // Test 1: Authentication check
    try {
      await getCurrentAdminUserId();
      tests.push({ name: 'Admin Authentication', passed: true });
    } catch (error) {
      tests.push({ 
        name: 'Admin Authentication', 
        passed: false, 
        error: error instanceof Error ? error.message : 'Auth test failed' 
      });
    }
    
    // Test 2: Health check
    try {
      const health = await healthCheckAction();
      tests.push({ 
        name: 'System Health Check', 
        passed: health.healthy,
        error: health.issues?.join(', ') 
      });
    } catch (error) {
      tests.push({ 
        name: 'System Health Check', 
        passed: false, 
        error: error instanceof Error ? error.message : 'Health check failed' 
      });
    }
    
    // Test 3: Settings retrieval
    try {
      const userId = await getCurrentAdminUserId();
      await getSiteSettings(userId);
      tests.push({ name: 'Settings Retrieval', passed: true });
    } catch (error) {
      tests.push({ 
        name: 'Settings Retrieval', 
        passed: false, 
        error: error instanceof Error ? error.message : 'Retrieval test failed' 
      });
    }
    
    const allPassed = tests.every(test => test.passed);
    
    console.info(`Settings system test completed: ${allPassed ? 'ALL PASSED' : 'SOME FAILED'}`);
    
    return { success: allPassed, tests };
    
  } catch (error) {
    console.error('Error running settings system tests:', error);
    
    tests.push({ 
      name: 'Test Framework', 
      passed: false, 
      error: error instanceof Error ? error.message : 'Test framework error' 
    });
    
    return { success: false, tests };
  }
}

// Comment: Testing actions enable automated verification of system
// functionality and help identify issues during development.

// ============================================================================
// CACHE AND OPTIMIZATION ACTIONS
// ============================================================================

/**
 * Invalidate Settings Cache Action
 * 
 * Server action to manually invalidate settings-related caches.
 * Useful when settings changes aren't immediately reflected.
 * 
 * @returns Promise<{success: boolean}>
 */
export async function invalidateSettingsCacheAction(): Promise<{ success: boolean }> {
  try {
    // Revalidate all settings-related pages
    revalidatePath('/admin/settings');
    revalidatePath('/admin');
    revalidatePath('/');
    
    // Could also clear any external caches here (Redis, etc.)
    
    console.info('Settings cache invalidated successfully');
    
    return { success: true };
    
  } catch (error) {
    console.error('Error invalidating settings cache:', error);
    
    return { success: false };
  }
}

// Comment: Cache invalidation ensures settings changes are immediately
// visible across the application when Next.js caching is involved.

// ============================================================================
// SERVER ACTIONS EXPORTS
// ============================================================================

// Export all server actions for use in client components
export {
  // Core actions
  updateSettingsAction,
  resetSettingsAction,
  
  // Category-specific actions
  updateGeneralSettingsAction,
  updateSEOSettingsAction,
  updateContentSettingsAction,
  updateSecuritySettingsAction,
  updateNotificationSettingsAction,
  updateIntegrationSettingsAction,
  
  // Bulk operations
  bulkUpdateSettingsAction,
  
  // Import/export
  exportSettingsAction,
  importSettingsAction,
  
  // Utility actions
  updateSingleSettingAction,
  healthCheckAction,
  
  // Backup/restore
  createBackupAction,
  restoreFromBackupAction,
  
  // Development/testing
  testSettingsSystemAction,
  invalidateSettingsCacheAction,
};

// ============================================================================
// SERVER ACTIONS IMPLEMENTATION COMPLETE
// ============================================================================
/**
 * USAGE EXAMPLES:
 * 
 * // In a form component
 * <form action={updateGeneralSettingsAction}>
 *   <input name="site_name" defaultValue={settings.general.site_name} />
 *   <button type="submit">Save</button>
 * </form>
 * 
 * // In a client component with useTransition
 * const [isPending, startTransition] = useTransition();
 * 
 * const handleSubmit = (formData: FormData) => {
 *   startTransition(async () => {
 *     const result = await updateGeneralSettingsAction(formData);
 *     if (result.success) {
 *       toast.success('Settings updated!');
 *     } else {
 *       toast.error(result.error);
 *     }
 *   });
 * };
 */
```

### Step 2: Create Action Utilities

Create the file: `src/lib/admin/action-utils.ts`

```typescript
// ============================================================================
// SERVER ACTION UTILITIES
// ============================================================================
// Description: Utility functions for server actions and form handling
// Author: AI Assistant
// Date: [Current Date]
// ============================================================================

import { z } from 'zod';

/**
 * Server Action Result Type
 * 
 * Standard result type for all server actions to ensure consistency.
 */
export type ActionResult<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  errors?: Array<{ field: string; message: string }>;
};

/**
 * Safe Server Action Wrapper
 * 
 * Wraps server actions with standardized error handling and logging.
 * 
 * @param action - The async function to wrap
 * @param actionName - Name for logging purposes
 * @returns Wrapped action with error handling
 */
export function safeAction<T extends any[], R>(
  action: (...args: T) => Promise<R>,
  actionName: string
) {
  return async (...args: T): Promise<ActionResult<R>> => {
    try {
      const result = await action(...args);
      return { success: true, data: result };
    } catch (error) {
      console.error(`Error in ${actionName}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : `Unknown error in ${actionName}`
      };
    }
  };
}

/**
 * Validate Form Data
 * 
 * Validates form data against a Zod schema with detailed error reporting.
 * 
 * @param schema - Zod schema for validation
 * @param data - Data to validate
 * @returns Validation result with detailed errors
 */
export function validateFormData<T>(
  schema: z.ZodSchema<T>,
  data: any
): ActionResult<T> {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      };
    }
    
    return {
      success: false,
      error: 'Validation failed with unknown error'
    };
  }
}

/**
 * Form Data Helper
 * 
 * Utility functions for working with FormData objects.
 */
export const formDataHelper = {
  /**
   * Get string value from FormData with fallback
   */
  getString: (formData: FormData, key: string, fallback = ''): string => {
    const value = formData.get(key);
    return typeof value === 'string' ? value.trim() : fallback;
  },
  
  /**
   * Get boolean value from FormData (checkbox handling)
   */
  getBoolean: (formData: FormData, key: string): boolean => {
    const value = formData.get(key);
    return value === 'on' || value === 'true';
  },
  
  /**
   * Get number value from FormData with validation
   */
  getNumber: (formData: FormData, key: string, fallback = 0): number => {
    const value = formData.get(key);
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? fallback : parsed;
    }
    return fallback;
  },
  
  /**
   * Convert FormData to plain object
   */
  toObject: (formData: FormData): Record<string, any> => {
    const obj: Record<string, any> = {};
    for (const [key, value] of formData.entries()) {
      obj[key] = value;
    }
    return obj;
  }
};
```

### Step 3: Create Server Action Tests

Create the file: `src/lib/admin/__tests__/settings-actions.test.ts`

```typescript
// ============================================================================
// SETTINGS SERVER ACTIONS TESTS
// ============================================================================
// Description: Tests for server actions with mocked dependencies
// Author: AI Assistant
// Date: [Current Date]
// ============================================================================

import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { revalidatePath } from 'next/cache';
import {
  updateGeneralSettingsAction,
  updateSEOSettingsAction,
  resetSettingsAction,
  exportSettingsAction,
  importSettingsAction
} from '../settings-actions';

// Mock Next.js functions
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}));

jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({})),
}));

// Mock Supabase
jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createServerActionClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: {
          user: {
            id: 'admin-user-id',
            user_metadata: { isAdmin: true }
          }
        },
        error: null
      })
    }
  }))
}));

// Mock service layer
jest.mock('../settingsService', () => ({
  updateSiteSettings: jest.fn().mockResolvedValue({ success: true }),
  resetSettingsToDefaults: jest.fn().mockResolvedValue({ success: true }),
  exportSettings: jest.fn().mockResolvedValue({ 
    success: true, 
    data: { general: { site_name: 'Test' } } 
  }),
  importSettings: jest.fn().mockResolvedValue({ success: true }),
}));

describe('Settings Server Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateGeneralSettingsAction', () => {
    it('should update general settings successfully', async () => {
      const formData = new FormData();
      formData.append('site_name', 'New Site Name');
      formData.append('site_url', 'https://newsite.com');
      formData.append('site_description', 'New description');
      formData.append('admin_email', '<EMAIL>');
      formData.append('timezone', 'UTC');
      formData.append('language', 'en');
      formData.append('maintenance_mode', 'off');
      formData.append('maintenance_message', '');

      const result = await updateGeneralSettingsAction(formData);

      expect(result.success).toBe(true);
      expect(revalidatePath).toHaveBeenCalledWith('/admin/settings');
    });

    it('should handle validation errors', async () => {
      const formData = new FormData();
      formData.append('site_name', ''); // Invalid: empty string
      formData.append('site_url', 'invalid-url'); // Invalid: not a URL

      const result = await updateGeneralSettingsAction(formData);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });
  });

  describe('resetSettingsAction', () => {
    it('should reset settings to defaults', async () => {
      const result = await resetSettingsAction('general');

      expect(result.success).toBe(true);
      expect(revalidatePath).toHaveBeenCalledWith('/admin/settings');
    });

    it('should reset all settings when no category provided', async () => {
      const result = await resetSettingsAction();

      expect(result.success).toBe(true);
    });
  });

  describe('exportSettingsAction', () => {
    it('should export settings successfully', async () => {
      const result = await exportSettingsAction();

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });
  });

  describe('importSettingsAction', () => {
    it('should import settings successfully', async () => {
      const mockSettings = {
        general: {
          site_name: 'Imported Site',
          site_url: 'https://imported.com',
          site_description: 'Imported description',
          admin_email: '<EMAIL>',
          timezone: 'UTC',
          language: 'en',
          maintenance_mode: false,
          maintenance_message: ''
        },
        seo: {
          meta_title: 'Imported Title',
          meta_description: 'Imported Description',
          meta_keywords: 'imported, keywords',
          og_image: '',
          twitter_card: 'summary_large_image',
          google_analytics_id: '',
          google_search_console: ''
        },
        content: {
          allow_user_registration: true,
          require_email_verification: true,
          allow_anonymous_comments: false,
          moderate_comments: true,
          max_review_length: 10000,
          max_comment_length: 1000,
          featured_reviews_count: 6
        },
        security: {
          enable_rate_limiting: true,
          require_strong_passwords: true,
          enable_two_factor: false,
          max_login_attempts: 5,
          session_timeout: 3600,
          max_file_size: 5242880
        },
        notifications: {
          email_notifications: true,
          admin_notifications: true,
          user_notifications: true,
          newsletter_enabled: true,
          smtp_host: '',
          smtp_port: 587,
          smtp_username: '',
          smtp_password: ''
        },
        integrations: {
          igdb_api_key: '',
          discord_webhook: '',
          slack_webhook: '',
          backup_enabled: false,
          backup_frequency: 'daily',
          backup_retention: 30
        }
      };

      const result = await importSettingsAction(mockSettings);

      expect(result.success).toBe(true);
      expect(revalidatePath).toHaveBeenCalledWith('/admin/settings');
    });
  });
});
```

## 📝 Implementation Comments Required

When implementing this guide, add comments explaining:

1. **Why 'use server' directive** is required for server actions
2. **How form data parsing** handles different input types
3. **The importance of** input validation before database operations
4. **How cache revalidation** ensures UI consistency
5. **Why error handling** is standardized across actions
6. **How authentication** is verified for each action

## ✅ Completion Checklist

- [x] settings-actions.ts file created with all server actions
- [x] Authentication verification implemented for all actions
- [x] Form data parsing functions for all categories
- [x] Validation integration with Zod schemas
- [x] Error handling comprehensive and user-friendly
- [x] Cache revalidation properly implemented
- [x] Import/export actions working correctly
- [x] Utility functions for common operations
- [ ] Test file created and passing
- [x] Action utilities created for reusability
- [x] All actions properly typed and documented

## 🔄 Next Step Initialization

Once this guide is complete and all tests pass, proceed to:

**Guide 5: UI Components and Forms Implementation** (`05-UI_COMPONENTS_GUIDE.md`)

The final guide will create the React components and forms that use these server actions to provide the admin interface.

## 🎯 AI Implementation Instructions

**AI Assistant**: When implementing this guide:

1. **Create comprehensive server actions** that handle all form operations
2. **Implement robust form data parsing** for each settings category
3. **Add thorough input validation** using the Zod schemas
4. **Include proper error handling** with detailed user-friendly messages
5. **Implement cache revalidation** for immediate UI updates
6. **Create utility functions** for common operations
7. **Add comprehensive tests** covering success and failure scenarios
8. **Document all functions** with clear usage examples
9. **Verify authentication** works correctly for all actions
10. **Initialize Guide 5** when ready to complete the implementation

Focus on creating robust, secure server actions that provide a clean API between the client forms and the service layer.