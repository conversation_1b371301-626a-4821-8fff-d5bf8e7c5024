#### Search Filters Component

```typescript
// File: /src/components/search/SearchFilters.tsx
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { SearchState } from './SearchInterface';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

// Interface for the filter options from API
interface FilterOptions {
  genres: Array<{ id: string, name: string, count: number }>;
  platforms: Array<{ id: string, name: string, count: number }>;
  tags: Array<{ id: string, name: string, count: number }>;
}

// Props for the component
interface SearchFiltersProps {
  filters: SearchState['filters'];
  searchType: SearchState['type'];
  onFiltersChange: (filters: SearchState['filters']) => void;
}

/**
 * Search filters component that renders different filter options
 * based on the search type and manages filter state
 */
export function SearchFilters({ filters, searchType, onFiltersChange }: SearchFiltersProps) {
  // State for filter options (would be fetched from API)
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    genres: [],
    platforms: [],
    tags: [],
  });
  
  // Loading state for filter options
  const [isLoading, setIsLoading] = useState<boolean>(false);
  
  /**
   * Fetch filter options from API
   */
  useEffect(() => {
    const fetchFilterOptions = async () => {
      setIsLoading(true);
      try {
        // In a real implementation, you would fetch from your API
        const response = await fetch('/api/filterOptions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: searchType,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch filter options');
        }
        
        const data = await response.json();
        setFilterOptions(data);
      } catch (error) {
        console.error('Error fetching filter options:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Mock data for demonstration
    setFilterOptions({
      genres: [
        { id: 'action', name: 'Action', count: 120 },
        { id: 'adventure', name: 'Adventure', count: 85 },
        { id: 'rpg', name: 'RPG', count: 67 },
        { id: 'strategy', name: 'Strategy', count: 45 },
        { id: 'simulation', name: 'Simulation', count: 38 },
      ],
      platforms: [
        { id: 'pc', name: 'PC', count: 150 },
        { id: 'ps5', name: 'PlayStation 5', count: 95 },
        { id: 'xbox', name: 'Xbox Series X', count: 88 },
        { id: 'switch', name: 'Nintendo Switch', count: 75 },
        { id: 'mobile', name: 'Mobile', count: 50 },
      ],
      tags: [
        { id: 'multiplayer', name: 'Multiplayer', count: 110 },
        { id: 'singleplayer', name: 'Single Player', count: 130 },
        { id: 'openworld', name: 'Open World', count: 65 },
        { id: 'indie', name: 'Indie', count: 80 },
        { id: 'vr', name: 'VR', count: 30 },
      ],
    });
    
    // In production, uncomment this to fetch real data
    // fetchFilterOptions();
  }, [searchType]);
  
  /**
   * Handle score range change
   */
  const handleScoreChange = (value: number[]) => {
    onFiltersChange({
      ...filters,
      minScore: value[0],
      maxScore: value[1],
    });
  };
  
  /**
   * Handle date range change
   */
  const handleDateChange = (field: 'dateFrom' | 'dateTo', value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value,
    });
  };
  
  /**
   * Handle genre toggle
   */
  const handleGenreToggle = (genreId: string, checked: boolean) => {
    if (checked) {
      onFiltersChange({
        ...filters,
        genres: [...filters.genres, genreId],
      });
    } else {
      onFiltersChange({
        ...filters,
        genres: filters.genres.filter(id => id !== genreId),
      });
    }
  };
  
  /**
   * Handle platform toggle
   */
  const handlePlatformToggle = (platformId: string, checked: boolean) => {
    if (checked) {
      onFiltersChange({
        ...filters,
        platforms: [...filters.platforms, platformId],
      });
    } else {
      onFiltersChange({
        ...filters,
        platforms: filters.platforms.filter(id => id !== platformId),
      });
    }
  };
  
  /**
   * Handle tag toggle
   */
  const handleTagToggle = (tagId: string, checked: boolean) => {
    if (checked) {
      onFiltersChange({
        ...filters,
        tags: [...filters.tags, tagId],
      });
    } else {
      onFiltersChange({
        ...filters,
        tags: filters.tags.filter(id => id !== tagId),
      });
    }
  };
  
  /**
   * Reset all filters
   */
  const handleResetFilters = () => {
    onFiltersChange({
      genres: [],
      platforms: [],
      tags: [],
      minScore: 0,
      maxScore: 10,
      dateFrom: undefined,
      dateTo: undefined,
    });
  };
  
  /**
   * Get count of active filters
   */
  const getActiveFilterCount = () => {
    let count = 0;
    
    count += filters.genres.length;
    count += filters.platforms.length;
    count += filters.tags.length;
    
    if (filters.minScore > 0 || filters.maxScore < 10) {
      count += 1;
    }
    
    if (filters.dateFrom) count += 1;
    if (filters.dateTo) count += 1;
    
    return count;
  };
  
  // Show different filters based on search type
  const showScoreFilter = searchType === 'all' || searchType === 'reviews' || searchType === 'games';
  const showGenreFilter = searchType === 'all' || searchType === 'games' || searchType === 'reviews';
  const showPlatformFilter = searchType === 'all' || searchType === 'games' || searchType === 'hardware';
  const showTagFilter = searchType === 'all' || searchType === 'games' || searchType === 'reviews' || searchType === 'hardware';
  const showDateFilter = searchType === 'all' || searchType === 'reviews';
  
  // Main component render
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex justify-between items-center">
          <span>Filters</span>
          {getActiveFilterCount() > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleResetFilters}
              aria-label="Reset all filters"
            >
              Reset
            </Button>
          )}
        </CardTitle>
        {getActiveFilterCount() > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {filters.genres.map(genreId => {
              const genre = filterOptions.genres.find(g => g.id === genreId);
              return genre && (
                <Badge 
                  key={`genre-${genreId}`}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {genre.name}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleGenreToggle(genreId, false)}
                  />
                </Badge>
              );
            })}
            
            {filters.platforms.map(platformId => {
              const platform = filterOptions.platforms.find(p => p.id === platformId);
              return platform && (
                <Badge 
                  key={`platform-${platformId}`}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {platform.name}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handlePlatformToggle(platformId, false)}
                  />
                </Badge>
              );
            })}
            
            {filters.tags.map(tagId => {
              const tag = filterOptions.tags.find(t => t.id === tagId);
              return tag && (
                <Badge 
                  key={`tag-${tagId}`}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {tag.name}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleTagToggle(tagId, false)}
                  />
                </Badge>
              );
            })}
            
            {(filters.minScore > 0 || filters.maxScore < 10) && (
              <Badge variant="secondary">
                Score: {filters.minScore}-{filters.maxScore}
              </Badge>
            )}
            
            {filters.dateFrom && (
              <Badge 
                variant="secondary"
                className="flex items-center gap-1"
              >
                From: {new Date(filters.dateFrom).toLocaleDateString()}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleDateChange('dateFrom', undefined as any)}
                />
              </Badge>
            )}
            
            {filters.dateTo && (
              <Badge 
                variant="secondary"
                className="flex items-center gap-1"
              >
                To: {new Date(filters.dateTo).toLocaleDateString()}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleDateChange('dateTo', undefined as any)}
                />
              </Badge>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Accordion type="multiple" className="w-full">
            {/* Score Filter */}
            {showScoreFilter && (
              <AccordionItem value="score">
                <AccordionTrigger>Score Range</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3">
                    <div className="pt-2">
                      <Slider 
                        defaultValue={[filters.minScore, filters.maxScore]} 
                        min={0} 
                        max={10} 
                        step={0.5}
                        onValueChange={handleScoreChange}
                        aria-label="Score range"
                        className="pt-2 pb-4"
                      />
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>{filters.minScore}</span>
                      <span>{filters.maxScore}</span>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
            
            {/* Genre Filter */}
            {showGenreFilter && (
              <AccordionItem value="genres">
                <AccordionTrigger>Genres</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {filterOptions.genres.map(genre => (
                      <div key={genre.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`genre-${genre.id}`}
                          checked={filters.genres.includes(genre.id)}
                          onCheckedChange={(checked) => {
                            handleGenreToggle(genre.id, checked as boolean);
                          }}
                          aria-label={`Filter by ${genre.name}`}
                        />
                        <Label 
                          htmlFor={`genre-${genre.id}`}
                          className="flex justify-between w-full text-sm"
                        >
                          <span>{genre.name}</span>
                          <span className="text-muted-foreground">({genre.count})</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
            
            {/* Platform Filter */}
            {showPlatformFilter && (
              <AccordionItem value="platforms">
                <AccordionTrigger>Platforms</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {filterOptions.platforms.map(platform => (
                      <div key={platform.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`platform-${platform.id}`}
                          checked={filters.platforms.includes(platform.id)}
                          onCheckedChange={(checked) => {
                            handlePlatformToggle(platform.id, checked as boolean);
                          }}
                          aria-label={`Filter by ${platform.name}`}
                        />
                        <Label 
                          htmlFor={`platform-${platform.id}`}
                          className="flex justify-between w-full text-sm"
                        >
                          <span>{platform.name}</span>
                          <span className="text-muted-foreground">({platform.count})</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
            
            {/* Tags Filter */}
            {showTagFilter && (
              <AccordionItem value="tags">
                <AccordionTrigger>Tags</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {filterOptions.tags.map(tag => (
                      <div key={tag.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`tag-${tag.id}`}
                          checked={filters.tags.includes(tag.id)}
                          onCheckedChange={(checked) => {
                            handleTagToggle(tag.id, checked as boolean);
                          }}
                          aria-label={`Filter by ${tag.name}`}
                        />
                        <Label 
                          htmlFor={`tag-${tag.id}`}
                          className="flex justify-between w-full text-sm"
                        >
                          <span>{tag.name}</span>
                          <span className="text-muted-foreground">({tag.count})</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
            
            {/* Date Filter */}
            {showDateFilter && (
              <AccordionItem value="date">
                <AccordionTrigger>Date Range</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    <div className="space-y-1">
                      <Label htmlFor="date-from">From</Label>
                      <DatePicker 
                        id="date-from"
                        date={filters.dateFrom ? new Date(filters.dateFrom) : undefined} 
                        onSelect={(date) => 
                          handleDateChange('dateFrom', date ? date.toISOString() : undefined as any)
                        }
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="date-to">To</Label>
                      <DatePicker 
                        id="date-to"
                        date={filters.dateTo ? new Date(filters.dateTo) : undefined}
                        onSelect={(date) => 
                          handleDateChange('dateTo', date ? date.toISOString() : undefined as any)
                        }
                        className="w-full"
                      />
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}
          </Accordion>
        </div>
      </CardContent>
    </Card>
  );
}
```
