'use client';

import React, { useState } from 'react';
import { <PERSON>, User, Bo<PERSON>, HelpCircle, TrendingUp, Users } from 'lucide-react';
import { useAuthContext } from '@/contexts/auth-context';

interface AIDetectionVotingProps {
  reviewId: string;
  className?: string;
}

/**
 * AI Detection Voting Component - Let users vote on whether a review is AI-generated
 * Features: Interactive confidence meter, voting buttons, community stats
 */
export default function AIDetectionVoting({ reviewId, className = '' }: AIDetectionVotingProps) {
  const { user } = useAuthContext();
  
  // Temporarily disable the hook to test the component structure
  const userVote = null;
  const stats = {
    humanVotes: 0,
    aiVotes: 0,
    unsureVotes: 0,
    totalVotes: 0,
    humanConfidence: 50
  };
  const isLoading = false;
  const submitVote = () => {};
  const isSubmitting = false;
  const canVote = !!user;
  const hasVoted = false;
  
  const [showDetails, setShowDetails] = useState(false);
  
  const confidenceInfo = {
    label: 'Uncertain',
    color: 'text-yellow-400',
    description: 'Community is split on authorship'
  };

  const handleVote = (vote: string) => {
    if (!canVote || isSubmitting) return;
    submitVote();
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-slate-800/40 rounded-lg p-4 ${className}`}>
        <div className="h-4 bg-slate-700 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-slate-700 rounded w-1/2"></div>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-br from-slate-900/60 to-slate-800/40 backdrop-blur-xl rounded-xl border border-slate-700/30 overflow-hidden ${className}`}>
      {/* Header with AI Detection Badge */}
      <div className="p-4 border-b border-slate-700/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-violet-500/20 rounded-lg">
              <Brain className="w-5 h-5 text-violet-400" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-slate-200 font-mono">
                AI Detection Scanner
              </h3>
              <p className="text-xs text-slate-400">
                Help the community identify AI-generated content
              </p>
            </div>
          </div>
          
          {/* Confidence Badge */}
          <div className="text-right">
            <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-mono font-semibold ${confidenceInfo.color} bg-slate-800/60`}>
              <TrendingUp className="w-3 h-3" />
              {stats.humanConfidence}% Human
            </div>
            <div className="text-xs text-slate-500 mt-1">
              {stats.totalVotes} {stats.totalVotes === 1 ? 'vote' : 'votes'}
            </div>
          </div>
        </div>
      </div>

      {/* Confidence Meter */}
      <div className="p-4">
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-slate-400 font-mono flex items-center gap-1">
              <Bot className="w-3 h-3" />
              AI
            </span>
            <span className="text-xs text-slate-400 font-mono">
              {confidenceInfo.label}
            </span>
            <span className="text-xs text-slate-400 font-mono flex items-center gap-1">
              <User className="w-3 h-3" />
              Human
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="relative h-3 bg-slate-800 rounded-full overflow-hidden">
            <div 
              className="absolute inset-y-0 left-0 bg-gradient-to-r from-red-500 via-yellow-500 to-emerald-500 transition-all duration-500 ease-out"
              style={{ width: `${stats.humanConfidence}%` }}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 via-yellow-500/20 to-emerald-500/20" />
          </div>
          
          <p className="text-xs text-slate-500 mt-2 text-center">
            {confidenceInfo.description}
          </p>
        </div>

        {/* Voting Buttons */}
        {canVote ? (
          <div className="space-y-3">
            <p className="text-xs text-slate-400 text-center font-mono">
              What do you think? Cast your vote:
            </p>
            
            <div className="grid grid-cols-3 gap-2">
              {[
                { type: 'ai', icon: '🤖', label: 'AI', color: 'text-red-400', count: stats.aiVotes },
                { type: 'unsure', icon: '🤷', label: "Can't Tell", color: 'text-yellow-400', count: stats.unsureVotes },
                { type: 'human', icon: '👤', label: 'Human', color: 'text-emerald-400', count: stats.humanVotes }
              ].map((voteOption) => {
                const isSelected = userVote === voteOption.type;
                
                return (
                  <button
                    key={voteOption.type}
                    onClick={() => handleVote(voteOption.type)}
                    disabled={isSubmitting}
                    className={`group relative p-3 rounded-lg border transition-all duration-300 font-mono text-xs ${
                      isSelected
                        ? `border-violet-500/50 bg-violet-500/20 ${voteOption.color}`
                        : 'border-slate-700/50 bg-slate-800/40 text-slate-400 hover:border-slate-600/70 hover:bg-slate-700/50'
                    } ${
                      isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  >
                    <div className="flex flex-col items-center gap-1">
                      <span className="text-lg">{voteOption.icon}</span>
                      <span className="font-semibold">{voteOption.label}</span>
                      <span className="text-xs opacity-70">{voteOption.count}</span>
                    </div>
                    
                    {isSelected && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-violet-500 rounded-full border-2 border-slate-900" />
                    )}
                  </button>
                );
              })}
            </div>
            
            {hasVoted && (
              <p className="text-xs text-center text-violet-400 font-mono">
                Thanks for voting! You can change your vote anytime.
              </p>
            )}
          </div>
        ) : (
          <div className="text-center py-4">
            <HelpCircle className="w-8 h-8 text-slate-500 mx-auto mb-2" />
            <p className="text-sm text-slate-400 font-mono">
              Sign in to vote on AI detection
            </p>
            <p className="text-xs text-slate-500 mt-1">
              Help the community identify AI-generated content
            </p>
          </div>
        )}

        {/* Toggle Details */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="w-full mt-4 text-xs text-slate-500 hover:text-slate-400 transition-colors font-mono"
        >
          {showDetails ? '▼' : '▶'} Vote Breakdown
        </button>

        {/* Detailed Stats */}
        {showDetails && (
          <div className="mt-3 space-y-2 text-xs font-mono">
            <div className="flex justify-between items-center p-2 bg-slate-800/60 rounded">
              <span className="flex items-center gap-2 text-emerald-400">
                <User className="w-3 h-3" />
                Human votes
              </span>
              <span className="text-slate-300">{stats.humanVotes}</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-slate-800/60 rounded">
              <span className="flex items-center gap-2 text-red-400">
                <Bot className="w-3 h-3" />
                AI votes
              </span>
              <span className="text-slate-300">{stats.aiVotes}</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-slate-800/60 rounded">
              <span className="flex items-center gap-2 text-yellow-400">
                <HelpCircle className="w-3 h-3" />
                Unsure votes
              </span>
              <span className="text-slate-300">{stats.unsureVotes}</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-slate-700/60 rounded border-t border-slate-600/30">
              <span className="flex items-center gap-2 text-slate-300">
                <Users className="w-3 h-3" />
                Total votes
              </span>
              <span className="text-slate-200 font-semibold">{stats.totalVotes}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}