'use client';

import { useState } from 'react';
import { useB2ImageUpload } from '@/hooks/useB2ImageUpload';
import UploadProgress from '@/components/ui/UploadProgress';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestUploadPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  
  const {
    uploads,
    isUploading,
    uploadFiles,
    clearUploads,
    cancelUploads,
  } = useB2ImageUpload({
    maxFiles: 5,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    onUploadComplete: (results) => {
      console.log('Upload completed:', results);
    },
    onUploadError: (error) => {
      console.error('Upload error:', error);
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    console.log('Files selected:', files.length, files);
    setSelectedFiles(files);
  };

  const handleUpload = async () => {
    if (selectedFiles.length > 0) {
      await uploadFiles(selectedFiles);
      setSelectedFiles([]);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>B2 Cloud Storage Upload Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Selection */}
          <div>
            <label htmlFor="file-input" className="block text-sm font-medium mb-2">
              Select Images to Upload
            </label>
            <input
              id="file-input"
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Selected Files:</h3>
              <ul className="space-y-1">
                {selectedFiles.map((file, index) => (
                  <li key={index} className="text-sm text-gray-600">
                    {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Upload Controls */}
          <div className="flex gap-2">
            <Button
              onClick={handleUpload}
              disabled={selectedFiles.length === 0 || isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload Files'}
            </Button>
            <div className="text-xs text-gray-500">
              Debug: Files={selectedFiles.length}, Uploading={isUploading.toString()}
            </div>
            <Button
              variant="outline"
              onClick={clearUploads}
              disabled={uploads.length === 0}
            >
              Clear Results
            </Button>
          </div>

          {/* Upload Progress */}
          {uploads.length > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Upload Progress:</h3>
              <UploadProgress
                uploads={uploads}
                onCancel={cancelUploads}
                showDetails={true}
              />
            </div>
          )}

          {/* Results */}
          {uploads.some(u => u.status === 'completed') && (
            <div>
              <h3 className="text-sm font-medium mb-2">Completed Uploads:</h3>
              <div className="space-y-2">
                {uploads
                  .filter(u => u.status === 'completed' && u.result?.url)
                  .map((upload) => (
                    <div key={upload.id} className="border rounded p-2">
                      <p className="text-sm font-medium">{upload.file.name}</p>
                      <p className="text-xs text-gray-500">URL: {upload.result?.url}</p>
                      {upload.result?.url && (
                        <img
                          src={upload.result.url}
                          alt={upload.file.name}
                          className="mt-2 max-w-xs max-h-32 object-contain"
                        />
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Configuration Info */}
          <div className="mt-8 p-4 bg-gray-50 rounded">
            <h3 className="text-sm font-medium mb-2">Configuration Status:</h3>
            <div className="text-xs space-y-1">
              <p>Max Files: 5</p>
              <p>Max File Size: 10MB</p>
              <p>Allowed Types: image/jpeg, image/png, image/webp, image/gif</p>
              <p>B2 Endpoint: {process.env.NEXT_PUBLIC_B2_ENDPOINT || 'Not configured'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
