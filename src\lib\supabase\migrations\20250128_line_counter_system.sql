-- ===========================
-- Line Counter System Migration
-- Created: 2025-01-28
-- Purpose: Track total lines of text written across all reviews
-- ===========================

-- 1. Add line_count column to reviews table
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS line_count INTEGER DEFAULT 0;

-- 2. Create site_statistics table for global counters
CREATE TABLE IF NOT EXISTS site_statistics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT UNIQUE NOT NULL,
  metric_value BIGINT DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Insert initial total_review_lines metric
INSERT INTO site_statistics (metric_name, metric_value) 
VALUES ('total_review_lines', 0) 
ON CONFLICT (metric_name) DO NOTHING;

-- 4. <PERSON>reate function to count lines from Lexical JSON content
CREATE OR REPLACE FUNCTION count_lines_from_lexical(content_lexical JSONB)
RETURNS INTEGER AS $$
DECLARE
  line_count INTEGER := 0;
  lexical_data JSONB;
BEGIN
  -- Parse the JSON string to JSONB
  BEGIN
    lexical_data := (content_lexical#>>'{}')::jsonb;
  EXCEPTION WHEN OTHERS THEN
    RETURN 0;
  END;
  
  -- Count paragraphs, headings, and other block elements as lines
  SELECT COUNT(*) INTO line_count
  FROM jsonb_array_elements(lexical_data->'root'->'children') as elem
  WHERE elem->>'type' IN ('paragraph', 'heading', 'quote', 'list', 'listitem');
  
  RETURN COALESCE(line_count, 0);
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger function to update line counts
CREATE OR REPLACE FUNCTION update_review_line_count()
RETURNS TRIGGER AS $$
DECLARE
  old_line_count INTEGER := 0;
  new_line_count INTEGER := 0;
  line_diff INTEGER := 0;
BEGIN
  -- Calculate new line count
  IF NEW.content_lexical IS NOT NULL THEN
    new_line_count := count_lines_from_lexical(NEW.content_lexical);
  END IF;
  
  -- Update the line_count column
  NEW.line_count := new_line_count;
  
  -- Calculate difference for global counter update
  IF TG_OP = 'UPDATE' THEN
    old_line_count := COALESCE(OLD.line_count, 0);
    line_diff := new_line_count - old_line_count;
  ELSIF TG_OP = 'INSERT' THEN
    line_diff := new_line_count;
  END IF;
  
  -- Update global counter if there's a difference
  IF line_diff != 0 THEN
    UPDATE site_statistics 
    SET 
      metric_value = metric_value + line_diff,
      last_updated = NOW()
    WHERE metric_name = 'total_review_lines';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Create trigger to automatically update line counts
DROP TRIGGER IF EXISTS trigger_update_review_line_count ON reviews;
CREATE TRIGGER trigger_update_review_line_count
  BEFORE INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_review_line_count();

-- 7. Create function to process historical reviews
CREATE OR REPLACE FUNCTION process_historical_review_line_counts(batch_size INTEGER DEFAULT 100)
RETURNS TABLE(processed_count INTEGER, total_lines BIGINT) AS $$
DECLARE
  review_record RECORD;
  processed INTEGER := 0;
  total_line_count BIGINT := 0;
  current_line_count INTEGER;
BEGIN
  -- Process reviews in batches where line_count is 0 or NULL
  FOR review_record IN 
    SELECT id, content_lexical 
    FROM reviews 
    WHERE COALESCE(line_count, 0) = 0 
      AND content_lexical IS NOT NULL
    LIMIT batch_size
  LOOP
    -- Calculate line count for this review
    current_line_count := count_lines_from_lexical(review_record.content_lexical);
    
    -- Update the review (this will trigger the trigger, but we'll handle it)
    UPDATE reviews 
    SET line_count = current_line_count 
    WHERE id = review_record.id;
    
    processed := processed + 1;
    total_line_count := total_line_count + current_line_count;
  END LOOP;
  
  -- Update the global counter with the total from this batch
  IF total_line_count > 0 THEN
    UPDATE site_statistics 
    SET 
      metric_value = (
        SELECT COALESCE(SUM(line_count), 0) 
        FROM reviews 
        WHERE line_count > 0
      ),
      last_updated = NOW()
    WHERE metric_name = 'total_review_lines';
  END IF;
  
  RETURN QUERY SELECT processed, total_line_count;
END;
$$ LANGUAGE plpgsql;

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_reviews_line_count ON reviews(line_count) WHERE line_count > 0;
CREATE INDEX IF NOT EXISTS idx_site_statistics_metric_name ON site_statistics(metric_name);

-- 9. Add RLS policies for site_statistics table
ALTER TABLE site_statistics ENABLE ROW LEVEL SECURITY;

-- Allow public read access to site statistics
CREATE POLICY site_statistics_public_read ON site_statistics
  FOR SELECT USING (true);

-- Only allow authenticated users to update (for admin functions)
CREATE POLICY site_statistics_auth_update ON site_statistics
  FOR UPDATE USING (auth.role() = 'authenticated');

-- 10. Add comments for documentation
COMMENT ON TABLE site_statistics IS 'Global site-wide statistics and counters';
COMMENT ON COLUMN reviews.line_count IS 'Number of text lines in the review content';
COMMENT ON FUNCTION count_lines_from_lexical(JSONB) IS 'Counts lines of text from Lexical JSON content';
COMMENT ON FUNCTION update_review_line_count() IS 'Trigger function to update line counts when reviews change';
COMMENT ON FUNCTION process_historical_review_line_counts(INTEGER) IS 'Processes existing reviews to calculate line counts';

-- Migration complete
-- Total lines written across all reviews will now be tracked automatically
