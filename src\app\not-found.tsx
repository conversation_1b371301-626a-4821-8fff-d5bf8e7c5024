'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Home, Search, ArrowLeft, Gamepad2, Sparkles } from 'lucide-react';
import { useState, useEffect } from 'react';

export default function NotFound() {
  const [isFloating, setIsFloating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsFloating(prev => !prev);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="text-center max-w-2xl mx-auto">
        {/* Animated Image */}
        <div className={`mb-8 transition-transform duration-3000 ease-in-out ${isFloating ? 'transform -translate-y-2' : 'transform translate-y-2'}`}>
          <div className="relative mx-auto w-64 h-64 mb-6">
            <Image
              src="/imgs/225ffbc6-8bd1-40ec-856f-224ac1570519.png"
              alt="404 Error"
              fill
              className="object-contain drop-shadow-2xl"
              priority
            />
          </div>
        </div>

        {/* Error Code with Gaming Style */}
        <div className="mb-6">
          <h1 className="text-8xl font-mono font-bold text-transparent bg-clip-text bg-gradient-to-r from-violet-400 to-purple-400 mb-2">
            404
          </h1>
          <div className="flex items-center justify-center gap-2 mb-4">
            <Gamepad2 className="text-violet-400 animate-bounce" size={24} />
            <h2 className="text-2xl font-mono text-white">
              <span className="text-violet-400">&lt;</span>
              Content Not Found
              <span className="text-violet-400">/&gt;</span>
            </h2>
            <Sparkles className="text-purple-400 animate-bounce" size={24} />
          </div>
        </div>

        {/* Description */}
        <div className="mb-8 space-y-3">
          <p className="text-lg text-slate-300 font-mono">
            The content you're looking for doesn't exist, has been moved, or is set to private.
          </p>
          <p className="text-sm text-slate-400 font-mono">
            <span className="text-violet-400">// </span>
            Let's get you back to gaming content!
          </p>
        </div>

        {/* Interactive Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            asChild 
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-mono border-0 px-6 py-3 text-lg shadow-lg hover:shadow-violet-500/25 transition-all duration-300"
          >
            <Link href="/">
              <Home className="mr-2 h-5 w-5" />
              <span className="text-violet-200">&lt;</span>
              Home
              <span className="text-violet-200">/&gt;</span>
            </Link>
          </Button>
          
          <Button 
            variant="outline" 
            asChild
            className="border-violet-500/50 text-violet-300 hover:bg-violet-500/10 hover:border-violet-400 font-mono px-6 py-3 text-lg transition-all duration-300"
          >
            <Link href="/reviews">
              <Search className="mr-2 h-5 w-5" />
              <span className="text-violet-400">&lt;</span>
              Browse Reviews
              <span className="text-violet-400">/&gt;</span>
            </Link>
          </Button>
          
          <Button 
            variant="ghost" 
            onClick={() => window.history.back()}
            className="text-slate-400 hover:text-white hover:bg-slate-800/50 font-mono px-6 py-3 text-lg transition-all duration-300"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            <span className="text-slate-500">&lt;</span>
            Go Back
            <span className="text-slate-500">/&gt;</span>
          </Button>
        </div>

        {/* Easter Egg */}
        <div className="mt-12 text-xs text-slate-500 font-mono">
          <p>Error Code: GAME_OVER_404</p>
          <p className="mt-1">
            <span className="text-violet-400">tip:</span> Try pressing ESC to respawn
          </p>
        </div>
      </div>
    </div>
  );
}