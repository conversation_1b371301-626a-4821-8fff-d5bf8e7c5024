import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

/**
 * ADMIN VERIFICATION API ROUTE WITH MFA INTEGRATION
 * Provides server-side admin verification for client components
 * Fixes CRITICAL vulnerability: Client-side admin authentication bypass
 * 
 * Date: June 14, 2025 - Updated: June 16, 2025
 * Security Level: CRITICAL
 * Purpose: Double verification layer for admin access with MFA support
 */

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for server-side verification
    const supabase = await createServerClient();
    
    // LAYER 1: Verify JWT token and get authenticated user
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('🔒 SECURITY: Admin verification failed - no user', {
        error: error?.message,
        timestamp: new Date().toISOString()
      });
      
      return NextResponse.json(
        { isAdmin: false, error: 'Authentication required' }, 
        { status: 401 }
      );
    }

    // LAYER 2: Verify admin status from database
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin, suspended, admin_level')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('🚨 SECURITY: Admin verification database error', {
        userId: user.id,
        error: profileError.message,
        timestamp: new Date().toISOString()
      });
      
      return NextResponse.json(
        { isAdmin: false, error: 'Verification failed' }, 
        { status: 500 }
      );
    }

    // LAYER 3: Check admin privileges and suspension status
    if (!profile?.is_admin || profile?.suspended) {
      console.warn('🔒 SECURITY: Admin verification denied', {
        userId: user.id,
        email: user.email,
        isAdmin: profile?.is_admin || false,
        suspended: profile?.suspended || false,
        timestamp: new Date().toISOString()
      });
      
      return NextResponse.json(
        { isAdmin: false, error: 'Admin privileges required' }, 
        { status: 403 }
      );
    }

    // LAYER 4: Check MFA status
    const { data: mfaSettings, error: mfaError } = await supabase
      .from('user_mfa_settings')
      .select('is_enabled')
      .eq('user_id', user.id)
      .single();

    let mfaRequired = false;
    let mfaVerified = true; // Default to true if MFA not enabled
    let mfaConfigured = false;

    // If no MFA settings found, user hasn't configured MFA yet
    if (mfaError || !mfaSettings) {
      mfaConfigured = false;
      mfaRequired = true; // TEMPORÁRIO: Forçar MFA para teste
      mfaVerified = false; // TEMPORÁRIO: Não verificado para teste
    } else if (mfaSettings.is_enabled) {
      mfaConfigured = true;
      mfaRequired = true;
      
      // Check for valid MFA verification session
      const { data: mfaSession } = await supabase
        .from('mfa_verification_sessions')
        .select('verified, expires_at')
        .eq('user_id', user.id)
        .eq('verified', true)
        .gte('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      mfaVerified = !!mfaSession;
    } else {
      // MFA configured but disabled
      mfaConfigured = true;
      mfaRequired = false;
      mfaVerified = true;
    }

    // LAYER 5: Log successful verification
    console.info('✅ SECURITY: Admin verification successful', {
      userId: user.id,
      email: user.email,
      adminLevel: profile.admin_level || 'MODERATOR',
      mfaRequired,
      mfaVerified,
      timestamp: new Date().toISOString()
    });

    // Return verification result with MFA status
    return NextResponse.json({ 
      isAdmin: true, 
      adminLevel: profile.admin_level || 'MODERATOR',
      userId: user.id,
      mfaRequired,
      mfaVerified,
      mfaConfigured
    });

  } catch (error) {
    console.error('🚨 CRITICAL: Admin verification error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json(
      { isAdmin: false, error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' }, 
    { status: 405 }
  );
}
