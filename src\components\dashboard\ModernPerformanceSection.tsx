"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from "@/lib/utils";
import {
  useOptimizedFilter,
  usePagination,
  PaginationControls,
  MemoizedCard,
  LoadingSkeleton
} from "./DashboardOptimizations";
import { DashboardCard } from "./DashboardCard";
import { CodeTitle, SectionCodeTitle, CodeBadge } from "./CodeTitle";

// DASHBOARD REDESIGN: Phase 2 - Component Styling
// Date: 15/06/2025  
// Task: dashboardStyleAdmin002
//
// Updated ModernPerformanceSection with admin-style design patterns:
// - Applied DashboardCard component for performance survey items
// - Implemented CodeTitle components for consistent typography
// - Enhanced performance indicators with gaming-themed styling
// - Added glassmorphism effects and hover animations
// - Applied purple cosmic theme throughout
import {
  <PERSON>,
  Filter,
  <PERSON><PERSON>,
  Monitor,
  Gamepad2,
  Smartphone,
  Laptop,
  Trash2,
  Download,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Zap,
  Settings,
  Info,
  Calendar,
  Clock
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PerformanceReviewCard } from "./PerformanceReviewCard";
import { updateSurveyPrivacy, softDeletePerformanceSurvey } from "@/lib/services/privacyService";
import { useToast } from "@/hooks/use-toast";
import { useAuthContext } from "@/contexts/auth-context";

interface PerformanceSurvey {
  id?: string;
  game_title?: string;
  device_type: string;
  cpu?: string | null;
  gpu?: string | null;
  platform: string;
  fps_average?: number | null;
  resolution?: string | null;
  frame_gen?: boolean | null;
  frame_gen_type?: string | null;
  upscale?: boolean | null;
  upscale_type?: string | null;
  upscale_preset?: string | null;
  smoothness?: number | null;
  created_at?: string;
  is_private?: boolean;
}

interface ModernPerformanceSectionProps {
  surveys: PerformanceSurvey[];
  loading: boolean;
  onDeleteSurvey: (surveyId: string) => Promise<void>;
  isDarkBackground?: boolean;
}

type DeviceFilter = 'all' | 'pc' | 'handheld' | 'notebook';

const deviceIcons = {
  pc: Monitor,
  handheld: Gamepad2,
  notebook: Laptop
};

const deviceLabels = {
  pc: 'Desktop PC',
  handheld: 'Handheld',
  notebook: 'Laptop'
};

export function ModernPerformanceSection({
  surveys,
  loading,
  onDeleteSurvey,
  isDarkBackground: propIsDarkBackground = true
}: ModernPerformanceSectionProps) {
  const isDarkBackground = useBackgroundBrightness();
  const { user } = useAuthContext();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [deviceFilter, setDeviceFilter] = useState<DeviceFilter>('all');
  const [activeTab, setActiveTab] = useState<'published' | 'private'>('published');

  // Infinite scrolling state
  const [displayedCount, setDisplayedCount] = useState(6);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Ensure surveys is an array to prevent undefined errors
  const safeSurveys = surveys || [];

  // Separate surveys by privacy
  const publishedSurveys = safeSurveys.filter(survey => survey.is_private !== true);
  const privateSurveys = safeSurveys.filter(survey => survey.is_private === true);

  // Get current tab surveys
  const currentTabSurveys = activeTab === 'published' ? publishedSurveys : privateSurveys;

  // Filter and sort surveys based on current tab
  const filteredSurveys = currentTabSurveys.filter((survey: any) => {
    if (!searchQuery) return true;
    const searchTerm = searchQuery.toLowerCase();
    return (
      survey.game_title?.toLowerCase().includes(searchTerm) ||
      survey.cpu?.toLowerCase().includes(searchTerm) ||
      survey.gpu?.toLowerCase().includes(searchTerm) ||
      survey.platform?.toLowerCase().includes(searchTerm)
    );
  }).sort((a: any, b: any) => {
    const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
    return dateB - dateA;
  });

  // Apply device filter
  const deviceFilteredSurveys = useMemo(() => {
    if (deviceFilter === 'all') return filteredSurveys;
    return filteredSurveys.filter(survey => survey.device_type === deviceFilter);
  }, [filteredSurveys, deviceFilter]);

  // Get currently displayed surveys
  const displayedSurveys = useMemo(() => {
    return deviceFilteredSurveys.slice(0, displayedCount);
  }, [deviceFilteredSurveys, displayedCount]);

  // Reset displayed count when filters change
  useEffect(() => {
    setDisplayedCount(6);
  }, [searchQuery, deviceFilter, activeTab]);

  // Infinite scroll handler
  const loadMore = useCallback(async () => {
    if (isLoadingMore || displayedCount >= deviceFilteredSurveys.length) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    setDisplayedCount(prev => Math.min(prev + 6, deviceFilteredSurveys.length));
    setIsLoadingMore(false);
  }, [isLoadingMore, displayedCount, deviceFilteredSurveys.length]);

  // Scroll event listener for infinite scroll
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight) return;
      loadMore();
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadMore]);

  // Stats
  const stats = useMemo(() => {
    if (safeSurveys.length === 0) return { avgFps: 0, totalGames: 0, deviceCounts: {} };

    const validFps = safeSurveys.filter(s => s.fps_average).map(s => s.fps_average!);
    const avgFps = validFps.length > 0 ? validFps.reduce((sum, fps) => sum + fps, 0) / validFps.length : 0;
    const totalGames = new Set(safeSurveys.map(s => s.game_title).filter(Boolean)).size;
    const deviceCounts = safeSurveys.reduce((acc, s) => {
      acc[s.device_type] = (acc[s.device_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return { avgFps: Math.round(avgFps), totalGames, deviceCounts };
  }, [safeSurveys]);


  // Privacy handlers
  const handlePrivacyToggle = async (surveyId: string, isPrivate: boolean) => {
    if (!user?.uid) return;

    try {
      const result = await updateSurveyPrivacy(surveyId, user.uid, isPrivate);
      if (result.success) {
        toast({
          title: "Privacy Updated",
          description: `Survey has been made ${isPrivate ? 'private' : 'public'}.`,
        });
        // Note: We don't have a refresh function here, but the data should update automatically
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update survey privacy. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSoftDelete = async (surveyId: string) => {
    if (!user?.uid) return;

    try {
      const result = await softDeletePerformanceSurvey(surveyId, user.uid);
      if (result.success) {
        toast({
          title: "Survey Hidden",
          description: "Survey has been hidden. You can restore it from privacy settings.",
        });
        // Call the existing delete handler to update the UI
        await onDeleteSurvey(surveyId);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to hide survey. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="p-4 space-y-6">
        <LoadingSkeleton count={3} height="h-8" />
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="bg-slate-900/40 border border-slate-700/40 rounded-lg p-4 animate-pulse">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-7 h-7 bg-slate-700 rounded"></div>
                <div className="flex-1">
                  <div className="h-4 bg-slate-700 rounded mb-1 w-1/3"></div>
                  <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                </div>
              </div>
              <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-3">
                {[...Array(6)].map((_, j) => (
                  <div key={j} className="bg-slate-800/30 rounded p-2">
                    <div className="h-2 bg-slate-700 rounded mb-1"></div>
                    <div className="h-4 bg-slate-700 rounded"></div>
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="bg-slate-800/20 rounded p-2">
                    <div className="h-2 bg-slate-700 rounded mb-1"></div>
                    <div className="h-3 bg-slate-700 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="p-2 sm:p-3 lg:p-4 xl:p-6 space-y-6">
        {/* Header with Stats */}
        <div className="space-y-4">
          <div className="border-l-4 border-purple-500 pl-4">
            <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
              <span className="text-violet-400/60">&lt;</span>
              <span className="px-2">My Surveys</span>
              <span className="text-violet-400/60">/&gt;</span>
            </span>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg border border-slate-700">
            <button
              type="button"
              onClick={() => setActiveTab('published')}
              className={cn(
                "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                activeTab === 'published'
                  ? "bg-purple-600 text-white shadow-lg"
                  : "text-slate-400 hover:text-slate-200 hover:bg-slate-700/50"
              )}
            >
              Published
              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                activeTab === 'published'
                  ? 'bg-purple-700 text-purple-100'
                  : 'bg-slate-600 text-slate-300'
              }`}>
                {publishedSurveys.length}
              </span>
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('private')}
              className={cn(
                "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                activeTab === 'private'
                  ? "bg-purple-600 text-white shadow-lg"
                  : "text-slate-400 hover:text-slate-200 hover:bg-slate-700/50"
              )}
            >
              Private
              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                activeTab === 'private'
                  ? 'bg-purple-700 text-purple-100'
                  : 'bg-slate-600 text-slate-300'
              }`}>
                {privateSurveys.length}
              </span>
            </button>
          </div>
        </div>


        {/* Container for Controls and Content */}
        <div className="border-gray-800 bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl border overflow-hidden">
          {/* Controls Header */}
          <div className="p-6 border-b border-gray-700/50 bg-gray-800/30">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search by game, hardware..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full h-10 rounded-lg border border-slate-600/50 bg-slate-800/50 pl-9 pr-3 text-sm text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all font-['Lato'] backdrop-blur-sm"
                />
              </div>

              {/* Device Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="font-['Lato'] border-slate-600/50 bg-slate-800/50 hover:bg-slate-700/50 backdrop-blur-sm">
                    <Filter className="w-4 h-4 mr-2" />
                    {deviceFilter === 'all' ? 'All Devices' : deviceLabels[deviceFilter]}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-slate-800/95 border-slate-600/50 backdrop-blur-sm">
                  <DropdownMenuItem onClick={() => setDeviceFilter('all')} className="font-['Lato'] hover:bg-slate-700/50">
                    All Devices
                  </DropdownMenuItem>
                  {Object.entries(deviceLabels).map(([type, label]) => (
                    <DropdownMenuItem key={type} onClick={() => setDeviceFilter(type as DeviceFilter)} className="font-['Lato'] hover:bg-slate-700/50">
                      {label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Content Area */}
          <div className="p-6">
            {/* Surveys List */}
            <div className="space-y-3">
              {/* Empty State */}
              {deviceFilteredSurveys.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-slate-600" />
                  </div>
                  <h3 className="text-lg font-medium text-slate-300 mb-2">
                    {searchQuery ? 'No surveys match your search' : `No ${activeTab} surveys`}
                  </h3>
                  <p className="text-slate-500">
                    {searchQuery
                      ? 'Try adjusting your search criteria'
                      : `Your ${activeTab} surveys will appear here`
                    }
                  </p>
                </div>
              )}

              {/* Surveys */}
              {displayedSurveys.map((survey: any) => (
                <PerformanceReviewCard
                  key={survey.id}
                  survey={survey}
                  onHideSurvey={survey.id ? () => handleSoftDelete(survey.id!) : undefined}
                  onPrivacyToggle={survey.id ? (isPrivate: boolean) => handlePrivacyToggle(survey.id!, isPrivate) : undefined}
                  onEditSurvey={() => {
                    // TODO: Implement edit functionality when survey editing is refined
                    toast({
                      title: "Edit Survey",
                      description: "Survey editing will be available soon.",
                    });
                  }}
                />
              ))}

              {/* Load More Button */}
              {displayedCount < deviceFilteredSurveys.length && (
                <div className="flex justify-center pt-4">
                  <Button
                    onClick={loadMore}
                    disabled={isLoadingMore}
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-purple-600 hover:border-purple-600 hover:text-white"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      `Load More (${deviceFilteredSurveys.length - displayedCount} remaining)`
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}



export default ModernPerformanceSection;
