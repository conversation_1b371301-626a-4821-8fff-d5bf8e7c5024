'use client';

import React, { useState, useEffect } from 'react';
import { Star, Eye, Heart, Calendar, ExternalLink, ShoppingCart, Gamepad2, Trophy, Zap, TrendingUp } from 'lucide-react';
import Link from 'next/link';
import EnebaIcon from '@/components/ui/icons/stores/EnebaIcon';
import G2AIcon from '@/components/ui/icons/stores/G2AIcon';
import GOGIcon from '@/components/ui/icons/stores/GOGIcon';
import HRKIcon from '@/components/ui/icons/stores/HRKIcon';
import InstantGamingIcon from '@/components/ui/icons/stores/InstantGamingIcon';
import KinguinIcon from '@/components/ui/icons/stores/KinguinIcon';
import NuuvemIcon from '@/components/ui/icons/stores/NuuvemIcon';

interface FeaturedReviewData {
  id: string;
  title: string;
  game_name: string;
  main_image_url?: string;
  overall_score: number;
  content_lexical: any;
  like_count: number;
  view_count: number;
  created_at: string;
  played_on?: string;
  slug?: string;
}

interface StoreLink {
  id: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  display_order: number;
  color_gradient: string;
}

interface SimpleFeaturedReviewProps {
  userId: string;
}

export default function SimpleFeaturedReview({ userId }: SimpleFeaturedReviewProps) {
  const [featuredReview, setFeaturedReview] = useState<FeaturedReviewData | null>(null);
  const [storeLinks, setStoreLinks] = useState<StoreLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredStripe, setHoveredStripe] = useState<string | null>(null);
  const [showScoreGauge, setShowScoreGauge] = useState(false);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [activeStripe, setActiveStripe] = useState<string | null>(null);

  // Store icon mapping function
  const getStoreIcon = (storeName: string, className: string = "h-4 w-4") => {
    const iconProps = { className };
    switch (storeName) {
      case 'Eneba': return <EnebaIcon {...iconProps} />;
      case 'G2A': return <G2AIcon {...iconProps} />;
      case 'GOG': return <GOGIcon {...iconProps} />;
      case 'HRK': return <HRKIcon {...iconProps} />;
      case 'Instant Gaming': return <InstantGamingIcon {...iconProps} />;
      case 'Kinguin': return <KinguinIcon {...iconProps} />;
      case 'Nuuvem': return <NuuvemIcon {...iconProps} />;
      default: return <ShoppingCart {...iconProps} />;
    }
  };

  // Calculate discount percentage and heat color - SIMPLIFIED VERSION
  const calculateDiscount = (currentPrice: string, originalPrice?: string) => {
    console.log('🔍 Calculating discount:', { currentPrice, originalPrice });

    if (!originalPrice || originalPrice.trim() === '') {
      console.log('❌ No original price provided');
      return null;
    }

    // SIMPLIFIED: Extract only numbers, ignore all symbols and decimals
    const parsePrice = (priceStr: string): number => {
      if (!priceStr) return NaN;

      // Remove everything except numbers
      const numbersOnly = priceStr.replace(/[^0-9]/g, '');

      if (numbersOnly === '') return NaN;

      return parseInt(numbersOnly, 10);
    };

    const current = parsePrice(currentPrice);
    const original = parsePrice(originalPrice);

    console.log('🔢 Parsed prices (numbers only):', { current, original, currentRaw: currentPrice, originalRaw: originalPrice });

    if (isNaN(current) || isNaN(original) || original <= 0) {
      console.log('❌ Invalid price values');
      return null;
    }

    const discountPercent = Math.round(((original - current) / original) * 100);
    const clampedDiscount = Math.max(0, Math.min(99, discountPercent));

    console.log('💰 Discount calculated:', { discountPercent, clampedDiscount });
    return clampedDiscount;
  };

  const getDiscountHeatColor = (discountPercent: number) => {
    // Heat map: 0% = white, 99% = very dark green for better text contrast
    if (discountPercent <= 0) return 'rgb(255, 255, 255)'; // White
    if (discountPercent >= 99) return 'rgb(5, 46, 22)'; // Green-950 (very dark for excellent contrast)

    // Interpolate between white and very dark green
    const hue = 120; // Green hue
    const saturation = Math.min(100, discountPercent * 1.4); // Increase saturation even more with discount
    const lightness = Math.max(15, 100 - (discountPercent * 0.85)); // Decrease lightness aggressively for better contrast

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  const getTextColor = (discountPercent: number) => {
    // More conservative text color switching for better readability
    if (discountPercent > 40) return '#ffffff'; // White text for medium to high discounts
    return '#000000'; // Black text for low discounts
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log('🎯 SimpleFeaturedReview: Fetching for user:', userId);

        // Fetch featured review
        const reviewResponse = await fetch('/api/u/featured-review', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getFeatured',
            userId: userId
          })
        });

        if (!reviewResponse.ok) {
          throw new Error(`HTTP error! status: ${reviewResponse.status}`);
        }

        const reviewResult = await reviewResponse.json();
        console.log('🎯 SimpleFeaturedReview: Review API response:', reviewResult);

        if (reviewResult.success && reviewResult.data) {
          setFeaturedReview(reviewResult.data);
        } else {
          setFeaturedReview(null);
        }

        // Fetch store links
        const storeResponse = await fetch('/api/u/featured-review', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getStoreLinks',
            userId: userId
          })
        });

        if (storeResponse.ok) {
          const storeResult = await storeResponse.json();
          console.log('🎯 SimpleFeaturedReview: Store links response:', storeResult);

          if (storeResult.success && storeResult.data) {
            setStoreLinks(storeResult.data);
          }
        }

      } catch (err) {
        console.error('❌ SimpleFeaturedReview: Error:', err);
        setError(err instanceof Error ? err.message : 'Failed to load featured review');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className="relative overflow-hidden bg-gray-900/40 border border-gray-800/50 rounded-xl h-96 max-h-[450px] backdrop-blur-sm">
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        
        {/* Shimmer Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent translate-x-[-100%] animate-[shimmer_2s_infinite]"></div>
        
        {/* Loading Content */}
        <div className="absolute bottom-6 left-6 right-6 space-y-4">
          <div className="space-y-2">
            <div className="h-7 bg-gray-700/60 rounded-lg w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-700/40 rounded w-1/2 animate-pulse"></div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="h-6 bg-gray-700/40 rounded w-16 animate-pulse"></div>
            <div className="h-6 bg-gray-700/40 rounded w-16 animate-pulse"></div>
            <div className="h-6 bg-gray-700/40 rounded w-20 animate-pulse"></div>
          </div>
        </div>
        
        {/* Loading Stripes */}
        {[0, 1, 2].map((index) => (
          <div 
            key={index}
            className={`absolute right-0 transform rotate-12 origin-right w-60 h-12 bg-gray-700/30 rounded-lg backdrop-blur-sm animate-pulse ${
              index === 0 ? 'top-6 translate-x-0' :
              index === 1 ? 'top-20 translate-x-4 delay-75' :
              'top-34 translate-x-8 delay-150'
            }`}
          ></div>
        ))}
        
        {/* Loading Score Display */}
        <div className="absolute top-6 left-6 bg-gray-800/50 rounded-lg px-4 py-2 animate-pulse">
          <div className="h-6 bg-gray-700/40 rounded w-16"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-900/40 border border-red-700/50 rounded-xl p-8 backdrop-blur-sm h-96 max-h-[450px] flex flex-col items-center justify-center">
        <div className="text-red-400 text-center space-y-2">
          <div className="text-2xl">⚠️</div>
          <p className="font-mono font-medium">// Error loading featured review</p>
          <p className="font-mono text-sm text-red-300/80">{error}</p>
        </div>
      </div>
    );
  }

  if (!featuredReview) {
    return null;
  }



  // Convert score to 0-100 range and no decimals
  // Handle different possible score formats from database
  const rawScore = featuredReview.overall_score || 0;
  let displayScore = 0;
  
  if (rawScore <= 1) {
    // If score is 0-1 (decimal percentage), convert to 0-100
    displayScore = Math.round(rawScore * 100);
  } else if (rawScore <= 10) {
    // If score is 0-10, convert to 0-100
    displayScore = Math.round(rawScore * 10);
  } else {
    // Assume it's already 0-100, just clamp and round
    displayScore = Math.round(Math.max(0, Math.min(100, rawScore)));
  }
  
  console.log('🎯 SimpleFeaturedReview Display score calculation:', {
    originalScore: featuredReview.overall_score,
    displayScore,
    type: typeof featuredReview.overall_score,
    conversionUsed: rawScore <= 1 ? 'decimal-to-percent' : rawScore <= 10 ? '10-scale-to-100' : 'clamped-100'
  });

  // Generate review URL using slug if available, fallback to ID
  const reviewUrl = featuredReview.slug 
    ? `/reviews/view/${featuredReview.slug}` 
    : `/reviews/${featuredReview.id}`;

  return (
    <div className="relative group">
      <Link href={reviewUrl} className="block">
        {/* Neural Banner Container */}
        <div 
          className="relative overflow-hidden rounded-xl h-96 max-h-[450px] cursor-pointer bg-gray-900/40 border border-gray-800/50 backdrop-blur-sm"
          onMouseEnter={() => setShowScoreGauge(true)}
          onMouseLeave={() => setShowScoreGauge(false)}
        >
        
        {/* Neural Background System */}
        <div className="absolute inset-0">
          {featuredReview.main_image_url ? (
            <img 
              src={featuredReview.main_image_url}
              alt={featuredReview.game_name}
              className={`w-full h-full object-cover ${
                isImageLoaded ? 'opacity-80' : 'opacity-0'
              }`}
              onLoad={() => setIsImageLoaded(true)}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-slate-900 via-black to-slate-800"></div>
          )}
          
          {/* Neural Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/40 to-black/10"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-transparent to-black/30"></div>
          
          {/* Subtle Code Pattern Overlay */}
          <div className="absolute inset-0 opacity-5">
            <div className="w-full h-full bg-gradient-to-br from-primary-accent/20 via-transparent to-transparent"></div>
          </div>
          
        </div>

        {/* Neural Deal System - Icon to Left Animation */}
        {storeLinks.length > 0 && (
          <div className="absolute top-1/2 right-4 transform -translate-y-1/2 flex flex-col gap-2 z-30">
            {storeLinks.map((link) => (
              <div
                key={link.id}
                className="relative cursor-pointer group"
                onMouseEnter={() => {
                  setHoveredStripe(link.id);
                  setActiveStripe(link.id);
                }}
                onMouseLeave={() => {
                  setHoveredStripe(null);
                  setActiveStripe(null);
                }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  window.open(link.store_url, '_blank', 'noopener,noreferrer');
                }}
              >
                {/* Dynamic Width Container - Expands to accommodate content */}
                <div className="relative min-w-96 w-auto h-12 overflow-visible">
                  {(() => {
                    const discountPercent = calculateDiscount(link.price, link.original_price);
                    const hasDiscount = discountPercent !== null && discountPercent > 0;

                    return (
                      <>
                        {/* Collapsed State - Deal Icon (Always Visible on Right) */}
                        <div className={`absolute right-0 top-0 h-12 rounded-lg bg-gray-800/30 backdrop-blur-md border border-gray-700/30 flex items-center justify-center z-20 px-2 ${hasDiscount ? 'w-32' : 'w-24'}`}>
                          {/* Discount Badge inside BUY NOW button */}
                          {hasDiscount && (
                            <div
                              className="flex items-center justify-center px-2 py-1 rounded text-sm font-mono font-bold mr-2"
                              style={{
                                backgroundColor: getDiscountHeatColor(discountPercent),
                                color: getTextColor(discountPercent)
                              }}
                            >
                              -{discountPercent}%
                            </div>
                          )}

                          <span className="text-white/80 text-xs font-mono font-bold text-center leading-tight">
                            BUY NOW
                          </span>
                        </div>

                        {/* Sliding Content Panel */}
                        <div className={`
                          absolute ${hasDiscount ? 'right-36' : 'right-28'} top-0 h-12 rounded-lg bg-gray-800/30 backdrop-blur-md
                          border border-gray-700/30 flex items-center px-3 gap-2
                          transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]
                          ${hoveredStripe === link.id ?
                            'w-auto translate-x-0 opacity-100' :
                            'w-0 translate-x-8 opacity-0'
                          }
                        `}>

                          {/* Price Info */}
                          <div className="flex flex-col justify-center flex-shrink-0 pl-3">
                            <div className="text-white font-mono text-sm font-bold whitespace-nowrap">
                              {link.price}
                            </div>
                            {link.original_price && (
                              <div className="text-white/50 font-mono text-sm line-through whitespace-nowrap">
                                {link.original_price}
                              </div>
                            )}
                          </div>

                          {/* Platform Badge */}
                          <div className="bg-gray-900/50 rounded pr-2 border border-gray-700/30 flex items-center gap-2 flex-shrink-0">
                            <div className="flex items-center justify-center">
                              {getStoreIcon(link.store_name, "h-7 w-7 text-white/70 flex-shrink-0")}
                            </div>
                            <span className="text-white/80 font-mono text-xs whitespace-nowrap flex items-center">
                              {link.store_name}
                            </span>
                          </div>

                          {/* Action Button */}
                          <div className="w-8 h-8 rounded bg-gray-900/50 border border-gray-700/30 flex items-center justify-center hover:bg-gray-800/50 transition-colors shrink-0">
                            <ExternalLink className="h-4 w-4 text-white/60" />
                          </div>
                        </div>


                      </>
                    );
                  })()}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Neural Content System */}
        <div className="absolute bottom-0 left-0 right-0 p-6 z-10">
          <div className="space-y-3">
            {/* Title with Code Aesthetic */}
            <div className="space-y-2">
              <h3 className="text-2xl font-bold text-white font-mono tracking-tight leading-tight">
                <span className="text-white/40 font-mono text-lg mr-2">//</span>
                {featuredReview.title || featuredReview.game_name}
              </h3>
              
              {/* Played On Info */}
              <div className="text-sm text-white/70 font-mono">
                <span className="text-white/40">//</span> Played on {featuredReview.played_on || 'PC'}
              </div>
            </div>
            
            {/* Neural Stats Bar */}
            <div className="flex items-center gap-4 font-mono text-xs">
              <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                <Heart className="w-3 h-3 text-red-400" />
                <span className="text-white tabular-nums">{featuredReview.like_count || 0}</span>
              </div>
              
              <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                <Eye className="w-3 h-3 text-blue-400" />
                <span className="text-white tabular-nums">{featuredReview.view_count || 0}</span>
              </div>
              
              <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                <Calendar className="w-3 h-3 text-green-400" />
                <span className="text-white/60">
                  {new Date(featuredReview.created_at).toLocaleDateString('en-US')}
                </span>
              </div>
              
            </div>
          </div>
        </div>

        {/* Neural Score Display */}
        {showScoreGauge && (
          <div className="absolute top-6 left-6 z-50 animate-[fadeInScale_400ms_ease-out]">
            <div className="bg-gray-900/40 backdrop-blur-md rounded-lg border border-gray-800/50 px-4 py-2">
              <div className="text-xl font-mono font-bold text-white tabular-nums">
                <span className="text-white/40 text-sm">//</span> {displayScore}<span className="text-white/60 text-sm">/100</span>
              </div>
            </div>
          </div>
        )}

        </div>
      </Link>

      {/* Enhanced Custom Animations */}
      <style jsx>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(30px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }
        
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8) rotate(-5deg);
          }
          to {
            opacity: 1;
            transform: scale(1) rotate(0deg);
          }
        }
        
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }
        
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
} 