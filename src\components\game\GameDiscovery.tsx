'use client';

import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';
import { GameData } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, ExternalLink, Compass, Sparkles } from 'lucide-react';

interface GameDiscoveryProps {
  gameId: string;
  gameSlug: string;
}

export default function GameDiscovery({ gameId, gameSlug }: GameDiscoveryProps) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['similar-games', gameSlug],
    queryFn: async () => {
      const response = await fetch(`/api/games/by-slug/${gameSlug}/similar?limit=6`);
      if (!response.ok) {
        throw new Error('Failed to fetch similar games');
      }
      return response.json();
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  const similarGames: GameData[] = data?.similar_games || [];

  const formatRating = (rating: number | null) => {
    if (!rating) return null;
    return rating.toFixed(1);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-4 bg-gray-800/50 border-gray-700">
          <div className="h-4 bg-gray-700 rounded w-2/3 mb-3 animate-pulse" />
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex space-x-3 animate-pulse">
                <div className="w-12 h-16 bg-gray-700 rounded" />
                <div className="flex-1 space-y-2">
                  <div className="h-3 bg-gray-700 rounded w-full" />
                  <div className="h-2 bg-gray-700 rounded w-2/3" />
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    );
  }

  if (error || !similarGames.length) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Similar Games */}
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex items-center space-x-2 mb-4">
          <Compass className="w-5 h-5 text-blue-400" />
          <h3 className="font-semibold text-white">Similar Games</h3>
        </div>
        
        <div className="space-y-3">
          {similarGames.slice(0, 4).map((game) => (
            <Link 
              key={game.id} 
              href={`/games/${game.slug}`}
              className="group block"
            >
              <div className="flex space-x-3 p-2 rounded-lg hover:bg-gray-700/30 transition-colors">
                <div className="flex-shrink-0">
                  {game.supabase_cover_url || game.cover_url ? (
                    <Image
                      src={game.supabase_cover_url || game.cover_url}
                      alt={game.name}
                      width={48}
                      height={64}
                      className="w-12 h-16 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-16 bg-gray-700 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-400">🎮</span>
                    </div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-white group-hover:text-blue-400 transition-colors line-clamp-1">
                    {game.name}
                  </h4>
                  
                  {game.aggregated_rating && (
                    <div className="flex items-center space-x-1 mt-1">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-400">
                        {formatRating(game.aggregated_rating)}
                      </span>
                    </div>
                  )}
                  
                  {game.genres && game.genres.length > 0 && (
                    <div className="mt-1">
                      <Badge 
                        variant="secondary" 
                        className="text-xs bg-gray-700 text-gray-300 py-0"
                      >
                        {game.genres[0]}
                      </Badge>
                    </div>
                  )}
                </div>
                
                <div className="flex-shrink-0 flex items-center">
                  <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-blue-400 transition-colors" />
                </div>
              </div>
            </Link>
          ))}
        </div>
        
        {similarGames.length > 4 && (
          <div className="mt-4 text-center">
            <Button variant="outline" size="sm" className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600">
              View More Similar Games
            </Button>
          </div>
        )}
      </Card>

      {/* Game Discovery CTA */}
      <Card className="p-4 bg-gradient-to-br from-indigo-900/20 to-purple-900/20 border-indigo-500/30">
        <div className="text-center">
          <div className="flex justify-center mb-2">
            <Sparkles className="w-5 h-5 text-indigo-400" />
          </div>
          <h3 className="text-sm font-semibold text-white mb-2">Discover More Games</h3>
          <p className="text-xs text-gray-300 mb-3">
            Explore our game database with advanced filters and recommendations
          </p>
          <Button 
            size="sm" 
            className="w-full bg-indigo-600 hover:bg-indigo-500 text-white"
            asChild
          >
            <Link href="/games">Browse All Games</Link>
          </Button>
        </div>
      </Card>

      {/* Trending Tags - Mock Content */}
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <h3 className="font-semibold text-white mb-3 text-sm">🔥 Trending Tags</h3>
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            Action
          </Badge>
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            Indie
          </Badge>
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            Strategy
          </Badge>
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            RPG
          </Badge>
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            Multiplayer
          </Badge>
          <Badge variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700/50 text-xs">
            Simulation
          </Badge>
        </div>
      </Card>

      {/* Community Recommendations - Mock Content */}
      <Card className="p-4 bg-gradient-to-br from-green-900/20 to-teal-900/20 border-green-500/30">
        <h3 className="font-semibold text-white mb-3 text-sm">👥 Community Picks</h3>
        <div className="space-y-2">
          <div className="text-xs text-gray-300">
            "If you like this game, try Hades - Amazing action RPG!"
          </div>
          <div className="text-xs text-gray-400">
            - Recommended by GamerPro123
          </div>
        </div>
        <Button 
          size="sm" 
          variant="outline" 
          className="w-full mt-3 bg-green-600/20 border-green-500/30 text-green-300 hover:bg-green-600/30 text-xs"
        >
          Share Your Recommendation
        </Button>
      </Card>

      {/* Recent Activity - Mock Content */}
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <h3 className="font-semibold text-white mb-3 text-sm">📈 Recent Activity</h3>
        <div className="space-y-2 text-xs">
          <div className="flex justify-between items-center">
            <span className="text-gray-300">New review posted</span>
            <span className="text-gray-400">2h ago</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-300">Performance data added</span>
            <span className="text-gray-400">5h ago</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-300">Achievement unlocked</span>
            <span className="text-gray-400">1d ago</span>
          </div>
        </div>
      </Card>
    </div>
  );
}