import React from 'react';

interface NintendoSwitchIconSimpleProps {
  className?: string;
  size?: number;
}

const NintendoSwitchIconSimple: React.FC<NintendoSwitchIconSimpleProps> = ({ 
  className = '', 
  size = 24 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Nintendo Switch logo - Joy-Con controllers with screen */}
      <rect x="2" y="4" width="4" height="16" rx="2" fill="currentColor"/>
      <rect x="18" y="4" width="4" height="16" rx="2" fill="currentColor"/>
      <rect x="7" y="6" width="10" height="12" rx="1" fill="currentColor"/>
      <rect x="8" y="7" width="8" height="10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
      <circle cx="4" cy="8" r="0.8" fill="none" stroke="currentColor" strokeWidth="0.3"/>
      <circle cx="20" cy="8" r="0.8" fill="none" stroke="currentColor" strokeWidth="0.3"/>
      <circle cx="4" cy="16" r="0.8" fill="none" stroke="currentColor" strokeWidth="0.3"/>
      <circle cx="20" cy="16" r="0.8" fill="none" stroke="currentColor" strokeWidth="0.3"/>
    </svg>
  );
};

export default NintendoSwitchIconSimple;