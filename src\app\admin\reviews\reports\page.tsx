'use client';

import { useEffect, useState } from 'react';
import { useAuthContext } from '@/hooks/use-auth-context';
import { useToast } from '@/hooks/use-toast';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, CheckCircle, X, ChevronLeft, ChevronRight, Search, Eye, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  getAllReportsSecure, 
  resolveReportSecure, 
  type ContentFlag, 
  type ReportModerationAction,
  moderateReviewSecure
} from '../actions';
import { ReportsActionsDropdown } from '@/components/admin/ReportsActionsDropdown';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

type ReportStatus = 'pending' | 'resolved' | 'dismissed';

export default function AdminReportsPage() {
  const { user, loading, isAdmin } = useAuthContext();
  const { toast } = useToast();
  
  const [reports, setReports] = useState<ContentFlag[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>(['pending']);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalReports, setTotalReports] = useState(0);
  const [sortBy, setSortBy] = useState<'created_at' | 'resolved_at'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const reportsPerPage = 20;

  // Load reports
  const loadReports = async () => {
    if (!user?.id || !isAdmin) return;

    setIsLoadingReports(true);
    try {
      const result = await getAllReportsSecure({
        status: statusFilter,
        page: currentPage,
        limit: reportsPerPage,
        sortBy,
        sortOrder,
        search: searchTerm
      });

      setReports(result.reports);
      setTotalReports(result.total);
    } catch (error: any) {
      console.error('Error loading reports:', error);
      toast({
        title: "Error loading reports",
        description: error.message || "Failed to load reports for moderation",
        variant: "destructive"
      });
      // Fallback: Set empty state
      setReports([]);
      setTotalReports(0);
    } finally {
      setIsLoadingReports(false);
    }
  };

  useEffect(() => {
    if (!loading && isAdmin && user?.id) {
      loadReports();
    }
  }, [user, loading, isAdmin, statusFilter, currentPage, sortBy, sortOrder, searchTerm]);

  // Keyboard shortcuts for reports management
  useKeyboardShortcuts({
    shortcuts: [
      {
        key: 'F5',
        action: () => {
          loadReports();
          toast({
            title: "Reports refreshed",
            description: "Report list has been updated"
          });
        },
        description: 'Refresh reports list'
      },
      {
        key: '/',
        action: () => {
          const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
          if (searchInput) {
            searchInput.focus();
          }
        },
        description: 'Focus search box'
      },
      {
        key: 'Escape',
        action: () => {
          setSearchTerm('');
          setStatusFilter(['pending']);
          setCurrentPage(1);
        },
        description: 'Clear filters and search'
      }
    ]
  });

  const getStatusBadgeVariant = (status: ReportStatus): "default" | "secondary" | "outline" | "destructive" => {
    switch (status) {
      case 'pending':
        return 'destructive';
      case 'resolved':
        return 'default';
      case 'dismissed':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusDisplayName = (status: ReportStatus): string => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'resolved': return 'Resolved';
      case 'dismissed': return 'Dismissed';
      default: return status;
    }
  };

  const handleResolveReport = async (reportId: string, action: ReportModerationAction) => {
    if (!user?.id) return;

    try {
      const result = await resolveReportSecure(reportId, action);

      if (result.success) {
        toast({
          title: "Report processed successfully",
          description: `Report was ${action.action === 'resolve' ? 'resolved' : 'dismissed'}`,
        });
        await loadReports(); // Reload the list
      } else {
        toast({
          title: "Failed to process report",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error processing report",
        description: "Failed to process report",
        variant: "destructive"
      });
    }
  };

  // Função para bloquear/desbloquear review
  const handleBlockReview = async (reviewId: string, action: { action: 'block' | 'unblock' }) => {
    if (!user?.id) return;
    try {
      await moderateReviewSecure(reviewId, action);
      toast({
        title: action.action === 'block' ? 'Review blocked' : 'Review unblocked',
        description: action.action === 'block' ? 'The review was blocked successfully.' : 'The review was unblocked successfully.',
      });
      await loadReports();
    } catch (error) {
      toast({
        title: 'Error blocking/unblocking review',
        description: 'Unable to process the action.',
        variant: 'destructive'
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-10rem)]"><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div></div>;
  }

  if (!isAdmin) {
    return (
      <AdminLayout
        title="Access Denied"
        description="You don't have administrator permissions"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Reports' }
        ]}
      >
        <div className="flex items-center justify-center min-h-[calc(100vh-20rem)]">
          <Card className="w-full max-w-md bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
            <CardHeader className="text-center">
              <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <CardTitle className="text-destructive font-mono tracking-tight">
                <span className="text-red-400">&lt;</span>
                <span className="text-red-300 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Access Denied</span>
                <span className="text-red-400">/&gt;</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground font-mono text-sm">
                You don't have administrator privileges.
              </p>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Report Management"
      description="Manage user content reports and flags"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Reviews', href: '/admin/reviews' },
        { label: 'Reports' }
      ]}
    >
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <div className="flex gap-2">
          <Button asChild variant="outline" className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
            <Link href="/admin/reviews">
              ← Back to Reviews
            </Link>
          </Button>
        </div>
      </div>

      <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-2xl font-mono tracking-tight">
            <span className="text-violet-400">&lt;</span>
            <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Reports & Flags Queue</span>
            <span className="text-violet-400">/&gt;</span>
          </CardTitle>
          <CardDescription className="text-muted-foreground font-mono text-sm">
            Manage content reports and inappropriate content flags from users.
          </CardDescription>
          
          <div className="flex flex-col md:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by reason or description..."
                className="pl-10 bg-slate-800/60 border-slate-600/50 focus:border-violet-500/50 transition-colors font-mono text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Select value={statusFilter.join(',')} onValueChange={(value) => setStatusFilter(value.split(','))}>
              <SelectTrigger className="w-[200px] bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                <SelectItem value="pending" className="font-mono text-sm">Pending</SelectItem>
                <SelectItem value="resolved" className="font-mono text-sm">Resolved</SelectItem>
                <SelectItem value="dismissed" className="font-mono text-sm">Dismissed</SelectItem>
                <SelectItem value="pending,resolved,dismissed" className="font-mono text-sm">All</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field as any);
              setSortOrder(order as any);
            }}>
              <SelectTrigger className="w-[200px] bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                <SelectItem value="created_at-desc" className="font-mono text-sm">Most Recent</SelectItem>
                <SelectItem value="created_at-asc" className="font-mono text-sm">Oldest First</SelectItem>
                <SelectItem value="resolved_at-desc" className="font-mono text-sm">Recently Resolved</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          {isLoadingReports ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table className="border-slate-700/30">
                <TableHeader>
                  <TableRow className="border-slate-700/30 hover:bg-slate-800/30">
                    <TableHead className="font-mono text-violet-300">Review</TableHead>
                    <TableHead className="font-mono text-violet-300">Reported by</TableHead>
                    <TableHead className="font-mono text-violet-300">Reason</TableHead>
                    <TableHead className="font-mono text-violet-300">Status</TableHead>
                    <TableHead className="font-mono text-violet-300">Date</TableHead>
                    <TableHead className="text-right font-mono text-violet-300">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.length > 0 ? reports.map((report) => (
                    <TableRow key={report.id} className="hover:bg-slate-800/20 transition-colors border-slate-700/20">
                      <TableCell className="font-mono">
                        <div className="space-y-1">
                          <div className="font-medium">
                            {report.review_slug ? (
                              <Link
                                href={`/reviews/view/${report.review_slug}`}
                                target="_blank"
                                className="text-violet-400 hover:text-violet-300 transition-colors flex items-center gap-1 font-mono text-sm"
                              >
                                {report.review_title}
                                <ExternalLink className="h-3 w-3" />
                              </Link>
                            ) : (
                              <span className="text-muted-foreground font-mono text-sm">{report.review_title}</span>
                            )}
                          </div>
                          {report.description && (
                            <div className="text-xs text-muted-foreground max-w-xs truncate font-mono">
                              {report.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell className="font-mono">
                        <div className="space-y-1">
                          <div className="font-medium text-sm">{report.reporter_name}</div>
                          <div className="text-xs text-muted-foreground">@{report.reporter_username}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell className="font-mono">
                        <Badge variant="outline" className="font-mono text-xs">
                          {report.reason}
                        </Badge>
                      </TableCell>
                      
                      <TableCell className="font-mono">
                        <Badge variant={getStatusBadgeVariant(report.status as ReportStatus)} className="font-mono text-xs">
                          {getStatusDisplayName(report.status as ReportStatus)}
                        </Badge>
                      </TableCell>
                      
                      <TableCell className="font-mono">
                        <div className="space-y-1">
                          <div className="text-xs text-muted-foreground">{formatDate(report.created_at)}</div>
                          {report.resolved_at && (
                            <div className="text-xs text-muted-foreground">
                              Resolved: {formatDate(report.resolved_at)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell className="text-right font-mono">
                        <ReportsActionsDropdown 
                          report={report}
                          onResolveReport={handleResolveReport}
                          onBlockReview={handleBlockReview}
                          disabled={isLoadingReports}
                        />
                      </TableCell>
                    </TableRow>
                  )) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center text-muted-foreground py-8 font-mono text-sm">
                        No reports found matching search criteria.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex items-center justify-between pt-4">
          <span className="text-sm text-muted-foreground font-mono">
            Showing <strong className="text-violet-400">{reports.length}</strong> of <strong className="text-violet-400">{totalReports}</strong> reports
          </span>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              className="border-slate-600/50 hover:border-violet-500/50 hover:bg-slate-700/30 transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <span className="flex items-center px-3 text-sm font-mono text-muted-foreground">
              Page <span className="text-violet-400">{currentPage}</span> of <span className="text-violet-400">{Math.ceil(totalReports / reportsPerPage)}</span>
            </span>
            <Button
              variant="outline"
              size="icon"
              disabled={currentPage >= Math.ceil(totalReports / reportsPerPage)}
              onClick={() => setCurrentPage(prev => prev + 1)}
              className="border-slate-600/50 hover:border-violet-500/50 hover:bg-slate-700/30 transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </AdminLayout>
  );
} 