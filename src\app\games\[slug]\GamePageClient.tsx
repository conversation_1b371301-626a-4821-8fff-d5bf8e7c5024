'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { GameData, GameStats as GameStatsType } from '@/lib/services/gameService';
import GameHero from '@/components/game/GameHero';
import GameStatsComponent from '@/components/game/GameStats';
import GameReviews from '@/components/game/GameReviews';
import GamePerformance from '@/components/game/GamePerformance';
import GameDiscovery from '@/components/game/GameDiscovery';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
// Note: ViewTracker is for reviews, game page analytics would need a separate implementation

interface GamePageClientProps {
  slug: string;
  initialGame: GameData;
  initialStats: GameStatsType;
}

export default function GamePageClient({ 
  slug, 
  initialGame, 
  initialStats 
}: GamePageClientProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Track page view
  useEffect(() => {
    // Track game page view for analytics
    if (typeof window !== 'undefined') {
      // Implementation would depend on your analytics setup
      console.log(`Game page viewed: ${initialGame.name}`);
    }
  }, [initialGame.name]);

  // Query for real-time stats updates
  const { data: currentStats } = useQuery({
    queryKey: ['game-stats', initialGame.id],
    queryFn: async () => {
      const response = await fetch(`/api/games/by-slug/${slug}`);
      const data = await response.json();
      return data.stats;
    },
    initialData: initialStats,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* TODO: Implement game page analytics tracking */}

      {/* Game Hero Section */}
      <GameHero game={initialGame} stats={currentStats} />

      {/* Main Content Area */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs 
              value={activeTab} 
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4 w-full mb-8 bg-gray-800/50 border border-gray-700">
                <TabsTrigger 
                  value="overview" 
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger 
                  value="reviews"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                >
                  Reviews ({currentStats.total_reviews})
                </TabsTrigger>
                <TabsTrigger 
                  value="performance"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                >
                  Performance
                </TabsTrigger>
                <TabsTrigger 
                  value="community"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
                >
                  Community
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-8">
                {/* Game Summary */}
                {initialGame.summary && (
                  <Card className="p-6 bg-gray-800/50 border-gray-700">
                    <h2 className="text-xl font-bold text-white mb-4">About {initialGame.name}</h2>
                    <p className="text-gray-300 leading-relaxed">
                      {initialGame.summary}
                    </p>
                  </Card>
                )}

                {/* Quick Stats */}
                <GameStatsComponent game={initialGame} stats={currentStats} />

                {/* Recent Reviews Preview */}
                <Card className="p-6 bg-gray-800/50 border-gray-700">
                  <h2 className="text-xl font-bold text-white mb-4">Recent Reviews</h2>
                  <GameReviews 
                    gameId={initialGame.id} 
                    gameSlug={slug}
                    limit={3}
                    showPagination={false}
                  />
                  {currentStats.total_reviews > 3 && (
                    <div className="mt-4 text-center">
                      <button
                        onClick={() => setActiveTab('reviews')}
                        className="text-blue-400 hover:text-blue-300 font-medium"
                      >
                        View all {currentStats.total_reviews} reviews →
                      </button>
                    </div>
                  )}
                </Card>
              </TabsContent>

              <TabsContent value="reviews">
                <GameReviews 
                  gameId={initialGame.id} 
                  gameSlug={slug}
                  showPagination={true}
                />
              </TabsContent>

              <TabsContent value="performance">
                <GamePerformance 
                  gameId={initialGame.id} 
                  gameSlug={slug}
                />
              </TabsContent>

              <TabsContent value="community">
                <div className="space-y-6">
                  {/* Community Stats */}
                  <Card className="p-6 bg-gray-800/50 border-gray-700">
                    <h2 className="text-xl font-bold text-white mb-4">Community Activity</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">
                          {currentStats.total_reviews}
                        </div>
                        <div className="text-sm text-gray-400">Reviews</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">
                          {currentStats.total_performance_surveys}
                        </div>
                        <div className="text-sm text-gray-400">Performance Reports</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">
                          {currentStats.total_views}
                        </div>
                        <div className="text-sm text-gray-400">Total Views</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-400">
                          {currentStats.recent_activity_count}
                        </div>
                        <div className="text-sm text-gray-400">Recent Activity</div>
                      </div>
                    </div>
                  </Card>

                  {/* Community Reviews */}
                  <GameReviews 
                    gameId={initialGame.id} 
                    gameSlug={slug}
                    showPagination={true}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Game Discovery */}
            <GameDiscovery 
              gameId={initialGame.id} 
              gameSlug={slug}
            />

            {/* Monetization Area - Mock Content */}
            <Card className="p-6 bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/30">
              <h3 className="text-lg font-bold text-white mb-4">🛒 Where to Buy</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <div>
                    <div className="text-sm font-medium text-white">Steam</div>
                    <div className="text-xs text-green-400">$29.99</div>
                  </div>
                  <button className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-500">
                    View
                  </button>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <div>
                    <div className="text-sm font-medium text-white">Epic Games</div>
                    <div className="text-xs text-yellow-400">$27.99</div>
                  </div>
                  <button className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-500">
                    View
                  </button>
                </div>
              </div>
            </Card>

            {/* Hardware Recommendations - Mock Content */}
            <Card className="p-6 bg-gradient-to-br from-green-900/20 to-blue-900/20 border-green-500/30">
              <h3 className="text-lg font-bold text-white mb-4">⚡ Recommended Hardware</h3>
              <div className="space-y-3">
                <div className="p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <div className="text-sm font-medium text-white">GPU: RTX 4060</div>
                  <div className="text-xs text-gray-400">Optimal 1080p performance</div>
                  <div className="text-xs text-green-400 mt-1">$299 - View deals</div>
                </div>
                <div className="p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                  <div className="text-sm font-medium text-white">CPU: Ryzen 5 5600X</div>
                  <div className="text-xs text-gray-400">Great price/performance</div>
                  <div className="text-xs text-green-400 mt-1">$199 - View deals</div>
                </div>
              </div>
            </Card>

            {/* Community Creator Spotlight - Mock Content */}
            <Card className="p-6 bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/30">
              <h3 className="text-lg font-bold text-white mb-4">⭐ Top Contributors</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                    1
                  </div>
                  <div>
                    <div className="text-sm font-medium text-white">GamerPro123</div>
                    <div className="text-xs text-gray-400">5 detailed reviews</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                    2
                  </div>
                  <div>
                    <div className="text-sm font-medium text-white">TechReviewer</div>
                    <div className="text-xs text-gray-400">3 performance reports</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}