'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { GameData, GameStats as GameStatsType } from '@/lib/services/gameService';
import GameHero from '@/components/game/GameHero';
import GameStatsComponent from '@/components/game/GameStats';
import GameReviews from '@/components/game/GameReviews';
import GamePerformance from '@/components/game/GamePerformance';
import GameDiscovery from '@/components/game/GameDiscovery';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  Users,
  BarChart3,
  MessageSquare,
  TrendingUp,
  Eye,
  Heart,
  Calendar,
  Clock
} from 'lucide-react';
// Note: ViewTracker is for reviews, game page analytics would need a separate implementation

interface GamePageClientProps {
  slug: string;
  initialGame: GameData;
  initialStats: GameStatsType;
}

export default function GamePageClient({ 
  slug, 
  initialGame, 
  initialStats 
}: GamePageClientProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Track page view
  useEffect(() => {
    // Track game page view for analytics
    if (typeof window !== 'undefined') {
      // Implementation would depend on your analytics setup
      console.log(`Game page viewed: ${initialGame.name}`);
    }
  }, [initialGame.name]);

  // Query for real-time stats updates
  const { data: currentStats } = useQuery({
    queryKey: ['game-stats', initialGame.id],
    queryFn: async () => {
      const response = await fetch(`/api/games/by-slug/${slug}`);
      const data = await response.json();
      return data.stats;
    },
    initialData: initialStats,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800">
      {/* TODO: Implement game page analytics tracking */}

      {/* Game Hero Section */}
      <GameHero game={initialGame} stats={currentStats} />

      {/* Main Content Area */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Modern Navigation Cards */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[
                {
                  id: 'overview',
                  label: 'Overview',
                  icon: Star,
                  count: null,
                  gradient: 'from-blue-900/60 to-blue-800/60',
                  border: 'border-blue-500/30'
                },
                {
                  id: 'reviews',
                  label: 'Reviews',
                  icon: MessageSquare,
                  count: currentStats.total_reviews,
                  gradient: 'from-purple-900/60 to-purple-800/60',
                  border: 'border-purple-500/30'
                },
                {
                  id: 'performance',
                  label: 'Performance',
                  icon: BarChart3,
                  count: null,
                  gradient: 'from-green-900/60 to-green-800/60',
                  border: 'border-green-500/30'
                },
                {
                  id: 'community',
                  label: 'Community',
                  icon: Users,
                  count: null,
                  gradient: 'from-orange-900/60 to-orange-800/60',
                  border: 'border-orange-500/30'
                }
              ].map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;

                return (
                  <motion.div
                    key={tab.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
                  >
                    <Card
                      className={`
                        relative cursor-pointer transition-all duration-300
                        ${isActive
                          ? `bg-gradient-to-br ${tab.gradient} border ${tab.border} shadow-lg`
                          : 'bg-slate-900/60 border-slate-700/50 hover:bg-slate-800/60 hover:border-slate-600/50'
                        }
                      `}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <div className="p-4 text-center">
                        <Icon className={`w-6 h-6 mx-auto mb-2 ${isActive ? 'text-white' : 'text-slate-400'}`} />
                        <h3 className={`font-medium text-sm ${isActive ? 'text-white' : 'text-slate-300'}`}>
                          {tab.label}
                        </h3>
                        {tab.count !== null && (
                          <Badge
                            variant="secondary"
                            className={`mt-1 text-xs ${isActive ? 'bg-white/20 text-white' : 'bg-slate-700/50 text-slate-300'}`}
                          >
                            {tab.count}
                          </Badge>
                        )}
                      </div>
                      {isActive && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 rounded-lg border-2 border-white/20"
                          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                        />
                      )}
                    </Card>
                  </motion.div>
                );
              })}
            </div>

            {/* Content Sections */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="space-y-8"
            >
              {activeTab === 'overview' && (
                <>
                  {/* Game Summary */}
                  {initialGame.summary && (
                    <Card className="p-6 bg-slate-900/60 border border-slate-700/50 rounded-lg">
                      <h2 className="text-xl font-bold text-white mb-4" style={{ textShadow: '2px 3px 0px rgba(0,0,0,1)' }}>
                        // About {initialGame.name}
                      </h2>
                      <p className="text-slate-300 leading-relaxed">
                        {initialGame.summary}
                      </p>
                    </Card>
                  )}

                  {/* Quick Stats */}
                  <GameStatsComponent game={initialGame} stats={currentStats} />

                  {/* Recent Reviews Preview */}
                  <Card className="p-6 bg-slate-900/60 border border-slate-700/50 rounded-lg">
                    <h2 className="text-xl font-bold text-white mb-4" style={{ textShadow: '2px 3px 0px rgba(0,0,0,1)' }}>
                      // Recent Reviews
                    </h2>
                    <GameReviews
                      gameId={initialGame.id}
                      gameSlug={slug}
                      limit={3}
                      showPagination={false}
                    />
                    {currentStats.total_reviews > 3 && (
                      <div className="mt-4 text-center">
                        <Button
                          type="button"
                          onClick={() => setActiveTab('reviews')}
                          variant="ghost"
                          className="text-blue-400 hover:text-blue-300 hover:bg-blue-900/20"
                        >
                          View all {currentStats.total_reviews} reviews →
                        </Button>
                      </div>
                    )}
                  </Card>
                </>
              )}

              {activeTab === 'reviews' && (
                <GameReviews
                  gameId={initialGame.id}
                  gameSlug={slug}
                  showPagination={true}
                />
              )}

              {activeTab === 'performance' && (
                <GamePerformance
                  gameId={initialGame.id}
                  gameSlug={slug}
                />
              )}

              {activeTab === 'community' && (
                <div className="space-y-6">
                  {/* Community Stats */}
                  <Card className="p-6 bg-slate-900/60 border border-slate-700/50 rounded-lg">
                    <h2 className="text-xl font-bold text-white mb-4" style={{ textShadow: '2px 3px 0px rgba(0,0,0,1)' }}>
                      // Community Activity
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[
                        { label: 'Reviews', value: currentStats.total_reviews, icon: MessageSquare, color: 'text-blue-400' },
                        { label: 'Performance Reports', value: currentStats.total_performance_surveys, icon: BarChart3, color: 'text-green-400' },
                        { label: 'Total Views', value: currentStats.total_views, icon: Eye, color: 'text-purple-400' },
                        { label: 'Recent Activity', value: currentStats.recent_activity_count, icon: TrendingUp, color: 'text-orange-400' }
                      ].map((stat, index) => {
                        const Icon = stat.icon;
                        return (
                          <div key={index} className="text-center p-4 bg-slate-800/40 rounded-lg border border-slate-600/30">
                            <Icon className={`w-6 h-6 mx-auto mb-2 ${stat.color}`} />
                            <div className={`text-2xl font-bold ${stat.color}`}>
                              {stat.value.toLocaleString()}
                            </div>
                            <div className="text-sm text-slate-400">{stat.label}</div>
                          </div>
                        );
                      })}
                    </div>
                  </Card>

                  {/* Community Reviews */}
                  <GameReviews
                    gameId={initialGame.id}
                    gameSlug={slug}
                    showPagination={true}
                  />
                </div>
              )}
            </motion.div>

          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Game Discovery */}
            <GameDiscovery
              gameId={initialGame.id}
              gameSlug={slug}
            />

            {/* Where to Buy - Enhanced Design */}
            <Card className="p-6 bg-gradient-to-br from-blue-900/40 to-purple-900/40 border border-blue-500/30 rounded-lg">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <span className="text-blue-400">🛒</span>
                Where to Buy
              </h3>
              <div className="space-y-3">
                {[
                  { platform: 'Steam', price: '$29.99', color: 'text-green-400', discount: null },
                  { platform: 'Epic Games', price: '$27.99', color: 'text-yellow-400', discount: '7% OFF' }
                ].map((store, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center justify-between p-3 bg-slate-800/60 rounded-lg border border-slate-600/50 hover:border-slate-500/50 transition-all duration-200"
                  >
                    <div>
                      <div className="text-sm font-medium text-white">{store.platform}</div>
                      <div className={`text-xs ${store.color} font-semibold`}>
                        {store.price}
                        {store.discount && (
                          <Badge variant="secondary" className="ml-2 text-xs bg-green-900/50 text-green-300">
                            {store.discount}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-500 text-white text-xs"
                    >
                      View
                    </Button>
                  </motion.div>
                ))}
              </div>
            </Card>

            {/* Hardware Recommendations - Enhanced Design */}
            <Card className="p-6 bg-gradient-to-br from-green-900/40 to-emerald-900/40 border border-green-500/30 rounded-lg">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <span className="text-green-400">⚡</span>
                Recommended Hardware
              </h3>
              <div className="space-y-3">
                {[
                  {
                    component: 'GPU: RTX 4060',
                    description: 'Optimal 1080p performance',
                    price: '$299',
                    icon: '🎮'
                  },
                  {
                    component: 'CPU: Ryzen 5 5600X',
                    description: 'Great price/performance',
                    price: '$199',
                    icon: '🔧'
                  }
                ].map((hardware, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="p-3 bg-slate-800/60 rounded-lg border border-slate-600/50 hover:border-green-500/30 transition-all duration-200"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="text-sm font-medium text-white flex items-center gap-2">
                          <span>{hardware.icon}</span>
                          {hardware.component}
                        </div>
                        <div className="text-xs text-slate-400 mt-1">{hardware.description}</div>
                      </div>
                      <div className="text-xs text-green-400 font-semibold">{hardware.price}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </Card>

            {/* Top Contributors - Enhanced Design */}
            <Card className="p-6 bg-gradient-to-br from-purple-900/40 to-pink-900/40 border border-purple-500/30 rounded-lg">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <span className="text-purple-400">⭐</span>
                Top Contributors
              </h3>
              <div className="space-y-3">
                {[
                  {
                    rank: 1,
                    name: 'GamerPro123',
                    contribution: '5 detailed reviews',
                    color: 'bg-gradient-to-r from-yellow-500 to-orange-500'
                  },
                  {
                    rank: 2,
                    name: 'TechReviewer',
                    contribution: '3 performance reports',
                    color: 'bg-gradient-to-r from-gray-400 to-gray-500'
                  },
                  {
                    rank: 3,
                    name: 'GameCritic',
                    contribution: '2 in-depth analyses',
                    color: 'bg-gradient-to-r from-amber-600 to-yellow-600'
                  }
                ].map((contributor, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="flex items-center space-x-3 p-3 bg-slate-800/60 rounded-lg border border-slate-600/50 hover:border-purple-500/30 transition-all duration-200"
                  >
                    <div className={`w-8 h-8 ${contributor.color} rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg`}>
                      {contributor.rank}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-white">{contributor.name}</div>
                      <div className="text-xs text-slate-400">{contributor.contribution}</div>
                    </div>
                    <Heart className="w-4 h-4 text-pink-400" />
                  </motion.div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}