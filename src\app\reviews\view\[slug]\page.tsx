import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getReviewBySlug } from '@/lib/review-service';
import { getReviewBySlugServer } from '@/lib/review-service-server';
import ReviewPageClient from './ReviewPageClient';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Helper function to safely convert timestamps to ISO strings
function toISOString(timestamp: any): string | undefined {
  if (!timestamp) return undefined;

  if (typeof timestamp === 'string') return timestamp;

  if (timestamp instanceof Date) return timestamp.toISOString();

  // Handle Firebase Timestamp-like objects
  if (typeof timestamp === 'object' && 'toDate' in timestamp && typeof timestamp.toDate === 'function') {
    try {
      return timestamp.toDate().toISOString();
    } catch (error) {
      console.error('Error converting timestamp:', error);
      return undefined;
    }
  }

  // Handle objects with seconds/nanoseconds
  if (typeof timestamp === 'object' && 'seconds' in timestamp) {
    try {
      return new Date(timestamp.seconds * 1000).toISOString();
    } catch (error) {
      console.error('Error converting timestamp object:', error);
      return undefined;
    }
  }

  return undefined;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    // Await params in Next.js 15+
    const { slug } = await params;

    // Fetch review data for metadata generation (don't track view)
    const review = await getReviewBySlugServer(slug);

    if (!review) {
      return {
        title: 'Review Not Found - CriticalPixel',
        description: 'The requested review could not be found.',
      };
    }

    // Use stored SEO metadata if available, otherwise generate fallback
    const title = review.metaTitle || `${review.gameName} Review - CriticalPixel`;
    const description = review.metaDescription ||
      `Read our detailed review of ${review.gameName}. ${review.overallScore ? `Rated ${review.overallScore}/100.` : ''} Comprehensive analysis and rating.`;

    const keywords = review.focusKeyword ?
      [review.focusKeyword, review.gameName.toLowerCase(), 'game review', 'gaming'] :
      [review.gameName.toLowerCase(), 'game review', 'gaming'];

    // Generate social media tags
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://critical-pixel.com';
    const reviewUrl = `${baseUrl}/reviews/view/${review.slug}`;
    const imageUrl = review.mainImageUrl || review.igdbCoverUrl || `${baseUrl}/api/og-image/${review.slug}`;

    return {
      title,
      description,
      keywords: keywords.join(', '),
      authors: [{ name: review.authorName || 'CriticalPixel' }],
      openGraph: {
        title: review.metaTitle || `🎮 ${review.gameName} Review${review.overallScore ? ` - ${review.overallScore}/100` : ''}`,
        description: description,
        type: 'article',
        url: reviewUrl,
        siteName: 'CriticalPixel',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${review.gameName} review screenshot`,
          },
        ],
        publishedTime: toISOString(review.publishDate) || toISOString(review.createdAt),
        modifiedTime: toISOString(review.createdAt),
        section: 'Game Reviews',
        tags: [
          review.gameName,
          'game review',
          'gaming',
          ...(review.platforms || []),
          ...(review.genres || []),
          ...(review.tags || [])
        ].slice(0, 10), // Limit to 10 tags
      },
      twitter: {
        card: 'summary_large_image',
        title: review.metaTitle || `🎮 ${review.gameName} Review${review.overallScore ? ` - ${review.overallScore}/100` : ''}`,
        description: description,
        images: [imageUrl],
        site: '@CriticalPixel',
        creator: review.authorName ? `@${review.authorName.replace(/\s+/g, '')}` : '@CriticalPixel',
      },
      alternates: {
        canonical: reviewUrl,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Review - CriticalPixel',
      description: 'Game review on CriticalPixel',
    };
  }
}

// Server component for review pages with SEO metadata generation
export default async function ReviewPage({ params }: PageProps) {
  try {
    // Await params in Next.js 15+
    const { slug } = await params;

    // Fetch review data for server-side rendering
    const review = await getReviewBySlugServer(slug);

    if (!review) {
      // Show 404 page for all cases (private, non-existent, etc.)
      notFound();
    }

    return <ReviewPageClient slug={slug} initialReview={review} />;
  } catch (error) {
    console.error('Error fetching review:', error);
    notFound();
  }
}

