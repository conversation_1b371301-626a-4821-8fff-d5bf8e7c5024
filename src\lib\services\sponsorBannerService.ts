import { createClient } from '@/lib/supabase/client';

/**
 * Get sponsor banner for a user
 * @param userId The user's ID
 * @returns The user's sponsor banner or null if none exists
 */
export async function getUserSponsorBanner(userId: string) {
  if (!userId) {
    console.error('Error fetching sponsor banner: userId is required');
    return null;
  }

  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_sponsor_banners')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      // For PostgreSQL's PGRST116 error (no rows returned), return null silently
      if (error.code === 'PGRST116') {
        return null;
      }
      
      // Only log actual errors, not missing data
      console.error('Error fetching sponsor banner:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
        userId
      });
      
      return null;
    }

    return data;
  } catch (err) {
    console.error('Unexpected error in getUserSponsorBanner:', err);
    return null;
  }
}

/**
 * Save or update a sponsor banner
 * @param params Object containing userId, imgUrl, url, and optional isActive flag
 * @returns The saved sponsor banner data or throws an error
 */
export async function saveSponsorBanner({
  userId,
  imgUrl,
  url,
  isActive = true,
}: {
  userId: string;
  imgUrl: string;
  url: string;
  isActive?: boolean;
}) {
  try {
    if (!userId) {
      console.error('Cannot save sponsor banner: userId is required');
      throw new Error('User ID is required');
    }

    const supabase = createClient();
    
    // Check if user already has a sponsor banner
    // We'll manually check rather than using getUserSponsorBanner to avoid potential errors
    const { data: existing, error: fetchError } = await supabase
      .from('user_sponsor_banners')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record exists

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 means no rows returned
      console.error('Error checking for existing banner:', fetchError);
      throw new Error(`Failed to check for existing banner: ${fetchError.message}`);
    }

    let response;

    if (existing) {
      // Update existing record
      console.log('Updating existing sponsor banner for user:', userId);
      response = await supabase
        .from('user_sponsor_banners')
        .update({
          img_url: imgUrl,
          url: url,
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existing.id)
        .select();
    } else {
      // Create new record
      console.log('Creating new sponsor banner for user:', userId);
      response = await supabase
        .from('user_sponsor_banners')
        .insert({
          user_id: userId,
          img_url: imgUrl,
          url: url,
          is_active: isActive,
        })
        .select();
    }

    const { data, error } = response;

    if (error) {
      console.error('Error saving sponsor banner:', error);
      throw new Error(error.message);
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned after saving sponsor banner');
    }

    console.log('Successfully saved sponsor banner:', data[0]);
    return data[0];
  } catch (error) {
    console.error('Error in saveSponsorBanner:', error);
    throw error;
  }
}

/**
 * Deactivate a sponsor banner
 * @param userId The user's ID
 * @returns The updated sponsor banner data or throws an error
 */
export async function deactivateSponsorBanner(userId: string) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('user_sponsor_banners')
    .update({
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .select();

  if (error) {
    console.error('Error deactivating sponsor banner:', error);
    throw new Error(error.message);
  }

  return data[0];
}

/**
 * Completely delete a sponsor banner and all its analytics data
 * @param userId The user's ID
 * @returns True if successful or throws an error
 */
export async function deleteSponsorBanner(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // First get the banner to get its ID for analytics deletion
    const banner = await getUserSponsorBanner(userId);
    if (!banner) {
      return true; // Already deleted
    }

    // Delete analytics data first (foreign key constraint)
    if (banner.id) {
      const { error: analyticsError } = await supabase
        .from('sponsor_banner_analytics')
        .delete()
        .eq('banner_id', banner.id);

      if (analyticsError) {
        console.error('Error deleting sponsor banner analytics:', analyticsError);
        throw new Error(analyticsError.message);
      }
    }

    // Then delete the banner itself
    const { error: bannerError } = await supabase
      .from('user_sponsor_banners')
      .delete()
      .eq('user_id', userId);

    if (bannerError) {
      console.error('Error deleting sponsor banner:', bannerError);
      throw new Error(bannerError.message);
    }

    return true;
  } catch (error) {
    console.error('Error in deleteSponsorBanner:', error);
    throw error;
  }
}

/**
 * Track impression when banner is viewed
 * @param bannerId The banner's ID
 * @param userAgent Optional user agent string
 * @param referrer Optional referrer URL
 * @returns true if successful, false otherwise
 */
export async function trackSponsorImpression(
  bannerId: string,
  userAgent?: string,
  referrer?: string
) {
  const supabase = createClient();

  const { data, error } = await supabase.rpc('increment_sponsor_impression', {
    banner_id: bannerId,
    user_agent_param: userAgent || null,
    ip_address_param: null, // IP tracking would need server-side implementation
    referrer_param: referrer || null
  });

  if (error) {
    console.error('Error tracking impression:', error);
    return false;
  }

  return true;
}

/**
 * Track click when banner link is clicked
 * @param bannerId The banner's ID
 * @param userAgent Optional user agent string
 * @param referrer Optional referrer URL
 * @returns true if successful, false otherwise
 */
export async function trackSponsorClick(
  bannerId: string,
  userAgent?: string,
  referrer?: string
) {
  const supabase = createClient();

  const { data, error } = await supabase.rpc('increment_sponsor_click', {
    banner_id: bannerId,
    user_agent_param: userAgent || null,
    ip_address_param: null, // IP tracking would need server-side implementation
    referrer_param: referrer || null
  });

  if (error) {
    console.error('Error tracking click:', error);
    return false;
  }

  return true;
}
