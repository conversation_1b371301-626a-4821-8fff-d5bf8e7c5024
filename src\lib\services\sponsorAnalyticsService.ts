import { createClient } from '@/lib/supabase/client';

// Types for analytics data
export interface SponsorBannerSummary {
  total_impressions: number;
  total_clicks: number;
  ctr: string;
  last_impression: string | null;
  last_click: string | null;
  created_at: string;
  days_active: number;
}

export interface DailyStats {
  date: string;
  impressions: number;
  clicks: number;
  ctr: string;
}

export interface AnalyticsEvent {
  id: string;
  banner_id: string;
  event_type: 'impression' | 'click';
  user_agent: string | null;
  ip_address: string | null;
  referrer: string | null;
  created_at: string;
}

/**
 * Get comprehensive analytics summary for a sponsor banner
 * @param bannerId The banner's ID
 * @returns Summary analytics data or null if not found
 */
export async function getSponsorBannerSummary(bannerId: string): Promise<SponsorBannerSummary | null> {
  const supabase = createClient();
  
  try {
    console.log('Fetching sponsor banner summary for banner:', bannerId);
    
    const { data, error } = await supabase.rpc('get_sponsor_banner_summary', {
      banner_id: bannerId
    });

    if (error) {
      console.error('Error fetching sponsor banner summary:', error);
      return null;
    }

    if (!data || data.length === 0) {
      console.log('No summary data found for banner:', bannerId);
      return null;
    }

    return data[0] as SponsorBannerSummary;
  } catch (error) {
    console.error('Error in getSponsorBannerSummary:', error);
    return null;
  }
}

/**
 * Get daily analytics stats for a sponsor banner
 * @param bannerId The banner's ID
 * @param daysBack Number of days to look back (default: 30)
 * @returns Array of daily stats
 */
export async function getSponsorBannerDailyStats(
  bannerId: string, 
  daysBack: number = 30
): Promise<DailyStats[]> {
  const supabase = createClient();
  
  try {
    console.log('Fetching daily stats for banner:', bannerId, 'days back:', daysBack);
    
    const { data, error } = await supabase.rpc('get_sponsor_banner_daily_stats', {
      banner_id: bannerId,
      days_back: daysBack
    });

    if (error) {
      console.error('Error fetching daily stats:', error);
      return [];
    }

    return (data || []) as DailyStats[];
  } catch (error) {
    console.error('Error in getSponsorBannerDailyStats:', error);
    return [];
  }
}

/**
 * Get recent analytics events for a sponsor banner
 * @param bannerId The banner's ID
 * @param limit Number of events to fetch (default: 100)
 * @returns Array of analytics events
 */
export async function getRecentAnalyticsEvents(
  bannerId: string, 
  limit: number = 100
): Promise<AnalyticsEvent[]> {
  const supabase = createClient();
  
  try {
    console.log('Fetching recent analytics events for banner:', bannerId);
    
    const { data, error } = await supabase
      .from('sponsor_banner_analytics')
      .select('*')
      .eq('banner_id', bannerId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching analytics events:', error);
      return [];
    }

    return (data || []) as AnalyticsEvent[];
  } catch (error) {
    console.error('Error in getRecentAnalyticsEvents:', error);
    return [];
  }
}

/**
 * Get analytics data aggregated by time period
 * @param bannerId The banner's ID
 * @param period 'hour', 'day', 'week', or 'month'
 * @param limit Number of periods to fetch
 * @returns Aggregated analytics data
 */
export async function getAnalyticsByPeriod(
  bannerId: string,
  period: 'hour' | 'day' | 'week' | 'month' = 'day',
  limit: number = 30
) {
  const supabase = createClient();
  
  try {
    let dateFormat: string;
    let interval: string;
    
    switch (period) {
      case 'hour':
        dateFormat = 'YYYY-MM-DD HH24:00:00';
        interval = '1 hour';
        break;
      case 'week':
        dateFormat = 'YYYY-"W"WW';
        interval = '1 week';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        interval = '1 month';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        interval = '1 day';
    }

    const query = `
      SELECT 
        TO_CHAR(created_at, '${dateFormat}') as period,
        COUNT(CASE WHEN event_type = 'impression' THEN 1 END) as impressions,
        COUNT(CASE WHEN event_type = 'click' THEN 1 END) as clicks,
        CASE 
          WHEN COUNT(CASE WHEN event_type = 'impression' THEN 1 END) > 0 
          THEN ROUND(
            (COUNT(CASE WHEN event_type = 'click' THEN 1 END)::NUMERIC / 
             COUNT(CASE WHEN event_type = 'impression' THEN 1 END)::NUMERIC) * 100, 
            2
          )
          ELSE 0
        END as ctr
      FROM sponsor_banner_analytics
      WHERE banner_id = '${bannerId}'
        AND created_at >= NOW() - INTERVAL '${limit} ${interval.split(' ')[1]}'
      GROUP BY TO_CHAR(created_at, '${dateFormat}')
      ORDER BY period DESC
      LIMIT ${limit}
    `;

    const { data, error } = await supabase.rpc('exec_sql', { sql: query });

    if (error) {
      console.error('Error fetching analytics by period:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAnalyticsByPeriod:', error);
    return [];
  }
}

/**
 * Calculate performance metrics for a sponsor banner
 * @param bannerId The banner's ID
 * @returns Performance metrics
 */
export async function getPerformanceMetrics(bannerId: string) {
  try {
    const [summary, dailyStats] = await Promise.all([
      getSponsorBannerSummary(bannerId),
      getSponsorBannerDailyStats(bannerId, 30) // Last 30 days to match content banner
    ]);

    if (!summary) {
      return null;
    }

    // Calculate trends (comparing last 7 days vs previous 7 days)
    const last7Days = dailyStats.slice(-7);
    const previous7Days = dailyStats.slice(-14, -7);

    const last7Impressions = last7Days.reduce((sum, day) => sum + day.impressions, 0);
    const previous7Impressions = previous7Days.reduce((sum, day) => sum + day.impressions, 0);
    const impressionTrend = previous7Impressions > 0
      ? ((last7Impressions - previous7Impressions) / previous7Impressions) * 100
      : 0;

    const last7Clicks = last7Days.reduce((sum, day) => sum + day.clicks, 0);
    const previous7Clicks = previous7Days.reduce((sum, day) => sum + day.clicks, 0);
    const clickTrend = previous7Clicks > 0
      ? ((last7Clicks - previous7Clicks) / previous7Clicks) * 100
      : 0;

    const last7CTR = last7Impressions > 0 ? (last7Clicks / last7Impressions) * 100 : 0;
    const previous7CTR = previous7Impressions > 0 ? (previous7Clicks / previous7Impressions) * 100 : 0;
    const ctrTrend = previous7CTR > 0 ? ((last7CTR - previous7CTR) / previous7CTR) * 100 : 0;

    // Calculate averages
    const avgDailyImpressions = dailyStats.length > 0
      ? dailyStats.reduce((sum, day) => sum + day.impressions, 0) / dailyStats.length
      : 0;
    const avgDailyClicks = dailyStats.length > 0
      ? dailyStats.reduce((sum, day) => sum + day.clicks, 0) / dailyStats.length
      : 0;

    return {
      ...summary,
      impressionTrend,
      clickTrend,
      ctrTrend,
      avgDailyImpressions,
      avgDailyClicks
    };
  } catch (error) {
    console.error('Error calculating performance metrics:', error);
    return null;
  }
}

/**
 * Export analytics data to CSV format
 * @param bannerId The banner's ID
 * @param daysBack Number of days to include
 * @returns CSV string
 */
export async function exportAnalyticsToCSV(bannerId: string, daysBack: number = 30): Promise<string> {
  try {
    const dailyStats = await getSponsorBannerDailyStats(bannerId, daysBack);
    
    const headers = ['Date', 'Impressions', 'Clicks', 'CTR (%)'];
    const csvRows = [headers.join(',')];
    
    dailyStats.forEach(stat => {
      const row = [
        stat.date,
        stat.impressions.toString(),
        stat.clicks.toString(),
        stat.ctr.toString()
      ];
      csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
  } catch (error) {
    console.error('Error exporting analytics to CSV:', error);
    return '';
  }
}
