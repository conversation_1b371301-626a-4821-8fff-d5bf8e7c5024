import React from 'react';

interface IconProps {
  className?: string;
  [key: string]: any;
}

const G2AIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 32 32"
    fill="currentColor"
    {...props}
  >
    <title>G2A</title>
    {/* Rectangular background to match other store icons */}
    <rect width="32" height="32" fill="#f05f00" rx="6"/>
    <text x="16" y="20" textAnchor="middle" fill="white" fontSize="10" fontFamily="Arial, sans-serif" fontWeight="bold">
      G2A
    </text>
  </svg>
);

export default G2AIcon;