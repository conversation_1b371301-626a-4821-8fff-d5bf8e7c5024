# 🎮 TWITCH API ENDPOINTS REFERENCE
**CriticalPixel - Twitch Integration API Documentation**

*Document Version: 1.0*  
*Created: January 16, 2025*  
*Author: AI Development Assistant*

---

## 📋 OVERVIEW

This document provides comprehensive reference for Twitch API endpoints used in the CriticalPixel Twitch integration module. All endpoints follow the Twitch Helix API specification and require proper OAuth 2.0 authentication.

### 🔗 Base URLs
- **Twitch API**: `https://api.twitch.tv/helix`
- **OAuth**: `https://id.twitch.tv/oauth2`

### 🔑 Authentication Requirements
All API calls require:
- `Authorization: Bearer {access_token}`
- `Client-Id: {your_client_id}`

---

## 🔐 OAUTH 2.0 ENDPOINTS

### 1. Authorization URL
**Endpoint**: `GET https://id.twitch.tv/oauth2/authorize`

**Purpose**: Redirect users to Twitch for OAuth authorization

**Query Parameters**:
```typescript
interface AuthorizeParams {
  client_id: string;           // Your Twitch application client ID
  redirect_uri: string;        // Registered redirect URI
  response_type: 'code';       // OAuth flow type
  scope: string;              // Space-separated scopes
  state: string;              // CSRF protection token
  force_verify?: boolean;     // Force re-authorization
}
```

**Example**:
```bash
GET https://id.twitch.tv/oauth2/authorize?
  client_id=your_client_id&
  redirect_uri=https://yourapp.com/auth/twitch/callback&
  response_type=code&
  scope=user:read:email+clips:read&
  state=random_state_string
```

**Response**: Redirects to your `redirect_uri` with authorization code

---

### 2. Token Exchange
**Endpoint**: `POST https://id.twitch.tv/oauth2/token`

**Purpose**: Exchange authorization code for access/refresh tokens

**Headers**:
```
Content-Type: application/x-www-form-urlencoded
```

**Body Parameters**:
```typescript
interface TokenExchangeBody {
  client_id: string;
  client_secret: string;
  code: string;                // Authorization code from callback
  grant_type: 'authorization_code';
  redirect_uri: string;
}
```

**Example Request**:
```bash
POST https://id.twitch.tv/oauth2/token
Content-Type: application/x-www-form-urlencoded

client_id=your_client_id&
client_secret=your_client_secret&
code=authorization_code_here&
grant_type=authorization_code&
redirect_uri=https://yourapp.com/auth/twitch/callback
```

**Response**:
```typescript
interface TokenResponse {
  access_token: string;
  expires_in: number;          // Seconds until expiration
  refresh_token: string;
  scope: string[];
  token_type: 'bearer';
}
```

---

### 3. Token Refresh
**Endpoint**: `POST https://id.twitch.tv/oauth2/token`

**Purpose**: Refresh expired access token

**Body Parameters**:
```typescript
interface TokenRefreshBody {
  client_id: string;
  client_secret: string;
  grant_type: 'refresh_token';
  refresh_token: string;
}
```

**Example Request**:
```bash
POST https://id.twitch.tv/oauth2/token
Content-Type: application/x-www-form-urlencoded

client_id=your_client_id&
client_secret=your_client_secret&
grant_type=refresh_token&
refresh_token=your_refresh_token
```

**Response**: Same as Token Exchange response

---

### 4. Token Validation
**Endpoint**: `GET https://id.twitch.tv/oauth2/validate`

**Purpose**: Validate current access token

**Headers**:
```
Authorization: Bearer {access_token}
```

**Example Request**:
```bash
GET https://id.twitch.tv/oauth2/validate
Authorization: Bearer your_access_token
```

**Response**:
```typescript
interface ValidationResponse {
  client_id: string;
  login: string;              // Username
  scopes: string[];
  user_id: string;
  expires_in: number;         // Seconds remaining
}
```

---

## 👤 USER DATA ENDPOINTS

### 1. Get Users
**Endpoint**: `GET https://api.twitch.tv/helix/users`

**Purpose**: Get user information for authenticated user or specified users

**Headers**:
```
Authorization: Bearer {user_access_token}
Client-Id: {your_client_id}
```

**Query Parameters**:
```typescript
interface GetUsersParams {
  id?: string;               // User ID(s) - comma separated
  login?: string;            // Username(s) - comma separated
}
```

**Example Request**:
```bash
GET https://api.twitch.tv/helix/users
Authorization: Bearer user_access_token
Client-Id: your_client_id
```

**Response**:
```typescript
interface UsersResponse {
  data: Array<{
    id: string;
    login: string;
    display_name: string;
    type: string;
    broadcaster_type: 'partner' | 'affiliate' | '';
    description: string;
    profile_image_url: string;
    offline_image_url: string;
    view_count: number;
    email?: string;            // Only if email scope granted
    created_at: string;        // ISO 8601 timestamp
  }>;
}
```

**Rate Limit**: 800 requests per minute

---

## 🎬 CLIPS ENDPOINTS

### 1. Get Clips
**Endpoint**: `GET https://api.twitch.tv/helix/clips`

**Purpose**: Get clips for a broadcaster, game, or clip IDs

**Headers**:
```
Authorization: Bearer {app_access_token_or_user_access_token}
Client-Id: {your_client_id}
```

**Query Parameters**:
```typescript
interface GetClipsParams {
  broadcaster_id?: string;   // Get clips for this broadcaster
  game_id?: string;         // Get clips for this game
  id?: string;              // Clip ID(s) - comma separated
  started_at?: string;      // RFC3339 timestamp
  ended_at?: string;        // RFC3339 timestamp
  first?: number;           // Number of clips (max 100, default 20)
  before?: string;          // Pagination cursor
  after?: string;           // Pagination cursor
}
```

**Example Request**:
```bash
GET https://api.twitch.tv/helix/clips?broadcaster_id=123456789&first=20&started_at=2024-01-01T00:00:00Z
Authorization: Bearer access_token
Client-Id: your_client_id
```

**Response**:
```typescript
interface ClipsResponse {
  data: Array<{
    id: string;
    url: string;
    embed_url: string;
    broadcaster_id: string;
    broadcaster_name: string;
    creator_id: string;
    creator_name: string;
    video_id: string;
    game_id: string;
    language: string;
    title: string;
    view_count: number;
    created_at: string;       // RFC3339 timestamp
    thumbnail_url: string;
    duration: number;         // Seconds
    vod_offset: number;       // Seconds into VOD
  }>;
  pagination?: {
    cursor?: string;
  };
}
```

**Rate Limit**: 800 requests per minute

---

### 2. Create Clip
**Endpoint**: `POST https://api.twitch.tv/helix/clips`

**Purpose**: Create a clip from a live stream

**Headers**:
```
Authorization: Bearer {user_access_token}
Client-Id: {your_client_id}
```

**Query Parameters**:
```typescript
interface CreateClipParams {
  broadcaster_id: string;    // Required
  has_delay?: boolean;       // Default false
}
```

**Response**:
```typescript
interface CreateClipResponse {
  data: Array<{
    id: string;
    edit_url: string;        // URL to edit the clip
  }>;
}
```

**Required Scope**: `clips:edit`
**Rate Limit**: 800 requests per minute

---

## 📺 STREAMS ENDPOINTS

### 1. Get Streams
**Endpoint**: `GET https://api.twitch.tv/helix/streams`

**Purpose**: Get live stream information

**Headers**:
```
Authorization: Bearer {app_access_token_or_user_access_token}
Client-Id: {your_client_id}
```

**Query Parameters**:
```typescript
interface GetStreamsParams {
  user_id?: string;         // User ID(s) - comma separated
  user_login?: string;      // Username(s) - comma separated
  game_id?: string;         // Game ID(s) - comma separated
  type?: 'live';           // Stream type (only 'live' supported)
  language?: string;        // Language code(s) - comma separated
  first?: number;           // Number of streams (max 100, default 20)
  before?: string;          // Pagination cursor
  after?: string;           // Pagination cursor
}
```

**Example Request**:
```bash
GET https://api.twitch.tv/helix/streams?user_id=123456789
Authorization: Bearer access_token
Client-Id: your_client_id
```

**Response**:
```typescript
interface StreamsResponse {
  data: Array<{
    id: string;
    user_id: string;
    user_login: string;
    user_name: string;
    game_id: string;
    game_name: string;
    type: 'live';
    title: string;
    viewer_count: number;
    started_at: string;       // RFC3339 timestamp
    language: string;
    thumbnail_url: string;    // Template URL with {width}x{height}
    tag_ids: string[];        // Deprecated
    tags: string[];
    is_mature: boolean;
  }>;
  pagination?: {
    cursor?: string;
  };
}
```

**Rate Limit**: 800 requests per minute

---

## 🎮 GAMES ENDPOINTS

### 1. Get Games
**Endpoint**: `GET https://api.twitch.tv/helix/games`

**Purpose**: Get game information by ID or name

**Headers**:
```
Authorization: Bearer {app_access_token_or_user_access_token}
Client-Id: {your_client_id}
```

**Query Parameters**:
```typescript
interface GetGamesParams {
  id?: string;              // Game ID(s) - comma separated
  name?: string;            // Game name(s) - comma separated
  igdb_id?: string;         // IGDB ID(s) - comma separated
}
```

**Example Request**:
```bash
GET https://api.twitch.tv/helix/games?name=Fortnite
Authorization: Bearer access_token
Client-Id: your_client_id
```

**Response**:
```typescript
interface GamesResponse {
  data: Array<{
    id: string;
    name: string;
    box_art_url: string;      // Template URL with {width}x{height}
    igdb_id: string;
  }>;
}
```

**Rate Limit**: 800 requests per minute

---

## 📊 ANALYTICS ENDPOINTS

### 1. Get Extension Analytics
**Endpoint**: `GET https://api.twitch.tv/helix/analytics/extensions`

**Purpose**: Get analytics data for extensions (not applicable for this integration)

### 2. Get Game Analytics
**Endpoint**: `GET https://api.twitch.tv/helix/analytics/games`

**Purpose**: Get analytics data for games (not applicable for this integration)

---

## 🔧 WEBHOOK ENDPOINTS

### 1. Get Webhook Subscriptions
**Endpoint**: `GET https://api.twitch.tv/helix/webhooks/subscriptions`

**Purpose**: Get current webhook subscriptions (deprecated - use EventSub instead)

**Note**: Webhooks are deprecated. Use EventSub for real-time events.

---

## 🎯 EVENTSUB ENDPOINTS

### 1. Create EventSub Subscription
**Endpoint**: `POST https://api.twitch.tv/helix/eventsub/subscriptions`

**Purpose**: Subscribe to real-time events (advanced feature)

**Example for Stream Online/Offline**:
```typescript
interface StreamOnlineSubscription {
  type: 'stream.online';
  version: '1';
  condition: {
    broadcaster_user_id: string;
  };
  transport: {
    method: 'webhook';
    callback: string;
    secret: string;
  };
}
```

**Required for Real-time Stream Status**: Consider implementing for production

---

## 📋 RATE LIMITING

### General Rate Limits
- **Default**: 800 requests per minute
- **Per User**: Shared across all endpoints
- **Per App**: Shared across all users

### Rate Limit Headers
```
Ratelimit-Helixratelimit-Limit: 800
Ratelimit-Helixratelimit-Remaining: 799
Ratelimit-Helixratelimit-Reset: 1642781400
```

### Rate Limit Handling
```typescript
const handleRateLimit = async (response: Response) => {
  if (response.status === 429) {
    const resetTime = response.headers.get('Ratelimit-Helixratelimit-Reset');
    const waitTime = parseInt(resetTime!) * 1000 - Date.now();
    await new Promise(resolve => setTimeout(resolve, waitTime));
    // Retry request
  }
};
```

---

## 🔍 ERROR HANDLING

### Common Error Codes
```typescript
interface TwitchErrorResponse {
  error: string;
  status: number;
  message: string;
}
```

**Common Errors**:
- `400 Bad Request`: Invalid parameters
- `401 Unauthorized`: Invalid or expired token
- `403 Forbidden`: Missing required scope
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Twitch server error

### Error Handling Strategy
```typescript
const handleTwitchError = (error: TwitchErrorResponse) => {
  switch (error.status) {
    case 401:
      // Refresh token or re-authenticate
      break;
    case 403:
      // Check required scopes
      break;
    case 429:
      // Implement backoff strategy
      break;
    case 500:
      // Retry with exponential backoff
      break;
    default:
      // Log error and provide user feedback
      break;
  }
};
```

---

## 🛠️ IMPLEMENTATION EXAMPLES

### OAuth Flow Implementation
```typescript
// Step 1: Redirect to authorization
const authUrl = `https://id.twitch.tv/oauth2/authorize?${new URLSearchParams({
  client_id: process.env.TWITCH_CLIENT_ID!,
  redirect_uri: process.env.TWITCH_REDIRECT_URI!,
  response_type: 'code',
  scope: 'user:read:email clips:read',
  state: generateRandomState(),
})}`;

// Step 2: Handle callback
const exchangeCodeForTokens = async (code: string) => {
  const response = await fetch('https://id.twitch.tv/oauth2/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id: process.env.TWITCH_CLIENT_ID!,
      client_secret: process.env.TWITCH_CLIENT_SECRET!,
      code,
      grant_type: 'authorization_code',
      redirect_uri: process.env.TWITCH_REDIRECT_URI!,
    }),
  });
  
  return response.json();
};
```

### API Call with Error Handling
```typescript
const makeApiCall = async (endpoint: string, token: string) => {
  try {
    const response = await fetch(`https://api.twitch.tv/helix${endpoint}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Client-Id': process.env.TWITCH_CLIENT_ID!,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Twitch API Error: ${error.message}`);
    }

    return response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};
```

### Stream Status Check
```typescript
const checkStreamStatus = async (userId: string, token: string) => {
  const response = await makeApiCall(`/streams?user_id=${userId}`, token);
  
  return {
    isLive: response.data.length > 0,
    stream: response.data[0] || null,
  };
};
```

### Fetch User Clips
```typescript
const getUserClips = async (broadcasterId: string, token: string, limit = 20) => {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
  
  const response = await makeApiCall(
    `/clips?broadcaster_id=${broadcasterId}&first=${limit}&started_at=${thirtyDaysAgo}`,
    token
  );
  
  return response.data;
};
```

---

## 🔒 SECURITY BEST PRACTICES

### Token Management
- **Storage**: Store tokens encrypted in database
- **Transmission**: Always use HTTPS
- **Validation**: Validate tokens before each API call
- **Refresh**: Implement automatic token refresh
- **Revocation**: Revoke tokens on disconnection

### Scope Management
- **Minimal Scopes**: Request only necessary permissions
- **Scope Validation**: Verify granted scopes match requirements
- **User Consent**: Clear explanation of data access

### Rate Limit Compliance
- **Caching**: Cache responses to reduce API calls
- **Batching**: Combine multiple requests when possible
- **Backoff**: Implement exponential backoff for retries
- **Monitoring**: Track API usage patterns

---

## 📚 USEFUL RESOURCES

### Official Documentation
- [Twitch API Reference](https://dev.twitch.tv/docs/api/reference/)
- [Authentication Guide](https://dev.twitch.tv/docs/authentication/)
- [Getting Started](https://dev.twitch.tv/docs/api/guide/)

### Tools & Testing
- [Twitch Developer Console](https://dev.twitch.tv/console)
- [API Reference (Interactive)](https://dev.twitch.tv/docs/api/reference/)
- [Postman Collection](https://documenter.getpostman.com/view/8854915/Szf26WHn)

### Community Resources
- [Twitch Developer Forums](https://discuss.dev.twitch.tv/)
- [TwitchDev Discord](https://discord.gg/twitchdev)
- [GitHub Examples](https://github.com/twitchdev)

---

*This API reference provides the foundation for implementing Twitch integration. Always refer to the official Twitch API documentation for the most up-to-date information.*