// src/app/api/igdb/game/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { fetchIGDBData } from '@/lib/igdb-api';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Invalid game ID' },
        { status: 400 }
      );
    }

    // Construct a detailed IGDB API query for a specific game
    const query = `
      where id = ${id};
      fields name, id, cover.url, first_release_date, 
             platforms.name, genres.name, 
             involved_companies.company.name, involved_companies.developer, involved_companies.publisher,
             rating, summary, total_rating, aggregated_rating, aggregated_rating_count,
             screenshots.url, game_engines.name, player_perspectives.name, videos.video_id;
      limit 1;
    `;

    // Fetch detailed game data
    const data = await fetchIGDBData('games', query);

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const game = data[0];

    // Helper function to convert protocol-relative URLs to absolute HTTPS URLs
    const normalizeImageUrl = (url: string) => {
      if (!url) return url;
      if (url.startsWith('//')) {
        return `https:${url}`;
      }
      return url;
    };

    // Transform the data to match your frontend interface
    const transformedGame = {
      id: game.id,
      name: game.name,
      cover: game.cover ? {
        id: game.cover.id,
        url: normalizeImageUrl(game.cover.url)
      } : undefined,
      releaseDate: game.first_release_date,
      first_release_date: game.first_release_date,
      platforms: game.platforms ? game.platforms.map((p: any) => p.name) : [],
      genres: game.genres ? game.genres.map((g: any) => g.name) : [],
      involved_companies: game.involved_companies ? game.involved_companies.map((ic: any) => ({
        id: ic.id,
        company: {
          id: ic.company.id,
          name: ic.company.name
        },
        developer: ic.developer,
        publisher: ic.publisher
      })) : [],
      developers: game.involved_companies ? game.involved_companies
        .filter((ic: any) => ic.developer)
        .map((ic: any) => ({
          id: ic.company.id,
          name: ic.company.name
        })) : [],
      rating: game.rating,
      total_rating: game.total_rating,
      summary: game.summary,
      aggregated_rating: game.aggregated_rating,
      aggregated_rating_count: game.aggregated_rating_count,
      // time_to_beat: game.time_to_beat ? {
      //   normally: game.time_to_beat.normally,
      //   completely: game.time_to_beat.completely
      // } : undefined,
      screenshots: game.screenshots ? game.screenshots.map((s: any) => ({
        id: s.id,
        url: normalizeImageUrl(s.url)
      })) : [],
      game_engines: game.game_engines ? game.game_engines.map((ge: any) => ge.name) : [],
      player_perspectives: game.player_perspectives ? game.player_perspectives.map((pp: any) => pp.name) : [],
      videos: game.videos ? game.videos.map((v: any) => ({ id: v.id, video_id: v.video_id })) : [],
    };

    return NextResponse.json(transformedGame);
  } catch (error: any) {
    console.error('[API /api/igdb/game/[id]] IGDB API error:', error); // Server-side log
    return NextResponse.json(
      { error: 'Failed to fetch game details from IGDB.', details: error.message || 'No further details' },
      { status: 500 }
    );
  }
}
