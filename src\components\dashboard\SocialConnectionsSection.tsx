'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle,
  Users,
  Shield,
  Zap,
  ChevronDown,
  ChevronUp,
  Share2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import DiscordIcon from '@/components/ui/icons/socials/DiscordIcon';
import TwitterIcon from '@/components/ui/icons/socials/TwitterIcon';
import TwitchIcon from '@/components/ui/icons/socials/TwitchIcon';
import YouTubeIcon from '@/components/ui/icons/socials/YouTubeIcon';
import FacebookIcon from '@/components/ui/icons/socials/FacebookIcon';
import InstagramIcon from '@/components/ui/icons/socials/InstagramIcon';
import TikTokIcon from '@/components/ui/icons/socials/TikTokIcon';

// Social Platform Icons
const DiscordIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <DiscordIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const TwitterIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <TwitterIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const TwitchIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <TwitchIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const YouTubeIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <YouTubeIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const FacebookIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <FacebookIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const InstagramIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <InstagramIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const TikTokIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <TikTokIcon className="w-5 h-5 text-slate-300" />
  </div>
);

interface SocialConnection {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  connected: boolean;
  apiAvailable: boolean;
}

const socialConnections: SocialConnection[] = [
  {
    id: 'discord',
    name: 'Discord',
    description: 'Rich presence and server integration',
    icon: <DiscordIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'youtube',
    name: 'YouTube',
    description: 'Gaming content and channel sync',
    icon: <YouTubeIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    description: 'Share reviews and gaming updates',
    icon: <TwitterIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'twitch',
    name: 'Twitch',
    description: 'Streaming and viewer analytics',
    icon: <TwitchIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'facebook',
    name: 'Facebook',
    description: 'Gaming groups and community',
    icon: <FacebookIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'instagram',
    name: 'Instagram',
    description: 'Gaming photos and stories',
    icon: <InstagramIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    description: 'Gaming content and viral videos',
    icon: <TikTokIconWrapper />,
    connected: false,
    apiAvailable: true
  }
];

interface SocialConnectionCardProps {
  connection: SocialConnection;
  onToggle: (id: string, connected: boolean) => void;
}

const SocialConnectionCard: React.FC<SocialConnectionCardProps> = ({ connection, onToggle }) => {
  const { toast } = useToast();

  const handleConnect = () => {
    if (!connection.apiAvailable) {
      toast({
        title: "API Not Available",
        description: `${connection.name} API integration is not currently available.`,
        variant: "destructive"
      });
      return;
    }

    // Mock connection logic
    toast({
      title: connection.connected ? "Disconnected" : "Connected",
      description: `${connection.name} has been ${connection.connected ? 'disconnected' : 'connected'} successfully.`,
    });
    
    onToggle(connection.id, !connection.connected);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-cyan-500/30 transition-all duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {connection.icon}
          <div className="flex-1">
            <h4 className="font-mono text-sm font-semibold text-white">
              {connection.name}
            </h4>
            <p className="text-xs text-gray-400 font-['Lato'] mt-1">
              {connection.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {connection.connected ? (
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs font-mono uppercase tracking-wide">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="border-gray-600 text-gray-400 text-xs font-mono uppercase tracking-wide">
              <XCircle className="w-3 h-3 mr-1" />
              Disconnected
            </Badge>
          )}
          
          <Button
            onClick={handleConnect}
            variant={connection.connected ? "outline" : "default"}
            size="sm"
            disabled={!connection.apiAvailable}
            className={`font-mono text-xs uppercase tracking-wide font-semibold ${
              connection.connected 
                ? "border-red-500/50 text-red-400 hover:bg-red-500/10" 
                : "bg-cyan-600 hover:bg-cyan-700 text-white"
            }`}
          >
            {connection.connected ? "Disconnect" : "Connect"}
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

interface SocialConnectionsSectionProps {
  userId: string;
}

export default function SocialConnectionsSection({ userId }: SocialConnectionsSectionProps) {
  const [userConnections, setUserConnections] = useState(socialConnections);
  const [isExpanded, setIsExpanded] = useState(true);

  const handleToggle = (id: string, connected: boolean) => {
    setUserConnections(prev => prev.map(conn => 
      conn.id === id ? { ...conn, connected } : conn
    ));
  };

  const totalConnected = userConnections.filter(c => c.connected).length;

  return (
    <div className="space-y-6">
      {/* Social Platform Connections Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-cyan-400 mr-1">//</span>
                Social Platform Connections
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1 font-mono">
                Connect your social platforms to share content and build community
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-6">
                {/* Social Platforms List */}
                <div className="space-y-3">
                  {userConnections.map((connection) => (
                    <SocialConnectionCard
                      key={connection.id}
                      connection={connection}
                      onToggle={handleToggle}
                    />
                  ))}
                </div>

              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
}