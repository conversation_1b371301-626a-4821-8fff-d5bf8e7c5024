# Phase 6: Search Engine Implementation Guide
## CriticalPixel Comprehensive Search System

### 📊 **Implementation Overview**

**Objective:** Implement a state-of-the-art search engine supporting games, reviews, users, and hardware with advanced filtering capabilities using PostgreSQL Full-Text Search via Supabase.

**Duration:** 3-4 days  
**Priority:** HIGH  
**Dependencies:** Phase 1 (Database Schema), Phase 2 (Review System Core)

### 🎯 **Strategic Approach**

This implementation leverages **PostgreSQL Full-Text Search (FTS)** over alternatives like Elasticsearch or Algolia because:

- ✅ **Zero additional infrastructure costs** (vs $200+/month for external services)
- ✅ **Real-time data consistency** with existing Supabase RLS policies
- ✅ **6-16ms query performance** for gaming content with proper indexing
- ✅ **Built-in search ranking** and relevance scoring
- ✅ **Seamless integration** with existing search optimization infrastructure

### 🏗️ **Architecture Overview**

```mermaid
graph TD
    A[Search Request] --> B[Unified Search API]
    B --> C{Search Type}
    C -->|Games| D[IGDB Search API]
    C -->|Reviews| E[PostgreSQL FTS]
    C -->|Users| F[PostgreSQL FTS]
    C -->|Hardware| G[Hardware Search API]
    
    E --> H[Reviews Search Vector]
    F --> I[Profiles Search Vector]
    
    D --> J[Search Optimizer]
    E --> J
    F --> J
    G --> J
    
    J --> K[Cached Results]
    J --> L[Real-time Results]
    
    K --> M[Search Response]
    L --> M
    
    M --> N[Advanced Filter UI]
    N --> O[Mixed Filter Results]
```

### 📋 **Current State Analysis**

**✅ Existing Infrastructure:**
- IGDB game search: Production-ready (`/src/app/api/igdb/search/route.ts`)
- Hardware search: Complete CPU/GPU implementation
- Search optimization: Enterprise-level patterns (`/src/lib/utils/searchOptimization.ts`)

**❌ Missing Components:**
- Review search frontend (backend exists in `review-service.ts`)
- User search functionality
- Global site search
- Advanced filtering with "mix and match" capabilities

### 🚀 **Step-by-Step Implementation**

---

## **Step 6.1: PostgreSQL FTS Database Schema Implementation**

### **6.1.1: Add Search Vectors to Existing Tables**

**File:** Supabase SQL Editor

```sql
-- Step 6.1.1: Add search vectors to reviews table
ALTER TABLE reviews ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Step 6.1.2: Add search vectors to profiles table  
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Step 6.1.3: Create GIN indexes for fast full-text search
CREATE INDEX IF NOT EXISTS reviews_search_idx ON reviews USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS profiles_search_idx ON profiles USING GIN(search_vector);

-- Step 6.1.4: Create additional indexes for filtering
CREATE INDEX IF NOT EXISTS reviews_genre_idx ON reviews USING GIN(genres);
CREATE INDEX IF NOT EXISTS reviews_platform_idx ON reviews USING GIN(platforms);
CREATE INDEX IF NOT EXISTS reviews_tags_idx ON reviews USING GIN(tags);
CREATE INDEX IF NOT EXISTS reviews_score_idx ON reviews (overall_score);
CREATE INDEX IF NOT EXISTS reviews_date_idx ON reviews (created_at);
CREATE INDEX IF NOT EXISTS reviews_author_idx ON reviews (author_id);

-- Step 6.1.5: Create composite indexes for complex filtering
CREATE INDEX IF NOT EXISTS reviews_status_score_date_idx ON reviews (status, overall_score, created_at);
CREATE INDEX IF NOT EXISTS reviews_author_status_idx ON reviews (author_id, status);
```

### **6.1.2: Create Search Update Functions**

```sql
-- Step 6.1.6: Function to update review search vectors
CREATE OR REPLACE FUNCTION update_review_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector = 
    setweight(to_tsvector('english', COALESCE(NEW.title, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.game_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.meta_description, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(NEW.focus_keyword, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.genres, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.platforms, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.tags, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(NEW.summary, '')), 'D');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 6.1.7: Function to update profile search vectors
CREATE OR REPLACE FUNCTION update_profile_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector = 
    setweight(to_tsvector('english', COALESCE(NEW.username, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.display_name, '')), 'A') ||
    setweight(to_tsvector('english', COALESCE(NEW.bio, '')), 'B') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.preferred_genres, ' '), '')), 'C') ||
    setweight(to_tsvector('english', COALESCE(array_to_string(NEW.favorite_consoles, ' '), '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### **6.1.3: Create Search Triggers**

```sql
-- Step 6.1.8: Create triggers for automatic search vector updates
DROP TRIGGER IF EXISTS reviews_search_update ON reviews;
CREATE TRIGGER reviews_search_update
  BEFORE INSERT OR UPDATE ON reviews
  FOR EACH ROW EXECUTE FUNCTION update_review_search_vector();

DROP TRIGGER IF EXISTS profiles_search_update ON profiles;
CREATE TRIGGER profiles_search_update
  BEFORE INSERT OR UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_profile_search_vector();
```

### **6.1.4: Create Search RPC Functions**

```sql
-- Step 6.1.9: Advanced review search function with filtering
CREATE OR REPLACE FUNCTION search_reviews(
  query_text text DEFAULT '',
  filter_genres text[] DEFAULT '{}',
  filter_platforms text[] DEFAULT '{}',
  filter_tags text[] DEFAULT '{}',
  min_score numeric DEFAULT 0,
  max_score numeric DEFAULT 10,
  author_id_filter text DEFAULT '',
  limit_count integer DEFAULT 20,
  offset_count integer DEFAULT 0
)
RETURNS TABLE(
  id text,
  title text,
  game_name text,
  slug text,
  overall_score numeric,
  genres text[],
  platforms text[],
  tags text[],
  author_name text,
  author_slug text,
  created_at timestamptz,
  main_image_url text,
  igdb_cover_url text,
  search_rank real
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.id,
    r.title,
    r.game_name,
    r.slug,
    r.overall_score,
    r.genres,
    r.platforms,
    r.tags,
    r.author_name,
    r.author_slug,
    r.created_at,
    r.main_image_url,
    r.igdb_cover_url,
    CASE 
      WHEN query_text = '' THEN 1.0
      ELSE ts_rank(r.search_vector, websearch_to_tsquery('english', query_text))
    END as search_rank
  FROM reviews r
  WHERE 
    r.status = 'published'
    AND (query_text = '' OR r.search_vector @@ websearch_to_tsquery('english', query_text))
    AND (cardinality(filter_genres) = 0 OR r.genres && filter_genres)
    AND (cardinality(filter_platforms) = 0 OR r.platforms && filter_platforms)
    AND (cardinality(filter_tags) = 0 OR r.tags && filter_tags)
    AND r.overall_score >= min_score
    AND r.overall_score <= max_score
    AND (author_id_filter = '' OR r.author_id = author_id_filter)
  ORDER BY 
    CASE WHEN query_text = '' THEN r.created_at ELSE search_rank END DESC,
    r.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Step 6.1.10: User search function
CREATE OR REPLACE FUNCTION search_profiles(
  query_text text DEFAULT '',
  limit_count integer DEFAULT 20,
  offset_count integer DEFAULT 0
)
RETURNS TABLE(
  id text,
  username text,
  display_name text,
  slug text,
  avatar_url text,
  bio text,
  level integer,
  review_count integer,
  preferred_genres text[],
  search_rank real
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.username,
    p.display_name,
    p.slug,
    p.avatar_url,
    p.bio,
    p.level,
    p.review_count,
    p.preferred_genres,
    CASE 
      WHEN query_text = '' THEN 1.0
      ELSE ts_rank(p.search_vector, websearch_to_tsquery('english', query_text))
    END as search_rank
  FROM profiles p
  WHERE 
    (query_text = '' OR p.search_vector @@ websearch_to_tsquery('english', query_text))
  ORDER BY 
    CASE WHEN query_text = '' THEN p.level ELSE search_rank END DESC,
    p.review_count DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;
```

### **6.1.5: Update Existing Data**

```sql
-- Step 6.1.11: Update search vectors for existing data
UPDATE reviews SET search_vector = 
  setweight(to_tsvector('english', COALESCE(title, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(game_name, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(meta_description, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(focus_keyword, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(genres, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(platforms, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(tags, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(summary, '')), 'D')
WHERE search_vector IS NULL;

UPDATE profiles SET search_vector = 
  setweight(to_tsvector('english', COALESCE(username, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(display_name, '')), 'A') ||
  setweight(to_tsvector('english', COALESCE(bio, '')), 'B') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(preferred_genres, ' '), '')), 'C') ||
  setweight(to_tsvector('english', COALESCE(array_to_string(favorite_consoles, ' '), '')), 'C')
WHERE search_vector IS NULL;
```

---

## **Step 6.2: Unified Search API Routes Creation**

### **6.2.1: Create Main Search API Route**

**File:** `/src/app/api/search/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { SearchOptimizer } from '@/lib/utils/searchOptimization';

// Create search optimizer instance
const searchOptimizer = new SearchOptimizer({
  maxSize: 100,
  ttlMs: 10 * 60 * 1000 // 10 minutes for search results
}, {
  maxRequests: 20,
  windowMs: 60 * 1000, // 1 minute
  minQueryLength: 2,
  debounceMs: 500
});

export interface SearchFilters {
  genres?: string[];
  platforms?: string[];
  tags?: string[];
  minScore?: number;
  maxScore?: number;
  authorId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface SearchRequest {
  query: string;
  type: 'all' | 'games' | 'reviews' | 'users' | 'hardware';
  filters?: SearchFilters;
  limit?: number;
  offset?: number;
}

export interface SearchResponse {
  games: any[];
  reviews: any[];
  users: any[];
  hardware: any[];
  totalResults: number;
  searchTime: number;
  fromCache: boolean;
  hasMore: boolean;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const body: SearchRequest = await request.json();
    const { query, type = 'all', filters = {}, limit = 20, offset = 0 } = body;

    // Validate query
    if (!query || typeof query !== 'string' || query.trim().length < 2) {
      return NextResponse.json({
        error: 'Query must be at least 2 characters long',
        games: [],
        reviews: [],
        users: [],
        hardware: [],
        totalResults: 0,
        searchTime: 0,
        fromCache: false,
        hasMore: false
      }, { status: 400 });
    }

    const trimmedQuery = query.trim();
    
    // Use search optimizer for caching and rate limiting
    const searchResult = await searchOptimizer.search(
      `${trimmedQuery}-${type}-${JSON.stringify(filters)}-${limit}-${offset}`,
      async () => {
        return await performSearch(trimmedQuery, type, filters, limit, offset);
      }
    );

    if (searchResult.error) {
      return NextResponse.json({
        error: searchResult.error,
        games: [],
        reviews: [],
        users: [],
        hardware: [],
        totalResults: 0,
        searchTime: Date.now() - startTime,
        fromCache: false,
        hasMore: false
      }, { status: 429 });
    }

    const response: SearchResponse = {
      ...searchResult.results,
      searchTime: Date.now() - startTime,
      fromCache: searchResult.fromCache
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json({
      error: 'Internal search error',
      games: [],
      reviews: [],
      users: [],
      hardware: [],
      totalResults: 0,
      searchTime: Date.now() - startTime,
      fromCache: false,
      hasMore: false
    }, { status: 500 });
  }
}

async function performSearch(
  query: string,
  type: string,
  filters: SearchFilters,
  limit: number,
  offset: number
): Promise<SearchResponse> {
  const supabase = createServerClient();
  let games: any[] = [];
  let reviews: any[] = [];
  let users: any[] = [];
  let hardware: any[] = [];
  let totalResults = 0;

  // Search Games (IGDB)
  if (type === 'all' || type === 'games') {
    try {
      const gameResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/igdb/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: query })
      });
      
      if (gameResponse.ok) {
        games = await gameResponse.json();
        games = games.slice(offset, offset + limit);
      }
    } catch (error) {
      console.error('IGDB search error:', error);
    }
  }

  // Search Reviews (PostgreSQL FTS)
  if (type === 'all' || type === 'reviews') {
    try {
      const { data: reviewData, error } = await supabase.rpc('search_reviews', {
        query_text: query,
        filter_genres: filters.genres || [],
        filter_platforms: filters.platforms || [],
        filter_tags: filters.tags || [],
        min_score: filters.minScore || 0,
        max_score: filters.maxScore || 10,
        author_id_filter: filters.authorId || '',
        limit_count: limit,
        offset_count: offset
      });

      if (!error && reviewData) {
        reviews = reviewData;
      }
    } catch (error) {
      console.error('Review search error:', error);
    }
  }

  // Search Users (PostgreSQL FTS)
  if (type === 'all' || type === 'users') {
    try {
      const { data: userData, error } = await supabase.rpc('search_profiles', {
        query_text: query,
        limit_count: limit,
        offset_count: offset
      });

      if (!error && userData) {
        users = userData;
      }
    } catch (error) {
      console.error('User search error:', error);
    }
  }

  // Search Hardware (existing APIs)
  if (type === 'all' || type === 'hardware') {
    try {
      const [cpuResponse, gpuResponse] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/hardware/cpu/search?q=${encodeURIComponent(query)}&limit=${limit}`),
        fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/hardware/gpu/search?q=${encodeURIComponent(query)}&limit=${limit}`)
      ]);

      const cpuData = cpuResponse.ok ? await cpuResponse.json() : { data: [] };
      const gpuData = gpuResponse.ok ? await gpuResponse.json() : { data: [] };

      hardware = [
        ...cpuData.data.map((item: any) => ({ ...item, type: 'cpu' })),
        ...gpuData.data.map((item: any) => ({ ...item, type: 'gpu' }))
      ].slice(offset, offset + limit);
    } catch (error) {
      console.error('Hardware search error:', error);
    }
  }

  totalResults = games.length + reviews.length + users.length + hardware.length;
  const hasMore = totalResults === limit; // Approximate check

  return {
    games,
    reviews,
    users,
    hardware,
    totalResults,
    searchTime: 0, // Will be set by caller
    fromCache: false, // Will be set by optimizer
    hasMore
  };
}
```

### **6.2.2: Create Search Suggestions API**

**File:** `/src/app/api/search/suggestions/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const type = searchParams.get('type') || 'all';

    if (!query || query.length < 2) {
      return NextResponse.json({ suggestions: [] });
    }

    const supabase = createServerClient();
    const suggestions: string[] = [];

    // Get review title suggestions
    if (type === 'all' || type === 'reviews') {
      const { data: reviewSuggestions } = await supabase
        .from('reviews')
        .select('title, game_name')
        .textSearch('title', query, { type: 'websearch' })
        .eq('status', 'published')
        .limit(5);

      if (reviewSuggestions) {
        reviewSuggestions.forEach(review => {
          if (review.title && !suggestions.includes(review.title)) {
            suggestions.push(review.title);
          }
          if (review.game_name && !suggestions.includes(review.game_name)) {
            suggestions.push(review.game_name);
          }
        });
      }
    }

    // Get user suggestions
    if (type === 'all' || type === 'users') {
      const { data: userSuggestions } = await supabase
        .from('profiles')
        .select('username, display_name')
        .textSearch('username', query, { type: 'websearch' })
        .limit(3);

      if (userSuggestions) {
        userSuggestions.forEach(user => {
          if (user.username && !suggestions.includes(user.username)) {
            suggestions.push(user.username);
          }
          if (user.display_name && !suggestions.includes(user.display_name)) {
            suggestions.push(user.display_name);
          }
        });
      }
    }

    return NextResponse.json({ 
      suggestions: suggestions.slice(0, 8) 
    });

  } catch (error) {
    console.error('Search suggestions error:', error);
    return NextResponse.json({ suggestions: [] });
  }
}
```

---

## **Step 6.3: Advanced Search UI Components Development**

### **6.3.1: Main Search Interface Component**

**File:** `/src/components/search/SearchInterface.tsx`

```typescript
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { SearchFilters } from './SearchFilters';
import { SearchResults } from './SearchResults';
import { SearchSuggestions } from './SearchSuggestions';
import { SearchIcon, FilterIcon, XIcon } from 'lucide-react';

export interface SearchState {
  query: string;
  type: 'all' | 'games' | 'reviews' | 'users' | 'hardware';
  filters: {
    genres: string[];
    platforms: string[];
    tags: string[];
    minScore: number;
    maxScore: number;
    authorId: string;
    dateFrom: string;
    dateTo: string;
  };
  results: {
    games: any[];
    reviews: any[];
    users: any[];
    hardware: any[];
    totalResults: number;
    hasMore: boolean;
  };
  isLoading: boolean;
  showFilters: boolean;
  showSuggestions: boolean;
  searchTime: number;
  fromCache: boolean;
}

export function SearchInterface() {
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    type: 'all',
    filters: {
      genres: [],
      platforms: [],
      tags: [],
      minScore: 0,
      maxScore: 10,
      authorId: '',
      dateFrom: '',
      dateTo: ''
    },
    results: {
      games: [],
      reviews: [],
      users: [],
      hardware: [],
      totalResults: 0,
      hasMore: false
    },
    isLoading: false,
    showFilters: false,
    showSuggestions: false,
    searchTime: 0,
    fromCache: false
  });

  const [suggestions, setSuggestions] = useState<string[]>([]);
  
  // Debounce search query
  const debouncedQuery = useDebounce(searchState.query, 500);

  // Debounce suggestions query (faster)
  const debouncedSuggestionsQuery = useDebounce(searchState.query, 300);

  // Perform search
  const performSearch = useCallback(async (
    query: string,
    type: string,
    filters: any,
    offset: number = 0
  ) => {
    if (!query.trim() || query.length < 2) {
      setSearchState(prev => ({
        ...prev,
        results: {
          games: [],
          reviews: [],
          users: [],
          hardware: [],
          totalResults: 0,
          hasMore: false
        },
        isLoading: false,
        showSuggestions: false
      }));
      return;
    }

    setSearchState(prev => ({ ...prev, isLoading: true, showSuggestions: false }));

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          type,
          filters,
          limit: 20,
          offset
        })
      });

      const data = await response.json();

      if (response.ok) {
        setSearchState(prev => ({
          ...prev,
          results: offset === 0 ? data : {
            games: [...prev.results.games, ...data.games],
            reviews: [...prev.results.reviews, ...data.reviews],
            users: [...prev.results.users, ...data.users],
            hardware: [...prev.results.hardware, ...data.hardware],
            totalResults: data.totalResults,
            hasMore: data.hasMore
          },
          isLoading: false,
          searchTime: data.searchTime,
          fromCache: data.fromCache
        }));
      } else {
        console.error('Search error:', data.error);
        setSearchState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Search request error:', error);
      setSearchState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Fetch suggestions
  const fetchSuggestions = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const response = await fetch(
        `/api/search/suggestions?q=${encodeURIComponent(query)}&type=${searchState.type}`
      );
      const data = await response.json();
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error('Suggestions error:', error);
    }
  }, [searchState.type]);

  // Effect for search
  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery, searchState.type, searchState.filters);
    }
  }, [debouncedQuery, searchState.type, searchState.filters, performSearch]);

  // Effect for suggestions
  useEffect(() => {
    if (debouncedSuggestionsQuery && searchState.showSuggestions) {
      fetchSuggestions(debouncedSuggestionsQuery);
    }
  }, [debouncedSuggestionsQuery, searchState.showSuggestions, fetchSuggestions]);

  const updateQuery = (query: string) => {
    setSearchState(prev => ({ 
      ...prev, 
      query,
      showSuggestions: query.length >= 2 && query.length < 20
    }));
  };

  const updateFilters = (filters: any) => {
    setSearchState(prev => ({ ...prev, filters }));
  };

  const updateType = (type: any) => {
    setSearchState(prev => ({ ...prev, type }));
  };

  const clearFilters = () => {
    setSearchState(prev => ({
      ...prev,
      filters: {
        genres: [],
        platforms: [],
        tags: [],
        minScore: 0,
        maxScore: 10,
        authorId: '',
        dateFrom: '',
        dateTo: ''
      }
    }));
  };

  const loadMore = () => {
    const currentTotal = Object.values(searchState.results).reduce((sum, arr) => {
      return sum + (Array.isArray(arr) ? arr.length : 0);
    }, 0);
    performSearch(debouncedQuery, searchState.type, searchState.filters, currentTotal);
  };

  const hasActiveFilters = Object.values(searchState.filters).some(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'string') return value !== '';
    if (typeof value === 'number') return value !== 0 && value !== 10;
    return false;
  });

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Search Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SearchIcon className="w-5 h-5" />
            Search CriticalPixel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Input
              type="text"
              placeholder="Search games, reviews, users, hardware..."
              value={searchState.query}
              onChange={(e) => updateQuery(e.target.value)}
              onFocus={() => setSearchState(prev => ({ 
                ...prev, 
                showSuggestions: prev.query.length >= 2 
              }))}
              className="pr-10"
            />
            {searchState.query && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1 h-8 w-8 p-0"
                onClick={() => updateQuery('')}
              >
                <XIcon className="w-4 h-4" />
              </Button>
            )}
            
            {/* Search Suggestions */}
            {searchState.showSuggestions && suggestions.length > 0 && (
              <SearchSuggestions
                suggestions={suggestions}
                onSelect={(suggestion) => {
                  updateQuery(suggestion);
                  setSearchState(prev => ({ ...prev, showSuggestions: false }));
                }}
                onClose={() => setSearchState(prev => ({ 
                  ...prev, 
                  showSuggestions: false 
                }))}
              />
            )}
          </div>

          {/* Search Type Tabs */}
          <Tabs value={searchState.type} onValueChange={updateType}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="games">Games</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="hardware">Hardware</TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Filter Controls */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSearchState(prev => ({ 
                ...prev, 
                showFilters: !prev.showFilters 
              }))}
              className="flex items-center gap-2"
            >
              <FilterIcon className="w-4 h-4" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-1">
                  Active
                </Badge>
              )}
            </Button>

            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            )}
          </div>

          {/* Search Stats */}
          {searchState.query && (
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>
                {searchState.results.totalResults} results found
              </span>
              <span>
                {searchState.searchTime}ms
              </span>
              {searchState.fromCache && (
                <Badge variant="outline" className="text-xs">
                  Cached
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Advanced Filters */}
      {searchState.showFilters && (
        <SearchFilters
          filters={searchState.filters}
          searchType={searchState.type}
          onFiltersChange={updateFilters}
        />
      )}

      {/* Search Results */}
      {searchState.isLoading && !searchState.results.totalResults ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      ) : (
        <SearchResults
          results={searchState.results}
          searchType={searchState.type}
          isLoading={searchState.isLoading}
          hasMore={searchState.results.hasMore}
          onLoadMore={loadMore}
        />
      )}
    </div>
  );
}
```

### **6.3.2: Search Filters Component**

**File:** `/src/components/search/SearchFilters.tsx`

```typescript
'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const GENRES = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Simulation', 'Sports',
  'Racing', 'Puzzle', 'Platform', 'Shooter', 'Fighting', 'Horror',
  'Survival', 'MMO', 'Indie', 'VR'
];

const PLATFORMS = [
  'PC', 'PlayStation 5', 'PlayStation 4', 'Xbox Series X/S', 'Xbox One',
  'Nintendo Switch', 'Steam', 'Epic Games', 'Android', 'iOS'
];

const POPULAR_TAGS = [
  'Multiplayer', 'Single-player', 'Co-op', 'Competitive', 'Open World',
  'Story Rich', 'Early Access', 'Free to Play', 'Battle Royale',
  'Roguelike', 'Sandbox', 'Tactical', 'Turn-Based', 'Real-Time'
];

interface SearchFiltersProps {
  filters: {
    genres: string[];
    platforms: string[];
    tags: string[];
    minScore: number;
    maxScore: number;
    authorId: string;
    dateFrom: string;
    dateTo: string;
  };
  searchType: string;
  onFiltersChange: (filters: any) => void;
}

export function SearchFilters({ filters, searchType, onFiltersChange }: SearchFiltersProps) {
  const updateFilter = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const toggleArrayFilter = (key: string, value: string) => {
    const currentArray = filters[key as keyof typeof filters] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFilter(key, newArray);
  };

  // Show different filters based on search type
  const showReviewFilters = searchType === 'all' || searchType === 'reviews';
  const showUserFilters = searchType === 'all' || searchType === 'users';

  return (
    <Card>
      <CardHeader>
        <CardTitle>Advanced Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Genre Filters */}
        {showReviewFilters && (
          <div className="space-y-3">
            <Label className="text-sm font-semibold">Genres</Label>
            <div className="flex flex-wrap gap-2">
              {GENRES.map(genre => (
                <Badge
                  key={genre}
                  variant={filters.genres.includes(genre) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                  onClick={() => toggleArrayFilter('genres', genre)}
                >
                  {genre}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Platform Filters */}
        {showReviewFilters && (
          <div className="space-y-3">
            <Label className="text-sm font-semibold">Platforms</Label>
            <div className="flex flex-wrap gap-2">
              {PLATFORMS.map(platform => (
                <Badge
                  key={platform}
                  variant={filters.platforms.includes(platform) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                  onClick={() => toggleArrayFilter('platforms', platform)}
                >
                  {platform}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Tags Filters */}
        {showReviewFilters && (
          <div className="space-y-3">
            <Label className="text-sm font-semibold">Tags</Label>
            <div className="flex flex-wrap gap-2">
              {POPULAR_TAGS.map(tag => (
                <Badge
                  key={tag}
                  variant={filters.tags.includes(tag) ? "default" : "outline"}
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                  onClick={() => toggleArrayFilter('tags', tag)}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Score Range */}
        {showReviewFilters && (
          <div className="space-y-3">
            <Label className="text-sm font-semibold">
              Score Range: {filters.minScore} - {filters.maxScore}
            </Label>
            <div className="px-3">
              <Slider
                value={[filters.minScore, filters.maxScore]}
                onValueChange={([min, max]) => {
                  updateFilter('minScore', min);
                  updateFilter('maxScore', max);
                }}
                max={10}
                min={0}
                step={0.1}
                className="w-full"
              />
            </div>
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0</span>
              <span>5</span>
              <span>10</span>
            </div>
          </div>
        )}

        {/* Date Range */}
        {showReviewFilters && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateFrom" className="text-sm font-semibold">
                  From Date
                </Label>
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => updateFilter('dateFrom', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dateTo" className="text-sm font-semibold">
                  To Date
                </Label>
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => updateFilter('dateTo', e.target.value)}
                />
              </div>
            </div>
          </>
        )}

        {/* Author Filter */}
        {showReviewFilters && (
          <div className="space-y-2">
            <Label htmlFor="authorId" className="text-sm font-semibold">
              Filter by Author ID
            </Label>
            <Input
              id="authorId"
              type="text"
              placeholder="Enter author user ID..."
              value={filters.authorId}
              onChange={(e) => updateFilter('authorId', e.target.value)}
            />
          </div>
        )}

        {/* Active Filters Summary */}
        {(filters.genres.length > 0 || filters.platforms.length > 0 || filters.tags.length > 0) && (
          <>
            <Separator />
            <div className="space-y-2">
              <Label className="text-sm font-semibold">Active Filters</Label>
              <div className="flex flex-wrap gap-1">
                {filters.genres.map(genre => (
                  <Badge key={`genre-${genre}`} variant="secondary" className="text-xs">
                    Genre: {genre}
                  </Badge>
                ))}
                {filters.platforms.map(platform => (
                  <Badge key={`platform-${platform}`} variant="secondary" className="text-xs">
                    Platform: {platform}
                  </Badge>
                ))}
                {filters.tags.map(tag => (
                  <Badge key={`tag-${tag}`} variant="secondary" className="text-xs">
                    Tag: {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
```

### **6.3.3: Search Results Component**

**File:** `/src/components/search/SearchResults.tsx`

```typescript
'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScoreCircle } from '@/components/ui/ScoreCircle';
import { Skeleton } from '@/components/ui/skeleton';
import { ExternalLinkIcon, UserIcon, GamepadIcon, CpuIcon } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface SearchResultsProps {
  results: {
    games: any[];
    reviews: any[];
    users: any[];
    hardware: any[];
    totalResults: number;
    hasMore: boolean;
  };
  searchType: string;
  isLoading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export function SearchResults({ 
  results, 
  searchType, 
  isLoading, 
  hasMore, 
  onLoadMore 
}: SearchResultsProps) {
  if (!results.totalResults && !isLoading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-muted-foreground">No results found. Try adjusting your search terms or filters.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Games Results */}
      {(searchType === 'all' || searchType === 'games') && results.games.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <GamepadIcon className="w-5 h-5" />
            Games ({results.games.length})
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {results.games.map((game) => (
              <Card key={game.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    {game.cover?.url && (
                      <Image
                        src={`https:${game.cover.url.replace('t_thumb', 't_cover_small')}`}
                        alt={game.name}
                        width={60}
                        height={80}
                        className="rounded object-cover"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold truncate">{game.name}</h4>
                      {game.genres && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {game.genres.slice(0, 2).map((genre: string) => (
                            <Badge key={genre} variant="outline" className="text-xs">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      )}
                      {game.platforms && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {game.platforms.slice(0, 3).join(', ')}
                        </p>
                      )}
                      {game.rating && (
                        <div className="flex items-center gap-2 mt-2">
                          <ScoreCircle score={Math.round(game.rating / 10)} size="small" />
                          <span className="text-xs text-muted-foreground">
                            IGDB Rating
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Reviews Results */}
      {(searchType === 'all' || searchType === 'reviews') && results.reviews.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <ExternalLinkIcon className="w-5 h-5" />
            Reviews ({results.reviews.length})
          </h3>
          <div className="space-y-4">
            {results.reviews.map((review) => (
              <Card key={review.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    {(review.main_image_url || review.igdb_cover_url) && (
                      <Image
                        src={review.main_image_url || review.igdb_cover_url}
                        alt={review.title}
                        width={80}
                        height={60}
                        className="rounded object-cover"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          <Link 
                            href={`/reviews/view/${review.slug}`}
                            className="block hover:underline"
                          >
                            <h4 className="font-semibold line-clamp-1">{review.title}</h4>
                          </Link>
                          <p className="text-sm text-muted-foreground">
                            {review.game_name}
                          </p>
                          <div className="flex items-center gap-2 mt-2">
                            <Link 
                              href={`/u/${review.author_slug}`}
                              className="text-sm text-muted-foreground hover:underline"
                            >
                              by {review.author_name}
                            </Link>
                            <span className="text-xs text-muted-foreground">
                              {new Date(review.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <ScoreCircle score={review.overall_score} size="medium" />
                      </div>
                      
                      {/* Genres and Platforms */}
                      <div className="flex flex-wrap gap-1 mt-3">
                        {review.genres?.slice(0, 3).map((genre: string) => (
                          <Badge key={genre} variant="outline" className="text-xs">
                            {genre}
                          </Badge>
                        ))}
                        {review.platforms?.slice(0, 2).map((platform: string) => (
                          <Badge key={platform} variant="secondary" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Users Results */}
      {(searchType === 'all' || searchType === 'users') && results.users.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <UserIcon className="w-5 h-5" />
            Users ({results.users.length})
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {results.users.map((user) => (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={user.avatar_url} alt={user.username} />
                      <AvatarFallback>
                        {user.username?.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <Link 
                        href={`/u/${user.slug}`}
                        className="block hover:underline"
                      >
                        <h4 className="font-semibold truncate">
                          {user.display_name || user.username}
                        </h4>
                      </Link>
                      <p className="text-sm text-muted-foreground">
                        @{user.username}
                      </p>
                      {user.bio && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {user.bio}
                        </p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                        <span>Level {user.level}</span>
                        <span>{user.review_count} reviews</span>
                      </div>
                      {user.preferred_genres?.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {user.preferred_genres.slice(0, 3).map((genre: string) => (
                            <Badge key={genre} variant="outline" className="text-xs">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Hardware Results */}
      {(searchType === 'all' || searchType === 'hardware') && results.hardware.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <CpuIcon className="w-5 h-5" />
            Hardware ({results.hardware.length})
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {results.hardware.map((item, index) => (
              <Card key={`${item.type}-${index}`} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold line-clamp-1">{item.name}</h4>
                        <Badge variant="outline" className="text-xs mt-1">
                          {item.type?.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                    {item.specifications && (
                      <div className="space-y-1 text-xs text-muted-foreground">
                        {Object.entries(item.specifications).slice(0, 3).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="capitalize">{key}:</span>
                            <span>{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center pt-4">
          <Button 
            onClick={onLoadMore} 
            disabled={isLoading}
            variant="outline"
            className="min-w-32"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Skeleton className="w-4 h-4 rounded-full" />
                Loading...
              </div>
            ) : (
              'Load More Results'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
```

### **6.3.4: Search Suggestions Component**

**File:** `/src/components/search/SearchSuggestions.tsx`

```typescript
'use client';

import React, { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SearchIcon } from 'lucide-react';

interface SearchSuggestionsProps {
  suggestions: string[];
  onSelect: (suggestion: string) => void;
  onClose: () => void;
}

export function SearchSuggestions({ suggestions, onSelect, onClose }: SearchSuggestionsProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  if (suggestions.length === 0) return null;

  return (
    <div ref={containerRef} className="absolute top-full left-0 right-0 z-50 mt-1">
      <Card className="border shadow-lg">
        <CardContent className="p-2">
          <div className="space-y-1">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="ghost"
                className="w-full justify-start text-left h-auto p-2 hover:bg-muted"
                onClick={() => onSelect(suggestion)}
              >
                <SearchIcon className="w-4 h-4 mr-2 text-muted-foreground" />
                <span className="truncate">{suggestion}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

---

## **Step 6.4: Search Optimization Integration**

### **6.4.1: Enhanced Search Hook**

**File:** `/src/hooks/useAdvancedSearch.ts`

```typescript
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '@/hooks/useDebounce';

export interface SearchFilters {
  genres: string[];
  platforms: string[];
  tags: string[];
  minScore: number;
  maxScore: number;
  authorId: string;
  dateFrom: string;
  dateTo: string;
}

export interface SearchState {
  query: string;
  type: 'all' | 'games' | 'reviews' | 'users' | 'hardware';
  filters: SearchFilters;
  results: {
    games: any[];
    reviews: any[];
    users: any[];
    hardware: any[];
    totalResults: number;
    hasMore: boolean;
  };
  isLoading: boolean;
  error: string | null;
  searchTime: number;
  fromCache: boolean;
}

export function useAdvancedSearch() {
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    type: 'all',
    filters: {
      genres: [],
      platforms: [],
      tags: [],
      minScore: 0,
      maxScore: 10,
      authorId: '',
      dateFrom: '',
      dateTo: ''
    },
    results: {
      games: [],
      reviews: [],
      users: [],
      hardware: [],
      totalResults: 0,
      hasMore: false
    },
    isLoading: false,
    error: null,
    searchTime: 0,
    fromCache: false
  });

  const debouncedQuery = useDebounce(searchState.query, 500);

  const performSearch = useCallback(async (
    query: string,
    type: string,
    filters: SearchFilters,
    offset: number = 0,
    append: boolean = false
  ) => {
    if (!query.trim() || query.length < 2) {
      setSearchState(prev => ({
        ...prev,
        results: {
          games: [],
          reviews: [],
          users: [],
          hardware: [],
          totalResults: 0,
          hasMore: false
        },
        isLoading: false,
        error: null
      }));
      return;
    }

    setSearchState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          type,
          filters,
          limit: 20,
          offset
        })
      });

      const data = await response.json();

      if (response.ok) {
        setSearchState(prev => ({
          ...prev,
          results: append ? {
            games: [...prev.results.games, ...data.games],
            reviews: [...prev.results.reviews, ...data.reviews],
            users: [...prev.results.users, ...data.users],
            hardware: [...prev.results.hardware, ...data.hardware],
            totalResults: data.totalResults,
            hasMore: data.hasMore
          } : data,
          isLoading: false,
          searchTime: data.searchTime,
          fromCache: data.fromCache,
          error: null
        }));
      } else {
        setSearchState(prev => ({
          ...prev,
          isLoading: false,
          error: data.error || 'Search failed'
        }));
      }
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Network error occurred'
      }));
    }
  }, []);

  // Auto-search when query changes
  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery, searchState.type, searchState.filters);
    }
  }, [debouncedQuery, searchState.type, searchState.filters, performSearch]);

  const updateQuery = useCallback((query: string) => {
    setSearchState(prev => ({ ...prev, query }));
  }, []);

  const updateType = useCallback((type: SearchState['type']) => {
    setSearchState(prev => ({ ...prev, type }));
  }, []);

  const updateFilters = useCallback((filters: SearchFilters) => {
    setSearchState(prev => ({ ...prev, filters }));
  }, []);

  const loadMore = useCallback(() => {
    if (searchState.results.hasMore && !searchState.isLoading) {
      const currentTotal = Object.values(searchState.results).reduce((sum, arr) => {
        return sum + (Array.isArray(arr) ? arr.length : 0);
      }, 0);
      performSearch(debouncedQuery, searchState.type, searchState.filters, currentTotal, true);
    }
  }, [debouncedQuery, searchState.type, searchState.filters, searchState.results.hasMore, searchState.isLoading, performSearch]);

  const clearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      results: {
        games: [],
        reviews: [],
        users: [],
        hardware: [],
        totalResults: 0,
        hasMore: false
      },
      error: null
    }));
  }, []);

  return {
    searchState,
    updateQuery,
    updateType,
    updateFilters,
    loadMore,
    clearSearch,
    performSearch: (query: string, type?: string, filters?: SearchFilters) => 
      performSearch(query, type || searchState.type, filters || searchState.filters)
  };
}
```

### **6.4.2: Search Analytics Integration**

**File:** `/src/lib/analytics/searchAnalytics.ts`

```typescript
export interface SearchAnalyticsData {
  query: string;
  searchType: string;
  resultsCount: number;
  searchTime: number;
  clickedResult?: {
    type: 'game' | 'review' | 'user' | 'hardware';
    id: string;
    position: number;
  };
  filters: any;
  userId?: string;
  sessionId: string;
  timestamp: string;
}

class SearchAnalytics {
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
  }

  private generateSessionId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  async trackSearch(data: Omit<SearchAnalyticsData, 'sessionId' | 'timestamp' | 'userId'>) {
    const analyticsData: SearchAnalyticsData = {
      ...data,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    };

    try {
      // Store analytics data - could be sent to analytics service
      await fetch('/api/analytics/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(analyticsData)
      });
    } catch (error) {
      console.error('Failed to track search analytics:', error);
    }
  }

  async trackClick(
    query: string,
    resultType: 'game' | 'review' | 'user' | 'hardware',
    resultId: string,
    position: number
  ) {
    try {
      await fetch('/api/analytics/search-click', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          resultType,
          resultId,
          position,
          sessionId: this.sessionId,
          userId: this.userId,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to track search click:', error);
    }
  }
}

export const searchAnalytics = new SearchAnalytics();
```

---

## **Step 6.5: Search Analytics and Monitoring Implementation**

### **6.5.1: Search Analytics API**

**File:** `/src/app/api/analytics/search/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const analyticsData = await request.json();

    // Store search analytics (you might want to create a search_analytics table)
    const { error } = await supabase
      .from('search_analytics')
      .insert({
        query: analyticsData.query,
        search_type: analyticsData.searchType,
        results_count: analyticsData.resultsCount,
        search_time: analyticsData.searchTime,
        filters: analyticsData.filters,
        user_id: analyticsData.userId,
        session_id: analyticsData.sessionId,
        created_at: analyticsData.timestamp
      });

    if (error) {
      console.error('Analytics storage error:', error);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Search analytics error:', error);
    return NextResponse.json({ success: false }, { status: 500 });
  }
}
```

### **6.5.2: Search Performance Monitoring**

**File:** `/src/lib/monitoring/searchMonitoring.ts`

```typescript
export interface SearchPerformanceMetrics {
  averageResponseTime: number;
  slowQueries: Array<{
    query: string;
    responseTime: number;
    timestamp: string;
  }>;
  popularQueries: Array<{
    query: string;
    count: number;
  }>;
  errorRate: number;
  cacheHitRate: number;
}

export class SearchMonitoring {
  private metrics: SearchPerformanceMetrics = {
    averageResponseTime: 0,
    slowQueries: [],
    popularQueries: [],
    errorRate: 0,
    cacheHitRate: 0
  };

  recordSearch(query: string, responseTime: number, fromCache: boolean, hasError: boolean) {
    // Record slow queries (>2 seconds)
    if (responseTime > 2000) {
      this.metrics.slowQueries.push({
        query,
        responseTime,
        timestamp: new Date().toISOString()
      });

      // Keep only last 50 slow queries
      if (this.metrics.slowQueries.length > 50) {
        this.metrics.slowQueries = this.metrics.slowQueries.slice(-50);
      }
    }

    // Track popular queries
    const existingQuery = this.metrics.popularQueries.find(pq => pq.query === query);
    if (existingQuery) {
      existingQuery.count++;
    } else {
      this.metrics.popularQueries.push({ query, count: 1 });
    }

    // Sort and keep top 20 popular queries
    this.metrics.popularQueries = this.metrics.popularQueries
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);
  }

  getMetrics(): SearchPerformanceMetrics {
    return { ...this.metrics };
  }

  generateReport(): string {
    const { averageResponseTime, slowQueries, popularQueries, errorRate, cacheHitRate } = this.metrics;

    return `
Search Performance Report
========================
Average Response Time: ${averageResponseTime}ms
Cache Hit Rate: ${(cacheHitRate * 100).toFixed(1)}%
Error Rate: ${(errorRate * 100).toFixed(1)}%

Slow Queries (>2s): ${slowQueries.length}
${slowQueries.slice(0, 5).map(q => `- "${q.query}" (${q.responseTime}ms)`).join('\n')}

Popular Queries:
${popularQueries.slice(0, 10).map(q => `- "${q.query}" (${q.count} searches)`).join('\n')}
    `.trim();
  }
}

export const searchMonitoring = new SearchMonitoring();
```

---

## **Implementation Timeline & Checklist**

### **Day 1: Database Schema Setup**
- [ ] Execute PostgreSQL FTS schema updates in Supabase
- [ ] Create search vector columns and indexes
- [ ] Create search RPC functions
- [ ] Test search functions with sample data
- [ ] Update existing data with search vectors

### **Day 2: API Development**
- [ ] Create unified search API route (`/api/search/route.ts`)
- [ ] Create search suggestions API (`/api/search/suggestions/route.ts`)
- [ ] Integrate with existing search optimization utilities
- [ ] Test API endpoints with various queries and filters
- [ ] Implement error handling and rate limiting

### **Day 3: Frontend Components**
- [ ] Build main SearchInterface component
- [ ] Create SearchFilters component with advanced filtering
- [ ] Develop SearchResults component with proper formatting
- [ ] Build SearchSuggestions component
- [ ] Test components with real data

### **Day 4: Integration & Testing**
- [ ] Integrate useAdvancedSearch hook
- [ ] Add search analytics and monitoring
- [ ] Performance testing and optimization
- [ ] Cross-browser testing
- [ ] Mobile responsiveness testing

---

## **Success Metrics**

### **Performance Targets**
- ✅ Search response time: <500ms for 90% of queries
- ✅ Cache hit rate: >60% for repeated searches
- ✅ Database query time: <50ms for FTS operations
- ✅ UI responsiveness: Search suggestions in <300ms

### **Functionality Requirements**
- ✅ Multi-entity search (games, reviews, users, hardware)
- ✅ Advanced filtering with mix-and-match capabilities
- ✅ Real-time search suggestions
- ✅ Pagination and infinite scroll support
- ✅ Analytics and performance monitoring

### **User Experience Goals**
- ✅ Intuitive search interface
- ✅ Fast and responsive search
- ✅ Relevant and ranked results
- ✅ Mobile-friendly design
- ✅ Accessibility compliance

---

## **Post-Implementation Tasks**

1. **Performance Optimization**
   - Monitor search performance metrics
   - Optimize slow queries
   - Adjust cache strategies

2. **User Feedback Integration**
   - Collect user search behavior data
   - Refine search ranking algorithms
   - Improve suggestion quality

3. **Feature Enhancements**
   - Add search result export functionality
   - Implement saved search functionality
   - Add search history for users

4. **Maintenance**
   - Regular index optimization
   - Search vector updates for new content
   - Performance monitoring alerts

---

**Implementation Lead:** Claude AI Assistant  
**Phase Duration:** 3-4 days  
**Dependencies:** Phase 1 (Database Schema), Phase 2 (Review System Core)  
**Next Phase:** Phase 7 (Testing & Validation)