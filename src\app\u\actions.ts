'use server';

import { createServerClient } from '@/lib/supabase/server';
import {
  ProfileSchema,
  UsernameSchema,
  ProfileUpdateSchema,
  UsernameAvailabilitySchema,
  UsernameChangeSchema
} from '@/lib/validations/profile';
import { z } from 'zod';
import type {
  UserProfile,
  ProfileUpdateInput,
  ProfileOperationResult,
  UsernameAvailabilityResult,
  UsernameSuggestion
} from '@/lib/types/profile';
import { cookies } from 'next/headers';
import { revalidatePath, revalidateTag, unstable_cache } from 'next/cache';
import { getCompleteUserProfile } from './actions-profiles';
import { validateUserNotSuspended } from '@/lib/middleware/suspensionCheck';

/**
 * Enrich profile with data from related tables
 */
async function enrichProfileWithRelatedData(profile: UserProfile): Promise<UserProfile> {
  try {
    const relatedData = await getCompleteUserProfile(profile.id);

    return {
      ...profile,
      gaming_profiles: relatedData.gaming_profiles,
      social_profiles: relatedData.social_profiles
    };
  } catch (error) {
    console.error(`Error enriching profile with related data for user ${profile.id}:`, error);

    // Instead of re-throwing, return the profile with empty arrays for the related data
    // This ensures the main profile still loads even if related data can't be fetched
    return {
      ...profile,
      gaming_profiles: [],
      social_profiles: []
    };
  }
}

/**
 * Internal function to fetch profile from database by username
 */
async function _fetchProfileByUsername(username: string, cookieStore?: any): Promise<UserProfile | null> {
  try {
    const supabase = await createServerClient(cookieStore);

    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        slug_lower,
        avatar_url,
        banner_url,
        bio,
        preferred_genres,
        favorite_consoles,
        theme,
        custom_colors,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        privacy_settings,
        suspended,
        suspension_reason,
        suspended_at,
        created_at,
        updated_at
      `)
      .eq('username', username)
      .single();

          if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned - profile not found
          console.warn(`🔍 Profile not found for username: ${username}`);
          return null;
        }
        console.error('🚨 Error fetching profile:', error);
        return null;
      }

      // SECURITY CHECK: Block access to suspended user profiles
      if (profile.suspended) {
        console.warn(`🚫 Access blocked to suspended profile: ${profile.username}`);
        return null;
      }

      console.log(`✅ Profile found for username: ${username}`);
      return await enrichProfileWithRelatedData(profile as UserProfile);
  } catch (error) {
    console.error('Error in _fetchProfileByUsername:', error);
    return null;
  }
}

/**
 * Internal function to fetch profile from database by slug
 */
async function _fetchProfileBySlug(slug: string, cookieStore?: any): Promise<UserProfile | null> {
  try {
    const supabase = await createServerClient(cookieStore);

    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        slug_lower,
        avatar_url,
        banner_url,
        bio,
        preferred_genres,
        favorite_consoles,
        theme,
        custom_colors,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        privacy_settings,
        suspended,
        suspension_reason,
        suspended_at,
        created_at,
        updated_at
      `)
      .eq('slug_lower', slug.toLowerCase())
      .single();

          if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned - profile not found
          console.warn(`🔍 Profile not found for slug: ${slug}`);
          return null;
        }
        console.error('🚨 Error fetching profile by slug:', error);
        return null;
      }

      // SECURITY CHECK: Block access to suspended user profiles
      if (profile.suspended) {
        console.warn(`🚫 Access blocked to suspended profile: ${profile.username}`);
        return null;
      }

      console.log(`✅ Profile found for slug: ${slug}`);
      return await enrichProfileWithRelatedData(profile as UserProfile);
  } catch (error) {
    console.error('Error in _fetchProfileBySlug:', error);
    return null;
  }
}

/**
 * Get user profile by username with comprehensive error handling
 */
export async function getUserProfileByUsername(username: string): Promise<UserProfile | null> {
  try {
    // Validate username input
    const validatedUsername = UsernameSchema.parse(username);

    // Get cookies outside of cache scope
    const cookieStore = cookies();

    // Call the fetch function directly (temporarily removing cache to fix cookies issue)
    const profile = await _fetchProfileByUsername(validatedUsername, cookieStore);

    if (!profile) {
      return null;
    }

    // Fetch gaming and social profiles from separate tables
    try {
      const { getUserGamingProfiles, getUserSocialMediaProfiles } = await import('./actions-profiles');
      const [gamingProfiles, socialProfiles] = await Promise.all([
        getUserGamingProfiles(profile.id),
        getUserSocialMediaProfiles(profile.id)
      ]);

      // Add the profiles to the main profile object
      return {
        ...profile,
        gaming_profiles: gamingProfiles,
        social_profiles: socialProfiles
      };
    } catch (profilesError) {
      console.error('Error fetching gaming/social profiles:', profilesError);
      // Return profile without gaming/social profiles rather than failing completely
      return {
        ...profile,
        gaming_profiles: [],
        social_profiles: []
      };
    }
  } catch (error) {
    console.error('🚨 Error in getUserProfileByUsername for username:', username, 'Error:', error);
    return null;
  }
}

/**
 * Get user profile by slug with comprehensive error handling
 */
export async function getUserProfileBySlug(slug: string): Promise<UserProfile | null> {
  try {
    // Validate slug input (using same validation as username for consistency)
    const validatedSlug = UsernameSchema.parse(slug.toLowerCase());

    // Get cookies outside of cache scope
    const cookieStore = cookies();

    // Call the fetch function directly (temporarily removing cache to fix cookies issue)
    return await _fetchProfileBySlug(validatedSlug, cookieStore);
  } catch (error) {
    console.error('🚨 Error in getUserProfileBySlug for slug:', slug, 'Error:', error);
    return null;
  }
}

/**
 * Get user profile by slug or username with fallback logic
 * This handles the case where users try to access profiles using usernames that contain special characters
 */
export async function getUserProfileBySlugOrUsername(identifier: string): Promise<UserProfile | null> {
  try {
    console.log(`🔍 Looking up profile for identifier: "${identifier}"`);

    // Use cookies() properly for Next.js
    const supabase = await createServerClient();

    // Try direct database lookup first (bypassing cache and validation)
    // IMPORTANT: Include suspension fields to check access
    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        slug_lower,
        avatar_url,
        banner_url,
        bio,
        preferred_genres,
        favorite_consoles,
        theme,
        custom_colors,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        privacy_settings,
        suspended,
        suspension_reason,
        suspended_at,
        created_at,
        updated_at
      `)
      .or(`username.eq.${identifier},slug.eq.${identifier},slug_lower.eq.${identifier.toLowerCase()}`)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.warn(`❌ Profile not found for: ${identifier}`);
        return null;
      }
      console.error('🚨 Database error:', error);
      return null;
    }

    if (profile) {
      // SECURITY CHECK: Block access to suspended user profiles completely
      if (profile.suspended) {
        console.warn(`🚫 Access blocked to suspended profile: ${profile.username}`);
        return null; // Return null as if profile doesn't exist
      }

      console.log(`✅ Profile found for "${identifier}": ${profile.username}`);
      return await enrichProfileWithRelatedData(profile as UserProfile);
    }

    console.warn(`❌ No profile found for: ${identifier}`);
    return null;
  } catch (error) {
    console.error('🚨 Error in getUserProfileBySlugOrUsername:', error);
    return null;
  }
}

/**
 * Update user profile with validation and authorization
 */
export async function updateUserProfile(
  userId: string,
  updates: ProfileUpdateInput
): Promise<ProfileOperationResult<UserProfile>> {
  try {
    console.log('🔍 DEBUG: updateUserProfile called with userId:', userId);
    console.log('🔍 DEBUG: updateUserProfile profile updates:', JSON.stringify(updates, null, 2));

    // Validate input data
    console.log('🔍 DEBUG: Validating profile data with ProfileUpdateSchema');
    const validatedData = ProfileUpdateSchema.parse(updates);
    console.log('✅ DEBUG: Data validation passed');

    console.log('🔍 DEBUG: Creating Supabase client with cookies');
    const supabase = await createServerClient(await cookies());
    console.log('✅ DEBUG: Created Supabase client');

    // Verify user authentication and authorization
    console.log('🔍 DEBUG: Verifying user authentication');
    const { data: currentUser } = await supabase.auth.getUser();
    console.log('🔍 DEBUG: Current auth user:', currentUser?.user?.id || 'None');

    if (!currentUser.user) {
      console.error('❌ DEBUG: No authenticated user found');
      return {
        success: false,
        error: 'Unauthorized: No authenticated user found'
      };
    }

    if (currentUser.user.id !== userId) {
      console.error(`❌ DEBUG: Auth mismatch - current: ${currentUser.user.id}, target: ${userId}`);
      return {
        success: false,
        error: 'Unauthorized: Cannot update other user profiles'
      };
    }
    
    console.log('✅ DEBUG: User authorization verified');

    // Check suspension status
    console.log('🔍 DEBUG: Checking if user is suspended');
    await validateUserNotSuspended(userId);
    console.log('✅ DEBUG: User is not suspended');

    // Fetch current profile data to compare and for debugging
    const { data: currentProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (fetchError) {
      console.error('❌ DEBUG: Error fetching current profile:', fetchError);
    } else {
      console.log('✅ DEBUG: Current profile retrieved:', currentProfile.username);
    }

    // Update profile with timestamp
    console.log('🔍 DEBUG: Sending profile update to database');
    
    // Convert undefined back to null for database storage (undefined fields are omitted, null explicitly removes)
    const processedData = { ...validatedData } as any;
    if (updates.avatar_url === '') {
      console.log('🔍 DEBUG: Setting avatar_url to null for database (image removal)');
      processedData.avatar_url = null;
    }
    if (updates.banner_url === '') {
      console.log('🔍 DEBUG: Setting banner_url to null for database (image removal)');
      processedData.banner_url = null;
    }
    
    const updateData = {
      ...processedData,
      updated_at: new Date().toISOString()
    };
    console.log('🔍 DEBUG: Final update data for database:', JSON.stringify(updateData, null, 2));

    const { data: profile, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('❌ DEBUG: Error updating profile - code:', error.code);
      console.error('❌ DEBUG: Error updating profile - message:', error.message);
      console.error('❌ DEBUG: Error updating profile - details:', error.details);
      console.error('❌ DEBUG: Error updating profile - hint:', error.hint);
      
      return {
        success: false,
        error: `Failed to update profile: ${error.message || 'Please try again.'}`
      };
    }

    console.log('✅ DEBUG: Profile successfully updated');

    // Revalidate cache and paths
    console.log('🔍 DEBUG: Revalidating paths and tags');
    revalidatePath(`/u/${profile.slug}`);
    revalidateTag(`profile-${profile.username}`);
    revalidateTag(`profile-slug-${profile.slug}`);
    revalidateTag('profiles');
    console.log('✅ DEBUG: Cache revalidation complete');

    return {
      success: true,
      data: profile as UserProfile
    };
  } catch (error: any) {
    console.error('❌ DEBUG: Error in updateUserProfile:', error);
    console.error('❌ DEBUG: Error stack:', error.stack);

    // Handle suspension errors
    if (error.message && error.message.includes('suspended')) {
      console.error('❌ DEBUG: User suspension error detected');
      return {
        success: false,
        error: error.message,
        suspensionError: true as any
      };
    }

    // Handle validation errors
    if (error.name === 'ZodError') {
      console.error('❌ DEBUG: Zod validation error details:');
      console.error('❌ DEBUG: Raw error object:', error);
      console.error('❌ DEBUG: Formatted errors:', JSON.stringify(error.errors, null, 2));
      console.error('❌ DEBUG: Original updates data:', JSON.stringify(updates, null, 2));
      
      const detailedErrors = error.errors.map((err: any) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
        received: err.received,
        expected: err.expected
      }));
      
      return {
        success: false,
        error: `Invalid profile data: ${detailedErrors.map((e: any) => `${e.field}: ${e.message}`).join(', ')}`,
        validation_errors: detailedErrors
      };
    }

    return {
      success: false,
      error: `Failed to update profile: ${error.message || 'Unknown error'}`
    };
  }
}

/**
 * Check username availability with suggestions
 */
export async function checkUsernameAvailability(
  username: string,
  excludeUserId?: string
): Promise<UsernameAvailabilityResult> {
  try {
    // Validate username format
    const validatedUsername = UsernameSchema.parse(username);

    const supabase = await createServerClient(await cookies());

    // Build query
    let query = supabase
      .from('profiles')
      .select('username')
      .eq('username', validatedUsername);

    // Exclude current user if provided
    if (excludeUserId) {
      query = query.neq('id', excludeUserId);
    }

    const { data, error } = await query.single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking username availability:', error);
      return {
        available: false,
        error: 'Failed to check username availability'
      };
    }

    const available = !data; // Available if no data returned

    // Generate suggestions if username is not available
    let suggestions: UsernameSuggestion[] = [];
    if (!available) {
      suggestions = await generateUsernameSuggestions(validatedUsername);
    }

    return {
      available,
      suggestions: suggestions.length > 0 ? suggestions : undefined
    };
  } catch (error: any) {
    console.error('Error in checkUsernameAvailability:', error);

    // Handle validation errors (reserved usernames, format issues)
    if (error.name === 'ZodError') {
      return {
        available: false,
        reserved: true,
        error: error.errors[0]?.message || 'Invalid username format'
      };
    }

    return {
      available: false,
      error: 'Failed to check username availability'
    };
  }
}

/**
 * Generate username suggestions based on a desired username
 */
export async function generateUsernameSuggestions(
  baseUsername: string,
  maxSuggestions: number = 5
): Promise<UsernameSuggestion[]> {
  try {
    const suggestions: UsernameSuggestion[] = [];
    const supabase = await createServerClient(await cookies());

    // Generate variations
    const variations = [
      `${baseUsername}_gamer`,
      `${baseUsername}_player`,
      `${baseUsername}123`,
      `${baseUsername}2024`,
      `${baseUsername}_cp`, // CriticalPixel abbreviation
      `the_${baseUsername}`,
      `${baseUsername}_pro`,
      `${baseUsername}x`,
      `${baseUsername}_`,
      `${baseUsername}99`
    ];

    // Add random number variations
    for (let i = 0; i < 3; i++) {
      const randomNum = Math.floor(Math.random() * 9999) + 1;
      variations.push(`${baseUsername}${randomNum}`);
    }

    // Check availability for each variation
    for (const variation of variations) {
      if (suggestions.length >= maxSuggestions) break;

      try {
        // Validate the suggestion format
        UsernameSchema.parse(variation);

        const { data, error } = await supabase
          .from('profiles')
          .select('username')
          .eq('username', variation)
          .single();

        // If no data returned (PGRST116), username is available
        if (error && error.code === 'PGRST116') {
          suggestions.push({
            username: variation,
            available: true,
            similarity_score: calculateSimilarity(baseUsername, variation)
          });
        }
      } catch (validationError) {
        // Skip invalid suggestions
        continue;
      }
    }

    // Sort by similarity score (higher is better)
    suggestions.sort((a, b) => (b.similarity_score || 0) - (a.similarity_score || 0));

    return suggestions.slice(0, maxSuggestions);
  } catch (error) {
    console.error('Error generating username suggestions:', error);
    return [];
  }
}

/**
 * Calculate similarity score between two usernames
 */
function calculateSimilarity(original: string, suggestion: string): number {
  // Simple similarity calculation based on common characters and length
  const originalLower = original.toLowerCase();
  const suggestionLower = suggestion.toLowerCase();

  // Base score for containing the original username
  let score = suggestionLower.includes(originalLower) ? 0.8 : 0.3;

  // Bonus for shorter suggestions (more desirable)
  const lengthDiff = Math.abs(suggestion.length - original.length);
  score -= lengthDiff * 0.05;

  // Bonus for simpler additions (numbers vs words)
  if (suggestion.match(/^\w+\d+$/)) score += 0.1; // username123 format
  if (suggestion.match(/^\w+_\w+$/)) score += 0.05; // username_word format

  return Math.max(0, Math.min(1, score));
}

/**
 * Change username with slug update and validation
 */
export async function changeUsername(
  userId: string,
  newUsername: string,
  currentPassword: string
): Promise<ProfileOperationResult> {
  try {
    // Validate input
    const validatedData = UsernameChangeSchema.parse({
      username: newUsername,
      current_password: currentPassword
    });

    const supabase = createServerClient(cookies());

    // Verify authentication
    const { data: currentUser, error: authError } = await supabase.auth.getUser();
    if (authError || !currentUser.user || currentUser.user.id !== userId) {
      return { success: false, error: 'Unauthorized: Cannot change username for other users' };
    }

    // Check suspension status
    await validateUserNotSuspended(userId);

    // Check username availability
    const availability = await checkUsernameAvailability(newUsername, userId);
    if (!availability.available) {
      return {
        success: false,
        error: 'Username não está disponível'
      };
    }

    // Generate new slug
    const newSlug = newUsername.toLowerCase().replace(/[^a-z0-9]/g, '');

    // Update profile with new username and slug atomically
    const { data: profile, error } = await supabase
      .from('profiles')
      .update({
        username: validatedData.username,
        slug: newSlug,
        slug_lower: newSlug,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;

    // Revalidate cache for the new slug route
    revalidatePath(`/u/${newSlug}`);

    return { success: true, data: profile as UserProfile };
  } catch (error: any) {
    console.error('Error changing username:', error);

    // Handle suspension errors
    if (error.message && error.message.includes('suspended')) {
      return {
        success: false,
        error: error.message,
        suspensionError: true
      };
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: 'Dados inválidos: ' + error.errors.map(e => e.message).join(', ')
      };
    }
    return { success: false, error: 'Failed to change username' };
  }
}

/**
 * Create signed upload URL for profile images
 */
export async function createSignedUploadUrl(
  userId: string,
  fileName: string,
  fileType: 'avatar' | 'banner'
): Promise<{ success: boolean; signedUrl?: string; path?: string; error?: string }> {
  try {
    const supabase = await createServerClient(await cookies());

    // Verify authentication
    const { data: user } = await supabase.auth.getUser();
    if (!user.user || user.user.id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Generate unique file path
    const timestamp = Date.now();
    const fileExtension = fileName.split('.').pop()?.toLowerCase();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const path = `${fileType}s/${userId}/${timestamp}-${sanitizedFileName}`;

    // Create signed URL for upload
    const { data, error } = await supabase.storage
      .from('user_images')
      .createSignedUploadUrl(path, {
        upsert: true
      });

    if (error) {
      console.error('Error creating signed URL:', error);
      return { success: false, error: 'Failed to create upload URL' };
    }

    return {
      success: true,
      signedUrl: data.signedUrl,
      path: path
    };
  } catch (error) {
    console.error('Error in createSignedUploadUrl:', error);
    return { success: false, error: 'Failed to create upload URL' };
  }
}

/**
 * Update profile with new image URL after successful upload
 */
export async function updateProfileImage(
  userId: string,
  imagePath: string,
  imageType: 'avatar' | 'banner'
): Promise<ProfileOperationResult<{ url: string }>> {
  try {
    const supabase = createServerClient(cookies());

    // Verify authentication
    const { data: user } = await supabase.auth.getUser();
    if (!user.user || user.user.id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Check suspension status
    await validateUserNotSuspended(userId);

    // Get public URL for the uploaded image
    const { data: urlData } = supabase.storage
      .from('user_images')
      .getPublicUrl(imagePath);

    if (!urlData.publicUrl) {
      return { success: false, error: 'Failed to get image URL' };
    }

    // Update profile with new image URL
    const updateField = imageType === 'avatar' ? 'avatar_url' : 'banner_url';
    const { data: profile, error } = await supabase
      .from('profiles')
      .update({
        [updateField]: urlData.publicUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select('username, slug')
      .single();

    if (error) {
      console.error('Error updating profile image:', error);
      return { success: false, error: 'Failed to update profile' };
    }

    // Revalidate profile pages
    revalidatePath(`/u/${profile.slug}`);

    return {
      success: true,
      data: { url: urlData.publicUrl }
    };
  } catch (error: any) {
    console.error('Error in updateProfileImage:', error);

    // Handle suspension errors
    if (error.message && error.message.includes('suspended')) {
      return {
        success: false,
        error: error.message,
        suspensionError: true
      };
    }

    return { success: false, error: 'Failed to update profile image' };
  }
}

/**
 * Clean up old profile images when a new one is uploaded
 */
export async function cleanupOldProfileImages(
  userId: string,
  imageType: 'avatar' | 'banner',
  currentImageUrl?: string
): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
  try {
    const supabase = createServerClient(cookies());

    // Verify authentication
    const { data: user } = await supabase.auth.getUser();
    if (!user.user || user.user.id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // List all files in user's image folder
    const folderPath = `${imageType}s/${userId}/`;
    const { data: files, error: listError } = await supabase.storage
      .from('user_images')
      .list(folderPath);

    if (listError) {
      console.error('Error listing files:', listError);
      return { success: false, error: 'Failed to list files' };
    }

    if (!files || files.length === 0) {
      return { success: true, deletedCount: 0 };
    }

    // Extract current image filename from URL if provided
    let currentImageName: string | null = null;
    if (currentImageUrl) {
      const urlParts = currentImageUrl.split('/');
      currentImageName = urlParts[urlParts.length - 1];
    }

    // Filter files to delete (keep only the most recent and current)
    const filesToDelete = files
      .filter(file => file.name !== currentImageName) // Don't delete current image
      .sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime()) // Sort by creation date
      .slice(2); // Keep 2 most recent files, delete the rest

    if (filesToDelete.length === 0) {
      return { success: true, deletedCount: 0 };
    }

    // Delete old files
    const filePaths = filesToDelete.map(file => `${folderPath}${file.name}`);
    const { error: deleteError } = await supabase.storage
      .from('user_images')
      .remove(filePaths);

    if (deleteError) {
      console.error('Error deleting files:', deleteError);
      return { success: false, error: 'Failed to delete old files' };
    }

    console.log(`Cleaned up ${filesToDelete.length} old ${imageType} files for user ${userId}`);
    return { success: true, deletedCount: filesToDelete.length };

  } catch (error) {
    console.error('Error in cleanupOldProfileImages:', error);
    return { success: false, error: 'Failed to cleanup old images' };
  }
}

/**
 * Get storage usage statistics for a user
 */
export async function getUserStorageStats(
  userId: string
): Promise<{ success: boolean; stats?: { totalFiles: number; totalSize: number; avatars: number; banners: number }; error?: string }> {
  try {
    const supabase = createServerClient(cookies());

    // Verify authentication
    const { data: user } = await supabase.auth.getUser();
    if (!user.user || user.user.id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Get avatar files
    const { data: avatarFiles, error: avatarError } = await supabase.storage
      .from('user_images')
      .list(`avatars/${userId}/`);

    // Get banner files
    const { data: bannerFiles, error: bannerError } = await supabase.storage
      .from('user_images')
      .list(`banners/${userId}/`);

    if (avatarError || bannerError) {
      console.error('Error getting storage stats:', avatarError || bannerError);
      return { success: false, error: 'Failed to get storage statistics' };
    }

    const allFiles = [...(avatarFiles || []), ...(bannerFiles || [])];
    const totalSize = allFiles.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);

    return {
      success: true,
      stats: {
        totalFiles: allFiles.length,
        totalSize,
        avatars: avatarFiles?.length || 0,
        banners: bannerFiles?.length || 0
      }
    };

  } catch (error) {
    console.error('Error in getUserStorageStats:', error);
    return { success: false, error: 'Failed to get storage statistics' };
  }
}

/**
 * Internal function to fetch similar profiles from database
 */
async function _fetchSimilarProfiles(searchTerm: string, cookieStore?: any): Promise<UserProfile[]> {
  try {
    const supabase = createServerClient(cookieStore);

    // Search for profiles with similar usernames or display names
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        avatar_url,
        bio,
        level,
        review_count,
        privacy_settings,
        suspended
      `)
      .or(`username.ilike.%${searchTerm}%,display_name.ilike.%${searchTerm}%`)
      .limit(6);

    if (error) {
      console.error('Error fetching similar profiles:', error);
      return [];
    }

    // Filter out private profiles and suspended users
    return (profiles || [])
      .filter(profile => {
        // Block suspended users
        if (profile.suspended) return false;
        
        const privacy = profile.privacy_settings as any;
        return !privacy || privacy.profile_visibility !== 'private';
      })
      .map(profile => profile as UserProfile);
  } catch (error) {
    console.error('Error in _fetchSimilarProfiles:', error);
    return [];
  }
}

/**
 * Get similar profiles for 404 suggestions with caching
 */
export async function getSimilarProfiles(searchTerm: string): Promise<UserProfile[]> {
  if (!searchTerm.trim()) return [];

  const cookieStore = cookies();

  const cachedFetch = unstable_cache(
    async (term: string, cookieStore: any) => _fetchSimilarProfiles(term, cookieStore),
    [`similar-profiles-${searchTerm.toLowerCase()}`],
    {
      revalidate: 600, // Cache for 10 minutes
      tags: ['profiles', 'similar-profiles']
    }
  );

  return await cachedFetch(searchTerm, cookieStore);
}

/**
 * Internal function to fetch featured profiles from database
 */
async function _fetchFeaturedProfiles(cookieStore?: any): Promise<UserProfile[]> {
  try {
    const supabase = createServerClient(cookieStore);

    // Get profiles with high review counts and public visibility
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        avatar_url,
        bio,
        level,
        review_count,
        privacy_settings,
        suspended
      `)
      .gte('review_count', 1)
      .order('review_count', { ascending: false })
      .limit(8);

    if (error) {
      console.error('Error fetching featured profiles:', error);
      return [];
    }

    // Filter out private profiles and suspended users
    return (profiles || [])
      .filter(profile => {
        // Block suspended users
        if (profile.suspended) return false;
        
        const privacy = profile.privacy_settings as any;
        return !privacy || privacy.profile_visibility !== 'private';
      })
      .map(profile => profile as UserProfile);
  } catch (error) {
    console.error('Error in _fetchFeaturedProfiles:', error);
    return [];
  }
}

/**
 * Get popular/featured profiles for 404 page with caching
 */
export async function getFeaturedProfiles(): Promise<UserProfile[]> {
  const cookieStore = cookies();

  const cachedFetch = unstable_cache(
    async (cookieStore: any) => _fetchFeaturedProfiles(cookieStore),
    ['featured-profiles'],
    {
      revalidate: 1800, // Cache for 30 minutes
      tags: ['profiles', 'featured-profiles']
    }
  );

  return await cachedFetch(cookieStore);
}

/**
 * Prefetch related profile data for better performance
 */
export async function prefetchProfileData(slug: string): Promise<void> {
  try {
    // Prefetch the main profile by slug or username (with fallback)
    const profilePromise = getUserProfileBySlugOrUsername(slug);

    // Prefetch similar profiles for potential 404 suggestions
    const similarPromise = getSimilarProfiles(slug);

    // Prefetch featured profiles
    const featuredPromise = getFeaturedProfiles();

    // Execute all prefetch operations in parallel
    await Promise.allSettled([
      profilePromise,
      similarPromise,
      featuredPromise
    ]);
  } catch (error) {
    // Prefetch failures should not affect the main functionality
    console.warn('Prefetch failed:', error);
  }
}

/**
 * Batch fetch multiple profiles for better performance
 */
export async function batchGetProfiles(usernames: string[]): Promise<(UserProfile | null)[]> {
  try {
    const supabase = await createServerClient(await cookies());

    // Validate all usernames
    const validatedUsernames = usernames
      .map(username => {
        try {
          return UsernameSchema.parse(username);
        } catch {
          return null;
        }
      })
      .filter(Boolean) as string[];

    if (validatedUsernames.length === 0) {
      return [];
    }

    // Batch fetch from database
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        slug,
        slug_lower,
        avatar_url,
        banner_url,
        bio,
        website,
        location,
        preferred_genres,
        favorite_consoles,
        theme,
        custom_colors,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        privacy_settings,
        suspended,
        created_at,
        updated_at
      `)
      .in('username', validatedUsernames);

    if (error) {
      console.error('Error batch fetching profiles:', error);
      return usernames.map(() => null);
    }

    // Map results back to original order, filtering out suspended users
    return usernames.map(username => {
      const profile = profiles?.find(p => p.username === username);
      // Block suspended users
      if (profile && profile.suspended) return null;
      return profile ? (profile as UserProfile) : null;
    });
  } catch (error) {
    console.error('Error in batchGetProfiles:', error);
    return usernames.map(() => null);
  }
}

/**
 * Set a review as featured for a user's profile
 */
export async function setFeaturedReview(
  userId: string,
  reviewId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🎯 setFeaturedReview called with:', { userId, reviewId });
    
    const supabase = await createServerClient();
    console.log('✅ Supabase client created');

    // Check if user is suspended
    console.log('🔍 Checking user suspension status...');
    try {
      await validateUserNotSuspended(userId);
      console.log('✅ User is not suspended');
    } catch (error) {
      console.log('❌ User is suspended:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'User is suspended' 
      };
    }

    // First, remove featured status from all other reviews by this user
    console.log('🔄 Removing featured status from all existing reviews...');
    const { error: unfeaturedError } = await supabase
      .from('reviews')
      .update({ is_featured: false })
      .eq('author_id', userId);

    if (unfeaturedError) {
      console.error('❌ Error removing featured status from existing reviews:', unfeaturedError);
      return { success: false, error: 'Failed to update existing featured reviews' };
    }
    console.log('✅ Successfully removed featured status from existing reviews');

    // Then set the new review as featured
    console.log('⭐ Setting new review as featured:', reviewId);
    const { error: featuredError } = await supabase
      .from('reviews')
      .update({ is_featured: true })
      .eq('id', reviewId)
      .eq('author_id', userId); // Security: ensure user owns the review

    if (featuredError) {
      console.error('❌ Error setting review as featured:', featuredError);
      return { success: false, error: 'Failed to set review as featured' };
    }
    console.log('✅ Successfully set review as featured');

    // Revalidate user's profile pages
    revalidatePath(`/u/[slug]`, 'page');
    revalidateTag(`profile-${userId}`);

    return { success: true };
  } catch (error) {
    console.error('Error in setFeaturedReview:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

/**
 * Remove featured status from a user's review
 */
export async function removeFeaturedReview(
  userId: string,
  reviewId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createServerClient();

    // Check if user is suspended
    try {
      await validateUserNotSuspended(userId);
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'User is suspended' 
      };
    }

    let query = supabase
      .from('reviews')
      .update({ is_featured: false })
      .eq('author_id', userId);

    // If specific review ID provided, only remove from that review
    if (reviewId) {
      query = query.eq('id', reviewId);
    }

    const { error } = await query;

    if (error) {
      console.error('Error removing featured status:', error);
      return { success: false, error: 'Failed to remove featured status' };
    }

    // Revalidate user's profile pages
    revalidatePath(`/u/[slug]`, 'page');
    revalidateTag(`profile-${userId}`);

    return { success: true };
  } catch (error) {
    console.error('Error in removeFeaturedReview:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}
