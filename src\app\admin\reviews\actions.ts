'use server';

// FORTRESS-LEVEL ADMIN REVIEW ACTIONS
// Microsoft Senior Security Specialist Implementation  
// Date: 12/01/2025
// Classification: TOP SECRET
// SECURITY: Enhanced multi-layer authentication and authorization for reviews

import { createServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import {
  verifyAdminSessionEnhanced,
  logSecurityEvent,
} from '@/lib/admin/security';
import {
  CriticalOperation,
  AdminPermissionLevel
} from '@/lib/admin/security-utils';

// Review-specific types with enhanced security metadata
export interface SecureReviewModerationData {
  id: string;
  title: string;
  slug: string;
  game_name: string;
  author_id: string;
  author_name: string;
  status: 'draft' | 'published' | 'pending' | 'flagged' | 'archived';
  overall_score: number;
  content_lexical: any;
  created_at: string;
  updated_at: string;
  publish_date?: string;
  flag_count?: number;
  moderation_notes?: string;
  last_moderated_by?: string;
  last_moderated_at?: string;
  platforms?: string[];
  tags?: string[];
  view_count?: number;
  like_count?: number;
  comment_count?: number;
  is_featured?: boolean;
  is_blocked?: boolean;
}

export interface SecureModerationAction {
  action: 'approve' | 'reject' | 'flag' | 'feature' | 'unfeature' | 'archive' | 'block' | 'unblock';
  reason?: string;
  notes?: string;
  justification?: string;
}

// INTERFACE PARA REPORTS/FLAGS DE CONTEÚDO
export interface ContentFlag {
  id: string;
  content_id: string;
  content_type: 'review' | 'comment';
  reporter_id: string;
  reporter_name?: string;
  reporter_username?: string;
  reason: string;
  description?: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
  resolved_by?: string;
  resolved_at?: string;
  review_title?: string;
  review_slug?: string;
}

export interface ReportModerationAction {
  action: 'resolve' | 'dismiss';
  notes?: string;
  justification?: string;
}

// FORTRESS-LEVEL REVIEW LIST RETRIEVAL WITH ENHANCED SECURITY
export async function getReviewsListSecure(
  options: {
    status?: string[];
    page?: number;
    limit?: number;
    sortBy?: 'created_at' | 'updated_at' | 'flag_count';
    sortOrder?: 'asc' | 'desc';
    search?: string;
  } = {}
): Promise<{
  reviews: SecureReviewModerationData[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for content moderation
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    const {
      status = ['pending', 'flagged', 'published'],
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search = ''
    } = options;

    const sanitizedPage = Math.max(1, Math.min(page, 1000)); // Max 1000 pages
    const sanitizedLimit = Math.max(1, Math.min(limit, 100)); // Max 100 reviews per page
    const sanitizedSearch = search.replace(/[<>'"]/g, ''); // SQL injection prevention
    
    // SECURITY LAYER 3: Log data access attempt
    await logSecurityEvent('ADMIN_REVIEW_LIST_ACCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      status: status,
      search: sanitizedSearch,
      sortBy,
      sortOrder,
      permissionLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 4: Data minimization - only select necessary fields (safe columns only)
    const allowedFields = [
      'id',
      'title',
      'slug',
      'game_name',
      'author_id',
      'author_name',
      'status',
      'overall_score',
      'content_lexical',
      'created_at',
      'updated_at',
      'publish_date',
      'platforms',
      'tags',
      'view_count',
      'like_count',
      'comment_count',
      'is_featured',
      'moderation_notes',
      'last_moderated_by',
      'last_moderated_at'
    ];

    // SECURITY LAYER 5: Build secure query with RLS enforcement
    let query = supabase
      .from('reviews')
      .select(`
        ${allowedFields.join(', ')},
        profiles:author_id (
          username,
          display_name,
          avatar_url
        )
      `, { count: 'exact' });

    // SECURITY LAYER 6: Secure filter application with input validation
    if (status.length > 0) {
      // Validate status values to prevent injection
      const allowedStatuses = ['draft', 'published', 'pending', 'flagged', 'archived'];
      const validStatuses = status.filter(s => allowedStatuses.includes(s));
      if (validStatuses.length > 0) {
        query = query.in('status', validStatuses as any); // as any para evitar erro de tipagem do Supabase
      }
    }

    // Apply search filter with sanitization
    if (sanitizedSearch.length >= 2) {
      query = query.or(`title.ilike.%${sanitizedSearch}%,game_name.ilike.%${sanitizedSearch}%,author_name.ilike.%${sanitizedSearch}%`);
    }

    // Apply secure sorting with validation
    const allowedSortFields = ['created_at', 'updated_at', 'flag_count'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'desc';
    query = query.order(validSortBy, { ascending: validSortOrder === 'asc' });

    // SECURITY LAYER 7: Secure pagination with bounds checking
    const offset = (sanitizedPage - 1) * sanitizedLimit;
    query = query.range(offset, offset + sanitizedLimit - 1);

    const { data, error, count } = await query;

    if (error) {
      await logSecurityEvent('ADMIN_REVIEW_LIST_ERROR', adminVerification.adminId, {
        error: error.message,
        options: options
      });
      throw new Error(`Failed to fetch reviews: ${error.message}`);
    }

    // SECURITY LAYER 8: Data sanitization and transformation
    const reviews: SecureReviewModerationData[] = (data || []).map((review: any) => ({
      id: review.id,
      title: review.title,
      slug: review.slug,
      game_name: review.game_name,
      author_id: review.author_id,
      author_name: review.author_name,
      status: review.status,
      overall_score: review.overall_score * 10, // Convert to 0-100 scale
      content_lexical: review.content_lexical,
      created_at: review.created_at,
      updated_at: review.updated_at,
      publish_date: review.publish_date,
      platforms: review.platforms || [],
      tags: review.tags || [],
      view_count: review.view_count || 0,
      like_count: review.like_count || 0,
      comment_count: review.comment_count || 0,
      is_featured: review.is_featured || false,
      flag_count: review.flag_count || 0, // Now using real flag_count from database
      moderation_notes: review.moderation_notes || '',
      last_moderated_by: review.last_moderated_by || '',
      last_moderated_at: review.last_moderated_at || '',
      is_blocked: review.is_blocked || false
    }));

    const total = count || 0;
    const totalPages = Math.ceil(total / sanitizedLimit);

    // SECURITY LAYER 9: Enhanced audit logging
    await logSecurityEvent('ADMIN_REVIEW_LIST_SUCCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      status: status,
      search: sanitizedSearch,
      total: total,
      returned: reviews.length,
      permissionLevel: adminVerification.permissionLevel
    });

    return {
      reviews,
      total,
      page: sanitizedPage,
      limit: sanitizedLimit,
      totalPages
    };

  } catch (error) {
    console.error('getReviewsListSecure error:', error);
    throw error;
  }
}

// FORTRESS-LEVEL REVIEW MODERATION WITH ENHANCED SECURITY
export async function moderateReviewSecure(
  reviewId: string,
  action: SecureModerationAction
): Promise<{ success: boolean; error?: string }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for content moderation
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    if (!reviewId || typeof reviewId !== 'string') {
      throw new Error('INVALID_REVIEW_ID');
    }

    const sanitizedAction = action.action?.trim()?.toLowerCase();
    if (!sanitizedAction) {
      throw new Error('INVALID_ACTION');
    }

    // SECURITY LAYER 3: Action validation with permission checks
    const allowedActions = ['approve', 'reject', 'flag', 'feature', 'unfeature', 'archive', 'block', 'unblock'];
    if (!allowedActions.includes(sanitizedAction)) {
      await logSecurityEvent('INVALID_MODERATION_ACTION', adminVerification.adminId, {
        reviewId,
        attemptedAction: sanitizedAction,
        allowedActions
      });
      throw new Error(`Invalid action. Only ${allowedActions.join(', ')} actions are allowed.`);
    }

    // SECURITY LAYER 4: Permission level validation for sensitive actions
    if (sanitizedAction === 'feature' || sanitizedAction === 'unfeature') {
      if (adminVerification.permissionLevel === AdminPermissionLevel.VIEWER) {
        await logSecurityEvent('INSUFFICIENT_PERMISSIONS_FEATURE', adminVerification.adminId, {
          reviewId,
          action: sanitizedAction,
          adminLevel: adminVerification.permissionLevel
        });
        throw new Error('INSUFFICIENT_PRIVILEGES_FOR_FEATURE_ACTION');
      }
    }

    // SECURITY LAYER 5: Get current review state for audit trail
    const { data: currentReview, error: fetchError } = await supabase
      .from('reviews')
      .select('title, game_name, author_name, status, is_featured, is_blocked, slug')
      .eq('id', reviewId as any)
      .single();

    if (fetchError || !currentReview || typeof currentReview !== 'object') {
      await logSecurityEvent('TARGET_REVIEW_FETCH_FAILED', adminVerification.adminId, {
        reviewId,
        error: fetchError?.message || 'Not found'
      });
      throw new Error('TARGET_REVIEW_NOT_FOUND');
    }

    // SECURITY LAYER 6: Prepare secure update data with atomic transaction
    const updateData: any = {
      updated_at: new Date().toISOString(),
      last_moderated_by: adminVerification.adminId,
      last_moderated_at: new Date().toISOString()
    };

    // Add moderation notes if provided
    if (action.notes && action.notes.trim()) {
      updateData.moderation_notes = action.notes.trim().substring(0, 1000); // Limit length
    }

    // Apply action-specific changes
    switch (sanitizedAction) {
      case 'approve':
        updateData.status = 'published';
        updateData.publish_date = new Date().toISOString();
        updateData.is_blocked = false;
        break;
      case 'reject':
        updateData.status = 'draft';
        updateData.is_blocked = false;
        break;
      case 'flag':
        updateData.status = 'flagged';
        updateData.is_blocked = false;
        break;
      case 'feature':
        updateData.is_featured = true;
        break;
      case 'unfeature':
        updateData.is_featured = false;
        break;
      case 'archive':
        updateData.status = 'archived';
        updateData.is_blocked = false;
        break;
      case 'block':
        updateData.is_blocked = true;
        break;
      case 'unblock':
        updateData.is_blocked = false;
        break;
    }

    // SECURITY LAYER 7: Execute secure update with atomic transaction
    const { error } = await supabase
      .from('reviews')
      .update(updateData)
      .eq('id', reviewId);

    if (error) {
      await logSecurityEvent('REVIEW_MODERATION_FAILED', adminVerification.adminId, {
        reviewId,
        action: sanitizedAction,
        error: error.message
      });
      throw new Error(`Failed to moderate review: ${error.message}`);
    }

    // SECURITY LAYER 8: Enhanced audit logging with complete context
    await logSecurityEvent('REVIEW_MODERATION_SUCCESS', adminVerification.adminId, {
      reviewId,
      reviewTitle: (currentReview as any).title,
      gameName: (currentReview as any).game_name,
      authorName: (currentReview as any).author_name,
      previousStatus: (currentReview as any).status,
      newStatus: updateData.status || (currentReview as any).status,
      previousFeatured: (currentReview as any).is_featured,
      newFeatured: updateData.is_featured !== undefined ? updateData.is_featured : (currentReview as any).is_featured,
      previousBlocked: (currentReview as any).is_blocked,
      newBlocked: updateData.is_blocked !== undefined ? updateData.is_blocked : (currentReview as any).is_blocked,
      action: sanitizedAction,
      notes: action.notes,
      justification: action.justification,
      adminLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 9: Cache invalidation and notifications
    let reviewSlug = '';
    if ((currentReview as any).slug) {
      reviewSlug = (currentReview as any).slug;
    } else {
      // Buscar slug se não estiver presente
      const { data: reviewAfter, error: errorAfter } = await supabase
        .from('reviews')
        .select('slug')
        .eq('id', reviewId as any)
        .single();
      if (!errorAfter && reviewAfter && reviewAfter.slug) {
        reviewSlug = reviewAfter.slug;
      }
    }
    revalidatePath('/admin/reviews');
    revalidatePath(`/reviews/view/${reviewSlug || reviewId}`);

    // Log success for monitoring
    console.log(`🔒 SECURITY: Review moderated successfully - ${(currentReview as any).title} (${sanitizedAction}) by ${adminVerification.adminId}`);

    return { success: true };

  } catch (error: any) {
    await logSecurityEvent('REVIEW_MODERATION_ERROR', '', {
      reviewId,
      action: action.action,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: moderateReviewSecure failed:', error);
    return { success: false, error: error.message };
  }
}

// FORTRESS-LEVEL SINGLE REVIEW RETRIEVAL FOR ADMIN
export async function getReviewForAdminSecure(
  reviewId: string
): Promise<{ review?: SecureReviewModerationData; error?: string }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation
    if (!reviewId || typeof reviewId !== 'string') {
      throw new Error('INVALID_REVIEW_ID');
    }

    // SECURITY LAYER 3: Log access attempt
    await logSecurityEvent('ADMIN_REVIEW_DETAIL_ACCESS', adminVerification.adminId, {
      reviewId,
      permissionLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 4: Secure data retrieval with data minimization
    const { data: review, error } = await supabase
      .from('reviews')
      .select(`
        id,
        title,
        slug,
        game_name,
        author_id,
        author_name,
        status,
        overall_score,
        content_lexical,
        created_at,
        updated_at,
        publish_date,
        platforms,
        tags,
        view_count,
        like_count,
        comment_count,
        is_featured,
        moderation_notes,
        last_moderated_by,
        last_moderated_at,
        profiles:author_id (
          username,
          display_name,
          avatar_url
        )
      `)
      .eq('id', reviewId)
      .single();

    if (error) {
      await logSecurityEvent('ADMIN_REVIEW_DETAIL_ERROR', adminVerification.adminId, {
        reviewId,
        error: error.message
      });
      return { error: error.message };
    }

    // SECURITY LAYER 5: Data sanitization and transformation
    const secureReview: SecureReviewModerationData = {
      id: review.id,
      title: review.title,
      slug: review.slug,
      game_name: review.game_name,
      author_id: review.author_id,
      author_name: review.author_name,
      status: review.status,
      overall_score: review.overall_score * 10, // Convert to 0-100 scale
      content_lexical: review.content_lexical,
      created_at: review.created_at,
      updated_at: review.updated_at,
      publish_date: review.publish_date,
      platforms: review.platforms || [],
      tags: review.tags || [],
      view_count: review.view_count || 0,
      like_count: review.like_count || 0,
      comment_count: review.comment_count || 0,
      is_featured: review.is_featured || false,
      flag_count: 0, // TODO: Implement flag counting
      moderation_notes: review.moderation_notes || '',
      last_moderated_by: review.last_moderated_by || '',
      last_moderated_at: review.last_moderated_at || '',
      is_blocked: review.is_blocked || false
    };

    // SECURITY LAYER 6: Audit logging
    await logSecurityEvent('ADMIN_REVIEW_DETAIL_SUCCESS', adminVerification.adminId, {
      reviewId,
      reviewTitle: review.title,
      permissionLevel: adminVerification.permissionLevel
    });

    return { review: secureReview };

  } catch (error: any) {
    await logSecurityEvent('ADMIN_REVIEW_DETAIL_ERROR', '', {
      reviewId,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: getReviewForAdminSecure failed:', error);
    return { error: error.message };
  }
}

// FORTRESS-LEVEL BATCH REVIEW MODERATION
export async function batchModerateReviewsSecure(
  reviewIds: string[],
  action: SecureModerationAction
): Promise<{ success: boolean; processed: number; errors: string[] }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for batch operations
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.BULK_CONTENT_UPDATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    // SECURITY LAYER 2: Input validation and limits
    if (!Array.isArray(reviewIds) || reviewIds.length === 0) {
      throw new Error('INVALID_REVIEW_IDS');
    }

    if (reviewIds.length > 50) { // Limit batch size for security
      throw new Error('BATCH_SIZE_EXCEEDED');
    }

    // SECURITY LAYER 3: Log batch operation attempt
    await logSecurityEvent('ADMIN_BATCH_MODERATION_ATTEMPT', adminVerification.adminId, {
      reviewCount: reviewIds.length,
      action: action.action,
      permissionLevel: adminVerification.permissionLevel
    });

    const results = {
      success: true,
      processed: 0,
      errors: [] as string[]
    };

    // SECURITY LAYER 4: Process each review with individual security checks
    for (const reviewId of reviewIds) {
      try {
        const result = await moderateReviewSecure(reviewId, action);
        if (result.success) {
          results.processed++;
        } else {
          results.errors.push(`Review ${reviewId}: ${result.error}`);
        }
      } catch (error: any) {
        results.errors.push(`Review ${reviewId}: ${error.message}`);
      }
    }

    // SECURITY LAYER 5: Final audit logging
    await logSecurityEvent('ADMIN_BATCH_MODERATION_COMPLETE', adminVerification.adminId, {
      requestedCount: reviewIds.length,
      processedCount: results.processed,
      errorCount: results.errors.length,
      action: action.action,
      permissionLevel: adminVerification.permissionLevel
    });

    return results;

  } catch (error: any) {
    await logSecurityEvent('ADMIN_BATCH_MODERATION_ERROR', '', {
      reviewIds,
      action: action.action,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: batchModerateReviewsSecure failed:', error);
    return { success: false, processed: 0, errors: [error.message] };
  }
}

// FORTRESS-LEVEL CONTENT FLAGS MANAGEMENT WITH ENHANCED SECURITY

// BUSCAR REPORTS DE UMA REVIEW ESPECÍFICA
export async function getReviewReportsSecure(
  reviewId: string
): Promise<{ reports: ContentFlag[]; error?: string }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation
    if (!reviewId || typeof reviewId !== 'string') {
      throw new Error('INVALID_REVIEW_ID');
    }

    // SECURITY LAYER 3: Log access attempt
    await logSecurityEvent('ADMIN_REVIEW_REPORTS_ACCESS', adminVerification.adminId, {
      reviewId,
      permissionLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 4: Secure data retrieval
    const { data: reports, error } = await supabase
      .from('content_flags')
      .select(`
        id,
        content_id,
        content_type,
        reporter_id,
        reason,
        description,
        status,
        created_at,
        resolved_by,
        resolved_at,
        profiles:reporter_id (
          username,
          display_name
        ),
        reviews:content_id (
          title,
          slug
        )
      `)
      .eq('content_id', reviewId)
      .eq('content_type', 'review')
      .order('created_at', { ascending: false });

    if (error) {
      await logSecurityEvent('ADMIN_REVIEW_REPORTS_ERROR', adminVerification.adminId, {
        reviewId,
        error: error.message
      });
      return { reports: [], error: error.message };
    }

    // SECURITY LAYER 5: Data transformation
    const secureReports: ContentFlag[] = (reports || []).map((report: any) => ({
      id: report.id,
      content_id: report.content_id,
      content_type: report.content_type,
      reporter_id: report.reporter_id,
      reporter_name: report.profiles?.display_name || 'Unknown User',
      reporter_username: report.profiles?.username || 'unknown',
      reason: report.reason,
      description: report.description || '',
      status: report.status,
      created_at: report.created_at,
      resolved_by: report.resolved_by,
      resolved_at: report.resolved_at,
      review_title: report.reviews?.title || 'Unknown Review',
      review_slug: report.reviews?.slug || ''
    }));

    // SECURITY LAYER 6: Audit logging
    await logSecurityEvent('ADMIN_REVIEW_REPORTS_SUCCESS', adminVerification.adminId, {
      reviewId,
      reportCount: secureReports.length,
      permissionLevel: adminVerification.permissionLevel
    });

    return { reports: secureReports };

  } catch (error: any) {
    await logSecurityEvent('ADMIN_REVIEW_REPORTS_ERROR', '', {
      reviewId,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: getReviewReportsSecure failed:', error);
    return { reports: [], error: error.message };
  }
}

// BUSCAR TODOS OS REPORTS DO SISTEMA COM PAGINAÇÃO
export async function getAllReportsSecure(
  options: {
    status?: string[];
    page?: number;
    limit?: number;
    sortBy?: 'created_at' | 'resolved_at';
    sortOrder?: 'asc' | 'desc';
    search?: string;
  } = {}
): Promise<{
  reports: ContentFlag[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    const {
      status = ['pending'],
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search = ''
    } = options;

    const sanitizedPage = Math.max(1, Math.min(page, 1000));
    const sanitizedLimit = Math.max(1, Math.min(limit, 100));
    const sanitizedSearch = search.replace(/[<>'"]/g, '');

    // SECURITY LAYER 3: Log access attempt
    await logSecurityEvent('ADMIN_ALL_REPORTS_ACCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      status: status,
      search: sanitizedSearch,
      permissionLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 4: Build secure query - ONLY REVIEW REPORTS (not comment reports)
    let query = supabase
      .from('content_flags')
      .select(`
        *
      `, { count: 'exact' })
      .eq('content_type', 'review'); // ONLY review reports, comment reports go to user dashboards

    // Apply status filter
    if (status.length > 0) {
      const allowedStatuses = ['pending', 'resolved', 'dismissed'];
      const validStatuses = status.filter(s => allowedStatuses.includes(s));
      if (validStatuses.length > 0) {
        query = query.in('status', validStatuses);
      }
    }

    // Apply search filter
    if (sanitizedSearch.length >= 2) {
      query = query.or(`reason.ilike.%${sanitizedSearch}%,description.ilike.%${sanitizedSearch}%`);
    }

    // Apply sorting
    const allowedSortFields = ['created_at', 'resolved_at'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['asc', 'desc'].includes(sortOrder) ? sortOrder : 'desc';
    query = query.order(validSortBy, { ascending: validSortOrder === 'asc' });

    // Apply pagination
    const offset = (sanitizedPage - 1) * sanitizedLimit;
    query = query.range(offset, offset + sanitizedLimit - 1);

    const { data, error, count } = await query;

    if (error) {
      await logSecurityEvent('ADMIN_ALL_REPORTS_ERROR', adminVerification.adminId, {
        error: error.message,
        options: options
      });
      throw new Error(`Failed to fetch reports: ${error.message}`);
    }

    // SECURITY LAYER 5: Data transformation with separate queries for related data
    const reports: ContentFlag[] = [];
    
    if (data && data.length > 0) {
      // Get unique reporter IDs and content IDs
      const reporterIds = [...new Set(data.map((r: any) => r.reporter_id))];
      const contentIds = [...new Set(data.map((r: any) => r.content_id))];
      
      // Fetch profiles data
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, username, display_name')
        .in('id', reporterIds);
      
      // Fetch reviews data
      const { data: reviews } = await supabase
        .from('reviews')
        .select('id, title, slug')
        .in('id', contentIds);
      
      // Create lookup maps
      const profilesMap = new Map(profiles?.map(p => [p.id, p]) || []);
      const reviewsMap = new Map(reviews?.map(r => [r.id, r]) || []);
      
      // Transform data
      for (const report of data) {
        const profile = profilesMap.get(report.reporter_id);
        const review = reviewsMap.get(report.content_id);
        
        reports.push({
          id: report.id,
          content_id: report.content_id,
          content_type: report.content_type,
          reporter_id: report.reporter_id,
          reporter_name: profile?.display_name || 'Unknown User',
          reporter_username: profile?.username || 'unknown',
          reason: report.reason,
          description: report.description || '',
          status: report.status,
          created_at: report.created_at,
          resolved_by: report.resolved_by,
          resolved_at: report.resolved_at,
          review_title: review?.title || 'Unknown Review',
          review_slug: review?.slug || ''
        });
      }
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / sanitizedLimit);

    // SECURITY LAYER 6: Audit logging
    await logSecurityEvent('ADMIN_ALL_REPORTS_SUCCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      status: status,
      search: sanitizedSearch,
      total: total,
      returned: reports.length,
      permissionLevel: adminVerification.permissionLevel
    });

    return {
      reports,
      total,
      page: sanitizedPage,
      limit: sanitizedLimit,
      totalPages
    };

  } catch (error: any) {
    console.error('getAllReportsSecure error:', error);
    throw error;
  }
}

// RESOLVER OU DESCARTAR UM REPORT
export async function resolveReportSecure(
  reportId: string,
  action: ReportModerationAction
): Promise<{ success: boolean; error?: string }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation
    if (!reportId || typeof reportId !== 'string') {
      throw new Error('INVALID_REPORT_ID');
    }

    const sanitizedAction = action.action?.trim()?.toLowerCase();
    const allowedActions = ['resolve', 'dismiss'];
    if (!allowedActions.includes(sanitizedAction)) {
      throw new Error('INVALID_REPORT_ACTION');
    }

    // SECURITY LAYER 3: Get current report state
    const { data: currentReport, error: fetchError } = await supabase
      .from('content_flags')
      .select('id, content_id, reporter_id, reason, status')
      .eq('id', reportId)
      .single();

    if (fetchError) {
      await logSecurityEvent('REPORT_RESOLUTION_FETCH_FAILED', adminVerification.adminId, {
        reportId,
        error: fetchError.message
      });
      throw new Error('REPORT_NOT_FOUND');
    }

    // SECURITY LAYER 4: Prepare update data
    const updateData: any = {
      status: sanitizedAction === 'resolve' ? 'resolved' : 'dismissed',
      resolved_by: adminVerification.adminId,
      resolved_at: new Date().toISOString()
    };

    // SECURITY LAYER 5: Execute update
    const { error } = await supabase
      .from('content_flags')
      .update(updateData)
      .eq('id', reportId);

    if (error) {
      await logSecurityEvent('REPORT_RESOLUTION_FAILED', adminVerification.adminId, {
        reportId,
        action: sanitizedAction,
        error: error.message
      });
      throw new Error(`Failed to ${sanitizedAction} report: ${error.message}`);
    }

    // SECURITY LAYER 6: Enhanced audit logging
    await logSecurityEvent('REPORT_RESOLUTION_SUCCESS', adminVerification.adminId, {
      reportId,
      contentId: currentReport.content_id,
      reporterId: currentReport.reporter_id,
      reason: currentReport.reason,
      previousStatus: currentReport.status,
      newStatus: updateData.status,
      action: sanitizedAction,
      notes: action.notes,
      justification: action.justification,
      adminLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 7: Cache invalidation
    revalidatePath('/admin/reviews');
    revalidatePath('/admin/reports');

    console.log(`🔒 SECURITY: Report ${sanitizedAction}d successfully - ${reportId} by ${adminVerification.adminId}`);

    return { success: true };

  } catch (error: any) {
    await logSecurityEvent('REPORT_RESOLUTION_ERROR', '', {
      reportId,
      action: action.action,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: resolveReportSecure failed:', error);
    return { success: false, error: error.message };
  }
}

// ATUALIZAR FLAG COUNT REAL DE UMA REVIEW
export async function updateReviewFlagCountSecure(
  reviewId: string
): Promise<{ success: boolean; flagCount: number; error?: string }> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.CONTENT_MODERATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Count pending flags
    const { data: flagsData, error: flagsError } = await supabase
      .from('content_flags')
      .select('id', { count: 'exact', head: true })
      .eq('content_id', reviewId)
      .eq('content_type', 'review')
      .eq('status', 'pending');

    if (flagsError) {
      throw new Error(`Failed to count flags: ${flagsError.message}`);
    }

    const flagCount = flagsData || 0;

    // SECURITY LAYER 3: Update review flag_count
    const { error: updateError } = await supabase
      .from('reviews')
      .update({ flag_count: flagCount })
      .eq('id', reviewId);

    if (updateError) {
      throw new Error(`Failed to update flag count: ${updateError.message}`);
    }

    // SECURITY LAYER 4: Audit logging
    await logSecurityEvent('REVIEW_FLAG_COUNT_UPDATED', adminVerification.adminId, {
      reviewId,
      flagCount,
      permissionLevel: adminVerification.permissionLevel
    });

    return { success: true, flagCount };

  } catch (error: any) {
    console.error('🚨 SECURITY ERROR: updateReviewFlagCountSecure failed:', error);
    return { success: false, flagCount: 0, error: error.message };
  }
} 