// src/hooks/useUploadNotifications.ts
import { useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { UploadProgress } from './useB2ImageUpload';

export function useUploadNotifications(uploads: UploadProgress[]) {
  useEffect(() => {
    uploads.forEach((upload) => {
      if (upload.status === 'completed' && upload.result?.success) {
        toast({
          title: "Upload Successful",
          description: `${upload.file.name} uploaded successfully`,
          variant: "default",
        });
      } else if (upload.status === 'error') {
        toast({
          title: "Upload Failed",
          description: upload.error || `Failed to upload ${upload.file.name}`,
          variant: "destructive",
        });
      }
    });
  }, [uploads]);
}
