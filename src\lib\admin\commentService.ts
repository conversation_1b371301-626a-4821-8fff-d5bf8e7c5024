/**
 * Admin Comment Moderation Service
 * Provides comprehensive comment moderation functionality for admin dashboard
 * Created: 20/01/2025 - Admin System Completion
 */

import { createClient } from '@/lib/supabase/client';
import { logAdminAction, AdminAction } from '@/lib/audit/adminActions';

// Comment moderation interfaces
export interface CommentModerationData {
  id: string;
  content: string;
  author_id: string;
  author_name: string;
  author_username?: string;
  review_id: string;
  review_title: string;
  review_slug: string;
  parent_id?: string;
  upvotes: number;
  downvotes: number;
  is_pinned: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  flag_count?: number;
  moderation_notes?: string;
  last_moderated_by?: string;
  last_moderated_at?: string;
}

export interface CommentModerationAction {
  action: 'approve' | 'delete' | 'pin' | 'unpin' | 'flag' | 'warn_user';
  reason?: string;
  notes?: string;
}

export interface CommentFlag {
  id: string;
  comment_id: string;
  reporter_id: string;
  reporter_name: string;
  reason: string;
  description?: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
  resolved_by?: string;
  resolved_at?: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Use the is_admin() RPC function for verification
    const { data, error } = await supabase.rpc('is_admin', { user_id: userId });

    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Get comments for moderation with filters and pagination
export async function getCommentsForModeration(
  adminUserId: string,
  options: {
    status?: 'all' | 'flagged' | 'deleted' | 'recent';
    search?: string;
    reviewId?: string;
    sortBy?: 'created_at' | 'updated_at' | 'upvotes' | 'downvotes' | 'flag_count';
    sortOrder?: 'asc' | 'desc';
    page?: number;
    limit?: number;
  } = {}
): Promise<{ comments: CommentModerationData[]; total: number; hasMore: boolean }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    const supabase = createClient();
    const {
      status = 'all',
      search = '',
      reviewId,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = options;

    let query = supabase
      .from('comments')
      .select(`
        *,
        reviews!inner(title, slug),
        profiles!inner(username)
      `, { count: 'exact' });

    // Apply filters
    if (status === 'flagged') {
      // Note: We'll need to add flag_count to comments table or create a flags table
      query = query.gt('flag_count', 0);
    } else if (status === 'deleted') {
      query = query.eq('is_deleted', true);
    } else if (status === 'recent') {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      query = query.gte('created_at', oneDayAgo);
    }

    if (reviewId) {
      query = query.eq('review_id', reviewId);
    }

    if (search) {
      query = query.or(`content.ilike.%${search}%,author_name.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching comments for moderation:', error);
      throw error;
    }

    // Transform data to match interface
    const comments: CommentModerationData[] = (data || []).map((comment: any) => ({
      id: comment.id,
      content: comment.content,
      author_id: comment.author_id,
      author_name: comment.author_name,
      author_username: comment.profiles?.username,
      review_id: comment.review_id,
      review_title: comment.reviews?.title || 'Unknown Review',
      review_slug: comment.reviews?.slug || '',
      parent_id: comment.parent_id,
      upvotes: comment.upvotes || 0,
      downvotes: comment.downvotes || 0,
      is_pinned: comment.is_pinned || false,
      is_deleted: comment.is_deleted || false,
      created_at: comment.created_at,
      updated_at: comment.updated_at,
      flag_count: comment.flag_count || 0,
      moderation_notes: comment.moderation_notes,
      last_moderated_by: comment.last_moderated_by,
      last_moderated_at: comment.last_moderated_at
    }));

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_QUEUE, {
      resource: 'comments',
      filters: { status, search, reviewId },
      count: comments.length
    });

    return {
      comments,
      total: count || 0,
      hasMore: (count || 0) > page * limit
    };

  } catch (error) {
    console.error('Error in getCommentsForModeration:', error);
    throw error;
  }
}

// Get a specific comment for admin editing
export async function getCommentForAdmin(
  adminUserId: string,
  commentId: string
): Promise<CommentModerationData | null> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    const supabase = createClient();

    const { data, error } = await supabase
      .from('comments')
      .select(`
        *,
        reviews!inner(title, slug),
        profiles!inner(username)
      `)
      .eq('id', commentId)
      .single();

    if (error) {
      console.error('Error fetching comment for admin:', error);
      return null;
    }

    if (!data) return null;

    // Transform data to match interface
    const comment: CommentModerationData = {
      id: data.id,
      content: data.content,
      author_id: data.author_id,
      author_name: data.author_name,
      author_username: data.profiles?.username,
      review_id: data.review_id,
      review_title: data.reviews?.title || 'Unknown Review',
      review_slug: data.reviews?.slug || '',
      parent_id: data.parent_id,
      upvotes: data.upvotes || 0,
      downvotes: data.downvotes || 0,
      is_pinned: data.is_pinned || false,
      is_deleted: data.is_deleted || false,
      created_at: data.created_at,
      updated_at: data.updated_at,
      flag_count: data.flag_count || 0,
      moderation_notes: data.moderation_notes,
      last_moderated_by: data.last_moderated_by,
      last_moderated_at: data.last_moderated_at
    };

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_DETAILS, {
      resource: 'comment',
      resourceId: commentId
    });

    return comment;

  } catch (error) {
    console.error('Error in getCommentForAdmin:', error);
    return null;
  }
}

// Moderate a comment (approve, delete, pin, etc.)
export async function moderateComment(
  adminUserId: string,
  commentId: string,
  action: CommentModerationAction
): Promise<{ success: boolean; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    const supabase = createClient();

    // Prepare update data based on action
    let updateData: any = {
      updated_at: new Date().toISOString(),
      last_moderated_by: adminUserId,
      last_moderated_at: new Date().toISOString()
    };

    if (action.notes) {
      updateData.moderation_notes = action.notes;
    }

    switch (action.action) {
      case 'approve':
        updateData.is_deleted = false;
        break;
      case 'delete':
        updateData.is_deleted = true;
        break;
      case 'pin':
        updateData.is_pinned = true;
        break;
      case 'unpin':
        updateData.is_pinned = false;
        break;
      case 'flag':
        // For flagging, we might want to increment flag_count
        // This would require adding flag_count column to comments table
        break;
      case 'warn_user':
        // For user warnings, we might want to create a separate warnings system
        // For now, just log the action
        break;
    }

    // Update the comment
    const { error } = await supabase
      .from('comments')
      .update(updateData)
      .eq('id', commentId);

    if (error) {
      console.error('Error moderating comment:', error);
      return { success: false, error: error.message };
    }

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.MODERATE_CONTENT, {
      resource: 'comment',
      resourceId: commentId,
      action: action.action,
      reason: action.reason,
      notes: action.notes
    });

    return { success: true };

  } catch (error) {
    console.error('Error in moderateComment:', error);
    return { success: false, error: 'Failed to moderate comment' };
  }
}

// Batch moderate multiple comments
export async function batchModerateComments(
  adminUserId: string,
  commentIds: string[],
  action: CommentModerationAction
): Promise<{ success: boolean; error?: string; processed: number }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required', processed: 0 };
    }

    let processed = 0;
    const errors: string[] = [];

    // Process each comment individually to ensure proper logging
    for (const commentId of commentIds) {
      const result = await moderateComment(adminUserId, commentId, action);
      if (result.success) {
        processed++;
      } else {
        errors.push(`Comment ${commentId}: ${result.error}`);
      }
    }

    // Log batch action
    await logAdminAction(adminUserId, AdminAction.BATCH_MODERATE_CONTENT, {
      resource: 'comments',
      action: action.action,
      count: commentIds.length,
      processed,
      errors: errors.length
    });

    if (errors.length > 0) {
      return {
        success: false,
        error: `Processed ${processed}/${commentIds.length}. Errors: ${errors.join('; ')}`,
        processed
      };
    }

    return { success: true, processed };

  } catch (error) {
    console.error('Error in batchModerateComments:', error);
    return { success: false, error: 'Failed to batch moderate comments', processed: 0 };
  }
}
