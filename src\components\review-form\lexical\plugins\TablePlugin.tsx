// src/components/review-form/lexical/plugins/TablePlugin.tsx
'use client';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { TablePlugin as LexicalTablePlugin } from '@lexical/react/LexicalTablePlugin';
import {
  TableNode,
  TableCellNode,
  TableRowNode,
  $createTableNodeWithDimensions,
  INSERT_TABLE_COMMAND
} from '@lexical/table';
import { $insertNodes } from 'lexical';
import { useEffect } from 'react';

export default function TablePlugin() {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    // Register table insertion command
    return editor.registerCommand(
      INSERT_TABLE_COMMAND,
      (payload: { columns: string; rows: string }) => {
        editor.update(() => {
          const { columns, rows } = payload;
          const tableNode = $createTableNodeWithDimensions(
            Number(rows),
            Number(columns),
            true // includeHeaders
          );
          $insertNodes([tableNode]);
        });
        return true;
      },
      1 // Priority
    );
  }, [editor]);

  return <LexicalTablePlugin hasCellMerge={true} hasCellBackgroundColor={false} />;
}
