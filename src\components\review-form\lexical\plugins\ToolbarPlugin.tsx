// src/components/review-form/lexical/plugins/ToolbarPlugin.tsx
'use client';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  $getSelection,
  $isRangeSelection,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  FORMAT_TEXT_COMMAND,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  UNDO_COMMAND,
  $isRootOrShadowRoot,
  $isParagraphNode,
  $createParagraphNode,
  $setSelection,
  $createRangeSelection,
  FORMAT_ELEMENT_COMMAND,
  ElementFormatType,
} from 'lexical';
import React, { useCallback, useEffect, useState } from 'react';
import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';
import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { $insertNodes } from 'lexical';
import { $createImageNode } from '../nodes/ImageNode';
import { $createYouTubeNode } from '../nodes/YouTubeNode';
import LinkInsertModal from './LinkInsertModal';
import ImageInsertModal from './ImageInsertModal';
import ImageViewModal from './ImageViewModal';
import YouTubeInsertModal from './YouTubeInsertModal';
import { 
  INSERT_ORDERED_LIST_COMMAND, 
  INSERT_UNORDERED_LIST_COMMAND, 
  REMOVE_LIST_COMMAND, 
  ListNode,
  $isListNode,
  $isListItemNode,
} from '@lexical/list';
import { $findMatchingParent, $getNearestNodeOfType } from '@lexical/utils';
import {
  $isHeadingNode,
  $createHeadingNode,
  $createQuoteNode,
  $isQuoteNode
} from '@lexical/rich-text';
import { $isCodeNode } from '@lexical/code';

const LowPriority = 1;

interface ToolbarPluginProps {
  isLightMode?: boolean;
  gameName?: string;
}

export default function ToolbarPlugin({ isLightMode = false, gameName }: ToolbarPluginProps) {
  const [editor] = useLexicalComposerContext();
  const [mounted, setMounted] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [blockType, setBlockType] = useState('paragraph');
  const [isLink, setIsLink] = useState(false);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [elementFormat, setElementFormat] = useState<ElementFormatType>('left');

  // Modal states
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [isYouTubeModalOpen, setIsYouTubeModalOpen] = useState(false);
  const [isImageViewModalOpen, setIsImageViewModalOpen] = useState(false);
  const [imageViewData, setImageViewData] = useState<{src: string; altText: string; caption?: string} | null>(null);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Listen for image click events
  useEffect(() => {
    const handleImageClick = (event: CustomEvent) => {
      setImageViewData(event.detail);
      setIsImageViewModalOpen(true);
    };

    window.addEventListener('lexical-image-click', handleImageClick as EventListener);
    return () => window.removeEventListener('lexical-image-click', handleImageClick as EventListener);
  }, []);

  const updateToolbar = useCallback(() => {
    if (!mounted) return;
    
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode();
      let element =
        anchorNode.getKey() === 'root'
          ? anchorNode
          : $findMatchingParent(anchorNode, (e) => {
              const parent = e.getParent();
              return parent !== null && $isRootOrShadowRoot(parent);
            });

      if (element === null) {
        element = anchorNode.getTopLevelElementOrThrow();
      }

      setIsBold(selection.hasFormat('bold'));
      setIsItalic(selection.hasFormat('italic'));
      setIsUnderline(selection.hasFormat('underline'));
      setIsStrikethrough(selection.hasFormat('strikethrough'));

      // Get element format (alignment)
      if (element) {
        setElementFormat(element.getFormatType() || 'left');
      }

      const node = selection.anchor.getNode();
      const parent = node.getParent();
      setIsLink($isLinkNode(parent) || $isLinkNode(node));
      
      // Check if we're in a list or list item
      const listNode = $findMatchingParent(anchorNode, $isListNode) || 
                       ($isListNode(element) ? element : null);
      
      if (listNode) {
        setBlockType(listNode.getListType());
      } else if ($isListItemNode(element)) {
        // We're in a list item, find its parent list
        const parentList = element.getParent();
        if ($isListNode(parentList)) {
          setBlockType(parentList.getListType());
        } else {
          setBlockType('paragraph');
        }
      } else {
        let type = element.getType();
        if ($isHeadingNode(element)) {
          type = element.getTag();
        } else if ($isCodeNode(element)) {
          type = 'code';
        } else if ($isParagraphNode(element)) {
          type = 'paragraph';
        }
        setBlockType(type);
      }
    }
  }, [mounted]);

  useEffect(() => {
    if (!mounted) return;
    
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          editor.getEditorState().read(() => {
            updateToolbar();
          });
          return false;
        },
        LowPriority,
      ),
      editor.registerCommand(
        CAN_UNDO_COMMAND,
        (payload) => {
          setCanUndo(payload);
          return false;
        },
        LowPriority,
      ),
      editor.registerCommand(
        CAN_REDO_COMMAND,
        (payload) => {
          setCanRedo(payload);
          return false;
        },
        LowPriority,
      ),
    );
  }, [editor, updateToolbar, mounted]);

  // Formatting functions with proper selection handling
  const formatParagraph = useCallback(() => {
    if (!mounted) return;
    
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        // First, remove any list formatting
        editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
        
        // Then ensure we have paragraph formatting
        const nodes = selection.getNodes();
        
        if (nodes.length === 0) {
          return;
        }

        nodes.forEach(node => {
          const element = node.getTopLevelElement();
          if (element && !$isParagraphNode(element) && !$isListNode(element) && !$isListItemNode(element)) {
            const newParagraph = $createParagraphNode();

            // Preserve the children of the old element
            const children = element.getChildren();
            children.forEach((child: any) => {
              newParagraph.append(child);
            });
            
            element.replace(newParagraph);
            
            // Set selection to the new paragraph
            const newSelection = $createRangeSelection();
            newSelection.anchor.set(newParagraph.getKey(), 0, 'element');
            newSelection.focus.set(newParagraph.getKey(), newParagraph.getChildrenSize(), 'element');
            $setSelection(newSelection);
          }
        });
      }
    });
  }, [editor, mounted]);

  const formatHeading = useCallback((headingSize: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6') => {
    if (!mounted) return;
    
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();
        
        if (nodes.length === 0) {
          return;
        }

        nodes.forEach(node => {
          const element = node.getTopLevelElement();
          if (element) {
            const newHeading = $createHeadingNode(headingSize);

            // Preserve the children of the old element
            const children = element.getChildren();
            children.forEach((child: any) => {
              newHeading.append(child);
            });
            
            element.replace(newHeading);
            
            // Set selection to the new heading
            const newSelection = $createRangeSelection();
            newSelection.anchor.set(newHeading.getKey(), 0, 'element');
            newSelection.focus.set(newHeading.getKey(), newHeading.getChildrenSize(), 'element');
            $setSelection(newSelection);
          }
        });
      }
    });
  }, [editor, mounted]);

  const formatQuote = useCallback(() => {
    if (!mounted) return;
    
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();
        
        if (nodes.length === 0) {
          return;
        }

        nodes.forEach(node => {
          const element = node.getTopLevelElement();
          if (element && !$isQuoteNode(element)) {
            const newQuote = $createQuoteNode();

            // Preserve the children of the old element
            const children = element.getChildren();
            children.forEach((child: any) => {
              newQuote.append(child);
            });
            
            element.replace(newQuote);
            
            // Set selection to the new quote
            const newSelection = $createRangeSelection();
            newSelection.anchor.set(newQuote.getKey(), 0, 'element');
            newSelection.focus.set(newQuote.getKey(), newQuote.getChildrenSize(), 'element');
            $setSelection(newSelection);
          }
        });
      }
    });
  }, [editor, mounted]);

  const formatCodeBlock = useCallback(() => {
    if (!mounted) return;

    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();

        if (nodes.length === 0) {
          return;
        }

        nodes.forEach(node => {
          const element = node.getTopLevelElement();
          if (element) {
            // Import CodeNode dynamically
            import('@lexical/code').then(({ $createCodeNode, $isCodeNode }) => {
              editor.update(() => {
                if (!$isCodeNode(element)) {
                  const newCodeBlock = $createCodeNode();

                  // Preserve the children of the old element
                  const children = element.getChildren();
                  children.forEach((child: any) => {
                    newCodeBlock.append(child);
                  });

                  element.replace(newCodeBlock);

                  // Set selection to the new code block
                  const newSelection = $createRangeSelection();
                  newSelection.anchor.set(newCodeBlock.getKey(), 0, 'element');
                  newSelection.focus.set(newCodeBlock.getKey(), newCodeBlock.getChildrenSize(), 'element');
                  $setSelection(newSelection);
                }
              });
            });
          }
        });
      }
    });
  }, [editor, mounted]);

  const insertLink = useCallback(() => {
    if (!mounted) return;

    if (!isLink) {
      setIsLinkModalOpen(true);
    } else {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    }
  }, [editor, isLink, mounted]);

  const handleLinkInsert = useCallback((url: string, text?: string) => {
    editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
  }, [editor]);

  const handleImageInsert = useCallback((src: string, altText: string, caption?: string) => {
    editor.update(() => {
      const imageNode = $createImageNode({
        src,
        altText,
        caption,
        showCaption: !!caption,
      });
      $insertNodes([imageNode]);
    });
  }, [editor]);

  const insertImage = useCallback(() => {
    if (!mounted) return;
    setIsImageModalOpen(true);
  }, [mounted]);

  const handleYouTubeInsert = useCallback((videoId: string) => {
    editor.update(() => {
      const youtubeNode = $createYouTubeNode({
        videoId,
        showCaption: false,
      });
      $insertNodes([youtubeNode]);
    });
  }, [editor]);

  const insertYouTube = useCallback(() => {
    if (!mounted) return;
    setIsYouTubeModalOpen(true);
  }, [mounted]);

  const insertTable = useCallback(() => {
    if (!mounted) return;

    const rows = prompt('Number of rows:', '3');
    const columns = prompt('Number of columns:', '3');

    if (rows && columns && !isNaN(Number(rows)) && !isNaN(Number(columns))) {
      editor.dispatchCommand(INSERT_TABLE_COMMAND, { rows, columns });
    }
  }, [editor, mounted]);

  const searchWeb = useCallback(() => {
    if (!mounted) return;

    let searchQuery = '';

    // Try to get selected text first
    editor.getEditorState().read(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const selectedText = selection.getTextContent().trim();
        if (selectedText) {
          searchQuery = selectedText;
        }
      }
    });

    // Fallback to game name if no selection
    if (!searchQuery && gameName) {
      searchQuery = gameName;
    }

    // Final fallback to generic search
    if (!searchQuery) {
      searchQuery = 'gaming review';
    }

    // Open search in new tab
    const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
    window.open(searchUrl, '_blank');
  }, [editor, mounted, gameName]);

  const formatElement = useCallback((format: ElementFormatType) => {
    if (!mounted) return;
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  }, [editor, mounted]);

  // Always render the same structure to prevent hydration mismatch
  return (
    <>
    <div className="lexical-toolbar">
      {/* History Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${!canUndo ? 'disabled' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(UNDO_COMMAND, undefined)}
          disabled={!canUndo}
          title="Undo (Ctrl+Z)"
          type="button"
        >
          ↶
        </button>
        <button
          className={`toolbar-button ${!canRedo ? 'disabled' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(REDO_COMMAND, undefined)}
          disabled={!canRedo}
          title="Redo (Ctrl+Y)"
          type="button"
        >
          ↷
        </button>
      </div>
      
      <div className="toolbar-separator" />
      
      {/* Block Format Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${blockType === 'paragraph' ? 'active' : ''}`}
          onClick={formatParagraph}
          title="Normal Text"
          type="button"
        >
          P
        </button>
        <button
          className={`toolbar-button ${blockType === 'h1' ? 'active' : ''}`}
          onClick={() => formatHeading('h1')}
          title="Heading 1"
          type="button"
        >
          H1
        </button>
        <button
          className={`toolbar-button ${blockType === 'h2' ? 'active' : ''}`}
          onClick={() => formatHeading('h2')}
          title="Heading 2"
          type="button"
        >
          H2
        </button>
        <button
          className={`toolbar-button ${blockType === 'quote' ? 'active' : ''}`}
          onClick={formatQuote}
          title="Quote"
          type="button"
        >
          "
        </button>
        <button
          className={`toolbar-button ${blockType === 'code' ? 'active' : ''}`}
          onClick={formatCodeBlock}
          title="Code Block"
          type="button"
        >
          &lt;/&gt;
        </button>
      </div>

      <div className="toolbar-separator" />

      {/* Text Formatting Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${isBold ? 'active' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold')}
          title="Bold (Ctrl+B)"
          type="button"
        >
          <strong>B</strong>
        </button>
        <button
          className={`toolbar-button ${isItalic ? 'active' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic')}
          title="Italic (Ctrl+I)"
          type="button"
        >
          <em>I</em>
        </button>
        <button
          className={`toolbar-button ${isUnderline ? 'active' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline')}
          title="Underline (Ctrl+U)"
          type="button"
        >
          <u>U</u>
        </button>
        <button
          className={`toolbar-button ${isStrikethrough ? 'active' : ''}`}
          onClick={() => mounted && editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'strikethrough')}
          title="Strikethrough"
          type="button"
        >
          <s>S</s>
        </button>
      </div>
      
      <div className="toolbar-separator" />

      {/* List Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${blockType === 'bullet' ? 'active' : ''}`}
          onClick={() => {
            if (!mounted) return;
            if (blockType === 'bullet') {
              editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
            } else {
              editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
            }
          }}
          title="Bullet List"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="6" cy="7" r="2" fill="currentColor"/>
            <circle cx="6" cy="12" r="2" fill="currentColor"/>
            <circle cx="6" cy="17" r="2" fill="currentColor"/>
            <line x1="12" y1="7" x2="20" y2="7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="12" y1="12" x2="20" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="12" y1="17" x2="20" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
        <button
          className={`toolbar-button ${blockType === 'number' ? 'active' : ''}`}
          onClick={() => {
            if (!mounted) return;
            if (blockType === 'number') {
              editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
            } else {
              editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
            }
          }}
          title="Numbered List"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="12" y1="7" x2="20" y2="7" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="12" y1="12" x2="20" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="12" y1="17" x2="20" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <text x="6" y="9" fontSize="10" fill="currentColor" fontFamily="monospace" fontWeight="bold">1</text>
            <text x="6" y="14" fontSize="10" fill="currentColor" fontFamily="monospace" fontWeight="bold">2</text>
            <text x="6" y="19" fontSize="10" fill="currentColor" fontFamily="monospace" fontWeight="bold">3</text>
          </svg>
        </button>
      </div>
      
      <div className="toolbar-separator" />

      {/* Alignment Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${elementFormat === 'left' ? 'active' : ''}`}
          onClick={() => formatElement('left')}
          title="Align Left"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="3" y1="12" x2="15" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="3" y1="18" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
        <button
          className={`toolbar-button ${elementFormat === 'center' ? 'active' : ''}`}
          onClick={() => formatElement('center')}
          title="Align Center"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="6" y1="12" x2="18" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="4" y1="18" x2="20" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
        <button
          className={`toolbar-button ${elementFormat === 'right' ? 'active' : ''}`}
          onClick={() => formatElement('right')}
          title="Align Right"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="9" y1="12" x2="21" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            <line x1="6" y1="18" x2="21" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      </div>

      <div className="toolbar-separator" />

      {/* Link & Media Controls */}
      <div className="toolbar-group">
        <button
          className={`toolbar-button ${isLink ? 'active' : ''}`}
          onClick={insertLink}
          title={isLink ? 'Remove Link' : 'Insert Link'}
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <button
          className="toolbar-button"
          onClick={insertImage}
          title="Insert Image"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
            <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 15l-5-5L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <button
          className="toolbar-button"
          onClick={insertYouTube}
          title="Insert YouTube Video"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="currentColor"/>
          </svg>
        </button>
        <button
          className="toolbar-button"
          onClick={insertTable}
          title="Insert Table"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v6m0 0v6m0-6h6m6 0v6a2 2 0 0 1-2 2H9m6-6H9m6 0v6m-6 0h6m-6 0v6m0-6H3v6a2 2 0 0 0 2 2h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>

      <div className="toolbar-separator" />

      {/* Utility Controls */}
      <div className="toolbar-group">
        <button
          className="toolbar-button"
          onClick={searchWeb}
          title="Search Web"
          type="button"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
            <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>

    {/* Modals */}
    <LinkInsertModal
      isOpen={isLinkModalOpen}
      onClose={() => setIsLinkModalOpen(false)}
      onInsert={handleLinkInsert}
    />

    <ImageInsertModal
      isOpen={isImageModalOpen}
      onClose={() => setIsImageModalOpen(false)}
      onInsert={handleImageInsert}
    />

    <YouTubeInsertModal
      isOpen={isYouTubeModalOpen}
      onClose={() => setIsYouTubeModalOpen(false)}
      onInsert={handleYouTubeInsert}
    />

    {imageViewData && (
      <ImageViewModal
        isOpen={isImageViewModalOpen}
        onClose={() => {
          setIsImageViewModalOpen(false);
          setImageViewData(null);
        }}
        src={imageViewData.src}
        altText={imageViewData.altText}
        caption={imageViewData.caption}
      />
    )}
    </>
  );
}