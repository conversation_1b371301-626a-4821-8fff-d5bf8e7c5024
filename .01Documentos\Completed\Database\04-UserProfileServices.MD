# Phase 4: User Profile Services Implementation
## Complete User Profile Management System

### 🎯 **Phase Objective**
Implement comprehensive user profile services that enable profile management, username validation, public profile viewing, and privacy controls with full Supabase integration.

### 📊 **Phase Status**
**Current Progress: 35%**
**Estimated Duration: 2-3 days**
**Priority: HIGH**
**Dependencies: Phase 1 - Database Schema, Phase 3 - RLS Security**

### 🏗️ **Profile System Architecture**

```mermaid
graph TD
    A[Profile Services] --> B[Database Layer]
    A --> C[Privacy Controls]
    A --> D[Validation Layer]
    
    B --> B1[Profile CRUD]
    B --> B2[Username Management]
    B --> B3[Avatar/Banner Upload]
    
    C --> C1[Privacy Settings]
    C --> C2[Visibility Controls]
    C --> C3[Data Protection]
    
    D --> D1[Username Validation]
    D --> D2[Profile Data Validation]
    D --> D3[Image Validation]
    
    E[Profile Pages] --> A
    F[Settings Pages] --> A
    G[Dashboard] --> A
```

### 📝 **Implementation Tasks**

#### **Task 4.1: Core Profile Services** ⏳
**Estimated Time:** 6 hours
**File Location:** `/src/app/u/actions.ts`

##### **4.1.1: Replace Placeholder Functions**
Current file has all functions returning null/false - implement real Supabase operations:

```typescript
'use server';

import { createServerClient } from '@/lib/supabase/server';
import { createClient } from '@/lib/supabase/client';
import type { UserProfile } from '@/lib/types';
import { cookies } from 'next/headers';

// Get user profile by username (public view)
export async function getUserProfileByUsername(username: string): Promise<UserProfile | null> {
  try {
    const supabase = createServerClient(cookies());
    
    // Implementation details in checklist
    
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

// Update user profile with validation
export async function updateUserProfile(
  userId: string, 
  updates: Partial<UserProfile>
): Promise<{ success: boolean; profile?: UserProfile; error?: string }> {
  try {
    const supabase = createServerClient(cookies());
    
    // Implementation details in checklist
    
  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: 'Failed to update profile' };
  }
}

// Check username availability
export async function checkUsernameAvailability(username: string): Promise<{
  available: boolean;
  suggestions?: string[];
  error?: string;
}> {
  try {
    const supabase = createServerClient(cookies());
    
    // Implementation details in checklist
    
  } catch (error) {
    console.error('Error checking username:', error);
    return { available: false, error: 'Failed to check username' };
  }
}

// Generate username suggestions
export async function generateUsernameSuggestions(baseUsername: string): Promise<string[]> {
  // Implementation details in checklist
}

// Update user privacy settings
export async function updatePrivacySettings(
  userId: string, 
  privacySettings: any
): Promise<{ success: boolean; error?: string }> {
  // Implementation details in checklist
}
```

##### **4.1.2: Profile Validation Functions**
```typescript
// Comprehensive profile validation
export interface ProfileValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

export function validateProfileData(profile: Partial<UserProfile>): ProfileValidationResult {
  const errors: Record<string, string> = {};
  const warnings: Record<string, string> = {};
  
  // Implementation details in checklist
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
}

// Username validation rules
export function validateUsername(username: string): { valid: boolean; error?: string } {
  // Implementation details in checklist
}
```

#### **Task 4.2: Profile Display Components** ⏳
**Estimated Time:** 5 hours

##### **4.2.1: Public Profile Page Implementation**
**File Location:** `/src/app/u/[slug]/page.tsx`

Currently shows placeholder data - implement real profile fetching:

```typescript
import { getUserProfileByUsername } from '@/app/u/actions';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // Implementation details in checklist
}

export default async function UserProfilePage({ params }: { params: { slug: string } }) {
  const profile = await getUserProfileByUsername(params.slug);
  
  if (!profile) {
    notFound();
  }
  
  // Implementation details in checklist
}
```

##### **4.2.2: Profile Card Component Enhancement**
**File Location:** `/src/components/userprofile/GamerCard.tsx`

Integrate with real profile data and privacy settings:

```typescript
// Enhanced GamerCard with privacy-aware data display
interface GamerCardProps {
  profile: UserProfile;
  isOwnProfile?: boolean;
  viewerPermissions?: ProfileViewPermissions;
}

export default function GamerCard({ profile, isOwnProfile, viewerPermissions }: GamerCardProps) {
  // Implementation details in checklist
}
```

##### **4.2.3: Edit Profile Modal Enhancement**
**File Location:** `/src/components/userprofile/EditProfileModal.tsx`

Connect to real profile update functionality:

```typescript
// Enhanced EditProfileModal with real Supabase operations
export default function EditProfileModal({ isOpen, onClose, currentProfile }: EditProfileModalProps) {
  const [formData, setFormData] = useState(currentProfile);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const handleSubmit = async () => {
    // Implementation details in checklist
  };
  
  // Implementation details in checklist
}
```

#### **Task 4.3: Username Management System** ⏳
**Estimated Time:** 4 hours

##### **4.3.1: Username Validation Component**
```typescript
// Real-time username validation component
interface UsernameValidatorProps {
  currentUsername?: string;
  onValidation: (valid: boolean, username: string) => void;
}

export function UsernameValidator({ currentUsername, onValidation }: UsernameValidatorProps) {
  // Implementation details in checklist
}
```

##### **4.3.2: Username Change Flow**
```typescript
// Username change with slug update
export async function changeUsername(
  userId: string, 
  newUsername: string
): Promise<{ success: boolean; newSlug?: string; error?: string }> {
  // Implementation details in checklist
}
```

#### **Task 4.4: Privacy Controls Implementation** ⏳
**Estimated Time:** 4 hours

##### **4.4.1: Privacy Settings Component**
```typescript
// Privacy settings management
interface PrivacySettingsProps {
  currentSettings: PrivacySettings;
  onUpdate: (settings: PrivacySettings) => void;
}

export function PrivacySettingsComponent({ currentSettings, onUpdate }: PrivacySettingsProps) {
  // Implementation details in checklist
}
```

##### **4.4.2: Profile Visibility Engine**
```typescript
// Determine what profile data is visible to viewer
export interface ProfileViewPermissions {
  canViewOnlineStatus: boolean;
  canViewGamingProfiles: boolean;
  canViewAchievements: boolean;
  canViewContactInfo: boolean;
  canViewFullProfile: boolean;
}

export function calculateProfilePermissions(
  profile: UserProfile,
  viewerId?: string
): ProfileViewPermissions {
  // Implementation details in checklist
}
```

#### **Task 4.5: Profile Analytics and Insights** ⏳
**Estimated Time:** 3 hours

##### **4.5.1: Profile Statistics Service**
```typescript
// Profile statistics and insights
export interface ProfileStats {
  totalReviews: number;
  publishedReviews: number;
  totalViews: number;
  totalLikes: number;
  averageScore: number;
  topGenres: string[];
  topPlatforms: string[];
  level: number;
  experience: number;
  achievements: Achievement[];
}

export async function getProfileStatistics(userId: string): Promise<ProfileStats> {
  // Implementation details in checklist
}
```

##### **4.5.2: Achievement System Integration**
```typescript
// Achievement tracking and display
export async function checkAndAwardAchievements(userId: string): Promise<Achievement[]> {
  // Implementation details in checklist
}

export async function getUserAchievements(userId: string): Promise<Achievement[]> {
  // Implementation details in checklist
}
```

#### **Task 4.6: Image Upload and Management** ⏳
**Estimated Time:** 4 hours

##### **4.6.1: Avatar Upload System**
```typescript
// Avatar upload with validation and processing
export async function uploadAvatar(
  userId: string, 
  file: File
): Promise<{ success: boolean; avatarUrl?: string; error?: string }> {
  try {
    // Implementation details in checklist
  } catch (error) {
    return { success: false, error: 'Failed to upload avatar' };
  }
}
```

##### **4.6.2: Banner Upload System**
```typescript
// Banner upload with validation and processing
export async function uploadBanner(
  userId: string, 
  file: File
): Promise<{ success: boolean; bannerUrl?: string; error?: string }> {
  // Implementation details in checklist
}
```

##### **4.6.3: Image Optimization and Validation**
```typescript
// Image validation and optimization
export function validateImage(file: File): { valid: boolean; error?: string } {
  // Implementation details in checklist
}

export async function optimizeImage(file: File): Promise<File> {
  // Implementation details in checklist
}
```

### ✅ **Task Completion Checklist**

#### **Core Profile Services (Task 4.1)**
- [ ] **4.1.1:** Profile service functions implemented
  - [ ] `getUserProfileByUsername()` with RLS-aware queries
  - [ ] Public vs private profile data filtering
  - [ ] Profile existence validation
  - [ ] Error handling for invalid usernames
- [ ] **4.1.2:** `updateUserProfile()` function implemented
  - [ ] Permission validation (user can only update own profile)
  - [ ] Data validation before database update
  - [ ] Slug regeneration on username change
  - [ ] Cache invalidation after updates
- [ ] **4.1.3:** `checkUsernameAvailability()` function implemented
  - [ ] Real-time availability checking
  - [ ] Case-insensitive validation
  - [ ] Reserved username protection
  - [ ] Username suggestion generation
- [ ] **4.1.4:** Profile validation functions implemented
  - [ ] `validateProfileData()` with comprehensive rules
  - [ ] `validateUsername()` with platform-specific requirements
  - [ ] Bio length and content validation
  - [ ] Contact information validation
- [ ] **AI Comment:** _[Implementation details, validation rules applied, and any data migration considerations]_

#### **Profile Display Components (Task 4.2)**
- [ ] **4.2.1:** Public profile page implemented
  - [ ] Server-side profile fetching
  - [ ] SEO metadata generation
  - [ ] 404 handling for non-existent profiles
  - [ ] Privacy-aware data display
- [ ] **4.2.2:** GamerCard component enhanced
  - [ ] Real profile data integration
  - [ ] Privacy settings enforcement
  - [ ] Theme customization support
  - [ ] Responsive design implementation
- [ ] **4.2.3:** EditProfileModal enhanced
  - [ ] Real-time validation feedback
  - [ ] Image upload integration
  - [ ] Privacy settings management
  - [ ] Form state management
- [ ] **AI Comment:** _[Component implementation details, user experience improvements, and privacy considerations]_

#### **Username Management (Task 4.3)**
- [ ] **4.3.1:** Username validation component created
  - [ ] Real-time availability checking
  - [ ] Visual feedback for validation states
  - [ ] Suggestion display when unavailable
  - [ ] Debounced API calls for performance
- [ ] **4.3.2:** Username change flow implemented
  - [ ] Slug update automation
  - [ ] URL redirect handling for old slugs
  - [ ] Change history tracking
  - [ ] Notification system integration
- [ ] **AI Comment:** _[Username management implementation and user experience considerations]_

#### **Privacy Controls (Task 4.4)**
- [ ] **4.4.1:** Privacy settings component created
  - [ ] Granular privacy control options
  - [ ] Real-time settings updates
  - [ ] Clear privacy explanations
  - [ ] Default privacy recommendations
- [ ] **4.4.2:** Profile visibility engine implemented
  - [ ] `calculateProfilePermissions()` function
  - [ ] Viewer-specific data filtering
  - [ ] Privacy setting enforcement
  - [ ] Anonymous user handling
- [ ] **AI Comment:** _[Privacy implementation details and compliance considerations]_

#### **Profile Analytics (Task 4.5)**
- [ ] **4.5.1:** Profile statistics service implemented
  - [ ] `getProfileStatistics()` with aggregated data
  - [ ] Performance optimized queries
  - [ ] Real-time statistics updates
  - [ ] Caching strategy for expensive calculations
- [ ] **4.5.2:** Achievement system integrated
  - [ ] `checkAndAwardAchievements()` function
  - [ ] Achievement trigger logic
  - [ ] Achievement display components
  - [ ] Notification system for new achievements
- [ ] **AI Comment:** _[Analytics implementation and achievement system details]_

#### **Image Upload Management (Task 4.6)**
- [ ] **4.6.1:** Avatar upload system implemented
  - [ ] File validation (size, type, dimensions)
  - [ ] Image processing and optimization
  - [ ] Supabase Storage integration
  - [ ] Old image cleanup
- [ ] **4.6.2:** Banner upload system implemented
  - [ ] Banner-specific validation rules
  - [ ] Aspect ratio enforcement
  - [ ] Automatic cropping/resizing
  - [ ] Storage path organization
- [ ] **4.6.3:** Image optimization implemented
  - [ ] `validateImage()` function with comprehensive checks
  - [ ] `optimizeImage()` function with compression
  - [ ] Format conversion (WebP support)
  - [ ] Progressive loading support
- [ ] **AI Comment:** _[Image upload implementation, optimization strategies, and storage considerations]_

### 🔍 **Testing Requirements**

#### **Unit Tests**
- [ ] Profile service functions with edge cases
- [ ] Username validation with various inputs
- [ ] Privacy permission calculations
- [ ] Image validation and processing

#### **Integration Tests**
- [ ] Complete profile update flow
- [ ] Username change with slug updates
- [ ] Privacy settings enforcement
- [ ] Image upload and storage integration

#### **User Experience Tests**
- [ ] Profile page loading performance
- [ ] Real-time validation responsiveness
- [ ] Mobile responsiveness for profile components
- [ ] Accessibility compliance for profile forms

### 🎯 **Success Criteria**

1. **Profile Management:** Users can view and edit their profiles successfully
2. **Username System:** Username availability checking and changes work seamlessly  
3. **Privacy Controls:** Privacy settings properly control data visibility
4. **Public Profiles:** Public profile pages load correctly with appropriate data
5. **Image Uploads:** Avatar and banner uploads work with proper validation
6. **Performance:** Profile operations complete within acceptable time limits
7. **Security:** All profile operations respect RLS policies and user permissions

### 🚨 **Critical Implementation Notes**

1. **Privacy First:** Default to private settings, require explicit public sharing
2. **Username Uniqueness:** Ensure username changes don't break existing URLs
3. **Image Security:** Validate and sanitize all uploaded images
4. **Performance:** Cache frequently accessed profile data
5. **Accessibility:** Ensure profile components work with screen readers

### 📊 **Performance Benchmarks**

After completion, verify:
- Profile page load: < 1.5 seconds
- Username availability check: < 300ms
- Profile update: < 2 seconds
- Image upload: < 5 seconds for 2MB images
- Privacy setting updates: < 500ms

### 🔄 **Integration Points**

This profile system integrates with:
- **Phase 2:** Review System (author profiles on reviews)
- **Phase 3:** RLS Security (privacy enforcement)
- **Phase 5:** Admin System (user management)
- **Phase 6:** Testing (profile functionality validation)

### 📈 **Feature Completeness Tracking**

```
Profile Services Implementation:
[████████████████████                    ] 35% → Target: 100%

Current Status:
✅ Auth Context Integration (90%)
✅ Profile Structure (80%)
🔄 Profile Actions (0% - placeholder functions)
🔄 Public Profile Pages (0% - needs real data)
🔄 Privacy Controls (0% - needs implementation)
🔄 Username Management (0% - availability checking broken)
🔄 Image Upload (0% - needs Supabase Storage)
🔄 Analytics Integration (0% - needs statistics)
```

---

**Phase Completion Status:** ⏳ **PENDING**
**Previous Phase:** `03-RLSSecurity.MD` (Must be completed first)
**Next Phase:** `05-AdminSystemRestore.MD`
**Last Updated:** December 7, 2025