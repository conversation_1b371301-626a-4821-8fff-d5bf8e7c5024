import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';

/**
 * CRITICAL SECURITY MIDDLEWARE
 * Protects all /admin routes with server-side authentication
 * Fixes CRITICAL vulnerability: Client-side admin authentication bypass
 * 
 * Date: June 14, 2025
 * Security Level: CRITICAL
 * CVSS Score: 9.8 (Critical)
 */
export async function middleware(request: NextRequest) {
  // Only protect admin routes - let other routes pass through
  if (!request.nextUrl.pathname.startsWith('/admin')) {
    return NextResponse.next();
  }

  try {
    // Create Supabase client for server-side authentication
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) =>
              request.cookies.set(name, value)
            );
          },
        },
      }
    );
    
    // LAYER 1: Verify JW<PERSON> token and get authenticated user
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.warn('🔒 SECURITY: Unauthenticated admin access attempt', {
        path: request.nextUrl.pathname,
        ip: request.ip || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      });
      
      // Redirect to login with callback URL
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', request.nextUrl.pathname);
      return NextResponse.redirect(loginUrl);
    }

    // LAYER 2: Verify admin status from database (cannot be bypassed)
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin, suspended, admin_level')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('🚨 SECURITY: Profile verification failed', {
        userId: user.id,
        error: profileError.message,
        path: request.nextUrl.pathname,
        timestamp: new Date().toISOString()
      });
      
      // Redirect to home page on database error
      return NextResponse.redirect(new URL('/', request.url));
    }

    // LAYER 3: Check admin privileges
    if (!profile?.is_admin) {
      console.warn('🔒 SECURITY: Non-admin user attempted admin access', {
        userId: user.id,
        email: user.email,
        path: request.nextUrl.pathname,
        timestamp: new Date().toISOString()
      });
      
      // Redirect non-admin users to home page
      return NextResponse.redirect(new URL('/', request.url));
    }

    // LAYER 4: Check suspension status
    if (profile?.suspended) {
      console.warn('🔒 SECURITY: Suspended admin attempted access', {
        userId: user.id,
        email: user.email,
        path: request.nextUrl.pathname,
        timestamp: new Date().toISOString()
      });
      
      // Redirect suspended admins to home page
      return NextResponse.redirect(new URL('/', request.url));
    }

    // LAYER 5: Log successful admin access
    console.info('✅ SECURITY: Admin access granted', {
      userId: user.id,
      email: user.email,
      adminLevel: profile.admin_level || 'MODERATOR',
      path: request.nextUrl.pathname,
      timestamp: new Date().toISOString()
    });

    // Allow access to admin routes
    return NextResponse.next();

  } catch (error) {
    console.error('🚨 CRITICAL: Middleware authentication error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      path: request.nextUrl.pathname,
      timestamp: new Date().toISOString()
    });
    
    // Fail secure - redirect to home page on any error
    return NextResponse.redirect(new URL('/', request.url));
  }
}

// Configure middleware to run on all admin routes
export const config = {
  matcher: '/admin/:path*'
};
