'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  MousePointer,
  Calendar,
  Download,
  Activity,
  Target,
  BarChart3
} from 'lucide-react';

interface UnifiedBannerAnalyticsProps {
  bannerId: string;
  bannerType: 'content' | 'sponsor';
  className?: string;
}

interface AnalyticsMetrics {
  total_impressions: number;
  total_clicks: number;
  ctr: string | number;
  days_active: number;
  created_at: string;
  impressionTrend: number;
  clickTrend: number;
  ctrTrend?: number;
}

interface DailyStats {
  date: string;
  impressions: number;
  clicks: number;
  ctr: string;
}

const UnifiedBannerAnalytics: React.FC<UnifiedBannerAnalyticsProps> = ({
  bannerId,
  bannerType,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([]);
  const [timeRange, setTimeRange] = useState<7 | 30 | 90>(30);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (bannerId) {
      loadAnalyticsData();
    }
  }, [bannerId, timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Dynamic import based on banner type
      if (bannerType === 'content') {
        const { getContentBannerPerformanceMetrics, getContentBannerDailyStats } = await import('@/lib/services/contentBannerAnalyticsService');
        const [metricsData, statsData] = await Promise.all([
          getContentBannerPerformanceMetrics(bannerId),
          getContentBannerDailyStats(bannerId, timeRange)
        ]);
        setMetrics(metricsData);
        setDailyStats(statsData.reverse());
      } else {
        const { getPerformanceMetrics, getSponsorBannerDailyStats } = await import('@/lib/services/sponsorAnalyticsService');
        const [metricsData, statsData] = await Promise.all([
          getPerformanceMetrics(bannerId),
          getSponsorBannerDailyStats(bannerId, timeRange)
        ]);
        setMetrics(metricsData);
        setDailyStats(statsData.reverse());
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);
      let csvData;
      
      if (bannerType === 'content') {
        const { exportContentBannerAnalyticsToCSV } = await import('@/lib/services/contentBannerAnalyticsService');
        csvData = await exportContentBannerAnalyticsToCSV(bannerId, timeRange);
      } else {
        const { exportAnalyticsToCSV } = await import('@/lib/services/sponsorAnalyticsService');
        csvData = await exportAnalyticsToCSV(bannerId, timeRange);
      }
      
      if (csvData) {
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${bannerType}-banner-analytics-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Error exporting analytics:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-3 w-3 text-green-400" />;
    if (trend < 0) return <TrendingDown className="h-3 w-3 text-red-400" />;
    return <Activity className="h-3 w-3 text-gray-400" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-400';
    if (trend < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  const accentColor = bannerType === 'content' ? 'blue' : 'emerald';

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 ${className}`}
      >
        <div className="flex items-center justify-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-400"></div>
          <span className="ml-2 text-gray-400 font-mono text-xs">Loading analytics...</span>
        </div>
      </motion.div>
    );
  }

  if (!metrics) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`p-4 rounded-lg bg-gray-800/30 border border-gray-700/50 ${className}`}
      >
        <div className="text-center py-6">
          <BarChart3 className="h-8 w-8 text-gray-500 mx-auto mb-2" />
          <p className="text-gray-400 font-mono text-sm">No analytics data available yet</p>
          <p className="text-gray-500 font-mono text-xs mt-1">
            Data will appear once your banner starts receiving impressions
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-4 ${className}`}
    >
      {/* Compact Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BarChart3 className={`h-4 w-4 text-${accentColor}-400`} />
          <h4 className="font-mono text-sm font-semibold text-white uppercase tracking-wide">
            {bannerType === 'content' ? 'Content' : 'Sponsor'} Analytics
          </h4>
        </div>
        <div className="flex items-center gap-2">
          {/* Time Range Selector */}
          <div className="flex bg-gray-800/50 rounded-md p-0.5">
            {[7, 30, 90].map((days) => (
              <Button
                key={days}
                variant="ghost"
                size="sm"
                onClick={() => setTimeRange(days as 7 | 30 | 90)}
                className={`h-6 px-2 text-xs font-mono transition-all duration-200 ${
                  timeRange === days
                    ? `bg-${accentColor}-600 text-white`
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                {days}d
              </Button>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={isExporting}
            className="h-6 px-2 text-xs font-mono border-gray-600 text-gray-300 hover:bg-gray-700/50"
          >
            <Download className="h-3 w-3 mr-1" />
            {isExporting ? 'Exporting...' : 'CSV'}
          </Button>
        </div>
      </div>

      {/* Compact Metrics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
        {/* Impressions */}
        <div className="p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-mono text-xs text-gray-400 uppercase tracking-wide">Impressions</p>
              <p className="font-mono text-lg font-bold text-white">{formatNumber(metrics.total_impressions)}</p>
              <div className="flex items-center gap-1 mt-1">
                {getTrendIcon(metrics.impressionTrend)}
                <span className={`font-mono text-xs ${getTrendColor(metrics.impressionTrend)}`}>
                  {metrics.impressionTrend > 0 ? '+' : ''}{metrics.impressionTrend.toFixed(1)}%
                </span>
              </div>
            </div>
            <Eye className="h-6 w-6 text-blue-400" />
          </div>
        </div>

        {/* Clicks */}
        <div className="p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-mono text-xs text-gray-400 uppercase tracking-wide">Clicks</p>
              <p className="font-mono text-lg font-bold text-white">{formatNumber(metrics.total_clicks)}</p>
              <div className="flex items-center gap-1 mt-1">
                {getTrendIcon(metrics.clickTrend)}
                <span className={`font-mono text-xs ${getTrendColor(metrics.clickTrend)}`}>
                  {metrics.clickTrend > 0 ? '+' : ''}{metrics.clickTrend.toFixed(1)}%
                </span>
              </div>
            </div>
            <MousePointer className="h-6 w-6 text-emerald-400" />
          </div>
        </div>

        {/* CTR */}
        <div className="p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-mono text-xs text-gray-400 uppercase tracking-wide">CTR</p>
              <p className="font-mono text-lg font-bold text-white">
                {typeof metrics.ctr === 'string' ? parseFloat(metrics.ctr).toFixed(2) : metrics.ctr.toFixed(2)}%
              </p>
              <div className="mt-1">
                <Badge
                  variant="secondary"
                  className={`font-mono text-xs ${
                    (typeof metrics.ctr === 'string' ? parseFloat(metrics.ctr) : metrics.ctr) >= 1
                      ? 'bg-green-600/20 text-green-400 border-green-500/30'
                      : 'bg-gray-600/20 text-gray-400 border-gray-500/30'
                  }`}
                >
                  {(typeof metrics.ctr === 'string' ? parseFloat(metrics.ctr) : metrics.ctr) >= 1 ? 'Good' : 'Average'}
                </Badge>
              </div>
            </div>
            <Target className="h-6 w-6 text-purple-400" />
          </div>
        </div>

        {/* Days Active */}
        <div className="p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-mono text-xs text-gray-400 uppercase tracking-wide">Days Active</p>
              <p className="font-mono text-lg font-bold text-white">{metrics.days_active}</p>
              <div className="mt-1">
                <span className="font-mono text-xs text-gray-500">
                  Since {new Date(metrics.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </span>
              </div>
            </div>
            <Calendar className="h-6 w-6 text-orange-400" />
          </div>
        </div>
      </div>

      {/* Compact Chart */}
      <div className="p-4 rounded-lg bg-gray-800/40 border border-gray-700/30">
        <div className="flex items-center justify-between mb-3">
          <h5 className="font-mono text-sm font-semibold text-white">Performance Trend</h5>
          <span className="font-mono text-xs text-gray-400">Last {timeRange} days</span>
        </div>
        <div className="h-32">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={dailyStats}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
              <XAxis
                dataKey="date"
                stroke="#6B7280"
                fontSize={10}
                tickFormatter={formatDate}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                stroke="#6B7280" 
                fontSize={10}
                axisLine={false}
                tickLine={false}
                width={30}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '6px',
                  color: '#F9FAFB',
                  fontSize: '12px',
                  fontFamily: 'monospace'
                }}
                labelFormatter={(value) => `${formatDate(value as string)}`}
              />
              <Line
                type="monotone"
                dataKey="impressions"
                stroke="#60A5FA"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 3, stroke: '#60A5FA', strokeWidth: 2 }}
                name="Impressions"
              />
              <Line
                type="monotone"
                dataKey="clicks"
                stroke="#34D399"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 3, stroke: '#34D399', strokeWidth: 2 }}
                name="Clicks"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </motion.div>
  );
};

export default UnifiedBannerAnalytics;
