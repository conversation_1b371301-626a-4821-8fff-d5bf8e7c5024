import React from 'react';

interface SteamIconSimpleProps {
  className?: string;
  size?: number;
}

const SteamIconSimple: React.FC<SteamIconSimpleProps> = ({ 
  className = '', 
  size = 24 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="currentColor"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Steam logo - circle with gear and steam elements */}
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1" fill="none"/>
      <circle cx="15.5" cy="8.5" r="2.5" fill="currentColor"/>
      <circle cx="8.5" cy="15.5" r="2" fill="currentColor"/>
      <path d="m12 12-3.5 3.5" stroke="currentColor" strokeWidth="1.5"/>
      <path d="m12 12 3.5-3.5" stroke="currentColor" strokeWidth="1.5"/>
      <circle cx="15.5" cy="8.5" r="1" fill="none" stroke="currentColor" strokeWidth="0.5"/>
      <circle cx="8.5" cy="15.5" r="0.8" fill="none" stroke="currentColor" strokeWidth="0.5"/>
    </svg>
  );
};

export default SteamIconSimple;