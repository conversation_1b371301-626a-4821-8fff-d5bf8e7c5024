'use client';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect, useRef } from 'react';
import {
  $getSelection,
  $isRangeSelection,
  KEY_ENTER_COMMAND,
  COMMAND_PRIORITY_HIGH,
  $createLineBreakNode,
  $createParagraphNode,
  $insertNodes,
  $isElementNode,
} from 'lexical';
import { $isListItemNode } from '@lexical/list';
import { $isHeadingNode } from '@lexical/rich-text';

export default function LineBreakPlugin() {
  const [editor] = useLexicalComposerContext();
  const lastEnterTimeRef = useRef<number>(0);
  const consecutiveEntersRef = useRef(0);

  useEffect(() => {
    return editor.registerCommand(
      KEY_ENTER_COMMAND,
      (event) => {
        const selection = $getSelection();

        if (!$isRangeSelection(selection)) {
          consecutiveEntersRef.current = 0;
          return false;
        }

        const anchorNode = selection.anchor.getNode();
        const element = anchorNode.getTopLevelElement();

        // Skip special handling for lists and headings - let default behavior work
        if ($isListItemNode(element) || $isHeadingNode(element)) {
          consecutiveEntersRef.current = 0;
          return false;
        }

        // Skip if not in a paragraph-like element
        if (!$isElementNode(element) || element.getType() !== 'paragraph') {
          consecutiveEntersRef.current = 0;
          return false;
        }

        const currentTime = Date.now();
        const timeDiff = currentTime - lastEnterTimeRef.current;

        // Consider consecutive if within 1 second
        if (timeDiff < 1000) {
          consecutiveEntersRef.current += 1;
        } else {
          consecutiveEntersRef.current = 1;
        }

        lastEnterTimeRef.current = currentTime;

        // First Enter: Insert line break
        if (consecutiveEntersRef.current === 1) {
          event?.preventDefault();
          
          editor.update(() => {
            const lineBreak = $createLineBreakNode();
            $insertNodes([lineBreak]);
          });

          return true;
        }

        // Second Enter: Create new paragraph
        if (consecutiveEntersRef.current === 2) {
          event?.preventDefault();
          
          editor.update(() => {
            const paragraph = $createParagraphNode();
            $insertNodes([paragraph]);
          });

          // Reset counter after creating paragraph
          consecutiveEntersRef.current = 0;
          return true;
        }

        // For any subsequent enters, just reset and allow default behavior
        consecutiveEntersRef.current = 0;
        return false;
      },
      COMMAND_PRIORITY_HIGH // High priority to override default behavior
    );
  }, [editor]);

  return null;
}