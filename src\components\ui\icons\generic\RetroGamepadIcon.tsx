import React from 'react';

const RetroGamepadIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M4 8c-1.1 0-2 .9-2 2v4c0 1.1.9 2 2 2h2v-2H4v-4h2V8H4z"/>
    <path d="M20 8h-2v2h2v4h-2v2h2c1.1 0 2-.9 2-2v-4c0-1.1-.9-2-2-2z"/>
    <rect x="6" y="6" width="12" height="12" rx="2"/>
    <rect x="8" y="8" width="2" height="2"/>
    <rect x="8" y="11" width="2" height="2"/>
    <rect x="8" y="14" width="2" height="2"/>
    <rect x="11" y="11" width="2" height="2"/>
    <circle cx="15" cy="9" r="1"/>
    <circle cx="17" cy="11" r="1"/>
    <circle cx="15" cy="13" r="1"/>
    <circle cx="13" cy="11" r="1"/>
  </svg>
);

export default RetroGamepadIcon;