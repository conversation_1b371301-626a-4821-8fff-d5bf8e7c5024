import React from 'react';

const UploadBannerIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <rect x="2" y="8" width="20" height="8" rx="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M12 4v8m-4-4l4-4 4 4" stroke="currentColor" strokeWidth="2" fill="none"/>
    <rect x="6" y="18" width="12" height="1"/>
    <rect x="8" y="20" width="8" height="1"/>
    <rect x="4" y="10" width="1" height="1"/>
    <rect x="19" y="10" width="1" height="1"/>
    <rect x="4" y="14" width="1" height="1"/>
    <rect x="19" y="14" width="1" height="1"/>
    <circle cx="7" cy="12" r="0.5"/>
    <circle cx="17" cy="12" r="0.5"/>
  </svg>
);

export default UploadBannerIcon;