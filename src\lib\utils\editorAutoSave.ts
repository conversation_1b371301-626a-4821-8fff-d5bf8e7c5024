// src/lib/utils/editorAutoSave.ts
'use client';

export interface AutoSaveData {
  content: string;
  timestamp: number;
  userId: string;
  reviewId?: string;
  gameName?: string;
  reviewTitle?: string;
  contentHash: string;
}

export interface AutoSaveOptions {
  userId: string;
  reviewId?: string;
  gameName?: string;
  reviewTitle?: string;
  debounceMs?: number;
  maxAge?: number; // in milliseconds
}

class EditorAutoSave {
  private static instance: EditorAutoSave;
  private saveTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private readonly STORAGE_PREFIX = 'lexical-autosave';
  private readonly DEFAULT_DEBOUNCE = 30000; // 30 seconds
  private readonly DEFAULT_MAX_AGE = 7 * 24 * 60 * 60 * 1000; // 7 days

  static getInstance(): EditorAutoSave {
    if (!EditorAutoSave.instance) {
      EditorAutoSave.instance = new EditorAutoSave();
    }
    return EditorAutoSave.instance;
  }

  private constructor() {
    // Clean up old auto-saves on initialization
    this.cleanupOldSaves();
  }

  /**
   * Generate a unique key for the auto-save
   */
  private generateKey(options: AutoSaveOptions): string {
    const { userId, reviewId } = options;
    return `${this.STORAGE_PREFIX}-${userId}-${reviewId || 'new'}`;
  }

  /**
   * Generate a simple hash for content comparison
   */
  private generateContentHash(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Save content to localStorage with debouncing
   */
  save(content: string, options: AutoSaveOptions): void {
    if (typeof window === 'undefined') return;

    const key = this.generateKey(options);
    const debounceMs = options.debounceMs || this.DEFAULT_DEBOUNCE;

    // Clear existing timeout for this key
    const existingTimeout = this.saveTimeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      this.performSave(content, options, key);
      this.saveTimeouts.delete(key);
    }, debounceMs);

    this.saveTimeouts.set(key, timeout);
  }

  /**
   * Immediately save content without debouncing
   */
  saveImmediate(content: string, options: AutoSaveOptions): void {
    if (typeof window === 'undefined') {
      console.log('Auto-save skipped: window undefined');
      return;
    }

    const key = this.generateKey(options);
    console.log('Auto-save key generated:', key);
    this.performSave(content, options, key);
  }

  /**
   * Perform the actual save operation
   */
  private performSave(content: string, options: AutoSaveOptions, key: string): void {
    try {
      const contentHash = this.generateContentHash(content);
      console.log('Auto-save: Generated content hash:', contentHash);

      // Check if content has actually changed
      const existing = this.load(options);
      if (existing && existing.contentHash === contentHash) {
        console.log('Auto-save skipped: content unchanged');
        return; // No changes, skip save
      }

      const saveData: AutoSaveData = {
        content,
        timestamp: Date.now(),
        userId: options.userId,
        reviewId: options.reviewId,
        gameName: options.gameName,
        reviewTitle: options.reviewTitle,
        contentHash,
      };

      localStorage.setItem(key, JSON.stringify(saveData));
      console.log('Auto-save: Data saved to localStorage with key:', key);

      // Dispatch event for UI feedback
      window.dispatchEvent(new CustomEvent('lexical-autosave-success', {
        detail: { key, timestamp: saveData.timestamp }
      }));

    } catch (error) {
      console.warn('Auto-save failed:', error);
      
      // Dispatch error event
      window.dispatchEvent(new CustomEvent('lexical-autosave-error', {
        detail: { error: error instanceof Error ? error.message : 'Unknown error' }
      }));
    }
  }

  /**
   * Load saved content
   */
  load(options: AutoSaveOptions): AutoSaveData | null {
    if (typeof window === 'undefined') return null;

    try {
      const key = this.generateKey(options);
      const saved = localStorage.getItem(key);
      
      if (!saved) return null;

      const data: AutoSaveData = JSON.parse(saved);
      
      // Check if data is too old
      const maxAge = options.maxAge || this.DEFAULT_MAX_AGE;
      if (Date.now() - data.timestamp > maxAge) {
        this.remove(options);
        return null;
      }

      return data;
    } catch (error) {
      console.warn('Failed to load auto-save:', error);
      return null;
    }
  }

  /**
   * Remove saved content
   */
  remove(options: AutoSaveOptions): void {
    if (typeof window === 'undefined') return;

    try {
      const key = this.generateKey(options);
      localStorage.removeItem(key);
      
      // Clear any pending save timeout
      const timeout = this.saveTimeouts.get(key);
      if (timeout) {
        clearTimeout(timeout);
        this.saveTimeouts.delete(key);
      }
    } catch (error) {
      console.warn('Failed to remove auto-save:', error);
    }
  }

  /**
   * Check if auto-save exists
   */
  exists(options: AutoSaveOptions): boolean {
    return this.load(options) !== null;
  }

  /**
   * Get all auto-saves for a user
   */
  getAllForUser(userId: string): AutoSaveData[] {
    if (typeof window === 'undefined') return [];

    const saves: AutoSaveData[] = [];
    const prefix = `${this.STORAGE_PREFIX}-${userId}-`;

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const data = localStorage.getItem(key);
          if (data) {
            const parsed: AutoSaveData = JSON.parse(data);
            saves.push(parsed);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to get auto-saves for user:', error);
    }

    return saves.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Clean up old auto-saves
   */
  private cleanupOldSaves(): void {
    if (typeof window === 'undefined') return;

    try {
      const now = Date.now();
      const keysToRemove: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          const data = localStorage.getItem(key);
          if (data) {
            const parsed: AutoSaveData = JSON.parse(data);
            if (now - parsed.timestamp > this.DEFAULT_MAX_AGE) {
              keysToRemove.push(key);
            }
          }
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      if (keysToRemove.length > 0) {
        console.log(`Cleaned up ${keysToRemove.length} old auto-saves`);
      }
    } catch (error) {
      console.warn('Failed to cleanup old auto-saves:', error);
    }
  }

  /**
   * Clear all pending save operations
   */
  clearPendingSaves(): void {
    this.saveTimeouts.forEach(timeout => clearTimeout(timeout));
    this.saveTimeouts.clear();
  }

  /**
   * Get formatted time since last save
   */
  getTimeSinceLastSave(options: AutoSaveOptions): string | null {
    const data = this.load(options);
    if (!data) return null;

    const now = Date.now();
    const diff = now - data.timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)} minutes ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)} hours ago`;
    return `${Math.floor(diff / 86400000)} days ago`;
  }
}

// Export singleton instance
export const editorAutoSave = EditorAutoSave.getInstance();

// Utility functions for easier usage
export const saveEditorContent = (content: string, options: AutoSaveOptions) => {
  editorAutoSave.save(content, options);
};

export const loadEditorContent = (options: AutoSaveOptions): AutoSaveData | null => {
  return editorAutoSave.load(options);
};

export const removeEditorContent = (options: AutoSaveOptions) => {
  editorAutoSave.remove(options);
};

export const hasAutoSave = (options: AutoSaveOptions): boolean => {
  return editorAutoSave.exists(options);
};
