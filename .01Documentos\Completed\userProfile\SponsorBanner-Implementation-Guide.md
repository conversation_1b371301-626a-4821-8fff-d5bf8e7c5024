# Sponsor Banner Module - Production Implementation Guide

## Overview

This guide outlines the steps required to fully implement the Sponsor Banner module in production. The feature allows users to add a sponsor banner with an image URL and affiliate link to their profile's side navbar.

Currently, the implementation uses local storage for demonstration purposes. This document details all necessary steps to transform it into a production-ready feature with proper backend integration.

## Table of Contents

1. [Backend Implementation](#1-backend-implementation)
2. [Frontend Integration](#2-frontend-integration)
3. [Testing](#3-testing)
4. [Deployment](#4-deployment)
5. [Analytics & Tracking](#5-analytics--tracking)
6. [Documentation](#6-documentation)

## 1. Backend Implementation

### 1.1. Database Schema

Create a new table in the database to store sponsor banner data:

```sql
CREATE TABLE user_sponsor_banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  img_url TEXT NOT NULL,
  url TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  impression_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups by user_id
CREATE INDEX idx_user_sponsor_banners_user_id ON user_sponsor_banners(user_id);

-- Add RLS policies for security
ALTER TABLE user_sponsor_banners ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own sponsor data
CREATE POLICY user_sponsor_banners_select ON user_sponsor_banners
  FOR SELECT USING (auth.uid() = user_id);

-- Allow users to insert their own sponsor data
CREATE POLICY user_sponsor_banners_insert ON user_sponsor_banners
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to update only their own sponsor data
CREATE POLICY user_sponsor_banners_update ON user_sponsor_banners
  FOR UPDATE USING (auth.uid() = user_id);

-- Allow users to delete only their own sponsor data
CREATE POLICY user_sponsor_banners_delete ON user_sponsor_banners
  FOR DELETE USING (auth.uid() = user_id);
```

### 1.2. API Endpoints

Create the necessary API endpoints for CRUD operations on sponsor banners:

#### 1.2.1. Supabase Functions

If using Supabase, update or create API functions in `lib/api/sponsorBannerService.ts`:

```typescript
import { supabase } from './supabaseClient';

// Get sponsor banner for a user
export async function getUserSponsorBanner(userId: string) {
  const { data, error } = await supabase
    .from('user_sponsor_banners')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    console.error('Error fetching sponsor banner:', error);
    return null;
  }

  return data;
}

// Save or update a sponsor banner
export async function saveSponsorBanner({
  userId,
  imgUrl,
  url,
  isActive = true,
}: {
  userId: string;
  imgUrl: string;
  url: string;
  isActive?: boolean;
}) {
  // Check if user already has a sponsor banner
  const existing = await getUserSponsorBanner(userId);

  let response;

  if (existing) {
    // Update existing record
    response = await supabase
      .from('user_sponsor_banners')
      .update({
        img_url: imgUrl,
        url: url,
        is_active: isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', existing.id)
      .select();
  } else {
    // Create new record
    response = await supabase
      .from('user_sponsor_banners')
      .insert({
        user_id: userId,
        img_url: imgUrl,
        url: url,
        is_active: isActive,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select();
  }

  const { data, error } = response;

  if (error) {
    console.error('Error saving sponsor banner:', error);
    throw new Error(error.message);
  }

  return data[0];
}

// Delete or deactivate a sponsor banner
export async function deactivateSponsorBanner(userId: string) {
  const { data, error } = await supabase
    .from('user_sponsor_banners')
    .update({ 
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .select();

  if (error) {
    console.error('Error deactivating sponsor banner:', error);
    throw new Error(error.message);
  }

  return data[0];
}

// Track impressions when banner is viewed
export async function trackSponsorImpression(bannerId: string) {
  const { data, error } = await supabase.rpc('increment_sponsor_impression', {
    banner_id: bannerId
  });
  
  if (error) {
    console.error('Error tracking impression:', error);
    return false;
  }
  
  return true;
}

// Track clicks when banner link is clicked
export async function trackSponsorClick(bannerId: string) {
  const { data, error } = await supabase.rpc('increment_sponsor_click', {
    banner_id: bannerId
  });
  
  if (error) {
    console.error('Error tracking click:', error);
    return false;
  }
  
  return true;
}
```

#### 1.2.2. Database Functions

Create stored procedures to track impressions and clicks:

```sql
CREATE OR REPLACE FUNCTION increment_sponsor_impression(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET impression_count = impression_count + 1
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_sponsor_click(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET click_count = click_count + 1
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;
```

## 2. Frontend Integration

### 2.1. Update SponsorBannerConfig Component

Replace local storage implementation with API calls in `src/components/dashboard/SponsorBannerConfig.tsx`:

```typescript
// Update loadSponsorData function
const loadSponsorData = async () => {
  try {
    setIsLoading(true);
    
    const data = await getUserSponsorBanner(userId);
    
    if (data) {
      setSponsorData({
        id: data.id,
        user_id: data.user_id,
        img_url: data.img_url,
        url: data.url,
        is_active: data.is_active
      });
      setCurrentSponsor(data);
    }
  } catch (error) {
    console.error('Error loading sponsor data:', error);
    toast({
      title: "Error",
      description: "Failed to load sponsor data",
      variant: "destructive",
    });
  } finally {
    setIsLoading(false);
  }
};

// Update handleSave function
const handleSave = async () => {
  if (!validateForm()) return;

  try {
    setIsSaving(true);
    
    const savedData = await saveSponsorBanner({
      userId,
      imgUrl: sponsorData.img_url,
      url: sponsorData.url,
      isActive: true
    });
    
    setCurrentSponsor(savedData);
    setSponsorData({
      id: savedData.id,
      user_id: savedData.user_id,
      img_url: savedData.img_url,
      url: savedData.url,
      is_active: savedData.is_active
    });
    
    toast({
      title: "Success",
      description: "Sponsor banner settings saved successfully",
    });
  } catch (error) {
    console.error('Error saving sponsor data:', error);
    toast({
      title: "Error",
      description: "Failed to save sponsor banner settings",
      variant: "destructive",
    });
  } finally {
    setIsSaving(false);
  }
};

// Update handleDeactivate function
const handleDeactivate = async () => {
  try {
    setIsSaving(true);
    
    if (currentSponsor) {
      const updatedData = await deactivateSponsorBanner(userId);
      
      setCurrentSponsor(updatedData);
      setSponsorData({
        ...sponsorData,
        is_active: false
      });
      
      toast({
        title: "Success",
        description: "Sponsor banner deactivated",
      });
    }
  } catch (error) {
    console.error('Error deactivating sponsor banner:', error);
    toast({
      title: "Error",
      description: "Failed to deactivate sponsor banner",
      variant: "destructive",
    });
  } finally {
    setIsSaving(false);
  }
};
```

### 2.2. Update SponsorBanner Component

Replace local storage with API calls in `src/components/layout/SponsorBanner.tsx`:

```typescript
// Update loadSponsorData function
const loadSponsorData = async () => {
  try {
    setIsLoading(true);
    
    const data = await getUserSponsorBanner(userId);
    
    if (data && data.is_active) {
      setSponsorData(data);
      
      // Track impression after a short delay to ensure banner is actually viewed
      setTimeout(() => {
        if (data.id) {
          trackSponsorImpression(data.id).catch(console.error);
        }
      }, 1000);
    } else {
      setSponsorData(null);
    }
  } catch (error) {
    console.error('Error loading sponsor data:', error);
    setSponsorData(null);
  } finally {
    setIsLoading(false);
  }
};

// Update click handler to track clicks
const handleBannerClick = (e: React.MouseEvent) => {
  if (sponsorData?.id) {
    trackSponsorClick(sponsorData.id).catch(console.error);
  }
};
```

Add the click handler to the banner link:

```jsx
<a 
  href={sponsorData.url}
  target="_blank"
  rel="noopener noreferrer sponsored"
  className="block rounded-lg overflow-hidden transition-transform hover:scale-105 focus:scale-105 active:scale-95"
  title="Sponsored Link"
  onClick={handleBannerClick}
>
  {/* ... */}
</a>
```

### 2.3. Import Services

Make sure to add the necessary imports to both components:

```typescript
import { 
  getUserSponsorBanner,
  saveSponsorBanner,
  deactivateSponsorBanner,
  trackSponsorImpression,
  trackSponsorClick
} from '@/lib/api/sponsorBannerService';
```

## 3. Testing

### 3.1. Unit Tests

Create unit tests for the Sponsor banner components and services:

Create file: `src/__tests__/components/SponsorBannerConfig.test.tsx`
```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { mockUseToast } from '@/lib/test/mockHooks';
import SponsorBannerConfig from '@/components/dashboard/SponsorBannerConfig';
import * as sponsorService from '@/lib/api/sponsorBannerService';

// Mock the sponsor service
jest.mock('@/lib/api/sponsorBannerService');

describe('SponsorBannerConfig', () => {
  const mockUserId = 'test-user-123';
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseToast();
  });
  
  test('renders sponsor banner configuration form', () => {
    render(<SponsorBannerConfig userId={mockUserId} />);
    
    expect(screen.getByText('Sponsor Banner')).toBeInTheDocument();
    expect(screen.getByLabelText(/Banner Image URL/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Affiliate Link URL/i)).toBeInTheDocument();
    expect(screen.getByText(/Activate Sponsor Banner/i)).toBeInTheDocument();
  });
  
  test('loads existing sponsor data on mount', async () => {
    const mockData = {
      id: 'banner-123',
      user_id: mockUserId,
      img_url: 'https://example.com/image.png',
      url: 'https://example.com/affiliate',
      is_active: true
    };
    
    (sponsorService.getUserSponsorBanner as jest.Mock).mockResolvedValue(mockData);
    
    render(<SponsorBannerConfig userId={mockUserId} />);
    
    await waitFor(() => {
      expect(sponsorService.getUserSponsorBanner).toHaveBeenCalledWith(mockUserId);
      expect(screen.getByDisplayValue('https://example.com/image.png')).toBeInTheDocument();
      expect(screen.getByDisplayValue('https://example.com/affiliate')).toBeInTheDocument();
      expect(screen.getByText('Current Sponsor Banner')).toBeInTheDocument();
    });
  });
  
  // Additional tests for form validation, saving, etc.
});
```

Create file: `src/__tests__/components/SponsorBanner.test.tsx`
```typescript
import { render, screen } from '@testing-library/react';
import SponsorBanner from '@/components/layout/SponsorBanner';
import * as sponsorService from '@/lib/api/sponsorBannerService';

jest.mock('@/lib/api/sponsorBannerService');

describe('SponsorBanner', () => {
  const mockUserId = 'test-user-123';
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders sponsor banner when active sponsor data exists', async () => {
    const mockData = {
      id: 'banner-123',
      user_id: mockUserId,
      img_url: 'https://example.com/image.png',
      url: 'https://example.com/affiliate',
      is_active: true
    };
    
    (sponsorService.getUserSponsorBanner as jest.Mock).mockResolvedValue(mockData);
    
    render(<SponsorBanner userId={mockUserId} />);
    
    // Wait for data to load and component to render
    const sponsorLabel = await screen.findByText('Sponsor');
    expect(sponsorLabel).toBeInTheDocument();
    
    const bannerImage = screen.getByAltText('Sponsor');
    expect(bannerImage).toBeInTheDocument();
    expect(bannerImage).toHaveAttribute('src', 'https://example.com/image.png');
  });
  
  test('does not render when sponsor is not active', async () => {
    const mockData = {
      id: 'banner-123',
      user_id: mockUserId,
      img_url: 'https://example.com/image.png',
      url: 'https://example.com/affiliate',
      is_active: false
    };
    
    (sponsorService.getUserSponsorBanner as jest.Mock).mockResolvedValue(mockData);
    
    const { container } = render(<SponsorBanner userId={mockUserId} />);
    
    // Component should not render anything
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(container.innerHTML).toBe('');
  });
  
  // Additional tests for tracking impressions, clicks, etc.
});
```

### 3.2. Integration Testing

Create end-to-end tests to verify the full workflow:

1. User configures a sponsor banner in the dashboard
2. Banner appears in the side navbar
3. Clicks are tracked correctly

## 4. Deployment

### 4.1. Database Migrations

Create migration file in `src/migrations/YYYYMMDD_create_sponsor_banner_table.sql` with the database schema defined above.

### 4.2. Environment Variables

No additional environment variables are needed for this feature.

### 4.3. Deployment Checklist

- [ ] Run database migrations on production
- [ ] Deploy updated frontend components
- [ ] Verify API endpoints are functioning correctly
- [ ] Test sponsor banner configuration on production
- [ ] Validate data storage and retrieval

## 5. Analytics & Tracking

### 5.1. Data Collection

The design includes impression and click tracking. To enhance analytics:

1. Add timestamp fields for impressions and clicks:

```sql
ALTER TABLE user_sponsor_banners 
ADD COLUMN last_impression_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN last_click_at TIMESTAMP WITH TIME ZONE;
```

2. Update tracking functions:

```sql
CREATE OR REPLACE FUNCTION increment_sponsor_impression(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET 
    impression_count = impression_count + 1,
    last_impression_at = NOW()
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_sponsor_click(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET 
    click_count = click_count + 1,
    last_click_at = NOW()
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;
```

### 5.2. Dashboard Analytics

For a future enhancement, create a Sponsor Analytics section in the dashboard to display:

- Total impressions
- Total clicks
- Click-through rate (CTR)
- Time-based performance charts

## 6. Documentation

### 6.1. User-Facing Documentation

Create user-facing documentation explaining how to set up and use the sponsor banner feature:

- Image size recommendations (300x300px)
- Acceptable image formats (JPG, PNG, GIF, WebP)
- Guidelines for affiliate links
- Best practices for effective banners

### 6.2. API Documentation

Document the API endpoints for the Sponsor Banner service:

| Endpoint | Method | Description |
|----------|--------|-------------|
| `getUserSponsorBanner(userId)` | GET | Retrieves a user's sponsor banner data |
| `saveSponsorBanner(data)` | POST/PUT | Creates or updates a sponsor banner |
| `deactivateSponsorBanner(userId)` | PUT | Deactivates a sponsor banner |
| `trackSponsorImpression(bannerId)` | POST | Tracks a banner impression |
| `trackSponsorClick(bannerId)` | POST | Tracks a banner click |

## Conclusion

Following this guide will ensure the complete production implementation of the Sponsor Banner module. The feature allows users to configure, display, and track performance of their sponsor banners on their profile. By integrating with proper backend services and adding analytics, this module provides a valuable monetization tool for users while maintaining the platform's aesthetic and user experience.
