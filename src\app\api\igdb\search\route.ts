// src/app/api/igdb/search/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { fetchIGDBData } from '@/lib/igdb-api';

export async function POST(request: NextRequest) {
  try {
    // Parse the JSON body
    const body = await request.json();
    const { title } = body;

    if (!title || typeof title !== 'string') {
      return NextResponse.json(
        { error: 'Invalid search query' },
        { status: 400 }
      );
    }

    // Construct the IGDB API query
    const query = `
      search "${title}";
      fields name, id, cover.url, first_release_date, 
             platforms.name, genres.name, 
             involved_companies.company.name, involved_companies.developer, involved_companies.publisher,
             rating, summary, total_rating, aggregated_rating, aggregated_rating_count;
      limit 20;
    `;

    // Fetch data from IGDB
    const data = await fetchIGDBData('games', query);

    // Process the response to match your frontend interface
    const games = data.map((game: any) => ({
      id: game.id,
      name: game.name,
      cover: game.cover ? {
        id: game.cover.id,
        url: game.cover.url
      } : undefined,
      releaseDate: game.first_release_date,
      platforms: game.platforms ? game.platforms.map((p: any) => p.name) : [],
      genres: game.genres ? game.genres.map((g: any) => g.name) : [],
      involved_companies: game.involved_companies || [],
      developers: game.involved_companies ? game.involved_companies
        .filter((ic: any) => ic.developer)
        .map((ic: any) => ({
          id: ic.company.id,
          name: ic.company.name
        })) : [],
      rating: game.rating,
      total_rating: game.total_rating,
      summary: game.summary,
      aggregated_rating: game.aggregated_rating,
      aggregated_rating_count: game.aggregated_rating_count
    }));

    return NextResponse.json(games);
  } catch (error) {
    console.error('IGDB API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch game data' },
      { status: 500 }
    );
  }
}