'use client';

import { useState, useEffect } from 'react';
import { getUserTotalViews, getUserTopReviews, getUserAnalyticsSummary } from '@/lib/services/userAnalyticsService';

interface UseUserAnalyticsReturn {
  totalViews: number;
  uniqueViews: number;
  reviewCount: number;
  averageViewsPerReview: number;
  totalLikes: number;
  totalComments: number;
  averageScore: number;
  engagementRate: number;
  topReviews: Array<{
    id: string;
    title: string;
    slug: string;
    game_name: string;
    view_count: number;
    like_count: number;
    overall_score: number;
  }>;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useUserAnalytics(userId: string | null): UseUserAnalyticsReturn {
  const [data, setData] = useState({
    totalViews: 0,
    uniqueViews: 0,
    reviewCount: 0,
    averageViewsPerReview: 0,
    totalLikes: 0,
    totalComments: 0,
    averageScore: 0,
    engagementRate: 0,
    topReviews: [] as Array<{
      id: string;
      title: string;
      slug: string;
      game_name: string;
      view_count: number;
      like_count: number;
      overall_score: number;
    }>
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = async () => {
    if (!userId) {
      setError('User ID is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch all analytics data in parallel
      const [viewsResponse, summaryResponse, topReviewsResponse] = await Promise.all([
        getUserTotalViews(userId),
        getUserAnalyticsSummary(userId),
        getUserTopReviews(userId, 5)
      ]);

      if (!viewsResponse.success) {
        throw new Error(viewsResponse.error || 'Failed to fetch views data');
      }

      if (!summaryResponse.success) {
        throw new Error(summaryResponse.error || 'Failed to fetch summary data');
      }

      if (!topReviewsResponse.success) {
        throw new Error(topReviewsResponse.error || 'Failed to fetch top reviews');
      }

      setData({
        totalViews: viewsResponse.data?.totalViews || 0,
        uniqueViews: viewsResponse.data?.uniqueViews || 0,
        reviewCount: viewsResponse.data?.reviewCount || 0,
        averageViewsPerReview: viewsResponse.data?.averageViewsPerReview || 0,
        totalLikes: summaryResponse.data?.totalLikes || 0,
        totalComments: summaryResponse.data?.totalComments || 0,
        averageScore: summaryResponse.data?.averageScore || 0,
        engagementRate: summaryResponse.data?.engagementRate || 0,
        topReviews: topReviewsResponse.data || []
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchAnalytics();
    }
  }, [userId]);

  return {
    ...data,
    isLoading,
    error,
    refetch: fetchAnalytics
  };
}

// Hook for just total views (lighter version)
export function useUserTotalViews(userId: string | null) {
  const [data, setData] = useState({
    totalViews: 0,
    reviewCount: 0,
    averageViewsPerReview: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchViews = async () => {
    if (!userId) {
      setError('User ID is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await getUserTotalViews(userId);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch views data');
      }

      setData({
        totalViews: response.data?.totalViews || 0,
        reviewCount: response.data?.reviewCount || 0,
        averageViewsPerReview: response.data?.averageViewsPerReview || 0
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchViews();
    }
  }, [userId]);

  return {
    ...data,
    isLoading,
    error,
    refetch: fetchViews
  };
}