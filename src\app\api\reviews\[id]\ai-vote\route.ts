import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

type VoteType = 'human' | 'ai' | 'unsure';

interface AIVoteResponse {
  success: boolean;
  userVote?: VoteType;
  stats: {
    humanVotes: number;
    aiVotes: number;
    unsureVotes: number;
    totalVotes: number;
    humanConfidence: number; // Percentage (0-100)
  };
  error?: string;
}

/**
 * POST /api/reviews/[id]/ai-vote - Submit or update AI detection vote
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<AIVoteResponse>> {
  try {
    const reviewId = params.id;
    const { vote }: { vote: VoteType } = await request.json();
    
    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    if (!vote || !['human', 'ai', 'unsure'].includes(vote)) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Valid vote type is required (human, ai, or unsure)' 
        },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    // Check if review exists
    const { data: review, error: reviewError } = await supabase
      .from('reviews')
      .select('id')
      .eq('id', reviewId)
      .single();

    if (reviewError || !review) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Review not found' 
        },
        { status: 404 }
      );
    }

    // Check if user already voted on this review
    const { data: existingVote, error: voteCheckError } = await supabase
      .from('ai_detection_votes')
      .select('id, vote_type')
      .eq('review_id', reviewId)
      .eq('user_id', user.id)
      .single();

    if (voteCheckError && voteCheckError.code !== 'PGRST116') {
      console.error('Error checking existing vote:', voteCheckError);
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Failed to check vote status' 
        },
        { status: 500 }
      );
    }

    if (existingVote) {
      // Update existing vote
      const { error: updateError } = await supabase
        .from('ai_detection_votes')
        .update({ 
          vote_type: vote,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingVote.id);

      if (updateError) {
        console.error('Error updating AI vote:', updateError);
        return NextResponse.json(
          { 
            success: false, 
            stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
            error: 'Failed to update vote' 
          },
          { status: 500 }
        );
      }
    } else {
      // Insert new vote
      const { error: insertError } = await supabase
        .from('ai_detection_votes')
        .insert({
          review_id: reviewId,
          user_id: user.id,
          vote_type: vote,
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error adding AI vote:', insertError);
        return NextResponse.json(
          { 
            success: false, 
            stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
            error: 'Failed to add vote' 
          },
          { status: 500 }
        );
      }
    }

    // Get updated vote statistics
    const { data: voteStats, error: statsError } = await supabase
      .from('ai_detection_votes')
      .select('vote_type')
      .eq('review_id', reviewId);

    if (statsError) {
      console.error('Error fetching vote stats:', statsError);
      // Don't fail the operation, just return basic stats
    }

    const stats = calculateVoteStats(voteStats || []);

    return NextResponse.json({
      success: true,
      userVote: vote,
      stats
    });

  } catch (error) {
    console.error('Error in AI vote endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/reviews/[id]/ai-vote - Get AI detection vote stats and user's vote
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<AIVoteResponse>> {
  try {
    const reviewId = params.id;
    
    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user (optional for GET)
    const { data: { user } } = await supabase.auth.getUser();

    // Check if review exists
    const { data: review, error: reviewError } = await supabase
      .from('reviews')
      .select('id')
      .eq('id', reviewId)
      .single();

    if (reviewError || !review) {
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Review not found' 
        },
        { status: 404 }
      );
    }

    // Get all votes for this review
    const { data: voteStats, error: statsError } = await supabase
      .from('ai_detection_votes')
      .select('vote_type')
      .eq('review_id', reviewId);

    if (statsError) {
      console.error('Error fetching vote stats:', statsError);
      return NextResponse.json(
        { 
          success: false, 
          stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
          error: 'Failed to fetch vote stats' 
        },
        { status: 500 }
      );
    }

    let userVote: VoteType | undefined;

    // Get user's vote if authenticated
    if (user) {
      const { data: existingVote } = await supabase
        .from('ai_detection_votes')
        .select('vote_type')
        .eq('review_id', reviewId)
        .eq('user_id', user.id)
        .single();

      userVote = existingVote?.vote_type as VoteType;
    }

    const stats = calculateVoteStats(voteStats || []);

    return NextResponse.json({
      success: true,
      userVote,
      stats
    });

  } catch (error) {
    console.error('Error in AI vote status endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        stats: { humanVotes: 0, aiVotes: 0, unsureVotes: 0, totalVotes: 0, humanConfidence: 50 },
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * Calculate vote statistics from raw vote data
 */
function calculateVoteStats(votes: Array<{ vote_type: string }>) {
  const humanVotes = votes.filter(v => v.vote_type === 'human').length;
  const aiVotes = votes.filter(v => v.vote_type === 'ai').length;
  const unsureVotes = votes.filter(v => v.vote_type === 'unsure').length;
  const totalVotes = votes.length;
  
  // Calculate human confidence percentage
  // Unsure votes are weighted as 0.5 towards human confidence
  const humanConfidence = totalVotes > 0 
    ? Math.round(((humanVotes + (unsureVotes * 0.5)) / totalVotes) * 100)
    : 50; // Default to 50% when no votes

  return {
    humanVotes,
    aiVotes,
    unsureVotes,
    totalVotes,
    humanConfidence
  };
}