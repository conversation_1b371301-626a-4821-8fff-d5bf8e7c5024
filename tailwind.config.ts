import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			},
        // 🚨 ADDED: Custom colors for Lexical editor
        review: {
          'text-primary': 'var(--review-text-primary, #f1f5f9)',
          'text-secondary': 'var(--review-text-secondary, #94a3b8)',
          'bg-primary': 'var(--review-bg-primary, rgba(15, 23, 42, 0.9))',
          'bg-secondary': 'var(--review-bg-secondary, rgba(30, 41, 59, 0.8))',
          'accent': 'var(--review-accent, rgba(139, 92, 246, 0.4))',
          'border': 'var(--review-border, rgba(139, 92, 246, 0.2))',
        }
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		fontFamily: {
        'press-start-2p': ['"Press Start 2P"', 'cursive'],
        'pixelify-sans': ['var(--font-pixelify-sans)', 'sans-serif'],
        // 🚨 ADDED: Monospace fonts for editor
        'mono': ['Fira Code', 'Monaco', 'Cascadia Code', 'monospace'],
      },
      textShadow: {
        pixel: '2px 2px 0px hsl(var(--foreground) / 0.7)',
        'pixel-primary': '3px 3px 0px hsl(var(--primary))',
        'pixel-light': '2px 2px 0px hsl(var(--foreground) / 0.1)',
        'username-effect': '5px 4px 0px rgba(169,169,169,1)',
      },
      // 🚨 ADDED: Backdrop blur utilities
      backdropBlur: {
        xs: '2px',
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
      },
      screens: {
        'bp1360': '1360px',
      },
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
        'bounce-slow': {
          '0%, 100%': {
            transform: 'translateY(-15%)',
            animationTimingFunction: 'cubic-bezier(0.8,0,1,1)',
          },
          '50%': {
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0,0,0.2,1)',
          },
        },
        'tv-on-effect': {
          '0%': { transform: 'scaleX(0.05) scaleY(0.01)', opacity: '0' },
          '30%': { transform: 'scaleX(1) scaleY(0.01)', opacity: '0.5' },
          '60%': { transform: 'scaleX(1) scaleY(0.01)', opacity: '1' },
          '100%': { transform: 'scaleX(1) scaleY(1)', opacity: '1' },
        },
        'tv-off-effect': {
          '0%': { transform: 'scaleX(1) scaleY(1)', opacity: '1' },
          '40%': { transform: 'scaleX(1) scaleY(0.01)', opacity: '1' },
          '70%': { transform: 'scaleX(0.05) scaleY(0.01)', opacity: '0.5' },
          '100%': { transform: 'scaleX(0) scaleY(0.01)', opacity: '0' },
        },
        // 🚨 ADDED: Lexical editor animations
        'monetization-pulse': {
          '0%, 100%': { opacity: '0' },
          '50%': { opacity: '1' },
        },
        'toolbar-glow': {
          '0%': { boxShadow: '0 0 5px rgba(139, 92, 246, 0.3)' },
          '50%': { boxShadow: '0 0 20px rgba(139, 92, 246, 0.6)' },
          '100%': { boxShadow: '0 0 5px rgba(139, 92, 246, 0.3)' },
        }
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
        'bounce-slow': 'bounce-slow 1.5s infinite',
        'tv-on': 'tv-on-effect 0.35s ease-out forwards',
        'tv-off': 'tv-off-effect 0.25s ease-in forwards',
        // 🚨 ADDED: Lexical editor animations
        'monetization-pulse': 'monetization-pulse 2s ease-in-out',
        'toolbar-glow': 'toolbar-glow 3s ease-in-out infinite',
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    // 🚨 ADDED: Typography plugin for Lexical editor
    require("@tailwindcss/typography"),
    function ({ addUtilities, theme }: { addUtilities: any, theme: any }) {
      const newUtilities = {
        '.text-shadow-pixel': {
          textShadow: theme('textShadow.pixel'),
        },
        '.text-shadow-pixel-primary': {
          textShadow: theme('textShadow.pixel-primary'),
        },
        '.text-shadow-pixel-light': {
          textShadow: theme('textShadow.pixel-light'),
        },
        '.text-shadow-username-effect': {
          textShadow: theme('textShadow.username-effect'),
        },
        // 🚨 ADDED: Lexical editor utilities
        '.backdrop-blur-editor': {
          backdropFilter: 'blur(8px)',
        },
        '.gradient-border': {
          background: 'linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.2))',
          border: '1px solid rgba(139, 92, 246, 0.3)',
        },
        '.editor-glass': {
          background: 'rgba(15, 23, 42, 0.8)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        }
      }
      addUtilities(newUtilities, ['responsive', 'hover'])
    }
  ],
} satisfies Config;