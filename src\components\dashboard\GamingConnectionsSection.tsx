'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle,
  Gamepad2,
  Shield,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import SteamIcon from '@/components/ui/icons/gaming/SteamIcon';
import PlayStationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';
import NintendoSwitchIcon from '@/components/ui/icons/gaming/NintendoSwitchIcon';
import EpicGamesIcon from '@/components/ui/icons/gaming/EpicGamesIcon';

// Gaming Platform Icon Wrappers
const SteamIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <SteamIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const PSNIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <PlayStationIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const XboxIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <XboxIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const EpicIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <EpicGamesIcon className="w-5 h-5 text-slate-300" />
  </div>
);

const NintendoIconWrapper = () => (
  <div className="w-8 h-8 bg-slate-800/60 border border-slate-700/50 rounded-lg flex items-center justify-center">
    <NintendoSwitchIcon className="w-5 h-5 text-slate-300" />
  </div>
);

interface GamingConnection {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  connected: boolean;
  apiAvailable: boolean;
}

const gamingConnections: GamingConnection[] = [
  {
    id: 'steam',
    name: 'Steam',
    description: 'Import game library and achievements',
    icon: <SteamIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'psn',
    name: 'PlayStation',
    description: 'Sync PlayStation trophies and game stats',
    icon: <PSNIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'xbox',
    name: 'Xbox Live',
    description: 'Track Xbox achievements and Game Pass',
    icon: <XboxIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'epic',
    name: 'Epic Games',
    description: 'Connect Epic Store library',
    icon: <EpicIconWrapper />,
    connected: false,
    apiAvailable: true
  },
  {
    id: 'nintendo',
    name: 'Nintendo',
    description: 'Nintendo Switch profile integration',
    icon: <NintendoIconWrapper />,
    connected: false,
    apiAvailable: true
  }
];

interface GamingConnectionCardProps {
  connection: GamingConnection;
  onToggle: (id: string, connected: boolean) => void;
}

const GamingConnectionCard: React.FC<GamingConnectionCardProps> = ({ connection, onToggle }) => {
  const { toast } = useToast();

  const handleConnect = () => {
    if (!connection.apiAvailable) {
      toast({
        title: "API Not Available",
        description: `${connection.name} API integration is not currently available.`,
        variant: "destructive"
      });
      return;
    }

    // Mock connection logic
    toast({
      title: connection.connected ? "Disconnected" : "Connected",
      description: `${connection.name} has been ${connection.connected ? 'disconnected' : 'connected'} successfully.`,
    });
    
    onToggle(connection.id, !connection.connected);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {connection.icon}
          <div className="flex-1">
            <h4 className="font-mono text-sm font-semibold text-white">
              {connection.name}
            </h4>
            <p className="text-xs text-gray-400 font-['Lato'] mt-1">
              {connection.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {connection.connected ? (
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs font-mono uppercase tracking-wide">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="border-gray-600 text-gray-400 text-xs font-mono uppercase tracking-wide">
              <XCircle className="w-3 h-3 mr-1" />
              Disconnected
            </Badge>
          )}
          
          <Button
            onClick={handleConnect}
            variant={connection.connected ? "outline" : "default"}
            size="sm"
            disabled={!connection.apiAvailable}
            className={`font-mono text-xs uppercase tracking-wide font-semibold ${
              connection.connected 
                ? "border-red-500/50 text-red-400 hover:bg-red-500/10" 
                : "bg-purple-600 hover:bg-purple-700 text-white"
            }`}
          >
            {connection.connected ? "Disconnect" : "Connect"}
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

interface GamingConnectionsSectionProps {
  userId: string;
}

export default function GamingConnectionsSection({ userId }: GamingConnectionsSectionProps) {
  const [userConnections, setUserConnections] = useState(gamingConnections);
  const [isExpanded, setIsExpanded] = useState(true);

  const handleToggle = (id: string, connected: boolean) => {
    setUserConnections(prev => prev.map(conn => 
      conn.id === id ? { ...conn, connected } : conn
    ));
  };

  const totalConnected = userConnections.filter(c => c.connected).length;

  return (
    <div className="space-y-6">
      {/* Gaming Platform Connections Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Gaming Platform Connections
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1 font-mono">
                Connect your gaming platforms to import libraries and achievements
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-6">
                {/* Gaming Platforms List */}
                <div className="space-y-3">
                  {userConnections.map((connection) => (
                    <GamingConnectionCard
                      key={connection.id}
                      connection={connection}
                      onToggle={handleToggle}
                    />
                  ))}
                </div>

              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
}