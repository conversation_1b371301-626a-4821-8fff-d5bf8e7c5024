// Rate Limiting System for Admin Operations
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.3

// import { logSecurityEvent } from '@/lib/admin/security';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Rate limit configurations for different admin operations
export const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
  // Authentication attempts
  ADMIN_LOGIN: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5 // 5 login attempts per 15 minutes
  },
  
  // User management operations
  USER_DELETE: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10 // 10 user deletions per hour
  },
  
  USER_BULK_UPDATE: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    maxRequests: 3 // 3 bulk operations per 10 minutes
  },
  
  USER_ADMIN_PROMOTION: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5 // 5 admin promotions per hour
  },
  
  // Content moderation
  CONTENT_DELETE: {
    windowMs: 30 * 60 * 1000, // 30 minutes
    maxRequests: 50 // 50 content deletions per 30 minutes
  },
  
  BULK_MODERATION: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5 // 5 bulk moderation actions per 15 minutes
  },
  
  // System operations
  DATABASE_QUERY: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20 // 20 database queries per 5 minutes
  },
  
  SYSTEM_CONFIG_UPDATE: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10 // 10 config updates per hour
  },
  
  // General admin API calls
  ADMIN_API_GENERAL: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100 // 100 requests per minute
  }
};

export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

// Check if request is within rate limit
export function checkRateLimit(
  identifier: string, // Usually adminId + operation type
  operation: string,
  config?: RateLimitConfig
): RateLimitResult {
  const rateLimitConfig = config || RATE_LIMIT_CONFIGS[operation] || RATE_LIMIT_CONFIGS.ADMIN_API_GENERAL;
  const now = Date.now();
  const key = `${identifier}:${operation}`;
  
  // Clean up expired entries periodically
  cleanupExpiredEntries();
  
  let entry = rateLimitStore.get(key);
  
  // Initialize entry if it doesn't exist or window has expired
  if (!entry || now > entry.resetTime) {
    entry = {
      count: 0,
      resetTime: now + rateLimitConfig.windowMs,
      firstRequest: now
    };
  }
  
  // Check if limit exceeded
  const allowed = entry.count < rateLimitConfig.maxRequests;
  
  if (allowed) {
    entry.count++;
    rateLimitStore.set(key, entry);
  }
  
  const remaining = Math.max(0, rateLimitConfig.maxRequests - entry.count);
  const retryAfter = allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000);
  
  return {
    allowed,
    limit: rateLimitConfig.maxRequests,
    remaining,
    resetTime: entry.resetTime,
    retryAfter
  };
}

// Rate limit middleware for admin operations
export async function enforceRateLimit(
  adminId: string,
  operation: string,
  config?: {
    maxPerMinute?: number;
    maxPerHour?: number;
    maxPerDay?: number;
  }
): Promise<RateLimitResult> {
  const identifier = adminId;

  // Convert custom config to RateLimitConfig if provided
  let rateLimitConfig: RateLimitConfig | undefined;
  if (config) {
    // Use the most restrictive limit provided (prioritize minute, then hour, then day)
    if (config.maxPerMinute) {
      rateLimitConfig = {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: config.maxPerMinute
      };
    } else if (config.maxPerHour) {
      rateLimitConfig = {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: config.maxPerHour
      };
    } else if (config.maxPerDay) {
      rateLimitConfig = {
        windowMs: 24 * 60 * 60 * 1000, // 1 day
        maxRequests: config.maxPerDay
      };
    }
  }

  const result = checkRateLimit(identifier, operation, rateLimitConfig);

  if (!result.allowed) {
    // Log rate limit violation to console (database logging disabled)
    console.warn(`🔒 RATE LIMIT: Rate limit exceeded for admin ${adminId} on operation ${operation}`, {
      reason: 'Rate limit exceeded',
      operation,
      limit: result.limit,
      retry_after: result.retryAfter
    });
  }

  return result;
}

// Rate limit decorator for admin functions
export function withRateLimit(operation: string, config?: RateLimitConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      // Assume first argument is adminId
      const adminId = args[0];
      
      if (!adminId) {
        throw new Error('Admin ID required for rate limiting');
      }
      
      const rateLimitResult = await enforceRateLimit(adminId, operation, config);
      
      if (!rateLimitResult.allowed) {
        const error = new Error(`Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`);
        (error as any).rateLimitInfo = rateLimitResult;
        throw error;
      }
      
      return method.apply(this, args);
    };
    
    return descriptor;
  };
}

// Clean up expired entries to prevent memory leaks
function cleanupExpiredEntries(): void {
  const now = Date.now();
  const keysToDelete: string[] = [];
  
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      keysToDelete.push(key);
    }
  }
  
  keysToDelete.forEach(key => rateLimitStore.delete(key));
}

// Get current rate limit status for an admin user
export function getRateLimitStatus(adminId: string, operation: string): RateLimitResult | null {
  const key = `${adminId}:${operation}`;
  const entry = rateLimitStore.get(key);
  const config = RATE_LIMIT_CONFIGS[operation] || RATE_LIMIT_CONFIGS.ADMIN_API_GENERAL;
  
  if (!entry) {
    return {
      allowed: true,
      limit: config.maxRequests,
      remaining: config.maxRequests,
      resetTime: Date.now() + config.windowMs
    };
  }
  
  const now = Date.now();
  const remaining = Math.max(0, config.maxRequests - entry.count);
  
  return {
    allowed: entry.count < config.maxRequests && now <= entry.resetTime,
    limit: config.maxRequests,
    remaining,
    resetTime: entry.resetTime,
    retryAfter: now > entry.resetTime ? undefined : Math.ceil((entry.resetTime - now) / 1000)
  };
}

// Reset rate limit for a specific admin and operation (emergency use)
export function resetRateLimit(adminId: string, operation: string): void {
  const key = `${adminId}:${operation}`;
  rateLimitStore.delete(key);
  console.log(`Rate limit reset for admin ${adminId} on operation ${operation}`);
}

export default {
  checkRateLimit,
  enforceRateLimit,
  withRateLimit,
  getRateLimitStatus,
  resetRateLimit,
  RATE_LIMIT_CONFIGS
};
