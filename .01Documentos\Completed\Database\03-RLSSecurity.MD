# Phase 3: Row Level Security (RLS) Implementation
## Database-Level Security for CriticalPixel

### 🎯 **Phase Objective**
Implement comprehensive Row Level Security policies to ensure data privacy, user access control, and secure operations at the database level.

### 📊 **Phase Status**
**Current Progress: 0%**
**Estimated Duration: 2-3 days**
**Priority: CRITICAL**
**Dependencies: Phase 1 - Database Schema, Phase 2 - Review System Core**

### 🔒 **Security Architecture Overview**

```mermaid
graph TD
    A[User Request] --> B[Supabase Auth]
    B --> C{RLS Policies}
    C --> D[Database Access]
    
    C --> C1[User Policies]
    C --> C2[Content Policies]
    C --> C3[Admin Policies]
    C --> C4[Analytics Policies]
    
    C1 --> D1[Profile Access]
    C2 --> D2[Review/Comment Access]
    C3 --> D3[Admin Operations]
    C4 --> D4[Analytics Data]
    
    E[auth.uid()] --> C
    F[User Roles] --> C
    G[Content Ownership] --> C
```

### 🔐 **Security Principles**

1. **Principle of Least Privilege:** Users access only what they need
2. **Defense in Depth:** Multiple security layers
3. **Data Ownership:** Users control their own data
4. **Privacy by Design:** Default to private, explicit public access
5. **Audit Trail:** All access is logged and traceable

### 📝 **Implementation Tasks**

#### **Task 3.1: Enable RLS and Base Policies** ⏳
**Estimated Time:** 2 hours

##### **3.1.1: Enable RLS on All Tables**
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_surveys ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE hardware_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
```

##### **3.1.2: Create Base Security Functions**
```sql
-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND is_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns content
CREATE OR REPLACE FUNCTION is_owner(user_id uuid, content_user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN user_id = content_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if content is public
CREATE OR REPLACE FUNCTION is_public_content(content_status text)
RETURNS boolean AS $$
BEGIN
  RETURN content_status = 'published';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Task 3.2: Profile Security Policies** ⏳
**Estimated Time:** 3 hours

##### **3.2.1: Profile Access Policies**
```sql
-- Users can view their own profile completely
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

-- Users can view public profile information of others
CREATE POLICY "Public profile information" ON profiles
  FOR SELECT USING (
    auth.uid() IS NOT NULL AND (
      -- Basic public info available to authenticated users
      true
    )
  );

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Only authenticated users can insert profiles (registration)
CREATE POLICY "Authenticated users can create profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Only admins can delete profiles
CREATE POLICY "Admins can delete profiles" ON profiles
  FOR DELETE USING (is_admin(auth.uid()));
```

##### **3.2.2: Privacy-Aware Profile Policies**
```sql
-- Function to check privacy settings
CREATE OR REPLACE FUNCTION can_view_profile_field(
  viewer_id uuid, 
  profile_owner_id uuid, 
  field_name text
)
RETURNS boolean AS $$
DECLARE
  privacy_settings jsonb;
  is_owner boolean;
BEGIN
  -- Owner can always see their own data
  IF viewer_id = profile_owner_id THEN
    RETURN true;
  END IF;
  
  -- Get privacy settings
  SELECT p.privacy_settings INTO privacy_settings
  FROM profiles p WHERE p.id = profile_owner_id;
  
  -- Check specific field privacy
  CASE field_name
    WHEN 'online_status' THEN
      RETURN COALESCE((privacy_settings->>'showOnlineStatus')::boolean, true);
    WHEN 'gaming_profiles' THEN
      RETURN COALESCE((privacy_settings->>'showGamingProfiles')::boolean, true);
    WHEN 'achievements' THEN
      RETURN COALESCE((privacy_settings->>'showAchievements')::boolean, true);
    ELSE
      RETURN true;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Task 3.3: Review Content Security Policies** ⏳
**Estimated Time:** 4 hours

##### **3.3.1: Review Access Policies**
```sql
-- Anyone can view published reviews
CREATE POLICY "Anyone can view published reviews" ON reviews
  FOR SELECT USING (
    status = 'published' OR 
    is_owner(auth.uid(), author_id) OR 
    is_admin(auth.uid())
  );

-- Users can create their own reviews
CREATE POLICY "Users can create reviews" ON reviews
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND 
    auth.uid() = author_id
  );

-- Users can update their own reviews, admins can update any
CREATE POLICY "Users can update own reviews" ON reviews
  FOR UPDATE USING (
    is_owner(auth.uid(), author_id) OR 
    is_admin(auth.uid())
  )
  WITH CHECK (
    is_owner(auth.uid(), author_id) OR 
    is_admin(auth.uid())
  );

-- Users can delete their own reviews, admins can delete any
CREATE POLICY "Users can delete own reviews" ON reviews
  FOR DELETE USING (
    is_owner(auth.uid(), author_id) OR 
    is_admin(auth.uid())
  );
```

##### **3.3.2: Review Draft and Publishing Policies**
```sql
-- Function to handle review publishing permissions
CREATE OR REPLACE FUNCTION can_publish_review(user_id uuid, review_author_id uuid)
RETURNS boolean AS $$
BEGIN
  -- User can publish their own reviews
  -- Admins can publish any review
  RETURN is_owner(user_id, review_author_id) OR is_admin(user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policy for review status changes
CREATE POLICY "Review status management" ON reviews
  FOR UPDATE USING (
    CASE 
      WHEN OLD.status = 'draft' AND NEW.status = 'published' THEN
        can_publish_review(auth.uid(), author_id)
      WHEN OLD.status = 'published' AND NEW.status = 'archived' THEN
        is_owner(auth.uid(), author_id) OR is_admin(auth.uid())
      ELSE
        is_owner(auth.uid(), author_id) OR is_admin(auth.uid())
    END
  );
```

#### **Task 3.4: Comment System Security Policies** ⏳
**Estimated Time:** 3 hours

##### **3.4.1: Comment Access Policies**
```sql
-- Users can view comments on published reviews
CREATE POLICY "View comments on published content" ON comments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND (
        r.status = 'published' OR
        is_owner(auth.uid(), r.author_id) OR
        is_admin(auth.uid())
      )
    )
  );

-- Authenticated users can create comments
CREATE POLICY "Authenticated users can comment" ON comments
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND
    auth.uid() = author_id AND
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );

-- Users can update their own comments within time limit
CREATE POLICY "Users can edit own comments" ON comments
  FOR UPDATE USING (
    is_owner(auth.uid(), author_id) AND
    created_at > NOW() - INTERVAL '24 hours'
  )
  WITH CHECK (
    is_owner(auth.uid(), author_id)
  );

-- Users can soft-delete their own comments, admins can delete any
CREATE POLICY "Comment deletion policy" ON comments
  FOR UPDATE USING (
    (is_owner(auth.uid(), author_id) OR is_admin(auth.uid())) AND
    NEW.is_deleted = true
  );
```

##### **3.4.2: Comment Moderation Policies**
```sql
-- Function for comment moderation
CREATE OR REPLACE FUNCTION can_moderate_comment(moderator_id uuid, comment_author_id uuid)
RETURNS boolean AS $$
BEGIN
  -- Admins can moderate any comment
  -- Users can moderate comments on their own content
  RETURN is_admin(moderator_id) OR 
         EXISTS (
           SELECT 1 FROM comments c
           JOIN reviews r ON c.review_id = r.id
           WHERE c.author_id = comment_author_id 
           AND r.author_id = moderator_id
         );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Task 3.5: Performance Survey Security Policies** ⏳
**Estimated Time:** 2 hours

##### **3.5.1: Performance Survey Access Policies**
```sql
-- Users can view their own performance surveys
CREATE POLICY "Users view own performance surveys" ON performance_surveys
  FOR SELECT USING (is_owner(auth.uid(), user_id));

-- Aggregated performance data is public for published reviews
CREATE POLICY "Public aggregated performance data" ON performance_surveys
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );

-- Users can create their own performance surveys
CREATE POLICY "Users create performance surveys" ON performance_surveys
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    auth.uid() IS NOT NULL
  );

-- Users can update their own performance surveys
CREATE POLICY "Users update own performance surveys" ON performance_surveys
  FOR UPDATE USING (is_owner(auth.uid(), user_id))
  WITH CHECK (is_owner(auth.uid(), user_id));

-- Users can delete their own performance surveys
CREATE POLICY "Users delete own performance surveys" ON performance_surveys
  FOR DELETE USING (is_owner(auth.uid(), user_id));
```

#### **Task 3.6: Admin and Analytics Security Policies** ⏳
**Estimated Time:** 3 hours

##### **3.6.1: Admin-Only Policies**
```sql
-- Admins can view all user data
CREATE POLICY "Admins view all profiles" ON profiles
  FOR SELECT USING (is_admin(auth.uid()));

-- Admins can view all content regardless of status
CREATE POLICY "Admins view all reviews" ON reviews
  FOR SELECT USING (is_admin(auth.uid()));

-- Admins can view all analytics data
CREATE POLICY "Admins view analytics" ON review_analytics
  FOR SELECT USING (is_admin(auth.uid()));

-- Admins can manage user achievements
CREATE POLICY "Admins manage achievements" ON user_achievements
  FOR ALL USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));
```

##### **3.6.2: Analytics and Engagement Policies**
```sql
-- Users can view analytics for their own content
CREATE POLICY "Authors view own review analytics" ON review_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND is_owner(auth.uid(), r.author_id)
    )
  );

-- Users can like reviews (insert only)
CREATE POLICY "Users can like reviews" ON review_likes
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );

-- Users can remove their own likes
CREATE POLICY "Users can remove own likes" ON review_likes
  FOR DELETE USING (is_owner(auth.uid(), user_id));

-- Users can view like counts (aggregated)
CREATE POLICY "Public like visibility" ON review_likes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM reviews r 
      WHERE r.id = review_id AND r.status = 'published'
    )
  );
```

#### **Task 3.7: Hardware Configuration Security** ⏳
**Estimated Time:** 2 hours

##### **3.7.1: Hardware Config Policies**
```sql
-- Users can manage their own hardware configurations
CREATE POLICY "Users manage own hardware configs" ON hardware_configs
  FOR ALL USING (is_owner(auth.uid(), user_id))
  WITH CHECK (is_owner(auth.uid(), user_id));

-- Public hardware configs for performance comparison (anonymized)
CREATE POLICY "Public hardware comparison data" ON hardware_configs
  FOR SELECT USING (
    auth.uid() IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM performance_surveys ps
      JOIN reviews r ON ps.review_id = r.id
      WHERE ps.hardware_config_id = hardware_configs.id
      AND r.status = 'published'
    )
  );
```

#### **Task 3.8: Games Table Security** ⏳
**Estimated Time:** 1 hour

##### **3.8.1: Game Data Policies**
```sql
-- Games are publicly readable for all authenticated users
CREATE POLICY "Authenticated users can view games" ON games
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only admins can manage game data
CREATE POLICY "Admins manage game data" ON games
  FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins update game data" ON games
  FOR UPDATE USING (is_admin(auth.uid()))
  WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins delete game data" ON games
  FOR DELETE USING (is_admin(auth.uid()));
```

### ✅ **Task Completion Checklist**

#### **Base RLS Setup (Task 3.1)**
- [ ] **3.1.1:** RLS enabled on all tables
- [ ] **3.1.2:** Base security functions created (`is_admin`, `is_owner`, `is_public_content`)
- [ ] **Verification:** All tables show RLS enabled in Supabase dashboard
- [ ] **AI Comment:** _[Implementation details and any configuration challenges]_

#### **Profile Security (Task 3.2)**
- [ ] **3.2.1:** Profile access policies implemented
  - [ ] Users can view own profile completely
  - [ ] Limited public profile information for others
  - [ ] Profile update restrictions to owner only
  - [ ] Admin-only profile deletion
- [ ] **3.2.2:** Privacy-aware profile policies
  - [ ] `can_view_profile_field()` function created
  - [ ] Privacy settings enforcement
  - [ ] Online status privacy controls
- [ ] **Verification:** Profile data properly filtered based on user permissions
- [ ] **AI Comment:** _[Privacy implementation details and user experience impact]_

#### **Review Content Security (Task 3.3)**
- [ ] **3.3.1:** Review access policies implemented
  - [ ] Public access to published reviews
  - [ ] Author and admin access to all statuses
  - [ ] Creation, update, and deletion permissions
- [ ] **3.3.2:** Review publishing policies
  - [ ] `can_publish_review()` function implemented
  - [ ] Status change validation
  - [ ] Draft-to-published workflow security
- [ ] **Verification:** Review access properly controlled by status and ownership
- [ ] **AI Comment:** _[Content security implementation and edge cases handled]_

#### **Comment System Security (Task 3.4)**
- [ ] **3.4.1:** Comment access policies implemented
  - [ ] View permissions based on review status
  - [ ] Comment creation for authenticated users
  - [ ] Time-limited comment editing
  - [ ] Soft deletion policies
- [ ] **3.4.2:** Comment moderation policies
  - [ ] `can_moderate_comment()` function created
  - [ ] Admin and content owner moderation rights
- [ ] **Verification:** Comment permissions work correctly in threading scenarios
- [ ] **AI Comment:** _[Comment security and moderation capabilities]_

#### **Performance Survey Security (Task 3.5)**
- [ ] **3.5.1:** Performance survey policies implemented
  - [ ] Personal survey data privacy
  - [ ] Aggregated public data access
  - [ ] CRUD permissions for survey owners
- [ ] **Verification:** Performance data correctly segregated between personal and public
- [ ] **AI Comment:** _[Performance survey privacy and data sharing implementation]_

#### **Admin and Analytics Security (Task 3.6)**
- [ ] **3.6.1:** Admin-only policies implemented
  - [ ] Global admin access to all data
  - [ ] Admin content management capabilities
  - [ ] Analytics access for administrators
- [ ] **3.6.2:** Analytics and engagement policies
  - [ ] Author access to own review analytics
  - [ ] Like system permissions and privacy
  - [ ] Engagement data security
- [ ] **Verification:** Admin privileges work correctly without security bypasses
- [ ] **AI Comment:** _[Admin functionality and analytics security implementation]_

#### **Hardware Configuration Security (Task 3.7)**
- [ ] **3.7.1:** Hardware config policies implemented
  - [ ] Personal hardware configuration privacy
  - [ ] Anonymous public comparison data
  - [ ] Performance correlation security
- [ ] **Verification:** Hardware data properly anonymized for public access
- [ ] **AI Comment:** _[Hardware configuration privacy and comparison features]_

#### **Games Table Security (Task 3.8)**
- [ ] **3.8.1:** Game data policies implemented
  - [ ] Public read access for authenticated users
  - [ ] Admin-only write operations
  - [ ] Game data integrity protection
- [ ] **Verification:** Game data accessible but protected from unauthorized changes
- [ ] **AI Comment:** _[Game data security and IGDB integration protection]_

### 🔍 **Security Testing Requirements**

#### **Access Control Tests**
- [ ] User cannot access other users' private data
- [ ] Draft reviews not visible to non-owners
- [ ] Admin access works across all data
- [ ] Unauthenticated users properly restricted

#### **Privacy Tests**
- [ ] Privacy settings properly enforced
- [ ] Anonymous data aggregation working
- [ ] Personal information properly protected
- [ ] GDPR compliance verified

#### **Edge Case Tests**
- [ ] Deleted user data handling
- [ ] Ownership transfer scenarios
- [ ] Admin privilege escalation prevention
- [ ] SQL injection attack prevention

### 🎯 **Success Criteria**

1. **Data Privacy:** Users can only access data they're authorized to see
2. **Content Security:** Draft content remains private to authors
3. **Admin Functionality:** Admins have appropriate elevated access
4. **Performance:** RLS policies don't significantly impact query performance
5. **Privacy Compliance:** User privacy settings are properly enforced
6. **Audit Trail:** All data access is properly logged by Supabase

### 🚨 **Critical Security Notes**

1. **Test Thoroughly:** RLS policies can lock out legitimate access if misconfigured
2. **Performance Impact:** Complex policies may slow down queries - monitor performance
3. **Admin Access:** Ensure admin functionality still works after RLS implementation
4. **Backup Strategy:** Test data recovery scenarios with RLS enabled
5. **Policy Updates:** Changes to policies require careful migration planning

### 📊 **Performance Impact Assessment**

After implementation, verify:
- Profile queries: < 100ms (baseline comparison)
- Review listing: < 200ms (with RLS overhead)
- Comment threads: < 150ms (with nested policies)
- Admin queries: < 300ms (with elevated access)
- Search operations: < 500ms (with content filtering)

### 🔄 **Integration Points**

This security implementation enables:
- **Phase 4:** Secure user profile services
- **Phase 5:** Protected admin system operations  
- **Phase 6:** Comprehensive security testing
- **Production:** Database-level security for all operations

---

**Phase Completion Status:** ⏳ **PENDING**
**Previous Phase:** `02-ReviewSystemCore.MD` (Must be completed first)
**Next Phase:** `04-UserProfileServices.MD`
**Last Updated:** December 7, 2025