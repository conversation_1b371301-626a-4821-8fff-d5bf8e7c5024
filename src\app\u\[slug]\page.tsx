// User Profile Page with Supabase Integration
// Displays public user profiles with SEO optimization

import React from 'react';
import { Loader2, Edit3, Users, Calendar, Clock, Gamepad2, Trophy, Star, ChevronDown, Palette } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import GamerCard from '@/components/userprofile/GamerCard';
import EditProfileModal from '@/components/userprofile/EditProfileModal';
import ProfileNotFound from '@/components/userprofile/ProfileNotFound';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { UserProfile } from '@/lib/types/profile';
import type { ExtendedUserProfile, CustomColors } from '@/lib/types';
import BannerSvg from '@/components/ui/icons/BannerSvg';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { getUserProfileBySlugOrUsername, updateUserProfile, prefetchProfileData } from '@/app/u/actions';
import { ThemeManager } from '@/lib/ThemeManager';
import { convertToExtendedProfile, convertFromExtendedProfile } from '@/utils/profile-conversion';
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { getReviewDisplaySettings, getUserYouTubeSettings } from '@/app/u/dashboard/actions';
import ProfilePageClient from './ProfilePageClient';

// Simple CodeTitle component
const CodeTitle = ({ children }: { children: React.ReactNode }) => (
  <span className="font-mono relative inline-block">
    <span className="text-violet-400/60">&lt;</span>
    <span className="mx-1">{children}</span>
    <span className="text-violet-400/60">/&gt;</span>
  </span>
);

// Compact theme definitions - 6 muted themes
const themes = [
  {
    id: 'default',
    name: 'Cosmic',
    colors: {
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      accent: '#a78bfa'
    }
  },
  {
    id: 'ocean',
    name: 'Ocean',
    colors: {
      primary: '#3b82f6',
      secondary: '#2563eb',
      accent: '#60a5fa'
    }
  },
  {
    id: 'forest',
    name: 'Forest',
    colors: {
      primary: '#10b981',
      secondary: '#059669',
      accent: '#34d399'
    }
  },
  {
    id: 'crimson',
    name: 'Crimson',
    colors: {
      primary: '#ef4444',
      secondary: '#dc2626',
      accent: '#f87171'
    }
  },
  {
    id: 'silver',
    name: 'Silver',
    colors: {
      primary: '#6b7280',
      secondary: '#4b5563',
      accent: '#9ca3af'
    }
  },
  {
    id: 'amber',
    name: 'Amber',
    colors: {
      primary: '#f59e0b',
      secondary: '#d97706',
      accent: '#fbbf24'
    }
  }
];

// Client-side Theme Selector Component
const CompactThemeSelector = ({ currentTheme, onChange }: { currentTheme: string; onChange: (themeId: string) => void }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const selectedTheme = themes.find(t => t.id === currentTheme) || themes[0];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1.5 bg-gray-900/80 border border-gray-700/50 rounded-lg hover:bg-gray-900/90 transition-all duration-200 backdrop-blur-sm"
      >
        <div 
          className="w-4 h-4 rounded-full"
          style={{ backgroundColor: selectedTheme.colors.primary }}
        />
        <span className="text-xs font-mono text-gray-300">{selectedTheme.name}</span>
        <ChevronDown className={cn("h-3 w-3 text-gray-400 transition-transform", isOpen && "rotate-180")} />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute right-0 mt-2 p-2 bg-gray-900/95 border border-gray-700/50 rounded-lg backdrop-blur-xl shadow-xl z-50"
          >
            <div className="grid grid-cols-3 gap-1">
              {themes.map((theme) => (
                <button
                  key={theme.id}
                  onClick={() => {
                    onChange(theme.id);
                    setIsOpen(false);
                  }}
                  className={cn(
                    "p-2 rounded-md transition-all duration-200 group relative",
                    currentTheme === theme.id ? "bg-gray-800/60" : "hover:bg-gray-800/40"
                  )}
                >
                  <div className="flex flex-col items-center gap-1">
                    <div className="flex gap-0.5">
                      <div 
                        className="w-2 h-2 rounded-full" 
                        style={{ backgroundColor: theme.colors.primary }}
                      />
                      <div 
                        className="w-2 h-2 rounded-full" 
                        style={{ backgroundColor: theme.colors.secondary }}
                      />
                      <div 
                        className="w-2 h-2 rounded-full" 
                        style={{ backgroundColor: theme.colors.accent }}
                      />
                    </div>
                    <span className="text-[10px] font-mono text-gray-400 group-hover:text-gray-300">
                      {theme.name}
                    </span>
                  </div>
                  {currentTheme === theme.id && (
                    <motion.div
                      layoutId="theme-indicator"
                      className="absolute inset-0 rounded-md pointer-events-none"
                      style={{ 
                        borderWidth: '1px',
                        borderStyle: 'solid',
                        borderColor: theme.colors.primary 
                      }}
                    />
                  )}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Static Profile Header Component (Server Component compatible)
const ProfileHeader = ({ 
  profileData, 
  formatDate 
}: { 
  profileData: UserProfile;
  formatDate: (date: Date | null | undefined) => string;
}) => {
  const theme = ThemeManager.getTheme(profileData.theme || 'muted-dark');
  
  return (
    <div 
      className="relative overflow-hidden rounded-xl border border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur"
      style={{
        background: `linear-gradient(135deg, ${theme.colors.primary}20, ${theme.colors.secondary}10)`,
        borderColor: `${theme.colors.accent}30`
      }}
    >
      {/* Banner */}
      <div 
        className="h-32 bg-gradient-to-r"
        style={{
          background: profileData.banner_url 
            ? `url(${profileData.banner_url}) center/cover` 
            : `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`
        }}
      />

      <div className="relative p-6 -mt-16">
        <div className="flex items-start gap-4">
          {/* Avatar */}
          <div 
            className="relative h-24 w-24 rounded-full border-4 border-gray-900 bg-gray-800 overflow-hidden"
            style={{ borderColor: theme.colors.accent }}
          >
            {profileData.avatar_url ? (
              <img 
                src={profileData.avatar_url} 
                alt={`${profileData.display_name || profileData.username}'s avatar`}
                className="h-full w-full object-cover"
              />
            ) : (
              <div 
                className="flex h-full w-full items-center justify-center text-3xl font-bold text-white"
                style={{ backgroundColor: theme.colors.primary }}
              >
                {(profileData.display_name || profileData.username).charAt(0).toUpperCase()}
              </div>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h1 className="text-2xl font-bold text-white truncate">
                  {profileData.display_name || profileData.username}
                </h1>
                <p className="text-gray-400">@{profileData.username}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Row */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400 mt-4">
          <div className="flex items-center gap-1.5">
            <Calendar className="h-3.5 w-3.5" style={{ color: theme.colors.accent }} />
            <span>Joined {formatDate(profileData.created_at ? new Date(profileData.created_at) : null)}</span>
          </div>
          
          {profileData.favorite_consoles && profileData.favorite_consoles.length > 0 && (
            <div className="flex items-center gap-1.5">
              <Gamepad2 className="h-3.5 w-3.5" style={{ color: theme.colors.accent }} />
              <span>{profileData.favorite_consoles.length} platforms</span>
            </div>
          )}
          
          {profileData.preferred_genres && profileData.preferred_genres.length > 0 && (
            <div className="flex items-center gap-1.5">
              <Trophy className="h-3.5 w-3.5" style={{ color: theme.colors.accent }} />
              <span>{profileData.preferred_genres.length} genres</span>
            </div>
          )}
        </div>

        {profileData.bio && (
          <p className="text-sm text-gray-300 mt-3 line-clamp-2">{profileData.bio}</p>
        )}
      </div>
    </div>
  );
};

// Simple error display component (server component compatible)
const ProfileErrorDisplay = ({ message }: { message: string }) => {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-white mb-4">
          Profile not found
        </h1>
        <p className="text-gray-400 mb-6">
          {message || "The profile you're looking for doesn't exist or couldn't be loaded."}
        </p>
        <div className="space-y-4">
          <a
            href="/"
            className="inline-block px-6 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
          >
            Go Home
          </a>
          <br />
          <a
            href={`/u/${typeof window !== 'undefined' ? window.location.pathname.split('/').pop() : ''}`}
            className="inline-block px-6 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
          >
            Try Again
          </a>
        </div>
      </div>
    </div>
  );
};

interface UserProfilePageProps {
  params: Promise<{ slug: string }>;
}

export default async function UserProfilePage({ params }: UserProfilePageProps) {
  // Resolve params properly for Next.js App Router
  const { slug } = await params;

  if (!slug) {
    redirect('/404');
  }

  // Get profile data using server action
  const profileData = await getUserProfileBySlugOrUsername(slug);

  if (!profileData) {
    return <ProfileErrorDisplay message={`Profile not found for: ${slug}`} />;
  }

  // Get current user for ownership check (with anonymous user support)
  let currentUserId: string | null = null;
  try {
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);
    const { data: currentUser, error } = await supabase.auth.getUser();

    // Only set currentUserId if we have a valid authenticated user
    if (!error && currentUser?.user?.id) {
      currentUserId = currentUser.user.id;
    }
  } catch (error) {
    // Silently handle auth errors for anonymous users
    console.log('Anonymous user accessing profile - no authentication required');
    currentUserId = null;
  }

  // Fetch display settings and YouTube settings on server to prevent flash
  const [reviewDisplayResponse, youtubeResponse] = await Promise.all([
    getReviewDisplaySettings(profileData.id),
    getUserYouTubeSettings(profileData.id)
  ]);

  // Prepare initial settings with safe defaults
  const initialDisplaySettings = reviewDisplayResponse.success && reviewDisplayResponse.data ? {
    viewMode: reviewDisplayResponse.data.viewMode,
    itemsPerPage: reviewDisplayResponse.data.itemsPerPage,
    showFilters: reviewDisplayResponse.data.showFilters,
    defaultSort: reviewDisplayResponse.data.defaultSort
  } : {
    viewMode: 'grid' as const,
    itemsPerPage: 6,
    showFilters: false,
    defaultSort: 'date' as const
  };

  const initialYouTubeChannelUrl = youtubeResponse.success && youtubeResponse.data?.channelUrl
    ? youtubeResponse.data.channelUrl
    : undefined;

  // Pass data to client component for interactive features
  return (
    <ProfilePageClient
      profileData={profileData}
      currentUserId={currentUserId}
      initialDisplaySettings={initialDisplaySettings}
      initialYouTubeChannelUrl={initialYouTubeChannelUrl}
    />
  );
}