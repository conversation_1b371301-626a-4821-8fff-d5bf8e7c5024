/**
 * Social Media Meta Tag Generator for CriticalPixel
 * Generates platform-specific meta tags for optimal social media sharing
 */

import { type ReviewData, type GeneratedMetadata } from '@/lib/metadata/generator';

export interface SocialMetaTags {
  openGraph: {
    title: string;
    description: string;
    image?: string;
    imageAlt?: string;
    type: string;
    siteName: string;
    url?: string;
    locale?: string;
    article?: {
      author?: string;
      publishedTime?: string;
      modifiedTime?: string;
      section?: string;
      tag?: string[];
    };
  };
  twitter: {
    card: string;
    title: string;
    description: string;
    image?: string;
    imageAlt?: string;
    site?: string;
    creator?: string;
  };
  discord: {
    title: string;
    description: string;
    image?: string;
    themeColor: string;
    author?: {
      name: string;
      iconUrl?: string;
    };
  };
  linkedin: {
    title: string;
    description: string;
    image?: string;
  };
  facebook: {
    title: string;
    description: string;
    image?: string;
    type: string;
  };
}

export interface SocialPreview {
  platform: 'twitter' | 'discord' | 'facebook' | 'linkedin';
  title: string;
  description: string;
  image?: string;
  url?: string;
  author?: string;
  timestamp?: string;
}

export class SocialMetaGenerator {
  private readonly baseUrl: string;
  private readonly siteName = 'CriticalPixel';
  private readonly twitterHandle = '@CriticalPixel';
  private readonly themeColor = '#8B5CF6'; // Violet theme color
  private readonly defaultImage = '/images/og-default.jpg';

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
  }

  /**
   * Generate comprehensive social media meta tags
   */
  generateSocialMetaTags(
    reviewData: ReviewData, 
    metadata: GeneratedMetadata,
    authorName?: string
  ): SocialMetaTags {
    const reviewUrl = `${this.baseUrl}/reviews/view/${reviewData.slug}`;
    const imageUrl = this.getOptimalImage(reviewData);
    const imageAlt = `${reviewData.gameName} review screenshot`;
    
    return {
      openGraph: this.generateOpenGraphTags(reviewData, metadata, reviewUrl, imageUrl, imageAlt, authorName),
      twitter: this.generateTwitterTags(reviewData, metadata, imageUrl, imageAlt, authorName),
      discord: this.generateDiscordTags(reviewData, metadata, imageUrl, authorName),
      linkedin: this.generateLinkedInTags(reviewData, metadata, imageUrl),
      facebook: this.generateFacebookTags(reviewData, metadata, imageUrl)
    };
  }

  /**
   * Generate social media previews for different platforms
   */
  generateSocialPreviews(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    authorName?: string
  ): SocialPreview[] {
    const reviewUrl = `${this.baseUrl}/reviews/view/${reviewData.slug}`;
    const imageUrl = this.getOptimalImage(reviewData);
    
    return [
      {
        platform: 'twitter',
        title: this.truncateText(metadata.socialTitle, 70),
        description: this.truncateText(metadata.socialDescription, 200),
        image: imageUrl,
        url: reviewUrl,
        author: authorName || this.siteName,
        timestamp: this.formatTimestamp(reviewData.datePlayed)
      },
      {
        platform: 'discord',
        title: metadata.socialTitle,
        description: this.truncateText(metadata.socialDescription, 300),
        image: imageUrl,
        url: reviewUrl,
        author: authorName || this.siteName
      },
      {
        platform: 'facebook',
        title: metadata.socialTitle,
        description: this.truncateText(metadata.socialDescription, 300),
        image: imageUrl,
        url: reviewUrl,
        author: authorName || this.siteName
      },
      {
        platform: 'linkedin',
        title: metadata.socialTitle,
        description: this.truncateText(metadata.socialDescription, 200),
        image: imageUrl,
        url: reviewUrl,
        author: authorName || this.siteName
      }
    ];
  }

  /**
   * Generate Open Graph meta tags
   */
  private generateOpenGraphTags(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    reviewUrl: string,
    imageUrl: string,
    imageAlt: string,
    authorName?: string
  ) {
    const tags: SocialMetaTags['openGraph'] = {
      title: metadata.socialTitle,
      description: metadata.socialDescription,
      image: imageUrl,
      imageAlt,
      type: 'article',
      siteName: this.siteName,
      url: reviewUrl,
      locale: 'en_US'
    };

    // Add article-specific metadata
    if (reviewData.datePlayed || authorName) {
      tags.article = {};
      
      if (authorName) {
        tags.article.author = authorName;
      }
      
      if (reviewData.datePlayed) {
        tags.article.publishedTime = new Date(reviewData.datePlayed).toISOString();
        tags.article.modifiedTime = new Date().toISOString();
      }
      
      tags.article.section = 'Game Reviews';
      
      // Add tags from genres and platforms
      const articleTags = [];
      if (reviewData.selectedGenres) {
        const genres = Array.isArray(reviewData.selectedGenres) 
          ? reviewData.selectedGenres 
          : Array.from(reviewData.selectedGenres);
        articleTags.push(...genres);
      }
      if (reviewData.selectedPlatforms) {
        const platforms = Array.isArray(reviewData.selectedPlatforms)
          ? reviewData.selectedPlatforms
          : Array.from(reviewData.selectedPlatforms);
        articleTags.push(...platforms);
      }
      articleTags.push(reviewData.gameName, 'game review', 'gaming');
      
      tags.article.tag = articleTags.slice(0, 10); // Limit to 10 tags
    }

    return tags;
  }

  /**
   * Generate Twitter Card meta tags
   */
  private generateTwitterTags(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    imageUrl: string,
    imageAlt: string,
    authorName?: string
  ): SocialMetaTags['twitter'] {
    return {
      card: 'summary_large_image',
      title: this.truncateText(metadata.socialTitle, 70),
      description: this.truncateText(metadata.socialDescription, 200),
      image: imageUrl,
      imageAlt,
      site: this.twitterHandle,
      creator: authorName ? `@${authorName.replace(/\s+/g, '')}` : this.twitterHandle
    };
  }

  /**
   * Generate Discord embed meta tags
   */
  private generateDiscordTags(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    imageUrl: string,
    authorName?: string
  ): SocialMetaTags['discord'] {
    const tags: SocialMetaTags['discord'] = {
      title: metadata.socialTitle,
      description: this.truncateText(metadata.socialDescription, 300),
      image: imageUrl,
      themeColor: this.themeColor
    };

    if (authorName) {
      tags.author = {
        name: authorName,
        iconUrl: `${this.baseUrl}/images/author-avatar.jpg`
      };
    }

    return tags;
  }

  /**
   * Generate LinkedIn meta tags
   */
  private generateLinkedInTags(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    imageUrl: string
  ): SocialMetaTags['linkedin'] {
    return {
      title: metadata.socialTitle,
      description: this.truncateText(metadata.socialDescription, 200),
      image: imageUrl
    };
  }

  /**
   * Generate Facebook meta tags
   */
  private generateFacebookTags(
    reviewData: ReviewData,
    metadata: GeneratedMetadata,
    imageUrl: string
  ): SocialMetaTags['facebook'] {
    return {
      title: metadata.socialTitle,
      description: this.truncateText(metadata.socialDescription, 300),
      image: imageUrl,
      type: 'article'
    };
  }

  /**
   * Get the optimal image for social sharing
   */
  private getOptimalImage(reviewData: ReviewData): string {
    // Priority order for image selection
    if (reviewData.mainImageUrl) {
      return reviewData.mainImageUrl;
    }

    // Use IGDB cover image if available
    if (reviewData.igdbCoverUrl) {
      return reviewData.igdbCoverUrl;
    }

    if (reviewData.galleryImageUrls && reviewData.galleryImageUrls.length > 0) {
      return reviewData.galleryImageUrls[0];
    }

    // Generate dynamic OG image URL
    if (reviewData.slug) {
      return `${this.baseUrl}/api/og-image/${reviewData.slug}`;
    }

    // Fallback to default image
    return `${this.baseUrl}${this.defaultImage}`;
  }

  /**
   * Truncate text to specified length with ellipsis
   */
  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    
    return text.substring(0, maxLength - 3).trim() + '...';
  }

  /**
   * Format timestamp for social media display
   */
  private formatTimestamp(dateString?: string): string {
    if (!dateString) {
      return new Date().toLocaleDateString();
    }
    
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return new Date().toLocaleDateString();
    }
  }

  /**
   * Generate HTML meta tags string
   */
  generateMetaTagsHTML(socialTags: SocialMetaTags): string {
    const tags: string[] = [];

    // Open Graph tags
    const og = socialTags.openGraph;
    tags.push(`<meta property="og:title" content="${this.escapeHtml(og.title)}" />`);
    tags.push(`<meta property="og:description" content="${this.escapeHtml(og.description)}" />`);
    tags.push(`<meta property="og:type" content="${og.type}" />`);
    tags.push(`<meta property="og:site_name" content="${og.siteName}" />`);
    
    if (og.image) {
      tags.push(`<meta property="og:image" content="${og.image}" />`);
      if (og.imageAlt) {
        tags.push(`<meta property="og:image:alt" content="${this.escapeHtml(og.imageAlt)}" />`);
      }
    }
    
    if (og.url) {
      tags.push(`<meta property="og:url" content="${og.url}" />`);
    }
    
    if (og.locale) {
      tags.push(`<meta property="og:locale" content="${og.locale}" />`);
    }

    // Article-specific Open Graph tags
    if (og.article) {
      if (og.article.author) {
        tags.push(`<meta property="article:author" content="${this.escapeHtml(og.article.author)}" />`);
      }
      if (og.article.publishedTime) {
        tags.push(`<meta property="article:published_time" content="${og.article.publishedTime}" />`);
      }
      if (og.article.modifiedTime) {
        tags.push(`<meta property="article:modified_time" content="${og.article.modifiedTime}" />`);
      }
      if (og.article.section) {
        tags.push(`<meta property="article:section" content="${this.escapeHtml(og.article.section)}" />`);
      }
      if (og.article.tag) {
        og.article.tag.forEach(tag => {
          tags.push(`<meta property="article:tag" content="${this.escapeHtml(tag)}" />`);
        });
      }
    }

    // Twitter Card tags
    const twitter = socialTags.twitter;
    tags.push(`<meta name="twitter:card" content="${twitter.card}" />`);
    tags.push(`<meta name="twitter:title" content="${this.escapeHtml(twitter.title)}" />`);
    tags.push(`<meta name="twitter:description" content="${this.escapeHtml(twitter.description)}" />`);
    
    if (twitter.image) {
      tags.push(`<meta name="twitter:image" content="${twitter.image}" />`);
      if (twitter.imageAlt) {
        tags.push(`<meta name="twitter:image:alt" content="${this.escapeHtml(twitter.imageAlt)}" />`);
      }
    }
    
    if (twitter.site) {
      tags.push(`<meta name="twitter:site" content="${twitter.site}" />`);
    }
    
    if (twitter.creator) {
      tags.push(`<meta name="twitter:creator" content="${twitter.creator}" />`);
    }

    return tags.join('\n');
  }

  /**
   * Escape HTML characters for safe meta tag content
   */
  private escapeHtml(text: string): string {
    if (typeof document !== 'undefined') {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }
    
    // Fallback for server-side
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}

// Export singleton instance
export const socialMetaGenerator = new SocialMetaGenerator();
