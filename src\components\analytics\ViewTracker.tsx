'use client';

import { useRef, useEffect } from 'react';
import { useViewTracking, useIntersectionViewTracking } from '@/hooks/useViewTracking';

interface ViewTrackerProps {
  reviewId: string;
  method?: 'time' | 'intersection' | 'both';
  delay?: number;
  threshold?: number;
  className?: string;
  children?: React.ReactNode;
}

/**
 * ViewTracker component - Add this to review pages to track unique daily views
 * 
 * Usage:
 * <ViewTracker reviewId={review.id} method="intersection">
 *   <div>Your review content here</div>
 * </ViewTracker>
 */
export default function ViewTracker({
  reviewId,
  method = 'intersection',
  delay = 2000,
  threshold = 3000,
  className,
  children
}: ViewTrackerProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  // Time-based tracking
  useViewTracking(
    method === 'time' || method === 'both' ? reviewId : null,
    {
      enabled: true,
      delay,
      threshold
    }
  );

  // Intersection-based tracking
  useIntersectionViewTracking(
    method === 'intersection' || method === 'both' ? reviewId : null,
    contentRef,
    {
      enabled: true,
      delay,
      threshold: 0.5, // 50% visible
      rootMargin: '0px'
    }
  );

  if (method === 'intersection' || method === 'both') {
    return (
      <div ref={contentRef} className={className}>
        {children}
      </div>
    );
  }

  // For time-based tracking, just render children without ref
  return (
    <div className={className}>
      {children}
    </div>
  );
}

/**
 * Simple view tracker that doesn't wrap content
 * Use this if you just want to track views without wrapping elements
 */
export function SimpleViewTracker({
  reviewId,
  reviewSlug,
  delay = 2000,
  threshold = 3000
}: {
  reviewId: string;
  reviewSlug?: string;
  delay?: number;
  threshold?: number;
}) {
  // Debug logging to confirm the component is mounted
  console.log('🎬 SimpleViewTracker mounted:', { reviewId, reviewSlug, delay, threshold });
  
  useViewTracking(reviewId, {
    enabled: true,
    delay,
    threshold,
    reviewSlug
  });

  return null; // Renders nothing
}

/**
 * ViewTracker with visual feedback (for debugging)
 */
export function DebugViewTracker({
  reviewId,
  method = 'intersection',
  delay = 2000,
  threshold = 3000,
  className,
  children
}: ViewTrackerProps & { showDebug?: boolean }) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [debugInfo, setDebugInfo] = React.useState<{
    tracked: boolean;
    method: string;
    timestamp?: string;
  }>({
    tracked: false,
    method: method || 'none'
  });

  useEffect(() => {
    // Listen for tracking events (you could emit custom events from the tracking hooks)
    const handleTracking = () => {
      setDebugInfo({
        tracked: true,
        method: method || 'none',
        timestamp: new Date().toLocaleTimeString()
      });
    };

    // This is a simple implementation - you could make the hooks emit events
    const timer = setTimeout(handleTracking, delay + 500);
    
    return () => clearTimeout(timer);
  }, [delay, method]);

  // Time-based tracking
  useViewTracking(
    method === 'time' || method === 'both' ? reviewId : null,
    {
      enabled: true,
      delay,
      threshold
    }
  );

  // Intersection-based tracking
  useIntersectionViewTracking(
    method === 'intersection' || method === 'both' ? reviewId : null,
    contentRef,
    {
      enabled: true,
      delay,
      threshold: 0.5,
      rootMargin: '0px'
    }
  );

  const debugElement = (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-50">
      <div>Review: {reviewId.slice(0, 8)}...</div>
      <div>Method: {debugInfo.method}</div>
      <div>Tracked: {debugInfo.tracked ? '✅' : '⏳'}</div>
      {debugInfo.timestamp && <div>Time: {debugInfo.timestamp}</div>}
    </div>
  );

  if (method === 'intersection' || method === 'both') {
    return (
      <>
        <div ref={contentRef} className={className}>
          {children}
        </div>
        {process.env.NODE_ENV === 'development' && debugElement}
      </>
    );
  }

  return (
    <>
      <div className={className}>
        {children}
      </div>
      {process.env.NODE_ENV === 'development' && debugElement}
    </>
  );
}

// Import React for useState in DebugViewTracker
import React from 'react';