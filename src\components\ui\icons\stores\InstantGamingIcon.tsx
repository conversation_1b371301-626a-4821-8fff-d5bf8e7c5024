import React from 'react';

interface IconProps {
  className?: string;
  [key: string]: any;
}

const InstantGamingIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 32 32"
    fill="currentColor"
    {...props}
  >
    <title>Instant Gaming</title>
    {/* Simplified Instant Gaming logo representation */}
    <rect width="32" height="32" fill="#FF6B35" rx="6"/>
    <text x="16" y="20" textAnchor="middle" fill="white" fontSize="12" fontFamily="Arial, sans-serif" fontWeight="bold">
      IG
    </text>
  </svg>
);

export default InstantGamingIcon;