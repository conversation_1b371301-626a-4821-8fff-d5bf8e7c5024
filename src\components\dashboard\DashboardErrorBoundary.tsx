'use client';

import React, { Component, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class DashboardErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the optional onError callback
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service in production
    if (process.env.NODE_ENV === 'production') {
      // You can integrate with error tracking services like Sentry here
      console.error('Production error in dashboard:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="min-h-[400px] flex items-center justify-center p-6"
        >
          <Card className="bg-slate-900/60 border-slate-700/50 max-w-md w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-red-500/20 rounded-full w-fit">
                <AlertTriangle className="text-red-400" size={32} />
              </div>
              <CardTitle className="text-slate-200">
                Something went wrong
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-400 text-center">
                We encountered an error while loading your dashboard. This has been logged and we'll look into it.
              </p>

              {/* Error details in development */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="bg-slate-800/50 rounded-lg p-3 text-xs">
                  <summary className="text-slate-300 cursor-pointer mb-2">
                    Error Details (Development)
                  </summary>
                  <div className="text-red-300 font-mono whitespace-pre-wrap">
                    {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div className="text-slate-400 font-mono whitespace-pre-wrap mt-2 text-xs">
                      {this.state.error.stack}
                    </div>
                  )}
                </details>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={this.handleRetry}
                  className="flex items-center gap-2 flex-1"
                  variant="default"
                >
                  <RefreshCw size={16} />
                  Try Again
                </Button>
                <Link href="/u/dashboard" className="flex-1">
                  <Button
                    variant="outline"
                    className="w-full flex items-center gap-2"
                  >
                    <Home size={16} />
                    Go Home
                  </Button>
                </Link>
              </div>

              <p className="text-xs text-slate-500 text-center">
                If this problem persists, please contact support.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export function useDashboardErrorHandler() {
  const handleError = React.useCallback((error: Error, errorInfo?: React.ErrorInfo) => {
    console.error('Dashboard error:', error);
    
    if (process.env.NODE_ENV === 'production') {
      // Log to external service
      console.error('Production dashboard error:', {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo?.componentStack
      });
    }
  }, []);

  return { handleError };
}

// Simple error fallback component
export function DashboardErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error; 
  resetError: () => void; 
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6 text-center"
    >
      <AlertTriangle className="mx-auto mb-4 text-red-400" size={48} />
      <h3 className="text-lg font-semibold text-slate-200 mb-2">
        Oops! Something went wrong
      </h3>
      <p className="text-slate-400 mb-4">
        {error.message || 'An unexpected error occurred'}
      </p>
      <Button onClick={resetError} className="flex items-center gap-2">
        <RefreshCw size={16} />
        Try Again
      </Button>
    </motion.div>
  );
}

// Wrapper component for sections
export function DashboardSection({ 
  children, 
  title, 
  className 
}: { 
  children: ReactNode; 
  title?: string; 
  className?: string; 
}) {
  return (
    <DashboardErrorBoundary
      fallback={
        <div className={className}>
          <DashboardErrorFallback 
            error={new Error(`Failed to load ${title || 'section'}`)} 
            resetError={() => window.location.reload()} 
          />
        </div>
      }
    >
      {children}
    </DashboardErrorBoundary>
  );
}
