'use client';

import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  FileText, 
  Gauge, 
  Settings, 
  ChevronDown,
  Menu
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Tab {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  count?: number;
  disabled?: boolean;
}

export interface DashboardTabsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  reviewCount?: number;
  surveyCount?: number;
  className?: string;
}

const defaultTabs: Tab[] = [
  {
    id: 'overview',
    label: 'Overview',
    icon: BarChart3,
  },
  {
    id: 'reviews',
    label: 'Reviews',
    icon: FileText,
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: Gauge,
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
  },
];

export function DashboardTabs({
  activeTab,
  onTabChange,
  reviewCount = 0,
  surveyCount = 0,
  className
}: DashboardTabsProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Update tabs with counts
  const tabs = defaultTabs.map(tab => ({
    ...tab,
    count: tab.id === 'reviews' ? reviewCount : 
           tab.id === 'performance' ? surveyCount : 
           undefined
  }));

  const handleTabClick = useCallback((tabId: string) => {
    onTabChange(tabId);
    setIsMobileMenuOpen(false);
  }, [onTabChange]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent, tabId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTabClick(tabId);
    }
  }, [handleTabClick]);

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className={cn("w-full", className)}>
      {/* Desktop Tabs */}
      <div className="hidden md:block">
        <nav className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-1" aria-label="Dashboard navigation">
          <div className="flex space-x-1" role="tablist">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => handleTabClick(tab.id)}
                  onKeyDown={(e) => handleKeyDown(e, tab.id)}
                  disabled={tab.disabled}
                  role="tab"
                  aria-selected={isActive}
                  aria-controls={`tabpanel-${tab.id}`}
                  tabIndex={isActive ? 0 : -1}
                  className={cn(
                    "relative flex items-center gap-2 px-4 py-3 rounded-md text-sm font-medium transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                    isActive
                      ? "bg-purple-600/20 text-purple-300 shadow-lg"
                      : "text-slate-400 hover:text-slate-300 hover:bg-slate-800/50",
                    tab.disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <Icon size={18} />
                  <span>{tab.label}</span>
                  {tab.count !== undefined && tab.count > 0 && (
                    <span className={cn(
                      "ml-1 px-2 py-0.5 text-xs rounded-full",
                      isActive
                        ? "bg-purple-500/30 text-purple-200"
                        : "bg-slate-700/50 text-slate-400"
                    )}>
                      {tab.count}
                    </span>
                  )}
                  
                  {/* Active indicator */}
                  {isActive && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 bg-purple-600/10 rounded-md border border-purple-500/30"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 500,
                        damping: 30
                      }}
                    />
                  )}
                </button>
              );
            })}
          </div>
        </nav>
      </div>

      {/* Mobile Dropdown */}
      <div className="md:hidden">
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg">
          <button
            type="button"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle navigation menu"
            className="w-full flex items-center justify-between px-4 py-3 text-left"
          >
            <div className="flex items-center gap-2">
              {activeTabData && (
                <>
                  <activeTabData.icon size={18} className="text-purple-400" />
                  <span className="text-slate-200 font-medium">
                    {activeTabData.label}
                  </span>
                  {activeTabData.count !== undefined && activeTabData.count > 0 && (
                    <span className="ml-1 px-2 py-0.5 text-xs rounded-full bg-purple-500/30 text-purple-200">
                      {activeTabData.count}
                    </span>
                  )}
                </>
              )}
            </div>
            <ChevronDown 
              size={18} 
              className={cn(
                "text-slate-400 transition-transform duration-200",
                isMobileMenuOpen && "rotate-180"
              )}
            />
          </button>

          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="border-t border-slate-700/50"
              >
                <div className="p-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    const isActive = activeTab === tab.id;
                    
                    return (
                      <button
                        key={tab.id}
                        type="button"
                        onClick={() => handleTabClick(tab.id)}
                        disabled={tab.disabled}
                        className={cn(
                          "w-full flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
                          "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                          isActive
                            ? "bg-purple-600/20 text-purple-300"
                            : "text-slate-400 hover:text-slate-300 hover:bg-slate-800/50",
                          tab.disabled && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        <Icon size={16} />
                        <span>{tab.label}</span>
                        {tab.count !== undefined && tab.count > 0 && (
                          <span className={cn(
                            "ml-auto px-2 py-0.5 text-xs rounded-full",
                            isActive
                              ? "bg-purple-500/30 text-purple-200"
                              : "bg-slate-700/50 text-slate-400"
                          )}>
                            {tab.count}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
