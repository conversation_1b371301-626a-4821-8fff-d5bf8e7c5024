import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

/**
 * Check if the required profile tables exist in the database
 */
export async function checkProfileTablesExist(): Promise<{
  gaming_profiles: boolean;
  social_media_profiles: boolean;
  allExist: boolean;
  errors: string[];
}> {
  const result = {
    gaming_profiles: false,
    social_media_profiles: false,
    allExist: false,
    errors: [] as string[]
  };

  try {
    const supabase = createServerClient(cookies());

    // Check gaming_profiles table
    try {
      const { error: gamingError } = await supabase
        .from('gaming_profiles')
        .select('id')
        .limit(1);
      
      if (!gamingError) {
        result.gaming_profiles = true;
      } else if (gamingError.code === 'PGRST116' || gamingError.message?.includes('relation') || gamingError.message?.includes('does not exist')) {
        result.errors.push('Gaming profiles table does not exist');
      } else {
        result.errors.push(`Gaming profiles table error: ${gamingError.message}`);
      }
    } catch (error) {
      result.errors.push(`Gaming profiles check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Check social_media_profiles table
    try {
      const { error: socialError } = await supabase
        .from('social_media_profiles')
        .select('id')
        .limit(1);
      
      if (!socialError) {
        result.social_media_profiles = true;
      } else if (socialError.code === 'PGRST116' || socialError.message?.includes('relation') || socialError.message?.includes('does not exist')) {
        result.errors.push('Social media profiles table does not exist');
      } else {
        result.errors.push(`Social media profiles table error: ${socialError.message}`);
      }
    } catch (error) {
      result.errors.push(`Social media profiles check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    result.allExist = result.gaming_profiles && result.social_media_profiles;
    
    return result;
  } catch (error) {
    result.errors.push(`Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return result;
  }
}

/**
 * Get setup instructions for creating the missing tables
 */
export function getTableSetupInstructions(): string {
  return `
To fix the missing profile tables, please run the following SQL commands in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Run the migration file: supabase/migrations/20250126000001_create_profile_tables.sql

Or manually run these commands:

-- Create gaming_profiles table
CREATE TABLE IF NOT EXISTS public.gaming_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    platform TEXT NOT NULL CHECK (platform IN ('steam', 'xbox', 'playstation', 'nintendo', 'epic', 'origin', 'uplay')),
    username TEXT NOT NULL,
    url TEXT,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id, platform)
);

-- Create social_media_profiles table
CREATE TABLE IF NOT EXISTS public.social_media_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    platform TEXT NOT NULL CHECK (platform IN ('twitter', 'facebook', 'instagram', 'youtube', 'twitch', 'github', 'linkedin', 'discord', 'reddit', 'tiktok')),
    username TEXT NOT NULL,
    url TEXT,
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(user_id, platform)
);

-- Enable RLS and create policies (see full migration file for complete setup)

For more details, check the migration file at: supabase/migrations/20250126000001_create_profile_tables.sql
`;
} 