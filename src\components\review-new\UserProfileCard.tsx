import React from 'react';
import type { Review } from '@/lib/types';
import { Heart, Bookmark, Share2, MessageCircle, Eye } from 'lucide-react';
import { useAuthContext } from '@/contexts/auth-context';
import { useLikes } from '@/hooks/useLikes';

interface UserProfileCardProps {
  review?: Review;
}

const UserProfileCard: React.FC<UserProfileCardProps> = ({ review }) => {
  const { user } = useAuthContext ? useAuthContext() : { user: null };
  const { liked, likeCount, toggleLike, isToggling, canLike } = useLikes(review?.id || null);
  
  if (!review) return null;

  // Debug logging for view count updates
  if (process.env.NODE_ENV === 'development') {
    console.log('👁️ UserProfileCard received view_count:', review.view_count, 'at', new Date().toISOString());
  }

  const handleLikeClick = () => {
    if (canLike && !isToggling) {
      toggleLike();
    }
  };
  
  return (
    <div className="mb-6">
      <div className="border border-white/5 bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl overflow-hidden shadow-lg shadow-black/10">
        <div className="p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          {/* Author Info */}
          <a 
            href={`/creators/${review.authorId}`} 
            className="group flex items-center gap-3"
          >
            <div className="w-12 h-12 rounded-full bg-slate-700 border border-slate-600 overflow-hidden flex items-center justify-center">
              {review.authorPhotoURL ? (
                <img 
                  src={review.authorPhotoURL} 
                  alt={review.authorName || 'Anonymous'} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'https://via.placeholder.com/48?text=User'; 
                  }}
                />
              ) : (
                <span className="text-slate-400 text-lg font-mono">{(review.authorName || 'Anon').slice(0, 1)}</span>
              )}
            </div>
            
            <div className="text-left">
              <h3 className="text-lg 2xl:text-xl text-slate-200 font-bold group-hover:text-slate-100 transition-colors font-mono flex items-center gap-2">
                <span className="text-violet-400/80 group-hover:text-violet-400 transition-colors">&lt;</span>
                {review.authorName || 'Anonymous'}
                <span className="text-violet-400/80 group-hover:text-violet-400 transition-colors">/&gt;</span>
              </h3>
              <div className="text-xs 2xl:text-sm text-slate-500 mt-1 font-mono">Level 42 • 128 Reviews</div>
            </div>
          </a>

          {/* Enhanced Quick Actions */}
          <div className="flex items-center gap-2">
            {/* Views - Read Only */}
            <div className="group relative flex flex-col items-center gap-1 p-2 text-slate-400 border border-slate-700 rounded-lg transition-all duration-300 min-w-[3rem] bg-slate-800 cursor-default">
              <Eye className="w-4 h-4 2xl:w-5 2xl:h-5 transition-transform duration-300" />
              <span className="text-xs 2xl:text-sm font-mono font-bold">{(review.view_count || 0).toLocaleString()}</span>
            </div>

            {/* Like Button - Functional */}
            <button
              onClick={handleLikeClick}
              disabled={!canLike || isToggling}
              aria-label={liked ? 'Unlike' : 'Like'}
              className={`group relative flex flex-col items-center gap-1 p-2 border border-slate-700 rounded-lg transition-all duration-300 min-w-[3rem] ${
                !canLike 
                  ? 'bg-slate-800 cursor-not-allowed text-slate-500' 
                  : isToggling
                  ? 'bg-slate-800 cursor-wait text-slate-400'
                  : liked
                  ? 'bg-red-500/20 border-red-500/30 text-red-400 hover:bg-red-500/30 hover:border-red-500/50 transform hover:scale-105 focus:outline-none focus:ring-1 focus:ring-red-500/50'
                  : 'bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-slate-300 hover:border-slate-600 transform hover:scale-105 focus:outline-none focus:ring-1 focus:ring-slate-600'
              }`}
            >
              <Heart className={`w-4 h-4 2xl:w-5 2xl:h-5 transition-all duration-300 ${
                liked ? 'fill-current group-hover:scale-110' : 'group-hover:scale-110'
              } ${isToggling ? 'animate-pulse' : ''}`} />
              <span className="text-xs 2xl:text-sm font-mono font-bold">{likeCount.toLocaleString()}</span>
            </button>

            {/* Other Actions - Placeholder for now */}
            {[
              { Icon: Bookmark, label: 'Save', count: '89' },
              { Icon: Share2, label: 'Share', count: '56' },
              { Icon: MessageCircle, label: 'Discussion', count: '32', href: review.discussionLink || `/reviews/${review.id}/#discussion` }
            ].map(({Icon, label, count, href}) => {
              const Component = href ? 'a' : 'button';
              const props = href ? { href } : { 'aria-label': label };
              return (
                <Component
                  key={label}
                  {...props}
                  className="group relative flex flex-col items-center gap-1 p-2 text-slate-400 border border-slate-700 rounded-lg transition-all duration-300 min-w-[3rem] bg-slate-800 hover:bg-slate-700 hover:text-slate-300 hover:border-slate-600 transform hover:scale-105 focus:outline-none focus:ring-1 focus:ring-slate-600"
                >
                  <Icon className="w-4 h-4 2xl:w-5 2xl:h-5 group-hover:scale-110 transition-transform duration-300" />
                  <span className="text-xs 2xl:text-sm font-mono font-bold">{count}</span>
                </Component>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileCard;
