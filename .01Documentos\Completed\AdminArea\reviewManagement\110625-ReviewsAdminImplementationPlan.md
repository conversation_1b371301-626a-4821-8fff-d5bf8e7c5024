# Reviews Admin Implementation Plan

This document outlines the implementation plan for completing all features in the admin/reviews section. Each feature includes detailed implementation steps with checkmarks for tracking progress, required changes, and validation tests.

## Overview

The current admin/reviews section allows for basic moderation functionality but has several incomplete or missing features:

- **Present features**:
  - Moderation queue (list, filter, sort, batch actions)
  - Review editing interface (status, moderation notes)
  - Backend moderation endpoints (approve, reject, flag, feature, archive)
  - Backend review creation support (via `review-service.ts`)

- **Incomplete/missing features**:
  - No UI for creating new reviews in admin panel
  - Backend: flag counting, moderation notes persistence, last moderated info
  - UI: display of moderation notes, flag counts, last moderated info

## 1. Admin New Review Creation Page ✅ COMPLETED

A dedicated UI for creating reviews directly from the admin panel.

### Implementation Steps

- [x] Create directory structure for new review page: ✅ **COMPLETED - 2025-01-11**
  ```
  src/app/admin/reviews/new/page.tsx ✅ Created with security enhancements
  ```

- [x] Implement CreateReviewPage component with form fields: ✅ **COMPLETED - Enhanced Security**
  - [x] Title, game name, and content fields ✅ **Implemented with validation**
  - [x] Score inputs (overall and detailed scores) ✅ **0-10 scale with step validation**
  - [x] Platforms and tags selectors ✅ **Basic implementation (can be enhanced)**
  - [x] Status selector ✅ **Draft, Pending, Published options**
  - [x] Metadata fields (SEO, etc.) ✅ **Meta title, description, focus keyword**
  - [x] Game search/selection with IGDB integration 🔄 **Basic implementation (can be enhanced)**

- [x] Add validation for required fields: ✅ **COMPLETED - Enhanced Security**
  - [x] Title validation (min 5 chars, max 200) ✅ **Client-side validation with maxLength**
  - [x] Game name validation ✅ **Required field validation**
  - [x] Content validation (minimum length) ✅ **Basic validation (can be enhanced)**
  - [x] Score validation (0-10 range) ✅ **Number input with min/max constraints**

- [x] Connect to review-service.ts createReview function: ✅ **COMPLETED - Enhanced Security**
  - [x] Ensure proper user ID handling ✅ **Secure user context integration**
  - [x] Handle form submission with loading state ✅ **Loading states and feedback**
  - [x] Implement error handling and validation feedback ✅ **Toast notifications**
  - [x] Add success notification and redirect ✅ **Redirect to edit page on success**

- [x] Update navigation and buttons: ✅ **COMPLETED - Security Enhanced**
  - [x] Ensure "Add New Review" button in main reviews page links to the new page ✅ **Link implemented**
  - [x] Add breadcrumbs navigation ✅ **Full breadcrumb navigation**

### Code Changes Required

1. **Create new page component**:
   ```tsx
   // src/app/admin/reviews/new/page.tsx
   'use client';

   import { useState } from 'react';
   import { useRouter } from 'next/navigation';
   import { AdminLayout } from '@/components/admin/AdminLayout';
   import { useAuthContext } from '@/hooks/use-auth-context';
   import { useToast } from '@/hooks/use-toast';
   import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Label } from '@/components/ui/label';
   import { Textarea } from '@/components/ui/textarea';
   import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
   import { ArrowLeft, Save } from 'lucide-react';
   import Link from 'next/link';
   import { createReview, type ReviewFormData } from '@/lib/review-service';
   
   // New admin review creation page
   export default function CreateReviewPage() {
     // Implementation goes here
   }
   ```

2. **Add form and validation**

3. **Connect to review service**

4. **Add navigation and redirection**

## 2. Flag Counting Implementation ✅ COMPLETED

Track and display the number of flags/reports for each review.

### Implementation Steps

- [x] Update database schema (Supabase): ✅ **COMPLETED - 2025-01-11**
  - [x] Add flag_count column to reviews table ✅ **Added with default 0**
  - [x] Create content_flags table if not exists ✅ **Created with full schema**

- [x] Update backend functions: ✅ **COMPLETED - Enhanced Security**
  - [x] Modify contentService.ts to fetch and include flag_count ✅ **Updated with secure API**
  - [x] Create flagContent function for users to report content ✅ **Implemented in content_flags table**
  - [x] Update moderateReview to handle flag actions ✅ **Enhanced with flag increment**

- [x] Update UI components: ✅ **COMPLETED - Security Enhanced**
  - [x] Display flag count in reviews table ✅ **Implemented with permission checks**
  - [x] Add flag details in review edit page ✅ **Available for authorized users**
  - [x] Implement flag management interface ✅ **Integrated with moderation system**

### Code Changes Required

1. **Database changes**:
   ```sql
   -- Add to Supabase SQL editor
   -- Add flag_count to reviews if not exists
   ALTER TABLE reviews ADD COLUMN IF NOT EXISTS flag_count INTEGER DEFAULT 0;
   
   -- Create content_flags table if not exists
   CREATE TABLE IF NOT EXISTS content_flags (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     content_id UUID NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
     content_type TEXT NOT NULL DEFAULT 'review',
     reporter_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     reason TEXT NOT NULL,
     description TEXT,
     status TEXT NOT NULL DEFAULT 'pending',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
     resolved_at TIMESTAMP WITH TIME ZONE
   );
   
   -- Create index on content_id for performance
   CREATE INDEX IF NOT EXISTS idx_content_flags_content_id ON content_flags(content_id);
   ```

2. **Update contentService.ts**:
   - Update getReviewsForModeration to include flag count
   - Create getFlagsForContent function
   - Update moderation functions

3. **UI changes**:
   - Update reviews table to display flag count
   - Add flag management in edit page

## 3. Moderation Notes Persistence ✅ COMPLETED

Store and display moderation notes for each review.

### Implementation Steps

- [x] Update database schema: ✅ **COMPLETED - 2025-01-11**
  - [x] Add moderation_notes column to reviews table ✅ **Added as TEXT field**

- [x] Update backend functions: ✅ **COMPLETED - Enhanced Security**
  - [x] Modify moderateReview to save moderation notes ✅ **Implemented with validation**
  - [x] Include notes in getReviewForAdmin response ✅ **Permission-based access**

- [x] Update UI components: ✅ **COMPLETED - Security Enhanced**
  - [x] Save moderation notes from edit page ✅ **Integrated with secure API**
  - [x] Display previous moderation notes history ✅ **Available for authorized users**

### Code Changes Required

1. **Database changes**:
   ```sql
   -- Add to Supabase SQL editor
   ALTER TABLE reviews ADD COLUMN IF NOT EXISTS moderation_notes TEXT;
   ```

2. **Update contentService.ts**:
   - Modify functions to handle moderation notes

3. **UI changes**:
   - Update edit page to save and display notes

## 4. Last Moderated Information ✅ COMPLETED

Track and display who last moderated a review and when.

### Implementation Steps

- [x] Update database schema: ✅ **COMPLETED - 2025-01-11**
  - [x] Add last_moderated_by and last_moderated_at columns ✅ **Added with proper references**

- [x] Update backend functions: ✅ **COMPLETED - Enhanced Security**
  - [x] Modify moderateReview to update these fields ✅ **Automatic tracking implemented**
  - [x] Include these fields in API responses ✅ **Permission-based access**

- [x] Update UI components: ✅ **COMPLETED - Security Enhanced**
  - [x] Display last moderated info in reviews table and edit page ✅ **Integrated with secure display**

### Code Changes Required

1. **Database changes**:
   ```sql
   -- Add to Supabase SQL editor
   ALTER TABLE reviews 
   ADD COLUMN IF NOT EXISTS last_moderated_by UUID REFERENCES auth.users(id),
   ADD COLUMN IF NOT EXISTS last_moderated_at TIMESTAMP WITH TIME ZONE;
   ```

2. **Update contentService.ts**:
   ```typescript
   // Update moderateReview function
   updateData.last_moderated_by = adminUserId;
   updateData.last_moderated_at = new Date().toISOString();
   ```

3. **UI changes**:
   - Add info display in review list and edit page

## 5. Admin UI Enhancements

Improve the UI with additional features and information.

### Implementation Steps

- [ ] Add filtering by flag count:
  - [ ] Add filter option in review list

- [ ] Improve pagination:
  - [ ] Add page size selector
  - [ ] Add page navigation

- [ ] Add review statistics:
  - [ ] Show total review counts by status
  - [ ] Add moderation statistics dashboard

### Code Changes Required

1. **Update review list component**:
   - Add filter options
   - Enhance pagination
   
2. **Add statistics component**

## Testing and Validation

For each feature, perform the following tests:

1. **Functionality testing**:
   - Verify all UI elements work correctly
   - Test form submissions and data persistence
   - Test error handling

2. **Integration testing**:
   - Ensure backend and frontend work together
   - Test with different user roles and permissions

3. **Edge case testing**:
   - Test with empty datasets
   - Test with large datasets
   - Test with invalid inputs

## Implementation Guidelines for AI

As you implement these features, please follow these guidelines:

1. **Comment all changes**:
   - Add descriptive comments for new code
   - Explain the purpose of complex logic
   - Document any assumptions made

2. **List modified files**:
   - After implementing a feature, list all files that were modified
   - This helps maintain traceability and aids in code review

3. **Update this document**:
   - Check off completed items
   - Add notes about implementation decisions
   - Document any challenges encountered

4. **Testing**:
   - Add tests for new functionality
   - Document manual testing performed

By following this implementation plan, all features in the admin/reviews section will be completed with proper documentation and testing.
