'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Eye, Heart, Calendar, ExternalLink, ShoppingCart } from 'lucide-react';
import Link from 'next/link';
import EnebaIcon from '@/components/ui/icons/stores/EnebaIcon';
import G2AIcon from '@/components/ui/icons/stores/G2AIcon';
import GOGIcon from '@/components/ui/icons/stores/GOGIcon';
import HRKIcon from '@/components/ui/icons/stores/HRKIcon';
import InstantGamingIcon from '@/components/ui/icons/stores/InstantGamingIcon';
import KinguinIcon from '@/components/ui/icons/stores/KinguinIcon';
import NuuvemIcon from '@/components/ui/icons/stores/NuuvemIcon';

interface StoreLink {
  id: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  color_gradient: string;
  display_order: number;
}

interface FeaturedBanner {
  id: string;
  user_id: string;
  review_id: string;
  display_order: number;
  is_active: boolean;
  review: {
    id: string;
    title: string;
    game_name: string;
    igdb_cover_url?: string;
    main_image_url?: string;
    overall_score: number;
    like_count: number;
    view_count: number;
    created_at: string;
    played_on?: string;
    slug?: string;
    games?: {
      cover_url?: string;
      igdb_id?: number;
    };
  };
  store_links?: StoreLink[];
}

interface FeaturedBannersCarouselProps {
  userId: string;
  className?: string;
}

export default function FeaturedBannersCarousel({ userId, className = '' }: FeaturedBannersCarouselProps) {
  const [banners, setBanners] = useState<FeaturedBanner[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hoveredStripe, setHoveredStripe] = useState<string | null>(null);
  const [showScoreGauge, setShowScoreGauge] = useState(false);
  const [isImageLoaded, setIsImageLoaded] = useState<{[key: string]: boolean}>({});
  const [timerProgress, setTimerProgress] = useState(0);

  // Store icon mapping function
  const getStoreIcon = (storeName: string, className: string = "h-4 w-4") => {
    const iconProps = { className };
    switch (storeName) {
      case 'Eneba': return <EnebaIcon {...iconProps} />;
      case 'G2A': return <G2AIcon {...iconProps} />;
      case 'GOG': return <GOGIcon {...iconProps} />;
      case 'HRK': return <HRKIcon {...iconProps} />;
      case 'Instant Gaming': return <InstantGamingIcon {...iconProps} />;
      case 'Kinguin': return <KinguinIcon {...iconProps} />;
      case 'Nuuvem': return <NuuvemIcon {...iconProps} />;
      default: return <ShoppingCart {...iconProps} />;
    }
  };

  // Calculate discount percentage
  const calculateDiscount = (price: string, originalPrice?: string) => {
    if (!originalPrice || originalPrice === price) return null;
    
    const current = parseFloat(price.replace(/[^0-9.,]/g, '').replace(',', '.'));
    const original = parseFloat(originalPrice.replace(/[^0-9.,]/g, '').replace(',', '.'));
    
    if (isNaN(current) || isNaN(original) || original <= current) return null;
    
    return Math.round(((original - current) / original) * 100);
  };

  // Discount heat color function
  const getDiscountHeatColor = (discountPercent: number) => {
    if (discountPercent >= 70) return '#10b981'; // green-500
    if (discountPercent >= 50) return '#22c55e'; // green-400
    if (discountPercent >= 30) return '#84cc16'; // lime-400
    if (discountPercent >= 15) return '#eab308'; // yellow-500
    return '#f97316'; // orange-500
  };

  const getTextColor = (discountPercent: number) => {
    return discountPercent >= 30 ? '#000000' : '#ffffff';
  };

  // Load banners
  useEffect(() => {
    const loadBanners = async () => {
      try {
        setLoading(true);
        
        const response = await fetch('/api/u/featured-banners', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'getBanners', userId })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result && result.success && result.data) {
          // Load store links for each banner
          const bannersWithStoreLinks = await Promise.all(
            result.data.map(async (banner: FeaturedBanner) => {
              if (banner.id) {
                try {
                  const storeLinksResponse = await fetch('/api/u/featured-banners', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'getStoreLinks', userId, bannerId: banner.id })
                  });
                  
                  if (storeLinksResponse.ok) {
                    const storeLinksResult = await storeLinksResponse.json();
                    if (storeLinksResult.success) {
                      banner.store_links = storeLinksResult.data;
                    }
                  }
                } catch (error) {
                  console.error('Error loading store links for banner:', banner.id, error);
                }
              }
              return banner;
            })
          );
          
          setBanners(bannersWithStoreLinks.sort((a, b) => a.display_order - b.display_order));
        } else {
          setBanners([]);
        }
      } catch (error) {
        console.error('Error loading featured banners:', error);
        setError(error instanceof Error ? error.message : 'Failed to load featured banners');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      loadBanners();
    }
  }, [userId]);

  // Auto-advance carousel
  useEffect(() => {
    if (banners.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % banners.length);
      setTimerProgress(0);
    }, 8000);

    return () => clearInterval(timer);
  }, [banners.length, currentIndex]);

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
    setTimerProgress(0);
  }, []);

  // Show score gauge after image loads
  useEffect(() => {
    if (banners.length > 0 && currentIndex < banners.length) {
      const currentBanner = banners[currentIndex];
      if (isImageLoaded[currentBanner.id]) {
        const timer = setTimeout(() => setShowScoreGauge(true), 800);
        return () => clearTimeout(timer);
      }
    }
  }, [currentIndex, banners, isImageLoaded]);

  // Reset score gauge when changing banners
  useEffect(() => {
    setShowScoreGauge(false);
  }, [currentIndex]);

  // Timer progress animation
  useEffect(() => {
    if (banners.length <= 1) return;

    const interval = setInterval(() => {
      setTimerProgress(prev => {
        if (prev >= 100) return 0;
        return prev + (100 / 80); // 8000ms / 100ms intervals
      });
    }, 100);

    return () => clearInterval(interval);
  }, [banners.length, currentIndex]);

  if (loading) {
    return (
      <div className={`relative w-full h-96 bg-gray-900/60 border border-gray-700/50 rounded-xl overflow-hidden animate-pulse ${className}`}>
        <div className="absolute inset-0 bg-gradient-to-r from-gray-800 to-gray-700"></div>
        <div className="absolute bottom-6 left-6 space-y-3">
          <div className="h-8 bg-gray-600 rounded w-64"></div>
          <div className="h-4 bg-gray-600 rounded w-32"></div>
        </div>
      </div>
    );
  }

  if (error || banners.length === 0) {
    return null; // Don't show anything if no banners or error
  }

  const currentBanner = banners[currentIndex];
  const displayScore = Math.round(currentBanner.review.overall_score * 10);

  // Get the best available cover image with proper fallback logic
  const getCoverImageUrl = (review: FeaturedBanner['review']) => {
    const coverUrl = review.main_image_url || review.games?.cover_url || review.igdb_cover_url;
    return coverUrl;
  };

  return (
    <div className={`relative w-full h-96 overflow-hidden rounded-xl border border-gray-700/50 ${className}`}>
      {/* Background Image */}
      <div className="absolute inset-0">
        {getCoverImageUrl(currentBanner.review) ? (
          <img
            src={getCoverImageUrl(currentBanner.review)}
            alt={currentBanner.review.game_name}
            className="w-full h-full object-cover"
            onLoad={() => setIsImageLoaded(prev => ({ ...prev, [currentBanner.id]: true }))}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900"></div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>
      </div>

      <Link href={`/review/${currentBanner.review.slug || currentBanner.review.id}`} className="block h-full">
        <div className="relative h-full">
          {/* Store Links */}
          {currentBanner.store_links && currentBanner.store_links.length > 0 && (
            <div className="absolute top-1/2 right-4 transform -translate-y-1/2 flex flex-col gap-2 z-30">
              {currentBanner.store_links.map((link) => (
                <div
                  key={link.id}
                  className="relative cursor-pointer group"
                  onMouseEnter={() => setHoveredStripe(link.id)}
                  onMouseLeave={() => setHoveredStripe(null)}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.open(link.store_url, '_blank', 'noopener,noreferrer');
                  }}
                >
                  <div className="relative min-w-96 w-auto h-12 overflow-visible">
                    {(() => {
                      const discountPercent = calculateDiscount(link.price, link.original_price);
                      const hasDiscount = discountPercent !== null && discountPercent > 0;

                      return (
                        <>
                          {/* Buy Now Button */}
                          <div className={`absolute right-0 top-0 h-12 rounded-lg bg-gray-800/30 backdrop-blur-md border border-gray-700/30 flex items-center justify-center z-20 px-2 ${hasDiscount ? 'w-32' : 'w-24'}`}>
                            {hasDiscount && (
                              <div
                                className="flex items-center justify-center px-2 py-1 rounded text-sm font-mono font-bold mr-2"
                                style={{
                                  backgroundColor: getDiscountHeatColor(discountPercent),
                                  color: getTextColor(discountPercent)
                                }}
                              >
                                -{discountPercent}%
                              </div>
                            )}
                            <span className="text-white/80 text-xs font-mono font-bold text-center leading-tight">
                              BUY NOW
                            </span>
                          </div>

                          {/* Sliding Content Panel */}
                          <div className={`
                            absolute ${hasDiscount ? 'right-36' : 'right-28'} top-0 h-12 rounded-lg bg-gray-800/30 backdrop-blur-md
                            border border-gray-700/30 flex items-center pr-2 gap-2
                            transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]
                            ${hoveredStripe === link.id ?
                              'w-auto translate-x-0 opacity-100' :
                              'w-0 translate-x-8 opacity-0'
                            }
                          `}>
                            {/* Price Info */}
                            <div className="flex flex-col justify-center flex-shrink-0 pl-3">
                              <div className="text-white font-mono text-sm font-bold whitespace-nowrap">
                                {link.price}
                              </div>
                              {link.original_price && (
                                <div className="text-white/50 font-mono text-sm line-through whitespace-nowrap">
                                  {link.original_price}
                                </div>
                              )}
                            </div>

                            {/* Platform Badge */}
                            <div className="bg-gray-900/50 rounded pr-2 border border-gray-700/30 flex items-center gap-2 flex-shrink-0">
                              <div className="flex items-center justify-center">
                                {getStoreIcon(link.store_name, "h-7 w-7 text-white/70 flex-shrink-0")}
                              </div>
                              <span className="text-white/80 font-mono text-xs whitespace-nowrap flex items-center">
                                {link.store_name}
                              </span>
                            </div>

                            {/* Action Button */}
                            <div className="w-8 h-8 rounded bg-gray-900/50 border border-gray-700/30 flex items-center justify-center hover:bg-gray-800/50 transition-colors shrink-0">
                              <ExternalLink className="h-4 w-4 text-white/60" />
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Content System */}
          <div className="absolute bottom-0 left-0 right-0 p-6 z-10">
            <div className="space-y-3">
              {/* Title */}
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-white font-mono tracking-tight leading-tight">
                  <span className="text-white/40 font-mono text-lg mr-2">//</span>
                  {currentBanner.review.title || currentBanner.review.game_name}
                </h3>
                
                {/* Played On Info */}
                <div className="text-sm text-white/70 font-mono">
                  <span className="text-white/40">//</span> Played on {currentBanner.review.played_on || 'PC'}
                </div>
              </div>
              
              {/* Stats Bar */}
              <div className="flex items-center gap-4 font-mono text-xs">
                <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                  <Heart className="w-3 h-3 text-white/60" />
                  <span className="text-white tabular-nums">{currentBanner.review.like_count || 0}</span>
                </div>
                
                <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                  <Eye className="w-3 h-3 text-white/60" />
                  <span className="text-white tabular-nums">{currentBanner.review.view_count || 0}</span>
                </div>
                
                <div className="flex items-center gap-1 px-2 py-1 bg-gray-800/30 border border-gray-700/30 rounded">
                  <Calendar className="w-3 h-3 text-white/60" />
                  <span className="text-white/60">
                    {currentBanner.review.date_played || 
                     new Date(currentBanner.review.created_at).toLocaleDateString('en-US', {
                       month: '2-digit',
                       year: 'numeric'
                     })}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Score Display */}
          {showScoreGauge && (
            <div className="absolute top-6 right-6 z-50 animate-[fadeInScale_400ms_ease-out]">
              <div className="bg-gray-900/40 backdrop-blur-md rounded-lg border border-gray-800/50 px-4 py-2">
                <div className="text-xl font-mono font-bold text-white tabular-nums">
                  <span className="text-white/40 text-sm">//</span> {displayScore}<span className="text-white/60 text-sm">/100</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </Link>

      {/* Carousel Controls */}
      {banners.length > 1 && (
        <>
          {/* Enhanced Navigation System */}
          <div className="absolute top-6 left-6 z-40">
            <div className="bg-gray-800/30 backdrop-blur-md border border-gray-700/30 rounded-lg p-2">
              <div className="flex items-center gap-2">
                {/* Banner Navigation Dots */}
                <div className="flex gap-1">
                  {banners.map((_, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => goToSlide(index)}
                      className={`
                        relative transition-all duration-300 rounded-full group
                        ${index === currentIndex 
                          ? 'w-8 h-2 bg-gradient-to-r from-blue-500 to-purple-600 shadow-md' 
                          : 'w-2 h-2 bg-white/40 hover:bg-white/70 hover:w-4'
                        }
                      `}
                      title={`Banner ${index + 1}`}
                    >
                      {/* Timer progress bar for active banner */}
                      {index === currentIndex && banners.length > 1 && (
                        <div 
                          className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full transition-all duration-75 ease-linear"
                          style={{ width: `${timerProgress}%` }}
                        />
                      )}
                      
                      {/* Glow effect for active banner */}
                      {index === currentIndex && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-sm opacity-50 -z-10"></div>
                      )}
                    </button>
                  ))}
                </div>
                
                {/* Separator */}
                <div className="w-px h-3 bg-white/20"></div>
                
                {/* Current Banner Info */}
                <div className="text-white/80 text-xs font-mono font-bold">
                  {currentIndex + 1}/{banners.length}
                </div>
              </div>
            </div>
          </div>
        </>
      )}


      {/* Animations */}
      <style jsx>{`
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8) rotate(-5deg);
          }
          to {
            opacity: 1;
            transform: scale(1) rotate(0deg);
          }
        }
        
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </div>
  );
}
