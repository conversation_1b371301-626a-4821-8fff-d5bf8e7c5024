'use client';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { useAuthContext } from '@/hooks/use-auth-context';
import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Search, ShieldCheck, ShieldAlert, MoreVertical } from 'lucide-react';
import Link from 'next/link';
import { getUsersList, updateUserRole as updateUserRoleAction, updateUserStatus as updateUserStatusAction } from './actions';
import type { ExtendedUserProfile } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
  DropdownMenuSubContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
// AlertDialog imports removed - delete functionality removed for security


export default function AdminUsersPage() {
  const { user: adminUser, loading: authLoading, isAdmin } = useAuthContext();
  const { toast } = useToast();
  const router = useRouter();

  const [users, setUsers] = useState<ExtendedUserProfile[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  const [currentRoleMap, setCurrentRoleMap] = useState<Record<string, string>>({});
  const [currentStatusMap, setCurrentStatusMap] = useState<Record<string, boolean>>({});

  // Suspension dialog state
  const [suspensionDialog, setSuspensionDialog] = useState<{
    isOpen: boolean;
    userId: string;
    username: string;
    currentStatus: boolean;
  }>({
    isOpen: false,
    userId: '',
    username: '',
    currentStatus: false
  });
  const [suspensionReason, setSuspensionReason] = useState('');

  const fetchUsers = useCallback(async () => {
    if (!isAdmin || authLoading) {
      if (!authLoading) setError("Admin privileges required.");
      return;
    }
    setIsLoadingUsers(true);
    setError(null);
    try {
      const result = await getUsersList(1, 50); // page 1, limit 50
      const userList = result.users;
      setUsers(userList);
      const initialRoles: Record<string, string> = {};
      const initialStatuses: Record<string, boolean> = {};
      userList.forEach(u => {
        initialRoles[u.uid] = u.role || 'User';
        initialStatuses[u.uid] = !!u.disabled; // u.disabled now properly reflects suspension status
      });
      setCurrentRoleMap(initialRoles);
      setCurrentStatusMap(initialStatuses);
    } catch (err: any) {
      setError(err.message);
      toast({ title: "Error fetching users", description: err.message, variant: "destructive" });
    }
    setIsLoadingUsers(false);
  }, [isAdmin, toast, authLoading]);

  // Load users when component mounts and admin is verified
  useEffect(() => {
    if (isAdmin && !authLoading) {
      fetchUsers();
    }
  }, [isAdmin, authLoading, fetchUsers]);

  // handleDeleteUser function removed for security - user suspension should be used instead

  const handleRoleChange = async (uid: string, newRole: string) => {
    if (!isAdmin) return;
    try {
      await updateUserRoleAction(uid, newRole);
      toast({ title: "Role Updated", description: `User role updated to ${newRole}.` });
      // Only refresh from database - don't update local state manually
      fetchUsers();
    } catch (err: any) {
      toast({ title: "Error Updating Role", description: err.message, variant: "destructive" });
    }
  };

  const handleStatusChange = async (uid: string, newStatusDisabled: boolean) => {
    if (!isAdmin) return;

    // If suspending a user, open dialog to collect reason
    if (newStatusDisabled) {
      const user = users.find(u => u.uid === uid);
      setSuspensionDialog({
        isOpen: true,
        userId: uid,
        username: user?.displayName || user?.username || 'Unknown User',
        currentStatus: currentStatusMap[uid] || false
      });
      setSuspensionReason('');
      return;
    }

    // If unsuspending, proceed directly
    try {
      await updateUserStatusAction(uid, newStatusDisabled);
      toast({ title: "Status Updated", description: `User status updated to ${newStatusDisabled ? 'Suspended' : 'Active'}.` });
      // Only refresh from database - don't update local state manually
      fetchUsers();
    } catch (err: any) {
      toast({ title: "Error Updating Status", description: err.message, variant: "destructive" });
    }
  };

  const handleSuspensionConfirm = async () => {
    if (!suspensionReason.trim() || suspensionReason.trim().length < 5) {
      toast({
        title: "Invalid Reason",
        description: "Suspension reason must be at least 5 characters long.",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateUserStatusAction(suspensionDialog.userId, true, suspensionReason.trim());
      toast({
        title: "User Suspended",
        description: `${suspensionDialog.username} has been suspended.`
      });
      setSuspensionDialog({ isOpen: false, userId: '', username: '', currentStatus: false });
      setSuspensionReason('');
      // Only refresh from database - don't update local state manually
      fetchUsers();
    } catch (err: any) {
      toast({ title: "Error Suspending User", description: err.message, variant: "destructive" });
    }
  };

  const handleSuspensionCancel = () => {
    setSuspensionDialog({ isOpen: false, userId: '', username: '', currentStatus: false });
    setSuspensionReason('');
  };


  if (error) {
    return (
      <AdminLayout
        title="User Management"
        description="View, edit, and manage user accounts"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'User Management' }
        ]}
      >
        <Card className="w-full max-w-lg mx-auto shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-destructive font-mono tracking-tight">
              <span className="text-red-400">&lt;</span>
              <span className="text-red-300 drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Error Loading Users</span>
              <span className="text-red-400">/&gt;</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center font-mono text-sm">Could not load user data: {error}</p>
          </CardContent>
          <div className="flex flex-col space-y-2 p-6">
            <Button onClick={fetchUsers} className="w-full bg-violet-600/80 hover:bg-violet-600 transition-colors font-mono">Try Again</Button>
            <Button asChild variant="outline" className="w-full border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
              <Link href="/admin">Back to Admin</Link>
            </Button>
          </div>
        </Card>
      </AdminLayout>
    );
  }

  const filteredUsers = users.filter(u =>
    (u.displayName && u.displayName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (u.username && u.username.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getRoleBadgeVariant = (role?: string) => {
    if (role === 'Admin') return 'destructive';
    if (role === 'Moderator') return 'secondary';
    return 'outline'; // For 'User' or undefined
  };

  const getRoleBadgeClass = (role?: string) => {
    if (role === 'Admin') return 'bg-red-500/20 text-red-700 border-red-500/30';
    if (role === 'Moderator') return 'bg-purple-500/20 text-purple-700 border-purple-500/30';
    return ''; // Default for User
  };
  
  const getStatusBadgeVariant = (disabled?: boolean) => {
    return disabled ? 'destructive' : 'default';
  };

  const getStatusBadgeClass = (disabled?: boolean) => {
    return disabled 
      ? 'bg-orange-500/20 text-orange-700 border-orange-500/30 hover:bg-orange-500/30' // Suspended
      : 'bg-green-500/20 text-green-700 border-green-500/30 hover:bg-green-500/30'; // Active
  };
  

  return (
    <AdminLayout
      title="User Management"
      description="View, edit roles, and manage user accounts"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'User Management' }
      ]}
    >
      {/* Add User button removed for security - users should register through normal flow */}

      <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-2xl font-mono tracking-tight">
            <span className="text-violet-400">&lt;</span>
            <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">All Users</span>
            <span className="text-violet-400">/&gt;</span>
          </CardTitle>
          <CardDescription className="text-muted-foreground font-mono text-sm">A list of all registered users on CriticalPixel.</CardDescription>
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users by name or username..."
              className="pl-10 bg-slate-800/60 border-slate-600/50 focus:border-violet-500/50 transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardHeader>
        <CardContent>
          <Table className="border-slate-700/30">
            <TableHeader>
              <TableRow className="border-slate-700/30 hover:bg-slate-800/30">
                <TableHead className="font-mono text-violet-300">User</TableHead>
                <TableHead className="font-mono text-violet-300">Role</TableHead>
                <TableHead className="font-mono text-violet-300">Status</TableHead>
                <TableHead className="font-mono text-violet-300">Last Login</TableHead>
                <TableHead className="text-right font-mono text-violet-300">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length > 0 ? filteredUsers.map((u) => (
                <TableRow key={u.uid} className="border-slate-700/20 hover:bg-slate-800/20 transition-colors">
                  <TableCell className="font-medium font-mono">
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={u.photoURL || undefined} alt={u.displayName || 'User'} />
                        <AvatarFallback>{(u.displayName || 'U').substring(0, 2).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      {/* BUG FIX 210126: Added link to user profile */}
                      {u.username ? (
                        <Link 
                          href={`/u/${u.username}`}
                          className="hover:text-violet-400 hover:underline transition-colors font-mono text-sm"
                        >
                          {u.displayName || u.username || 'Anonymous User'}
                        </Link>
                      ) : (
                        <span className="font-mono text-sm">{u.displayName || 'Anonymous User'}</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(currentRoleMap[u.uid])} className={getRoleBadgeClass(currentRoleMap[u.uid])}>
                      {currentRoleMap[u.uid] || 'User'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(currentStatusMap[u.uid])} className={getStatusBadgeClass(currentStatusMap[u.uid])}>
                      {currentStatusMap[u.uid] ? 'Suspended' : 'Active'}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono text-sm text-muted-foreground">{u.lastSignInTime ? new Date(u.lastSignInTime).toLocaleDateString() : 'N/A'}</TableCell>
                  <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 p-0 hover:bg-slate-700/50 transition-colors">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                      <DropdownMenuLabel className="font-mono text-violet-300">Actions</DropdownMenuLabel>
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger>
                         <ShieldCheck className="mr-2 h-4 w-4 text-violet-400" /> <span className="font-mono text-sm">Change Role</span>
                        </DropdownMenuSubTrigger>
                        <DropdownMenuPortal>
                          <DropdownMenuSubContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                            <DropdownMenuRadioGroup value={currentRoleMap[u.uid]} onValueChange={(newRole) => handleRoleChange(u.uid, newRole)}>
                              <DropdownMenuRadioItem value="Moderator" className="font-mono text-sm">Moderator</DropdownMenuRadioItem>
                              <DropdownMenuRadioItem value="User" className="font-mono text-sm">User</DropdownMenuRadioItem>
                            </DropdownMenuRadioGroup>
                          </DropdownMenuSubContent>
                        </DropdownMenuPortal>
                      </DropdownMenuSub>
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger>
                         {currentStatusMap[u.uid] ? <ShieldCheck className="mr-2 h-4 w-4 text-violet-400" /> : <ShieldAlert className="mr-2 h-4 w-4 text-violet-400" /> }
                          <span className="font-mono text-sm">Change Status</span>
                        </DropdownMenuSubTrigger>
                        <DropdownMenuPortal>
                          <DropdownMenuSubContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                             <DropdownMenuRadioGroup value={currentStatusMap[u.uid] ? 'suspended' : 'active'} onValueChange={(val) => handleStatusChange(u.uid, val === 'suspended')}>
                                <DropdownMenuRadioItem value="active" className="font-mono text-sm">Active</DropdownMenuRadioItem>
                                <DropdownMenuRadioItem value="suspended" className="font-mono text-sm">Suspended</DropdownMenuRadioItem>
                              </DropdownMenuRadioGroup>
                          </DropdownMenuSubContent>
                        </DropdownMenuPortal>
                      </DropdownMenuSub>
                      {/* Delete User functionality removed for security - user suspension should be used instead */}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground py-8 font-mono text-sm">
                    {isLoadingUsers ? 'Loading users...' : 'No users found matching your search.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Suspension Reason Dialog */}
      <Dialog open={suspensionDialog.isOpen} onOpenChange={(open) => !open && handleSuspensionCancel()}>
        <DialogContent className="sm:max-w-[425px] bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
          <DialogHeader>
            <DialogTitle className="font-mono text-xl tracking-tight">
              <span className="text-orange-400">&lt;</span>
              <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Suspend User</span>
              <span className="text-orange-400">/&gt;</span>
            </DialogTitle>
            <DialogDescription className="font-mono text-sm text-muted-foreground">
              You are about to suspend <strong className="text-orange-300">{suspensionDialog.username}</strong>.
              Please provide a reason for this action.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="suspension-reason" className="font-mono text-sm text-violet-300">Suspension Reason</Label>
              <Textarea
                id="suspension-reason"
                placeholder="Enter the reason for suspension (minimum 5 characters)..."
                value={suspensionReason}
                onChange={(e) => setSuspensionReason(e.target.value)}
                className="min-h-[100px] bg-slate-800/60 border-slate-600/50 focus:border-violet-500/50 transition-colors font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground font-mono">
                <span className="text-violet-400">{suspensionReason.length}</span>/5 characters minimum
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleSuspensionCancel} className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleSuspensionConfirm}
              disabled={suspensionReason.trim().length < 5}
              className="bg-orange-600/80 hover:bg-orange-600 transition-colors font-mono"
            >
              Suspend User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
