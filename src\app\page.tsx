
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useAuthContext } from "@/contexts/auth-context";
// FIREBASE IMPORT REMOVED - updateUserProfileFields import removed
// TODO: Replace with Supabase user profile update when ready
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import Image from 'next/image';
import Link from "next/link";
import { Rocket, Star, Gamepad, Newspaper } from "lucide-react"; // Added Newspaper for latest review
import { useEffect, useRef } from 'react';
import { Badge } from "@/components/ui/badge"; // Added Badge import

// Removed unused UserProfile and EditGameCardModal imports as this is the homepage and not a profile page
interface ReviewTeaser {
  id: string;
  title: string;
  game: string;
  platform: string;
  score: number;
  imageUrl: string;
  author: string;
  date: string;
  slug: string;
  dataAiHint?: string;
  // Added specific scores for animation
  artScore?: number;
  gameplayScore?: number;
  storyScore?: number;
  excerpt?: string; // Added excerpt for latest review banner
}

const featuredReviews: ReviewTeaser[] = [
  { id: '1', title: "Epic Adventure in Eldoria", game: "Chronicles of Eldoria", platform: "PC", score: 9.2, imageUrl: "https://picsum.photos/seed/game1/1200/500", author: "Jane Doe", date: "Oct 26, 2023", slug: "epic-adventure-eldoria", dataAiHint: "fantasy landscape", artScore: 9.5, gameplayScore: 9.0, storyScore: 9.1, excerpt: "Chronicles of Eldoria sets a new benchmark for open-world RPGs with its breathtaking world and deep narrative." },
  { id: '2', title: "Cybernetic Mayhem Unleashed", game: "Neon City Takedown", platform: "PS5", score: 8.8, imageUrl: "https://picsum.photos/seed/game2/600/400", author: "John Smith", date: "Oct 24, 2023", slug: "cybernetic-mayhem", dataAiHint: "cyberpunk city", artScore: 8.9, gameplayScore: 8.7, storyScore: 8.5, excerpt: "A thrilling dive into a neon-soaked dystopia, Neon City Takedown offers fast-paced action and a compelling story." },
  { id: '3', title: "A Puzzling Masterpiece", game: "The Enigma Box", platform: "Switch", score: 9.5, imageUrl: "https://picsum.photos/seed/game3/600/400", author: "Alex Green", date: "Oct 22, 2023", slug: "puzzling-masterpiece", dataAiHint: "abstract puzzle", artScore: 9.6, gameplayScore: 9.4, storyScore: 9.5, excerpt: "Prepare to have your mind bent by The Enigma Box, a truly unique and rewarding puzzle adventure." },
];

const latestArticles: ReviewTeaser[] = [
    { id: '4', title: "Indie Gem: Whispering Woods", game: "Whispering Woods", platform: "PC", score: 8.5, imageUrl: "https://picsum.photos/seed/article1/600/400", author: "Chris Lee", date: "Oct 25, 2023", slug: "indie-gem-whispering-woods", dataAiHint: "mystical forest", artScore: 8.0, gameplayScore: 8.8, storyScore: 8.6 },
    { id: '5', title: "Retro Rewind: Pixel Warriors", game: "Pixel Warriors", platform: "Multi", score: 7.9, imageUrl: "https://picsum.photos/seed/article2/600/400", author: "Pat Kim", date: "Oct 23, 2023", slug: "retro-rewind-pixel-warriors", dataAiHint: "pixel art", artScore: 8.5, gameplayScore: 7.5, storyScore: 7.8 },
    { id: '6', title: "VR Breakthrough: Reality Shift", game: "Reality Shift", platform: "VR", score: 9.0, imageUrl: "https://picsum.photos/seed/article3/600/400", author: "Samira Khan", date: "Oct 21, 2023", slug: "vr-breakthrough-reality-shift", dataAiHint: "virtual reality", artScore: 9.2, gameplayScore: 9.1, storyScore: 8.9 },
];


export default function Home() {
  const latestReview = featuredReviews[0]; // Assuming the first review is the latest
  const { user: currentUser, loading, setUser: setAuthContextUser } = useAuthContext();


  
  // Placeholder for profileData if needed on the homepage, assuming it might be fetched elsewhere or not needed here
  // For the purpose of this diff, we'll assume it's accessible if isOwnProfile is true
  const isOwnProfile = false; // Assuming this is not a profile page
  const profileData: any = null; // Placeholder - replace with actual profile data if used on homepage
  // Placeholder for isEditGameCardModalOpen and handleSaveGameCard if needed, assuming they're part of a profile page component

  const handleThemeChange = async (themeId: string) => {
    if (!isOwnProfile || !currentUser || !profileData) return;

    try {
      // PLACEHOLDER: Firebase updateUserProfileFields function removed
      // TODO: Replace with Supabase user profile update when ready

      // Update local state (if profileData is used and managed with useState on this page)
      // setProfileData(prev => prev ? { ...prev, theme: themeId } : null); // Uncomment if setProfileData is available

      // Update auth context if needed
      if (setAuthContextUser && currentUser.uid === profileData.uid) {
        setAuthContextUser(prev => (prev ? { ...prev, theme: themeId } : null));
      }
    } catch (error) {
      console.error("Failed to update theme:", error);
    }
  };

  return (
    <div className="space-y-12 w-full bp1360:w-4/5 bp1360:mx-auto px-4 sm:px-6 lg:px-8">


      {/* Latest Review Banner Section */}
      {latestReview && (
        <section className="relative rounded-lg overflow-hidden shadow-2xl glow-purple group">
          <Image 
            src={latestReview.imageUrl} 
            alt={`Banner image for ${latestReview.game}`} 
            width={1200} 
            height={500} 
            className="w-full h-[350px] md:h-[550px] object-cover group-hover:scale-105 transition-transform duration-500 ease-in-out"
            data-ai-hint={latestReview.dataAiHint || "gameplay scene"}
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/70 to-transparent flex flex-col items-start justify-end text-left p-6 md:p-10">
            <Badge variant="secondary" className="mb-2 bg-primary/80 text-primary-foreground border-primary-foreground/50">
              <Newspaper className="mr-1.5 h-4 w-4" /> Latest Review
            </Badge>
            <h1 className="text-3xl md:text-5xl font-bold text-primary-foreground mb-2 text-shadow-pixel-light">
              {latestReview.title}
            </h1>
            <p className="text-xl md:text-2xl text-accent font-semibold mb-3">{latestReview.game}</p>
            <p className="text-md md:text-lg text-primary-foreground/80 mb-6 max-w-2xl line-clamp-2">
              {latestReview.excerpt || "Read our latest in-depth analysis of this exciting title!"}
            </p>
            <Button size="lg" asChild className="bg-primary text-primary-foreground hover:bg-primary/90 glow-purple-sm">
              <Link href={`/reviews/${latestReview.slug}`}>Read Full Review <Rocket className="ml-2 h-5 w-5" /></Link>
            </Button>
          </div>
        </section>
      )}

      {/* Featured Reviews Section (excluding the latest one if it was part of featured) */}
      <section>
        <h2 className="text-3xl font-bold mb-6 text-primary flex items-center">
          <Star className="mr-2 h-7 w-7 text-accent" /> Featured Reviews
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredReviews.filter(review => review.id !== latestReview?.id).slice(0,3).map((review) => ( // Show up to 3 other featured
            <ReviewCard key={review.id} review={review} />
          ))}
        </div>
      </section>

      {/* Latest Articles Section */}
      <section>
        <h2 className="text-3xl font-bold mb-6 text-primary flex items-center">
          <Gamepad className="mr-2 h-7 w-7 text-accent" /> Latest Articles
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {latestArticles.map((article) => (
            <ReviewCard key={article.id} review={article} isArticle />
          ))}
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="bg-card p-8 rounded-lg shadow-lg text-center">
        <h2 className="text-3xl font-bold text-primary mb-4">Join the Conversation!</h2>
        <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
          Become part of the CriticalPixel community. Sign up to rate games, write your own reviews, and connect with fellow gamers.
        </p>
        <div className="space-x-4">
          <Button size="lg" asChild className="bg-primary text-primary-foreground hover:bg-primary/90 glow-purple">
            <Link href="/signup">Sign Up Now</Link>
          </Button>
          <Button size="lg" variant="outline" asChild className="border-primary text-primary hover:bg-primary/10 hover:text-primary">
            <Link href="/community">Explore Community</Link>
          </Button>
        </div>
      </section>

      {/*
        EditGameCardModal usage moved to a profile page component
        {isOwnProfile && profileData && (
          <EditGameCardModal
            isOpen={isEditGameCardModalOpen}
            onClose={() => setIsEditGameCardModalOpen(false)}
            profileData={profileData}
            onSave={handleSaveGameCard}
            onThemeChange={handleThemeChange} // Add this prop
          />
        )}*/}
    </div>
  );
}

const ReviewCard = ({ review, isArticle = false }: { review: ReviewTeaser, isArticle?: boolean }) => {
  const circleRef = useRef<SVGCircleElement>(null);
  const artBarRef = useRef<HTMLDivElement>(null);
  const gameplayBarRef = useRef<HTMLDivElement>(null);
  const storyBarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Animate the circle
    if (circleRef.current) {
      const radius = circleRef.current.r.baseVal.value;
      const circumference = 2 * Math.PI * radius;
      const offset = circumference - (review.score / 10) * circumference; // Assuming score is out of 10 for this calculation
      circleRef.current.style.strokeDasharray = `${circumference} ${circumference}`;
      circleRef.current.style.strokeDashoffset = `${circumference}`;
      circleRef.current.getBoundingClientRect(); // Trigger reflow
      circleRef.current.style.transition = 'stroke-dashoffset 1s ease-out';
      circleRef.current.style.strokeDashoffset = `${offset}`;
    }

    // Animate the bars
    if (artBarRef.current && review.artScore !== undefined) {
      artBarRef.current.style.width = '0%';
      artBarRef.current.getBoundingClientRect(); // Trigger reflow
      artBarRef.current.style.transition = 'width 1s ease-out';
      artBarRef.current.style.width = `${(review.artScore / 10) * 100}%`; // Assuming scores are out of 10
    }
    if (gameplayBarRef.current && review.gameplayScore !== undefined) {
      gameplayBarRef.current.style.width = '0%';
      gameplayBarRef.current.getBoundingClientRect(); // Trigger reflow
      gameplayBarRef.current.style.transition = 'width 1s ease-out';
      gameplayBarRef.current.style.width = `${(review.gameplayScore / 10) * 100}%`; // Assuming scores are out of 10
    }
    if (storyBarRef.current && review.storyScore !== undefined) {
      storyBarRef.current.style.width = '0%';
      storyBarRef.current.getBoundingClientRect(); // Trigger reflow
      storyBarRef.current.style.transition = 'width 1s ease-out';
      storyBarRef.current.style.width = `${(review.storyScore / 10) * 100}%`; // Assuming scores are out of 10
    }

  }, [review]);

  return (
    <Card className="overflow-hidden hover:shadow-xl transition-shadow duration-300 ease-in-out transform hover:-translate-y-1 glow-purple flex flex-col">
      <Link href={`/reviews/${review.slug}`} className="block flex flex-col flex-grow group">
        <div className="aspect-[3/2] overflow-hidden">
          <Image
            src={review.imageUrl}
            alt={`Cover art for ${review.game}`}
            width={600}
            height={400}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            data-ai-hint={review.dataAiHint || "game cover"}
          />
        </div>
        <CardHeader className="flex-grow">
          <CardTitle className="text-xl text-primary group-hover:text-accent transition-colors">{review.title}</CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            {review.game} - <span className="font-semibold text-accent">{review.platform}</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isArticle && (
            <div className="flex flex-col items-center mb-4">
              {/* Overall Score Circle Animation */}
              <div className="relative w-24 h-24 mb-4">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    className="text-muted-foreground/20 stroke-current"
                    strokeWidth="10"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                  />
                  <circle
                    ref={circleRef}
                    className="text-primary stroke-current"
                    strokeWidth="10"
                    strokeLinecap="round"
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary">{review.score.toFixed(1)}</span>
                </div>
              </div>

              {/* Category Score Bars */}
              <div className="w-full space-y-2">
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground w-20">Art:</span>
                  <div className="w-full bg-muted-foreground/20 rounded-full h-2.5">
                    <div ref={artBarRef} className="bg-blue-500 h-2.5 rounded-full" style={{ width: '0%' }}></div>
                  </div>
                   <span className="text-sm text-muted-foreground ml-2">{review.artScore?.toFixed(1) || 'N/A'}</span>
                </div>
                 <div className="flex items-center">
                  <span className="text-sm text-muted-foreground w-20">Gameplay:</span>
                  <div className="w-full bg-muted-foreground/20 rounded-full h-2.5">
                    <div ref={gameplayBarRef} className="bg-green-500 h-2.5 rounded-full" style={{ width: '0%' }}></div>
                  </div>
                  <span className="text-sm text-muted-foreground ml-2">{review.gameplayScore?.toFixed(1) || 'N/A'}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm text-muted-foreground w-20">Story:</span>
                  <div className="w-full bg-muted-foreground/20 rounded-full h-2.5">
                    <div ref={storyBarRef} className="bg-red-500 h-2.5 rounded-full" style={{ width: '0%' }}></div>
                  </div>
                   <span className="text-sm text-muted-foreground ml-2">{review.storyScore?.toFixed(1) || 'N/A'}</span>
                </div>
              </div>

            </div>
          )}
           <p className="text-xs text-muted-foreground">
            By {review.author} on {review.date}
          </p>
        </CardContent>
      </Link>
    </Card>
  );
};

