'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Eye, 
  EyeOff, 
  Star, 
  Target, 
  Trophy, 
  Image, 
  Activity,
  BarChart3,
  Save,
  RotateCcw,
  Palette,
  Users,
  Lock,
  Globe,
  ChevronUp,
  ChevronDown,
  GripVertical,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ContentModule, UserContentPreferences } from '@/types/user-content';

interface ContentModulesConfigProps {
  preferences: UserContentPreferences;
  onSave: (preferences: UserContentPreferences) => Promise<void>;
  isOwnProfile: boolean;
  theme?: any;
}

// Módulos disponíveis padrão
const defaultModules: ContentModule[] = [
  {
    id: 'stats',
    type: 'stats',
    title: 'Estatísticas',
    description: 'Resumo das suas atividades e conquistas',
    is_enabled: true,
    display_order: 1,
    visibility: 'public',
    settings: {
      show_stats: true,
      max_items: 4,
      allow_interactions: false
    }
  },
  {
    id: 'reviews',
    type: 'reviews',
    title: 'Reviews',
    description: 'Suas avaliações e críticas de jogos',
    is_enabled: true,
    display_order: 2,
    visibility: 'public',
    settings: {
      max_items: 10,
      show_stats: true,
      allow_interactions: true,
      auto_update: true
    }
  },
  {
    id: 'surveys',
    type: 'surveys',
    title: 'Surveys de Performance',
    description: 'Dados técnicos e benchmarks compartilhados',
    is_enabled: true,
    display_order: 3,
    visibility: 'public',
    settings: {
      max_items: 8,
      show_stats: true,
      allow_interactions: true
    }
  },
  {
    id: 'achievements',
    type: 'achievements',
    title: 'Conquistas',
    description: 'Badges e marcos desbloqueados',
    is_enabled: true,
    display_order: 4,
    visibility: 'public',
    settings: {
      max_items: 12,
      show_stats: false,
      allow_interactions: false
    }
  },
  {
    id: 'media',
    type: 'media',
    title: 'Galeria',
    description: 'Screenshots e vídeos dos seus jogos',
    is_enabled: true,
    display_order: 5,
    visibility: 'public',
    settings: {
      max_items: 12,
      show_stats: false,
      allow_interactions: true
    }
  },
  {
    id: 'activity',
    type: 'activity',
    title: 'Atividade Recente',
    description: 'Histórico das suas ações na plataforma',
    is_enabled: true,
    display_order: 6,
    visibility: 'public',
    settings: {
      max_items: 10,
      show_stats: false,
      allow_interactions: false
    }
  }
];

// Ícones para cada tipo de módulo
const moduleIcons = {
  stats: BarChart3,
  reviews: Star,
  surveys: Target,
  achievements: Trophy,
  media: Image,
  activity: Activity
};

// Componente de configuração individual do módulo
const ModuleConfigCard = ({ 
  module, 
  onUpdate, 
  theme, 
  canMoveUp, 
  canMoveDown, 
  onMoveUp, 
  onMoveDown 
}: {
  module: ContentModule;
  onUpdate: (module: ContentModule) => void;
  theme: any;
  canMoveUp: boolean;
  canMoveDown: boolean;
  onMoveUp: () => void;
  onMoveDown: () => void;
}) => {
  const Icon = moduleIcons[module.type];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="group"
    >
      <Card className={cn(
        "border transition-all duration-200",
        module.is_enabled 
          ? "border-gray-700 bg-gray-900/50" 
          : "border-gray-800 bg-gray-900/20 opacity-60"
      )}>
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            {/* Drag handle e ícone */}
            <div className="flex flex-col items-center gap-2">
              <div className="flex flex-col">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onMoveUp}
                  disabled={!canMoveUp}
                  className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                >
                  <ChevronUp className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onMoveDown}
                  disabled={!canMoveDown}
                  className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                >
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </div>
              
              <div 
                className="p-2 rounded-lg"
                style={{ backgroundColor: `${theme?.colors?.primary}20` }}
              >
                <Icon className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
              </div>
            </div>

            {/* Conteúdo principal */}
            <div className="flex-1 space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-white">{module.title}</h3>
                    <Badge variant={module.is_enabled ? 'default' : 'secondary'}>
                      {module.is_enabled ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-400 mt-1">{module.description}</p>
                </div>

                <Switch
                  checked={module.is_enabled}
                  onCheckedChange={(enabled) => onUpdate({ ...module, is_enabled: enabled })}
                />
              </div>

              {/* Configurações avançadas - apenas se ativo */}
              {module.is_enabled && (
                <div className="space-y-3 pt-2 border-t border-gray-800">
                  {/* Visibilidade */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-300">Visibilidade:</span>
                      <Info className="h-3 w-3 text-gray-500" />
                    </div>
                    <select
                      value={module.visibility}
                      onChange={(e) => onUpdate({ 
                        ...module, 
                        visibility: e.target.value as 'public' | 'friends' | 'private' 
                      })}
                      aria-label={`Configurar visibilidade do módulo ${module.title}`}
                      className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white"
                    >
                      <option value="public">
                        <Globe className="h-3 w-3 inline mr-1" />
                        Público
                      </option>
                      <option value="friends">
                        <Users className="h-3 w-3 inline mr-1" />
                        Amigos
                      </option>
                      <option value="private">
                        <Lock className="h-3 w-3 inline mr-1" />
                        Privado
                      </option>
                    </select>
                  </div>

                  {/* Configurações específicas */}
                  {module.settings?.max_items && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Máximo de itens:</span>
                                              <input
                          type="number"
                          min="1"
                          max="50"
                          value={module.settings.max_items}
                          onChange={(e) => onUpdate({
                            ...module,
                            settings: {
                              ...module.settings,
                              max_items: parseInt(e.target.value) || 1
                            }
                          })}
                          aria-label={`Máximo de itens para ${module.title}`}
                          className="w-16 bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white text-center"
                        />
                    </div>
                  )}

                  {/* Interações */}
                  {module.settings?.allow_interactions !== undefined && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Permitir interações:</span>
                      <Switch
                        checked={module.settings.allow_interactions}
                        onCheckedChange={(enabled) => onUpdate({
                          ...module,
                          settings: {
                            ...module.settings,
                            allow_interactions: enabled
                          }
                        })}
                      />
                    </div>
                  )}

                  {/* Mostrar estatísticas */}
                  {module.settings?.show_stats !== undefined && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">Mostrar estatísticas:</span>
                      <Switch
                        checked={module.settings.show_stats}
                        onCheckedChange={(enabled) => onUpdate({
                          ...module,
                          settings: {
                            ...module.settings,
                            show_stats: enabled
                          }
                        })}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default function ContentModulesConfig({ 
  preferences, 
  onSave, 
  isOwnProfile, 
  theme 
}: ContentModulesConfigProps) {
  const [modules, setModules] = useState<ContentModule[]>(
    preferences.enabled_modules.length > 0 ? preferences.enabled_modules : defaultModules
  );
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Detectar mudanças
  useEffect(() => {
    const hasModuleChanges = JSON.stringify(modules) !== JSON.stringify(preferences.enabled_modules);
    setHasChanges(hasModuleChanges);
  }, [modules, preferences.enabled_modules]);

  const updateModule = (updatedModule: ContentModule) => {
    setModules(prev => prev.map(module => 
      module.id === updatedModule.id ? updatedModule : module
    ));
  };

  const moveModule = (moduleId: string, direction: 'up' | 'down') => {
    setModules(prev => {
      const index = prev.findIndex(m => m.id === moduleId);
      if (index === -1) return prev;

      const newModules = [...prev];
      const targetIndex = direction === 'up' ? index - 1 : index + 1;

      if (targetIndex < 0 || targetIndex >= newModules.length) return prev;

      // Trocar posições
      [newModules[index], newModules[targetIndex]] = [newModules[targetIndex], newModules[index]];
      
      // Atualizar display_order
      newModules.forEach((module, idx) => {
        module.display_order = idx + 1;
      });

      return newModules;
    });
  };

  const handleSave = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      const updatedPreferences: UserContentPreferences = {
        ...preferences,
        enabled_modules: modules,
        updated_at: new Date().toISOString()
      };

      await onSave(updatedPreferences);
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setModules(defaultModules);
  };

  if (!isOwnProfile) {
    return (
      <Card className="bg-gray-900/50 border-gray-800">
        <CardContent className="p-8 text-center">
          <Lock className="h-12 w-12 mx-auto mb-4 text-gray-500" />
          <p className="text-gray-400">
            Apenas o proprietário do perfil pode configurar os módulos de conteúdo.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div 
            className="p-2 rounded-lg"
            style={{ backgroundColor: `${theme?.colors?.primary}20` }}
          >
            <Settings className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white font-mono">
              <span style={{ color: theme?.colors?.accent }}>&lt;</span>
              Configurar Módulos
              <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
            </h2>
            <p className="text-sm text-gray-400">
              Personalize quais seções aparecem no seu perfil
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isSaving}
            className="border-gray-700 hover:bg-gray-800"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Restaurar Padrão
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </div>
      </div>

      {/* Módulos configuráveis */}
      <div className="space-y-4">
        {modules.map((module, index) => (
          <ModuleConfigCard
            key={module.id}
            module={module}
            onUpdate={updateModule}
            theme={theme}
            canMoveUp={index > 0}
            canMoveDown={index < modules.length - 1}
            onMoveUp={() => moveModule(module.id, 'up')}
            onMoveDown={() => moveModule(module.id, 'down')}
          />
        ))}
      </div>

      {/* Informações adicionais */}
      <Card className="bg-gray-900/30 border-gray-800">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-400 mt-0.5" />
            <div className="space-y-2">
              <h3 className="font-semibold text-white">Dicas de Configuração</h3>
              <ul className="text-sm text-gray-400 space-y-1">
                <li>• Use as setas para reordenar os módulos na ordem desejada</li>
                <li>• Módulos inativos não aparecem no seu perfil</li>
                <li>• Configurações de visibilidade controlam quem pode ver cada seção</li>
                <li>• Alterações são salvas automaticamente quando você clica em "Salvar"</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Indicador de mudanças */}
      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-4 right-4 z-50"
        >
          <Card className="bg-orange-900/90 border-orange-600 backdrop-blur-sm">
            <CardContent className="p-3">
              <div className="flex items-center gap-2 text-orange-200">
                <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Alterações não salvas</span>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
} 