'use client';

import { AdminLayout } from '@/components/admin/AdminLayout';
import { useState, useEffect } from 'react';
import { useAuthContext } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  Activity, 
  Settings, 
  Play, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  RefreshCw,
  Server,
  HardDrive,
  Cpu,
  Users,
  FileText,
  MessageSquare,
  TrendingUp
} from 'lucide-react';
import {
  DatabaseHealth,
  SystemMetrics,
  SystemConfiguration,
  MaintenanceTask,
  getDatabaseHealth,
  getSystemMetrics,
  getSystemConfiguration,
  updateSystemConfiguration,
  getMaintenanceTasks,
  runMaintenanceTask
} from '@/lib/admin/systemService';

export default function SystemAdministrationPage() {
  const { user, isAdmin } = useAuthContext();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dbHealth, setDbHealth] = useState<DatabaseHealth | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [systemConfig, setSystemConfig] = useState<SystemConfiguration | null>(null);
  const [maintenanceTasks, setMaintenanceTasks] = useState<MaintenanceTask[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Load system data on user change
  useEffect(() => {
    if (user?.id && isAdmin) {
      loadSystemData();
    }
  }, [user?.id, isAdmin]);

  const loadSystemData = async () => {
    if (!user?.id) return;
    
    try {
      setError(null);
      const [health, metrics, config, tasks] = await Promise.all([
        getDatabaseHealth(user.id),
        getSystemMetrics(user.id),
        getSystemConfiguration(user.id),
        getMaintenanceTasks(user.id)
      ]);
      
      setDbHealth(health);
      setSystemMetrics(metrics);
      setSystemConfig(config);
      setMaintenanceTasks(tasks);
    } catch (err) {
      console.error('Error loading system data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load system data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSystemData();
  };

  const handleConfigUpdate = async (updates: Partial<SystemConfiguration>) => {
    if (!user?.id || !systemConfig) return;
    
    try {
      const updatedConfig = await updateSystemConfiguration(user.id, updates);
      setSystemConfig(updatedConfig);
    } catch (err) {
      console.error('Error updating configuration:', err);
      setError(err instanceof Error ? err.message : 'Failed to update configuration');
    }
  };

  const handleRunTask = async (taskId: string) => {
    if (!user?.id) return;
    
    try {
      await runMaintenanceTask(user.id, taskId);
      // Refresh tasks to show updated status
      const updatedTasks = await getMaintenanceTasks(user.id);
      setMaintenanceTasks(updatedTasks);
    } catch (err) {
      console.error('Error running maintenance task:', err);
      setError(err instanceof Error ? err.message : 'Failed to run maintenance task');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
      case 'pending':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'critical':
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'completed':
        return 'bg-green-500';
      case 'warning':
      case 'pending':
        return 'bg-yellow-500';
      case 'critical':
      case 'failed':
        return 'bg-red-500';
      case 'running':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (error) {
    return (
      <AdminLayout
        title="System Administration"
        description="Monitor and manage system health, configuration, and maintenance"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'System Administration' }
        ]}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <XCircle className="h-12 w-12 text-red-500" />
          <h2 className="text-xl font-semibold text-red-600">System Error</h2>
          <p className="text-muted-foreground text-center max-w-md">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="System Administration"
      description="Monitor and manage system health, configuration, and maintenance"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'System Administration' }
      ]}
    >
      <div className="flex flex-col md:flex-row justify-end items-center gap-4 mb-6">
        <Button onClick={handleRefresh} disabled={refreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh Data
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* System Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics?.totalUsers.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  {systemMetrics?.activeUsers} active in last 30 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics?.totalReviews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Published content
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Response Time</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics?.avgResponseTime.toFixed(0)}ms</div>
                <p className="text-xs text-muted-foreground">
                  Average response time
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics?.uptime} days</div>
                <p className="text-xs text-muted-foreground">
                  Error rate: {systemMetrics?.errorRate.toFixed(2)}%
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Database Health Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Health
                {getStatusIcon(dbHealth?.status || 'unknown')}
              </CardTitle>
              <CardDescription>
                Current database performance and resource utilization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Connections</span>
                    <span>{dbHealth?.connections.active}/{dbHealth?.connections.max}</span>
                  </div>
                  <Progress value={dbHealth?.connections.percentage} className="h-2" />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Storage</span>
                    <span>{dbHealth?.storage.used}MB / {dbHealth?.storage.total}MB</span>
                  </div>
                  <Progress value={dbHealth?.storage.percentage} className="h-2" />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Cache Hit Ratio</span>
                    <span>{dbHealth?.performance.cacheHitRatio.toFixed(1)}%</span>
                  </div>
                  <Progress value={dbHealth?.performance.cacheHitRatio} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Health Status
                <Badge variant={dbHealth?.status === 'healthy' ? 'default' : 
                              dbHealth?.status === 'warning' ? 'secondary' : 'destructive'}>
                  {dbHealth?.status}
                </Badge>
              </CardTitle>
              <CardDescription>
                Detailed database performance metrics and health indicators
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Connection Metrics */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Connection Pool
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Active Connections</div>
                    <div className="text-2xl font-bold">{dbHealth?.connections.active}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Max Connections</div>
                    <div className="text-2xl font-bold">{dbHealth?.connections.max}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Utilization</div>
                    <div className="text-2xl font-bold">{dbHealth?.connections.percentage}%</div>
                  </div>
                </div>
                <Progress value={dbHealth?.connections.percentage} className="h-3" />
              </div>

              {/* Performance Metrics */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  Performance
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Avg Query Time</div>
                    <div className="text-2xl font-bold">{dbHealth?.performance.avgQueryTime.toFixed(1)}ms</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Slow Queries</div>
                    <div className="text-2xl font-bold">{dbHealth?.performance.slowQueries}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Cache Hit Ratio</div>
                    <div className="text-2xl font-bold">{dbHealth?.performance.cacheHitRatio.toFixed(1)}%</div>
                  </div>
                </div>
              </div>

              {/* Storage Metrics */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Storage
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Used Space</div>
                    <div className="text-2xl font-bold">{dbHealth?.storage.used} MB</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Total Space</div>
                    <div className="text-2xl font-bold">{dbHealth?.storage.total} MB</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Utilization</div>
                    <div className="text-2xl font-bold">{dbHealth?.storage.percentage}%</div>
                  </div>
                </div>
                <Progress value={dbHealth?.storage.percentage} className="h-3" />
              </div>

              <div className="text-xs text-muted-foreground">
                Last checked: {dbHealth?.lastChecked ? new Date(dbHealth.lastChecked).toLocaleString() : 'Never'}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Configuration
              </CardTitle>
              <CardDescription>
                Manage global system settings and features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Site Settings */}
              <div className="space-y-4">
                <h4 className="font-medium">Site Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">Site Name</Label>
                    <Input
                      id="siteName"
                      value={systemConfig?.siteName || ''}
                      onChange={(e) => handleConfigUpdate({ siteName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="siteDescription">Site Description</Label>
                    <Input
                      id="siteDescription"
                      value={systemConfig?.siteDescription || ''}
                      onChange={(e) => handleConfigUpdate({ siteDescription: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              {/* Feature Toggles */}
              <div className="space-y-4">
                <h4 className="font-medium">Feature Settings</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Maintenance Mode</Label>
                      <div className="text-sm text-muted-foreground">
                        Temporarily disable site access for maintenance
                      </div>
                    </div>
                    <Switch
                      checked={systemConfig?.maintenanceMode || false}
                      onCheckedChange={(checked) => handleConfigUpdate({ maintenanceMode: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>User Registration</Label>
                      <div className="text-sm text-muted-foreground">
                        Allow new users to register accounts
                      </div>
                    </div>
                    <Switch
                      checked={systemConfig?.registrationEnabled || false}
                      onCheckedChange={(checked) => handleConfigUpdate({ registrationEnabled: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Review Approval Required</Label>
                      <div className="text-sm text-muted-foreground">
                        Require admin approval before reviews are published
                      </div>
                    </div>
                    <Switch
                      checked={systemConfig?.reviewApprovalRequired || false}
                      onCheckedChange={(checked) => handleConfigUpdate({ reviewApprovalRequired: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Comment Moderation</Label>
                      <div className="text-sm text-muted-foreground">
                        Enable automatic comment moderation
                      </div>
                    </div>
                    <Switch
                      checked={systemConfig?.commentModerationEnabled || false}
                      onCheckedChange={(checked) => handleConfigUpdate({ commentModerationEnabled: checked })}
                    />
                  </div>
                </div>
              </div>

              {/* Performance Settings */}
              <div className="space-y-4">
                <h4 className="font-medium">Performance Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxFileSize">Max File Upload Size (MB)</Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={systemConfig?.maxFileUploadSize || 0}
                      onChange={(e) => handleConfigUpdate({ maxFileUploadSize: parseInt(e.target.value) })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={systemConfig?.sessionTimeout || 0}
                      onChange={(e) => handleConfigUpdate({ sessionTimeout: parseInt(e.target.value) })}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Maintenance Tasks
              </CardTitle>
              <CardDescription>
                Scheduled and on-demand system maintenance operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{task.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {task.type}
                        </Badge>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(task.status)}
                          <span className="text-sm capitalize">{task.status}</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">{task.description}</p>
                      {task.lastRun && (
                        <p className="text-xs text-muted-foreground">
                          Last run: {new Date(task.lastRun).toLocaleString()}
                          {task.duration && ` (${Math.round(task.duration / 1000)}s)`}
                          {task.result && ` - ${task.result}`}
                        </p>
                      )}
                      {task.nextRun && (
                        <p className="text-xs text-muted-foreground">
                          Next run: {new Date(task.nextRun).toLocaleString()}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRunTask(task.id)}
                        disabled={task.status === 'running'}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Run Now
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
}
