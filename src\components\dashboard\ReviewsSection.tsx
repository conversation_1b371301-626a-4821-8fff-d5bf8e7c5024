'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Plus,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Grid3X3,
  List,
  Calendar,
  Star,
  Eye,
  Edit,
  Trash2,
  Share2,
  Co<PERSON>,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { ReviewCard } from './ReviewCard';
import { ReviewFilters } from './ReviewFilters';
import type { Review } from '@/lib/types';
import type { DashboardFilters } from '@/types/dashboard';

export interface ReviewsSectionProps {
  reviews: Review[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

export function ReviewsSection({ 
  reviews, 
  isLoading = false,
  onRefresh 
}: ReviewsSectionProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<DashboardFilters['reviews']>({
    status: 'all',
    sortBy: 'publishDate',
    sortOrder: 'desc'
  });

  // Filter and search logic
  const filteredReviews = useMemo(() => {
    // Ensure reviews is an array to prevent undefined errors
    const safeReviews = reviews || [];
    let filtered = safeReviews.filter(r => !r.is_blocked);

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(review =>
        review.title.toLowerCase().includes(query) ||
        review.gameName.toLowerCase().includes(query) ||
        review.platforms?.some(platform => platform.toLowerCase().includes(query)) ||
        review.genres?.some(genre => genre.toLowerCase().includes(query))
      );
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(review => {
        if (filters.status === 'published') {
          return review.status === 'published' || (!review.status && review.publishDate);
        }
        return review.status === filters.status;
      });
    }

    // Apply platform filter
    if (filters.platform) {
      filtered = filtered.filter(review =>
        review.platforms?.includes(filters.platform!)
      );
    }

    // Apply genre filter
    if (filters.genre) {
      filtered = filtered.filter(review =>
        review.genres?.includes(filters.genre!)
      );
    }

    // Apply score range filter
    if (filters.scoreRange) {
      const [min, max] = filters.scoreRange;
      filtered = filtered.filter(review =>
        review.overallScore >= min && review.overallScore <= max
      );
    }

    // Apply date range filter
    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      filtered = filtered.filter(review => {
        const reviewDate = review.publishDate || review.createdAt;
        if (!reviewDate) return false;
        const date = new Date(reviewDate);
        return date >= startDate && date <= endDate;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (filters.sortBy) {
        case 'publishDate':
          aValue = a.publishDate ? new Date(a.publishDate).getTime() : 0;
          bValue = b.publishDate ? new Date(b.publishDate).getTime() : 0;
          break;
        case 'createdAt':
          aValue = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          bValue = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'overallScore':
          aValue = a.overallScore || 0;
          bValue = b.overallScore || 0;
          break;
        default:
          aValue = a.publishDate ? new Date(a.publishDate).getTime() : 0;
          bValue = b.publishDate ? new Date(b.publishDate).getTime() : 0;
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [reviews, searchQuery, filters]);

  // Get unique platforms and genres for filter options
  const availableOptions = useMemo(() => {
    const platforms = new Set<string>();
    const genres = new Set<string>();

    // Ensure reviews is an array to prevent undefined errors
    const safeReviews = reviews || [];
    safeReviews.forEach(review => {
      review.platforms?.forEach(platform => platforms.add(platform));
      review.genres?.forEach(genre => genres.add(genre));
    });

    return {
      platforms: Array.from(platforms).sort(),
      genres: Array.from(genres).sort()
    };
  }, [reviews]);

  const handleFilterChange = (newFilters: Partial<DashboardFilters['reviews']>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleClearFilters = () => {
    setFilters({
      status: 'all',
      sortBy: 'publishDate',
      sortOrder: 'desc'
    });
    setSearchQuery('');
  };

  const toggleSortOrder = () => {
    setFilters(prev => ({
      ...prev,
      sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-6">
            <div className="h-6 bg-slate-700 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-slate-700 rounded w-3/4"></div>
              <div className="h-4 bg-slate-700 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-lg font-semibold text-slate-200 flex items-center gap-2">
            <FileText className="text-blue-400" size={20} />
            Your Reviews
            <span className="text-sm font-normal text-slate-400">
              ({filteredReviews.length} of {(reviews || []).length})
            </span>
          </h2>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-purple-600/20 border-purple-500/50' : ''}
          >
            <Filter size={16} className="mr-2" />
            Filters
          </Button>
          
          <div className="flex items-center border border-slate-700/50 rounded-lg">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('grid')}
              className={`rounded-r-none ${viewMode === 'grid' ? 'bg-purple-600/20' : ''}`}
            >
              <Grid3X3 size={16} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('list')}
              className={`rounded-l-none ${viewMode === 'list' ? 'bg-purple-600/20' : ''}`}
            >
              <List size={16} />
            </Button>
          </div>

          <Button asChild className="bg-purple-600 hover:bg-purple-700">
            <Link href="/reviews/create">
              <Plus size={16} className="mr-2" />
              Create Review
            </Link>
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
        <Input
          placeholder="Search reviews by title, game, platform, or genre..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 bg-slate-900/60 border-slate-700/50 text-slate-200 placeholder-slate-400"
        />
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <ReviewFilters
              filters={filters}
              availableOptions={availableOptions}
              onFilterChange={handleFilterChange}
              onClearFilters={handleClearFilters}
              onToggleSortOrder={toggleSortOrder}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sort Controls */}
      <div className="flex items-center gap-2 text-sm text-slate-400">
        <span>Sort by:</span>
        <select
          value={filters.sortBy}
          onChange={(e) => handleFilterChange({ sortBy: e.target.value as any })}
          className="bg-slate-900/60 border border-slate-700/50 rounded px-2 py-1 text-slate-200"
        >
          <option value="publishDate">Publish Date</option>
          <option value="createdAt">Created Date</option>
          <option value="title">Title</option>
          <option value="overallScore">Score</option>
        </select>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSortOrder}
          className="p-1"
        >
          {filters.sortOrder === 'asc' ? (
            <SortAsc size={16} />
          ) : (
            <SortDesc size={16} />
          )}
        </Button>
      </div>

      {/* Reviews Grid/List */}
      {filteredReviews.length === 0 ? (
        <div className="bg-slate-900/60 border border-slate-700/50 rounded-lg p-8 text-center">
          <FileText className="mx-auto text-slate-500 mb-4" size={48} />
          <h3 className="text-slate-200 font-medium mb-2">
            {(reviews || []).length === 0 ? 'No reviews yet' : 'No reviews match your filters'}
          </h3>
          <p className="text-slate-400 mb-4">
            {(reviews || []).length === 0
              ? 'Start sharing your gaming experiences with the community'
              : 'Try adjusting your search or filter criteria'
            }
          </p>
          {(reviews || []).length === 0 ? (
            <Button asChild className="bg-purple-600 hover:bg-purple-700">
              <Link href="/reviews/create">Create Your First Review</Link>
            </Button>
          ) : (
            <Button onClick={handleClearFilters} variant="outline">
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <motion.div
          layout
          className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }
        >
          <AnimatePresence mode="popLayout">
            {filteredReviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
                viewMode={viewMode}
                onRefresh={onRefresh}
              />
            ))}
          </AnimatePresence>
        </motion.div>
      )}
    </div>
  );
}
