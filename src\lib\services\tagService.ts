// src/lib/services/tagService.ts
// Comprehensive Tag Management Service
// Handles CRUD operations, search, suggestions, and analytics for the tag system

import { createClient } from '@/lib/supabase/client';
import { createServerClient } from '@/lib/supabase/server';
import type { Database } from '@/lib/supabase/types';

// Type definitions
export type Tag = Database['public']['Tables']['tags']['Row'];
export type TagInsert = Database['public']['Tables']['tags']['Insert'];
export type TagUpdate = Database['public']['Tables']['tags']['Update'];
export type ReviewTag = Database['public']['Tables']['review_tags']['Row'];
export type TrendingTag = Database['public']['Views']['trending_tags']['Row'];
export type TagSuggestion = Database['public']['Views']['tag_suggestions']['Row'];

export interface TagSearchOptions {
  query?: string;
  category?: string;
  status?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'usage_count' | 'trend_score' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

export interface TagCreateInput {
  name: string;
  description?: string;
  category?: string;
  is_featured?: boolean;
}

export interface TagAnalyticsData {
  tag_id: string;
  views: number;
  clicks: number;
  searches: number;
  new_usages: number;
  avg_time_on_page?: number;
  bounce_rate?: number;
}

// Utility functions
export function generateTagSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

export function categorizeTag(name: string): string {
  const genres = ['action', 'rpg', 'strategy', 'adventure', 'simulation', 'horror', 'puzzle', 'sports', 'racing', 'fighting', 'platformer', 'shooter', 'mmo', 'moba', 'battle royale'];
  const platforms = ['pc', 'playstation', 'xbox', 'nintendo', 'switch', 'steam', 'mobile', 'vr', 'ios', 'android'];
  const features = ['multiplayer', 'single player', 'co-op', 'pvp', 'open world', 'story rich', 'crafting', 'survival', 'sandbox', 'roguelike', 'metroidvania'];
  const moods = ['relaxing', 'intense', 'competitive', 'atmospheric', 'dark', 'colorful', 'retro', 'realistic'];
  const difficulty = ['easy', 'challenging', 'hardcore', 'casual', 'difficult'];
  const length = ['short', 'medium', 'long', 'endless', 'quick', 'epic'];

  const lowerName = name.toLowerCase();

  if (genres.some(genre => lowerName.includes(genre))) return 'genre';
  if (platforms.some(platform => lowerName.includes(platform))) return 'platform';
  if (features.some(feature => lowerName.includes(feature))) return 'feature';
  if (moods.some(mood => lowerName.includes(mood))) return 'mood';
  if (difficulty.some(diff => lowerName.includes(diff))) return 'difficulty';
  if (length.some(len => lowerName.includes(len))) return 'length';

  return 'custom';
}

class TagService {
  private supabasePromise;
  private isServer: boolean;

  constructor(isServer = false) {
    this.isServer = isServer;
    this.supabasePromise = isServer ? createServerClient() : Promise.resolve(createClient());
  }

  private async getSupabase() {
    return await this.supabasePromise;
  }

  // CRUD Operations
  async createTag(input: TagCreateInput, userId?: string): Promise<{ success: boolean; tag?: Tag; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const slug = generateTagSlug(input.name);
      const category = input.category || categorizeTag(input.name);

      const tagData: TagInsert = {
        name: input.name.trim(),
        slug,
        description: input.description?.trim() || null,
        category,
        is_featured: input.is_featured || false,
        created_by: userId || null,
        status: 'active'
      };

      const { data, error } = await supabase
        .from('tags')
        .insert(tagData)
        .select()
        .single();

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          return { success: false, error: 'Tag already exists' };
        }
        throw error;
      }

      return { success: true, tag: data };
    } catch (error) {
      console.error('Error creating tag:', error);
      return { success: false, error: 'Failed to create tag' };
    }
  }

  async getTagById(id: string): Promise<{ success: boolean; tag?: Tag; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, error: 'Tag not found' };
        }
        throw error;
      }

      return { success: true, tag: data };
    } catch (error) {
      console.error('Error fetching tag:', error);
      return { success: false, error: 'Failed to fetch tag' };
    }
  }

  async getTagBySlug(slug: string): Promise<{ success: boolean; tag?: Tag; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'active')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, error: 'Tag not found' };
        }
        throw error;
      }

      return { success: true, tag: data };
    } catch (error) {
      console.error('Error fetching tag by slug:', error);
      return { success: false, error: 'Failed to fetch tag' };
    }
  }

  async searchTags(options: TagSearchOptions = {}): Promise<{ success: boolean; tags?: Tag[]; total?: number; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      let query = supabase
        .from('tags')
        .select('*', { count: 'exact' });

      // Apply filters
      if (options.query) {
        query = query.or(`name.ilike.%${options.query}%,description.ilike.%${options.query}%`);
      }

      if (options.category) {
        query = query.eq('category', options.category);
      }

      if (options.status) {
        query = query.eq('status', options.status);
      } else {
        query = query.eq('status', 'active'); // Default to active tags only
      }

      // Apply sorting
      const sortBy = options.sortBy || 'usage_count';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }

      const { data, error, count } = await query;

      if (error) throw error;

      return { success: true, tags: data || [], total: count || 0 };
    } catch (error) {
      console.error('Error searching tags:', error);
      return { success: false, error: 'Failed to search tags' };
    }
  }

  async updateTag(id: string, updates: TagUpdate): Promise<{ success: boolean; tag?: Tag; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      // If name is being updated, regenerate slug
      if (updates.name) {
        updates.slug = generateTagSlug(updates.name);
      }

      const { data, error } = await supabase
        .from('tags')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        if (error.code === '23505') {
          return { success: false, error: 'Tag name or slug already exists' };
        }
        throw error;
      }

      return { success: true, tag: data };
    } catch (error) {
      console.error('Error updating tag:', error);
      return { success: false, error: 'Failed to update tag' };
    }
  }

  async deleteTag(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      // Soft delete by setting status to 'archived'
      const { error } = await supabase
        .from('tags')
        .update({ status: 'archived' })
        .eq('id', id);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error deleting tag:', error);
      return { success: false, error: 'Failed to delete tag' };
    }
  }

  // Tag suggestions and autocomplete
  async getTagSuggestions(query: string, limit = 10): Promise<{ success: boolean; suggestions?: Tag[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('tags')
        .select('id, name, slug, category, usage_count')
        .eq('status', 'active')
        .ilike('name', `%${query}%`)
        .order('usage_count', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return { success: true, suggestions: data || [] };
    } catch (error) {
      console.error('Error getting tag suggestions:', error);
      return { success: false, error: 'Failed to get suggestions' };
    }
  }

  async getTagsByCategory(): Promise<{ success: boolean; categories?: TagSuggestion[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('tag_suggestions')
        .select('*');

      if (error) throw error;

      return { success: true, categories: data || [] };
    } catch (error) {
      console.error('Error getting tags by category:', error);
      return { success: false, error: 'Failed to get categories' };
    }
  }

  // Trending and popular tags
  async getTrendingTags(limit = 20): Promise<{ success: boolean; tags?: TrendingTag[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();

      // First try the trending_tags table
      const { data: trendingData, error: trendingError } = await supabase
        .from('trending_tags')
        .select('*')
        .eq('is_trending', true)
        .order('trend_score', { ascending: false })
        .limit(limit);

      // If trending_tags table doesn't exist or fails, fallback to tags table
      if (trendingError) {
        console.log('Trending tags table not available, using fallback...');
        
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('tags')
          .select('*')
          .order('usage_count', { ascending: false })
          .limit(limit);

        if (fallbackError) throw fallbackError;

        // Transform regular tags to trending format
        const trendingTags: TrendingTag[] = (fallbackData || []).map((tag, index) => ({
          id: tag.id,
          name: tag.name,
          slug: tag.slug,
          usage_count: tag.usage_count || 0,
          trend_score: Math.max(90 - (index * 10), 10), // Synthetic trend score
          is_trending: index < 5, // Mark first 5 as trending
          is_featured: tag.is_featured || false,
          review_count: tag.usage_count || 0,
          category: tag.category
        }));

        return { success: true, tags: trendingTags };
      }

      return { success: true, tags: trendingData || [] };
    } catch (error) {
      console.error('Error getting trending tags:', error);
      return { success: false, error: 'Failed to get trending tags' };
    }
  }

  async getFeaturedTags(limit = 10): Promise<{ success: boolean; tags?: Tag[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('status', 'active')
        .eq('is_featured', true)
        .order('usage_count', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return { success: true, tags: data || [] };
    } catch (error) {
      console.error('Error getting featured tags:', error);
      return { success: false, error: 'Failed to get featured tags' };
    }
  }

  async getPopularTags(limit = 50): Promise<{ success: boolean; tags?: TrendingTag[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('trending_tags')
        .select('*')
        .order('usage_count', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return { success: true, tags: data || [] };
    } catch (error) {
      console.error('Error getting popular tags:', error);
      return { success: false, error: 'Failed to get popular tags' };
    }
  }

  // Review-tag relationship management
  async addTagsToReview(reviewId: string, tagIds: string[]): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const reviewTags = tagIds.map(tagId => ({
        review_id: reviewId,
        tag_id: tagId
      }));

      const { error } = await supabase
        .from('review_tags')
        .insert(reviewTags);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error adding tags to review:', error);
      return { success: false, error: 'Failed to add tags to review' };
    }
  }

  async removeTagsFromReview(reviewId: string, tagIds?: string[]): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      let query = supabase
        .from('review_tags')
        .delete()
        .eq('review_id', reviewId);

      if (tagIds && tagIds.length > 0) {
        query = query.in('tag_id', tagIds);
      }

      const { error } = await query;

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error removing tags from review:', error);
      return { success: false, error: 'Failed to remove tags from review' };
    }
  }

  async getReviewTags(reviewId: string): Promise<{ success: boolean; tags?: Tag[]; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('review_tags')
        .select(`
          tag_id,
          tags:tag_id (*)
        `)
        .eq('review_id', reviewId);

      if (error) throw error;

      const tags = data?.map(rt => (rt as any).tags).filter(Boolean) || [];
      return { success: true, tags };
    } catch (error) {
      console.error('Error getting review tags:', error);
      return { success: false, error: 'Failed to get review tags' };
    }
  }

  async getTagReviews(tagId: string, limit = 20, offset = 0): Promise<{ success: boolean; reviews?: any[]; total?: number; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error, count } = await supabase
        .from('review_tags')
        .select(`
          review_id,
          reviews:review_id (
            id,
            title,
            slug,
            game_name,
            author_name,
            overall_score,
            main_image_url,
            created_at,
            view_count,
            like_count
          )
        `, { count: 'exact' })
        .eq('tag_id', tagId)
        .range(offset, offset + limit - 1);

      if (error) throw error;

      const reviews = data?.map(rt => (rt as any).reviews).filter(Boolean) || [];
      return { success: true, reviews, total: count || 0 };
    } catch (error) {
      console.error('Error getting tag reviews:', error);
      return { success: false, error: 'Failed to get tag reviews' };
    }
  }

  // Analytics and tracking
  async trackTagAnalytics(analyticsData: TagAnalyticsData): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .from('tag_analytics')
        .insert({
          tag_id: analyticsData.tag_id,
          views: analyticsData.views,
          clicks: analyticsData.clicks,
          searches: analyticsData.searches,
          new_usages: analyticsData.new_usages,
          avg_time_on_page: analyticsData.avg_time_on_page || 0,
          bounce_rate: analyticsData.bounce_rate || 0
        })
        .on_conflict('tag_id,date')
        .ignore();

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error tracking tag analytics:', error);
      return { success: false, error: 'Failed to track analytics' };
    }
  }

  async incrementTagView(tagId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .from('tags')
        .update({ view_count: supabase.sql`view_count + 1` })
        .eq('id', tagId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error incrementing tag view:', error);
      return { success: false, error: 'Failed to increment view' };
    }
  }

  async incrementTagClick(tagId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .from('tags')
        .update({ click_count: supabase.sql`click_count + 1` })
        .eq('id', tagId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error incrementing tag click:', error);
      return { success: false, error: 'Failed to increment click' };
    }
  }

  async incrementTagSearch(tagId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .from('tags')
        .update({ search_count: supabase.sql`search_count + 1` })
        .eq('id', tagId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error incrementing tag search:', error);
      return { success: false, error: 'Failed to increment search' };
    }
  }

  // Utility functions for tag management
  async findOrCreateTags(tagNames: string[], userId?: string): Promise<{ success: boolean; tags?: Tag[]; error?: string }> {
    try {
      const tags: Tag[] = [];
      
      for (const name of tagNames) {
        const trimmedName = name.trim();
        if (!trimmedName) continue;

        // Try to find existing tag
        const supabase = await this.getSupabase();
        const { data: existingTag } = await supabase
          .from('tags')
          .select('*')
          .eq('name', trimmedName)
          .single();

        if (existingTag) {
          tags.push(existingTag);
        } else {
          // Create new tag
          const createResult = await this.createTag({ name: trimmedName }, userId);
          if (createResult.success && createResult.tag) {
            tags.push(createResult.tag);
          }
        }
      }

      return { success: true, tags };
    } catch (error) {
      console.error('Error finding or creating tags:', error);
      return { success: false, error: 'Failed to process tags' };
    }
  }

  // Administrative functions
  async recalculateTrendScores(): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase.rpc('calculate_tag_trend_scores');
      
      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error recalculating trend scores:', error);
      return { success: false, error: 'Failed to recalculate trend scores' };
    }
  }

  async resetRecentUsage(): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase.rpc('reset_recent_tag_usage');
      
      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error resetting recent usage:', error);
      return { success: false, error: 'Failed to reset recent usage' };
    }
  }

  async migrateExistingTags(): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase.rpc('migrate_existing_review_tags');
      
      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error migrating existing tags:', error);
      return { success: false, error: 'Failed to migrate existing tags' };
    }
  }
}

// Export factory functions for client and server usage
export const createTagService = () => new TagService(false);
export const createServerTagService = () => new TagService(true);

// Export default instance for client-side usage
export default createTagService();