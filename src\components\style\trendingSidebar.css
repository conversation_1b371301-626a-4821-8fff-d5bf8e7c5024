/* Unified Trending Sidebar Styles */

/* Container */
.trending-sidebar-container {
    color: rgba(255, 255, 255, 0.7);
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 15px;
  }
  
  /* Header */
  .trending-sidebar-header {
    background: transparent;
    backdrop-filter: none;
    border-bottom: none;
    padding: 25px 0px 25px 0px;
    top: 0;
    z-index: 10;
  }
  
  .trending-sidebar-header-flex {
    display: flex;
    align-items: center;
  }
  
  .trending-sidebar-divider {
    height: 1px;
    background: rgba(100, 116, 139, 0.3);
    flex: 1;
  }
  
  .trending-sidebar-title {
    margin: 0 16px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgba(148, 163, 184, 1);
    transition: all 0.3s ease-out;
    font-weight: 600;
  }
  
  /* Main Container */
  .trending-sidebar-main-container {
    flex: 1;
    overflow: visible;
  }
  
  .trending-sidebar-list {
    padding: 8px 0;
    gap: 6px;
    display: flex;
    flex-direction: column;
  }
  
  /* Items */
  .trending-sidebar-item {
    display: block;
    padding: 2px 0;
    margin: 1px 0;
    transition: all 0.3s ease-out;
    transform: translateX(0);
    position: relative;
    border-radius: 8px;
    text-decoration: none;
  }
  
  .trending-sidebar-item-content {
    display: flex;
    align-items: center;
    min-width: 0;
    position: relative;
    z-index: 2;
  }
  
  .trending-sidebar-item-left {
    display: flex;
    align-items: center;
    gap: 20px;
    min-width: 0;
    flex-grow: 1;
  }
  
  /* Icon Container */
  .trending-sidebar-icon-container {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(139, 92, 246, 0.2);
    transition: all 0.3s ease-out;
    position: relative;
    overflow: hidden;
  }
  
  .trending-sidebar-item:hover .trending-sidebar-icon-container {
    transform: scale(1.1) rotate(3deg);
  }
  
  /* Avatar/Image/Icon Styles */
  .trending-sidebar-avatar {

    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease-out;
  }
  
  .trending-sidebar-cover {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease-out;
  }
  
  /* Icon styles for tag component */
  .trending-sidebar-icon {
    transition: all 0.3s ease-out;
    z-index: 2;
    position: relative;
  }
  
  /* Trend-specific icon colors */
  .trending-sidebar-icon.explosive {
    color: rgb(6, 182, 212);
    animation: trending-sidebar-pulse-electric 1.5s ease-in-out infinite;
  }
  
  .trending-sidebar-icon.hot {
    color: rgb(248, 113, 113);
    animation: trending-sidebar-pulse-fire 2s ease-in-out infinite;
  }
  
  .trending-sidebar-icon.rising {
    color: rgb(251, 191, 36);
    animation: trending-sidebar-bounce-subtle 2s ease-in-out infinite;
  }
  
  .trending-sidebar-icon.stable {
    color: rgb(148, 163, 184);
  }
  
  /* Badge */
  .trending-sidebar-badge {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: rgba(15, 23, 42, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(139, 92, 246, 0.4);
    transition: all 0.3s ease-out;
    z-index: 3;
  }
  
  .trending-sidebar-badge.legendary-glow,
  .trending-sidebar-badge.masterpiece-glow {
    animation: trending-sidebar-legendary-pulse 2s ease-in-out infinite;
  }
  
  /* Name/Title */
  .trending-sidebar-name {
    font-family: 'Courier New', monospace;
    font-size: 15px;
    font-weight: 600;
    color: rgba(226, 232, 240, 1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease-out;
    transform: translateX(0);
    flex: 1;
  }
  
  /* Progress Bar */
  .trending-sidebar-progress-container {
    width: 83%;
    height: 3px;
    background: #1e293bcc;
    border-radius: 9999px;
    overflow: hidden;
    margin-top: 6px;
    transition: all .3s ease-out;
    border: 1px solid #8b5cf61a;
    margin-left: auto;
    margin-right: auto;
  }
  
  .trending-sidebar-progress-bar {
    height: 100%;
    background: linear-gradient(to right, rgb(139, 92, 246), rgb(6, 182, 212));
    transition: all 0.7s cubic-bezier(0.68, -0.6, 0.32, 1.6);
    width: 0%;
    position: relative;
  }
  
  .trending-sidebar-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 20px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3));
    transform: translateX(100%);
    animation: trending-sidebar-shimmer 2s ease-in-out infinite;
  }
  
  /* Animations */
  @keyframes trending-sidebar-fade-slide-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes trending-sidebar-legendary-pulse {
    0%, 100% { 
      opacity: 0.6;
      transform: scale(1);
    }
    50% { 
      opacity: 1;
      transform: scale(1.05);
    }
  }
  
  @keyframes trending-sidebar-pulse-fire {
    0%, 100% { 
      opacity: 0.8;
      filter: brightness(1);
    }
    50% { 
      opacity: 1;
      filter: brightness(1.3);
    }
  }
  
  @keyframes trending-sidebar-pulse-electric {
    0%, 100% { 
      opacity: 0.7;
      filter: brightness(1) drop-shadow(0 0 2px currentColor);
    }
    50% { 
      opacity: 1;
      filter: brightness(1.4) drop-shadow(0 0 4px currentColor);
    }
  }
  
  @keyframes trending-sidebar-bounce-subtle {
    0%, 100% { 
      transform: translateY(0);
    }
    50% { 
      transform: translateY(-2px);
    }
  }
  
  @keyframes trending-sidebar-shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  /* Animation classes */
  .trending-sidebar-animate-fade-slide {
    animation: trending-sidebar-fade-slide-in 0.5s ease-out both;
  }
  
  /* Color variants for titles */
  .trending-sidebar-title-purple .trending-sidebar-title-accent {
    color: rgb(139, 92, 246);
  }
  
  .trending-sidebar-title-green .trending-sidebar-title-accent {
    color: rgb(34, 197, 94);
  }
  
  .trending-sidebar-title-cyan .trending-sidebar-title-accent {
    color: rgb(6, 182, 212);
  }