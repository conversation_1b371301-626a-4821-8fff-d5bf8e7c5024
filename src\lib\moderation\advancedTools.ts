// Advanced Moderation Tools
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import { createClient } from '@/lib/supabase/client';
import { SpamDetector } from '@/lib/security/spamDetection';
import { ContentFilter } from '@/lib/security/contentFilter';

export class AdvancedModerationTools {
  private supabase = createClient();

  // Bulk moderation actions
  async bulkModerateComments(
    commentIds: string[],
    action: 'approve' | 'reject' | 'delete',
    moderatorId: string,
    reason?: string
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    const updates: any = {
      moderated_by: moderatorId,
      moderated_at: new Date().toISOString(),
    };

    switch (action) {
      case 'approve':
        updates.is_approved = true;
        break;
      case 'reject':
        updates.is_approved = false;
        break;
      case 'delete':
        updates.is_deleted = true;
        break;
    }

    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < commentIds.length; i += batchSize) {
      const batch = commentIds.slice(i, i + batchSize);
      
      try {
        const { error } = await this.supabase
          .from('comments')
          .update(updates)
          .in('id', batch);

        if (error) {
          errors.push(`Batch ${i / batchSize + 1}: ${error.message}`);
          failed += batch.length;
        } else {
          success += batch.length;
          
          // Log bulk action for each comment
          await Promise.all(batch.map(async (id) => {
            try {
              await this.supabase.rpc('log_comment_action', {
                p_comment_id: id,
                p_action_type: action,
                p_reason: reason || `Bulk ${action}`,
              });
            } catch (logError) {
              console.warn('Failed to log action for comment:', id, logError);
            }
          }));
        }
      } catch (error) {
        errors.push(`Batch ${i / batchSize + 1}: ${error}`);
        failed += batch.length;
      }
    }

    return { success, failed, errors };
  }

  // Auto-moderation rules
  async applyAutoModerationRules(reviewId: string): Promise<{
    processed: number;
    approved: number;
    flagged: number;
    blocked: number;
  }> {
    const { data: settings } = await this.supabase
      .from('comment_moderation_settings')
      .select('*')
      .eq('review_id', reviewId)
      .single();

    if (!settings) {
      return { processed: 0, approved: 0, flagged: 0, blocked: 0 };
    }

    // Get pending comments
    const { data: comments } = await this.supabase
      .from('comments')
      .select('*')
      .eq('review_id', reviewId)
      .eq('is_approved', false)
      .eq('is_deleted', false);

    if (!comments) {
      return { processed: 0, approved: 0, flagged: 0, blocked: 0 };
    }

    const spamDetector = new SpamDetector();
    const contentFilter = new ContentFilter();
    
    let approved = 0;
    let flagged = 0;
    let blocked = 0;

    for (const comment of comments) {
      try {
        // Check spam
        const spamResult = await spamDetector.checkSpam(
          comment.content, 
          comment.author_id,
          comment.author_ip
        );
        
        // Check content
        const filterResult = await contentFilter.filterContent(comment.content);

        // Apply auto-moderation based on results
        if (spamResult.action === 'block' || filterResult.severity === 'high') {
          await this.supabase
            .from('comments')
            .update({
              is_deleted: true,
              moderated_by: 'system',
              moderated_at: new Date().toISOString(),
              moderation_notes: `Auto-deleted: ${spamResult.reasons.join(', ')}`,
            })
            .eq('id', comment.id);
          blocked++;
        } else if (spamResult.action === 'flag' || filterResult.severity === 'medium') {
          await this.supabase
            .from('comments')
            .update({
              flag_count: comment.flag_count + 1,
              moderation_notes: `Auto-flagged: ${spamResult.reasons.join(', ')}`,
              content: filterResult.filteredContent, // Use filtered content
            })
            .eq('id', comment.id);
          flagged++;
        } else if (settings.auto_approve) {
          await this.supabase
            .from('comments')
            .update({
              is_approved: true,
              content: filterResult.filteredContent, // Use filtered content
              moderated_by: 'system',
              moderated_at: new Date().toISOString(),
            })
            .eq('id', comment.id);
          approved++;
        }
      } catch (error) {
        console.error('Error processing comment for auto-moderation:', comment.id, error);
      }
    }

    return {
      processed: comments.length,
      approved,
      flagged,
      blocked,
    };
  }

  // Comment similarity detection (duplicate detection)
  async detectDuplicateComments(content: string, reviewId: string, authorId?: string): Promise<{
    isDuplicate: boolean;
    similarComments: Array<{ id: string; similarity: number; content: string }>;
  }> {
    const { data: recentComments } = await this.supabase
      .from('comments')
      .select('id, content, author_id')
      .eq('review_id', reviewId)
      .eq('is_deleted', false)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (!recentComments) return { isDuplicate: false, similarComments: [] };

    const similarComments: Array<{ id: string; similarity: number; content: string }> = [];
    const normalizedContent = content.toLowerCase().trim();

    for (const comment of recentComments) {
      // Skip if it's the same author (allow similar comments from same user)
      if (authorId && comment.author_id === authorId) continue;

      const normalizedExisting = comment.content.toLowerCase().trim();
      const similarity = this.calculateSimilarity(normalizedContent, normalizedExisting);
      
      if (similarity > 0.8) {
        similarComments.push({
          id: comment.id,
          similarity,
          content: comment.content,
        });
      }
    }

    return {
      isDuplicate: similarComments.length > 0,
      similarComments: similarComments.sort((a, b) => b.similarity - a.similarity),
    };
  }

  // Advanced comment filtering
  async getFilteredComments(filters: {
    reviewId?: string;
    authorId?: string;
    status?: 'pending' | 'approved' | 'flagged' | 'deleted';
    dateRange?: { start: Date; end: Date };
    contentSearch?: string;
    minFlagCount?: number;
    sortBy?: 'created_at' | 'flag_count' | 'upvotes';
    sortOrder?: 'asc' | 'desc';
    limit?: number;
    offset?: number;
  }) {
    let query = this.supabase
      .from('comments')
      .select(`
        *,
        author:profiles!author_id(id, username, display_name, avatar_url),
        review:reviews!review_id(id, title, slug)
      `);

    // Apply filters
    if (filters.reviewId) {
      query = query.eq('review_id', filters.reviewId);
    }

    if (filters.authorId) {
      query = query.eq('author_id', filters.authorId);
    }

    if (filters.status) {
      switch (filters.status) {
        case 'pending':
          query = query.eq('is_approved', false).eq('is_deleted', false);
          break;
        case 'approved':
          query = query.eq('is_approved', true).eq('is_deleted', false);
          break;
        case 'flagged':
          query = query.gt('flag_count', 0).eq('is_deleted', false);
          break;
        case 'deleted':
          query = query.eq('is_deleted', true);
          break;
      }
    }

    if (filters.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start.toISOString())
        .lte('created_at', filters.dateRange.end.toISOString());
    }

    if (filters.contentSearch) {
      query = query.ilike('content', `%${filters.contentSearch}%`);
    }

    if (filters.minFlagCount) {
      query = query.gte('flag_count', filters.minFlagCount);
    }

    // Apply sorting
    const sortBy = filters.sortBy || 'created_at';
    const sortOrder = filters.sortOrder || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    return query;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    );

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}
