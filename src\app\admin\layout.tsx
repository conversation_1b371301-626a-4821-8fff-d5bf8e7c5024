// Admin Layout Wrapper
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.2

import React from 'react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Admin Panel - Critical Pixel',
  description: 'Administrative interface for Critical Pixel website management.',
  robots: 'noindex, nofollow', // Prevent search engine indexing of admin pages
};

interface AdminLayoutWrapperProps {
  children: React.ReactNode;
}

export default function AdminLayoutWrapper({ children }: AdminLayoutWrapperProps) {
  return (
    <div className="admin-layout mt-[-3rem]">
      {children}
    </div>
  );
}
