# Steam API Implementation Checklist - FASE 1: Configuração e Fundamentos

## ✅ INSTRUÇÕES IMPORTANTES PARA A IA

**Ao implementar cada item desta checklist:**
1. ✅ **SEMPRE comente o código** explicando o que cada parte faz
2. ✅ **Documente as decisões** técnicas tomadas
3. ✅ **Explique o propósito** de cada arquivo/função criada
4. ✅ **Mencione dependências** e configurações necessárias
5. ✅ **Indique próximos passos** após cada implementação

---

## 📋 CHECKLIST FASE 1: CONFIGURAÇÃO E FUNDAMENTOS

### 🔧 1. CONFIGURAÇÃO INICIAL

#### ☐ 1.1 Instalação de Dependências
```bash
npm install next-auth @hyperplay/next-auth-steam @supabase/supabase-js swr zod
```

**Tarefa para IA:**
- [ ] Instalar as dependências acima
- [ ] **COMENTAR**: Explicar o propósito de cada dependência
- [ ] **DOCUMENTAR**: Por que cada biblioteca foi escolhida
- [ ] Verificar se o package.json foi atualizado corretamente

#### ☐ 1.2 Configuração de Variáveis de Ambiente
**Arquivo a criar:** `.env.local`

**Tarefa para IA:**
- [ ] Criar arquivo `.env.local` com as variáveis necessárias
- [ ] **COMENTAR**: Explicar cada variável de ambiente
- [ ] **DOCUMENTAR**: Como obter cada chave (Steam API, Supabase, etc.)
- [ ] Adicionar `.env.local` ao `.gitignore` se não estiver

**Variáveis obrigatórias:**
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=sua_secret_key_aqui_32_chars_minimo
STEAM_API_KEY=sua_steam_api_key
DATABASE_URL=sua_database_url
SUPABASE_ANON_KEY=sua_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=sua_supabase_service_role_key
NEXT_PUBLIC_SUPABASE_URL=sua_supabase_url
```

#### ☐ 1.3 Configuração do TypeScript para Steam
**Arquivo a criar:** `src/lib/types/steam.ts`

**Tarefa para IA:**
- [ ] Criar arquivo `src/lib/types/steam.ts`
- [ ] **COMENTAR**: Explicar cada interface e tipo
- [ ] **DOCUMENTAR**: Como os tipos se relacionam com a Steam API
- [ ] Definir interfaces: `SteamProfile`, `SteamGame`, `VerifiedGame`
- [ ] Estender tipos do NextAuth para incluir dados Steam

**Implementar:**
```typescript
// Interfaces para dados da Steam API
export interface SteamProfile { ... }
export interface SteamGame { ... }
export interface VerifiedGame { ... }

// Extensão dos tipos NextAuth
declare module "next-auth" { ... }
```

### 🗄️ 2. CONFIGURAÇÃO DO BANCO DE DADOS

#### ☐ 2.1 Criação das Tabelas Supabase
**Arquivo a criar:** `src/lib/supabase/migrations/steam_tables.sql`

**Tarefa para IA:**
- [ ] Criar arquivo SQL com as tabelas necessárias
- [ ] **COMENTAR**: Explicar o propósito de cada tabela e coluna
- [ ] **DOCUMENTAR**: Relacionamentos entre tabelas
- [ ] Incluir índices para performance
- [ ] Configurar RLS (Row Level Security)

**Tabelas a criar:**
- `steam_connections` - Conexões Steam dos usuários
- `verified_games` - Jogos verificados dos usuários

#### ☐ 2.2 Configuração do Cliente Supabase
**Arquivo a criar:** `src/lib/supabase/client.ts`

**Tarefa para IA:**
- [ ] Criar cliente Supabase para frontend
- [ ] **COMENTAR**: Diferença entre cliente anônimo e servidor
- [ ] **DOCUMENTAR**: Quando usar cada tipo de cliente
- [ ] Configurar tipos TypeScript para as tabelas

**Arquivo a criar:** `src/lib/supabase/server.ts`

**Tarefa para IA:**
- [ ] Criar cliente Supabase para servidor (service role)
- [ ] **COMENTAR**: Por que usar service role no servidor
- [ ] **DOCUMENTAR**: Cuidados de segurança com service role

### 🔐 3. CONFIGURAÇÃO DA AUTENTICAÇÃO

#### ☐ 3.1 Provider Steam para NextAuth
**Arquivo a criar:** `src/lib/auth/providers/steamProvider.ts`

**Tarefa para IA:**
- [ ] Criar provider personalizado para Steam
- [ ] **COMENTAR**: Como funciona o OpenID 2.0 da Steam
- [ ] **DOCUMENTAR**: Configurações necessárias
- [ ] Explicar callback URL e redirecionamentos

#### ☐ 3.2 Configuração Principal do NextAuth
**Arquivo a criar:** `src/app/api/auth/[...nextauth]/route.ts`

**Tarefa para IA:**
- [ ] Configurar NextAuth com provider Steam
- [ ] **COMENTAR**: Explicar callbacks JWT e session
- [ ] **DOCUMENTAR**: Fluxo de autenticação completo
- [ ] Implementar callbacks para salvar dados Steam na sessão
- [ ] Configurar páginas customizadas se necessário

**Callbacks obrigatórios:**
- `jwt` - Para processar dados Steam
- `session` - Para disponibilizar na sessão

### 🛡️ 4. VALIDAÇÃO E SEGURANÇA

#### ☐ 4.1 Schemas de Validação
**Arquivo a criar:** `src/lib/validation/steam.ts`

**Tarefa para IA:**
- [ ] Criar schemas Zod para validação
- [ ] **COMENTAR**: Por que validar dados Steam
- [ ] **DOCUMENTAR**: Tipos de ataques prevenidos
- [ ] Schemas: `steamIdSchema`, `gameIdSchema`, `syncRequestSchema`

#### ☐ 4.2 Sistema de Rate Limiting
**Arquivo a criar:** `src/lib/security/rateLimit.ts`

**Tarefa para IA:**
- [ ] Implementar rate limiting básico
- [ ] **COMENTAR**: Por que rate limiting é necessário
- [ ] **DOCUMENTAR**: Limites recomendados
- [ ] Configurar limites por endpoint

#### ☐ 4.3 Classes de Erro Customizadas
**Arquivo a criar:** `src/lib/errors/steam-errors.ts`

**Tarefa para IA:**
- [ ] Criar classes de erro específicas para Steam
- [ ] **COMENTAR**: Vantagens de erros tipados
- [ ] **DOCUMENTAR**: Como usar cada tipo de erro
- [ ] Implementar: `SteamAPIError`, `PrivateProfileError`, `NoGamesFoundError`

### 🧪 5. CONFIGURAÇÃO DE TESTES

#### ☐ 5.1 Configuração Jest (se não existir)
**Arquivo a verificar:** `jest.config.js`

**Tarefa para IA:**
- [ ] Verificar se Jest está configurado
- [ ] **COMENTAR**: Configurações específicas para Next.js
- [ ] **DOCUMENTAR**: Como executar testes
- [ ] Adicionar configurações para módulos Steam se necessário

#### ☐ 5.2 Testes Unitários Básicos
**Arquivo a criar:** `src/__tests__/lib/steamValidation.test.ts`

**Tarefa para IA:**
- [ ] Criar testes para validação Steam
- [ ] **COMENTAR**: Cenários de teste importantes
- [ ] **DOCUMENTAR**: Como adicionar novos testes
- [ ] Testar schemas de validação

### 📚 6. DOCUMENTAÇÃO BÁSICA

#### ☐ 6.1 README de Setup
**Arquivo a atualizar:** `README.md`

**Tarefa para IA:**
- [ ] Adicionar seção sobre configuração Steam
- [ ] **COMENTAR**: Passos necessários para desenvolvimento
- [ ] **DOCUMENTAR**: Como obter credenciais Steam
- [ ] Incluir instruções de setup do banco de dados

#### ☐ 6.2 Documentação das Variáveis de Ambiente
**Arquivo a criar:** `src/lib/config/env.example`

**Tarefa para IA:**
- [ ] Criar exemplo com todas as variáveis
- [ ] **COMENTAR**: Explicar cada variável
- [ ] **DOCUMENTAR**: Onde obter cada valor
- [ ] Incluir valores de exemplo (não reais)

---

## ✅ CRITÉRIOS DE CONCLUSÃO DA FASE 1

### Verificações Obrigatórias:

- [ ] ✅ Todas as dependências instaladas e funcionando
- [ ] ✅ Variáveis de ambiente configuradas
- [ ] ✅ Tipos TypeScript definidos e exportados
- [ ] ✅ Banco de dados com tabelas criadas
- [ ] ✅ Clientes Supabase configurados
- [ ] ✅ NextAuth configurado com provider Steam
- [ ] ✅ Sistema de validação implementado
- [ ] ✅ Rate limiting básico funcionando
- [ ] ✅ Classes de erro definidas
- [ ] ✅ Testes básicos executando
- [ ] ✅ Documentação inicial criada

### Testes de Validação:

1. **Teste de Conectividade:**
   - [ ] Aplicação inicia sem erros
   - [ ] Conexão com Supabase funciona
   - [ ] NextAuth carrega corretamente

2. **Teste de Autenticação:**
   - [ ] Rota `/api/auth/providers` retorna Steam
   - [ ] Steam OpenID está configurado
   - [ ] Callbacks são executados

3. **Teste de Banco:**
   - [ ] Tabelas existem no Supabase
   - [ ] RLS está configurado
   - [ ] Índices estão criados

---

## 🚀 PRÓXIMOS PASSOS

Após completar a Fase 1, prossiga para:
**STEAM_IMPLEMENTATION_CHECKLIST_FASE_2.md** - Backend e APIs

### O que você terá ao final da Fase 1:
- ✅ Base sólida para desenvolvimento
- ✅ Autenticação Steam configurada
- ✅ Banco de dados preparado
- ✅ Validações e segurança básica
- ✅ Estrutura de testes

### O que vem na Fase 2:
- 🔄 Serviços de integração com Steam API
- 🔄 API routes para sincronização
- 🔄 Sistema de verificação de posse
- 🔄 Middleware de tratamento de erros 