'use client';

// DASHBOARD REDESIGN: Phase 3 - Advanced Features & Polish
// Date: 15/06/2025
// Task: dashboardStyleAdmin003 (FINAL PHASE)
//
// Enhanced UserDashboardLayout with advanced features:
// - Integrated DynamicBackground for gaming atmosphere
// - ResponsiveNavigation for mobile/tablet optimization
// - AccessibilityProvider for WCAG 2.1 AA compliance
// - Error boundaries and performance optimizations
// - Advanced animations and micro-interactions

import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { useAuthContext } from '@/contexts/auth-context';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';

import { ResponsiveNavigation } from './ResponsiveNavigation';
import { AccessibilityProvider, useAccessibility } from './AccessibilityProvider';

// Import CSS for adaptive text classes
import '@/components/review-form/style/NewReview.css';

interface UserDashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  stats?: {
    totalReviews: number;
    publishedReviews: number;
    averageScore: number;
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalFollowers: number;
  };
}

// Error Boundary Component
class DashboardErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-slate-900">
          <div className="text-center space-y-4 p-8 max-w-md">
            <div className="text-6xl">🎮</div>
            <h2 className="text-2xl font-bold text-white font-mono">
              <span className="text-violet-400">&lt;</span>
              error
              <span className="text-violet-400">/&gt;</span>
            </h2>
            <p className="text-slate-400 font-mono">
              Something went wrong loading your dashboard.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors font-mono"
            >
              Reload Dashboard
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading Skeleton Component
const DashboardSkeleton = () => (
  <div className="space-y-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <div className="h-8 bg-slate-700/50 rounded-lg w-64" />
        <div className="h-4 bg-slate-700/30 rounded w-48" />
      </div>
      <div className="flex space-x-3">
        <div className="h-10 bg-slate-700/50 rounded-lg w-32" />
        <div className="h-10 w-10 bg-slate-700/50 rounded-lg" />
      </div>
    </div>
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="bg-slate-800/50 border border-slate-700/50 rounded-xl p-6">
          <div className="h-6 bg-slate-700/30 rounded w-20 mb-3" />
          <div className="h-8 bg-slate-700/50 rounded w-16 mb-2" />
          <div className="h-2 bg-slate-700/30 rounded w-full" />
        </div>
      ))}
    </div>
  </div>
);

export function UserDashboardLayout({
  children,
  title,
  description,
  activeTab = 'settings',
  onTabChange,
  stats
}: UserDashboardLayoutProps) {
  const { user } = useAuthContext();
  const isDarkBackground = useBackgroundBrightness();

  // Advanced page transition animations
  const pageVariants = {
    initial: { opacity: 0, y: 20, scale: 0.98 },
    in: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 100,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    },
    out: { 
      opacity: 0, 
      y: -20, 
      scale: 1.02,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 100
      }
    }
  };

  return (
    <AccessibilityProvider>
      <DashboardErrorBoundary>

        
        <motion.div 
          className="min-h-screen bg-transparent relative"
          variants={pageVariants}
          initial="initial"
          animate="in"
          exit="out"
          id="main-content"
          role="main"
          aria-label="Dashboard main content"
        >
          {/* Enhanced Fixed Header with animations */}
          <motion.div 
            className="fixed top-14 left-0 right-0 z-40 border-b border-violet-900/20 bg-gradient-to-r from-slate-900 to-slate-800 shadow-xl"
            initial={{ y: -100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="container mx-auto px-2 sm:px-3 lg:px-4 xl:px-6 py-4">
              <div className="flex items-center justify-between">
                <motion.div 
                  className="flex items-center space-x-4"
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                >
                  <h1 className="text-2xl font-bold text-white drop-shadow-lg font-mono tracking-tight">
                    <span className="text-violet-400">&lt;</span>
                    <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Dashboard</span>
                    <span className="text-violet-400">/&gt;</span>
                  </h1>
                  {user && (
                    <motion.div 
                      className="text-sm text-slate-400 font-mono"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                    >
                      Welcome back, {user.displayName || user.email?.split('@')[0]}
                    </motion.div>
                  )}
                </motion.div>
                
              </div>
            </div>
          </motion.div>

          {/* Enhanced Content with responsive navigation */}
          <div className="pt-16 container mx-auto px-2 sm:px-3 lg:px-4 xl:px-6 pb-6">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              {/* Responsive Navigation Integration */}
              <Suspense fallback={<DashboardSkeleton />}>
                <ResponsiveNavigation 
                  activeTab={activeTab}
                  onTabChange={onTabChange}
                  stats={stats}
                />
              </Suspense>

              {/* Enhanced Main Content */}
              <motion.div 
                className="lg:col-span-9"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                {/* Enhanced Page Header */}
                {(title || description) && (
                  <motion.div 
                    className="mb-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.5 }}
                  >
                    {title && (
                      <h2 className="text-2xl font-bold mb-2 tracking-tight">
                        {title}
                      </h2>
                    )}
                    {description && (
                      <p className="text-slate-400 text-sm">{description}</p>
                    )}
                  </motion.div>
                )}

                {/* Enhanced Content with error boundary */}
                <Suspense fallback={<DashboardSkeleton />}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    {children}
                  </motion.div>
                </Suspense>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </DashboardErrorBoundary>
    </AccessibilityProvider>
  );
} 