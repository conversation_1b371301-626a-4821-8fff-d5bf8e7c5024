/* Enhanced Gaming-Style Theme with Unified Dark Blue Navbar/User Menu and Gaming Aesthetics */

/* Common color variables */
:root {
  --footer-bg: #000000;
  --footer-border: rgba(139, 92, 246, 0.3);
  --footer-gradient: radial-gradient(circle at center, rgba(139, 92, 246, 0.1), transparent 60%);
  --footer-gradient-active: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(139, 92, 246, 0.2), transparent 60%);
  --footer-section-bg: linear-gradient(to bottom right, rgba(15, 23, 42, 0.5), rgba(30, 41, 59, 0.3));
  --footer-section-border: 1px solid rgba(255, 255, 255, 0.05);
  --footer-blur: blur(16px);
  --footer-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  --footer-text: #fff;
  --footer-text-muted: rgba(255, 255, 255, 0.7);
  --footer-hover-bg: linear-gradient(to bottom right, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.2));
  --footer-hover-border: 1px solid rgba(255, 255, 255, 0.2);
  --footer-social-bg: rgba(15, 23, 42, 0.5);
  --footer-social-border: 1px solid rgba(255, 255, 255, 0.05);
  --footer-back-to-top: linear-gradient(to right, #8b5cf6, #06b6d4);

  /* Enhanced navbar and user menu dark blue gradient */
  --navmenu-gradient: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(20, 23, 38, 0.92) 100%);
  --navmenu-bg: rgba(15, 23, 42, 0.95);
  --navmenu-fine-shadow: 0 2px 8px 0 rgba(20, 23, 38, 0.18);

  /* Gaming menu specific colors */
  --gaming-accent: rgba(139, 92, 246, 0.8);
  --gaming-accent-hover: rgba(139, 92, 246, 1);
  --gaming-secondary: rgba(6, 182, 212, 0.6);
  --gaming-bg-primary: rgba(15, 23, 42, 0.95);
  --gaming-bg-secondary: rgba(30, 41, 59, 0.4);
  --gaming-border: rgba(139, 92, 246, 0.2);
  --gaming-text: rgba(226, 232, 240, 1);
  --gaming-text-muted: rgba(148, 163, 184, 1);
  --gaming-terminal-font: 'Courier New', monospace;

  /* Search box color */
  --search-bg: rgba(36, 41, 61, 0.92);
  --search-border: 1px solid rgba(139, 92, 246, 0.18);
  --search-blur: blur(8px);
}

/* Universal Scrollbar Styling */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) rgba(30, 41, 59, 0.3);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6) 0%, rgba(6, 182, 212, 0.4) 100%);
  border-radius: 4px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(6, 182, 212, 0.6) 100%);
}

*::-webkit-scrollbar-corner {
  background: rgba(30, 41, 59, 0.3);
}

/* Fixed Navbar styles */
.nav-container,
.nav-container-active {
  background: var(--navmenu-gradient);
  backdrop-filter: blur(10px);
  box-shadow: var(--navmenu-fine-shadow);
  color: var(--footer-text);
}

/* Enhanced Gaming Menu Styles */
.gaming-menu-container {
  background: var(--navmenu-gradient);
  backdrop-filter: blur(20px);
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
  font-family: var(--gaming-terminal-font);
  color: var(--gaming-text);
}

.gaming-menu-item {
  position: relative;
  color: var(--gaming-text-muted);
  transition: all 0.3s ease;
}

.gaming-menu-item:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
  color: var(--gaming-text);
}

.gaming-menu-icon {
  color: var(--gaming-accent);
  transition: all 0.3s ease;
}

.gaming-menu-item:hover .gaming-menu-icon {
  color: var(--gaming-accent-hover);
  transform: scale(1.1);
}

/* Gaming nav links */
.gaming-nav-link {
  display: block;
  position: relative;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.gaming-nav-link:hover {
  color: var(--gaming-text);
}

/* Elegant Login Button */
.elegant-login-button {
  position: relative;
  overflow: hidden;
  font-family: var(--gaming-terminal-font);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.elegant-login-button:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-1px);
}

/* Gaming Menu Scrollable Area */
.gaming-menu-scrollable {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.4) transparent;
  contain: layout style paint;
}

.gaming-menu-scrollable::-webkit-scrollbar {
  width: 8px;
}

.gaming-menu-scrollable::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

.gaming-menu-scrollable::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.4));
  border-radius: 4px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.gaming-menu-scrollable::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(139, 92, 246, 0.8), rgba(6, 182, 212, 0.6));
}

/* Enhanced Terminal-Style Typography */
.terminal-text {
  font-family: var(--gaming-terminal-font);
  font-weight: 600;
  letter-spacing: 0.05em;
}

.terminal-prompt {
  color: var(--gaming-accent);
  font-family: var(--gaming-terminal-font);
}

.terminal-cursor::after {
  content: '_';
  animation: blink 1s infinite;
  color: var(--gaming-secondary);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Fixed Themed Hamburger Menu */
.themed-hamburger-menu {
  position: relative;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.7) 0%, rgba(15, 23, 42, 0.8) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.themed-hamburger-menu::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.themed-hamburger-menu:hover::before {
  opacity: 1;
}

.themed-hamburger-menu:hover {
  border-color: rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.2);
}

.themed-hamburger-menu.active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  border-color: rgba(139, 92, 246, 0.5);
}

.hamburger-container {
  position: relative;
  width: 22px;
  height: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9) 0%, rgba(139, 92, 246, 0.7) 100%);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  transform-origin: center;
}

.hamburger-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.5) 0%, rgba(6, 182, 212, 0.4) 100%);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.themed-hamburger-menu:hover .hamburger-line::after {
  opacity: 1;
}

.themed-hamburger-menu.active .hamburger-line-1 {
  transform: rotate(45deg) translate(6px, 6px);
  background: linear-gradient(90deg, rgba(139, 92, 246, 1) 0%, rgba(6, 182, 212, 0.8) 100%);
}

.themed-hamburger-menu.active .hamburger-line-2 {
  opacity: 0;
  transform: scaleX(0);
}

.themed-hamburger-menu.active .hamburger-line-3 {
  transform: rotate(-45deg) translate(6px, -6px);
  background: linear-gradient(90deg, rgba(139, 92, 246, 1) 0%, rgba(6, 182, 212, 0.8) 100%);
}

/* Fixed Themed Search Button */
.themed-search-button {
  position: relative;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.7) 0%, rgba(15, 23, 42, 0.8) 100%);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.themed-search-button::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.themed-search-button:hover::before {
  opacity: 1;
}

.themed-search-button:hover {
  border-color: rgba(6, 182, 212, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.2);
}

.themed-search-button.active {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: rgba(6, 182, 212, 0.5);
}

.search-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.themed-search-button:hover .search-icon {
  color: rgba(6, 182, 212, 0.9);
  transform: scale(1.1);
}

.themed-search-button.active .search-icon {
  color: rgba(6, 182, 212, 1);
  transform: scale(1.05) rotate(15deg);
}

/* Enhanced Search styles - NAVBAR SPECIFIC with high specificity */
.navbar-search-container .search-input-container,
nav .search-input-container {
  background: rgba(36, 41, 61, 0.9) !important;
  border: 1px solid rgba(139, 92, 246, 0.25) !important;
  border-radius: 0.75rem !important;
  backdrop-filter: blur(12px) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
}

.navbar-search-container .search-input-container:focus-within,
nav .search-input-container:focus-within {
  border-color: rgba(139, 92, 246, 0.5) !important;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15), 0 4px 20px rgba(139, 92, 246, 0.1) !important;
}

.search-input {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-family: var(--gaming-terminal-font);
  font-weight: 500;
  line-height: 1.5 !important;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
  font-style: italic;
}

/* Specific overrides for navbar search input to handle shadcn conflicts */
.navbar-search-container .search-input-container input,
nav .search-input-container input,
.navbar-search-input-wrapper input,
.navbar-search-mobile-form input {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  font-family: var(--gaming-terminal-font) !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  /* Override shadcn Input component defaults */
  --tw-ring-offset-shadow: none !important;
  --tw-ring-shadow: none !important;
  ring-offset-width: 0 !important;
  ring-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.navbar-search-container .search-input-container input:focus,
nav .search-input-container input:focus,
.navbar-search-input-wrapper input:focus,
.navbar-search-mobile-form input:focus {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
  --tw-ring-offset-shadow: none !important;
  --tw-ring-shadow: none !important;
  ring-offset-width: 0 !important;
  ring-width: 0 !important;
}

.navbar-search-container .search-input-container input::placeholder,
nav .search-input-container input::placeholder,
.navbar-search-input-wrapper input::placeholder,
.navbar-search-mobile-form input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
  font-style: italic !important;
}

/* CRITICAL: Override shadcn Input component Tailwind classes for navbar search */
.navbar-search-container input[type="search"],
nav input[type="search"],
.navbar-search-input-wrapper input[type="search"],
.navbar-search-mobile-form input[type="search"] {
  /* Reset all shadcn/Tailwind defaults */
  background-color: transparent !important;
  background: transparent !important;
  border: none !important;
  border-color: transparent !important;
  border-width: 0 !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  min-height: auto !important;
  box-shadow: none !important;
  outline: none !important;
  ring: none !important;
  --tw-ring-shadow: none !important;
  --tw-ring-offset-shadow: none !important;
  --tw-ring-color: transparent !important;
  --tw-ring-offset-color: transparent !important;
  ring-offset-width: 0 !important;
  ring-width: 0 !important;
  ring-offset-color: transparent !important;
  ring-color: transparent !important;
  /* Apply navbar search styling */
  color: rgba(255, 255, 255, 0.95) !important;
  font-family: var(--gaming-terminal-font) !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
  font-size: 1rem !important;
}

.search-dropdown {
  background: rgba(20, 23, 38, 0.98) !important;
  border: 1px solid rgba(139, 92, 246, 0.3) !important;
  border-radius: 0.75rem !important;
  backdrop-filter: blur(12px) !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4) !important;
}

.search-item-hover:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.25) 0%, rgba(6, 182, 212, 0.2) 100%) !important;
  color: rgba(255, 255, 255, 1) !important;
}

/* Enhanced Search Category Pills */
.search-category-selected {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(6, 182, 212, 0.7) 100%) !important;
  color: rgba(255, 255, 255, 1) !important;
  border: 1px solid rgba(139, 92, 246, 0.5) !important;
  font-weight: 700 !important;
  font-family: var(--gaming-terminal-font);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

.search-category-default {
  background: rgba(30, 41, 59, 0.8) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(139, 92, 246, 0.25) !important;
  font-weight: 600 !important;
  font-family: var(--gaming-terminal-font);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-category-default:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.5) 0%, rgba(6, 182, 212, 0.4) 100%) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(139, 92, 246, 0.4) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.2) !important;
}

/* Loading/error states */
.search-loading,
.search-error {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-error {
  color: #ff6b6b;
}

/* Enhanced User menu styles */
.user-menu-container {
  background: var(--navmenu-gradient);
  backdrop-filter: blur(20px);
  box-shadow: var(--navmenu-fine-shadow);
  color: var(--gaming-text);
}

.user-menu-header,
.user-menu-footer {
  border-bottom: none;
  border-top: none;
  color: var(--gaming-text);
}

.user-menu-item {
  color: var(--gaming-text-muted);
  font-family: var(--gaming-terminal-font);
  transition: all 0.3s ease;
}

.user-menu-item:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
  color: var(--gaming-text);
}

.user-menu-icon {
  color: var(--gaming-accent);
  transition: all 0.3s ease;
}

.user-menu-item:hover .user-menu-icon {
  color: var(--gaming-accent-hover);
  transform: scale(1.1);
}

/* Footer styles */
.footer-container {
  background-color: var(--footer-bg);
  border-top: 1px solid var(--footer-border);
  color: var(--footer-text);
}

.footer-gradient,
.footer-gradient-active {
  background: var(--footer-gradient);
}

.footer-gradient-active {
  background: var(--footer-gradient-active);
}

.footer-section {
  border: var(--footer-section-border);
  background: var(--footer-section-bg);
  backdrop-filter: var(--footer-blur);
  color: var(--footer-text-muted);
}

.footer-social-icon {
  background-color: var(--footer-social-bg);
  border: var(--footer-social-border);
}

.footer-social-icon:hover {
  background: var(--footer-hover-bg);
  border: var(--footer-hover-border);
}

.footer-back-to-top {
  background: var(--footer-back-to-top);
  color: var(--footer-text);
}

/* Review creation navbar styles */
.review-navbar,
.review-navbar-bg,
.review-admin-panel {
  background: var(--footer-bg);
  box-shadow: var(--footer-shadow);
  backdrop-filter: var(--footer-blur);
  color: var(--footer-text);
}

.review-step-active,
.review-step-completed,
.review-step-accessible,
.review-step-disabled {
  background: var(--footer-section-bg);
  border: var(--footer-section-border);
  color: var(--footer-text-muted);
}

.review-step-active {
  background: var(--footer-hover-bg);
  border: var(--footer-hover-border);
  color: var(--footer-text);
}

.review-step-completed {
  background: var(--footer-hover-bg);
  border: var(--footer-hover-border);
  color: var(--footer-text);
}

.review-step-accessible:hover {
  background: var(--footer-hover-bg);
}

.review-step-disabled {
  opacity: 0.4;
}

.review-publish-button {
  background: var(--footer-back-to-top);
  color: var(--footer-text);
  border: none;
}

.review-publish-button:hover {
  background: linear-gradient(to right, #a78bfa, #22d3ee);
}

/* Additional Gaming UI Elements */
.glow-effect {
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3), 0 0 20px rgba(139, 92, 246, 0.2);
}

.glow-effect:hover {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5), 0 0 30px rgba(139, 92, 246, 0.3);
}

.cyber-border {
  position: relative;
}

.cyber-border::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.5), rgba(6, 182, 212, 0.5));
  z-index: -1;
  border-radius: inherit;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🔍 EXCLUSIVE NAVBAR SEARCH STYLES - CONFLICT-FREE
   ═══════════════════════════════════════════════════════════════════════════ */

/* Main navbar search container - exclusive layout */
.navbar-search-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
  flex-shrink: 0;
}

/* Search container - exclusive positioning */
.navbar-search-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Search icon container - exclusive layout */
.navbar-search-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Desktop search expansion - exclusive positioning */
.navbar-search-desktop-expansion {
  display: none;
  position: absolute;
  right: 100%;
  margin-right: 0.75rem;
  z-index: 60;
  width: 280px;
}

@media (min-width: 768px) {
  .navbar-search-desktop-expansion {
    display: block;
  }
}

/* Search input wrapper - exclusive layout */
.navbar-search-input-wrapper {
  position: relative;
  width: 100%;
}

/* Ghost suggestion - exclusive positioning */
.navbar-search-ghost-suggestion {
  position: absolute;
  left: 2.5rem;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  pointer-events: none;
  user-select: none;
  font-size: 1rem;
  opacity: 0.6;
}

/* Category icon in input - exclusive positioning */
.navbar-search-category-icon {
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  color: rgba(165, 180, 252, 0.8);
}

/* Desktop category dropdown - exclusive layout */
.navbar-search-category-dropdown {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem;
  margin-top: 0.25rem;
  border-radius: 0.375rem;
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 50;
  justify-content: center;
}

/* Search results container - exclusive positioning */
.navbar-search-results-container {
  position: absolute;
  left: 0;
  width: 100%;
  margin-top: 0.25rem;
  border-radius: 0.375rem;
  z-index: 50;
  overflow: hidden;
}

/* Search result item - exclusive layout */
.navbar-search-result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.navbar-search-result-item:last-child {
  border-bottom: none;
}

/* Mobile search overlay - exclusive positioning */
.navbar-search-mobile-overlay {
  position: fixed;
  top: 3.5rem;
  left: 0;
  right: 0;
  z-index: 60;
  padding: 1rem;
}

@media (min-width: 768px) {
  .navbar-search-mobile-overlay {
    display: none;
  }
}

/* Mobile search form container - exclusive layout */
.navbar-search-mobile-form {
  position: relative;
  max-width: 28rem;
  margin: 0 auto;
}

/* Mobile ghost suggestion - exclusive positioning */
.navbar-search-mobile-ghost-suggestion {
  position: absolute;
  left: 3rem;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  pointer-events: none;
  user-select: none;
  font-size: 1rem;
  opacity: 0.6;
}

/* Mobile category icon - exclusive positioning */
.navbar-search-mobile-category-icon {
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  color: rgba(165, 180, 252, 0.8);
}

/* Mobile category dropdown - exclusive layout */
.navbar-search-mobile-category-dropdown {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  border-radius: 0.5rem;
  max-width: 28rem;
  margin-left: auto;
  margin-right: auto;
  justify-content: center;
}

/* Mobile search results - exclusive layout */
.navbar-search-mobile-results {
  margin-top: 0.5rem;
  border-radius: 0.5rem;
  max-width: 28rem;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
}

/* Mobile search result item - exclusive layout */
.navbar-search-mobile-result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.navbar-search-mobile-result-item:last-child {
  border-bottom: none;
}

/* Category button content - exclusive layout */
.navbar-search-category-content {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.navbar-search-mobile-category-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Loading spinner - exclusive styles */
.navbar-search-loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(165, 180, 252, 0.8);
  border-top: 2px solid transparent;
  border-radius: 50%;
}

.navbar-search-mobile-loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(165, 180, 252, 0.8);
  border-top: 2px solid transparent;
  border-radius: 50%;
}

/* Ghost suggestion transparent text - exclusive styles */
.navbar-search-ghost-suggestion span:first-child,
.navbar-search-mobile-ghost-suggestion span:first-child {
  color: transparent !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gaming-menu-container {
    width: 85vw;
    max-width: 260px;
  }

  .themed-hamburger-menu,
  .themed-search-button {
    width: 36px;
    height: 36px;
  }
}

/* Compact spacing adjustments for better vertical space usage */
.gaming-menu-container nav {
  font-size: 1rem;
}

.gaming-menu-item {
  line-height: 1.3;
}

/* Centered trending sections container */
.trending-sections-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  overflow-y: auto;
  padding: 1rem 0;
}

/* Responsive behavior for trending sections */
@media (max-height: 800px) {
  .trending-sections-container {
    justify-content: flex-start;
    overflow-y: auto;
  }
}

@media (max-height: 600px) {
  .trending-sections-container {
    padding: 0.5rem 0;
  }
  
  .trending-creators-sidebar,
  .trending-tags-sidebar,
  .trending-games-sidebar {
    transform: scale(0.85);
    margin-bottom: -12px;
  }
}

/* Specific overrides for the trending sections */
.trending-games-sidebar {
  transform: scale(0.95);
  transform-origin: top;
  margin-bottom: -8px;
  padding-left: 16px !important;
  padding-right: 16px !important;
  font-size: 1.05rem;
}

.trending-creators-sidebar {
  transform: scale(0.95);
  transform-origin: top;
  margin-bottom: -8px;
  padding-left: 16px !important;
  padding-right: 16px !important;
  font-size: 1.05rem;
}

.trending-tags-sidebar {
  transform: scale(0.95);
  transform-origin: top;
  margin-bottom: -8px;
  padding-left: 16px !important;
  padding-right: 16px !important;
  font-size: 1.05rem;
}
