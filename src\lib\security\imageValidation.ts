// src/lib/security/imageValidation.ts
import sharp from 'sharp';
import { createHash } from 'crypto';

export interface SecurityScanResult {
  safe: boolean;
  issues: string[];
  metadata: {
    format: string;
    dimensions: { width: number; height: number };
    size: number;
    hasMetadata: boolean;
    checksum: string;
  };
}

/**
 * Advanced image security validation
 */
export async function validateImageSecurity(buffer: Buffer, filename: string): Promise<SecurityScanResult> {
  const issues: string[] = [];
  
  try {
    // Parse image metadata
    const image = sharp(buffer);
    const metadata = await image.metadata();
    
    if (!metadata.format) {
      issues.push('Invalid or corrupted image format');
      return { safe: false, issues, metadata: {} as any };
    }

    // Check for suspicious dimensions
    if (metadata.width && metadata.width > 10000) {
      issues.push('Image width exceeds safe limits');
    }
    
    if (metadata.height && metadata.height > 10000) {
      issues.push('Image height exceeds safe limits');
    }

    // Check for embedded metadata that could contain malicious code
    const hasEXIF = metadata.exif !== undefined;
    const hasICCP = metadata.icc !== undefined;
    const hasXMP = metadata.xmp !== undefined;
    
    if (hasEXIF || hasICCP || hasXMP) {
      // Strip metadata for security
      buffer = await image.rotate().toBuffer(); // rotate() strips metadata
    }

    // Check file signature (magic bytes)
    const signature = buffer.slice(0, 8);
    const validSignatures = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
      'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF
      'image/gif': [0x47, 0x49, 0x46], // GIF
    };

    const detectedFormat = metadata.format as keyof typeof validSignatures;
    const expectedSignature = validSignatures[detectedFormat];
    
    if (expectedSignature && !expectedSignature.every((byte, i) => signature[i] === byte)) {
      issues.push('File signature does not match declared format');
    }

    // Generate checksum for duplicate detection
    const checksum = createHash('sha256').update(buffer).digest('hex');

    // Check filename for suspicious patterns
    const suspiciousPatterns = [
      /\.php$/i,
      /\.jsp$/i,
      /\.asp$/i,
      /\.js$/i,
      /\.html$/i,
      /\.exe$/i,
      /script/i,
      /<script/i,
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(filename))) {
      issues.push('Suspicious filename pattern detected');
    }

    return {
      safe: issues.length === 0,
      issues,
      metadata: {
        format: metadata.format,
        dimensions: { width: metadata.width || 0, height: metadata.height || 0 },
        size: buffer.length,
        hasMetadata: hasEXIF || hasICCP || hasXMP,
        checksum,
      },
    };
  } catch (error) {
    issues.push(`Security validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { safe: false, issues, metadata: {} as any };
  }
}

/**
 * Rate limiting for uploads per user
 */
export class UploadRateLimit {
  private uploads: Map<string, number[]> = new Map();
  private readonly maxUploads: number;
  private readonly timeWindow: number; // in milliseconds

  constructor(maxUploads: number = 20, timeWindowMinutes: number = 60) {
    this.maxUploads = maxUploads;
    this.timeWindow = timeWindowMinutes * 60 * 1000;
  }

  checkRateLimit(userId: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const userUploads = this.uploads.get(userId) || [];
    
    // Remove old uploads outside the time window
    const recentUploads = userUploads.filter(time => now - time < this.timeWindow);
    this.uploads.set(userId, recentUploads);

    const remaining = Math.max(0, this.maxUploads - recentUploads.length);
    const allowed = recentUploads.length < this.maxUploads;
    
    if (allowed) {
      recentUploads.push(now);
      this.uploads.set(userId, recentUploads);
    }

    const oldestUpload = recentUploads[0] || now;
    const resetTime = oldestUpload + this.timeWindow;

    return { allowed, remaining, resetTime };
  }
}

/**
 * Content-based duplicate detection
 */
export async function detectDuplicate(
  checksum: string,
  userId: string,
  supabase: any
): Promise<{ isDuplicate: boolean; existingUrl?: string }> {
  try {
    // Check if image with same checksum already exists for this user
    const { data, error } = await supabase
      .from('user_images')
      .select('b2_url')
      .eq('user_id', userId)
      .eq('checksum', checksum)
      .limit(1);

    if (error) {
      console.error('Duplicate check error:', error);
      return { isDuplicate: false };
    }

    if (data && data.length > 0) {
      return { isDuplicate: true, existingUrl: data[0].b2_url };
    }

    return { isDuplicate: false };
  } catch (error) {
    console.error('Duplicate detection failed:', error);
    return { isDuplicate: false };
  }
}
