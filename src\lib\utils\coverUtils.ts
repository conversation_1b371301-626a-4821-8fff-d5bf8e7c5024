// Cover utility functions for consistent cover handling across the app
// Note: This file is client-safe and doesn't import server-only modules

// Re-export types and constants from gameCoverService without importing the module
export type CoverSize = 'thumb' | 'small' | 'big';

// Cover size configurations (duplicated to avoid server imports)
export const COVER_SIZES = {
  thumb: { width: 90, height: 128, suffix: 'thumb' },
  small: { width: 264, height: 374, suffix: 'small' },
  big: { width: 512, height: 725, suffix: 'big' }
} as const;

// Game interface for cover utilities
export interface GameWithCover {
  id?: string;
  igdb_id?: number | string;
  cover_url?: string;
  supabase_cover_url?: string;
  cover_cache_status?: string;
  igdb_cover_cached_at?: string;
}

// Default cover image paths
export const DEFAULT_COVERS = {
  thumb: '/images/default-game-thumb.svg',
  small: '/images/default-game-small.svg',
  big: '/images/default-game-big.svg'
} as const;

/**
 * Get optimal cover URL (client-safe version)
 */
function getOptimalCoverUrl(game: {
  cover_cache_status?: string;
  supabase_cover_url?: string;
  cover_url?: string;
}, preferredSize: CoverSize = 'big'): string {
  // Return cached cover if available
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url) {
    // If we want a different size, modify the URL
    if (preferredSize !== 'big') {
      return game.supabase_cover_url.replace('/big.webp', `/${COVER_SIZES[preferredSize].suffix}.webp`);
    }
    return game.supabase_cover_url;
  }

  // Fallback to IGDB URL
  return game.cover_url || '';
}

/**
 * Get cover URL for specific size (client-safe version)
 */
function getCoverUrlForSize(
  game: {
    cover_cache_status?: string;
    supabase_cover_url?: string;
    cover_url?: string;
  },
  size: CoverSize,
  igdbId?: string
): string {
  // If we have cached covers, return the specific size
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url && igdbId) {
    // Generate the URL for the specific size
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    if (supabaseUrl) {
      return `${supabaseUrl}/storage/v1/object/public/game-covers/covers/${igdbId}/${COVER_SIZES[size].suffix}.webp`;
    }
  }

  // Fallback to IGDB URL with size conversion
  if (game.cover_url) {
    const sizeMap = {
      thumb: 't_thumb',
      small: 't_cover_small',
      big: 't_cover_big'
    };
    return game.cover_url.replace(/t_\w+/, sizeMap[size]);
  }

  return '';
}

/**
 * Get the best available cover URL for a game with fallback handling
 */
export function getBestCoverUrl(
  game: GameWithCover | null | undefined,
  size: CoverSize = 'big',
  includeDefault: boolean = true
): string {
  if (!game) {
    return includeDefault ? DEFAULT_COVERS[size] : '';
  }

  // Try to get cached cover first
  if (game.cover_cache_status === 'cached' && game.supabase_cover_url) {
    return getCoverUrlForSize(game, size, game.igdb_id?.toString());
  }

  // Fallback to IGDB cover
  if (game.cover_url) {
    return getOptimalCoverUrl(game, size);
  }

  // Final fallback to default image
  return includeDefault ? DEFAULT_COVERS[size] : '';
}

/**
 * Get multiple cover sizes for responsive images
 */
export function getResponsiveCoverUrls(game: GameWithCover | null | undefined): {
  thumb: string;
  small: string;
  big: string;
} {
  return {
    thumb: getBestCoverUrl(game, 'thumb'),
    small: getBestCoverUrl(game, 'small'),
    big: getBestCoverUrl(game, 'big')
  };
}

/**
 * Generate srcSet for responsive images
 */
export function generateCoverSrcSet(game: GameWithCover | null | undefined): string {
  const urls = getResponsiveCoverUrls(game);
  return [
    `${urls.thumb} 90w`,
    `${urls.small} 264w`,
    `${urls.big} 512w`
  ].join(', ');
}

/**
 * Get cover loading status for UI feedback
 */
export function getCoverLoadingStatus(game: GameWithCover | null | undefined): {
  status: 'cached' | 'processing' | 'failed' | 'pending' | 'igdb';
  isLoading: boolean;
  hasError: boolean;
  canRetry: boolean;
} {
  if (!game) {
    return {
      status: 'igdb',
      isLoading: false,
      hasError: false,
      canRetry: false
    };
  }

  const status = game.cover_cache_status as any || 'igdb';
  
  return {
    status,
    isLoading: status === 'processing',
    hasError: status === 'failed',
    canRetry: status === 'failed' || status === 'pending'
  };
}

/**
 * Check if a cover URL is from our Supabase storage
 */
export function isCachedCover(url: string): boolean {
  if (!url) return false;
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) return false;
  
  return url.includes(`${supabaseUrl}/storage/v1/object/public/game-covers/`);
}

/**
 * Extract IGDB ID from various sources
 */
export function extractIgdbId(game: GameWithCover | null | undefined): string | null {
  if (!game) return null;
  
  if (game.igdb_id) {
    return game.igdb_id.toString();
  }
  
  // Try to extract from IGDB cover URL
  if (game.cover_url) {
    const match = game.cover_url.match(/\/(\d+)\.jpg/);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Format cover cache timestamp for display
 */
export function formatCacheTimestamp(timestamp: string | null | undefined): string {
  if (!timestamp) return 'Never cached';
  
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Recently cached';
    }
  } catch {
    return 'Invalid timestamp';
  }
}

/**
 * Get cover dimensions for a specific size
 */
export function getCoverDimensions(size: CoverSize): { width: number; height: number } {
  const dimensions = {
    thumb: { width: 90, height: 128 },
    small: { width: 264, height: 374 },
    big: { width: 512, height: 725 }
  };
  
  return dimensions[size];
}

/**
 * Generate alt text for cover images
 */
export function generateCoverAltText(game: GameWithCover | null | undefined): string {
  if (!game) return 'Game cover';
  
  // Try to get game name from various possible fields
  const gameName = (game as any).name || (game as any).game_name || 'Unknown Game';
  return `${gameName} cover art`;
}

/**
 * Validate cover URL format
 */
export function isValidCoverUrl(url: string): boolean {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    
    // Check if it's a valid image URL
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    const hasValidExtension = validExtensions.some(ext => 
      urlObj.pathname.toLowerCase().includes(ext)
    );
    
    // Check if it's from IGDB or our Supabase storage
    const isIgdb = urlObj.hostname.includes('igdb.com');
    const isSupabase = isCachedCover(url);
    
    return hasValidExtension && (isIgdb || isSupabase);
  } catch {
    return false;
  }
}

/**
 * Get cover processing priority based on game importance
 */
export function getCoverProcessingPriority(game: GameWithCover | null | undefined): number {
  if (!game) return 0;
  
  let priority = 1;
  
  // Higher priority for games with reviews
  if ((game as any).review_count > 0) {
    priority += 2;
  }
  
  // Higher priority for featured games
  if ((game as any).is_featured) {
    priority += 3;
  }
  
  // Higher priority for recently added games
  if ((game as any).created_at) {
    const createdAt = new Date((game as any).created_at);
    const daysSinceCreated = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 7) {
      priority += 1;
    }
  }
  
  return priority;
}

/**
 * Create a placeholder data URL for loading states
 */
export function createCoverPlaceholder(size: CoverSize): string {
  const { width, height } = getCoverDimensions(size);
  
  // Create a simple SVG placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#1e293b"/>
      <rect x="20%" y="20%" width="60%" height="60%" fill="#334155" rx="8"/>
      <circle cx="50%" cy="40%" r="8%" fill="#64748b"/>
      <rect x="30%" y="55%" width="40%" height="4%" fill="#64748b" rx="2"/>
      <rect x="35%" y="65%" width="30%" height="3%" fill="#64748b" rx="1"/>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

/**
 * Preload cover images for better performance
 */
export function preloadCoverImage(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = url;
  });
}

/**
 * Batch preload multiple cover images
 */
export async function preloadCoverImages(urls: string[]): Promise<void> {
  const promises = urls.map(url => preloadCoverImage(url).catch(() => {})); // Ignore errors
  await Promise.all(promises);
}
