// Heavy Search Caching System
// src/lib/utils/heavySearchCache.ts

export interface CacheEntry {
  results: any[];
  timestamp: number;
  query: string;
  hitCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  memorySize: number;
  persistentSize: number;
  hitRate: number;
  totalQueries: number;
  cacheHits: number;
  popularQueries: string[];
}

export class HeavySearchCache {
  private memoryCache = new Map<string, CacheEntry>();
  private maxMemorySize: number;
  private maxPersistentSize: number;
  private memoryTTL: number; // Memory cache TTL
  private persistentTTL: number; // Persistent cache TTL
  private stats = {
    totalQueries: 0,
    cacheHits: 0,
    memoryHits: 0,
    persistentHits: 0
  };

  constructor(
    maxMemorySize = 200,
    maxPersistentSize = 500,
    memoryTTL = 15 * 60 * 1000, // 15 minutes
    persistentTTL = 24 * 60 * 60 * 1000 // 24 hours
  ) {
    this.maxMemorySize = maxMemorySize;
    this.maxPersistentSize = maxPersistentSize;
    this.memoryTTL = memoryTTL;
    this.persistentTTL = persistentTTL;
    
    // Load persistent cache on initialization
    this.loadPersistentCache();
    
    // Clean up expired entries periodically
    setInterval(() => this.cleanup(), 5 * 60 * 1000); // Every 5 minutes
  }

  // Generate cache key
  private generateKey(query: string): string {
    return `search_${query.toLowerCase().trim()}`;
  }

  // Get from memory cache
  private getFromMemory(key: string): CacheEntry | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;

    // Check if expired
    if (Date.now() - entry.timestamp > this.memoryTTL) {
      this.memoryCache.delete(key);
      return null;
    }

    // Update access stats
    entry.lastAccessed = Date.now();
    entry.hitCount++;
    return entry;
  }

  // Get from persistent cache (localStorage)
  private getFromPersistent(key: string): CacheEntry | null {
    try {
      if (typeof window === 'undefined' || !window.localStorage) return null;
      
      const cached = localStorage.getItem(key);
      if (!cached) return null;

      const entry: CacheEntry = JSON.parse(cached);
      
      // Check if expired
      if (Date.now() - entry.timestamp > this.persistentTTL) {
        localStorage.removeItem(key);
        return null;
      }

      return entry;
    } catch (error) {
      console.warn('Failed to read from persistent cache:', error);
      return null;
    }
  }

  // Set in memory cache
  private setInMemory(key: string, entry: CacheEntry): void {
    // Remove oldest entries if cache is full
    if (this.memoryCache.size >= this.maxMemorySize) {
      this.evictLRU();
    }

    this.memoryCache.set(key, entry);
  }

  // Set in persistent cache
  private setInPersistent(key: string, entry: CacheEntry): void {
    try {
      if (typeof window === 'undefined' || !window.localStorage) return;
      
      // Check persistent cache size and clean if needed
      this.cleanPersistentCache();
      
      localStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      console.warn('Failed to write to persistent cache:', error);
    }
  }

  // Evict least recently used entries from memory
  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  // Clean persistent cache if too large
  private cleanPersistentCache(): void {
    try {
      if (typeof window === 'undefined' || !window.localStorage) return;

      const keys = Object.keys(localStorage).filter(key => key.startsWith('search_'));
      
      if (keys.length >= this.maxPersistentSize) {
        // Get all entries with timestamps
        const entries = keys.map(key => {
          try {
            const entry = JSON.parse(localStorage.getItem(key) || '{}');
            return { key, lastAccessed: entry.lastAccessed || 0 };
          } catch {
            return { key, lastAccessed: 0 };
          }
        });

        // Sort by last accessed and remove oldest
        entries.sort((a, b) => a.lastAccessed - b.lastAccessed);
        const toRemove = entries.slice(0, Math.floor(keys.length * 0.2)); // Remove oldest 20%

        toRemove.forEach(({ key }) => {
          localStorage.removeItem(key);
        });
      }
    } catch (error) {
      console.warn('Failed to clean persistent cache:', error);
    }
  }

  // Load popular queries from persistent cache to memory
  private loadPersistentCache(): void {
    try {
      if (typeof window === 'undefined' || !window.localStorage) return;

      const keys = Object.keys(localStorage).filter(key => key.startsWith('search_'));
      const popularEntries: Array<{ key: string; entry: CacheEntry }> = [];

      keys.forEach(key => {
        try {
          const entry: CacheEntry = JSON.parse(localStorage.getItem(key) || '{}');
          if (entry.hitCount > 2) { // Only load frequently accessed entries
            popularEntries.push({ key, entry });
          }
        } catch (error) {
          // Remove corrupted entries
          localStorage.removeItem(key);
        }
      });

      // Sort by hit count and load top entries to memory
      popularEntries
        .sort((a, b) => b.entry.hitCount - a.entry.hitCount)
        .slice(0, Math.floor(this.maxMemorySize * 0.3)) // Load 30% of memory capacity
        .forEach(({ key, entry }) => {
          this.memoryCache.set(key, entry);
        });

      console.log(`📚 Loaded ${popularEntries.length} popular searches to memory cache`);
    } catch (error) {
      console.warn('Failed to load persistent cache:', error);
    }
  }

  // Main get method
  get(query: string): any[] | null {
    if (!query.trim()) return null;

    const key = this.generateKey(query);
    this.stats.totalQueries++;

    // Try memory cache first
    const memoryEntry = this.getFromMemory(key);
    if (memoryEntry) {
      this.stats.cacheHits++;
      this.stats.memoryHits++;
      console.log(`🚀 Memory cache hit for: ${query}`);
      return memoryEntry.results;
    }

    // Try persistent cache
    const persistentEntry = this.getFromPersistent(key);
    if (persistentEntry) {
      this.stats.cacheHits++;
      this.stats.persistentHits++;
      console.log(`💾 Persistent cache hit for: ${query}`);
      
      // Promote to memory cache
      persistentEntry.lastAccessed = Date.now();
      persistentEntry.hitCount++;
      this.setInMemory(key, persistentEntry);
      
      return persistentEntry.results;
    }

    return null;
  }

  // Main set method
  set(query: string, results: any[]): void {
    if (!query.trim()) return;

    const key = this.generateKey(query);
    const now = Date.now();
    
    const entry: CacheEntry = {
      results,
      timestamp: now,
      query: query.trim(),
      hitCount: 1,
      lastAccessed: now
    };

    // Set in both caches
    this.setInMemory(key, entry);
    this.setInPersistent(key, entry);

    console.log(`📝 Cached search results for: ${query} (${results.length} results)`);
  }

  // Prefetch popular searches
  async prefetch(queries: string[], searchFunction: (searchQuery: string) => Promise<any[]>): Promise<void> {
    console.log('🔄 Starting search prefetch...', queries);
    
    for (const query of queries) {
      if (!this.get(query)) {
        try {
          const results = await searchFunction(query);
          this.set(query, results);
          console.log(`🎯 Prefetched: ${query}`);
          
          // Small delay to avoid overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.warn(`Failed to prefetch ${query}:`, error);
        }
      }
    }
  }

  // Cache warming for common prefixes
  async warmCache(searchFunction: (searchQuery: string) => Promise<any[]>): Promise<void> {
    const commonPrefixes = [
      'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
      'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
      'user', 'test', 'admin', 'game', 'play'
    ];

    console.log('🔥 Warming search cache...');
    
    // Only warm cache with uncached prefixes
    const uncachedPrefixes = commonPrefixes.filter(prefix => !this.get(prefix));
    
    if (uncachedPrefixes.length > 0) {
      await this.prefetch(uncachedPrefixes.slice(0, 10), searchFunction); // Limit to 10 to avoid overload
    }
  }

  // Get cache statistics
  getStats(): CacheStats {
    const memorySize = this.memoryCache.size;
    let persistentSize = 0;
    
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        persistentSize = Object.keys(localStorage).filter(key => key.startsWith('search_')).length;
      }
    } catch (error) {
      // Ignore localStorage errors
    }

    const hitRate = this.stats.totalQueries > 0 ? 
      (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;

    // Get popular queries from memory cache
    const popularQueries = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => b.hitCount - a.hitCount)
      .slice(0, 5)
      .map(([, entry]) => entry.query);

    return {
      memorySize,
      persistentSize,
      hitRate: Math.round(hitRate * 100) / 100,
      totalQueries: this.stats.totalQueries,
      cacheHits: this.stats.cacheHits,
      popularQueries
    };
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now();
    
    // Clean memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > this.memoryTTL) {
        this.memoryCache.delete(key);
      }
    }

    // Clean persistent cache
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const keys = Object.keys(localStorage).filter(key => key.startsWith('search_'));
        
        keys.forEach(key => {
          try {
            const entry = JSON.parse(localStorage.getItem(key) || '{}');
            if (now - entry.timestamp > this.persistentTTL) {
              localStorage.removeItem(key);
            }
          } catch (error) {
            // Remove corrupted entries
            localStorage.removeItem(key);
          }
        });
      }
    } catch (error) {
      console.warn('Cleanup failed:', error);
    }

    console.log('🧹 Cache cleanup completed');
  }

  // Clear all cache
  clear(): void {
    this.memoryCache.clear();
    
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const keys = Object.keys(localStorage).filter(key => key.startsWith('search_'));
        keys.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error);
    }

    this.stats = {
      totalQueries: 0,
      cacheHits: 0,
      memoryHits: 0,
      persistentHits: 0
    };

    console.log('🗑️ All cache cleared');
  }
}

// Create global heavy cache instance
export const heavySearchCache = new HeavySearchCache(
  300, // 300 items in memory
  1000, // 1000 items in localStorage
  30 * 60 * 1000, // 30 minutes memory TTL
  7 * 24 * 60 * 60 * 1000 // 7 days persistent TTL
);

// Auto-warm cache on initialization
if (typeof window !== 'undefined') {
  // Delay cache warming to not interfere with initial page load
  setTimeout(() => {
    console.log('🚀 Heavy search cache initialized');
    
    // Log cache stats periodically in development
    if (process.env.NODE_ENV === 'development') {
      setInterval(() => {
        const stats = heavySearchCache.getStats();
        if (stats.totalQueries > 0) {
          console.log('📊 Cache stats:', stats);
        }
      }, 60000); // Every minute
    }
  }, 2000);
}
