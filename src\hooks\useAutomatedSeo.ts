/**
 * React Hook for Automated SEO and Social Media Integration
 * Provides easy integration with CriticalPixel review forms
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { metadataGenerator, type ReviewData, type GeneratedMetadata } from '@/lib/metadata/generator';
import { contentAnalyzer, type ContentAnalysis } from '@/lib/content/analyzer';
import { socialMetaGenerator, type SocialMetaTags, type SocialPreview } from '@/lib/social/meta-generator';

export interface AutomatedSeoState {
  // Generated content
  generatedMetadata: GeneratedMetadata | null;
  contentAnalysis: ContentAnalysis | null;
  socialMetaTags: SocialMetaTags | null;
  socialPreviews: SocialPreview[];
  
  // Loading states
  isGenerating: boolean;
  isAnalyzing: boolean;
  
  // Configuration
  autoMode: boolean;
  
  // Actions
  generateMetadata: () => Promise<void>;
  analyzeContent: () => void;
  generateSocialTags: () => void;
  setAutoMode: (enabled: boolean) => void;
  applySuggestion: (field: 'title' | 'description' | 'keyword', value: string) => void;
  
  // Utilities
  getConfidenceScore: () => number;
  getSuggestions: () => string[];
  hasValidContent: () => boolean;
}

export interface UseAutomatedSeoOptions {
  reviewData: ReviewData;
  metaTitle: string;
  metaDescription: string;
  focusKeyword: string;
  onMetaTitleChange: (value: string) => void;
  onMetaDescriptionChange: (value: string) => void;
  onFocusKeywordChange: (value: string) => void;
  authorName?: string;
  autoGenerateOnChange?: boolean;
  debounceMs?: number;
}

export function useAutomatedSeo(options: UseAutomatedSeoOptions): AutomatedSeoState {
  const {
    reviewData,
    metaTitle,
    metaDescription,
    focusKeyword,
    onMetaTitleChange,
    onMetaDescriptionChange,
    onFocusKeywordChange,
    authorName,
    autoGenerateOnChange = false,
    debounceMs = 1000
  } = options;

  // State
  const [generatedMetadata, setGeneratedMetadata] = useState<GeneratedMetadata | null>(null);
  const [contentAnalysis, setContentAnalysis] = useState<ContentAnalysis | null>(null);
  const [socialMetaTags, setSocialMetaTags] = useState<SocialMetaTags | null>(null);
  const [socialPreviews, setSocialPreviews] = useState<SocialPreview[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [autoMode, setAutoMode] = useState(false);

  // Memoized values
  const hasValidContent = useMemo(() => {
    return !!(
      reviewData.gameName &&
      reviewData.reviewContentLexical &&
      reviewData.reviewContentLexical.length > 0
    );
  }, [reviewData.gameName, reviewData.reviewContentLexical]);

  const getConfidenceScore = useCallback(() => {
    if (generatedMetadata) {
      return generatedMetadata.confidence;
    }
    if (contentAnalysis) {
      return contentAnalysis.seoScore;
    }
    return 0;
  }, [generatedMetadata, contentAnalysis]);

  const getSuggestions = useCallback(() => {
    const suggestions: string[] = [];
    
    if (generatedMetadata?.suggestions) {
      suggestions.push(...generatedMetadata.suggestions);
    }
    
    if (contentAnalysis?.suggestions) {
      suggestions.push(...contentAnalysis.suggestions);
    }
    
    // Add SEO-specific suggestions
    if (!metaTitle) {
      suggestions.push('Add a meta title for better search visibility');
    } else if (metaTitle.length > 60) {
      suggestions.push('Shorten meta title to under 60 characters');
    }
    
    if (!metaDescription) {
      suggestions.push('Add a meta description to improve click-through rates');
    } else if (metaDescription.length > 160) {
      suggestions.push('Shorten meta description to under 160 characters');
    }
    
    if (!focusKeyword) {
      suggestions.push('Add a focus keyword to target specific searches');
    }
    
    return [...new Set(suggestions)]; // Remove duplicates
  }, [generatedMetadata, contentAnalysis, metaTitle, metaDescription, focusKeyword]);

  // Content analysis
  const analyzeContent = useCallback(() => {
    if (!reviewData.reviewContentLexical) {
      setContentAnalysis(null);
      return;
    }

    setIsAnalyzing(true);
    try {
      const analysis = contentAnalyzer.analyzeContent(reviewData.reviewContentLexical);
      setContentAnalysis(analysis);
    } catch (error) {
      console.error('[useAutomatedSeo] Error analyzing content:', error);
      setContentAnalysis(null);
    } finally {
      setIsAnalyzing(false);
    }
  }, [reviewData.reviewContentLexical]);

  // Metadata generation
  const generateMetadata = useCallback(async () => {
    if (!hasValidContent) {
      console.warn('[useAutomatedSeo] Cannot generate metadata: invalid content');
      return;
    }

    setIsGenerating(true);
    try {
      const metadata = await metadataGenerator.generateMetadata(reviewData);
      setGeneratedMetadata(metadata);

      // Auto-apply if in auto mode or if fields are empty
      if (autoMode || !metaTitle) {
        onMetaTitleChange(metadata.title);
      }
      if (autoMode || !metaDescription) {
        onMetaDescriptionChange(metadata.description);
      }
      if (autoMode || !focusKeyword) {
        onFocusKeywordChange(metadata.focusKeyword);
      }
    } catch (error) {
      console.error('[useAutomatedSeo] Error generating metadata:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [
    hasValidContent,
    reviewData,
    autoMode,
    metaTitle,
    metaDescription,
    focusKeyword,
    onMetaTitleChange,
    onMetaDescriptionChange,
    onFocusKeywordChange
  ]);

  // Social media tags generation
  const generateSocialTags = useCallback(() => {
    if (!generatedMetadata) {
      console.warn('[useAutomatedSeo] Cannot generate social tags: no metadata available');
      return;
    }

    try {
      const socialTags = socialMetaGenerator.generateSocialMetaTags(
        reviewData,
        generatedMetadata,
        authorName
      );
      setSocialMetaTags(socialTags);

      const previews = socialMetaGenerator.generateSocialPreviews(
        reviewData,
        generatedMetadata,
        authorName
      );
      setSocialPreviews(previews);
    } catch (error) {
      console.error('[useAutomatedSeo] Error generating social tags:', error);
    }
  }, [reviewData, generatedMetadata, authorName]);

  // Apply suggestions
  const applySuggestion = useCallback((field: 'title' | 'description' | 'keyword', value: string) => {
    switch (field) {
      case 'title':
        onMetaTitleChange(value);
        break;
      case 'description':
        onMetaDescriptionChange(value);
        break;
      case 'keyword':
        onFocusKeywordChange(value);
        break;
    }
  }, [onMetaTitleChange, onMetaDescriptionChange, onFocusKeywordChange]);

  // Auto-analyze content when it changes
  useEffect(() => {
    if (reviewData.reviewContentLexical) {
      const timeoutId = setTimeout(() => {
        analyzeContent();
      }, debounceMs);

      return () => clearTimeout(timeoutId);
    }
  }, [reviewData.reviewContentLexical, analyzeContent, debounceMs]);

  // Auto-generate metadata when review data changes (if auto mode is enabled)
  useEffect(() => {
    if (autoGenerateOnChange && autoMode && hasValidContent) {
      const timeoutId = setTimeout(() => {
        generateMetadata();
      }, debounceMs);

      return () => clearTimeout(timeoutId);
    }
  }, [
    autoGenerateOnChange,
    autoMode,
    hasValidContent,
    reviewData.gameName,
    reviewData.reviewTitle,
    reviewData.overallScore,
    generateMetadata,
    debounceMs
  ]);

  // Generate social tags when metadata is available
  useEffect(() => {
    if (generatedMetadata) {
      generateSocialTags();
    }
  }, [generatedMetadata, generateSocialTags]);

  return {
    // Generated content
    generatedMetadata,
    contentAnalysis,
    socialMetaTags,
    socialPreviews,
    
    // Loading states
    isGenerating,
    isAnalyzing,
    
    // Configuration
    autoMode,
    
    // Actions
    generateMetadata,
    analyzeContent,
    generateSocialTags,
    setAutoMode,
    applySuggestion,
    
    // Utilities
    getConfidenceScore,
    getSuggestions,
    hasValidContent: () => hasValidContent
  };
}

// Additional utility hooks

/**
 * Hook for generating meta tags for Next.js metadata API
 */
export function useNextMetadata(reviewData: ReviewData, generatedMetadata: GeneratedMetadata | null) {
  return useMemo(() => {
    if (!generatedMetadata) return null;

    const socialTags = socialMetaGenerator.generateSocialMetaTags(reviewData, generatedMetadata);
    
    return {
      title: generatedMetadata.title,
      description: generatedMetadata.description,
      keywords: generatedMetadata.keywords.join(', '),
      openGraph: {
        title: socialTags.openGraph.title,
        description: socialTags.openGraph.description,
        images: socialTags.openGraph.image ? [socialTags.openGraph.image] : undefined,
        type: socialTags.openGraph.type as 'article',
        siteName: socialTags.openGraph.siteName,
        url: socialTags.openGraph.url,
        locale: socialTags.openGraph.locale
      },
      twitter: {
        card: socialTags.twitter.card as 'summary_large_image',
        title: socialTags.twitter.title,
        description: socialTags.twitter.description,
        images: socialTags.twitter.image ? [socialTags.twitter.image] : undefined,
        site: socialTags.twitter.site,
        creator: socialTags.twitter.creator
      }
    };
  }, [reviewData, generatedMetadata]);
}

/**
 * Hook for real-time SEO score calculation
 */
export function useSeoScore(
  metaTitle: string,
  metaDescription: string,
  focusKeyword: string,
  contentAnalysis: ContentAnalysis | null
) {
  return useMemo(() => {
    let score = 0;
    const maxScore = 100;

    // Meta title scoring (25 points)
    if (metaTitle) {
      if (metaTitle.length >= 30 && metaTitle.length <= 60) {
        score += 25;
      } else if (metaTitle.length > 0) {
        score += 15;
      }
    }

    // Meta description scoring (25 points)
    if (metaDescription) {
      if (metaDescription.length >= 120 && metaDescription.length <= 160) {
        score += 25;
      } else if (metaDescription.length > 0) {
        score += 15;
      }
    }

    // Focus keyword scoring (20 points)
    if (focusKeyword) {
      score += 20;
      
      // Bonus if keyword appears in title
      if (metaTitle.toLowerCase().includes(focusKeyword.toLowerCase())) {
        score += 5;
      }
      
      // Bonus if keyword appears in description
      if (metaDescription.toLowerCase().includes(focusKeyword.toLowerCase())) {
        score += 5;
      }
    }

    // Content analysis scoring (30 points)
    if (contentAnalysis) {
      score += Math.round((contentAnalysis.seoScore / 100) * 30);
    }

    return Math.min(maxScore, score);
  }, [metaTitle, metaDescription, focusKeyword, contentAnalysis]);
}
