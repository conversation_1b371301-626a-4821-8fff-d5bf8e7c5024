-- Tag Management System Migration
-- Creates dedicated tables for tag management, analytics, and ranking
-- Run this migration to upgrade from the current array-based tag system

-- Create the main tags table
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(120) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50),
    
    -- Usage statistics for ranking
    usage_count INTEGER DEFAULT 0,
    recent_usage_count INTEGER DEFAULT 0, -- Last 30 days
    view_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    search_count INTEGER DEFAULT 0,
    
    -- Ranking and trending metrics
    trend_score DECIMAL(10,4) DEFAULT 0.0,
    popularity_rank INTEGER,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    
    -- Administrative fields
    status VARCHAR(20) DEFAULT 'active', -- active, archived, moderated
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- SEO and metadata
    meta_title VARCHAR(200),
    meta_description VARCHAR(500),
    
    CONSTRAINT valid_status CHECK (status IN ('active', 'archived', 'moderated')),
    CONSTRAINT valid_category CHECK (category IN ('genre', 'platform', 'feature', 'mood', 'difficulty', 'length', 'style', 'custom'))
);

-- Create the review_tags junction table
CREATE TABLE IF NOT EXISTS public.review_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES public.reviews(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Analytics for tag performance per review
    clicks_from_tag INTEGER DEFAULT 0,
    conversions_from_tag INTEGER DEFAULT 0,
    
    UNIQUE(review_id, tag_id)
);

-- Create tag analytics table for tracking detailed metrics
CREATE TABLE IF NOT EXISTS public.tag_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Daily metrics
    views INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    searches INTEGER DEFAULT 0,
    new_usages INTEGER DEFAULT 0,
    
    -- Engagement metrics
    avg_time_on_page DECIMAL(10,2) DEFAULT 0.0,
    bounce_rate DECIMAL(5,4) DEFAULT 0.0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(tag_id, date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_slug ON public.tags(slug);
CREATE INDEX IF NOT EXISTS idx_tags_name ON public.tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_category ON public.tags(category);
CREATE INDEX IF NOT EXISTS idx_tags_status ON public.tags(status);
CREATE INDEX IF NOT EXISTS idx_tags_trending ON public.tags(is_trending) WHERE is_trending = true;
CREATE INDEX IF NOT EXISTS idx_tags_featured ON public.tags(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_tags_popularity_rank ON public.tags(popularity_rank);
CREATE INDEX IF NOT EXISTS idx_tags_trend_score ON public.tags(trend_score DESC);
CREATE INDEX IF NOT EXISTS idx_tags_recent_usage ON public.tags(recent_usage_count DESC);

CREATE INDEX IF NOT EXISTS idx_review_tags_review_id ON public.review_tags(review_id);
CREATE INDEX IF NOT EXISTS idx_review_tags_tag_id ON public.review_tags(tag_id);

CREATE INDEX IF NOT EXISTS idx_tag_analytics_tag_id ON public.tag_analytics(tag_id);
CREATE INDEX IF NOT EXISTS idx_tag_analytics_date ON public.tag_analytics(date DESC);

-- Create updated_at trigger for tags table
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tags_updated_at 
    BEFORE UPDATE ON public.tags 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update tag usage statistics
CREATE OR REPLACE FUNCTION update_tag_usage_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment usage count and set last_used_at
        UPDATE public.tags 
        SET 
            usage_count = usage_count + 1,
            recent_usage_count = recent_usage_count + 1,
            last_used_at = NOW()
        WHERE id = NEW.tag_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement usage count
        UPDATE public.tags 
        SET 
            usage_count = GREATEST(usage_count - 1, 0),
            recent_usage_count = GREATEST(recent_usage_count - 1, 0)
        WHERE id = OLD.tag_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tag_usage_on_review_tag_change
    AFTER INSERT OR DELETE ON public.review_tags
    FOR EACH ROW
    EXECUTE FUNCTION update_tag_usage_stats();

-- Function to calculate trend scores
CREATE OR REPLACE FUNCTION calculate_tag_trend_scores()
RETURNS void AS $$
BEGIN
    UPDATE public.tags 
    SET trend_score = (
        -- Base score from recent usage (70% weight)
        (recent_usage_count * 0.7) +
        -- Recency factor (20% weight) - higher score for recently used tags
        (CASE 
            WHEN last_used_at > NOW() - INTERVAL '1 day' THEN 20
            WHEN last_used_at > NOW() - INTERVAL '3 days' THEN 15
            WHEN last_used_at > NOW() - INTERVAL '7 days' THEN 10
            WHEN last_used_at > NOW() - INTERVAL '14 days' THEN 5
            ELSE 0
        END * 0.2) +
        -- Engagement factor (10% weight)
        (CASE 
            WHEN click_count > 0 AND view_count > 0 
            THEN (click_count::decimal / view_count * 100) * 0.1
            ELSE 0
        END)
    );
    
    -- Update popularity ranks
    UPDATE public.tags 
    SET popularity_rank = rank_data.rank
    FROM (
        SELECT id, 
               ROW_NUMBER() OVER (ORDER BY trend_score DESC, usage_count DESC) as rank
        FROM public.tags 
        WHERE status = 'active'
    ) as rank_data
    WHERE tags.id = rank_data.id;
    
    -- Mark trending tags (top 20% by trend score, minimum 5 recent uses)
    UPDATE public.tags 
    SET is_trending = (
        trend_score > (
            SELECT PERCENTILE_CONT(0.8) WITHIN GROUP (ORDER BY trend_score)
            FROM public.tags 
            WHERE status = 'active'
        ) 
        AND recent_usage_count >= 5
        AND status = 'active'
    );
END;
$$ language 'plpgsql';

-- Function to reset recent usage counts (run daily)
CREATE OR REPLACE FUNCTION reset_recent_tag_usage()
RETURNS void AS $$
BEGIN
    -- Reset recent usage count for tags not used in the last 30 days
    UPDATE public.tags 
    SET recent_usage_count = 0
    WHERE last_used_at < NOW() - INTERVAL '30 days'
       OR last_used_at IS NULL;
       
    -- Recalculate trend scores after reset
    PERFORM calculate_tag_trend_scores();
END;
$$ language 'plpgsql';

-- Function to migrate existing review tags from array to new system
CREATE OR REPLACE FUNCTION migrate_existing_review_tags()
RETURNS void AS $$
DECLARE
    review_record RECORD;
    tag_name TEXT;
    tag_record RECORD;
    tag_slug TEXT;
BEGIN
    -- Loop through all reviews that have tags
    FOR review_record IN 
        SELECT id, tags 
        FROM public.reviews 
        WHERE tags IS NOT NULL AND array_length(tags, 1) > 0
    LOOP
        -- Loop through each tag in the array
        FOREACH tag_name IN ARRAY review_record.tags
        LOOP
            -- Generate slug for the tag
            tag_slug := lower(regexp_replace(trim(tag_name), '[^a-zA-Z0-9\s]', '', 'g'));
            tag_slug := regexp_replace(tag_slug, '\s+', '-', 'g');
            
            -- Insert tag if it doesn't exist
            INSERT INTO public.tags (name, slug, category, status)
            VALUES (trim(tag_name), tag_slug, 'custom', 'active')
            ON CONFLICT (name) DO NOTHING;
            
            -- Get the tag record
            SELECT id INTO tag_record FROM public.tags WHERE name = trim(tag_name);
            
            -- Insert into review_tags junction table
            INSERT INTO public.review_tags (review_id, tag_id)
            VALUES (review_record.id, tag_record.id)
            ON CONFLICT (review_id, tag_id) DO NOTHING;
        END LOOP;
    END LOOP;
    
    -- Calculate initial trend scores
    PERFORM calculate_tag_trend_scores();
END;
$$ language 'plpgsql';

-- Enable Row Level Security
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tag_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for tags (publicly readable, admin-only write)
CREATE POLICY "Tags are publicly readable" ON public.tags
    FOR SELECT USING (true);

CREATE POLICY "Only admins can insert tags" ON public.tags
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

CREATE POLICY "Only admins can update tags" ON public.tags
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

CREATE POLICY "Only admins can delete tags" ON public.tags
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Create RLS policies for review_tags (users can manage their own review tags)
CREATE POLICY "Users can view all review tags" ON public.review_tags
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own review tags" ON public.review_tags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.reviews 
            WHERE id = review_tags.review_id 
            AND author_id = auth.uid()
        )
    );

-- Create RLS policies for tag_analytics (admin-only access)
CREATE POLICY "Only admins can access tag analytics" ON public.tag_analytics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Insert some default gaming tags to bootstrap the system
INSERT INTO public.tags (name, slug, category, status, is_featured) VALUES
    -- Genre tags
    ('Action', 'action', 'genre', 'active', true),
    ('RPG', 'rpg', 'genre', 'active', true),
    ('Strategy', 'strategy', 'genre', 'active', true),
    ('Adventure', 'adventure', 'genre', 'active', true),
    ('Simulation', 'simulation', 'genre', 'active', true),
    ('Horror', 'horror', 'genre', 'active', true),
    ('Puzzle', 'puzzle', 'genre', 'active', true),
    ('Sports', 'sports', 'genre', 'active', true),
    ('Racing', 'racing', 'genre', 'active', true),
    ('Fighting', 'fighting', 'genre', 'active', true),
    
    -- Platform tags
    ('PC', 'pc', 'platform', 'active', true),
    ('PlayStation 5', 'playstation-5', 'platform', 'active', true),
    ('Xbox Series X/S', 'xbox-series-x-s', 'platform', 'active', true),
    ('Nintendo Switch', 'nintendo-switch', 'platform', 'active', true),
    ('Steam Deck', 'steam-deck', 'platform', 'active', true),
    
    -- Feature tags
    ('Multiplayer', 'multiplayer', 'feature', 'active', true),
    ('Single Player', 'single-player', 'feature', 'active', true),
    ('Co-op', 'co-op', 'feature', 'active', true),
    ('Open World', 'open-world', 'feature', 'active', true),
    ('Story Rich', 'story-rich', 'feature', 'active', true),
    ('Character Customization', 'character-customization', 'feature', 'active', false),
    ('Crafting', 'crafting', 'feature', 'active', false),
    ('Survival', 'survival', 'feature', 'active', true),
    
    -- Mood tags
    ('Relaxing', 'relaxing', 'mood', 'active', false),
    ('Intense', 'intense', 'mood', 'active', false),
    ('Competitive', 'competitive', 'mood', 'active', false),
    ('Atmospheric', 'atmospheric', 'mood', 'active', false),
    
    -- Difficulty tags
    ('Easy', 'easy', 'difficulty', 'active', false),
    ('Challenging', 'challenging', 'difficulty', 'active', false),
    ('Hardcore', 'hardcore', 'difficulty', 'active', false),
    
    -- Length tags
    ('Short', 'short', 'length', 'active', false),
    ('Medium', 'medium', 'length', 'active', false),
    ('Long', 'long', 'length', 'active', false),
    ('Endless', 'endless', 'length', 'active', false)
ON CONFLICT (name) DO NOTHING;

-- Create a view for trending tags with rich metadata
CREATE OR REPLACE VIEW public.trending_tags AS
SELECT 
    t.id,
    t.name,
    t.slug,
    t.category,
    t.usage_count,
    t.recent_usage_count,
    t.trend_score,
    t.popularity_rank,
    t.is_trending,
    t.is_featured,
    COUNT(rt.review_id) as review_count,
    COALESCE(AVG(r.overall_score), 0) as avg_review_score
FROM public.tags t
LEFT JOIN public.review_tags rt ON t.id = rt.tag_id
LEFT JOIN public.reviews r ON rt.review_id = r.id AND r.status = 'published'
WHERE t.status = 'active'
GROUP BY t.id, t.name, t.slug, t.category, t.usage_count, t.recent_usage_count, 
         t.trend_score, t.popularity_rank, t.is_trending, t.is_featured
ORDER BY t.trend_score DESC, t.usage_count DESC;

-- Create a view for tag suggestions based on categories
CREATE OR REPLACE VIEW public.tag_suggestions AS
SELECT 
    category,
    array_agg(name ORDER BY usage_count DESC) as suggested_tags
FROM public.tags 
WHERE status = 'active' AND usage_count > 0
GROUP BY category;

COMMENT ON TABLE public.tags IS 'Master table for all tags used throughout the platform';
COMMENT ON TABLE public.review_tags IS 'Junction table linking reviews to tags';
COMMENT ON TABLE public.tag_analytics IS 'Daily analytics data for tag performance tracking';
COMMENT ON COLUMN public.tags.trend_score IS 'Calculated score for trending algorithm based on usage, recency, and engagement';
COMMENT ON COLUMN public.tags.recent_usage_count IS 'Number of times tag was used in the last 30 days';
COMMENT ON FUNCTION calculate_tag_trend_scores() IS 'Recalculates trend scores and popularity rankings for all tags';
COMMENT ON FUNCTION reset_recent_tag_usage() IS 'Daily maintenance function to reset recent usage counts';
COMMENT ON FUNCTION migrate_existing_review_tags() IS 'One-time migration function to move from array-based tags to relational system';