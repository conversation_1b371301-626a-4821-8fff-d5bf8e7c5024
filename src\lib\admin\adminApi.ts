// Admin API Client Functions
// Date: 16/01/2025
// Task: adminSystemImpl002 - Fix client-side admin API calls

import { UserProfile } from '@/lib/types';

// Base API URL
const API_BASE = '/api/admin';

// Helper function to handle API responses
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  return response.json();
}

// Get users list with pagination and filters
export async function getUsersList(
  page: number = 1,
  limit: number = 50,
  filters?: {
    search?: string;
    role?: string;
    status?: 'active' | 'suspended';
    dateFrom?: string;
    dateTo?: string;
  }
): Promise<{
  users: UserProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  const searchParams = new URLSearchParams();
  searchParams.set('page', page.toString());
  searchParams.set('limit', limit.toString());
  
  if (filters?.search) searchParams.set('search', filters.search);
  if (filters?.role) searchParams.set('role', filters.role);
  if (filters?.status) searchParams.set('status', filters.status);
  if (filters?.dateFrom) searchParams.set('dateFrom', filters.dateFrom);
  if (filters?.dateTo) searchParams.set('dateTo', filters.dateTo);

  const response = await fetch(`${API_BASE}/users?${searchParams.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return handleApiResponse(response);
}

// Get single user details
export async function getUserDetails(uid: string): Promise<{ user: any }> {
  const response = await fetch(`${API_BASE}/users/${uid}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return handleApiResponse(response);
}

// Update user profile
export async function updateUserProfile(
  uid: string,
  data: {
    display_name?: string;
    bio?: string;
    avatar_url?: string;
    banner_url?: string;
    theme?: string;
  }
): Promise<{ success: boolean; message: string }> {
  const response = await fetch(`${API_BASE}/users/${uid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  return handleApiResponse(response);
}

// Update user role (Admin/User)
export async function updateUserRole(
  uid: string,
  role: string
): Promise<{ success: boolean; message: string }> {
  const isAdmin = role === 'Admin';
  
  const response = await fetch(`${API_BASE}/users/${uid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ is_admin: isAdmin }),
  });

  return handleApiResponse(response);
}

// Update user status (Active/Suspended)
export async function updateUserStatus(
  uid: string,
  disabled: boolean,
  reason?: string
): Promise<{ success: boolean; message: string }> {
  const response = await fetch(`${API_BASE}/users/${uid}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      suspended: disabled,
      reason: reason || (disabled ? 'Admin action' : null)
    }),
  });

  return handleApiResponse(response);
}

// Delete user (placeholder)
export async function deleteUser(uid: string): Promise<{ success: boolean; message: string }> {
  // For now, this will return an error since deletion requires careful implementation
  throw new Error('User deletion not yet implemented. This requires careful cascade handling of user data.');
}

// Search users
export async function searchUsers(
  query: string,
  limit: number = 20
): Promise<UserProfile[]> {
  const result = await getUsersList(1, limit, { search: query });
  return result.users;
}

export default {
  getUsersList,
  getUserDetails,
  updateUserProfile,
  updateUserRole,
  updateUserStatus,
  deleteUser,
  searchUsers
};
