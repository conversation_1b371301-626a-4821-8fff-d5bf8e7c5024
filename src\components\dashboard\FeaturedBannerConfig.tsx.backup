'use client';

import React, { useState, useEffect, useC<PERSON>back, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useSteamGridDBIcon } from '@/hooks/useSteamGridDBIcon';
import Image from 'next/image';
import {
  Eye,
  Heart,
  MessageSquare,
  Loader2,
  Check,
  X,
  Trash2,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Edit3,
  Save,
  Gamepad2,
  Zap,
  Target,
  Layers,
  ShoppingCart,
  Link2,
  Info,
  Sparkles
} from 'lucide-react';
import { getUserReviews } from '@/app/u/actions-content';
import type { ContentFilters } from '@/types/user-content';

interface UserReview {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  is_public: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
  title: string;
  slug: string;
}

interface StoreLink {
  id?: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  display_order: number;
  color_gradient?: string;
  is_active?: boolean;
  isEditing?: boolean;
}

interface FeaturedBannerConfigProps {
  userId: string;
  className?: string;
}

const FeaturedBannerConfig: React.FC<FeaturedBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [userReviews, setUserReviews] = useState<UserReview[]>([]);
  const [selectedReview, setSelectedReview] = useState<UserReview | null>(null);
  const [currentFeaturedReview, setCurrentFeaturedReview] = useState<UserReview | null>(null);
  const [sortBy, setSortBy] = useState<'created_at' | 'views_count' | 'likes_count' | 'updated_at' | 'rating'>('created_at');
  const [isExpanded, setIsExpanded] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Store links management
  const [storeLinks, setStoreLinks] = useState<StoreLink[]>([]);
  const [isLoadingStoreLinks, setIsLoadingStoreLinks] = useState(false);
  const [isSavingStoreLinks, setIsSavingStoreLinks] = useState(false);

  const { toast } = useToast();

  // Debounce search term to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Load user's reviews on mount and when sort changes
  useEffect(() => {
    loadUserReviews();
    loadStoreLinks();
  }, [userId, sortBy]);

  // Reload when debounced search term changes
  useEffect(() => {
    loadUserReviews();
  }, [debouncedSearchTerm]);

  const loadUserReviews = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('Loading user reviews for userId:', userId, 'with sort:', sortBy);

      // Create filters for sorting and limiting results
      const filters: ContentFilters = {
        sort_by: sortBy,
        sort_order: 'desc',
        limit: debouncedSearchTerm ? 20 : 2 // Show more when searching, default to 2
      };

      const response = await getUserReviews(userId, filters);
      console.log('getUserReviews response:', response);

      if (response && response.success && response.data) {
        console.log('Setting user reviews:', response.data);
        setUserReviews(response.data);

        // Find current featured review
        const featured = response.data.find(review => review.is_featured);
        console.log('Found featured review:', featured);
        if (featured) {
          setCurrentFeaturedReview(featured);
          setSelectedReview(featured);
        }
      } else {
        console.error('Failed to load reviews:', response?.error);
        toast({
          title: "Error",
          description: response?.error || "Failed to load reviews",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading user reviews:', error);
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userId, sortBy, debouncedSearchTerm]);

  const handleSetFeatured = async () => {
    if (!selectedReview) return;

    try {
      setIsSaving(true);
      
      // Use FormData for server action
      const formData = new FormData();
      formData.append('action', 'setFeatured');
      formData.append('userId', userId);
      formData.append('reviewId', selectedReview.id);
      
      // Call server action via form submission
      const response = await fetch('/api/u/featured-review', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('🎯 Set featured result:', result);

      if (result && result.success) {
        toast({
          title: "Sucesso",
          description: `"${selectedReview.game_name}" agora é seu review em destaque!`,
        });
        
        setCurrentFeaturedReview(selectedReview);
        
        // Update the review list to reflect changes
        const updatedReviews = userReviews.map(review => ({
          ...review,
          is_featured: review.id === selectedReview.id
        }));
        setUserReviews(updatedReviews);
      } else {
        console.error('❌ Set featured failed:', result);
        toast({
          title: "Erro",
          description: (result && result.error) || "Falha ao definir review em destaque",
          variant: "destructive",
        });
      }
      
    } catch (error) {
      console.error('Error setting featured review:', error);
      toast({
        title: "Erro",
        description: "Falha ao definir review em destaque",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveFeatured = async () => {
    try {
      setIsSaving(true);
      
      // Use FormData for server action
      const formData = new FormData();
      formData.append('action', 'removeFeatured');
      formData.append('userId', userId);
      if (currentFeaturedReview?.id) {
        formData.append('reviewId', currentFeaturedReview.id);
      }
      
      // Call server action via API
      const response = await fetch('/api/u/featured-review', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      console.log('🗑️ Remove featured result:', result);
      
      if (result && result.success) {
        toast({
          title: "Sucesso",
          description: "Review em destaque removido",
        });
        
        setCurrentFeaturedReview(null);
        setSelectedReview(null);
        
        // Update the review list to reflect changes
        setUserReviews(prev => prev.map(review => ({
          ...review,
          is_featured: false
        })));
              } else {
          console.error('❌ Remove featured failed:', result);
          toast({
            title: "Erro",
            description: (result && result.error) || "Falha ao remover review em destaque",
            variant: "destructive",
          });
        }
      
    } catch (error) {
      console.error('Error removing featured review:', error);
      toast({
        title: "Erro",
        description: "Falha ao remover review em destaque",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Store Links Management Functions
  const loadStoreLinks = async () => {
    try {
      setIsLoadingStoreLinks(true);

      const { createClient } = await import('@/lib/supabase/client');
      const supabase = createClient();

      const { data, error } = await supabase
        .from('featured_review_store_links')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error loading store links:', error);
        return;
      }

      setStoreLinks(data || []);
    } catch (error) {
      console.error('Error loading store links:', error);
    } finally {
      setIsLoadingStoreLinks(false);
    }
  };

  const addStoreLink = () => {
    if (storeLinks.length >= 3) {
      toast({
        title: "Limite atingido",
        description: "Você pode adicionar no máximo 3 links de lojas",
        variant: "destructive",
      });
      return;
    }

    const newLink: StoreLink = {
      store_name: '',
      price: '',
      original_price: '',
      store_url: '',
      display_order: storeLinks.length + 1,
      color_gradient: getRandomGradient(),
      is_active: true,
      isEditing: true // Auto-open edit mode for new links
    };

    setStoreLinks([...storeLinks, newLink]);
  };

  const removeStoreLink = async (index: number) => {
    const linkToRemove = storeLinks[index];

    if (linkToRemove.id) {
      try {
        const { createClient } = await import('@/lib/supabase/client');
        const supabase = createClient();

        await supabase
          .from('featured_review_store_links')
          .delete()
          .eq('id', linkToRemove.id);
      } catch (error) {
        console.error('Error deleting store link:', error);
      }
    }

    const updatedLinks = storeLinks.filter((_, i) => i !== index);
    // Reorder display_order
    const reorderedLinks = updatedLinks.map((link, i) => ({
      ...link,
      display_order: i + 1
    }));

    setStoreLinks(reorderedLinks);
  };

  const updateStoreLink = (index: number, field: keyof StoreLink, value: string) => {
    const updatedLinks = [...storeLinks];
    updatedLinks[index] = { ...updatedLinks[index], [field]: value };
    setStoreLinks(updatedLinks);
  };

  const saveStoreLinks = async () => {
    try {
      setIsSavingStoreLinks(true);

      const { createClient } = await import('@/lib/supabase/client');
      const supabase = createClient();

      // Validate all links have required fields
      const validLinks = storeLinks.filter(link =>
        link.store_name.trim() &&
        link.price.trim() &&
        link.store_url.trim()
      );

      if (validLinks.length === 0) {
        toast({
          title: "Erro",
          description: "Preencha pelo menos um link completo",
          variant: "destructive",
        });
        return;
      }

      // Delete existing links for this user
      await supabase
        .from('featured_review_store_links')
        .delete()
        .eq('user_id', userId);

      // Insert new links
      const linksToInsert = validLinks.map((link, index) => ({
        user_id: userId,
        store_name: link.store_name,
        price: link.price,
        original_price: link.original_price || null,
        store_url: link.store_url,
        display_order: index + 1,
        color_gradient: link.color_gradient,
        is_active: true
      }));

      const { error } = await supabase
        .from('featured_review_store_links')
        .insert(linksToInsert);

      if (error) {
        throw error;
      }

      toast({
        title: "Sucesso",
        description: "Links das lojas salvos com sucesso!",
      });

      loadStoreLinks(); // Reload to get IDs
    } catch (error) {
      console.error('Error saving store links:', error);
      toast({
        title: "Erro",
        description: "Falha ao salvar links das lojas",
        variant: "destructive",
      });
    } finally {
      setIsSavingStoreLinks(false);
    }
  };

  const getRandomGradient = () => {
    const gradients = [
      'from-blue-500/90 to-blue-600/95',
      'from-purple-500/90 to-purple-600/95',
      'from-emerald-500/90 to-emerald-600/95',
      'from-orange-500/90 to-orange-600/95',
      'from-red-500/90 to-red-600/95',
      'from-indigo-500/90 to-indigo-600/95'
    ];
    return gradients[Math.floor(Math.random() * gradients.length)];
  };

  // Review Item Component with SteamgridDB integration
  const ReviewItem: React.FC<{ review: UserReview; isSelected: boolean; onClick: () => void }> = ({ 
    review, 
    isSelected, 
    onClick 
  }) => {
    const { iconUrl: steamGridIcon, isLoading: iconLoading } = useSteamGridDBIcon(review.game_name);
    
    // Debug logging
    useEffect(() => {
      console.log(`🎮 ReviewItem for "${review.game_name}":`, {
        steamGridIcon,
        iconLoading,
        game_image: review.game_image,
        hasIcon: !!(steamGridIcon || review.game_image)
      });
    }, [steamGridIcon, iconLoading, review.game_name, review.game_image]);

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`group p-4 border rounded-xl cursor-pointer transition-all duration-300 ${
          isSelected
            ? 'border-purple-500/60 bg-gradient-to-r from-purple-500/10 via-purple-400/5 to-cyan-400/10 shadow-lg shadow-purple-500/20'
            : 'border-gray-700/50 bg-gradient-to-r from-gray-800/40 to-gray-700/30 hover:border-purple-400/40 hover:shadow-md hover:shadow-purple-500/10'
        }`}
        onClick={onClick}
      >
        <div className="flex items-center gap-4">
          {/* Game Icon */}
          <div className="flex-shrink-0 w-12 h-12 bg-slate-800/60 border border-slate-700/50 rounded-lg overflow-hidden relative">
            {iconLoading ? (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800">
                <Loader2 className="text-purple-400 animate-spin" size={16} />
              </div>
            ) : steamGridIcon || review.game_image ? (
              <Image
                src={steamGridIcon || review.game_image || ''}
                alt={review.game_name}
                width={48}
                height={48}
                className="w-full h-full object-cover"
                sizes="48px"
                onError={(e) => {
                  console.log(`🖼️ Image failed to load for ${review.game_name}:`, e.currentTarget.src);
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20"></div>
                <div className="relative z-10">
                  <Gamepad2 className="text-slate-400" size={20} />
                </div>
                <div className="absolute top-1 right-1 w-1 h-1 bg-purple-500/60 rounded-full"></div>
                <div className="absolute bottom-1 left-1 w-1 h-1 bg-blue-500/60 rounded-full"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-slate-500 font-mono opacity-50">
                  {review.game_name.slice(0, 2).toUpperCase()}
                </div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h4 className="font-semibold text-white text-sm font-['Lato'] truncate group-hover:text-purple-200 transition-colors">
                {review.game_name}
              </h4>
              {review.is_featured && (
                <Badge variant="secondary" className="text-xs bg-purple-600/80 text-purple-100 font-['Lato'] border border-purple-500/50">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4 text-xs text-gray-400 font-['Lato']">
              <span className="flex items-center gap-1 hover:text-purple-300 transition-colors">
                <Eye className="h-3 w-3" />
                {review.views_count?.toLocaleString() || '0'}
              </span>
              <span className="flex items-center gap-1 hover:text-red-300 transition-colors">
                <Heart className="h-3 w-3" />
                {review.likes_count?.toLocaleString() || '0'}
              </span>
              <span className="flex items-center gap-1 hover:text-blue-300 transition-colors">
                <MessageSquare className="h-3 w-3" />
                {review.comments_count?.toLocaleString() || '0'}
              </span>
              <span className="text-gray-500">•</span>
              <span className="text-gray-500">
                {review.rating}/5 ★
              </span>
            </div>
          </div>

          {/* Selection Indicator */}
          <div className={`flex-shrink-0 transition-all duration-200 ${
            isSelected ? 'scale-100 opacity-100' : 'scale-75 opacity-0 group-hover:scale-100 group-hover:opacity-50'
          }`}>
            <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
              isSelected 
                ? 'border-purple-400 bg-purple-500' 
                : 'border-gray-500 bg-transparent'
            }`}>
              {isSelected && <Check className="h-3 w-3 text-white" />}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  // Memoize filtered reviews to prevent unnecessary re-calculations
  const filteredReviews = useMemo(() => {
    if (!debouncedSearchTerm) return userReviews;
    return userReviews.filter(review =>
      review.game_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [userReviews, debouncedSearchTerm]);

  // Handle search input change without losing focus
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    
    // Maintain focus on the input after state update
    setTimeout(() => {
      if (searchInputRef.current && document.activeElement !== searchInputRef.current) {
        searchInputRef.current.focus();
        // Restore cursor position
        const cursorPosition = newValue.length;
        searchInputRef.current.setSelectionRange(cursorPosition, cursorPosition);
      }
    }, 0);
  }, []);

  // Store name suggestions
  const storeNameSuggestions = ['Steam', 'GOG', 'Epic', 'XBOX', 'PSN', 'GMG', 'Humble'];

  const toggleStoreEditMode = (index: number) => {
    const updatedLinks = [...storeLinks];
    updatedLinks[index] = { 
      ...updatedLinks[index], 
      isEditing: !updatedLinks[index].isEditing 
    };
    setStoreLinks(updatedLinks);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Featured Banner Control Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Featured Review Banner
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1 font-mono">
                Select one of your reviews to be featured prominently on your profile
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-4">
                {/* Current Featured Review Display */}
                {currentFeaturedReview && (
                  <div className="p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-200 text-sm font-['Lato']">Currently Featured</h4>
                      <Button
                        onClick={handleRemoveFeatured}
                        disabled={isSaving}
                        variant="outline"
                        size="sm"
                        className="text-red-400 border-red-400/50 hover:bg-red-400/10 font-['Lato']"
                      >
                        {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
                        Remove
                      </Button>
                    </div>
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-white font-['Lato']">{currentFeaturedReview.game_name}</h3>
                      <div className="flex items-center gap-3 text-xs text-slate-400 font-['Lato']">
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {currentFeaturedReview.views_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {currentFeaturedReview.likes_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageSquare className="h-3 w-3" />
                          {currentFeaturedReview.comments_count}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

          {/* Search and Filter Reviews */}
          <div className="space-y-3 p-4 bg-slate-800/60 border border-slate-700/60 rounded-lg">
            <Label htmlFor="review-search" className="text-sm text-gray-300 font-mono">Search Your Reviews</Label>
            <div className="flex gap-3">
              <div className="relative flex-1">
                <Target className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
                <Input
                  ref={searchInputRef}
                  id="review-search"
                  placeholder="Search reviews by game name..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-10 bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] transition-all duration-200"
                  disabled={isLoading}
                />
              </div>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-48 bg-slate-900/80 border-slate-600 focus:border-purple-400 text-white font-['Lato']">
                  <Layers className="h-4 w-4 mr-2 text-purple-400" />
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent className="bg-slate-900 border-slate-700">
                  <SelectItem value="created_at" className="font-['Lato']">Latest</SelectItem>
                  <SelectItem value="views_count" className="font-['Lato']">Most Views</SelectItem>
                  <SelectItem value="likes_count" className="font-['Lato']">Most Likes</SelectItem>
                  <SelectItem value="rating" className="font-['Lato']">Highest Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-500 scrollbar-track-transparent">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
                <span className="ml-2 text-slate-400 font-['Lato']">Loading reviews...</span>
              </div>
            ) : filteredReviews.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Gamepad2 className="w-8 h-8 text-slate-600" />
                </div>
                <h3 className="text-lg font-medium text-slate-300 mb-2 font-mono">
                  {searchTerm ? 'No reviews found' : 'No reviews yet'}
                </h3>
                <p className="text-slate-500 font-['Lato'] text-sm">
                  {searchTerm 
                    ? 'Try adjusting your search terms'
                    : 'Create your first review to get started!'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredReviews.map((review) => (
                  <ReviewItem
                    key={review.id}
                    review={review}
                    isSelected={selectedReview?.id === review.id}
                    onClick={() => setSelectedReview(review)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {selectedReview && selectedReview.id !== currentFeaturedReview?.id && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="pt-4 border-t border-slate-700/50"
            >
              <Button
                onClick={handleSetFeatured}
                disabled={isSaving}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-['Lato'] font-medium"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Gamepad2 className="h-4 w-4 mr-2" />
                )}
                Set as Featured Review
              </Button>
            </motion.div>
          )}

          {/* Affiliate Links Management Section */}
          <div className="pt-6 border-t border-gray-700/50">
            <div className="mb-4">
              <h3 className="text-base text-white font-mono mb-2">
                <span className="text-purple-400 mr-1">//</span>
                Affiliate Links
              </h3>
              <p className="text-sm text-slate-400 font-mono mb-3">
                Add store links with pricing to monetize your featured review banner
              </p>
              <div className="p-4 bg-slate-800/60 border border-slate-700/60 rounded-lg">
                <div className="flex items-start gap-3">
                  <Info className="h-4 w-4 text-purple-400 mt-0.5 flex-shrink-0" />
                  <div className="space-y-2 text-sm text-gray-300 font-['Lato']">
                    <p><strong>Currency Freedom:</strong> Use any currency symbol ($ € £ ¥ etc.)</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {/* Store Links List */}
              <div className="space-y-4">
                {storeLinks.map((link, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-purple-500/30 rounded-xl overflow-hidden bg-gradient-to-r from-slate-800/40 to-slate-700/30 backdrop-blur-sm"
                  >
                    {/* Compact View */}
                    {!link.isEditing && (
                      <div className="p-4 flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <ShoppingCart className="h-4 w-4 text-cyan-400" />
                            <span className="font-['Lato'] text-sm text-purple-300 font-medium">
                              {link.store_name || 'Unnamed Store'} 
                              <span className="text-xs text-slate-400 ml-2 font-mono">({link.store_name.length}/10)</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm font-['Lato']">
                            <span className="text-green-400 font-semibold">{link.price}</span>
                            {link.original_price && (
                              <span className="text-slate-400 line-through text-xs">{link.original_price}</span>
                            )}
                          </div>
                          {link.store_url && (
                            <Link2 className="h-3 w-3 text-blue-400" />
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            onClick={() => toggleStoreEditMode(index)}
                            variant="outline"
                            size="sm"
                            className="text-purple-400 border-purple-400/50 hover:bg-purple-400/10 h-8 px-3 font-['Lato']"
                          >
                            <Edit3 className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button
                            onClick={() => removeStoreLink(index)}
                            variant="outline"
                            size="sm"
                            className="text-red-400 border-red-400/50 hover:bg-red-400/10 h-8 w-8 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Expanded Edit View */}
                    <AnimatePresence>
                      {link.isEditing && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                          className="overflow-hidden"
                        >
                          <div className="p-4 bg-slate-800/60">
                            <div className="flex items-center justify-between mb-4">
                              <span className="font-mono text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Store {index + 1} Configuration
                              </span>
                              <Button
                                onClick={() => toggleStoreEditMode(index)}
                                variant="outline"
                                size="sm"
                                className="text-green-400 border-green-400/50 hover:bg-green-400/10 h-8 px-3 font-['Lato']"
                              >
                                <Save className="h-3 w-3 mr-1" />
                                Done
                              </Button>
                            </div>

                            <div className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <Label className="font-mono text-xs text-gray-400 mb-2 block">
                                    Store Name (Max 10 chars)
                                  </Label>
                                  <div className="relative">
                                    <Input
                                      placeholder="Steam, GOG, XBOX..."
                                      value={link.store_name}
                                      onChange={(e) => {
                                        const value = e.target.value.slice(0, 10);
                                        updateStoreLink(index, 'store_name', value);
                                      }}
                                      className="bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] transition-all duration-200"
                                      maxLength={10}
                                      list={`store-suggestions-${index}`}
                                    />
                                    <datalist id={`store-suggestions-${index}`}>
                                      {storeNameSuggestions.map((suggestion) => (
                                        <option key={suggestion} value={suggestion} />
                                      ))}
                                    </datalist>
                                    <span className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-xs font-mono ${
                                      link.store_name.length > 8 ? 'text-red-400' : 'text-gray-400'
                                    }`}>
                                      {link.store_name.length}/10
                                    </span>
                                  </div>
                                </div>

                                <div>
                                  <Label className="font-mono text-xs text-gray-400 mb-2 block">
                                    Current Price
                                  </Label>
                                  <Input
                                    placeholder="$29.99"
                                    value={link.price}
                                    onChange={(e) => updateStoreLink(index, 'price', e.target.value)}
                                    className="bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] transition-all duration-200"
                                  />
                                </div>
                              </div>

                              <div>
                                <Label className="font-mono text-xs text-gray-400 mb-2 block">
                                  Original Price (Optional - for sale display)
                                </Label>
                                <Input
                                  placeholder="$39.99"
                                  value={link.original_price || ''}
                                  onChange={(e) => updateStoreLink(index, 'original_price', e.target.value)}
                                  className="bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] transition-all duration-200"
                                />
                              </div>

                              <div>
                                <Label className="font-mono text-xs text-gray-400 mb-2 block">
                                  Store URL
                                </Label>
                                <div className="relative">
                                  <Input
                                    placeholder="https://store.steampowered.com/app/..."
                                    value={link.store_url}
                                    onChange={(e) => updateStoreLink(index, 'store_url', e.target.value)}
                                    className="bg-slate-900/80 border-slate-600 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-white placeholder:text-gray-400 font-['Lato'] pr-10 transition-all duration-200"
                                  />
                                  <Link2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-purple-400" />
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>

              {/* Add New Link Button */}
              {storeLinks.length < 3 && (
                <Button
                  onClick={addStoreLink}
                  variant="outline"
                  className="w-full border-dashed border-purple-500/40 hover:border-purple-400 hover:bg-purple-900/20 text-purple-300 hover:text-purple-200 transition-all duration-300 py-6 font-['Lato']"
                  disabled={isLoadingStoreLinks}
                >
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  <span className="font-medium">
                    Add Gaming Store Link ({storeLinks.length}/3)
                  </span>
                </Button>
              )}

              {/* Save Button */}
              {storeLinks.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="pt-4 border-t border-slate-700/50"
                >
                  <Button
                    onClick={saveStoreLinks}
                    disabled={isSavingStoreLinks}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50 font-mono text-xs uppercase tracking-wide font-semibold transition-all duration-200"
                  >
                    {isSavingStoreLinks ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Affiliate Links
                      </>
                    )}
                  </Button>
                </motion.div>
              )}
                            </div>
              </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};

export default FeaturedBannerConfig;
