'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Flag, AlertTriangle } from 'lucide-react';
import { useUserReport } from '@/hooks/useUserReports';
import { useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/contexts/auth-context';

interface ReportButtonProps {
  reviewId: string;
  reviewTitle: string;
  className?: string;
  triggerAsText?: boolean;
}

const REPORT_REASONS = [
  { value: 'copyright', label: 'DMCA Request' },
  { value: 'illegal_content', label: 'Illegal Content' },
  { value: 'harassment', label: 'Harassment' },
];

export function ReportButton({ reviewId, reviewTitle, className, triggerAsText }: ReportButtonProps) {
  const { user } = useAuthContext();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [reason, setReason] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Check if user has already reported this content
  const { data: hasReported = false } = useUserReport(reviewId, 'review');

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: 'Autenticação necessária',
        description: 'Faça login para denunciar conteúdo',
        variant: 'destructive',
      });
      return;
    }
    if (!reason) {
      toast({
        title: 'Motivo obrigatório',
        description: 'Selecione um motivo para a denúncia',
        variant: 'destructive',
      });
      return;
    }
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentId: reviewId,
          contentType: 'review',
          reporterId: user.uid,
          reason,
          description: description.trim() || undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Invalidate the user report query to update the UI immediately
        queryClient.invalidateQueries({ 
          queryKey: ['user-report', user.uid, reviewId, 'review'] 
        });
        
        toast({
          title: 'Denúncia enviada',
          description: 'Obrigado por reportar. Nossa equipe irá analisar em breve.',
        });
        setOpen(false);
        setReason('');
        setDescription('');
      } else {
        toast({
          title: 'Falha ao denunciar',
          description: result.error || 'Não foi possível enviar a denúncia',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Report submission error:', error);
      toast({
        title: 'Erro ao denunciar',
        description: 'Ocorreu um erro inesperado',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerAsText ? (
          <span
            onClick={() => !hasReported && setOpen(true)}
            className={`select-none font-mono text-[0.65rem] md:text-xs font-bold pb-2 drop-shadow-sm ${
              hasReported 
                ? 'cursor-not-allowed text-red-400' 
                : 'cursor-pointer transition-colors hover:text-violet-400'
            } ${className || ''}`}
            style={{ display: 'inline-flex', alignItems: 'center', gap: 2 }}
            title={hasReported ? 'You have already reported this content' : 'Report this content'}
          >
            <span style={{ 
              color: hasReported ? '#f87171' : '#a78bfa', 
              fontWeight: 700, 
              fontSize: '1em', 
              textShadow: hasReported ? '0 1px 4px rgba(248,113,113,0.18)' : '0 1px 4px rgba(80,0,120,0.18)'
            }}>&lt;</span>
            <span className="mx-1" style={{ 
              color: hasReported ? '#f87171' : '#e5e7eb', 
              fontWeight: 700, 
              fontSize: '0.85em', 
              textShadow: hasReported ? '0 1px 4px rgba(248,113,113,0.18)' : '0 1px 4px rgba(0,0,0,0.18)'
            }}>
              {hasReported ? 'Reported' : 'Report Post'}
            </span>
            <span style={{ 
              color: hasReported ? '#f87171' : '#a78bfa', 
              fontWeight: 700, 
              fontSize: '1em', 
              textShadow: hasReported ? '0 1px 4px rgba(248,113,113,0.18)' : '0 1px 4px rgba(80,0,120,0.18)'
            }}>/&gt;</span>
          </span>
        ) : (
          <Button
            variant="outline"
            size="sm"
            disabled={hasReported}
            className={`transition-colors ${
              hasReported 
                ? 'text-red-400 cursor-not-allowed' 
                : 'text-slate-400 hover:text-red-400 hover:border-red-400'
            } ${className}`}
            title={hasReported ? 'You have already reported this content' : 'Report this content'}
          >
            <Flag className={`h-4 w-4 mr-2 ${hasReported ? 'fill-current' : ''}`} />
            {hasReported ? 'Reported' : 'Denunciar'}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-lg border border-slate-700/50 bg-slate-900/95 backdrop-blur-xl rounded-xl shadow-2xl p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 bg-slate-800/60 rounded-lg border border-slate-600/30">
              {hasReported ? (
                <Flag className="w-5 h-5 text-red-400 fill-current" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-slate-300" />
              )}
            </div>
            <div>
              <span className="text-lg font-bold text-slate-200">
                {hasReported ? 'Already Reported' : 'Report Content'}
              </span>
              <p className="text-slate-400 text-sm font-normal">
                {hasReported ? 'You have already reported this content' : 'Help keep the community safe'}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="relative">

          <div className="space-y-6">
            {/* Review Info */}
            <div className="bg-slate-800/40 rounded-lg p-4 border border-slate-600/30">
              <div className="text-sm text-slate-300 mb-2">
                Target review:
              </div>
              <div className="text-slate-200 text-sm truncate">
                "{reviewTitle}"
              </div>
            </div>

            {hasReported ? (
              /* Already Reported Message */
              <div className="bg-red-600/10 border border-red-600/30 rounded-lg p-4 text-center">
                <Flag className="w-8 h-8 text-red-400 fill-current mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-red-300 mb-2">Content Already Reported</h3>
                <p className="text-slate-300 text-sm mb-4">
                  You have already submitted a report for this content. Our moderation team will review it and take appropriate action.
                </p>
                <div className="bg-slate-800/40 rounded-lg p-3 border border-slate-600/30">
                  <p className="text-slate-400 text-xs font-mono">
                    Thank you for helping keep our community safe. Multiple reports are automatically handled by our system.
                  </p>
                </div>
              </div>
            ) : (
              /* Report Form */
              <>
                {/* Reason Selection */}
                <div className="space-y-3">
                  <label className="text-sm text-slate-300 block">
                    Reason *
                  </label>
                  <div className="relative">
                    <Select value={reason} onValueChange={setReason}>
                      <SelectTrigger className="bg-slate-800/60 border-slate-600/50 hover:border-slate-500/70 rounded-lg text-slate-200">
                        <SelectValue placeholder="Select violation type..." />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-800 border-slate-600/50 rounded-lg">
                        {REPORT_REASONS.map((option) => (
                          <SelectItem 
                            key={option.value} 
                            value={option.value}
                            className="text-slate-200 hover:bg-slate-700 focus:bg-slate-700"
                          >
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-3">
                  <label className="text-sm text-slate-300 block">
                    Additional details (optional)
                  </label>
                  <div className="relative">
                    <Textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Describe the issue in detail..."
                      className="min-h-[100px] resize-none bg-slate-800/60 border-slate-600/50 hover:border-slate-500/70 focus:border-slate-500/70 rounded-lg text-slate-200 placeholder:text-slate-500"
                      maxLength={500}
                    />
                    <div className="absolute bottom-3 right-3 text-xs text-slate-500">
                      {description.length}/500
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4 border-t border-slate-700/30">
              <button
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 bg-slate-800/60 hover:bg-slate-700/60 border border-slate-600/30 hover:border-slate-500/50 rounded-lg text-slate-300 hover:text-white transition-all duration-200 text-sm disabled:opacity-50"
              >
                {hasReported ? 'Close' : 'Cancel'}
              </button>
              {!hasReported && (
                <button
                  onClick={handleSubmit}
                  disabled={!reason || isSubmitting}
                  className="flex-1 px-4 py-2 bg-red-600/80 hover:bg-red-500/80 border border-red-500/50 hover:border-red-400/70 rounded-lg text-white transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center gap-2">
                      <span className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></span>
                      Sending...
                    </span>
                  ) : (
                    'Submit Report'
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 