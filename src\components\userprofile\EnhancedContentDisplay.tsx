'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON><PERSON>, 
  Crown, 
  Flame, 
  Star, 
  Trophy, 
  Zap,
  Eye,
  Heart,
  MessageSquare,
  Play,
  Volume2,
  VolumeX,
  Maximize,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Componente de partículas animadas para conteúdo destacado
const AnimatedParticles = ({ theme }: { theme: any }) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    duration: number;
    delay: number;
  }>>([]);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Generate particles only on client side to avoid hydration mismatch
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      duration: Math.random() * 3 + 2,
      delay: Math.random() * 2
    }));
    setParticles(newParticles);
  }, []);

  if (!isMounted) {
    return <div className="absolute inset-0 overflow-hidden pointer-events-none" />;
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full opacity-30"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: theme?.colors?.accent || '#8b5cf6'
          }}
          animate={{
            y: [-10, -30, -10],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

// Componente de borda animada para conteúdo premium
const AnimatedBorder = ({ children, theme, className }: { 
  children: React.ReactNode; 
  theme: any; 
  className?: string; 
}) => (
  <div className={cn("relative", className)}>
    <div 
      className="absolute inset-0 rounded-lg opacity-75"
      style={{
        background: `linear-gradient(45deg, ${theme?.colors?.primary}, ${theme?.colors?.secondary}, ${theme?.colors?.accent}, ${theme?.colors?.primary})`,
        backgroundSize: '400% 400%',
        animation: 'gradient-shift 3s ease infinite'
      }}
    />
    <div className="relative bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 m-[1px]">
      {children}
    </div>
    <style jsx>{`
      @keyframes gradient-shift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }
    `}</style>
  </div>
);

// Componente para exibir review destacada com efeitos especiais
const FeaturedReviewCard = ({ review, theme }: { review: any; theme: any }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showFullText, setShowFullText] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="relative"
    >
      <AnimatedBorder theme={theme} className="mb-6">
        <div className="relative p-6 overflow-hidden">
          {/* Efeito de partículas para reviews featured */}
          <AnimatedParticles theme={theme} />
          
          {/* Badge Premium */}
          <div className="absolute top-4 right-4 z-10">
            <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-black font-bold">
              <Crown className="h-3 w-3 mr-1" />
              Featured
            </Badge>
          </div>

          {/* Header */}
          <div className="flex items-start gap-4 mb-4 relative z-10">
            <div className="relative">
              {review.game_image ? (
                <img 
                  src={review.game_image} 
                  alt={review.game_name}
                  className="w-20 h-20 rounded-lg object-cover"
                  onError={(e) => {
                    // Fallback to game icon if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.parentElement?.querySelector('.image-fallback') as HTMLElement;
                    if (fallback) fallback.style.display = 'flex';
                  }}
                />
              ) : null}
              
              {/* Fallback element for missing or broken images */}
              <div
                className={`image-fallback w-20 h-20 rounded-lg flex items-center justify-center ${
                  review.game_image ? 'hidden' : 'flex'
                }`}
                style={{ 
                  backgroundColor: `${theme?.colors?.primary}40`,
                  display: review.game_image ? 'none' : 'flex'
                }}
              >
                <div className="text-center">
                  <Trophy className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                  <p className="text-xs text-gray-500">Review</p>
                </div>
              </div>
              
              <motion.div
                className="absolute inset-0 rounded-lg"
                style={{
                  background: `linear-gradient(45deg, ${theme?.colors?.primary}40, transparent)`
                }}
                animate={{
                  opacity: isHovered ? 1 : 0
                }}
                transition={{ duration: 0.3 }}
              />
            </div>
            
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2 font-mono">
                <span style={{ color: theme?.colors?.accent }}>&lt;</span>
                {review.game_name}
                <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
              </h3>
              
              {/* Rating com estrelas animadas */}
              <div className="flex items-center gap-2 mb-2">
                <div className="flex">
                  {Array.from({ length: 5 }, (_, i) => (
                    <motion.div
                      key={i}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <Star 
                        className={cn(
                          "h-5 w-5",
                          i < review.rating 
                            ? "text-yellow-400 fill-yellow-400" 
                            : "text-gray-600"
                        )} 
                      />
                    </motion.div>
                  ))}
                </div>
                <span className="text-white font-bold">{review.rating}/5</span>
              </div>

              {/* Plataforma e tempo de jogo */}
              <div className="flex gap-2">
                <Badge variant="outline">{review.platform}</Badge>
                {review.playtime_hours && (
                  <Badge variant="secondary">
                    {review.playtime_hours}h jogadas
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Texto da review */}
          <div className="relative z-10 mb-4">
            <motion.p 
              className="text-gray-300 leading-relaxed"
              animate={{
                height: showFullText ? 'auto' : '60px'
              }}
              style={{ overflow: 'hidden' }}
            >
              {review.review_text}
            </motion.p>
            
            {review.review_text.length > 150 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFullText(!showFullText)}
                className="mt-2 text-violet-400 hover:text-violet-300"
              >
                {showFullText ? 'Ver menos' : 'Ver mais'}
              </Button>
            )}
          </div>

          {/* Tags com animação */}
          {review.tags && (
            <div className="flex flex-wrap gap-2 mb-4 relative z-10">
              {review.tags.map((tag: string, index: number) => (
                <motion.div
                  key={tag}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Badge 
                    variant="secondary" 
                    className="bg-gray-800/50 hover:bg-gray-700/50 transition-colors cursor-pointer"
                  >
                    {tag}
                  </Badge>
                </motion.div>
              ))}
            </div>
          )}

          {/* Estatísticas com animação */}
          <div className="flex items-center justify-between relative z-10">
            <div className="flex items-center gap-6">
              <motion.div 
                className="flex items-center gap-1 text-gray-400"
                whileHover={{ scale: 1.1, color: '#ef4444' }}
              >
                <Heart className="h-4 w-4" />
                <span className="font-semibold">{review.likes_count}</span>
              </motion.div>
              
              <motion.div 
                className="flex items-center gap-1 text-gray-400"
                whileHover={{ scale: 1.1, color: '#10b981' }}
              >
                <Eye className="h-4 w-4" />
                <span className="font-semibold">{review.views_count}</span>
              </motion.div>
              
              <motion.div 
                className="flex items-center gap-1 text-gray-400"
                whileHover={{ scale: 1.1, color: '#3b82f6' }}
              >
                <MessageSquare className="h-4 w-4" />
                <span className="font-semibold">{review.comments_count}</span>
              </motion.div>
            </div>
            
            <div className="text-sm text-gray-500">
              {new Date(review.created_at).toLocaleDateString('pt-BR')}
            </div>
          </div>

          {/* Efeito de brilho no hover */}
          <motion.div
            className="absolute inset-0 rounded-lg pointer-events-none"
            style={{
              background: `radial-gradient(circle at 50% 50%, ${theme?.colors?.primary}20, transparent 70%)`
            }}
            animate={{
              scale: isHovered ? 1.05 : 1,
              opacity: isHovered ? 1 : 0
            }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </AnimatedBorder>
    </motion.div>
  );
};

// Componente para showcase de achievements gamificados
const AchievementShowcase = ({ achievements, theme }: { achievements: any[]; theme: any }) => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
    {achievements.map((achievement, index) => (
      <motion.div
        key={achievement.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        whileHover={{ scale: 1.05, y: -5 }}
        className="group relative"
      >
        <Card className="bg-gray-900/50 border-gray-800/50 backdrop-blur-sm overflow-hidden">
          <CardContent className="p-4 text-center">
            {/* Ícone do achievement com animação */}
            <motion.div
              className="mx-auto mb-3 p-3 rounded-full"
              style={{ backgroundColor: `${theme?.colors?.primary}20` }}
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <Trophy 
                className="h-6 w-6" 
                style={{ color: achievement.rarity === 'legendary' ? '#ffd700' : theme?.colors?.primary }}
              />
            </motion.div>
            
            {/* Nome e descrição */}
            <h4 className="text-sm font-semibold text-white mb-1">
              {achievement.name}
            </h4>
            <p className="text-xs text-gray-400 mb-2">
              {achievement.description}
            </p>
            
            {/* Badge de raridade */}
            <Badge 
              variant={achievement.rarity === 'legendary' ? 'default' : 'secondary'}
              className={cn(
                "text-xs",
                achievement.rarity === 'legendary' && "bg-gradient-to-r from-yellow-500 to-orange-500 text-black"
              )}
            >
              {achievement.rarity === 'legendary' && <Sparkles className="h-3 w-3 mr-1" />}
              {achievement.rarity}
            </Badge>

            {/* Efeito de brilho para achievements lendários */}
            {achievement.rarity === 'legendary' && (
              <motion.div
                className="absolute inset-0 rounded-lg pointer-events-none"
                style={{
                  background: 'radial-gradient(circle, rgba(255,215,0,0.2) 0%, transparent 70%)'
                }}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.5, 0.8, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            )}
          </CardContent>
        </Card>
      </motion.div>
    ))}
  </div>
);

// Componente para galeria de mídia interativa
const MediaGallery = ({ media, theme }: { media: any[]; theme: any }) => {
  const [selectedMedia, setSelectedMedia] = useState<any>(null);
  const [isMuted, setIsMuted] = useState(true);

  return (
    <>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {media.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            className="group relative cursor-pointer"
            onClick={() => setSelectedMedia(item)}
          >
            <div className="relative rounded-lg overflow-hidden bg-gray-900 aspect-video">
              {item.type === 'image' ? (
                <img 
                  src={item.url} 
                  alt={item.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <video 
                  src={item.url}
                  className="w-full h-full object-cover"
                  muted
                  loop
                  onMouseEnter={(e) => e.currentTarget.play()}
                  onMouseLeave={(e) => e.currentTarget.pause()}
                />
              )}
              
              {/* Overlay com informações */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-3">
                  <h4 className="text-white font-semibold text-sm mb-1">
                    {item.title}
                  </h4>
                  <p className="text-gray-300 text-xs">
                    {item.game_name}
                  </p>
                </div>
              </div>

              {/* Ícone de play para vídeos */}
              {item.type === 'video' && (
                <div className="absolute top-2 right-2">
                  <div className="bg-black/50 rounded-full p-1">
                    <Play className="h-4 w-4 text-white" />
                  </div>
                </div>
              )}

              {/* Ícone de expand */}
              <motion.div
                className="absolute top-2 left-2 bg-black/50 rounded-full p-1 opacity-0 group-hover:opacity-100"
                whileHover={{ scale: 1.1 }}
              >
                <Maximize className="h-4 w-4 text-white" />
              </motion.div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Modal para visualização em tela cheia */}
      <AnimatePresence>
        {selectedMedia && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 backdrop-blur-md z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedMedia(null)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              {selectedMedia.type === 'image' ? (
                <img 
                  src={selectedMedia.url} 
                  alt={selectedMedia.title}
                  className="max-w-full max-h-full object-contain rounded-lg"
                />
              ) : (
                <video 
                  src={selectedMedia.url}
                  className="max-w-full max-h-full object-contain rounded-lg"
                  controls
                  autoPlay
                  muted={isMuted}
                />
              )}

              {/* Controles do modal */}
              <div className="absolute top-4 right-4 flex gap-2">
                {selectedMedia.type === 'video' && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setIsMuted(!isMuted)}
                    className="bg-black/50 hover:bg-black/70"
                  >
                    {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                  </Button>
                )}
                
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => window.open(selectedMedia.url, '_blank')}
                  className="bg-black/50 hover:bg-black/70"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>

              {/* Informações da mídia */}
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
                  <h3 className="text-white font-bold mb-1">{selectedMedia.title}</h3>
                  <p className="text-gray-300 text-sm">{selectedMedia.game_name}</p>
                  {selectedMedia.description && (
                    <p className="text-gray-400 text-sm mt-2">{selectedMedia.description}</p>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Componente principal para conteúdo destacado
interface EnhancedContentDisplayProps {
  featuredReview?: any;
  achievements?: any[];
  media?: any[];
  theme?: any;
}

export default function EnhancedContentDisplay({
  featuredReview,
  achievements = [],
  media = [],
  theme
}: EnhancedContentDisplayProps) {
  return (
    <div className="space-y-8">
      {/* Review Destacada */}
      {featuredReview && (
        <div>

          
          <FeaturedReviewCard review={featuredReview} theme={theme} />
        </div>
      )}

      {/* Achievements */}
      {achievements.length > 0 && (
        <div>
          <div className="flex items-center gap-3 mb-4">
            <div 
              className="p-2 rounded-lg"
              style={{ backgroundColor: `${theme?.colors?.primary}20` }}
            >
              <Trophy className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white font-mono">
                <span style={{ color: theme?.colors?.accent }}>&lt;</span>
                Conquistas
                <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
              </h2>
              <p className="text-sm text-gray-400">
                Marcos e realizações desbloqueadas
              </p>
            </div>
          </div>
          
          <AchievementShowcase achievements={achievements} theme={theme} />
        </div>
      )}

      {/* Galeria de Mídia */}
      {media.length > 0 && (
        <div>
          <div className="flex items-center gap-3 mb-4">
            <div 
              className="p-2 rounded-lg"
              style={{ backgroundColor: `${theme?.colors?.primary}20` }}
            >
              <Play className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white font-mono">
                <span style={{ color: theme?.colors?.accent }}>&lt;</span>
                Galeria
                <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
              </h2>
              <p className="text-sm text-gray-400">
                Screenshots e vídeos dos jogos
              </p>
            </div>
          </div>
          
          <MediaGallery media={media} theme={theme} />
        </div>
      )}
    </div>
  );
}