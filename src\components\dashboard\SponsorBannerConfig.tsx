'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  Link as LinkIcon,
  Image as ImageIcon,
  Loader2,
  Check,
  X,
  BarChart,
  Eye,
  ExternalLink
} from 'lucide-react';
import {
  getUserSponsorBanner,
  saveSponsorBanner,
  deactivateSponsorBanner
} from '@/lib/services/sponsorBannerService';
import UnifiedBannerAnalytics from './UnifiedBannerAnalytics';

interface SponsorData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface SponsorBannerConfigProps {
  userId: string;
  className?: string;
}

const SponsorBannerConfig: React.FC<SponsorBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [sponsorData, setSponsorData] = useState<SponsorData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentSponsor, setCurrentSponsor] = useState<SponsorData | null>(null);
  const [errors, setErrors] = useState<{
    img_url?: string;
    url?: string;
  }>({});
  
  const { toast } = useToast();

  // Load user's sponsor data on mount
  useEffect(() => {
    loadSponsorData();
  }, [userId]);

  const loadSponsorData = async () => {
    try {
      setIsLoading(true);
      
      // Check if userId is defined before making the API call
      if (!userId) {
        console.warn('Cannot load sponsor data: userId is undefined');
        setIsLoading(false);
        return;
      }
      
      console.log('Loading sponsor data for userId:', userId);

      const data = await getUserSponsorBanner(userId);
      
      if (data) {
        console.log('Found sponsor data:', data);
        setSponsorData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentSponsor(data);
      } else {
        // No data found, but this is not an error state
        console.log('No sponsor data found for user');
      }
    } catch (error) {
      console.error('Error loading sponsor data:', error);
      toast({
        title: "Error",
        description: "Failed to load sponsor data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof SponsorData, value: string | boolean) => {
    setSponsorData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear validation errors when user types
    if (field === 'img_url' || field === 'url') {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {img_url?: string; url?: string} = {};
    let isValid = true;
    
    // Validate image URL
    if (!sponsorData.img_url) {
      newErrors.img_url = "Image URL is required";
      isValid = false;
    } else if (!isValidImageUrl(sponsorData.img_url)) {
      newErrors.img_url = "Enter a valid image URL (http/https)";
      isValid = false;
    }
    
    // Validate affiliate link URL
    if (!sponsorData.url) {
      newErrors.url = "Affiliate link URL is required";
      isValid = false;
    } else if (!isValidUrl(sponsorData.url)) {
      newErrors.url = "Enter a valid URL (http/https)";
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };
  
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  };
  
  const isValidImageUrl = (url: string) => {
    return isValidUrl(url) && (
      url.match(/\.(jpeg|jpg|gif|png|webp)$/) !== null || 
      url.includes('cloudinary.com') || 
      url.includes('unsplash.com') ||
      url.includes('amazonaws.com')
    );
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setIsSaving(true);
      
      const savedData = await saveSponsorBanner({
        userId,
        imgUrl: sponsorData.img_url,
        url: sponsorData.url,
        isActive: true
      });
      
      setCurrentSponsor(savedData);
      setSponsorData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });
      
      toast({
        title: "Success",
        description: "Sponsor banner settings saved successfully",
      });
    } catch (error) {
      console.error('Error saving sponsor data:', error);
      toast({
        title: "Error",
        description: "Failed to save sponsor banner settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeactivate = async () => {
    try {
      setIsSaving(true);
      
      if (currentSponsor) {
        const updatedData = await deactivateSponsorBanner(userId);
        
        setCurrentSponsor(updatedData);
        setSponsorData({
          ...sponsorData,
          is_active: false,
          updated_at: updatedData.updated_at
        });
        
        toast({
          title: "Success",
          description: "Sponsor banner deactivated",
        });
      }
    } catch (error) {
      console.error('Error deactivating sponsor banner:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate sponsor banner",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleImageTest = () => {
    if (sponsorData.img_url && isValidImageUrl(sponsorData.img_url)) {
      window.open(sponsorData.img_url, '_blank');
    } else {
      toast({
        title: "Invalid Image URL",
        description: "Please enter a valid image URL first",
        variant: "destructive",
      });
    }
  };
  
  const handleLinkTest = () => {
    if (sponsorData.url && isValidUrl(sponsorData.url)) {
      window.open(sponsorData.url, '_blank');
    } else {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid URL first",
        variant: "destructive",
      });
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Sponsor Banner Control Card */}
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5 text-emerald-500" />
            Sponsor Banner
          </CardTitle>
          <p className="text-sm text-slate-400">
            Add a sponsor banner that will appear at the bottom of your profile's side navbar
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Sponsor Banner Display */}
          {currentSponsor && currentSponsor.is_active && (
            <div className="p-4 bg-slate-800/50 border border-slate-700/50 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-slate-200">Current Sponsor Banner</h4>
                <Button
                  onClick={handleDeactivate}
                  disabled={isSaving}
                  variant="outline"
                  size="sm"
                  className="text-red-400 border-red-400/50 hover:bg-red-400/10"
                >
                  {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
                  Deactivate
                </Button>
              </div>

              <div className="flex items-center justify-center">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto border border-slate-600 rounded-lg overflow-hidden bg-slate-800/80 flex items-center justify-center mb-3">
                    {currentSponsor.img_url ? (
                      <img
                        src={currentSponsor.img_url}
                        alt="Sponsor Banner"
                        className="w-full h-full object-contain"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "/imgs/image-placeholder.svg";
                          (e.target as HTMLImageElement).classList.add("p-4");
                        }}
                      />
                    ) : (
                      <ImageIcon className="h-10 w-10 text-slate-500" />
                    )}
                  </div>

                  <div className="flex items-center justify-center gap-2">
                    <a
                      href={currentSponsor.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-emerald-400 hover:text-emerald-300 hover:underline flex items-center gap-1"
                    >
                      {currentSponsor.url.length > 30
                        ? `${currentSponsor.url.substring(0, 30)}...`
                        : currentSponsor.url}
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <div className="space-y-4">
            {/* Image URL Input */}
            <div className="space-y-2">
              <Label htmlFor="img-url" className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4 text-slate-400" />
                Banner Image URL 
                <span className="text-xs text-slate-500">(Recommended: 300x300px square image)</span>
              </Label>
              <div className="relative">
                <Input
                  id="img-url"
                  placeholder="https://example.com/your-banner-image.png"
                  value={sponsorData.img_url}
                  onChange={(e) => handleInputChange('img_url', e.target.value)}
                  className={errors.img_url ? "border-red-400/50" : ""}
                  disabled={isLoading || isSaving}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-7 text-xs"
                  onClick={handleImageTest}
                >
                  Test
                </Button>
              </div>
              {errors.img_url && (
                <p className="text-xs text-red-400">{errors.img_url}</p>
              )}
              <p className="text-xs text-slate-500">
                Use a direct link to an image (JPG, PNG, GIF, WebP)
              </p>
            </div>
            
            {/* Affiliate URL Input */}
            <div className="space-y-2">
              <Label htmlFor="affiliate-url" className="flex items-center gap-2">
                <LinkIcon className="h-4 w-4 text-slate-400" />
                Affiliate Link URL
              </Label>
              <div className="relative">
                <Input
                  id="affiliate-url"
                  placeholder="https://example.com/your-affiliate-link"
                  value={sponsorData.url}
                  onChange={(e) => handleInputChange('url', e.target.value)}
                  className={errors.url ? "border-red-400/50" : ""}
                  disabled={isLoading || isSaving}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-7 text-xs"
                  onClick={handleLinkTest}
                >
                  Test
                </Button>
              </div>
              {errors.url && (
                <p className="text-xs text-red-400">{errors.url}</p>
              )}
            </div>
          </div>

          {/* Preview */}
          <div className="pt-4">
            <Label className="mb-2 block">Preview</Label>
            <div className="border border-slate-700/50 rounded-lg overflow-hidden bg-slate-800/50 p-4 flex flex-col items-center">
              {sponsorData.img_url ? (
                <div className="relative w-32 h-32 mb-2">
                  <img 
                    src={sponsorData.img_url}
                    alt="Sponsor Banner Preview" 
                    className="w-full h-full object-contain rounded-md"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/imgs/image-placeholder.svg";
                      (e.target as HTMLImageElement).classList.add("p-4");
                    }} 
                  />
                </div>
              ) : (
                <div className="w-32 h-32 mb-2 border border-dashed border-slate-600 rounded-md flex items-center justify-center bg-slate-800/80">
                  <ImageIcon className="h-10 w-10 text-slate-500" />
                </div>
              )}
              
              {sponsorData.url && (
                <a 
                  href={sponsorData.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-xs text-emerald-400 hover:text-emerald-300 hover:underline break-all truncate max-w-full px-2"
                >
                  {sponsorData.url.length > 40 
                    ? `${sponsorData.url.substring(0, 40)}...` 
                    : sponsorData.url}
                </a>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="pt-4 border-t border-slate-700/50"
          >
            <Button
              onClick={handleSave}
              disabled={isSaving || isLoading}
              className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              {isSaving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Check className="h-4 w-4 mr-2" />
              )}
              {currentSponsor && currentSponsor.is_active ? 'Update Sponsor Banner' : 'Activate Sponsor Banner'}
            </Button>
          </motion.div>

          {/* Analytics Section - Now inside the Sponsor Banner container */}
          {currentSponsor && currentSponsor.is_active && currentSponsor.id && (
            <div className="pt-4 border-t border-gray-700/50">
              <UnifiedBannerAnalytics
                bannerId={currentSponsor.id}
                bannerType="sponsor"
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SponsorBannerConfig;
