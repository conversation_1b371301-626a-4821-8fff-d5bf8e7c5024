// src/components/lexical/Editor.tsx
'use client';

import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import type { EditorState } from 'lexical';
import { $getRoot, $createParagraphNode } from 'lexical';
import dynamic from 'next/dynamic';
import EditorNodes from './nodes';
import '@/components/review-form/style/autosave-progress.css';

// Dynamic import of plugins to avoid SSR issues
const PremiumToolbarPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/PremiumToolbarPlugin'),
  {
    ssr: false,
    loading: () => (
      <div className="lexical-toolbar premium-toolbar">
        <div className="toolbar-group">
          <div className="toolbar-button disabled">↶</div>
          <div className="toolbar-button disabled">↷</div>
        </div>
        <div className="toolbar-separator" />
        <div className="toolbar-group">
          <div className="toolbar-button">P</div>
          <div className="toolbar-button">H1</div>
          <div className="toolbar-button">H2</div>
          <div className="toolbar-button">H3</div>
          <div className="toolbar-button">"</div>
        </div>
        <div className="toolbar-separator" />
        <div className="toolbar-group">
          <div className="toolbar-button"><strong>B</strong></div>
          <div className="toolbar-button"><em>I</em></div>
          <div className="toolbar-button"><u>U</u></div>
          <div className="toolbar-button"><s>S</s></div>
        </div>
        <div className="toolbar-separator" />
        <div className="toolbar-group">
          <div className="toolbar-button">• List</div>
          <div className="toolbar-button">1. List</div>
        </div>
        <div className="toolbar-separator" />
        <div className="toolbar-group">
          <div className="toolbar-button">Link</div>
          <div className="toolbar-button premium-feature">Img+</div>
        </div>
        <div className="toolbar-separator" />
        <div className="toolbar-group premium-group">
          <div className="toolbar-button premium-feature">HR</div>
          <div className="toolbar-button premium-feature">AI</div>
        </div>
      </div>
    )
  }
);

const CodeBlockPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/CodeBlockPlugin'),
  { ssr: false }
);

const MarkdownShortcutsPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/MarkdownShortcutsPlugin'),
  { ssr: false }
);

const TablePlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/TablePlugin'),
  { ssr: false }
);

const ListBreakPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/ListBreakPlugin'),
  { ssr: false }
);

const LineBreakPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/LineBreakPlugin'),
  { ssr: false }
);

const AutoSavePlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/AutoSavePlugin'),
  { ssr: false }
);

const AutoSaveCircularProgress = dynamic(
  () => import('@/components/review-form/lexical/AutoSaveCircularProgress'),
  { ssr: false }
);

const EditorRefPlugin = dynamic(
  () => import('@/components/review-form/lexical/plugins/EditorRefPlugin'),
  { ssr: false }
);


interface EditorProps {
  onChange?: (editorState: EditorState) => void;
  initialEditorState?: string | null;
  placeholder?: string;
  readOnly?: boolean;
  isLightMode?: boolean;
  // Premium features
  usePremiumToolbar?: boolean;
  isPremiumUser?: boolean;
  // Auto-save props
  userId?: string;
  reviewId?: string;
  gameName?: string;
  reviewTitle?: string;
  enableAutoSave?: boolean;
}

export interface EditorRef {
  clearEditor: () => void;
}

const Editor = forwardRef<EditorRef, EditorProps>(({
  onChange,
  initialEditorState,
  placeholder = "ENTER = line break • ENTER ENTER = new paragraph",
  readOnly = false, // FIX: Add readOnly prop destructuring
  isLightMode = false,
  usePremiumToolbar = true, // Default to premium for now
  isPremiumUser = true, // Default to premium for now - will be user-based later
  userId,
  reviewId,
  gameName,
  reviewTitle,
  enableAutoSave = true
}: EditorProps, ref) => {
  const [mounted, setMounted] = useState(false);
  const [components, setComponents] = useState<any>({});
  const [nodes, setNodes] = useState<any[]>([]);
  const [isReady, setIsReady] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'pending' | 'saving' | 'saved' | 'error'>('idle');
  const [editorInstance, setEditorInstance] = useState<any>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    clearEditor: () => {
      if (editorInstance) {
        editorInstance.update(() => {
          const rootNode = $getRoot();
          rootNode.clear();
          const emptyParagraph = $createParagraphNode();
          rootNode.append(emptyParagraph);
        });
      }
    }
  }), [editorInstance]);

  // Only mount on client - prevents hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;
    
    // Load everything needed for the editor
    Promise.all([
      import('@lexical/react/LexicalComposer'),
      import('@lexical/react/LexicalRichTextPlugin'),
      import('@lexical/react/LexicalContentEditable'),
      import('@lexical/react/LexicalHistoryPlugin'),
      import('@lexical/react/LexicalErrorBoundary'),
      import('@lexical/react/LexicalOnChangePlugin'),
      import('@lexical/react/LexicalListPlugin'),
      import('@lexical/react/LexicalLinkPlugin'),
    ]).then(([
      { LexicalComposer },
      { RichTextPlugin },
      { ContentEditable },
      { HistoryPlugin },
      LexicalErrorBoundaryModule,
      { OnChangePlugin },
      { ListPlugin },
      { LinkPlugin },
    ]) => {
      // Use comprehensive EditorNodes instead of basic nodes

      setComponents({
        LexicalComposer,
        RichTextPlugin,
        ContentEditable,
        HistoryPlugin,
        LexicalErrorBoundary: LexicalErrorBoundaryModule.default,
        OnChangePlugin,
        ListPlugin,
        LinkPlugin,
      });

      setNodes(EditorNodes);
      setIsReady(true);
    }).catch(error => {
      console.error('Failed to load editor:', error);
    });
  }, [mounted]);

  // FIX: Improved editor config
  const editorConfig = {
    namespace: 'CodeGamerEditor',
    theme: {
      paragraph: 'editor-paragraph',
      quote: 'editor-quote',
      heading: {
        h1: 'editor-heading-h1',
        h2: 'editor-heading-h2',
        h3: 'editor-heading-h3',
        h4: 'editor-heading-h4',
        h5: 'editor-heading-h5',
        h6: 'editor-heading-h6',
      },
      list: {
        ol: 'editor-list-ol',
        ul: 'editor-list-ul',
        listitem: 'editor-listitem',
        nested: {
          listitem: 'editor-nested-listitem',
        },
      },
      link: 'editor-link',
      text: {
        bold: 'editor-text-bold',
        italic: 'editor-text-italic',
        underline: 'editor-text-underline',
        strikethrough: 'editor-text-strikethrough',
        underlineStrikethrough: 'editor-text-underlineStrikethrough',
        code: 'editor-text-code',
      },
      code: 'editor-code',
      codeHighlight: {
        comment: 'editor-tokenComment',
        punctuation: 'editor-tokenPunctuation',
        property: 'editor-tokenProperty',
        selector: 'editor-tokenSelector',
        operator: 'editor-tokenOperator',
        attr: 'editor-tokenAttr',
        variable: 'editor-tokenVariable',
        function: 'editor-tokenFunction',
      },
      hashtag: 'editor-hashtag',
      table: 'editor-table',
      tableCell: 'editor-table-cell',
      tableCellHeader: 'editor-table-cell-header',
    },
    nodes: nodes,
    onError: (error: Error) => {
      console.error('Lexical Editor Error:', error);
    },
    readOnly: readOnly,
    // FIX: Handle initial state properly
    ...(initialEditorState && { editorState: initialEditorState }),
  };

  const handleEditorChange = (editorState: EditorState) => {
    if (onChange && !readOnly) {  // FIX: Don't call onChange in readOnly mode
      try {
        onChange(editorState);
      } catch (error) {
        console.error("Error in change handler:", error);
      }
    }
  };

  // Loading state with code/gamer styling
  if (!mounted || !isReady || !components.LexicalComposer) {
    return (
      <div className={`lexical-editor-container ${isLightMode ? 'editor-light-mode' : ''}`}>
        {!readOnly && usePremiumToolbar && (
          <div className="lexical-toolbar premium-toolbar">
            <div className="toolbar-group">
              <div className="toolbar-button disabled">↶</div>
              <div className="toolbar-button disabled">↷</div>
            </div>
            <div className="toolbar-separator" />
            <div className="toolbar-group">
              <div className="toolbar-button">P</div>
              <div className="toolbar-button">H1</div>
              <div className="toolbar-button">H2</div>
              <div className="toolbar-button">H3</div>
              <div className="toolbar-button">"</div>
            </div>
            <div className="toolbar-separator" />
            <div className="toolbar-group">
              <div className="toolbar-button"><strong>B</strong></div>
              <div className="toolbar-button"><em>I</em></div>
              <div className="toolbar-button"><u>U</u></div>
              <div className="toolbar-button"><s>S</s></div>
            </div>
            <div className="toolbar-separator" />
            <div className="toolbar-group">
              <div className="toolbar-button">• List</div>
              <div className="toolbar-button">1. List</div>
            </div>
            <div className="toolbar-separator" />
            <div className="toolbar-group">
              <div className="toolbar-button">Link</div>
              <div className="toolbar-button premium-feature">Img+</div>
            </div>
            <div className="toolbar-separator" />
            <div className="toolbar-group premium-group">
              <div className="toolbar-button premium-feature">HR</div>
              <div className="toolbar-button premium-feature">AI</div>
            </div>
          </div>
        )}
        <div className="lexical-editor-content">
          <div className="lexical-content-editable">
            {!readOnly && (
              <div className="lexical-placeholder">{placeholder}</div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`lexical-editor-container ${isLightMode ? 'editor-light-mode' : ''}`}>
      <components.LexicalComposer initialConfig={editorConfig}>
        {/* Only render PremiumToolbarPlugin if not readOnly */}
        {!readOnly && usePremiumToolbar && (
          <PremiumToolbarPlugin
            isLightMode={isLightMode}
            gameName={gameName}
            isPremium={isPremiumUser}
          />
        )}

        {/* Always render editor content */}
        <div className={`lexical-editor-wrapper ${readOnly ? 'read-only' : ''}`}>
          <div className="lexical-editor-content relative">
            {/* Circular progress indicator */}
            {!readOnly && enableAutoSave && userId && (
              <AutoSaveCircularProgress
                isEnabled={enableAutoSave}
                saveStatus={autoSaveStatus}
                debounceMs={10000}
              />
            )}
            
            <components.RichTextPlugin
              contentEditable={
                <components.ContentEditable
                  className={`lexical-content-editable ${readOnly ? 'read-only' : ''}`}
                />
              }
              placeholder={
                // FIX: Better placeholder logic
                !readOnly ? <div className="lexical-placeholder">{placeholder}</div> : null
              }
              ErrorBoundary={components.LexicalErrorBoundary}
            />
            <components.HistoryPlugin />
            <components.ListPlugin />
            <components.LinkPlugin />
            {/* New plugins for enhanced functionality */}
            {!readOnly && <CodeBlockPlugin />}
            {!readOnly && <MarkdownShortcutsPlugin />}
            {!readOnly && <TablePlugin />}
            {!readOnly && <ListBreakPlugin />}
            {!readOnly && <LineBreakPlugin />}
            {/* Auto-save plugin */}
            {!readOnly && userId && (
              <AutoSavePlugin
                userId={userId}
                reviewId={reviewId}
                gameName={gameName}
                reviewTitle={reviewTitle}
                isEnabled={enableAutoSave}
                onStatusChange={setAutoSaveStatus}
                showIndicator={false}
              />
            )}
            {/* FIX: Always include OnChangePlugin but handle readOnly in the handler */}
            <components.OnChangePlugin onChange={handleEditorChange} />
            {/* Editor reference plugin to capture editor instance */}
            <EditorRefPlugin onEditorReady={setEditorInstance} />
          </div>
        </div>
      </components.LexicalComposer>
    </div>
  );
});

Editor.displayName = 'Editor';

export default Editor;