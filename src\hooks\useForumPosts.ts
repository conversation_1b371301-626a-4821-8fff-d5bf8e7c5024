import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { ForumPost, ForumStats } from '@/types/forum';
import { useAuthContext } from '@/contexts/auth-context';

export function useForumPosts(reviewId: string) {
  const { supabaseUser } = useAuthContext();

  return useQuery({
    queryKey: ['forum-posts', reviewId, supabaseUser?.id],
    queryFn: async (): Promise<ForumPost[]> => {
      const supabase = createClient();

      // Get main posts (including deleted ones)
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          author:profiles (
            id,
            username,
            display_name,
            avatar_url
          )
        `)
        .eq('review_id', reviewId)
        .is('parent_id', null)
        .order('is_pinned', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching forum posts:', error);
        throw error;
      }

      // Filter out posts from users blocked by the current user (if logged in)
      let filteredData = data || [];
      if (supabaseUser?.id) {
        const { data: blockedUsers, error: blockedError } = await supabase
          .from('user_blocks')
          .select('blocked_id')
          .eq('blocker_id', supabaseUser.id);
          
        if (blockedError) {
          console.error('Error fetching blocked users:', blockedError);
        } else if (blockedUsers && blockedUsers.length > 0) {
          const blockedUserIds = blockedUsers.map(b => b.blocked_id);
          filteredData = data?.filter(post => !blockedUserIds.includes(post.author_id)) || [];
        }
      }

      // Get reply counts and user votes for each post
      const postsWithData = await Promise.all(
        filteredData.map(async (comment) => {
          // Get reply count (including deleted comments)
          const { count: replyCount } = await supabase
            .from('comments')
            .select('*', { count: 'exact', head: true })
            .eq('parent_id', comment.id);

          // Get current user's vote if logged in
          let userVote = null;
          if (supabaseUser?.id) {
            const { data: voteData, error: voteError } = await supabase
              .from('comment_votes')
              .select('vote_type')
              .eq('comment_id', comment.id)
              .eq('user_id', supabaseUser.id)
              .maybeSingle();

            // Only log error if it's not a "no rows" error
            if (voteError && voteError.code !== 'PGRST116') {
              console.error('Error fetching user vote:', voteError);
            }

            userVote = voteData?.vote_type || null;
          }

          return {
            id: comment.id,
            review_id: comment.review_id,
            author_id: comment.author_id,
            title: comment.title || `Comment by ${comment.author_name}`,
            content: comment.content,
            category: null,
            upvotes: comment.upvotes || 0,
            downvotes: comment.downvotes || 0,
            reply_count: replyCount || 0,
            is_pinned: comment.is_pinned || false,
            is_hot: false,
            is_deleted: comment.is_deleted || false,
            created_at: comment.created_at,
            updated_at: comment.updated_at,
            author: comment.author,
            user_vote: userVote, // Actual user vote from database
          };
        })
      );

      return postsWithData;
    },
    enabled: !!reviewId,
  });
}

export type ForumSortBy = 'recent' | 'popular' | 'unpopular' | 'comments';

export interface ForumFilters {
  searchUsername?: string;
  sortBy?: ForumSortBy;
  showMyActivity?: boolean;
}

export function useFilteredForumPosts(reviewId: string, filters: ForumFilters = {}) {
  const { supabaseUser } = useAuthContext();
  const { searchUsername, sortBy = 'recent', showMyActivity = false } = filters;

  return useQuery({
    queryKey: ['filtered-forum-posts', reviewId, supabaseUser?.id, searchUsername, sortBy, showMyActivity],
    queryFn: async (): Promise<ForumPost[]> => {
      const supabase = createClient();

      let query = supabase
        .from('comments')
        .select(`
          *,
          author:profiles (
            id,
            username,
            display_name,
            avatar_url
          )
        `)
        .eq('review_id', reviewId)
        .is('parent_id', null);

      // Apply username search filter
      if (searchUsername && searchUsername.trim()) {
        // Search for posts by username OR posts that have replies from this user
        const searchTerm = searchUsername.trim();

        // First, get posts directly by the user
        const { data: directPosts, error: directError } = await supabase
          .from('comments')
          .select(`
            *,
            author:profiles (
              id,
              username,
              display_name,
              avatar_url
            )
          `)
          .eq('review_id', reviewId)
          .is('parent_id', null)
          .or(`author.username.ilike.%${searchTerm}%,author.display_name.ilike.%${searchTerm}%`, { foreignTable: 'author' });

        if (directError) throw directError;

        // Then, get posts that have replies from this user
        const { data: repliedPosts, error: repliedError } = await supabase
          .from('comments')
          .select(`
            parent_id,
            author:profiles!inner (
              username,
              display_name
            )
          `)
          .eq('review_id', reviewId)
          .not('parent_id', 'is', null)
          .or(`author.username.ilike.%${searchTerm}%,author.display_name.ilike.%${searchTerm}%`, { foreignTable: 'author' });

        if (repliedError) throw repliedError;

        // Get the parent post IDs where user has replied
        const parentPostIds = repliedPosts?.map(r => r.parent_id).filter(Boolean) || [];

        // Get the parent posts
        let parentPosts: any[] = [];
        if (parentPostIds.length > 0) {
          const { data: parentPostsData, error: parentPostsError } = await supabase
            .from('comments')
            .select(`
              *,
              author:profiles (
                id,
                username,
                display_name,
                avatar_url
              )
            `)
            .in('id', parentPostIds);

          if (parentPostsError) throw parentPostsError;
          parentPosts = parentPostsData || [];
        }

        // Combine and deduplicate posts
        const allPosts = [...(directPosts || []), ...parentPosts];
        const uniquePosts = allPosts.filter((post, index, self) =>
          index === self.findIndex(p => p.id === post.id)
        );

        // Apply user engagement filter if needed
        let filteredPosts = uniquePosts;
        if (showMyActivity && supabaseUser?.id) {
          filteredPosts = await filterUserEngagedPosts(supabase, uniquePosts, supabaseUser.id);
        }

        // Apply sorting and return
        return await processPosts(supabase, filteredPosts, supabaseUser, sortBy);
      }

      // Apply user engagement filter
      if (showMyActivity && supabaseUser?.id) {
        // Get posts where user has engaged (voted, created, or replied)
        const { data: allPosts, error: allPostsError } = await query;
        if (allPostsError) throw allPostsError;

        const engagedPosts = await filterUserEngagedPosts(supabase, allPosts || [], supabaseUser.id);
        return await processPosts(supabase, engagedPosts, supabaseUser, sortBy);
      }

      // Regular query without username search
      const { data, error } = await query;
      if (error) throw error;

      return await processPosts(supabase, data || [], supabaseUser, sortBy);
    },
    enabled: !!reviewId,
  });
}

async function filterUserEngagedPosts(supabase: any, posts: any[], userId: string) {
  const postIds = posts.map(p => p.id);

  // Get posts where user voted
  const { data: votedPosts } = await supabase
    .from('comment_votes')
    .select('comment_id')
    .in('comment_id', postIds)
    .eq('user_id', userId);

  // Get posts where user replied
  const { data: repliedPosts } = await supabase
    .from('comments')
    .select('parent_id')
    .in('parent_id', postIds)
    .eq('author_id', userId);

  const votedPostIds = new Set(votedPosts?.map(v => v.comment_id) || []);
  const repliedPostIds = new Set(repliedPosts?.map(r => r.parent_id) || []);

  return posts.filter(post =>
    post.author_id === userId || // User created the post
    votedPostIds.has(post.id) || // User voted on the post
    repliedPostIds.has(post.id)  // User replied to the post
  );
}

async function processPosts(supabase: any, posts: any[], supabaseUser: any, sortBy: ForumSortBy) {
  // Filter out posts from blocked users
  let filteredData = posts;
  if (supabaseUser?.id) {
    const { data: blockedUsers } = await supabase
      .from('user_blocks')
      .select('blocked_id')
      .eq('blocker_id', supabaseUser.id);

    if (blockedUsers && blockedUsers.length > 0) {
      const blockedUserIds = blockedUsers.map(b => b.blocked_id);
      filteredData = posts.filter(post => !blockedUserIds.includes(post.author_id));
    }
  }

  // Get reply counts and user votes for all posts
  const postIds = filteredData.map(post => post.id);

  const [replyCounts, userVotes] = await Promise.all([
    // Get reply counts
    supabase
      .from('comments')
      .select('parent_id')
      .in('parent_id', postIds)
      .eq('is_deleted', false),

    // Get user votes if logged in
    supabaseUser?.id ? supabase
      .from('comment_votes')
      .select('comment_id, vote_type')
      .in('comment_id', postIds)
      .eq('user_id', supabaseUser.id) : { data: [] }
  ]);

  // Process reply counts
  const replyCountMap = new Map();
  replyCounts.data?.forEach((reply: any) => {
    const count = replyCountMap.get(reply.parent_id) || 0;
    replyCountMap.set(reply.parent_id, count + 1);
  });

  // Process user votes
  const userVoteMap = new Map();
  userVotes.data?.forEach((vote: any) => {
    userVoteMap.set(vote.comment_id, vote.vote_type);
  });

  // Transform and sort posts
  const postsWithData = filteredData.map(comment => ({
    id: comment.id,
    review_id: comment.review_id,
    author_id: comment.author_id,
    title: comment.title || `Comment by ${comment.author_name}`,
    content: comment.content,
    category: null,
    upvotes: comment.upvotes || 0,
    downvotes: comment.downvotes || 0,
    reply_count: replyCountMap.get(comment.id) || 0,
    is_pinned: comment.is_pinned || false,
    is_hot: false,
    is_deleted: comment.is_deleted || false,
    created_at: comment.created_at,
    updated_at: comment.updated_at,
    author: comment.author,
    user_vote: userVoteMap.get(comment.id) || null,
  }));

  // Apply sorting
  switch (sortBy) {
    case 'popular':
      return postsWithData.sort((a, b) => {
        const scoreA = a.upvotes - a.downvotes;
        const scoreB = b.upvotes - b.downvotes;
        if (scoreB !== scoreA) return scoreB - scoreA;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

    case 'unpopular':
      return postsWithData.sort((a, b) => {
        const scoreA = a.upvotes - a.downvotes;
        const scoreB = b.upvotes - b.downvotes;
        if (scoreA !== scoreB) return scoreA - scoreB;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

    case 'comments':
      return postsWithData.sort((a, b) => {
        if (b.reply_count !== a.reply_count) return b.reply_count - a.reply_count;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

    default: // 'recent'
      return postsWithData.sort((a, b) => {
        // Pinned posts first
        if (a.is_pinned && !b.is_pinned) return -1;
        if (!a.is_pinned && b.is_pinned) return 1;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
  }
}

export function useForumStats(reviewId: string) {
  return useQuery({
    queryKey: ['forum-stats', reviewId],
    queryFn: async (): Promise<ForumStats> => {
      const supabase = createClient();
      // Get total posts count (only top-level comments, not replies, including deleted)
      const { count: totalPosts, error: postsError } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('review_id', reviewId)
        .is('parent_id', null);

      if (postsError) {
        console.error('Error fetching posts count:', postsError);
        throw postsError;
      }

      // Get active users count (users who posted in the last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: activeUsersData, error: usersError } = await supabase
        .from('comments')
        .select('author_id')
        .eq('review_id', reviewId)
        .eq('is_deleted', false)
        .gte('created_at', thirtyDaysAgo.toISOString());

      if (usersError) {
        console.error('Error fetching active users:', usersError);
        throw usersError;
      }

      const uniqueUsers = new Set(activeUsersData?.map(comment => comment.author_id) || []);

      // Get most recent activity (exclude deleted comments for activity)
      const { data: recentActivity, error: activityError } = await supabase
        .from('comments')
        .select('created_at')
        .eq('review_id', reviewId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })
        .limit(1);

      if (activityError) {
        console.error('Error fetching recent activity:', activityError);
        throw activityError;
      }

      return {
        total_posts: totalPosts || 0,
        active_users: uniqueUsers.size,
        recent_activity: recentActivity?.[0]?.created_at || null
      };
    },
    enabled: !!reviewId,
  });
}
