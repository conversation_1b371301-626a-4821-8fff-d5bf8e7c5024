
'use server';
/**
 * @fileOverview AI flow for generating SEO settings for game reviews.
 *
 * - generateSeoSettings - A function that calls the Genkit flow to generate SEO.
 * - GenerateSeoInput - The input type for the generateSeoSettings function.
 * - GenerateSeoOutput - The return type for the generateSeoSettings function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateSeoInputSchema = z.object({
  reviewTitle: z.string().describe('The title of the game review.'),
  gameName: z.string().describe('The name of the game being reviewed.'),
  reviewContent: z.string().describe('The main content of the game review (plain text).'),
});
export type GenerateSeoInput = z.infer<typeof GenerateSeoInputSchema>;

const GenerateSeoOutputSchema = z.object({
  metaTitle: z.string().describe('Generated SEO meta title for the review (50-60 characters).'),
  metaDescription: z.string().describe('Generated SEO meta description for the review (150-160 characters).'),
  focusKeyword: z.string().describe('Generated primary focus keyword or keyphrase for the review.'),
});
export type GenerateSeoOutput = z.infer<typeof GenerateSeoOutputSchema>;

export async function generateSeoSettings(input: GenerateSeoInput): Promise<GenerateSeoOutput> {
  return generateSeoFlow(input);
}

const seoPrompt = ai.definePrompt({
  name: 'generateSeoPrompt',
  input: {schema: GenerateSeoInputSchema},
  output: {schema: GenerateSeoOutputSchema},
  prompt: `You are an expert SEO content strategist specializing in video game reviews.
Given the following review details, generate an optimized Meta Title, Meta Description, and a primary Focus Keyword.

Review Title: {{reviewTitle}}
Game Name: {{gameName}}
Review Content:
{{reviewContent}}

Instructions for output:
- metaTitle: Should be catchy, include the game name and optionally a key aspect of the review. Aim for 50-60 characters.
- metaDescription: Should be a compelling summary of the review, encouraging clicks. Include the game name and relevant keywords. Aim for 150-160 characters.
- focusKeyword: The most important keyword or keyphrase for this review (e.g., "{{gameName}} review", "best RPG 2024 for {{gameName}}").
`,
});

const generateSeoFlow = ai.defineFlow(
  {
    name: 'generateSeoFlow',
    inputSchema: GenerateSeoInputSchema,
    outputSchema: GenerateSeoOutputSchema,
  },
  async (input) => {
    const {output} = await seoPrompt(input);
    if (!output) {
      throw new Error('Failed to generate SEO settings. AI model did not return an output.');
    }
    return output;
  }
);
