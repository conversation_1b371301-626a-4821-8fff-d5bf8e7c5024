'use client';

import React, { useState, useEffect, memo } from 'react';
import { Gamepad2, BookOpen, Palette, Sparkles } from 'lucide-react';
import type { Review } from '@/lib/types';

// Local interface for display purposes with icon mapping
interface CriterionWithIcon {
  id: string;
  name: string;
  score: number;
  icon: React.ComponentType<any>;
}

// Helper function to map scoring criteria to icons
function mapCriteriaToIcons(scoringCriteria: { id: string; name: string; score: number }[]): CriterionWithIcon[] {
  const iconMap: Record<string, React.ComponentType<any>> = {
    gameplay: Gamepad2,
    story: BookOpen,
    'art-style': Palette,
    'fun-factor': Sparkles,
    graphics: Palette,
    sound: Sparkles,
    performance: Gamepad2,
    value: BookOpen,
  };

  return scoringCriteria.map(criterion => ({
    ...criterion,
    icon: iconMap[criterion.id.toLowerCase()] || iconMap[criterion.name.toLowerCase().replace(/\s+/g, '-')] || Gamepad2
  }));
}

// Central Score Display
interface CentralScoreProps {
  score: number;
  isActive: boolean;
  onToggleRadar: () => void;
  showRadar: boolean;
}

const CentralScore = memo(({ score, isActive, onToggleRadar, showRadar }: CentralScoreProps) => {
  const [animatedScore, setAnimatedScore] = useState(0);
  
  useEffect(() => {
    if (isActive && score > 0) {
      let currentScore = 0;
      const increment = score / 50; // Animate over ~50 frames
      const timer = setInterval(() => {
        currentScore += increment;
        if (currentScore >= score) {
          setAnimatedScore(score);
          clearInterval(timer);
        } else {
          setAnimatedScore(Math.floor(currentScore));
        }
      }, 20); // 20ms intervals for smooth animation
      
      return () => clearInterval(timer);
    }
  }, [score, isActive]);

  const getTier = (score: number) => {
    if (score >= 95) return { name: 'Legendary', rank: 'SSS', color: 'text-yellow-400' };
    if (score >= 90) return { name: 'Epic', rank: 'SS', color: 'text-orange-400' };
    if (score >= 85) return { name: 'Excellent', rank: 'S', color: 'text-green-400' };
    if (score >= 80) return { name: 'Great', rank: 'A+', color: 'text-blue-400' };
    if (score >= 75) return { name: 'Good', rank: 'A', color: 'text-slate-400' };
    if (score >= 70) return { name: 'Decent', rank: 'B+', color: 'text-slate-500' };
    return { name: 'Average', rank: 'B', color: 'text-slate-600' };
  };

  const tier = getTier(animatedScore);

  return (
    <div 
      className="absolute left-1/2 transform -translate-x-1/2 !z-[9999] cursor-pointer group"
      style={{ top: '35%', transform: 'translate(-50%, -50%)' }}
      onClick={onToggleRadar}
    >
      <div className={`flex flex-col items-center justify-center w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 rounded-full border-2 transition-all duration-500 ${
        showRadar 
          ? 'bg-slate-800 border-slate-600 scale-90' 
          : 'bg-black border-slate-700 hover:border-slate-500 hover:scale-105'
      }`}>
        <div className={`text-xl sm:text-2xl md:text-3xl font-bold font-mono transition-all duration-300 ${tier.color}`}>
          {Math.round(animatedScore)}
        </div>
        <div className="text-xs text-slate-400 font-mono">Score</div>
      </div>
      
      {!showRadar && (
        <div className="absolute inset-0 rounded-full border-2 border-slate-600 animate-ping opacity-20"></div>
      )}
    </div>
  );
});


// Circle positioned criterion labels
interface CircleLabelsProps {
  criteria: CriterionWithIcon[];
  showRadar: boolean;
}

const CircleLabels = memo(({ criteria, showRadar }: CircleLabelsProps) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const getCirclePosition = (index: number): { x: number; y: number } => {
    const positions = [
      { x: 25, y: -25 }, // Top-left: Gameplay
      { x: 75, y: -25 }, // Top-right: Story
      { x: 5, y: 15 },   // Middle-left: Art Style (closer but no overlap)
      { x: 95, y: 15 }   // Middle-right: Fun Factor (closer but no overlap)
    ];

    return positions[index] || { x: 50, y: 50 };
  };

  const getDisplayName = (criterion: CriterionWithIcon): string => {
    if (criterion.id === 'gameplay' || criterion.name.toLowerCase().includes('gameplay')) return 'Gameplay';
    if (criterion.id === 'story' || criterion.name.toLowerCase().includes('story')) return 'Story';
    if (criterion.id === 'art-style' || criterion.id === 'visuals' || criterion.name.toLowerCase().includes('art') || criterion.name.toLowerCase().includes('visual')) return 'Art';
    if (criterion.id === 'fun-factor' || criterion.id === 'innovation' || criterion.name.toLowerCase().includes('fun') || criterion.name.toLowerCase().includes('innovation')) return 'Fun';
    return criterion.name;
  };

  if (!showRadar) return null;

  return (
    <>
      {criteria.map((criterion, index) => {
        const pos = getCirclePosition(index);
        const displayName = getDisplayName(criterion);
        
        return (
          <div
            key={criterion.id || index}
            className="absolute transform -translate-x-1/2 group cursor-pointer z-50"
            style={{
              left: `${pos.x}%`,
              top: `${pos.y}%`,
              animation: `floatIn 0.6s ease-out ${0.3 + index * 0.15}s both`
            }}
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
          >
            <div className="flex flex-col items-center">
              <div className="bg-slate-800 border border-slate-600 rounded-full p-2 sm:p-3 md:p-4 group-hover:scale-110 group-hover:bg-slate-700 transition-all duration-300 flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12">
                <div className="text-xs sm:text-sm md:text-base font-bold text-slate-200 font-mono">
                  {criterion.score}
                </div>
              </div>
              <div className="text-xs sm:text-sm text-slate-400 font-mono text-center mt-1 whitespace-nowrap">
                {displayName}
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
});

interface CleanReviewComponentProps {
  review: Review;
  gameCoverUrl?: string;
  onToggleCover?: (show: boolean) => void;
}

// Main Component
const CleanReviewComponent = ({ 
  review: reviewProp, 
  gameCoverUrl, 
  onToggleCover 
}: CleanReviewComponentProps) => {
  const [showRadar, setShowRadar] = useState(false);
  const [animationStarted, setAnimationStarted] = useState(false);

  // Return early if no review data is provided
  if (!reviewProp) {
    return null;
  }

  const review: Review = reviewProp;

  // Debug logging for cover URL issues
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const finalCoverUrl = gameCoverUrl || review.igdbCoverUrl;
      console.log('ReviewScoreComponent Debug:', {
        gameCoverUrlProp: gameCoverUrl,
        reviewIgdbCoverUrl: review.igdbCoverUrl,
        reviewGameName: review.gameName,
        finalCoverUrl,
        hasCoverUrl: !!finalCoverUrl,
        // ENHANCED: Additional IGDB data verification
        reviewId: review.id,
        igdbId: review.igdbId,
        hasIgdbData: !!(review.igdbId || review.igdbCoverUrl || review.developers?.length || review.publishers?.length),
        developers: review.developers,
        publishers: review.publishers,
        platforms: review.platforms,
        genres: review.genres,
        releaseDate: review.releaseDate,
        aggregatedRating: review.aggregatedRating
      });
      
      // ADDED: Specific cover URL validation
      if (!finalCoverUrl) {
        console.warn('No IGDB cover URL available for review:', {
          reviewId: review.id,
          gameName: review.gameName,
          propCoverUrl: gameCoverUrl,
          reviewCoverUrl: review.igdbCoverUrl,
          suggestion: 'Check if IGDB data was properly stored in database'
        });
      }
    }
  }, [gameCoverUrl, review.igdbCoverUrl, review.gameName, review.id, review.igdbId, review.developers, review.publishers]);

  // Map scoring criteria to include icons for display
  const criteriaWithIcons = mapCriteriaToIcons(review.scoringCriteria);

  useEffect(() => {
    const timer = setTimeout(() => setAnimationStarted(true), 100);
    return () => clearInterval(timer);
  }, []);

  const toggleRadar = () => {
    const newState = !showRadar;
    setShowRadar(newState);
    if (onToggleCover) {
      onToggleCover(newState);
    }
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes floatIn {
            0% { opacity: 0; transform: translateX(-50%) scale(0.5) translateY(20px); }
            100% { opacity: 1; transform: translateX(-50%) scale(1) translateY(0); }
          }
          
          @keyframes coverSlideUp {
            0% { 
              opacity: 0; 
              transform: translate(-50%, 100px) scale(0.6);
            }
            60% {
              opacity: 0.6;
              transform: translate(-50%, -20px) scale(1.1);
            }
            100% { 
              opacity: 0.8; 
              transform: translate(-50%, -50%) scale(1);
            }
          }
          
          @keyframes coverSlideDown {
            0% { 
              opacity: 0.8; 
              transform: translate(-50%, -50%) scale(1);
            }
            40% {
              opacity: 0.4;
              transform: translate(-50%, 20px) scale(0.9);
            }
            100% { 
              opacity: 0; 
              transform: translate(-50%, 80px) scale(0.5);
            }
          }
          
          .cover-slide-up {
            animation: coverSlideUp 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
          }
          
          .cover-slide-down {
            animation: coverSlideDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          }
        `
      }} />
      
      <div className="p-2 sm:p-4 w-full max-w-xs sm:max-w-sm md:max-w-lg mx-auto relative !z-[9999] isolate">
        <div className="relative w-64 h-32 sm:w-80 sm:h-36 md:w-96 md:h-40 mx-auto">
          {/* Game Cover Image - REMOVED: Only show in banner, not in score component */}
          
          {!showRadar && animationStarted && (
            <div className="absolute left-1/2 transform -translate-x-1/2 text-xs text-slate-500 font-mono text-center opacity-60 animate-pulse !z-[9999]" style={{ top: '70%' }}>
              click me
            </div>
          )}
          
          <CentralScore
            score={review.overallScore}
            isActive={animationStarted}
            onToggleRadar={toggleRadar}
            showRadar={showRadar}
          />
          
          <CircleLabels
            criteria={criteriaWithIcons}
            showRadar={showRadar}
          />
        </div>
      </div>
    </>
  );
};

CentralScore.displayName = 'CentralScore';
CircleLabels.displayName = 'CircleLabels';

export default CleanReviewComponent;