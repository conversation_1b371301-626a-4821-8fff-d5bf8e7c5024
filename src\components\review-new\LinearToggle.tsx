import React, { useState } from 'react';
import { Monitor, Sparkles, Target, ChevronDown, ChevronUp } from 'lucide-react';
import PlatformIcon from './PlatformIcon';

interface LinearToggleProps {
  platforms: (string | { name: string })[];
  genres: (string | { name: string })[];
  tags?: (string | { name: string })[];
}

const LinearToggle = ({ platforms = [], genres = [], tags = [] }: LinearToggleProps) => {
  const [activeCategory, setActiveCategory] = useState<'platforms' | 'genres' | 'tags'>('platforms');
  const [isExpanded, setIsExpanded] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  const categories = [
    { 
      key: 'platforms' as const, 
      label: 'Platforms', 
      icon: Monitor, 
      items: platforms,
      color: 'bg-blue-600',
      hoverColor: 'hover:bg-blue-700',
      textColor: 'text-blue-100'
    },
    { 
      key: 'genres' as const, 
      label: 'Genres', 
      icon: Sparkles, 
      items: genres,
      color: 'bg-purple-600',
      hoverColor: 'hover:bg-purple-700',
      textColor: 'text-purple-100'
    },
    { 
      key: 'tags' as const, 
      label: 'Tags', 
      icon: Target, 
      items: tags,
      color: 'bg-orange-600',
      hoverColor: 'hover:bg-orange-700',
      textColor: 'text-orange-100'
    }
  ];

  const currentCategory = categories.find(cat => cat.key === activeCategory);
  const currentItems = currentCategory?.items || [];

  // Helper function to extract clean text from item
  const getItemText = (item: string | { name: string }): string => {
    // Handle string items
    if (typeof item === 'string') {
      // Check if string looks like JSON
      if (item.includes('{"name":') || item.includes("{\"name\":")) {
        try {
          const parsed = JSON.parse(item);
          return parsed.name || item;
        } catch {
          // Extract name from JSON-like string using regex
          const nameMatch = item.match(/"name":"([^"]+)"/);
          if (nameMatch) {
            return nameMatch[1];
          }
          return item.replace(/[{}'"]/g, '').replace('name:', '').trim() || 'Unknown';
        }
      }
      return item;
    }
    
    // Handle object items
    if (typeof item === 'object' && item !== null) {
      if (item.name) {
        return item.name;
      }
      // Try to extract from stringified object
      const str = JSON.stringify(item);
      const nameMatch = str.match(/"name":"([^"]+)"/);
      if (nameMatch) {
        return nameMatch[1];
      }
    }
    
    return 'Unknown';
  };

  const handleCategoryClick = (categoryKey: typeof activeCategory) => {
    const category = categories.find(cat => cat.key === categoryKey);
    if (category && category.items.length > 0) {
      setActiveCategory(categoryKey);
      if (!isExpanded) {
        setIsExpanded(true);
      }
    }
  };

  return (
    <div className="w-full">
      {/* Category Selector */}
             <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          {categories.map((category) => {
            const Icon = category.icon;
            const isActive = activeCategory === category.key;
            const hasItems = category.items && category.items.length > 0;
            
            return (
              <button
                key={category.key}
                onClick={() => handleCategoryClick(category.key)}
                disabled={!hasItems}
                                 className={`flex items-center gap-1.5 px-2 py-1 rounded text-xs font-medium 
                   transition-all duration-200 ${
                  isActive && hasItems
                    ? `${category.color} ${category.textColor} border border-gray-500`
                    : hasItems
                      ? `bg-gray-700 text-gray-300 border border-gray-600 ${category.hoverColor}`
                      : 'bg-gray-800 text-gray-600 border border-gray-700 cursor-not-allowed'
                }`}
              >
                                 <Icon className="w-3 h-3" />
                 <span>
                   {category.label}
                 </span>
                                 {hasItems && (
                   <span className="text-xs bg-gray-800 text-gray-300 px-1 py-0.5 rounded border border-gray-600">
                     {category.items.length}
                   </span>
                 )}
              </button>
            );
          })}
        </div>
        
        {/* Expand/Collapse Toggle */}
        {currentItems.length > 0 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
                         className="flex items-center gap-1 px-1.5 py-0.5 text-xs bg-gray-700 text-gray-300 border border-gray-600 rounded hover:bg-gray-600 transition-colors"
          >
                         <span>
               {isExpanded ? 'Hide' : 'Show'}
             </span>
                         <div className={`transition-transform duration-200 ${isExpanded ? 'rotate-180' : 'rotate-0'}`}>
               {isExpanded ? 
                 <ChevronUp className="w-3 h-3" /> : 
                 <ChevronDown className="w-3 h-3" />
               }
             </div>
          </button>
        )}
      </div>

      {/* Content Display */}
      {isExpanded && currentItems.length > 0 && (
                 <div className="flex flex-wrap gap-1">
          {currentItems.map((item, index) => {
            const itemText = getItemText(item);
            const itemKey = `${activeCategory}-${itemText}-${index}`;
            
            return (
              <div
                key={itemKey}
                onMouseEnter={() => setHoveredItem(itemKey)}
                onMouseLeave={() => setHoveredItem(null)}
                                 className={`flex items-center px-1.5 py-0.5 rounded text-xs font-mono 
                   transition-all duration-200 border cursor-pointer ${
                  hoveredItem === itemKey 
                    ? `${currentCategory?.color} ${currentCategory?.textColor} border-gray-500`
                    : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'
                }`}
                             >
                 {/* Text Content */}
                 <span>
                   {itemText}
                 </span>
              </div>
            );
          })}
        </div>
      )}

      
    </div>
  );
};

export default LinearToggle;
