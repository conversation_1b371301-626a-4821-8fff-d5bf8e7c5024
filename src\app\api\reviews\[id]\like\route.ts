import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface LikeResponse {
  success: boolean;
  liked: boolean;
  likeCount: number;
  error?: string;
}

/**
 * POST /api/reviews/[id]/like - Toggle like for a review
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<LikeResponse>> {
  try {
    const { id: reviewId } = await params;
    
    if (!reviewId) {
      return NextResponse.json(
        { success: false, liked: false, likeCount: 0, error: 'Review ID is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { success: false, liked: false, likeCount: 0, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if review exists
    const { data: review, error: reviewError } = await supabase
      .from('reviews')
      .select('id, like_count')
      .eq('id', reviewId)
      .single();

    if (reviewError || !review) {
      return NextResponse.json(
        { success: false, liked: false, likeCount: 0, error: 'Review not found' },
        { status: 404 }
      );
    }

    // Check if user already liked this review
    const { data: existingLike, error: likeCheckError } = await supabase
      .from('review_likes')
      .select('id')
      .eq('review_id', reviewId)
      .eq('user_id', user.id)
      .single();

    if (likeCheckError && likeCheckError.code !== 'PGRST116') {
      console.error('Error checking existing like:', likeCheckError);
      return NextResponse.json(
        { success: false, liked: false, likeCount: review.like_count || 0, error: 'Failed to check like status' },
        { status: 500 }
      );
    }

    let newLiked = false;
    let newLikeCount = review.like_count || 0;

    if (existingLike) {
      // Unlike: Remove the like
      const { error: deleteError } = await supabase
        .from('review_likes')
        .delete()
        .eq('id', existingLike.id);

      if (deleteError) {
        console.error('Error removing like:', deleteError);
        return NextResponse.json(
          { success: false, liked: true, likeCount: newLikeCount, error: 'Failed to remove like' },
          { status: 500 }
        );
      }

      // Decrement like count
      newLikeCount = Math.max(0, newLikeCount - 1);
      newLiked = false;
    } else {
      // Like: Add the like
      const { error: insertError } = await supabase
        .from('review_likes')
        .insert({
          review_id: reviewId,
          user_id: user.id,
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error adding like:', insertError);
        return NextResponse.json(
          { success: false, liked: false, likeCount: newLikeCount, error: 'Failed to add like' },
          { status: 500 }
        );
      }

      // Increment like count
      newLikeCount = newLikeCount + 1;
      newLiked = true;
    }

    // Update the cached like count in reviews table
    const { error: updateError } = await supabase
      .from('reviews')
      .update({ like_count: newLikeCount })
      .eq('id', reviewId);

    if (updateError) {
      console.error('Error updating like count:', updateError);
      // Don't fail the operation if cache update fails
    }

    return NextResponse.json({
      success: true,
      liked: newLiked,
      likeCount: newLikeCount
    });

  } catch (error) {
    console.error('Error in like endpoint:', error);
    return NextResponse.json(
      { success: false, liked: false, likeCount: 0, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/reviews/[id]/like - Get like status for current user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<LikeResponse>> {
  try {
    const { id: reviewId } = await params;
    
    if (!reviewId) {
      return NextResponse.json(
        { success: false, liked: false, likeCount: 0, error: 'Review ID is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user (optional for GET)
    const { data: { user } } = await supabase.auth.getUser();

    // Get review like count
    const { data: review, error: reviewError } = await supabase
      .from('reviews')
      .select('like_count')
      .eq('id', reviewId)
      .single();

    if (reviewError || !review) {
      return NextResponse.json(
        { success: false, liked: false, likeCount: 0, error: 'Review not found' },
        { status: 404 }
      );
    }

    let userLiked = false;

    // Check if current user liked this review (if authenticated)
    if (user) {
      const { data: existingLike } = await supabase
        .from('review_likes')
        .select('id')
        .eq('review_id', reviewId)
        .eq('user_id', user.id)
        .single();

      userLiked = !!existingLike;
    }

    return NextResponse.json({
      success: true,
      liked: userLiked,
      likeCount: review.like_count || 0
    });

  } catch (error) {
    console.error('Error in like status endpoint:', error);
    return NextResponse.json(
      { success: false, liked: false, likeCount: 0, error: 'Internal server error' },
      { status: 500 }
    );
  }
}