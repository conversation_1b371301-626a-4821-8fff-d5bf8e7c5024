'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
// import { useSteamGridDBIcon } from '@/hooks/useSteamGridDBIcon';
import {
  Calendar,
  Star,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Share2,
  Copy,
  ExternalLink,
  Clock,
  Monitor,
  Tag,
  Heart,
  Gamepad2,
  Shield,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import Link from 'next/link';
import Image from 'next/image';
import type { Review } from '@/lib/types';

export interface ReviewCardProps {
  review: Review;
  viewMode: 'list';
  onRefresh?: () => void;
  onPrivacyToggle?: (reviewId: string, isPrivate: boolean) => void;
  onSoftDelete?: (reviewId: string) => void;
}

export function ReviewCard({
  review,
  viewMode,
  onRefresh,
  onPrivacyToggle,
  onSoftDelete
}: ReviewCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [isUpdatingPrivacy, setIsUpdatingPrivacy] = useState(false);
  
  // Fetch SteamGridDB icon for the game
  // const { iconUrl: steamGridIcon, isLoading: iconLoading } = useSteamGridDBIcon(review.gameName);
  const steamGridIcon = null;
  const iconLoading = false;

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'Draft';
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      return dateObj.toLocaleDateString();
    } catch {
      return 'Invalid Date';
    }
  };

  // Get status info


  // Handle share functionality
  const handleShare = async () => {
    if (navigator.share && review.slug) {
      try {
        await navigator.share({
          title: review.title,
          text: `Check out my review of ${review.gameName}`,
          url: `${window.location.origin}/reviews/${review.slug}`
        });
      } catch (err) {
        // Fallback to copy to clipboard
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  const handleCopyLink = async () => {
    if (review.slug) {
      try {
        await navigator.clipboard.writeText(`${window.location.origin}/reviews/${review.slug}`);
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy link:', err);
      }
    }
  };

  const handlePrivacyToggle = async () => {
    if (!onPrivacyToggle) return;

    setIsUpdatingPrivacy(true);
    try {
      await onPrivacyToggle(review.id, !review.is_private);
      onRefresh?.();
    } catch (error) {
      console.error('Failed to update privacy:', error);
    } finally {
      setIsUpdatingPrivacy(false);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      if (onSoftDelete) {
        await onSoftDelete(review.id);
      }
      onRefresh?.();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Failed to delete review:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Helper function to truncate game name
  const truncateGameName = (name: string, maxLength: number = 15) => {
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength) + '...';
  };

  // Enhanced list view layout with increased height and game icon
  return (
    <TooltipProvider>
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.15 }}
      className="group bg-white/70 dark:bg-slate-800/50 border border-slate-200/80 dark:border-slate-700/40 rounded-lg overflow-hidden hover:border-purple-300/80 dark:hover:border-purple-500/40 hover:bg-white/90 dark:hover:bg-slate-800/70 transition-all duration-200 shadow-sm hover:shadow-xs"
    >
      <div className="p-4">
        <div className="flex items-center gap-4">
          {/* Game Icon - 45x45 */}
          <div className="flex-shrink-0 w-[45px] h-[45px] bg-slate-800/60 dark:bg-slate-700/60 border border-slate-700/50 dark:border-slate-600/50 rounded-lg overflow-hidden">
            {steamGridIcon || review.mainImageUrl || review.igdbCoverUrl ? (
              <Image
                src={steamGridIcon || review.mainImageUrl || review.igdbCoverUrl || ''}
                alt={review.gameName}
                width={45}
                height={45}
                className="w-full h-full object-cover"
                sizes="45px"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 dark:from-slate-700 dark:via-slate-600 dark:to-slate-700 relative">
                {/* Background pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20"></div>
                
                {/* Main gamepad icon */}
                <div className="relative z-10">
                  <Gamepad2 className="text-slate-400 dark:text-slate-500" size={18} />
                </div>
                
                {/* Decorative elements */}
                <div className="absolute top-1 right-1 w-1 h-1 bg-purple-500/60 rounded-full"></div>
                <div className="absolute bottom-1 left-1 w-1 h-1 bg-blue-500/60 rounded-full"></div>
                <div className="absolute top-1 left-1 w-0.5 h-0.5 bg-slate-500/60 rounded-full"></div>
              </div>
            )}
          </div>

          {/* Content - Vertically Centered */}
          <div className="flex-1 min-w-0 flex items-center">
            <div className="flex-1 min-w-0">
              {/* Title Row */}
              <div className="flex items-center gap-3 mb-2">
                <Link 
                  href={`/reviews/view/${review.slug}`} 
                  className="hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                >
                  <h3 className="text-base font-semibold text-slate-800 dark:text-slate-200 truncate" title={review.title}>
                    {review.title}
                  </h3>
                </Link>
                
                {/* Privacy Badge */}
                {review.is_private && (
                  <div className="text-orange-500 dark:text-orange-400" title="Private">
                    <Shield size={14} />
                  </div>
                )}
              </div>
              
              {/* Metadata Row */}
              <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400">
                {review.gameName.length > 15 ? (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-slate-600 dark:text-slate-300 font-medium cursor-help">
                        {truncateGameName(review.gameName)}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="font-medium">{review.gameName}</p>
                    </TooltipContent>
                  </Tooltip>
                ) : (
                  <span className="text-slate-600 dark:text-slate-300 font-medium">{review.gameName}</span>
                )}
                <span className="text-slate-400 dark:text-slate-500">•</span>
                <span>{formatDate(review.publishDate || review.createdAt)}</span>
                <span className="text-slate-400 dark:text-slate-500">•</span>
                
                {/* Views */}
                <span className="flex items-center gap-1">
                  <Eye size={12} className="text-slate-400 dark:text-slate-500" />
                  {(review.view_count || 0).toLocaleString()}
                </span>
                
                <span className="text-slate-400 dark:text-slate-500">•</span>
                
                {/* Likes */}
                <span className="flex items-center gap-1">
                  <Heart size={12} className="text-slate-400 dark:text-slate-500" />
                  {review.likes_count || 0}
                </span>
              </div>
            </div>
            
            {/* Right side: Always Visible Actions Dropdown */}
            <div className="flex-shrink-0 ml-4 flex items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9 p-0 text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 transition-all border border-slate-300/50 dark:border-slate-600/50 rounded-lg shadow-sm"
                  >
                    <MoreHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  align="end" 
                  className="w-56 bg-slate-900/80 dark:bg-slate-900/80 backdrop-blur-md border border-slate-700/50 shadow-xl rounded-xl p-2"
                  sideOffset={5}
                >
                  {/* Primary Actions */}
                  <div className="space-y-1">
                    <DropdownMenuItem asChild>
                      <Link href={`/reviews/view/${review.slug}`} className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors">
                        <Eye size={16} className="mr-3 text-slate-400" />
                        View Review
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem asChild>
                      <Link href={`/reviews/new?edit=${review.id}`} className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors">
                        <Edit size={16} className="mr-3 text-slate-400" />
                        Edit Review
                      </Link>
                    </DropdownMenuItem>
                  </div>

                  <DropdownMenuSeparator className="my-2 bg-slate-700/50" />
                  
                  {/* Secondary Actions */}
                  <div className="space-y-1">
                    {/* Only show share button for non-draft reviews */}
                    {review.status !== 'draft' && (
                      <DropdownMenuItem 
                        onClick={handleShare}
                        className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors"
                      >
                        <Share2 size={16} className="mr-3 text-slate-400" />
                        Share Review
                      </DropdownMenuItem>
                    )}

                    {/* Only show privacy toggle for non-draft reviews */}
                    {review.status !== 'draft' && (
                      <DropdownMenuItem 
                        onClick={handlePrivacyToggle}
                        disabled={isUpdatingPrivacy}
                        className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors disabled:opacity-50"
                      >
                        {review.is_private ? (
                          <>
                            <Eye size={16} className="mr-3 text-slate-400" />
                            Make Public
                          </>
                        ) : (
                          <>
                            <EyeOff size={16} className="mr-3 text-slate-400" />
                            Make Private
                          </>
                        )}
                      </DropdownMenuItem>
                    )}
                  </div>

                  <DropdownMenuSeparator className="my-2 bg-slate-700/50" />
                  
                  {/* Destructive Action */}
                  <div className="space-y-1">
                    <DropdownMenuItem 
                      onClick={() => setShowDeleteDialog(true)}
                      disabled={isDeleting}
                      className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors disabled:opacity-50"
                    >
                      <Trash2 size={16} className="mr-3 text-slate-400" />
                      Delete Review
                    </DropdownMenuItem>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </motion.div>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
      <AlertDialogContent className="bg-slate-900/95 border border-slate-700/50 backdrop-blur-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-slate-200 font-mono">
            Delete Review: {review.title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-slate-400 font-mono">
            This will permanently remove "{review.gameName}" review from your profile and make it 
            inaccessible to other users. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel 
            className="bg-slate-800/50 border-slate-700/50 text-slate-300 hover:bg-slate-700/50 font-mono"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-900/80 hover:bg-red-800/80 text-red-100 border border-red-700/50 font-mono"
          >
            {isDeleting ? 'Deleting...' : 'Delete Review'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </TooltipProvider>
  );
}
