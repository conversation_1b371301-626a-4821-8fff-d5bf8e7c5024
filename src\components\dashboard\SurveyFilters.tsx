'use client';

import { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Filter,
  X,
  Search,
  Calendar,
  Monitor,
  Gamepad2,
  HardDrive,
  ChevronDown,
  RotateCcw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
// Removed Collapsible import - using simple state-based solution instead
import { cn } from '@/lib/utils';
import type { DashboardFilters } from '@/types/dashboard';

interface SurveyFiltersProps {
  filters: DashboardFilters['surveys'];
  onFiltersChange: (filters: DashboardFilters['surveys']) => void;
  totalCount: number;
  filteredCount: number;
  className?: string;
}

export function SurveyFilters({
  filters,
  onFiltersChange,
  totalCount,
  filteredCount,
  className
}: SurveyFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleFilterChange = useCallback((key: keyof DashboardFilters['surveys'], value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  }, [filters, onFiltersChange]);

  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    // Implement search logic here if needed
  }, []);

  const clearAllFilters = useCallback(() => {
    onFiltersChange({});
    setSearchQuery('');
  }, [onFiltersChange]);

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== 'all'
  ) || searchQuery.length > 0;

  const activeFilterCount = Object.values(filters).filter(value => 
    value !== undefined && value !== '' && value !== 'all'
  ).length + (searchQuery.length > 0 ? 1 : 0);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
        <Input
          placeholder="Search surveys by game, hardware, or platform..."
          value={searchQuery}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10 bg-slate-900/60 border-slate-700/50 text-slate-200 placeholder-slate-400"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleSearchChange('')}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X size={14} />
          </Button>
        )}
      </div>

      {/* Filter Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setIsExpanded(!isExpanded)}
            type="button"
            aria-expanded={isExpanded}
            aria-controls="filter-panel"
          >
            <Filter size={16} />
            <span>Filters</span>
            {activeFilterCount > 0 && (
              <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                {activeFilterCount}
              </span>
            )}
            <ChevronDown
              size={16}
              className={cn("transition-transform", isExpanded && "rotate-180")}
            />
          </Button>

          {isExpanded && (
            <motion.div
              id="filter-panel"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 space-y-4"
            >
              {/* Quick Filters Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Device Type Filter */}
                <div className="space-y-2">
                  <Label className="text-slate-300 flex items-center gap-2">
                    <Monitor size={14} />
                    Device Type
                  </Label>
                  <Select
                    value={filters.deviceType || 'all'}
                    onValueChange={(value) => handleFilterChange('deviceType', value === 'all' ? undefined : value)}
                  >
                    <SelectTrigger className="bg-slate-800/50 border-slate-600/50">
                      <SelectValue placeholder="All devices" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Devices</SelectItem>
                      <SelectItem value="desktop">
                        <div className="flex items-center gap-2">
                          <Monitor size={14} />
                          Desktop
                        </div>
                      </SelectItem>
                      <SelectItem value="laptop">
                        <div className="flex items-center gap-2">
                          <HardDrive size={14} />
                          Laptop
                        </div>
                      </SelectItem>
                      <SelectItem value="handheld">
                        <div className="flex items-center gap-2">
                          <Gamepad2 size={14} />
                          Handheld
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Platform Filter */}
                <div className="space-y-2">
                  <Label className="text-slate-300">Platform</Label>
                  <Select
                    value={filters.platform || 'all'}
                    onValueChange={(value) => handleFilterChange('platform', value === 'all' ? undefined : value)}
                  >
                    <SelectTrigger className="bg-slate-800/50 border-slate-600/50">
                      <SelectValue placeholder="All platforms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Platforms</SelectItem>
                      <SelectItem value="PC">PC</SelectItem>
                      <SelectItem value="Steam Deck">Steam Deck</SelectItem>
                      <SelectItem value="ROG Ally">ROG Ally</SelectItem>
                      <SelectItem value="Legion Go">Legion Go</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort By */}
                <div className="space-y-2">
                  <Label className="text-slate-300">Sort By</Label>
                  <Select
                    value={filters.sortBy || 'created_at'}
                    onValueChange={(value) => handleFilterChange('sortBy', value as any)}
                  >
                    <SelectTrigger className="bg-slate-800/50 border-slate-600/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at">Date Created</SelectItem>
                      <SelectItem value="game_title">Game Title</SelectItem>
                      <SelectItem value="device_type">Device Type</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Sort Order */}
              <div className="flex items-center gap-4">
                <Label className="text-slate-300">Sort Order:</Label>
                <div className="flex gap-2">
                  <Button
                    variant={filters.sortOrder === 'desc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', 'desc')}
                  >
                    Newest First
                  </Button>
                  <Button
                    variant={filters.sortOrder === 'asc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', 'asc')}
                  >
                    Oldest First
                  </Button>
                </div>
              </div>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <div className="flex justify-end pt-2 border-t border-slate-700/50">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-slate-400 hover:text-slate-200"
                  >
                    <RotateCcw size={14} className="mr-2" />
                    Clear All Filters
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </div>

        {/* Results Count */}
        <div className="text-sm text-slate-400">
          {filteredCount === totalCount ? (
            <span>Showing all {totalCount} surveys</span>
          ) : (
            <span>
              Showing {filteredCount} of {totalCount} surveys
            </span>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      <AnimatePresence>
        {hasActiveFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex flex-wrap gap-2"
          >
            {searchQuery && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full text-sm"
              >
                <Search size={12} />
                <span>"{searchQuery}"</span>
                <button
                  type="button"
                  onClick={() => handleSearchChange('')}
                  className="hover:text-purple-200"
                  aria-label="Clear search"
                  title="Clear search"
                >
                  <X size={12} />
                </button>
              </motion.div>
            )}

            {filters.deviceType && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm"
              >
                <Monitor size={12} />
                <span>{filters.deviceType}</span>
                <button
                  type="button"
                  onClick={() => handleFilterChange('deviceType', undefined)}
                  className="hover:text-blue-200"
                  aria-label="Clear device type filter"
                  title="Clear device type filter"
                >
                  <X size={12} />
                </button>
              </motion.div>
            )}

            {filters.platform && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm"
              >
                <span>{filters.platform}</span>
                <button
                  type="button"
                  onClick={() => handleFilterChange('platform', undefined)}
                  className="hover:text-green-200"
                  aria-label="Clear platform filter"
                  title="Clear platform filter"
                >
                  <X size={12} />
                </button>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
