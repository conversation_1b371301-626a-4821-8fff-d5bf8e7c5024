// src/app/tags/[slug]/page.tsx
// Tag page for browsing reviews by tag

import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import TagPageClient from './TagPageClient';
import { createServerTagService } from '@/lib/services/tagService';

interface TagPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    page?: string;
    sort?: string;
  };
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const tagService = createServerTagService();
  const result = await tagService.getTagBySlug(params.slug);

  if (!result.success || !result.tag) {
    return {
      title: 'Tag Not Found - CriticalPixel',
      description: 'The requested tag could not be found.',
    };
  }

  const tag = result.tag;
  const tagName = tag.name;
  const description = tag.description || `Discover reviews tagged with ${tagName}. Browse gaming content and reviews related to ${tagName}.`;

  return {
    title: `${tagName} Reviews - CriticalPixel`,
    description,
    keywords: [tagName, 'gaming reviews', 'game reviews', tag.category || 'gaming'].join(', '),
    openGraph: {
      title: `${tagName} Reviews - CriticalPixel`,
      description,
      type: 'website',
      siteName: 'CriticalPixel',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${tagName} Reviews - CriticalPixel`,
      description,
    },
    alternates: {
      canonical: `/tags/${tag.slug}`,
    },
  };
}

export default async function TagPage({ params, searchParams }: TagPageProps) {
  const tagService = createServerTagService();
  
  // Get tag information
  const tagResult = await tagService.getTagBySlug(params.slug);
  
  if (!tagResult.success || !tagResult.tag) {
    notFound();
  }

  const tag = tagResult.tag;
  const page = parseInt(searchParams.page || '1');
  const limit = 20;
  const offset = (page - 1) * limit;

  // Get reviews for this tag
  const reviewsResult = await tagService.getTagReviews(tag.id, limit, offset);
  
  if (!reviewsResult.success) {
    // Handle error gracefully
    return (
      <TagPageClient
        tag={tag}
        reviews={[]}
        total={0}
        currentPage={page}
        totalPages={0}
        error="Failed to load reviews for this tag"
      />
    );
  }

  const totalPages = Math.ceil((reviewsResult.total || 0) / limit);

  return (
    <TagPageClient
      tag={tag}
      reviews={reviewsResult.reviews || []}
      total={reviewsResult.total || 0}
      currentPage={page}
      totalPages={totalPages}
    />
  );
}