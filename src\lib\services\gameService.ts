import { createServerClient } from '@/lib/supabase/server';
import { createClient as createClientBrowser } from '@/lib/supabase/client';

export interface GameData {
  id: string;
  igdb_id: number | null;
  name: string;
  slug: string;
  summary: string | null;
  release_date: string | null;
  cover_url: string | null;
  supabase_cover_url: string | null;
  aggregated_rating: number | null;
  aggregated_rating_count: number | null;
  developers: string[] | null;
  publishers: string[] | null;
  genres: string[] | null;
  platforms: string[] | null;
  game_engines: string[] | null;
  player_perspectives: string[] | null;
  time_to_beat_normally: number | null;
  time_to_beat_completely: number | null;
  created_at: string;
  updated_at: string;
}

export interface GameReviewData {
  id: string;
  title: string;
  slug: string;
  user_id: string;
  author_name: string;
  author_avatar_url: string | null;
  overall_score: number | null;
  created_at: string;
  main_image_url: string | null;
  excerpt: string | null;
  likes_count: number;
  views_count: number;
}

export interface GamePerformanceData {
  id: string;
  user_id: string;
  platform: string;
  device_type: string;
  cpu: string | null;
  gpu: string | null;
  ram: string | null;
  storage: string | null;
  resolution: string | null;
  graphics_settings: string | null;
  fps_average: number | null;
  fps_min: number | null;
  fps_max: number | null;
  performance_rating: number | null;
  created_at: string;
}

export interface GameStats {
  total_reviews: number;
  average_rating: number | null;
  total_performance_surveys: number;
  total_views: number;
  total_likes: number;
  platforms_count: number;
  recent_activity_count: number;
}

// Server-side functions
export async function getGameBySlug(slug: string): Promise<GameData | null> {
  const supabase = await createServerClient();
  
  const { data, error } = await supabase
    .from('games')
    .select('*')
    .eq('slug', slug)
    .single();

  if (error || !data) {
    console.error('Error fetching game by slug:', slug, 'Error:', error);
    
    // Let's also check what games exist for debugging
    const { data: allGames } = await supabase
      .from('games')
      .select('slug, name')
      .limit(10);
    
    console.log('Available game slugs:', allGames?.map(g => g.slug));
    return null;
  }

  return data;
}

export async function getGameReviews(
  gameId: string,
  limit: number = 10,
  offset: number = 0
): Promise<GameReviewData[]> {
  const supabase = await createServerClient();
  
  const { data, error } = await supabase
    .from('reviews')
    .select(`
      id,
      title,
      slug,
      user_id,
      author_name,
      author_avatar_url,
      overall_score,
      created_at,
      main_image_url,
      excerpt,
      likes_count,
      views_count
    `)
    .eq('game_id', gameId)
    .eq('status', 'published')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching game reviews:', error);
    return [];
  }

  return data || [];
}

export async function getGamePerformanceData(
  gameId: string,
  limit: number = 50
): Promise<GamePerformanceData[]> {
  const supabase = await createServerClient();
  
  // Get game name first
  const { data: gameData } = await supabase
    .from('games')
    .select('name')
    .eq('id', gameId)
    .single();

  if (!gameData) return [];

  const { data, error } = await supabase
    .from('performance_surveys')
    .select(`
      id,
      user_id,
      platform,
      device_type,
      cpu,
      gpu,
      ram,
      storage,
      resolution,
      graphics_settings,
      fps_average,
      fps_min,
      fps_max,
      performance_rating,
      created_at
    `)
    .eq('game_title', gameData.name)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching game performance data:', error);
    return [];
  }

  return data || [];
}

export async function getGameStats(gameId: string): Promise<GameStats> {
  const supabase = await createServerClient();
  
  // Get game name for performance surveys
  const { data: gameData } = await supabase
    .from('games')
    .select('name, platforms')
    .eq('id', gameId)
    .single();

  if (!gameData) {
    return {
      total_reviews: 0,
      average_rating: null,
      total_performance_surveys: 0,
      total_views: 0,
      total_likes: 0,
      platforms_count: 0,
      recent_activity_count: 0
    };
  }

  // Get review stats
  const { data: reviewStats } = await supabase
    .from('reviews')
    .select('overall_score, likes_count, views_count')
    .eq('game_id', gameId)
    .eq('status', 'published');

  // Get performance survey count
  const { count: performanceCount } = await supabase
    .from('performance_surveys')
    .select('*', { count: 'exact', head: true })
    .eq('game_title', gameData.name);

  // Get recent activity (reviews from last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const { count: recentActivityCount } = await supabase
    .from('reviews')
    .select('*', { count: 'exact', head: true })
    .eq('game_id', gameId)
    .eq('status', 'published')
    .gte('created_at', thirtyDaysAgo.toISOString());

  const totalReviews = reviewStats?.length || 0;
  const averageRating = reviewStats && reviewStats.length > 0
    ? reviewStats.reduce((sum, review) => sum + (review.overall_score || 0), 0) / reviewStats.filter(r => r.overall_score).length
    : null;
  
  const totalViews = reviewStats?.reduce((sum, review) => sum + (review.views_count || 0), 0) || 0;
  const totalLikes = reviewStats?.reduce((sum, review) => sum + (review.likes_count || 0), 0) || 0;

  return {
    total_reviews: totalReviews,
    average_rating: averageRating,
    total_performance_surveys: performanceCount || 0,
    total_views: totalViews,
    total_likes: totalLikes,
    platforms_count: gameData.platforms?.length || 0,
    recent_activity_count: recentActivityCount || 0
  };
}

export async function getSimilarGames(
  gameId: string,
  limit: number = 6
): Promise<GameData[]> {
  const supabase = await createServerClient();
  
  // Get current game data
  const { data: currentGame } = await supabase
    .from('games')
    .select('genres, platforms, developers')
    .eq('id', gameId)
    .single();

  if (!currentGame) return [];

  // Find similar games based on genres and platforms
  const { data, error } = await supabase
    .from('games')
    .select('*')
    .neq('id', gameId)
    .not('cover_url', 'is', null)
    .order('aggregated_rating', { ascending: false })
    .limit(limit * 2); // Get more to filter later

  if (error || !data) return [];

  // Score games by similarity
  const scoredGames = data.map(game => {
    let score = 0;
    
    // Genre matching
    if (currentGame.genres && game.genres) {
      const genreOverlap = currentGame.genres.filter(g => game.genres.includes(g)).length;
      score += genreOverlap * 3;
    }
    
    // Platform matching
    if (currentGame.platforms && game.platforms) {
      const platformOverlap = currentGame.platforms.filter(p => game.platforms.includes(p)).length;
      score += platformOverlap * 2;
    }
    
    // Developer matching
    if (currentGame.developers && game.developers) {
      const devOverlap = currentGame.developers.filter(d => game.developers.includes(d)).length;
      score += devOverlap * 4;
    }
    
    return { ...game, similarity_score: score };
  });

  // Sort by similarity score and return top results
  return scoredGames
    .sort((a, b) => b.similarity_score - a.similarity_score)
    .slice(0, limit);
}

// Client-side functions
export async function getGameBySlugClient(slug: string): Promise<GameData | null> {
  const supabase = createClientBrowser();
  
  const { data, error } = await supabase
    .from('games')
    .select('*')
    .eq('slug', slug)
    .single();

  if (error || !data) {
    console.error('Error fetching game:', error);
    return null;
  }

  return data;
}

export async function getGameReviewsClient(
  gameId: string,
  limit: number = 10,
  offset: number = 0
): Promise<GameReviewData[]> {
  const supabase = createClientBrowser();
  
  const { data, error } = await supabase
    .from('reviews')
    .select(`
      id,
      title,
      slug,
      user_id,
      author_name,
      author_avatar_url,
      overall_score,
      created_at,
      main_image_url,
      excerpt,
      likes_count,
      views_count
    `)
    .eq('game_id', gameId)
    .eq('status', 'published')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    console.error('Error fetching game reviews:', error);
    return [];
  }

  return data || [];
}

export async function getGameStatsClient(gameId: string): Promise<GameStats> {
  const supabase = createClientBrowser();
  
  // Get game name for performance surveys
  const { data: gameData } = await supabase
    .from('games')
    .select('name, platforms')
    .eq('id', gameId)
    .single();

  if (!gameData) {
    return {
      total_reviews: 0,
      average_rating: null,
      total_performance_surveys: 0,
      total_views: 0,
      total_likes: 0,
      platforms_count: 0,
      recent_activity_count: 0
    };
  }

  // Get review stats
  const { data: reviewStats } = await supabase
    .from('reviews')
    .select('overall_score, likes_count, views_count')
    .eq('game_id', gameId)
    .eq('status', 'published');

  // Get performance survey count
  const { count: performanceCount } = await supabase
    .from('performance_surveys')
    .select('*', { count: 'exact', head: true })
    .eq('game_title', gameData.name);

  // Get recent activity (reviews from last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const { count: recentActivityCount } = await supabase
    .from('reviews')
    .select('*', { count: 'exact', head: true })
    .eq('game_id', gameId)
    .eq('status', 'published')
    .gte('created_at', thirtyDaysAgo.toISOString());

  const totalReviews = reviewStats?.length || 0;
  const averageRating = reviewStats && reviewStats.length > 0
    ? reviewStats.reduce((sum, review) => sum + (review.overall_score || 0), 0) / reviewStats.filter(r => r.overall_score).length
    : null;
  
  const totalViews = reviewStats?.reduce((sum, review) => sum + (review.views_count || 0), 0) || 0;
  const totalLikes = reviewStats?.reduce((sum, review) => sum + (review.likes_count || 0), 0) || 0;

  return {
    total_reviews: totalReviews,
    average_rating: averageRating,
    total_performance_surveys: performanceCount || 0,
    total_views: totalViews,
    total_likes: totalLikes,
    platforms_count: gameData.platforms?.length || 0,
    recent_activity_count: recentActivityCount || 0
  };
}