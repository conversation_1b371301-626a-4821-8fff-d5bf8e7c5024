// Admin Audit Logging System
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 1 Milestone 1.3

import { createClient } from '@/lib/supabase/client';

export interface AuditLogEntry {
  id?: string;
  admin_id: string;
  action: string;
  target_type?: string;
  target_id?: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
  success: boolean;
  error_message?: string;
}

// Audit log actions enum
export enum AdminAction {
  // User Management
  USER_VIEW = 'user_view',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  USER_SUSPEND = 'user_suspend',
  USER_UNSUSPEND = 'user_unsuspend',
  USER_PROMOTE_ADMIN = 'user_promote_admin',
  USER_DEMOTE_ADMIN = 'user_demote_admin',
  USER_LIST = 'user_list',
  USER_SEARCH = 'user_search',
  
  // Content Moderation
  VIEW_CONTENT_QUEUE = 'view_content_queue',
  VIEW_CONTENT_DETAILS = 'view_content_details',
  MODERATE_CONTENT = 'moderate_content',
  BATCH_MODERATE_CONTENT = 'batch_moderate_content',
  REVIEW_MODERATE = 'review_moderate',
  REVIEW_DELETE = 'review_delete',
  REVIEW_FEATURE = 'review_feature',
  REVIEW_UNFEATURE = 'review_unfeature',
  REVIEW_APPROVE = 'review_approve',
  REVIEW_REJECT = 'review_reject',
  REVIEW_FLAG = 'review_flag',
  REVIEW_ARCHIVE = 'review_archive',
  COMMENT_MODERATE = 'comment_moderate',
  COMMENT_DELETE = 'comment_delete',
  
  // Analytics
  VIEW_ANALYTICS = 'view_analytics',
  EXPORT_ANALYTICS = 'export_analytics',

  // System Administration
  VIEW_SYSTEM_HEALTH = 'view_system_health',
  VIEW_SYSTEM_METRICS = 'view_system_metrics',
  VIEW_SYSTEM_CONFIG = 'view_system_config',
  UPDATE_SYSTEM_CONFIG = 'update_system_config',
  VIEW_MAINTENANCE_TASKS = 'view_maintenance_tasks',
  RUN_MAINTENANCE_TASK = 'run_maintenance_task',
  SYSTEM_CONFIG_UPDATE = 'system_config_update',
  DATABASE_QUERY = 'database_query',
  SECURITY_SCAN = 'security_scan',

  // Security monitoring actions
  VIEW_SECURITY_EVENTS = 'view_security_events',
  VIEW_THREAT_ASSESSMENT = 'view_threat_assessment',
  VIEW_ACCESS_PATTERNS = 'view_access_patterns',
  VIEW_SECURITY_METRICS = 'view_security_metrics',
  RESOLVE_SECURITY_EVENT = 'resolve_security_event',

  // Comment moderation actions
  VIEW_COMMENT_QUEUE = 'view_comment_queue',
  VIEW_COMMENT_DETAILS = 'view_comment_details',
  MODERATE_COMMENT = 'moderate_comment',
  BATCH_MODERATE_COMMENTS = 'batch_moderate_comments',
  PIN_COMMENT = 'pin_comment',
  UNPIN_COMMENT = 'unpin_comment',
  DELETE_COMMENT = 'delete_comment',
  WARN_USER = 'warn_user',

  // Ad management actions
  VIEW_AD_CONFIGURATIONS = 'view_ad_configurations',
  CREATE_AD_CONFIGURATION = 'create_ad_configuration',
  UPDATE_AD_CONFIGURATION = 'update_ad_configuration',
  DELETE_AD_CONFIGURATION = 'delete_ad_configuration',
  VIEW_AD_PERFORMANCE = 'view_ad_performance',
  VIEW_AFFILIATE_LINKS = 'view_affiliate_links',
  CREATE_AFFILIATE_LINK = 'create_affiliate_link',
  UPDATE_AFFILIATE_LINK = 'update_affiliate_link',
  DELETE_AFFILIATE_LINK = 'delete_affiliate_link',

  // Settings management actions
  VIEW_SITE_SETTINGS = 'view_site_settings',
  UPDATE_SITE_SETTINGS = 'update_site_settings',
  RESET_SETTINGS_CATEGORY = 'reset_settings_category',
  EXPORT_SITE_SETTINGS = 'export_site_settings',
  IMPORT_SITE_SETTINGS = 'import_site_settings',

  // Authentication
  ADMIN_LOGIN = 'admin_login',
  ADMIN_LOGOUT = 'admin_logout',
  ADMIN_ACCESS_DENIED = 'admin_access_denied'
}

// Log admin action with full context
export async function logAdminAction(
  adminId: string,
  action: AdminAction | string,
  targetType?: string,
  targetId?: string,
  details?: Record<string, any>,
  success: boolean = true,
  errorMessage?: string,
  request?: Request
): Promise<void> {
  try {
    // Extract request metadata if available
    let ipAddress: string | undefined;
    let userAgent: string | undefined;
    
    if (request) {
      // Get IP address from various headers
      ipAddress = 
        request.headers.get('x-forwarded-for')?.split(',')[0] ||
        request.headers.get('x-real-ip') ||
        request.headers.get('cf-connecting-ip') ||
        'unknown';
      
      userAgent = request.headers.get('user-agent') || 'unknown';
    }

    const auditEntry: AuditLogEntry = {
      admin_id: adminId,
      action,
      target_type: targetType,
      target_id: targetId,
      details: details || {},
      ip_address: ipAddress,
      user_agent: userAgent,
      timestamp: new Date().toISOString(),
      success,
      error_message: errorMessage
    };

    // For now, log to console with structured format
    // TODO: In production, this should write to a dedicated audit_logs table
    console.log('🔍 ADMIN AUDIT LOG:', JSON.stringify(auditEntry, null, 2));

    // Also log to browser console in development for debugging
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      console.group(`🔍 Admin Action: ${action}`);
      console.log('Admin ID:', adminId);
      console.log('Target:', targetType, targetId);
      console.log('Details:', details);
      console.log('Success:', success);
      if (errorMessage) console.error('Error:', errorMessage);
      console.log('Timestamp:', auditEntry.timestamp);
      console.groupEnd();
    }

    // TODO: Future implementation - store in database
    // const supabase = createClient();
    // await supabase.from('admin_audit_logs').insert(auditEntry);

  } catch (error) {
    // Never let audit logging break the main operation
    console.error('Failed to log admin action:', error);
  }
}

// Get audit logs (for future admin interface)
export async function getAuditLogs(
  adminId: string,
  filters?: {
    action?: string;
    targetType?: string;
    targetId?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
    offset?: number;
  }
): Promise<AuditLogEntry[]> {
  try {
    // TODO: Implement when audit_logs table is created
    console.log('getAuditLogs called - not yet implemented');
    return [];
    
    // Future implementation:
    // const supabase = createClient();
    // let query = supabase
    //   .from('admin_audit_logs')
    //   .select('*')
    //   .order('timestamp', { ascending: false });
    
    // Apply filters...
    // return query results
    
  } catch (error) {
    console.error('Failed to get audit logs:', error);
    return [];
  }
}

// Security event logging
export async function logSecurityEvent(
  eventType: 'SUSPICIOUS_ACTIVITY' | 'FAILED_LOGIN' | 'PRIVILEGE_ESCALATION' | 'UNAUTHORIZED_ACCESS',
  userId?: string,
  details?: Record<string, any>,
  request?: Request
): Promise<void> {
  await logAdminAction(
    userId || 'system',
    `SECURITY_${eventType}`,
    'security',
    undefined,
    {
      event_type: eventType,
      ...details
    },
    false,
    `Security event: ${eventType}`,
    request
  );
}

// Batch audit logging for bulk operations
export async function logBulkAdminAction(
  adminId: string,
  action: AdminAction | string,
  targets: Array<{ type: string; id: string }>,
  details?: Record<string, any>,
  request?: Request
): Promise<void> {
  // Log the bulk operation
  await logAdminAction(
    adminId,
    `BULK_${action}`,
    'bulk',
    undefined,
    {
      target_count: targets.length,
      targets: targets.slice(0, 10), // Log first 10 targets to avoid huge logs
      ...details
    },
    true,
    undefined,
    request
  );

  // Log individual actions for important operations
  if (action.includes('DELETE') || action.includes('SUSPEND')) {
    for (const target of targets.slice(0, 50)) { // Limit to 50 individual logs
      await logAdminAction(
        adminId,
        action,
        target.type,
        target.id,
        { bulk_operation: true, ...details },
        true,
        undefined,
        request
      );
    }
  }
}

export default {
  logAdminAction,
  logSecurityEvent,
  logBulkAdminAction,
  getAuditLogs,
  AdminAction
};
