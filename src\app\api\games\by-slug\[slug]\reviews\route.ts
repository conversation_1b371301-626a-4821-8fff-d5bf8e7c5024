import { NextRequest, NextResponse } from 'next/server';
import { getGameBySlug, getGameReviews } from '@/lib/services/gameService';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get game first
    const game = await getGameBySlug(slug);
    
    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    const reviews = await getGameReviews(game.id, limit, offset);

    return NextResponse.json({
      reviews,
      pagination: {
        limit,
        offset,
        hasMore: reviews.length === limit
      }
    });
  } catch (error) {
    console.error('Error fetching game reviews:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}