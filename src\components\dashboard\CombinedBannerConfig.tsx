'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  Link as LinkIcon, 
  Image as ImageIcon,
  Loader2,
  Check,
  X,
  Monitor,
  BarChart,
  Eye,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  TrendingUp
} from 'lucide-react';
import {
  getUserContentBanner,
  saveContentBanner,
  deactivateContentBanner,
  type ContentBannerData
} from '@/lib/services/contentBannerService';
import {
  getUserSponsorBanner,
  saveSponsorBanner,
  deactivateSponsorBanner
} from '@/lib/services/sponsorBannerService';
import ContentBannerAnalytics from './ContentBannerAnalytics';
import SponsorAnalytics from './SponsorAnalytics';

interface CombinedBannerConfigProps {
  userId: string;
  className?: string;
}

interface ContentData {
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  id?: string;
  created_at?: string;
  updated_at?: string;
}

interface SponsorData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

const CombinedBannerConfig: React.FC<CombinedBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeAnalytics, setActiveAnalytics] = useState<'content' | 'sponsor' | null>(null);
  
  // Content Banner State
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isContentSaving, setIsContentSaving] = useState(false);
  const [contentData, setContentData] = useState<ContentData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentContentBanner, setCurrentContentBanner] = useState<ContentBannerData | null>(null);
  const [contentErrors, setContentErrors] = useState<{
    img_url?: string;
    url?: string;
  }>({});

  // Sponsor Banner State
  const [isSponsorLoading, setIsSponsorLoading] = useState(false);
  const [isSponsorSaving, setIsSponsorSaving] = useState(false);
  const [sponsorData, setSponsorData] = useState<SponsorData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentSponsor, setCurrentSponsor] = useState<SponsorData | null>(null);
  const [sponsorErrors, setSponsorErrors] = useState<{
    img_url?: string;
    url?: string;
  }>({});
  
  const { toast } = useToast();

  // Load data on mount
  useEffect(() => {
    loadContentData();
    loadSponsorData();
  }, [userId]);

  // Content Banner Functions
  const loadContentData = async () => {
    try {
      setIsContentLoading(true);
      
      if (!userId) {
        console.warn('Cannot load content banner data: userId is undefined');
        setIsContentLoading(false);
        return;
      }
      
      const data = await getUserContentBanner(userId);
      
      if (data) {
        setContentData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentContentBanner(data);
      }
    } catch (error) {
      console.error('Error loading content banner data:', error);
    } finally {
      setIsContentLoading(false);
    }
  };

  const loadSponsorData = async () => {
    try {
      setIsSponsorLoading(true);
      
      if (!userId) {
        console.warn('Cannot load sponsor data: userId is undefined');
        setIsSponsorLoading(false);
        return;
      }
      
      const data = await getUserSponsorBanner(userId);
      
      if (data) {
        setSponsorData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentSponsor(data);
      }
    } catch (error) {
      console.error('Error loading sponsor data:', error);
    } finally {
      setIsSponsorLoading(false);
    }
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const validateContentForm = () => {
    const newErrors: { img_url?: string; url?: string } = {};

    if (!contentData.img_url.trim()) {
      newErrors.img_url = 'Banner image URL is required';
    } else if (!isValidUrl(contentData.img_url)) {
      newErrors.img_url = 'Please enter a valid image URL';
    }

    if (!contentData.url.trim()) {
      newErrors.url = 'Target link URL is required';
    } else if (!isValidUrl(contentData.url)) {
      newErrors.url = 'Please enter a valid URL';
    }

    setContentErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateSponsorForm = () => {
    const newErrors: {img_url?: string; url?: string} = {};
    let isValid = true;
    
    if (!sponsorData.img_url) {
      newErrors.img_url = "Image URL is required";
      isValid = false;
    } else if (!isValidUrl(sponsorData.img_url)) {
      newErrors.img_url = "Enter a valid image URL (http/https)";
      isValid = false;
    }
    
    if (!sponsorData.url) {
      newErrors.url = "Affiliate link URL is required";
      isValid = false;
    } else if (!isValidUrl(sponsorData.url)) {
      newErrors.url = "Enter a valid URL (http/https)";
      isValid = false;
    }
    
    setSponsorErrors(newErrors);
    return isValid;
  };

  const handleSaveContent = async () => {
    if (!validateContentForm()) return;

    try {
      setIsContentSaving(true);
      
      const savedData = await saveContentBanner({
        userId,
        imgUrl: contentData.img_url,
        url: contentData.url,
        isActive: true
      });
      
      setCurrentContentBanner(savedData);
      setContentData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });
      
      toast({
        title: "Success",
        description: "Content banner settings saved successfully",
      });
    } catch (error) {
      console.error('Error saving content banner data:', error);
      toast({
        title: "Error",
        description: "Failed to save content banner settings",
        variant: "destructive",
      });
    } finally {
      setIsContentSaving(false);
    }
  };

  const handleSaveSponsor = async () => {
    if (!validateSponsorForm()) return;

    try {
      setIsSponsorSaving(true);
      
      const savedData = await saveSponsorBanner({
        userId,
        imgUrl: sponsorData.img_url,
        url: sponsorData.url,
        isActive: true
      });
      
      setCurrentSponsor(savedData);
      setSponsorData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });
      
      toast({
        title: "Success",
        description: "Sponsor banner settings saved successfully",
      });
    } catch (error) {
      console.error('Error saving sponsor data:', error);
      toast({
        title: "Error",
        description: "Failed to save sponsor banner settings",
        variant: "destructive",
      });
    } finally {
      setIsSponsorSaving(false);
    }
  };

  const handleDeactivateContent = async () => {
    try {
      setIsContentSaving(true);
      
      if (currentContentBanner) {
        const updatedData = await deactivateContentBanner(userId);
        
        setCurrentContentBanner(updatedData);
        setContentData({
          ...contentData,
          is_active: false
        });
        
        toast({
          title: "Success",
          description: "Content banner deactivated",
        });
      }
    } catch (error) {
      console.error('Error deactivating content banner:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate content banner",
        variant: "destructive",
      });
    } finally {
      setIsContentSaving(false);
    }
  };

  const handleDeactivateSponsor = async () => {
    try {
      setIsSponsorSaving(true);
      
      if (currentSponsor) {
        const updatedData = await deactivateSponsorBanner(userId);
        
        setCurrentSponsor(updatedData);
        setSponsorData({
          ...sponsorData,
          is_active: false,
          updated_at: updatedData.updated_at
        });
        
        toast({
          title: "Success",
          description: "Sponsor banner deactivated",
        });
      }
    } catch (error) {
      console.error('Error deactivating sponsor banner:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate sponsor banner",
        variant: "destructive",
      });
    } finally {
      setIsSponsorSaving(false);
    }
  };

  const toggleAnalytics = (type: 'content' | 'sponsor') => {
    setActiveAnalytics(activeAnalytics === type ? null : type);
  };

  if (isContentLoading && isSponsorLoading) {
    return (
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
            <span className="ml-2 text-slate-400">Loading banner settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  <Monitor className="h-5 w-5 text-blue-500" />
                  <BarChart className="h-5 w-5 text-emerald-500" />
                </div>
                Banner Management
              </CardTitle>
              <p className="text-sm text-slate-400 mt-1">
                Manage your content and sponsor banners in one place
              </p>
            </div>
            <Button
              onClick={() => setIsExpanded(!isExpanded)}
              variant="ghost"
              size="sm"
              className="text-slate-400 hover:text-slate-200 ml-4"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardHeader>
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-6">
                {/* Side by Side Banner Configuration */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Content Banner Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2 border-b border-slate-700/50">
                      <Monitor className="h-4 w-4 text-blue-500" />
                      <h3 className="font-semibold text-slate-200">Content Banner</h3>
                      {currentContentBanner && currentContentBanner.is_active && (
                        <Button
                          onClick={() => toggleAnalytics('content')}
                          variant="ghost"
                          size="sm"
                          className="ml-auto text-blue-400 hover:text-blue-300"
                        >
                          <TrendingUp className="h-4 w-4 mr-1" />
                          Analytics
                        </Button>
                      )}
                    </div>

                    {/* Current Content Banner Display */}
                    {currentContentBanner && currentContentBanner.is_active && (
                      <div className="p-3 bg-slate-800/50 border border-slate-700/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-slate-200">Active</h4>
                          <Button
                            onClick={handleDeactivateContent}
                            disabled={isContentSaving}
                            variant="outline"
                            size="sm"
                            className="border-red-600/50 text-red-400 hover:bg-red-600/10 h-7 px-2 text-xs"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Remove
                          </Button>
                        </div>
                        
                        <div className="relative w-full h-16 bg-slate-800/60 border border-slate-700/50 rounded overflow-hidden mb-2">
                          <img
                            src={currentContentBanner.img_url}
                            alt="Content Banner Preview"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        
                        <div className="flex items-center gap-1 text-xs text-slate-400">
                          <ExternalLink className="h-3 w-3" />
                          <span className="truncate">{currentContentBanner.url}</span>
                        </div>
                      </div>
                    )}

                    {/* Content Banner Form */}
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="content_img_url" className="text-xs text-slate-300">
                          Banner Image URL
                        </Label>
                        <Input
                          id="content_img_url"
                          type="url"
                          value={contentData.img_url}
                          onChange={(e) => setContentData({ ...contentData, img_url: e.target.value })}
                          placeholder="https://example.com/banner-image.jpg"
                          className="text-xs bg-slate-800/50 border-slate-700/50 text-slate-200"
                        />
                        {contentErrors.img_url && (
                          <p className="text-red-400 text-xs mt-1">{contentErrors.img_url}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="content_url" className="text-xs text-slate-300">
                          Target Link URL
                        </Label>
                        <Input
                          id="content_url"
                          type="url"
                          value={contentData.url}
                          onChange={(e) => setContentData({ ...contentData, url: e.target.value })}
                          placeholder="https://example.com/affiliate-link"
                          className="text-xs bg-slate-800/50 border-slate-700/50 text-slate-200"
                        />
                        {contentErrors.url && (
                          <p className="text-red-400 text-xs mt-1">{contentErrors.url}</p>
                        )}
                      </div>

                      <Button
                        onClick={handleSaveContent}
                        disabled={isContentSaving}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs h-8"
                      >
                        {isContentSaving ? (
                          <Loader2 className="h-3 w-3 animate-spin mr-2" />
                        ) : (
                          <Check className="h-3 w-3 mr-2" />
                        )}
                        {currentContentBanner && currentContentBanner.is_active ? 'Update' : 'Activate'}
                      </Button>
                    </div>
                  </div>

                  {/* Sponsor Banner Section */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 pb-2 border-b border-slate-700/50">
                      <BarChart className="h-4 w-4 text-emerald-500" />
                      <h3 className="font-semibold text-slate-200">Sponsor Banner</h3>
                      {currentSponsor && currentSponsor.is_active && (
                        <Button
                          onClick={() => toggleAnalytics('sponsor')}
                          variant="ghost"
                          size="sm"
                          className="ml-auto text-emerald-400 hover:text-emerald-300"
                        >
                          <TrendingUp className="h-4 w-4 mr-1" />
                          Analytics
                        </Button>
                      )}
                    </div>

                    {/* Current Sponsor Banner Display */}
                    {currentSponsor && currentSponsor.is_active && (
                      <div className="p-3 bg-slate-800/50 border border-slate-700/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium text-slate-200">Active</h4>
                          <Button
                            onClick={handleDeactivateSponsor}
                            disabled={isSponsorSaving}
                            variant="outline"
                            size="sm"
                            className="border-red-600/50 text-red-400 hover:bg-red-600/10 h-7 px-2 text-xs"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Remove
                          </Button>
                        </div>
                        
                        <div className="relative w-20 h-20 bg-slate-800/60 border border-slate-700/50 rounded overflow-hidden mb-2 mx-auto">
                          <img
                            src={currentSponsor.img_url}
                            alt="Sponsor Banner Preview"
                            className="w-full h-full object-contain"
                          />
                        </div>
                        
                        <div className="flex items-center gap-1 text-xs text-slate-400 justify-center">
                          <ExternalLink className="h-3 w-3" />
                          <span className="truncate">{currentSponsor.url.length > 20 ? `${currentSponsor.url.substring(0, 20)}...` : currentSponsor.url}</span>
                        </div>
                      </div>
                    )}

                    {/* Sponsor Banner Form */}
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="sponsor_img_url" className="text-xs text-slate-300">
                          Banner Image URL
                        </Label>
                        <Input
                          id="sponsor_img_url"
                          type="url"
                          value={sponsorData.img_url}
                          onChange={(e) => setSponsorData({ ...sponsorData, img_url: e.target.value })}
                          placeholder="https://example.com/sponsor-banner.png"
                          className="text-xs bg-slate-800/50 border-slate-700/50 text-slate-200"
                        />
                        {sponsorErrors.img_url && (
                          <p className="text-red-400 text-xs mt-1">{sponsorErrors.img_url}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="sponsor_url" className="text-xs text-slate-300">
                          Affiliate Link URL
                        </Label>
                        <Input
                          id="sponsor_url"
                          type="url"
                          value={sponsorData.url}
                          onChange={(e) => setSponsorData({ ...sponsorData, url: e.target.value })}
                          placeholder="https://example.com/affiliate-link"
                          className="text-xs bg-slate-800/50 border-slate-700/50 text-slate-200"
                        />
                        {sponsorErrors.url && (
                          <p className="text-red-400 text-xs mt-1">{sponsorErrors.url}</p>
                        )}
                      </div>

                      <Button
                        onClick={handleSaveSponsor}
                        disabled={isSponsorSaving}
                        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white text-xs h-8"
                      >
                        {isSponsorSaving ? (
                          <Loader2 className="h-3 w-3 animate-spin mr-2" />
                        ) : (
                          <Check className="h-3 w-3 mr-2" />
                        )}
                        {currentSponsor && currentSponsor.is_active ? 'Update' : 'Activate'}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Analytics Section - Only show when requested */}
      <AnimatePresence>
        {activeAnalytics === 'content' && currentContentBanner && currentContentBanner.is_active && currentContentBanner.id && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <ContentBannerAnalytics bannerId={currentContentBanner.id} />
          </motion.div>
        )}
        
        {activeAnalytics === 'sponsor' && currentSponsor && currentSponsor.is_active && currentSponsor.id && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <SponsorAnalytics bannerId={currentSponsor.id} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CombinedBannerConfig;