'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ert<PERSON>riangle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AdminMFAPromptProps {
  onMFAVerified: () => void;
  onCancel: () => void;
}

export function AdminMFAPrompt({ onMFAVerified, onCancel }: AdminMFAPromptProps) {
  const [mfaCode, setMfaCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');

  const handleVerifyMFA = async () => {
    if (!mfaCode.trim()) {
      setError('Por favor, insira o código MFA');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const response = await fetch('/api/admin/mfa-verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token: mfaCode.trim()
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        onMFAVerified();
      } else {
        setError(result.error || 'Código MFA inválido');
      }
    } catch (error) {
      setError('Erro ao verificar MFA. Tente novamente.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleVerifyMFA();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
      <Card className="w-full max-w-md shadow-lg glow-purple">
        <CardHeader className="items-center">
          <ShieldCheck className="h-16 w-16 text-primary mb-4" />
          <CardTitle className="text-2xl font-bold text-primary">
            Verificação MFA Necessária
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Para acessar o painel administrativo, insira o código de autenticação de dois fatores do seu aplicativo autenticador.
          </p>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="mfa-code">Código MFA (6 dígitos)</Label>
            <Input
              id="mfa-code"
              type="text"
              placeholder="000000"
              value={mfaCode}
              onChange={(e) => setMfaCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              onKeyPress={handleKeyPress}
              maxLength={6}
              className="text-center text-lg font-mono tracking-widest"
              disabled={isVerifying}
              autoFocus
            />
          </div>

          <div className="flex space-x-2">
            <Button
              onClick={handleVerifyMFA}
              disabled={isVerifying || mfaCode.length !== 6}
              className="flex-1"
            >
              {isVerifying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verificando...
                </>
              ) : (
                'Verificar'
              )}
            </Button>
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isVerifying}
            >
              Cancelar
            </Button>
          </div>

          <div className="text-xs text-muted-foreground mt-4">
            <p>🔒 Acesso seguro com autenticação de dois fatores</p>
            <p>Se você perdeu acesso ao seu autenticador, entre em contato com outro administrador.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AdminMFAPrompt; 