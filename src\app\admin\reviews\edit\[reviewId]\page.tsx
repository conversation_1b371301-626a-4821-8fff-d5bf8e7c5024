'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuthContext } from '@/hooks/use-auth-context';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ShieldAlert, ArrowLeft, Save, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { getReviewForAdminSecure, moderateReviewSecure, type SecureReviewModerationData, type SecureModerationAction } from '../../actions';
import { ReviewActionsDropdown } from '@/components/admin/ReviewActionsDropdown';

interface ReviewFormData {
  title: string;
  gameName: string;
  status: 'published' | 'draft' | 'pending' | 'flagged' | 'archived';
  overallScore: number;
  platforms: string;
  tags: string;
  moderationNotes: string;
}

export default function EditReviewPage() {
  const { user, loading, isAdmin } = useAuthContext();
  const router = useRouter();
  const { reviewId } = useParams<{ reviewId: string }>();
  const { toast } = useToast();

  const [review, setReview] = useState<SecureReviewModerationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<ReviewFormData>({
    title: '',
    gameName: '',
    status: 'draft',
    overallScore: 0,
    platforms: '',
    tags: '',
    moderationNotes: '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!loading && !isAdmin) {
      router.push('/');
    }
  }, [user, loading, isAdmin, router]);

  // Load review for editing
  const loadReview = async () => {
    if (!user?.id || !isAdmin || !reviewId) return;

    setIsLoading(true);
    try {
              const result = await getReviewForAdminSecure(reviewId);

      if (result.error) {
        setError(result.error);
        toast({
          title: "Error loading review",
          description: result.error,
          variant: "destructive"
        });
      } else if (result.review) {
        setReview(result.review);
        setFormData({
          title: result.review.title,
          gameName: result.review.game_name,
          status: result.review.status,
          overallScore: result.review.overall_score,
          platforms: result.review.platforms?.join(', ') || '',
          tags: result.review.tags?.join(', ') || '',
          moderationNotes: result.review.moderation_notes || '',
        });
      }
    } catch (error) {
      setError('Failed to load review');
      toast({
        title: "Error loading review",
        description: "Failed to load review for editing",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!loading && isAdmin && user?.id && reviewId) {
      loadReview();
    }
  }, [user, loading, isAdmin, reviewId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prevFormData => ({
      ...prevFormData,
      [name]: name === 'overallScore' ? parseFloat(value) || 0 : value,
    }));
  };

  const handleStatusChange = (value: string) => {
    setFormData(prev => ({ ...prev, status: value as any }));
  };

  const handleModerateReview = async (action: SecureModerationAction) => {
    if (!user?.id || !reviewId) return;

    try {
      const result = await moderateReviewSecure(reviewId, action);

      if (result.success) {
        toast({
          title: "Review moderated successfully",
          description: `Review has been ${action.action}d`,
        });
        await loadReview(); // Reload the review data
      } else {
        toast({
          title: "Moderation failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Moderation error",
        description: "Failed to moderate review",
        variant: "destructive"
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // For now, we'll just update the status via moderation
      // In the future, this could be expanded to update other fields
      if (review && formData.status !== review.status) {
        let action: SecureModerationAction['action'];
        switch (formData.status) {
          case 'published':
            action = 'approve';
            break;
          case 'flagged':
            action = 'flag';
            break;
          case 'archived':
            action = 'archive';
            break;
          default:
            action = 'reject'; // For draft/pending
        }

        await handleModerateReview({
          action,
          notes: formData.moderationNotes
        });
      }

      toast({
        title: "Review updated",
        description: "Review has been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Update failed",
        description: "Failed to update review",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-10rem)]"><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div></div>;
  }
  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
        <Card className="w-full max-w-md shadow-lg glow-purple">
          <CardHeader className="items-center">
            <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
            <CardTitle className="text-3xl font-bold text-destructive">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">You don't have admin privileges.</p>
            <p className="text-muted-foreground text-sm mt-2">Contact an administrator if you believe this is an error.</p>
          </CardContent>
          <CardFooter></CardFooter>
        </Card>
      </div>
    );
  }
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-10rem)]">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-10rem)] text-center text-destructive">
        <p>{error}</p>
      </div>
    );
  }
  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "outline" | "destructive" => {
    switch (status) {
      case 'published': return 'default';
      case 'draft': return 'secondary';
      case 'pending': return 'outline';
      case 'flagged': return 'destructive';
      case 'archived': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" asChild>
            <Link href="/admin/reviews">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Reviews
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-primary">Edit Review</h1>
            <p className="text-muted-foreground">Moderate and edit review details</p>
          </div>
        </div>
        {review && review.status === 'published' && (
          <Button variant="outline" asChild>
            <Link href={`/reviews/view/${review.slug}`} target="_blank">
              <ExternalLink className="mr-2 h-4 w-4" />
              View Review
            </Link>
          </Button>
        )}
      </div>

      {review && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Review Info Card */}
          <Card className="lg:col-span-2 shadow-lg glow-purple">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{review.title}</CardTitle>
                  <CardDescription>Game: {review.game_name}</CardDescription>
                </div>
                <Badge variant={getStatusBadgeVariant(review.status)}>
                  {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="title">Review Title</Label>
                    <Input
                      id="title"
                      name="title"
                      type="text"
                      value={formData.title}
                      onChange={handleInputChange}
                      className="bg-background"
                      readOnly
                    />
                  </div>
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="gameName">Game Name</Label>
                    <Input
                      id="gameName"
                      name="gameName"
                      type="text"
                      value={formData.gameName}
                      onChange={handleInputChange}
                      className="bg-background"
                      readOnly
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="status">Status</Label>
                    <Select value={formData.status} onValueChange={handleStatusChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending">Pending Review</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                        <SelectItem value="flagged">Flagged</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="overallScore">Overall Score</Label>
                    <Input
                      id="overallScore"
                      name="overallScore"
                      type="number"
                      step="1"
                      min="0"
                      max="100"
                      value={formData.overallScore}
                      onChange={handleInputChange}
                      className="bg-background"
                      readOnly
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="platforms">Platforms</Label>
                    <Input
                      id="platforms"
                      name="platforms"
                      type="text"
                      value={formData.platforms}
                      onChange={handleInputChange}
                      className="bg-background"
                      readOnly
                    />
                  </div>
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="tags">Tags</Label>
                    <Input
                      id="tags"
                      name="tags"
                      type="text"
                      value={formData.tags}
                      onChange={handleInputChange}
                      className="bg-background"
                      readOnly
                    />
                  </div>
                </div>
                <div className="flex flex-col space-y-1.5">
                  <Label htmlFor="moderationNotes">Moderation Notes</Label>
                  <Textarea
                    id="moderationNotes"
                    name="moderationNotes"
                    value={formData.moderationNotes}
                    onChange={handleInputChange}
                    placeholder="Add notes about this review..."
                    className="bg-background"
                    rows={3}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isSaving}>
                  <Save className="mr-2 h-4 w-4" />
                  {isSaving ? 'Updating...' : 'Update Review'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Review Stats and Actions Card */}
          <Card className="shadow-lg glow-purple">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Statistics & Actions</CardTitle>
                  <CardDescription>Review information and moderation actions</CardDescription>
                </div>
                <ReviewActionsDropdown
                  review={review}
                  onModerate={handleModerateReview}
                  showEditAction={false}
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Author</p>
                    <p className="font-medium">{review.author_name}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Status</p>
                    <p className="font-medium capitalize">{review.status}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Created</p>
                    <p className="font-medium">{new Date(review.created_at).toLocaleDateString('en-US')}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Featured</p>
                    <p className="font-medium">{review.is_featured ? 'Yes' : 'No'}</p>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <p className="text-sm text-muted-foreground mb-2">Engagement Statistics</p>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-primary">{review.view_count}</p>
                      <p className="text-xs text-muted-foreground">Views</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-accent">{review.like_count}</p>
                      <p className="text-xs text-muted-foreground">Likes</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-secondary">{review.comment_count}</p>
                      <p className="text-xs text-muted-foreground">Comments</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
