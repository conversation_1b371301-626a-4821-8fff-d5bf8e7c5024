"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";

// DASHBOARD REDESIGN: Phase 3 - Advanced Features & Polish
// Date: 15/06/2025
// Task: dashboardStyleAdmin003 (FINAL PHASE)
//
// Created DynamicBackground component with gaming-themed effects:
// - Animated grid pattern with continuous movement
// - Floating particle system with physics-based motion
// - Performance-optimized animations using CSS transforms
// - Responsive particle density based on screen size
// - Gaming aesthetic matching admin panel design

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  opacity: number;
  speed: number;
  direction: number;
}

export function DynamicBackground() {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  // Generate particles based on screen size
  useEffect(() => {
    setIsMounted(true);
    
    const createParticles = () => {
      const isLargeScreen = window.innerWidth >= 1024;
      const particleCount = isLargeScreen ? 25 : 15; // Reduce particles on mobile
      
      const newParticles: Particle[] = Array.from({ length: particleCount }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 3 + 1, // 1-4px
        opacity: Math.random() * 0.5 + 0.1, // 0.1-0.6
        speed: Math.random() * 0.5 + 0.2, // 0.2-0.7
        direction: Math.random() * Math.PI * 2 // Random direction
      }));
      
      setParticles(newParticles);
    };

    createParticles();
    
    // Recreate particles on resize
    const handleResize = () => {
      createParticles();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Animate particles continuously
  useEffect(() => {
    if (!isMounted || particles.length === 0) return;

    const animateParticles = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => {
          let newX = particle.x + Math.cos(particle.direction) * particle.speed * 0.1;
          let newY = particle.y + Math.sin(particle.direction) * particle.speed * 0.1;
          
          // Wrap around screen edges
          if (newX > 100) newX = -2;
          if (newX < -2) newX = 100;
          if (newY > 100) newY = -2;
          if (newY < -2) newY = 100;
          
          return {
            ...particle,
            x: newX,
            y: newY
          };
        })
      );
    };

    const interval = setInterval(animateParticles, 50); // 20fps for smooth movement
    return () => clearInterval(interval);
  }, [isMounted, particles.length]);

  if (!isMounted) return null;

  return (
    <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
      {/* Base gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" />
      
      {/* Animated grid pattern */}
      <motion.div 
        className="absolute inset-0 opacity-[0.08]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.08 }}
        transition={{ duration: 2 }}
      >
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(139, 92, 246, 0.4) 1px, transparent 1px),
              linear-gradient(90deg, rgba(139, 92, 246, 0.4) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px',
            animation: 'gridMove 30s linear infinite'
          }}
        />
      </motion.div>
      
      {/* Secondary grid for depth */}
      <motion.div 
        className="absolute inset-0 opacity-[0.04]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.04 }}
        transition={{ duration: 3, delay: 0.5 }}
      >
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(6, 182, 212, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(6, 182, 212, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '120px 120px',
            animation: 'gridMove 45s linear infinite reverse'
          }}
        />
      </motion.div>
      
      {/* Floating particles */}
      <div className="absolute inset-0">
        {particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: `rgba(139, 92, 246, ${particle.opacity})`,
              boxShadow: `0 0 ${particle.size * 2}px rgba(139, 92, 246, ${particle.opacity * 0.5})`
            }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ 
              duration: Math.random() * 2 + 1,
              delay: Math.random() * 2 
            }}
          />
        ))}
      </div>
      
      {/* Additional accent particles for depth */}
      <div className="absolute inset-0">
        {Array.from({ length: 8 }).map((_, i) => (
          <motion.div
            key={`accent-${i}`}
            className="absolute w-1 h-1 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              backgroundColor: 'rgba(6, 182, 212, 0.6)',
              boxShadow: '0 0 4px rgba(6, 182, 212, 0.8)'
            }}
            animate={{
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
          />
        ))}
      </div>
      
      {/* Subtle radial gradient overlay */}
      <div 
        className="absolute inset-0 opacity-30"
        style={{
          background: `
            radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%)
          `
        }}
      />
      
      <style jsx>{`
        @keyframes gridMove {
          0% {
            transform: translate(0, 0);
          }
          100% {
            transform: translate(60px, 60px);
          }
        }
        
        @keyframes float {
          0%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          33% {
            transform: translateY(-10px) rotate(120deg);
          }
          66% {
            transform: translateY(5px) rotate(240deg);
          }
        }
      `}</style>
    </div>
  );
} 