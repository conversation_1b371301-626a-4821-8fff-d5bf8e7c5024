import { useQuery } from '@tanstack/react-query';
import { CPUSpec } from '@/lib/cpu-data';
import { GPUSpec } from '@/lib/gpu-data';

interface HardwareSearchResponse {
  success: boolean;
  data: CPUSpec[] | GPUSpec[];
  count: number;
  error?: string;
}

// Hook for searching CPUs
export function useCPUSearch(query: string, enabled: boolean = true) {
  return useQuery<CPUSpec[], Error>({
    queryKey: ['cpu-search', query],
    queryFn: async () => {
      if (!query || query.length < 2) return [];
      
      try {
        const response = await fetch('/api/hardware/cpu/search', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query, limit: 10 }),
        });
        
        if (!response.ok) {
          throw new Error(`CPU search failed: ${response.statusText}`);
        }
        
        const result: HardwareSearchResponse = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'CPU search failed');
        }
        
        return result.data as CPUSpec[];
      } catch (error) {
        console.error('Error fetching CPU data:', error);
        return [];
      }
    },
    enabled: enabled && query.length >= 2,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}

// Hook for searching GPUs
export function useGPUSearch(query: string, enabled: boolean = true) {
  return useQuery<GPUSpec[], Error>({
    queryKey: ['gpu-search', query],
    queryFn: async () => {
      if (!query || query.length < 2) return [];
      
      try {
        const response = await fetch('/api/hardware/gpu/search', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query, limit: 10 }),
        });
        
        if (!response.ok) {
          throw new Error(`GPU search failed: ${response.statusText}`);
        }
        
        const result: HardwareSearchResponse = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'GPU search failed');
        }
        
        return result.data as GPUSpec[];
      } catch (error) {
        console.error('Error fetching GPU data:', error);
        return [];
      }
    },
    enabled: enabled && query.length >= 2,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
}

// Hook for getting top CPUs (for initial suggestions)
export function useTopCPUs(limit: number = 10) {
  return useQuery<CPUSpec[], Error>({
    queryKey: ['top-cpus', limit],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/hardware/cpu/search?limit=${limit}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch top CPUs: ${response.statusText}`);
        }
        
        const result: HardwareSearchResponse = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch top CPUs');
        }
        
        return result.data as CPUSpec[];
      } catch (error) {
        console.error('Error fetching top CPUs:', error);
        return [];
      }
    },
    staleTime: 30 * 60 * 1000, // Cache for 30 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
  });
}

// Hook for getting top GPUs (for initial suggestions)
export function useTopGPUs(limit: number = 10) {
  return useQuery<GPUSpec[], Error>({
    queryKey: ['top-gpus', limit],
    queryFn: async () => {
      try {
        const response = await fetch(`/api/hardware/gpu/search?limit=${limit}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch top GPUs: ${response.statusText}`);
        }
        
        const result: HardwareSearchResponse = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch top GPUs');
        }
        
        return result.data as GPUSpec[];
      } catch (error) {
        console.error('Error fetching top GPUs:', error);
        return [];
      }
    },
    staleTime: 30 * 60 * 1000, // Cache for 30 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
  });
}
