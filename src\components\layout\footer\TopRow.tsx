'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Hash,
  Users,
  FileText,
  Tag,
  TrendingUp,
  ChevronDown,
  ChevronUp,
  Gamepad2,
  MessageSquare,
  Newspaper,
  BookOpen,
  Mail,
  Shield,
  Flame
} from 'lucide-react';

interface TrendingTag {
  id: string;
  name: string;
  count: number;
  trend: 'hot' | 'rising' | 'stable';
  percentage: number;
}

interface SiteStats {
  totalReviews: number;
  totalUsers: number;
  totalTags: number;
}

const TopRow = () => {
  const [hoveredTag, setHoveredTag] = useState<string | null>(null);
  const [hoveredLink, setHoveredLink] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<'navigation' | 'trending'>('navigation');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Animation for initial mount
  useEffect(() => {
    setMounted(true);
  }, []);

  const siteStats: SiteStats = {
    totalReviews: 12847,
    totalUsers: 89321,
    totalTags: 1456
  };

  const trendingTags: TrendingTag[] = [
    { id: "1",  name: "Action RPG",   count: 2847, trend: "hot",     percentage: 95 },
    { id: "2",  name: "Open World",   count: 1923, trend: "rising",  percentage: 78 },
    { id: "3",  name: "Multiplayer",  count: 1654, trend: "stable",  percentage: 65 },
    { id: "4",  name: "Indie",        count: 1432, trend: "rising",  percentage: 58 },
    { id: "5",  name: "Survival",     count: 1289, trend: "hot",     percentage: 52 },
    { id: "6",  name: "Strategy",     count: 987,  trend: "stable",  percentage: 38 },
    { id: "7",  name: "Horror",       count: 876,  trend: "rising",  percentage: 35 },
    { id: "8",  name: "Sandbox",      count: 743,  trend: "stable",  percentage: 30 },
    { id: "9",  name: "Roguelike",    count: 698,  trend: "hot",     percentage: 28 },
    { id: "10", name: "Platformer",   count: 612,  trend: "rising",  percentage: 25 },
    { id: "11", name: "Simulation",   count: 589,  trend: "stable",  percentage: 24 },
    { id: "12", name: "Racing",       count: 534,  trend: "rising",  percentage: 22 },
    { id: "13", name: "Fighting",     count: 487,  trend: "stable",  percentage: 20 },
    { id: "14", name: "Puzzle",       count: 456,  trend: "hot",     percentage: 19 },
    { id: "15", name: "Shooter",      count: 423,  trend: "rising",  percentage: 17 }
  ];

  const quickLinks = [
    {
      name: 'Guides',
      href: '/guides',
      icon: BookOpen,
      description: 'Tips & Walkthroughs',
      featured: true
    },
    { name: 'Reviews',   href: '/reviews',       icon: Gamepad2,      description: 'Latest Game Reviews' },
    { name: 'Community', href: '/community',     icon: MessageSquare, description: 'Join the Discussion' },
    { name: 'News',      href: '/gaming-news',   icon: Newspaper,     description: 'Gaming Updates' },
    { name: 'Contact',   href: '/contact',       icon: Mail,          description: 'Get In Touch' },
    { name: 'Privacy',   href: '/privacy-policy',icon: Shield,        description: 'Privacy & Terms' }
  ];

  const formatNumber = (n: number) =>
    n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'hot':    return Flame;
      case 'rising': return TrendingUp;
      default:       return Tag;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'hot':    return 'text-red-400';
      case 'rising': return 'text-yellow-400';
      default:       return 'text-slate-400';
    }
  };

  const CodeTitle = ({
    children,
    className = "",
    onMouseEnter,
    onMouseLeave
  }: {
    children: React.ReactNode;
    className?: string;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
  }) => (
    <span
      className={`font-mono inline-block relative ${className}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <span className="text-violet-400/60 transition-colors duration-300">&lt;</span>
      <span className="px-1 relative">
        {children}
        <span className="absolute inset-0 bg-gradient-to-r from-violet-400/0 via-violet-400/30 to-violet-400/0 opacity-0 hover:opacity-100 transition-opacity duration-700 animate-pulse" />
      </span>
      <span className="text-violet-400/60 transition-colors duration-300">/&gt;</span>
    </span>
  );

  const handleNavClick = () => {
    if (isCollapsed) setIsCollapsed(false);
    setActiveSection('navigation');
  };
  const handleTrendingClick = () => {
    if (isCollapsed) setIsCollapsed(false);
    setActiveSection('trending');
  };

  return (
    <div className={`space-y-8 mb-8 transition-opacity duration-500 ease-out ${mounted ? 'opacity-100' : 'opacity-0'}`}>
      {/* ── QUICK LINKS & TRENDING ── */}
      <div className="border border-white/5 bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-xl rounded-xl shadow-lg shadow-black/10 transform transition-all duration-300 ease-out hover:shadow-xl hover:shadow-black/20 hover:scale-[1.005]">
        {/* Toggles + Collapse */}
        <div className={`flex flex-col sm:flex-row sm:items-center justify-between px-4 md:px-6 py-3 md:py-4 transition-all duration-500 ease-in-out ${isCollapsed ? '' : 'border-b border-white/10'}`}>
          <div className="flex-1 flex overflow-x-auto no-scrollbar mb-3 sm:mb-0">
            <div className="flex space-x-3 md:space-x-6 min-w-max">
              <button
                onClick={handleNavClick}
                className={`flex items-center space-x-2 px-2 md:px-3 py-1 rounded-lg transition-all duration-300 ease-out text-sm md:text-base whitespace-nowrap transform hover:scale-105 ${
                  activeSection === 'navigation' && !isCollapsed
                    ? 'bg-violet-500/20 text-violet-300 shadow-md shadow-violet-500/10'
                    : 'text-slate-400 hover:bg-slate-800/50 hover:text-white'
                }`}
              >
                <Gamepad2 size={14} className="md:w-4 md:h-4 transition-transform duration-300 ease-out group-hover:rotate-12"/>
                <span>Site Nav</span>
              </button>
              <button
                onClick={handleTrendingClick}
                className={`flex items-center space-x-2 px-2 md:px-3 py-1 rounded-lg transition-all duration-300 ease-out text-sm md:text-base whitespace-nowrap transform hover:scale-105 ${
                  activeSection === 'trending' && !isCollapsed
                    ? 'bg-cyan-500/20 text-cyan-300 shadow-md shadow-cyan-500/10'
                    : 'text-slate-400 hover:bg-slate-800/50 hover:text-white'
                }`}
              >
                <TrendingUp size={14} className="md:w-4 md:h-4 transition-transform duration-300 ease-out group-hover:translate-y-[-2px]"/>
                <span>Trending Tags</span>
              </button>
            </div>
          </div>
          <button
            onClick={() => setIsCollapsed(v => !v)}
            className="w-full sm:w-auto flex items-center justify-center sm:justify-start space-x-1 px-2 md:px-3 py-2 md:py-1.5 bg-slate-800/80 hover:bg-slate-700 rounded-lg text-slate-300 hover:text-white text-xs md:text-sm transition-all duration-300 ease-out transform hover:scale-105 hover:shadow-md"
          >
            <span className="font-mono whitespace-nowrap">
              <span className="text-violet-400/60 transition-colors duration-300">&lt;</span>
              <span className="px-1">{isCollapsed ? 'Expand' : 'Collapse'}</span>
              <span className="text-violet-400/60 transition-colors duration-300">/&gt;</span>
            </span>
            {isCollapsed ? 
              <ChevronDown size={14} className="md:w-4 md:h-4 transition-transform duration-300 ease-out"/> : 
              <ChevronUp size={14} className="md:w-4 md:h-4 transition-transform duration-300 ease-out"/>
            }
          </button>
        </div>

        {/* Smooth collapse/expand + cross-fade between sections */}
        <div 
          className={`transition-all duration-700 ease-in-out overflow-hidden ${
            isCollapsed ? 'max-h-0 opacity-0' : 'max-h-[700px] opacity-100'
          }`}
          style={{
            transitionTimingFunction: "cubic-bezier(0.34, 1.56, 0.64, 1)"
          }}
        >
          <div className="relative">
            {/* Site Navigation */}
            <div className={`px-4 md:px-6 py-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 transition-all duration-700 ease-custom ${
              activeSection === 'navigation'
                ? 'opacity-100 relative transform translate-y-0'
                : 'opacity-0 absolute inset-0 pointer-events-none transform translate-y-8'
            }`}>
              {quickLinks.map((link, idx) => {
                const IconComp = link.icon;
                // Calculate staggered animation delay
                const animDelay = idx * 50;
                
                return (
                  <Link
                    key={idx}
                    href={link.href}
                    className="group"
                    onMouseEnter={() => setHoveredLink(link.name)}
                    onMouseLeave={() => setHoveredLink(null)}
                    style={{
                      animationDelay: `${animDelay}ms`,
                      animation: mounted && activeSection === 'navigation' ? 'fadeSlideIn 0.5s ease-out both' : 'none'
                    }}
                  >
                    <div className={`relative p-3 md:p-4 rounded-lg border transition-all duration-300 ease-out transform hover:scale-[1.03] hover:shadow-lg ${
                      link.featured
                        ? 'border-violet-500/30 bg-violet-500/5 hover:bg-violet-500/10 hover:border-violet-400/50 hover:shadow-violet-500/10'
                        : 'border-white/5 bg-slate-800/20 hover:bg-slate-700/30 hover:border-white/10 hover:shadow-black/20'
                    }`}>
                      {link.featured && (
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-violet-400 rounded-full animate-pulse shadow-md shadow-violet-500/30"/>
                      )}
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300 ease-out group-hover:scale-110 ${
                          link.featured
                            ? 'bg-violet-500/20 group-hover:bg-violet-500/30'
                            : 'bg-slate-700/50 group-hover:bg-violet-500/20'
                        }`}>
                          <IconComp size={16} className={`
                            ${link.featured
                              ? 'text-violet-400'
                              : 'text-slate-400 group-hover:text-violet-400'
                            }
                            transition-all duration-300 ease-out group-hover:rotate-6
                          `}/>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-white text-sm truncate transition-transform duration-300 ease-out group-hover:translate-x-0.5">{link.name}</div>
                          <div className={`text-xs text-slate-400 transition-all duration-500 ease-out truncate ${
                            hoveredLink === link.name ? 'opacity-100 translate-x-1' : 'opacity-60'
                          }`}>
                            {link.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* Trending Tags */}
            <div className={`overflow-y-auto custom-scrollbar max-h-[400px] md:max-h-[560px] transition-all duration-700 ease-custom ${
              activeSection === 'trending'
                ? 'opacity-100 relative transform translate-y-0'
                : 'opacity-0 absolute inset-0 pointer-events-none transform translate-y-8'
            }`}>
              <div className="sticky top-0 z-10 bg-slate-800/30 backdrop-blur-sm px-4 md:px-6 py-2 border-b border-white/10 transition-all duration-300 ease-out">
                <div className="flex items-center">
                  <div className="flex-1 h-px bg-slate-600/30"/>
                  <span className="mx-3 text-xs uppercase tracking-wide text-slate-400 transition-all duration-300 ease-out hover:text-slate-200">Trending Now</span>
                  <div className="flex-1 h-px bg-slate-600/30"/>
                </div>
              </div>
              <div className="px-4 md:px-6 py-3 md:py-4 space-y-2">
                {trendingTags.slice(0, 10).map((tag, idx) => {
                  const TrendIcon = getTrendIcon(tag.trend);
                  // Calculate staggered animation delay
                  const animDelay = idx * 50;
                  
                  return (
                    <Link
                      key={tag.id}
                      href={`/tags/${tag.name.toLowerCase().replace(/\s+/g, '-')}`}
                      className="block group"
                      onMouseEnter={() => setHoveredTag(tag.id)}
                      onMouseLeave={() => setHoveredTag(null)}
                      style={{
                        animationDelay: `${animDelay}ms`,
                        animation: mounted && activeSection === 'trending' ? 'fadeSlideIn 0.5s ease-out both' : 'none'
                      }}
                    >
                      <div className="flex items-center justify-between p-2 md:p-3 rounded-lg hover:bg-slate-800/30 transition-all duration-300 ease-out transform hover:translate-x-1 hover:shadow-md">
                        <div className="flex items-center space-x-2 md:space-x-3 min-w-0">
                          <span className="text-xs font-mono text-slate-500 w-5 md:w-6 flex-shrink-0 transition-colors duration-300 ease-out group-hover:text-slate-400"># {idx + 1}</span>
                          <TrendIcon 
                            size={14} 
                            className={`
                              ${getTrendColor(tag.trend)} 
                              transition-all duration-300 ease-out 
                              group-hover:scale-125 group-hover:rotate-6
                              ${tag.trend === 'hot' ? 'animate-pulse-slow' : ''}
                            `} 
                          />
                          <span className="font-medium text-slate-200 group-hover:text-white transition-all duration-300 ease-out truncate group-hover:translate-x-0.5">{tag.name}</span>
                        </div>
                        <div className="text-xs text-slate-400 flex-shrink-0 transition-all duration-300 ease-out group-hover:text-slate-200 group-hover:scale-110">{formatNumber(tag.count)}</div>
                      </div>
                      <div className="w-full h-1 bg-slate-800 rounded-full overflow-hidden mt-1 transition-all duration-300 ease-out group-hover:h-1.5">
                        <div
                          className="h-full bg-gradient-to-r from-violet-500 to-cyan-500 transition-all duration-700 ease-custom-bounce"
                          style={{ 
                            width: hoveredTag === tag.id ? `${tag.percentage}%` : '0%',
                          }}
                        />
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollbar & Animation Styles */}
      <style jsx>{`
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(
            to bottom,
            rgba(139, 92, 246, 0.4),
            rgba(56, 189, 248, 0.4)
          );
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(
            to bottom,
            rgba(139, 92, 246, 0.6),
            rgba(56, 189, 248, 0.6)
          );
        }
        .no-scrollbar {
          -ms-overflow-style: none;  /* IE and Edge */
          scrollbar-width: none;  /* Firefox */
        }
        .no-scrollbar::-webkit-scrollbar {
          display: none;  /* Chrome, Safari, Opera */
        }
        .glitch:hover {
          animation: glitch 0.3s cubic-bezier(0.455, 0.03, 0.515, 0.955);
        }
        
        @keyframes glitch {
          0%, 100% { transform: translate(0); }
          10% { transform: translate(-1px, -1px); }
          20% { transform: translate(1px, 1px); }
          30% { transform: translate(-1px, 1px); }
          40% { transform: translate(1px, -1px); }
          50% { transform: translate(-1px, -1px); }
          60% { transform: translate(1px, 1px); }
          70% { transform: translate(-1px, 1px); }
          80% { transform: translate(1px, -1px); }
          90% { transform: translate(-1px, -1px); }
        }
        
        @keyframes fadeSlideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .ease-custom {
          transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .ease-custom-bounce {
          transition-timing-function: cubic-bezier(0.68, -0.6, 0.32, 1.6);
        }
        
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
        
        .animate-pulse-slow {
          animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
};

export default TopRow;