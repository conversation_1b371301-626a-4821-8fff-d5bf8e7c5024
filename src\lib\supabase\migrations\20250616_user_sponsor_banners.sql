-- Create user_sponsor_banners table
CREATE TABLE IF NOT EXISTS user_sponsor_banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  img_url TEXT NOT NULL,
  url TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  impression_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups by user_id
CREATE INDEX IF NOT EXISTS idx_user_sponsor_banners_user_id ON user_sponsor_banners(user_id);

-- Add RLS policies for security
ALTER TABLE user_sponsor_banners ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own sponsor data
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'user_sponsor_banners' AND policyname = 'user_sponsor_banners_select'
  ) THEN
    CREATE POLICY user_sponsor_banners_select ON user_sponsor_banners
      FOR SELECT USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Allow users to insert their own sponsor data
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'user_sponsor_banners' AND policyname = 'user_sponsor_banners_insert'
  ) THEN
    CREATE POLICY user_sponsor_banners_insert ON user_sponsor_banners
      FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;

-- Allow users to update only their own sponsor data
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'user_sponsor_banners' AND policyname = 'user_sponsor_banners_update'
  ) THEN
    CREATE POLICY user_sponsor_banners_update ON user_sponsor_banners
      FOR UPDATE USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Allow users to delete only their own sponsor data
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'user_sponsor_banners' AND policyname = 'user_sponsor_banners_delete'
  ) THEN
    CREATE POLICY user_sponsor_banners_delete ON user_sponsor_banners
      FOR DELETE USING (auth.uid() = user_id);
  END IF;
END
$$;

-- Create stored procedures to track impressions and clicks
CREATE OR REPLACE FUNCTION increment_sponsor_impression(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET impression_count = impression_count + 1
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_sponsor_click(banner_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sponsor_banners
  SET click_count = click_count + 1
  WHERE id = banner_id;
END;
$$ LANGUAGE plpgsql;
