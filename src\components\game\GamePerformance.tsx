'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { GamePerformanceData } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Monitor, 
  Cpu, 
  HardDrive, 
  Zap, 
  TrendingUp,
  BarChart3,
  Settings,
  Gamepad2
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface GamePerformanceProps {
  gameId: string;
  gameSlug: string;
}

interface PerformanceStats {
  total_surveys: number;
  average_fps: number | null;
  average_performance_rating: number | null;
  platforms: string[];
  device_types: string[];
  popular_gpus: Array<{ name: string; count: number }>;
  popular_cpus: Array<{ name: string; count: number }>;
}

export default function GamePerformance({ gameId, gameSlug }: GamePerformanceProps) {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [selectedDeviceType, setSelectedDeviceType] = useState<string>('all');

  const { data, isLoading, error } = useQuery({
    queryKey: ['game-performance', gameSlug],
    queryFn: async () => {
      const response = await fetch(`/api/games/by-slug/${gameSlug}/performance`);
      if (!response.ok) {
        throw new Error('Failed to fetch performance data');
      }
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const performanceData: GamePerformanceData[] = data?.performance_data || [];
  const stats: PerformanceStats = data?.performance_stats || {
    total_surveys: 0,
    average_fps: null,
    average_performance_rating: null,
    platforms: [],
    device_types: [],
    popular_gpus: [],
    popular_cpus: []
  };

  // Filter data based on selected filters
  const filteredData = performanceData.filter(item => {
    const platformMatch = selectedPlatform === 'all' || item.platform === selectedPlatform;
    const deviceMatch = selectedDeviceType === 'all' || item.device_type === selectedDeviceType;
    return platformMatch && deviceMatch;
  });

  const getPerformanceColor = (rating: number | null) => {
    if (!rating) return 'text-gray-400';
    if (rating >= 8) return 'text-green-400';
    if (rating >= 6) return 'text-yellow-400';
    if (rating >= 4) return 'text-orange-400';
    return 'text-red-400';
  };

  const getFpsColor = (fps: number | null) => {
    if (!fps) return 'text-gray-400';
    if (fps >= 60) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  const formatFps = (fps: number | null) => {
    if (!fps) return 'N/A';
    return `${fps.toFixed(0)} FPS`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-6 bg-gray-800/50 border-gray-700 animate-pulse">
            <div className="h-4 bg-gray-700 rounded w-1/3 mb-4" />
            <div className="space-y-2">
              <div className="h-3 bg-gray-700 rounded w-full" />
              <div className="h-3 bg-gray-700 rounded w-2/3" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 bg-red-900/20 border-red-500/30">
        <div className="text-center text-red-300">
          <p>Failed to load performance data. Please try again later.</p>
        </div>
      </Card>
    );
  }

  if (stats.total_surveys === 0) {
    return (
      <Card className="p-8 bg-gray-800/50 border-gray-700">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-4">📊</div>
          <h3 className="text-lg font-semibold text-white mb-2">No Performance Data Yet</h3>
          <p className="text-gray-400 mb-4">
            Be the first to submit performance data for this game!
          </p>
          <Button className="bg-green-600 hover:bg-green-500">
            Submit Performance Data
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-6 bg-gradient-to-br from-blue-900/20 to-blue-800/20 border-blue-500/30">
          <div className="flex items-center space-x-3 mb-2">
            <BarChart3 className="w-5 h-5 text-blue-400" />
            <h3 className="font-semibold text-white">Average FPS</h3>
          </div>
          <div className={`text-2xl font-bold ${getFpsColor(stats.average_fps)}`}>
            {formatFps(stats.average_fps)}
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Based on {stats.total_surveys} reports
          </p>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-green-900/20 to-green-800/20 border-green-500/30">
          <div className="flex items-center space-x-3 mb-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            <h3 className="font-semibold text-white">Performance Rating</h3>
          </div>
          <div className={`text-2xl font-bold ${getPerformanceColor(stats.average_performance_rating)}`}>
            {stats.average_performance_rating ? `${stats.average_performance_rating.toFixed(1)}/10` : 'N/A'}
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Community average
          </p>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-purple-900/20 to-purple-800/20 border-purple-500/30">
          <div className="flex items-center space-x-3 mb-2">
            <Monitor className="w-5 h-5 text-purple-400" />
            <h3 className="font-semibold text-white">Platforms</h3>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.platforms.length}
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Supported platforms
          </p>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4 bg-gray-800/50 border-gray-700">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-300">Filter by:</span>
          </div>
          
          <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
            <SelectTrigger className="w-40 bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="all">All Platforms</SelectItem>
              {stats.platforms.map(platform => (
                <SelectItem key={platform} value={platform}>{platform}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedDeviceType} onValueChange={setSelectedDeviceType}>
            <SelectTrigger className="w-40 bg-gray-700 border-gray-600 text-white">
              <SelectValue placeholder="Device Type" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-700">
              <SelectItem value="all">All Devices</SelectItem>
              {stats.device_types.map(deviceType => (
                <SelectItem key={deviceType} value={deviceType}>{deviceType}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="text-sm text-gray-400">
            Showing {filteredData.length} of {performanceData.length} reports
          </div>
        </div>
      </Card>

      {/* Popular Hardware */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Popular GPUs */}
        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <div className="flex items-center space-x-3 mb-4">
            <Zap className="w-5 h-5 text-yellow-400" />
            <h3 className="font-semibold text-white">Popular GPUs</h3>
          </div>
          <div className="space-y-3">
            {stats.popular_gpus.slice(0, 5).map((gpu, index) => (
              <div key={gpu.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-600 text-white' :
                    index === 1 ? 'bg-gray-500 text-white' :
                    index === 2 ? 'bg-orange-600 text-white' :
                    'bg-gray-700 text-gray-300'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="text-sm text-gray-300">{gpu.name}</span>
                </div>
                <Badge variant="secondary" className="bg-gray-700 text-gray-300">
                  {gpu.count} reports
                </Badge>
              </div>
            ))}
          </div>
        </Card>

        {/* Popular CPUs */}
        <Card className="p-6 bg-gray-800/50 border-gray-700">
          <div className="flex items-center space-x-3 mb-4">
            <Cpu className="w-5 h-5 text-blue-400" />
            <h3 className="font-semibold text-white">Popular CPUs</h3>
          </div>
          <div className="space-y-3">
            {stats.popular_cpus.slice(0, 5).map((cpu, index) => (
              <div key={cpu.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-600 text-white' :
                    index === 1 ? 'bg-gray-500 text-white' :
                    index === 2 ? 'bg-orange-600 text-white' :
                    'bg-gray-700 text-gray-300'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="text-sm text-gray-300">{cpu.name}</span>
                </div>
                <Badge variant="secondary" className="bg-gray-700 text-gray-300">
                  {cpu.count} reports
                </Badge>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Performance Reports */}
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Recent Performance Reports</h3>
        <div className="space-y-4">
          {filteredData.slice(0, 10).map((report, index) => (
            <div key={`${report.id}-${index}`} className="border border-gray-700 rounded-lg p-4 bg-gray-900/30">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-xs text-gray-400 mb-1">Platform & Device</div>
                  <div className="text-sm text-white">{report.platform}</div>
                  <div className="text-xs text-gray-400">{report.device_type}</div>
                </div>
                
                <div>
                  <div className="text-xs text-gray-400 mb-1">Hardware</div>
                  <div className="text-sm text-white">{report.gpu || 'Unknown GPU'}</div>
                  <div className="text-xs text-gray-400">{report.cpu || 'Unknown CPU'}</div>
                </div>
                
                <div>
                  <div className="text-xs text-gray-400 mb-1">Performance</div>
                  <div className={`text-sm font-medium ${getFpsColor(report.fps_average)}`}>
                    {formatFps(report.fps_average)}
                  </div>
                  {report.fps_min && report.fps_max && (
                    <div className="text-xs text-gray-400">
                      {report.fps_min}-{report.fps_max} FPS range
                    </div>
                  )}
                </div>
                
                <div>
                  <div className="text-xs text-gray-400 mb-1">Settings & Rating</div>
                  <div className="text-sm text-white">{report.graphics_settings || 'Unknown'}</div>
                  {report.performance_rating && (
                    <div className={`text-xs font-medium ${getPerformanceColor(report.performance_rating)}`}>
                      {report.performance_rating}/10 rating
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredData.length > 10 && (
          <div className="text-center mt-4">
            <Button variant="outline" className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600">
              View All Reports ({filteredData.length})
            </Button>
          </div>
        )}
      </Card>

      {/* Submit Your Own Data */}
      <Card className="p-6 bg-gradient-to-r from-green-900/20 to-blue-900/20 border-green-500/30">
        <div className="text-center">
          <div className="flex justify-center mb-3">
            <Gamepad2 className="w-8 h-8 text-green-400" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Share Your Performance Data</h3>
          <p className="text-gray-300 mb-4">
            Help the community by sharing how this game runs on your hardware!
          </p>
          <Button className="bg-green-600 hover:bg-green-500">
            Submit Performance Report
          </Button>
        </div>
      </Card>
    </div>
  );
}