// EnhancedThemePicker.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Check, Palette } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Theme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

const themes: Theme[] = [
  {
    id: 'default',
    name: 'Cosmic',
    colors: {
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      accent: '#a78bfa'
    }
  },
  {
    id: 'ocean',
    name: 'Ocean',
    colors: {
      primary: '#3b82f6',
      secondary: '#2563eb',
      accent: '#60a5fa'
    }
  },
  {
    id: 'forest',
    name: 'Forest',
    colors: {
      primary: '#10b981',
      secondary: '#059669',
      accent: '#34d399'
    }
  },
  {
    id: 'crimson',
    name: 'Crimson',
    colors: {
      primary: '#ef4444',
      secondary: '#dc2626',
      accent: '#f87171'
    }
  },
  {
    id: 'silver',
    name: 'Silver',
    colors: {
      primary: '#6b7280',
      secondary: '#4b5563',
      accent: '#9ca3af'
    }
  },
  {
    id: 'amber',
    name: '<PERSON>',
    colors: {
      primary: '#f59e0b',
      secondary: '#d97706',
      accent: '#fbbf24'
    }
  }
];

interface EnhancedThemePickerProps {
  value: string;
  onChange: (themeId: string) => void;
  onCustomColorsChange?: (colors: any) => void; // Keep for compatibility but won't be used
}

const EnhancedThemePicker: React.FC<EnhancedThemePickerProps> = ({ 
  value, 
  onChange 
}) => {
  const selectedTheme = themes.find(t => t.id === value) || themes[0];

  return (
    <div className="edit-form-group">
      <label className="edit-form-label primary">Theme Selection</label>

      {/* Theme Preview Card */}
      <div className="edit-add-profile-section mb-6">
        <div className="absolute inset-0 bg-gradient-to-br opacity-10"
          style={{
            backgroundImage: `linear-gradient(135deg, ${selectedTheme.colors.primary}30, ${selectedTheme.colors.secondary}20)`
          }}
        />
        
        <div className="relative space-y-3">
          <div className="flex items-center justify-between">
            <span className="edit-form-label">current_theme:</span>
            <span className="edit-form-label primary">{selectedTheme.name}</span>
          </div>
          
          <div className="flex gap-3 justify-center py-2">
            <div className="flex flex-col items-center gap-1">
              <div 
                className="w-12 h-12 rounded-full shadow-lg ring-2 ring-gray-700/50"
                style={{ backgroundColor: selectedTheme.colors.primary }}
              />
              <span className="edit-form-label">primary</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <div 
                className="w-12 h-12 rounded-full shadow-lg ring-2 ring-gray-700/50"
                style={{ backgroundColor: selectedTheme.colors.secondary }}
              />
              <span className="edit-form-label">secondary</span>
            </div>
            <div className="flex flex-col items-center gap-1">
              <div 
                className="w-12 h-12 rounded-full shadow-lg ring-2 ring-gray-700/50"
                style={{ backgroundColor: selectedTheme.colors.accent }}
              />
              <span className="edit-form-label">accent</span>
            </div>
          </div>
        </div>
      </div>

      {/* Theme Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {themes.map((theme) => (
          <motion.button
            key={theme.id}
            onClick={() => onChange(theme.id)}
            className={cn(
              "edit-button secondary relative p-4 pt-6 flex flex-col items-center justify-center min-h-[100px]",
              value === theme.id && "border-opacity-100 shadow-lg"
            )}
            style={{
              borderColor: value === theme.id ? theme.colors.primary : undefined
            }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Theme colors preview */}
            <div className="flex justify-center gap-1 mb-1 mt-3">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: theme.colors.primary }}
              />
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: theme.colors.secondary }}
              />
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: theme.colors.accent }}
              />
            </div>
            
            {/* Theme name below circles */}
            <div className="text-center">
              <span className={cn(
                "edit-form-label text-xs",
                value === theme.id && "primary"
              )}>
                {theme.name}
              </span>
            </div>
            
            {/* Selected indicator */}
            {value === theme.id && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute top-2 right-2 w-5 h-5 rounded-full flex items-center justify-center"
                style={{ backgroundColor: theme.colors.primary }}
              >
                <Check className="h-3 w-3 text-white" />
              </motion.div>
            )}
            
            {/* Hover glow effect */}
            <div 
              className={cn(
                "absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200",
                "pointer-events-none"
              )}
              style={{
                boxShadow: `0 0 20px ${theme.colors.primary}40`,
                opacity: value === theme.id ? 0.3 : 0
              }}
            />
          </motion.button>
        ))}
      </div>

      {/* Theme info */}
      <div className="edit-add-profile-section mt-4">
        <p className="edit-form-label">
          // Themes apply accent colors to components like avatars, buttons, and cards
        </p>
      </div>
    </div>
  );
};

export default EnhancedThemePicker;