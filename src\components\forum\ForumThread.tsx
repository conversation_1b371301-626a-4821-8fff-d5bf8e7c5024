'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, Clock, User, Reply, Send, Plus, Minus, Flag, Ban, Smile } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useForumThread, useForumMutations, UserBlockedError } from '@/hooks/useForumMutations';
import { ForumReportButton } from './ForumReportButton';
import { BlockedUserModal } from '@/components/moderation/BlockedUserModal';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';

interface ForumThreadProps {
  postId: string;
  reviewId: string;
  currentUserId?: string;
  reviewAuthorId: string;
  highlightedCommentId?: string | null;
}

// Gaming-focused emojis for quick access
const commonEmojis = [
  '🎮', '🕹️', '🎯', '🏆', '👑', '💎', '🔥', '💥',
  '⚡', '🚀', '🎲', '🎪', '🌟', '💀', '👾', '🤖',
  '😤', '😎', '🤩', '🥵', '🤯', '😈', '💪', '👊'
];

// Component to show banned user flag
function BannedUserFlag({ userId, reviewAuthorId }: { userId: string; reviewAuthorId: string }) {
  const { data: isBanned } = useQuery({
    queryKey: ['user-banned', userId, reviewAuthorId],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewAuthorId)
        .eq('blocked_id', userId)
        .maybeSingle();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking user ban status:', error);
        return false;
      }
      
      return !!data;
    },
    enabled: !!userId && !!reviewAuthorId && userId !== reviewAuthorId,
  });

  if (!isBanned) return null;

  return (
    <div className="absolute -top-1 -right-1 bg-red-600 rounded-full p-1 border-2 border-slate-800">
      <Ban className="w-3 h-3 text-white" />
    </div>
  );
}

// Component to show banned tag under username
function BannedUserTag({ userId, reviewAuthorId }: { userId: string; reviewAuthorId: string }) {
  const { data: isBanned } = useQuery({
    queryKey: ['user-banned', userId, reviewAuthorId],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewAuthorId)
        .eq('blocked_id', userId)
        .maybeSingle();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking user ban status:', error);
        return false;
      }
      
      return !!data;
    },
    enabled: !!userId && !!reviewAuthorId && userId !== reviewAuthorId,
  });

  if (!isBanned) return null;

  return (
    <div className="mt-1">
      <Badge variant="destructive" className="bg-red-900/30 text-red-300 border-red-800/40 text-xs px-2 py-0.5">
        <Ban className="w-2.5 h-2.5 mr-1" />
        Banned
      </Badge>
    </div>
  );
}

export function ForumThread({ postId, reviewId, currentUserId, reviewAuthorId, highlightedCommentId }: ForumThreadProps) {
  const [replyContent, setReplyContent] = useState('');
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [showBlockedModal, setShowBlockedModal] = useState(false);
  const [blockedByUserName, setBlockedByUserName] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { data: thread, isLoading, error } = useForumThread(postId);
  const { createReply, voteOnPost, voteOnReply } = useForumMutations(reviewId);

  // Fetch flagged comments for this thread
  const { data: flaggedComments } = useQuery({
    queryKey: ['flagged-comments', postId],
    queryFn: async () => {
      const supabase = createClient();
      
      // Get all comment IDs in this thread (post + replies)
      const commentIds = [postId];
      if (thread?.replies) {
        commentIds.push(...thread.replies.map(r => r.id));
      }
      
      // Get flags for these comments
      const { data: flags, error } = await supabase
        .from('content_flags')
        .select('content_id')
        .in('content_id', commentIds)
        .eq('content_type', 'comment')
        .eq('status', 'pending');
      
      if (error) {
        console.error('Error fetching flagged comments:', error);
        return [];
      }
      
      return flags?.map(f => f.content_id) || [];
    },
    enabled: !!thread,
  });

  const handleReply = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!replyContent.trim()) {
      toast.error('Please enter a reply');
      return;
    }

    try {
      await createReply.mutateAsync({
        postId,
        content: replyContent.trim()
      });
      
      setReplyContent('');
      setShowReplyForm(false);
      toast.success('Reply posted successfully!');
    } catch (error) {
      console.error('Error creating reply:', error);
      
      if (error instanceof UserBlockedError) {
        setBlockedByUserName(error.contentOwnerName);
        setShowBlockedModal(true);
      } else {
        toast.error('Failed to post reply. Please try again.');
      }
    }
  };

  const insertEmoji = (emoji: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = replyContent.substring(0, start) + emoji + replyContent.substring(end);
      setReplyContent(newContent);

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
      }, 0);
    }
    setShowEmojiPicker(false);
  };

  const handleVotePost = async (currentVote: string | null, newVote: 'upvote' | 'downvote') => {
    const voteType = currentVote === newVote ? null : newVote;
    try {
      await voteOnPost.mutateAsync({ postId, voteType });
    } catch (error) {
      console.error('Error voting on post:', error);
      toast.error('Failed to vote. Please try again.');
    }
  };

  const handleVoteReply = async (replyId: string, currentVote: string | null, newVote: 'upvote' | 'downvote') => {
    const voteType = currentVote === newVote ? null : newVote;
    try {
      await voteOnReply.mutateAsync({ replyId, voteType });
    } catch (error) {
      console.error('Error voting on reply:', error);
      toast.error('Failed to vote. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1 space-y-3">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          </CardContent>
        </Card>
        {[...Array(2)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700/50 ml-8">
            <CardContent className="p-4">
              <div className="flex gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !thread) {
    return (
      <div className="text-center py-12 text-slate-400">
        <MessageCircle className="mx-auto h-12 w-12 mb-4 opacity-50" />
        <p>Unable to load discussion thread.</p>
      </div>
    );
  }

  const post = thread.post;
  const replies = thread.replies || [];
  const isAuthorPost = post.author_id === reviewAuthorId;
  const postScore = post.upvotes - post.downvotes;
  
  // Check if post or any replies are flagged
  const isPostFlagged = flaggedComments?.includes(postId) || false;

  return (
    <div className="space-y-6">
      {/* Main Post - Gaming-Themed */}
      <Card 
        id={`comment-${postId}`}
        className={cn(
          "bg-gradient-to-br border transition-all duration-200",
          isPostFlagged
            ? "from-red-950/40 to-red-900/40 border-red-800/40"
            : "from-slate-800/60 to-slate-900/60 border-slate-700/50"
        )}
      >
        <CardContent className="p-0">
          {/* Desktop Layout */}
          <div className="hidden sm:block">
            <div className="p-6 pb-4">
              <div className="flex gap-4">
                {/* Left Side - Voting + Avatar */}
                <div className="flex items-start gap-3">
                  {/* Voting Buttons */}
                  {currentUserId && (
                    <div className="flex flex-col items-center gap-2 pt-1">
                      {/* Plus Button (Upvote) */}
                      <button
                        type="button"
                        onClick={() => handleVotePost(post.user_vote, 'upvote')}
                        disabled={voteOnPost.isPending}
                        className={cn(
                          "group relative flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-200 border-2 font-bold",
                          post.user_vote === 'upvote'
                            ? "bg-emerald-500/25 border-emerald-400/60 text-emerald-300"
                            : "bg-slate-800/50 border-slate-600/50 text-slate-400 hover:bg-emerald-500/15 hover:border-emerald-400/40 hover:text-emerald-300"
                        )}
                      >
                        <Plus className={cn(
                          "h-5 w-5 transition-all duration-200",
                          post.user_vote === 'upvote' && "scale-110",
                          voteOnPost.isPending && "animate-pulse"
                        )} />
                        {post.user_vote === 'upvote' && (
                          <div className="absolute -inset-1 bg-emerald-400/30 rounded-xl animate-pulse" />
                        )}
                      </button>

                      {/* Minus Button (Downvote) */}
                      <button
                        type="button"
                        onClick={() => handleVotePost(post.user_vote, 'downvote')}
                        disabled={voteOnPost.isPending}
                        className={cn(
                          "group relative flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-200 border-2 font-bold",
                          post.user_vote === 'downvote'
                            ? "bg-red-500/25 border-red-400/60 text-red-300"
                            : "bg-slate-800/50 border-slate-600/50 text-slate-400 hover:bg-red-500/15 hover:border-red-400/40 hover:text-red-300"
                        )}
                      >
                        <Minus className={cn(
                          "h-5 w-5 transition-all duration-200",
                          post.user_vote === 'downvote' && "scale-110",
                          voteOnPost.isPending && "animate-pulse"
                        )} />
                        {post.user_vote === 'downvote' && (
                          <div className="absolute -inset-1 bg-red-400/30 rounded-xl animate-pulse" />
                        )}
                      </button>
                    </div>
                  )}

                  {/* Avatar - Right of voting buttons */}
                  <div className="relative pt-1">
                    <div className="w-[88px] h-[88px] rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 p-0.5">
                      <img
                        src={post.author?.avatar_url || '/imgs/profile.svg'}
                        alt={post.author?.display_name || 'User'}
                        className="w-full h-full rounded-xl object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = '/imgs/profile.svg';
                        }}
                      />
                    </div>
                    {/* Banned User Flag */}
                    <BannedUserFlag userId={post.author_id} reviewAuthorId={reviewAuthorId} />
                  </div>
                </div>

                {/* Content Section */}
                <div className="flex-1 min-w-0 pr-20">
                  <div className="flex items-center gap-3 mb-4 flex-wrap">
                    <h1 className="text-xl font-bold text-slate-100 leading-tight">
                      {post.title}
                    </h1>
                    
                    {isAuthorPost && (
                      <Badge variant="secondary" className="bg-slate-800 text-slate-200 border-slate-600 text-xs font-bold px-1.5 py-0.5 h-5 w-8 flex items-center justify-center rounded-md">
                        OP
                      </Badge>
                    )}
                    
                    {post.category && (
                      <Badge variant="outline" className="text-purple-400 border-purple-500/40 text-xs font-medium">
                        {post.category}
                      </Badge>
                    )}
                    
                    {isPostFlagged && (
                      <Badge variant="destructive" className="bg-red-900/30 text-red-300 border-red-800/40 text-xs px-2 py-1">
                        <Flag className="h-3 w-3 mr-1" />
                        Flagged
                      </Badge>
                    )}
                  </div>

                  <div className="prose prose-slate prose-invert max-w-none">
                    <p className="text-slate-200 leading-relaxed whitespace-pre-wrap font-medium">
                      {post.is_deleted ? (
                        <span className="text-slate-400 italic">
                          This comment was removed by moderation
                        </span>
                      ) : (
                        post.content
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="sm:hidden">
            <div className="p-4">
              {/* Mobile Header */}
              <div className="flex gap-3 mb-3">
                <div className="relative flex-shrink-0">
                  <img
                    src={post.author?.avatar_url || '/imgs/profile.svg'}
                    alt={post.author?.display_name || 'User'}
                    className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/imgs/profile.svg';
                    }}
                  />
                  {/* Banned User Flag */}
                  <BannedUserFlag userId={post.author_id} reviewAuthorId={reviewAuthorId} />
                </div>
                
                <div className="flex-1 min-w-0 pr-12">
                  <h1 className="text-lg font-bold text-slate-100 leading-tight mb-2">
                    {post.title}
                  </h1>
                  
                  <div className="flex items-center gap-2 flex-wrap">
                    {isAuthorPost && (
                      <Badge variant="secondary" className="bg-slate-800 text-slate-200 border-slate-600 text-xs font-bold px-1.5 py-0.5 h-5 w-8 flex items-center justify-center rounded-md">
                        OP
                      </Badge>
                    )}
                    
                    {post.category && (
                      <Badge variant="outline" className="text-purple-400 border-purple-500/40 text-xs font-medium">
                        {post.category}
                      </Badge>
                    )}
                    
                    {isPostFlagged && (
                      <Badge variant="destructive" className="bg-red-900/30 text-red-300 border-red-800/40 text-xs px-2 py-1">
                        <Flag className="h-3 w-3 mr-1" />
                        Flagged
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Mobile Content */}
              <div className="mb-4">
                <p className="text-slate-200 leading-relaxed whitespace-pre-wrap text-sm">
                  {post.is_deleted ? (
                    <span className="text-slate-400 italic">
                      This comment was removed by moderation
                    </span>
                  ) : (
                    post.content
                  )}
                </p>
              </div>

              {/* Mobile Voting */}
              {currentUserId && (
                <div className="flex items-center justify-center gap-4 mb-4 py-3 bg-slate-800/30 rounded-lg">
                  <button
                    type="button"
                    onClick={() => handleVotePost(post.user_vote, 'upvote')}
                    disabled={voteOnPost.isPending}
                    className={cn(
                      "flex items-center justify-center w-12 h-8 rounded-lg transition-all duration-200 border font-bold text-sm",
                      post.user_vote === 'upvote'
                        ? "bg-emerald-500/25 border-emerald-400/60 text-emerald-300"
                        : "bg-slate-800/50 border-slate-600/50 text-slate-400 hover:bg-emerald-500/15 hover:border-emerald-400/40 hover:text-emerald-300"
                    )}
                  >
                    <Plus className="h-4 w-4" />
                  </button>

                  <span className="font-mono font-bold text-slate-300 min-w-[3rem] text-center">
                    {postScore > 0 ? `+${postScore}` : postScore}
                  </span>

                  <button
                    type="button"
                    onClick={() => handleVotePost(post.user_vote, 'downvote')}
                    disabled={voteOnPost.isPending}
                    className={cn(
                      "flex items-center justify-center w-12 h-8 rounded-lg transition-all duration-200 border font-bold text-sm",
                      post.user_vote === 'downvote'
                        ? "bg-red-500/25 border-red-400/60 text-red-300"
                        : "bg-slate-800/50 border-slate-600/50 text-slate-400 hover:bg-red-500/15 hover:border-red-400/40 hover:text-red-300"
                    )}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Footer - Metadata and Actions */}
          <div className="border-t border-slate-700/40 bg-slate-800/20 px-3 py-2 sm:px-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
              <div className="flex items-center gap-3 sm:gap-6 text-xs text-slate-400 font-mono flex-wrap">
                <div className="flex flex-col gap-1">
                  <div className="flex items-center gap-1 sm:gap-2">
                    <User className="w-3 h-3" />
                    <span className="font-medium">{post.author?.display_name || 'Anonymous'}</span>
                  </div>
                  <BannedUserTag userId={post.author_id} reviewAuthorId={reviewAuthorId} />
                </div>
                <div className="flex items-center gap-1 sm:gap-2">
                  <Clock className="w-3 h-3" />
                  <span>{formatDistanceToNow(new Date(post.created_at))} ago</span>
                </div>
                <div className="flex items-center gap-1 sm:gap-2">
                  <MessageCircle className="w-3 h-3" />
                  <span className="font-medium">{replies.length} {replies.length === 1 ? 'reply' : 'replies'}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* Score Display */}
                <div className="flex items-center justify-center px-3 h-7 bg-slate-800 border border-slate-600 rounded-md">
                  <span className="text-xs font-mono font-bold text-slate-300">
                    {postScore > 0 ? `+${postScore}` : postScore}
                  </span>
                </div>

                {currentUserId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowReplyForm(!showReplyForm)}
                    className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 hover:border-purple-500/40 hover:text-purple-300 transition-all duration-200 h-7 self-start sm:self-auto"
                  >
                    <Reply className="w-3 h-3 mr-1" />
                    Reply
                  </Button>
                )}

                {/* Report Button */}
                <ForumReportButton
                  postId={postId}
                  postTitle={post.title || 'Forum Post'}
                  size="sm"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Reply Form */}
      {showReplyForm && currentUserId && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="ml-0 sm:ml-16"
        >
          <Card className="bg-gradient-to-br from-slate-800/40 to-slate-900/40 border border-slate-700/40">
            <CardContent className="p-3 sm:p-5">
              <div className="mb-3">
                <div className="flex items-center justify-between">
                  <label className="font-mono text-sm text-slate-300">
                    <span className="text-slate-500">//</span> Reply to {post.author?.display_name || 'Anonymous'}
                  </label>
                  <div className="flex items-center gap-2 relative">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      className="text-slate-400 hover:text-white h-8 px-2"
                    >
                      <Smile className="w-4 h-4" />
                    </Button>

                    {/* Emoji Picker */}
                    <AnimatePresence>
                      {showEmojiPicker && (
                        <motion.div
                          initial={{ opacity: 0, x: 10, y: 10, scale: 0.95 }}
                          animate={{ opacity: 1, x: 0, y: 0, scale: 1 }}
                          exit={{ opacity: 0, x: 10, y: 10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                          className="absolute top-full right-0 mt-2 bg-slate-800 border border-slate-500 rounded-xl p-4 shadow-2xl z-50 w-72"
                        >
                          <div className="mb-2">
                            <h3 className="font-mono text-xs text-slate-400">
                              <span className="text-slate-500">//</span> Quick Emojis
                            </h3>
                          </div>
                          <div className="grid grid-cols-8 gap-1">
                            {commonEmojis.map((emoji) => (
                              <button
                                key={emoji}
                                type="button"
                                onClick={() => insertEmoji(emoji)}
                                className="w-8 h-8 flex items-center justify-center text-lg hover:bg-slate-700/70 rounded-lg transition-all duration-200 hover:scale-110 active:scale-95"
                              >
                                {emoji}
                              </button>
                            ))}
                          </div>
                          <div className="mt-3 pt-2 border-t border-slate-700/50">
                            <p className="font-mono text-xs text-slate-500 text-center">
                              Click an emoji to insert
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </div>
              <form onSubmit={handleReply} className="space-y-4">
                <Textarea
                  ref={textareaRef}
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  placeholder="Share your thoughts..."
                  className="bg-slate-900/60 border-slate-600/50 text-slate-200 placeholder:text-slate-500 min-h-[80px] sm:min-h-[100px] rounded-xl focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200"
                  maxLength={1000}
                />
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-0">
                  <span className="text-xs text-slate-500 font-mono text-center sm:text-left">
                    {replyContent.length}/1000 characters
                  </span>
                  <div className="flex gap-3 justify-center sm:justify-end">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowReplyForm(false);
                        setReplyContent('');
                      }}
                      className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 transition-all duration-200 flex-1 sm:flex-none"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      size="sm"
                      disabled={createReply.isPending || !replyContent.trim()}
                      className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white transition-all duration-200 flex-1 sm:flex-none"
                    >
                      {createReply.isPending ? (
                        <>
                          <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Posting...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Post Reply
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Enhanced Replies Section */}
      {replies.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-slate-200 ml-0 sm:ml-16 font-mono">
            <span className="text-slate-500">//</span> Replies ({replies.length})
          </h3>
          
          {replies.map((reply, index) => {
            const isAuthorReply = reply.author_id === reviewAuthorId;
            const isReplyFlagged = flaggedComments?.includes(reply.id) || false;
            
            return (
              <motion.div
                key={reply.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="ml-0 sm:ml-16"
              >
                <Card 
                  id={`comment-${reply.id}`}
                  className={cn(
                    "bg-gradient-to-br border transition-all duration-200",
                    isReplyFlagged
                      ? "from-red-950/30 to-red-900/30 border-red-800/30"
                      : "from-slate-800/30 to-slate-900/30 border-slate-700/30"
                  )}
                >
                  <CardContent className="p-3 sm:p-5">
                    <div className="flex gap-3 sm:gap-4">
                      {/* Avatar with Gaming Border */}
                      <div className="relative flex-shrink-0">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-gradient-to-br from-purple-500/15 to-blue-500/15 p-0.5">
                          <img
                            src={reply.author?.avatar_url || '/imgs/profile.svg'}
                            alt={reply.author?.display_name || 'User'}
                            className="w-full h-full rounded-lg object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/imgs/profile.svg';
                            }}
                          />
                        </div>
                        {/* Banned User Flag */}
                        <BannedUserFlag userId={reply.author_id} reviewAuthorId={reviewAuthorId} />
                      </div>

                      {/* Enhanced Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-start gap-1 sm:gap-3 mb-2 sm:mb-3">
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                              <span className="font-bold text-slate-200 text-sm">
                                {reply.author?.display_name || 'Anonymous'}
                              </span>
                              
                              {isAuthorReply && (
                                <Badge variant="secondary" className="bg-slate-800 text-slate-200 border-slate-600 text-xs font-bold px-1.5 py-0.5 h-4 w-7 flex items-center justify-center rounded-md">
                                  OP
                                </Badge>
                              )}
                              
                              {isReplyFlagged && (
                                <Badge variant="destructive" className="bg-red-900/30 text-red-300 border-red-800/40 text-xs px-2 py-1">
                                  <Flag className="h-3 w-3 mr-1" />
                                  Flagged
                                </Badge>
                              )}
                            </div>
                            <BannedUserTag userId={reply.author_id} reviewAuthorId={reviewAuthorId} />
                          </div>
                          
                          <span className="text-xs text-slate-500 font-mono">
                            {formatDistanceToNow(new Date(reply.created_at))} ago
                          </span>
                        </div>

                        <p className="text-slate-300 text-sm leading-relaxed whitespace-pre-wrap mb-3">
                          {reply.is_deleted ? (
                            <span className="text-slate-400 italic">
                              This comment was removed by moderation
                            </span>
                          ) : (
                            reply.content
                          )}
                        </p>

                        {/* Reply Actions */}
                        <div className="flex items-center justify-end">
                          <ForumReportButton
                            postId={reply.id}
                            postTitle={`Reply by ${reply.author?.display_name || 'Anonymous'}`}
                            size="xs"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      )}

      {/* Blocked User Modal */}
      <BlockedUserModal
        isOpen={showBlockedModal}
        onClose={() => setShowBlockedModal(false)}
        contentOwnerName={blockedByUserName}
      />
    </div>
  );
}
