'use client';

import { useState, useEffect } from 'react';

const STORAGE_KEY = 'background-dimmer-value';
const DEFAULT_DIMMER_VALUE = 50; // 50% = original brightness, 0% = darkest, 100% = brightest

export const useBackgroundDimmer = () => {
  const [dimmerValue, setDimmerValue] = useState<number>(DEFAULT_DIMMER_VALUE);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load dimmer value from localStorage on mount
  useEffect(() => {
    const savedValue = localStorage.getItem(STORAGE_KEY);
    if (savedValue !== null) {
      const parsedValue = parseInt(savedValue, 10);
      if (!isNaN(parsedValue) && parsedValue >= 0 && parsedValue <= 100) {
        setDimmerValue(parsedValue);
      }
    }
    setIsLoaded(true);
  }, []);

  // Apply dimmer effect to document root
  useEffect(() => {
    if (!isLoaded) return;

    // Convert dimmer value to overlay opacities
    // 0% = very dark, 50% = original, 100% = almost white
    let darkOpacity = 0;
    let lightOpacity = 0;
    
    if (dimmerValue <= 50) {
      // 0-50%: Add dark overlay (0.8 max darkness at 0%)
      darkOpacity = (50 - dimmerValue) / 50 * 0.8;
      lightOpacity = 0;
    } else {
      // 50-100%: Add light overlay (0.9 max brightness at 100% - almost white)
      darkOpacity = 0;
      lightOpacity = (dimmerValue - 50) / 50 * 0.9;
    }
    
    // Apply CSS custom properties to document root
    document.documentElement.style.setProperty('--bg-dimmer-dark-opacity', darkOpacity.toString());
    document.documentElement.style.setProperty('--bg-dimmer-light-opacity', lightOpacity.toString());
  }, [dimmerValue, isLoaded]);

  // Update dimmer value and save to localStorage
  const updateDimmerValue = (value: number) => {
    const clampedValue = Math.max(0, Math.min(100, value));
    setDimmerValue(clampedValue);
    localStorage.setItem(STORAGE_KEY, clampedValue.toString());
  };

  return {
    dimmerValue,
    updateDimmerValue,
    isLoaded,
  };
};
