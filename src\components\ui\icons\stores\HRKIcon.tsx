import React from 'react';

interface IconProps {
  className?: string;
  [key: string]: any;
}

const HRKIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 32 32"
    fill="currentColor"
    {...props}
  >
    <title>HRK Game</title>
    {/* Simplified HRK logo representation */}
    <rect width="32" height="32" fill="#E74C3C" rx="6"/>
    <text x="16" y="20" textAnchor="middle" fill="white" fontSize="11" fontFamily="Arial, sans-serif" fontWeight="bold">
      HRK
    </text>
  </svg>
);

export default HRKIcon;