# CriticalPixel Design System Overview

## 🎨 Design Philosophy

CriticalPixel embodies a **dark, sophisticated gaming aesthetic** that balances professional design with gaming culture. The design system prioritizes:

- **Dark-first approach**: Deep backgrounds with strategic lighting
- **Purple cosmic theme**: Primary brand color #8b5cf6 with gradient variations
- **Code/Terminal aesthetic**: Monospace fonts and developer-inspired UI patterns
- **Card-based layouts**: Modular, contained content sections
- **Subtle animations**: Smooth transitions with cubic-bezier easing
- **Gaming-focused UX**: Intuitive navigation for gaming content

## 🌈 Color System

### Primary Brand Colors
```css
/* Core Brand Identity */
--primary-purple: #8b5cf6;        /* Main brand color */
--secondary-purple: #7c3aed;      /* Darker variant */
--accent-purple: #a78bfa;         /* Lighter accent */
--purple-rgb: 139, 92, 246;       /* RGB values for opacity */
```

### Background System
```css
/* Background Hierarchy */
--bg-primary: #030712;            /* Main page background (gray-950) */
--bg-secondary: rgba(17, 24, 39, 0.5);  /* Card backgrounds (gray-900/50) */
--bg-tertiary: rgba(30, 41, 59, 0.3);   /* Nested content (slate-800/30) */
--bg-overlay: rgba(16, 18, 27, 0.75);   /* Modal overlays */

/* Gradient Backgrounds */
--bg-gradient-main: linear-gradient(to bottom right, #1a202c, #35363A);
--bg-gradient-card: linear-gradient(to bottom right, rgba(15, 23, 42, 0.5), rgba(30, 41, 59, 0.3));
--bg-gradient-hover: linear-gradient(to bottom right, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.2));
```

### Text Hierarchy
```css
/* Text Color System */
--text-primary: #f1f5f9;          /* Main content text (slate-100) */
--text-secondary: #94a3b8;        /* Secondary text (slate-400) */
--text-muted: #64748b;            /* Muted text (slate-500) */
--text-accent: #8b5cf6;           /* Accent text (brand purple) */
```

### Border & Effects
```css
/* Border System */
--border-primary: rgba(71, 85, 105, 0.3);    /* Main borders (slate-600/30) */
--border-secondary: rgba(71, 85, 105, 0.2);  /* Subtle borders */
--border-focus: rgba(139, 92, 246, 0.5);     /* Focus states */
--border-glow: rgba(139, 92, 246, 0.8);      /* Glow effects */

/* Shadow System */
--shadow-card: 0 20px 60px rgba(0, 0, 0, 0.4);
--shadow-hover: 0 25px 80px rgba(0, 0, 0, 0.5);
--shadow-glow: 0 0 20px rgba(139, 92, 246, 0.2);
```

## 🎯 Theme Variations

### Available Themes
The system supports 6 carefully curated themes:

1. **Cosmic (Default)**: Purple tones matching site aesthetic
2. **Ocean**: Calming blue tones (#3b82f6)
3. **Forest**: Natural green hues (#10b981)
4. **Crimson**: Bold red accents (#ef4444)
5. **Sunset**: Warm orange tones (#f59e0b)
6. **Monochrome**: Classic black/white/gray

### Theme Implementation
```css
/* Theme CSS Variables Pattern */
.theme-cosmic {
  --theme-primary: #8b5cf6;
  --theme-secondary: #7c3aed;
  --theme-accent: #a78bfa;
  --theme-primary-rgb: 139, 92, 246;
}
```

## 📐 Spacing System

### Standard Spacing Scale
```css
/* Tailwind-based spacing (rem units) */
--space-xs: 0.25rem;    /* 4px */
--space-sm: 0.5rem;     /* 8px */
--space-md: 1rem;       /* 16px */
--space-lg: 1.5rem;     /* 24px */
--space-xl: 2rem;       /* 32px */
--space-2xl: 3rem;      /* 48px */
--space-3xl: 4rem;      /* 64px */
```

### Component Spacing
- **Card padding**: 24px (1.5rem)
- **Section margins**: 32px (2rem)
- **Element gaps**: 16px (1rem)
- **Form spacing**: 24px between groups

## 🔧 Border Radius System

```css
/* Consistent border radius scale */
--radius-sm: 6px;       /* Small elements */
--radius-md: 8px;       /* Default radius */
--radius-lg: 12px;      /* Cards and containers */
--radius-xl: 16px;      /* Large containers */
--radius-full: 9999px;  /* Pills and circles */
```

## 🌟 Visual Effects

### Backdrop Filters
```css
/* Glassmorphism effects */
--blur-sm: blur(8px);
--blur-md: blur(12px);
--blur-lg: blur(16px);
```

### Transitions
```css
/* Standard easing and duration */
--transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
--transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
--transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
```

## 📱 Responsive Breakpoints

```css
/* Custom breakpoints */
--bp-sm: 640px;         /* Mobile landscape */
--bp-md: 768px;         /* Tablet */
--bp-lg: 1024px;        /* Desktop */
--bp-xl: 1280px;        /* Large desktop */
--bp-2xl: 1536px;       /* Extra large */
--bp-1360: 1360px;      /* Custom breakpoint for layout */
```

## 🎮 Gaming-Specific Elements

### Score/Rating Colors
```css
/* Score tier colors */
--score-excellent: #10b981;  /* 9.0+ (green) */
--score-great: #3b82f6;      /* 8.0-8.9 (blue) */
--score-good: #f59e0b;       /* 7.0-7.9 (orange) */
--score-average: #ef4444;    /* 6.0-6.9 (red) */
--score-poor: #6b7280;       /* <6.0 (gray) */
```

### Platform Colors
```css
/* Gaming platform identification */
--platform-pc: #00d4aa;
--platform-ps5: #0070f3;
--platform-xbox: #107c10;
--platform-switch: #e60012;
--platform-steam: #1b2838;
```

## 🔍 Accessibility Standards

- **Contrast ratios**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Focus indicators**: Visible purple outline (--border-focus)
- **Color independence**: Never rely solely on color for information
- **Touch targets**: Minimum 44px for interactive elements
- **Motion respect**: Reduced motion support via `prefers-reduced-motion`

## 📋 Implementation Notes

### CSS Architecture
- **Tailwind CSS**: Primary utility framework
- **CSS Custom Properties**: For theming and dynamic values
- **Component-specific CSS**: Scoped styles in dedicated files
- **Shadcn UI**: Base component library with custom theming

### File Organization
```
src/components/style/
├── navColors.css       # Navigation-specific styles
├── gamerCard.css       # Card component styles
├── authModal.css       # Authentication modal styles
├── editProfileModal.css # Profile editing styles
└── profile-themes.css  # Theme system definitions
```

### Performance Considerations
- **CSS-in-JS avoided**: Prefer CSS files for better performance
- **Critical CSS**: Inline essential styles for above-the-fold content
- **Lazy loading**: Non-critical styles loaded asynchronously
- **Purging**: Unused Tailwind classes removed in production

---

*This design system serves as the foundation for all CriticalPixel UI implementations. Consistency in applying these guidelines ensures a cohesive, professional gaming platform experience.*
