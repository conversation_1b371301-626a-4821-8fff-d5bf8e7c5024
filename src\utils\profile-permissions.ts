import type { UserProfile, ProfileViewPermissions, PrivacySettings } from '@/lib/types/profile';

/**
 * Calculate what a viewer can see on a user's profile based on privacy settings
 */
export function calculateProfilePermissions(
  profile: UserProfile,
  viewerId?: string,
  isAdmin?: boolean
): ProfileViewPermissions {
  const isOwner = viewerId === profile.id;
  const privacy = profile.privacy_settings;
  
  // Owner and admins can see everything
  if (isOwner || isAdmin) {
    return {
      canViewOnlineStatus: true,
      canViewGamingProfiles: true,
      canViewSocialProfiles: true,
      canViewAchievements: true,
      canViewContactInfo: true,
      canViewFullProfile: true,
      canViewStats: true,
      canSendFriendRequest: false, // Can't friend yourself
      canContact: true
    };
  }
  
  // Default privacy settings if none set
  const defaultPrivacy: PrivacySettings = {
    profile_visibility: 'public',
    show_online_status: true,
    show_gaming_profiles: true,
    show_social_profiles: true,
    show_achievements: true,
    allow_contact: true,
    allow_friend_requests: true
  };
  
  const effectivePrivacy = privacy || defaultPrivacy;
  
  // Private profile - very limited access
  if (effectivePrivacy.profile_visibility === 'private') {
    return {
      canViewOnlineStatus: false,
      canViewGamingProfiles: false,
      canViewSocialProfiles: false,
      canViewAchievements: false,
      canViewContactInfo: false,
      canViewFullProfile: false,
      canViewStats: false,
      canSendFriendRequest: effectivePrivacy.allow_friend_requests,
      canContact: false
    };
  }
  
  // Friends-only profile (TODO: implement friend relationship check)
  if (effectivePrivacy.profile_visibility === 'friends') {
    // For now, treat as public since we don't have friend relationships implemented
    // In the future, check if viewerId is in profile owner's friends list
    const isFriend = false; // TODO: implement friend check
    
    if (!isFriend) {
      return {
        canViewOnlineStatus: false,
        canViewGamingProfiles: false,
        canViewSocialProfiles: false,
        canViewAchievements: false,
        canViewContactInfo: false,
        canViewFullProfile: false,
        canViewStats: false,
        canSendFriendRequest: effectivePrivacy.allow_friend_requests,
        canContact: effectivePrivacy.allow_contact
      };
    }
  }
  
  // Public profile with granular privacy controls
  return {
    canViewOnlineStatus: effectivePrivacy.show_online_status,
    canViewGamingProfiles: effectivePrivacy.show_gaming_profiles,
    canViewSocialProfiles: effectivePrivacy.show_social_profiles,
    canViewAchievements: effectivePrivacy.show_achievements,
    canViewContactInfo: effectivePrivacy.allow_contact,
    canViewFullProfile: effectivePrivacy.profile_visibility === 'public',
    canViewStats: effectivePrivacy.profile_visibility === 'public',
    canSendFriendRequest: effectivePrivacy.allow_friend_requests,
    canContact: effectivePrivacy.allow_contact
  };
}

/**
 * Check if a user can perform a specific action on a profile
 */
export function canPerformAction(
  action: 'view' | 'edit' | 'contact' | 'friend' | 'admin',
  profile: UserProfile,
  viewerId?: string,
  isAdmin?: boolean
): boolean {
  const permissions = calculateProfilePermissions(profile, viewerId, isAdmin);
  const isOwner = viewerId === profile.id;
  
  switch (action) {
    case 'view':
      return permissions.canViewFullProfile;
    case 'edit':
      return isOwner || (isAdmin || false);
    case 'contact':
      return permissions.canContact;
    case 'friend':
      return permissions.canSendFriendRequest && !isOwner;
    case 'admin':
      return isAdmin || false;
    default:
      return false;
  }
}

/**
 * Filter profile data based on viewer permissions
 */
export function filterProfileData(
  profile: UserProfile,
  viewerId?: string,
  isAdmin?: boolean
): Partial<UserProfile> {
  const permissions = calculateProfilePermissions(profile, viewerId, isAdmin);
  
  // Always show basic info
  const filteredProfile: Partial<UserProfile> = {
    id: profile.id,
    username: profile.username,
    display_name: profile.display_name,
    avatar_url: profile.avatar_url,
    slug: profile.slug
  };
  
  // Add data based on permissions
  if (permissions.canViewFullProfile) {
    filteredProfile.bio = profile.bio;
    filteredProfile.banner_url = profile.banner_url;
    filteredProfile.preferred_genres = profile.preferred_genres;
    filteredProfile.favorite_consoles = profile.favorite_consoles;
    filteredProfile.theme = profile.theme;
    filteredProfile.level = profile.level;
    filteredProfile.experience = profile.experience;
    filteredProfile.review_count = profile.review_count;
    filteredProfile.created_at = profile.created_at;
  }
  
  if (permissions.canViewContactInfo) {
    filteredProfile.website = profile.website;
    filteredProfile.location = profile.location;
  }
  
  if (permissions.canViewOnlineStatus) {
    filteredProfile.is_online = profile.is_online;
    filteredProfile.last_seen = profile.last_seen;
  }
  
  if (permissions.canViewAchievements) {
    filteredProfile.achievements = profile.achievements;
  }
  
  if (permissions.canViewStats) {
    filteredProfile.user_stats = profile.user_stats;
  }
  
  return filteredProfile;
}

/**
 * Get privacy level description for UI
 */
export function getPrivacyLevelDescription(visibility: 'public' | 'friends' | 'private'): string {
  switch (visibility) {
    case 'public':
      return 'Seu perfil é visível para todos os usuários';
    case 'friends':
      return 'Seu perfil é visível apenas para seus amigos';
    case 'private':
      return 'Seu perfil é privado e não é visível para outros usuários';
    default:
      return 'Configuração de privacidade desconhecida';
  }
}

/**
 * Get recommended privacy settings for new users
 */
export function getRecommendedPrivacySettings(): PrivacySettings {
  return {
    profile_visibility: 'public',
    show_online_status: true,
    show_gaming_profiles: true,
    show_social_profiles: true,
    show_achievements: true,
    allow_contact: true,
    allow_friend_requests: true
  };
}

/**
 * Validate privacy settings object
 */
export function validatePrivacySettings(settings: any): settings is PrivacySettings {
  return (
    typeof settings === 'object' &&
    settings !== null &&
    ['public', 'friends', 'private'].includes(settings.profile_visibility) &&
    typeof settings.show_online_status === 'boolean' &&
    typeof settings.show_gaming_profiles === 'boolean' &&
    typeof settings.show_social_profiles === 'boolean' &&
    typeof settings.show_achievements === 'boolean' &&
    typeof settings.allow_contact === 'boolean' &&
    typeof settings.allow_friend_requests === 'boolean'
  );
}
