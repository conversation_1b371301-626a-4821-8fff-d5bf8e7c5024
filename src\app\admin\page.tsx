
'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Newspaper, BarChart3, DollarSign, Settings, Database, Shield, Gamepad2, MessageSquare, Zap, ShieldCheck } from 'lucide-react';
import Link from 'next/link';

const AdminPage = () => {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const [debugMode, setDebugMode] = useState(false);

  // Animation for initial mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // DEBUG: Test MFA API
  const testMFAAPI = async () => {
    try {
      console.log('🔍 Testing MFA API...');
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      const result = await response.json();
      console.log('📊 MFA API Result:', result);
      alert(`MFA API Result: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      console.error('❌ MFA API Error:', error);
      alert(`MFA API Error: ${error}`);
    }
  };

  return (
    <AdminLayout
      title="Admin Dashboard"
      description="Manage CriticalPixel content and users"
      breadcrumbs={[
        { label: 'Admin Dashboard' }
      ]}
    >
      {/* DEBUG SECTION - Remove after testing */}
      <div className="mb-6 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
        <h3 className="text-yellow-400 font-mono text-sm mb-2">🔍 MFA Debug Panel</h3>
        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={testMFAAPI}
            variant="outline"
            size="sm"
            className="text-yellow-400 border-yellow-500/50 hover:bg-yellow-500/10"
          >
            Test MFA API
          </Button>
          <Button
            onClick={() => setDebugMode(!debugMode)}
            variant="outline"
            size="sm"
            className="text-yellow-400 border-yellow-500/50 hover:bg-yellow-500/10"
          >
            {debugMode ? 'Hide' : 'Show'} Debug Info
          </Button>
        </div>
        {debugMode && (
          <div className="mt-2 text-xs text-yellow-300 font-mono">
            <div>Current URL: {typeof window !== 'undefined' ? window.location.href : 'SSR'}</div>
            <div>Mounted: {mounted.toString()}</div>
            <div>Debug Mode: {debugMode.toString()}</div>
          </div>
        )}
      </div>

      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 transition-opacity duration-500 ${mounted ? 'opacity-100' : 'opacity-0'}`}>
        <AdminActionCard
          id="reviews"
          title="Manage Reviews"
          description="Create, edit, or delete game reviews across the platform."
          icon={<Newspaper className="h-8 w-8" />}
          href="/admin/reviews"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
          stats={{ count: 1247, label: "Reviews" }}
        />
        <AdminActionCard
          id="users"
          title="Manage Users"
          description="View, edit, or manage user accounts and roles with permissions."
          icon={<Users className="h-8 w-8" />}
          href="/admin/users"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
          stats={{ count: 8932, label: "Users" }}
          featured={true}
        />
        <AdminActionCard
          id="mfa"
          title="Multi-Factor Auth"
          description="Configure and manage two-factor authentication security for administrators."
          icon={<ShieldCheck className="h-8 w-8" />}
          href="/admin/security/mfa"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
          featured={true}
        />
        <AdminActionCard
          id="analytics"
          title="Site Analytics"
          description="View comprehensive website analytics and performance metrics."
          icon={<BarChart3 className="h-8 w-8" />}
          href="/admin/analytics"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
          stats={{ count: 156783, label: "Views" }}
        />
        <AdminActionCard
          id="ads"
          title="Ad Management"
          description="Configure ad placements and manage affiliate partnerships."
          icon={<DollarSign className="h-8 w-8" />}
          href="/admin/ads"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
        />
        <AdminActionCard
          id="db"
          title="Database"
          description="Manage database records and optimize performance."
          icon={<Database className="h-8 w-8" />}
          href="/admin/database"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
        />
        <AdminActionCard
          id="settings"
          title="Site Settings"
          description="Configure global settings and maintenance options."
          icon={<Settings className="h-8 w-8" />}
          href="/admin/settings"
          onHover={setHoveredCard}
          hoveredCard={hoveredCard}
        />
      </div>

      {/* Animation & styling */}
      <style jsx global>{`
        @keyframes fadeSlideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .admin-card {
          animation: fadeSlideIn 0.5s ease-out both;
        }
        
        .admin-card:nth-child(1) { animation-delay: 100ms; }
        .admin-card:nth-child(2) { animation-delay: 150ms; }
        .admin-card:nth-child(3) { animation-delay: 200ms; }
        .admin-card:nth-child(4) { animation-delay: 250ms; }
        .admin-card:nth-child(5) { animation-delay: 300ms; }
        .admin-card:nth-child(6) { animation-delay: 350ms; }
        .admin-card:nth-child(7) { animation-delay: 400ms; }
        
        .ease-custom {
          transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </AdminLayout>
  );
};

interface AdminActionCardProps {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  stats?: { count: number; label: string };
  featured?: boolean;
  onHover: (id: string | null) => void;
  hoveredCard: string | null;
}

const AdminActionCard = ({ 
  id, 
  title, 
  description, 
  icon, 
  href, 
  stats,
  featured = false,
  onHover,
  hoveredCard
}: AdminActionCardProps) => {
  const isHovered = hoveredCard === id;
  
  // Format large numbers with comma separators
  const formatNumber = (n: number) =>
    n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
  // Code title styling with angle brackets
  const CodeTitle = ({ children }: { children: React.ReactNode }) => (
    <span className="font-mono inline-block relative">
      <span className="text-violet-400/60 transition-colors duration-300">&lt;</span>
      <span className="px-1 relative">
        {children}
        <span className="absolute inset-0 bg-gradient-to-r from-violet-400/0 via-violet-400/30 to-violet-400/0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse" />
      </span>
      <span className="text-violet-400/60 transition-colors duration-300">/&gt;</span>
    </span>
  );

  return (
    <Link 
      href={href}
      className="admin-card block h-full group"
      onMouseEnter={() => onHover(id)}
      onMouseLeave={() => onHover(null)}
    >
      <Card className={`h-full flex flex-col border backdrop-blur-md overflow-hidden transition-all duration-500 ease-custom hover:shadow-2xl transform ${
        featured
          ? 'border-violet-500/30 bg-gradient-to-br from-slate-900/80 to-violet-900/10 hover:border-violet-400/50 hover:shadow-violet-500/20'
          : 'border-white/5 bg-gradient-to-br from-slate-900/80 to-slate-800/30 hover:border-white/10 hover:shadow-black/20'
      } ${
        isHovered 
          ? 'scale-[1.02]' 
          : 'scale-100'
      }`}>
        {/* Card highlight effect */}
        <div 
          className="absolute inset-0 bg-gradient-to-br from-violet-600/20 via-violet-600/10 to-cyan-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-in-out"
          style={{
            backgroundSize: '200% 200%',
            animation: isHovered ? 'gradient-shift 3s ease infinite' : 'none'
          }}
        />
        
        {/* Featured indicator */}
        {featured && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-violet-400 rounded-full animate-pulse shadow-md shadow-violet-500/30"/>
        )}
        
        <CardHeader className="flex flex-row items-start space-x-4 pb-3 relative">
          <div className={`p-3 rounded-lg flex items-center justify-center transition-all duration-300 ease-out group-hover:scale-110 ${
            featured
              ? 'bg-violet-500/20 group-hover:bg-violet-500/30'
              : 'bg-slate-700/50 group-hover:bg-violet-500/20'
          }`}>
            <div className={`${
              featured
                ? 'text-violet-400'
                : 'text-slate-400 group-hover:text-violet-400'
              }
              transition-all duration-300 ease-out group-hover:rotate-6
            `}>
              {icon}
            </div>
          </div>
          <CardTitle className="text-xl text-white pt-1">
            <CodeTitle>{title}</CodeTitle>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-grow">
          <CardDescription className={`text-slate-400 transition-all duration-300 ease-out ${isHovered ? 'text-slate-300' : ''}`}>
            {description}
          </CardDescription>
          
          {/* Stats display if available */}
          {stats && (
            <div className={`mt-4 flex items-center transition-all duration-500 ${isHovered ? 'opacity-100 translate-y-0' : 'opacity-60 translate-y-1'}`}>
              <div className="text-lg font-bold text-white mr-2">{formatNumber(stats.count)}</div>
              <div className="text-xs text-slate-400">{stats.label}</div>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button 
            variant="outline" 
            className={`w-full transition-all duration-300 ease-custom ${
              featured 
                ? 'border-violet-500/40 text-violet-400 hover:bg-violet-500/20 hover:border-violet-400/60'
                : 'border-slate-700 text-slate-400 hover:bg-slate-800/70 hover:text-white hover:border-slate-600'
            } group-hover:scale-105`}
          >
            <span className="mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -translate-x-2 group-hover:translate-x-0">→</span>
            Go to {title.split(' ')[1] || title.split(' ')[0]}
          </Button>
        </CardFooter>
      </Card>
      
      {/* Card glow effect */}
      <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ${
        featured ? 'bg-violet-500/5' : 'bg-slate-500/5'
      }`} />
    </Link>
  );
};

export default AdminPage;
