import React from 'react';

const FreeTierIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M8 12h8M12 8v8" stroke="currentColor" strokeWidth="2"/>
    <rect x="6" y="6" width="1" height="1"/>
    <rect x="17" y="6" width="1" height="1"/>
    <rect x="6" y="17" width="1" height="1"/>
    <rect x="17" y="17" width="1" height="1"/>
    <circle cx="12" cy="4" r="0.5"/>
    <circle cx="12" cy="20" r="0.5"/>
    <circle cx="4" cy="12" r="0.5"/>
    <circle cx="20" cy="12" r="0.5"/>
  </svg>
);

export default FreeTierIcon;