// src/lib/review-service-server.ts
// Server-only version of review service functions
// This file should only be imported in server components/API routes

import { createServerClient } from '@/lib/supabase/server';
import type { Review } from '@/lib/types';

// Server-side version of getReviewBySlug for SSR/SSG with authentication
export async function getReviewBySlugServer(slug: string): Promise<Review | null> {
  try {
    const supabase = await createServerClient();

    // Get current user from server-side authentication
    const { data: { user } } = await supabase.auth.getUser();

    // Modified to allow owners to view their private reviews
    let query = supabase
      .from('reviews')
      .select('*')
      .eq('slug', slug)
      .eq('is_blocked', false);

    // If user is authenticated, allow them to see their own private reviews and drafts
    if (user) {
      // Allow: 1) published non-private reviews, 2) any review owned by the current user
      query = query.or(`and(status.eq.published,or(is_private.is.null,is_private.eq.false)),author_id.eq.${user.id}`);
    } else {
      // For non-authenticated users, only show published, non-private reviews
      query = query.eq('status', 'published').or('is_private.is.null,is_private.eq.false');
    }

    const { data: review, error: reviewError } = await query.single();

    if (reviewError || !review) {
      console.error('Error fetching review (server):', reviewError);
      return null;
    }

    // Manually fetch related data to avoid nested query issues
    const [profileResult, gameResult] = await Promise.all([
      supabase
        .from('profiles')
        .select('username, display_name, avatar_url, slug')
        .eq('id', review.author_id)
        .single(),
      supabase
        .from('games')
        .select('*')
        .eq('id', review.game_id)
        .single()
    ]);

    // Combine the data manually
    const combinedReview = {
      ...review,
      profiles: profileResult.data,
      games: gameResult.data
    };

    // Helper function to normalize IGDB image URLs
    function normalizeIGDBImageUrl(url: string | null | undefined): string | undefined {
      if (!url) return undefined;
      
      // Handle double protocol issue in database
      if (url.startsWith('https:https://')) {
        return url.replace('https:https://', 'https://');
      }
      
      // If URL already has protocol, return as is
      if (url.startsWith('https://') || url.startsWith('http://')) {
        return url;
      }
      
      // If URL starts with //, add https:
      if (url.startsWith('//')) {
        return `https:${url}`;
      }
      
      // Default: assume it needs https:// prefix
      return `https://${url}`;
    }

    // Convert to Review interface
    const reviewResult: Review = {
      id: combinedReview.id,
      title: combinedReview.title,
      gameName: combinedReview.games?.name || combinedReview.game_name,
      slug: combinedReview.slug,
      publishDate: combinedReview.publish_date ? new Date(combinedReview.publish_date) : undefined,
      scoringCriteria: Object.entries(combinedReview.scoring_criteria || {}).map(([id, score]) => ({
        id,
        name: id,
        score: typeof score === 'number' ? score * 10 : 0
      })),
      overallScore: typeof combinedReview.overall_score === 'number' ? combinedReview.overall_score * 10 : 0,
      platforms: combinedReview.platforms || [],
      genres: combinedReview.games?.genres || combinedReview.genres || [],
      language: combinedReview.language || 'en',
      playedOn: combinedReview.played_on,
      datePlayed: combinedReview.date_played,
      contentLexical: combinedReview.content_lexical,
      metaTitle: combinedReview.meta_title,
      metaDescription: combinedReview.meta_description,
      focusKeyword: combinedReview.focus_keyword,
      tags: combinedReview.tags || [],
      mainImageUrl: combinedReview.main_image_url,
      mainImagePosition: combinedReview.main_image_position,
      igdbCoverUrl: normalizeIGDBImageUrl(combinedReview.igdb_cover_url || combinedReview.games?.cover_url),
      galleryImageUrls: combinedReview.gallery_image_urls || [],
      videoUrl: combinedReview.video_url,
      monetizationBlocks: combinedReview.monetization_blocks || [],
      status: combinedReview.status as 'draft' | 'pending' | 'published',
      featuredHomepage: combinedReview.featured_homepage || false,
      authorId: combinedReview.author_id,
      authorName: combinedReview.profiles?.display_name || combinedReview.author_name || 'Anonymous',
      authorPhotoURL: combinedReview.profiles?.avatar_url,
      authorSlug: combinedReview.profiles?.slug,
      createdAt: new Date(combinedReview.created_at),
      igdbId: combinedReview.games?.igdb_id,
      summary: combinedReview.games?.summary,
      aggregatedRating: combinedReview.games?.aggregated_rating,
      aggregatedRatingCount: combinedReview.games?.aggregated_rating_count,
      developers: combinedReview.games?.developers,
      publishers: combinedReview.games?.publishers,
      gameEngines: combinedReview.games?.game_engines,
      playerPerspectives: combinedReview.games?.player_perspectives,
      timeToBeatNormally: combinedReview.games?.time_to_beat_normally,
      timeToBeatCompletely: combinedReview.games?.time_to_beat_completely,
      releaseDate: combinedReview.games?.release_date ? new Date(combinedReview.games.release_date) : undefined,
      officialGameLink: combinedReview.official_game_link || combinedReview.games?.official_game_link,
      discussionLink: `/reviews/view/${combinedReview.slug}#comments`,
      is_blocked: combinedReview.is_blocked || false,
      view_count: combinedReview.view_count || 0,
      likes_count: combinedReview.like_count || 0,
      enable_comments: combinedReview.enable_comments !== false,
      is_private: combinedReview.is_private || false
    };

    return reviewResult;
  } catch (error) {
    console.error('Error in getReviewBySlugServer:', error);
    return null;
  }
}