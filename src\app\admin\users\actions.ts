// src/app/admin/users/actions.ts
'use server';

// FORTRESS-LEVEL ADMIN USER ACTIONS
// Microsoft Senior Security Specialist Implementation  
// Date: 11/01/2025
// Classification: TOP SECRET
// SECURITY: Enhanced multi-layer authentication and authorization

import type { UserProfile } from '@/lib/types';
import { createServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';
import {
  verifyAdminSessionEnhanced,
  validateTargetUserModification,
  logSecurityEvent,
  requestAdminApproval,
  validateBulkOperation
} from '@/lib/admin/security';
import {
  CriticalOperation,
  AdminPermissionLevel
} from '@/lib/admin/security-utils';

// DEPRECATED: Legacy admin verification - replaced with enhanced security
// This function is maintained for backward compatibility but should not be used
async function verifyAdminSession(operation?: string) {
  console.warn('⚠️  DEPRECATED: Using legacy admin verification. Migrate to verifyAdminSessionEnhanced()');
  
  const supabase = await createServerClient();
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error('Authentication required');
  }

  // Basic admin check using profile table
  const { data: profile, error: adminError } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('id', user.id)
    .single();
    
  if (adminError || !profile?.is_admin) {
    throw new Error('Admin privileges required');
  }
  
  return { supabase, user };
}

// SECURE USER LIST RETRIEVAL WITH FORTRESS-LEVEL PROTECTION
export async function getUsersList(
  page: number = 1,
  limit: number = 50,
  filters?: {
    search?: string;
    role?: string;
    status?: 'active' | 'suspended';
    dateFrom?: string;
    dateTo?: string;
  }
): Promise<{
  users: UserProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.USER_ROLE_MODIFY);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    const sanitizedPage = Math.max(1, Math.min(page, 1000)); // Max 1000 pages
    const sanitizedLimit = Math.max(1, Math.min(limit, 100)); // Max 100 users per page
    
    // SECURITY LAYER 3: Log data access attempt
    await logSecurityEvent('ADMIN_USER_LIST_ACCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      filters: filters,
      permissionLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 4: Data minimization - only select necessary fields (safe columns only)
    const allowedFields = [
      'id',
      'username', 
      'display_name',
      'avatar_url',
      'banner_url',
      'bio',
      'is_admin',
      'admin_level',
      'is_online',
      'last_seen',
      'level',
      'experience',
      'review_count',
      'created_at',
      'updated_at',
      // BUG FIX 210126: Added suspension fields for proper status display
      'suspended',
      'suspension_reason',
      'suspended_at',
      'suspended_by'
    ];

    // SECURITY LAYER 5: Build secure query with RLS enforcement
    let query = supabase
      .from('profiles')
      .select(allowedFields.join(', '), { count: 'exact' });

    // SECURITY LAYER 6: Secure filter application with input sanitization
    if (filters?.search) {
      // Sanitize search input to prevent injection
      const sanitizedSearch = filters.search.replace(/[<>'"]/g, '');
      if (sanitizedSearch.length >= 2) {
        query = query.or(`username.ilike.%${sanitizedSearch}%,display_name.ilike.%${sanitizedSearch}%`);
      }
    }

    // Apply role filter with validation
    if (filters?.role === 'admin') {
      query = query.eq('is_admin', true);
    } else if (filters?.role === 'user') {
      query = query.eq('is_admin', false);
    } else if (filters?.role === 'moderator') {
      query = query.eq('admin_level', 'MODERATOR');
    }

    // BUG FIX 210126: Suspension status filtering now enabled with suspended column
    if (filters?.status === 'suspended') {
      query = query.eq('suspended', true);
    } else if (filters?.status === 'active') {
      query = query.or('suspended.is.null,suspended.eq.false');
    }

    // Apply date filters with validation
    if (filters?.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      if (!isNaN(fromDate.getTime())) {
        query = query.gte('created_at', fromDate.toISOString());
      }
    }
    
    if (filters?.dateTo) {
      const toDate = new Date(filters.dateTo);
      if (!isNaN(toDate.getTime())) {
        query = query.lte('created_at', toDate.toISOString());
      }
    }

    // SECURITY LAYER 7: Secure pagination with bounds checking
    const offset = (sanitizedPage - 1) * sanitizedLimit;
    query = query.range(offset, offset + sanitizedLimit - 1);

    // Order by creation date (newest first) - secure default ordering
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      await logSecurityEvent('ADMIN_USER_LIST_ERROR', adminVerification.adminId, {
        error: error.message,
        filters
      });
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    // SECURITY LAYER 8: Data sanitization and role mapping
    const users: UserProfile[] = (data || []).map((profile: any) => {
      // Determine role based on admin level hierarchy
      let role = 'User';
      if (profile.is_admin) {
        role = profile.admin_level || 'Admin';
      }

      return {
        uid: profile.id,
        email: profile.username || '',
        displayName: profile.display_name,
        photoURL: profile.avatar_url,
        username: profile.username,
        bio: profile.bio,
        isAdmin: profile.is_admin,
        isOnline: profile.is_online,
        lastSeen: profile.last_seen,
        level: profile.level,
        experience: profile.experience,
        reviewCount: profile.review_count,
        creationTime: profile.created_at,
        lastSignInTime: profile.last_seen,
        role: role,
        // BUG FIX 210126: Read actual suspension status from database
        disabled: !!profile.suspended, // Convert to boolean, suspended = true means disabled = true
        suspensionReason: profile.suspension_reason,
        suspendedAt: profile.suspended_at,
        // Add admin level for enhanced security tracking
        adminLevel: profile.admin_level
      };
    });

    const total = count || 0;
    const totalPages = Math.ceil(total / sanitizedLimit);

    // SECURITY LAYER 9: Enhanced audit logging
    await logSecurityEvent('ADMIN_USER_LIST_SUCCESS', adminVerification.adminId, {
      page: sanitizedPage,
      limit: sanitizedLimit,
      filters: filters,
      total: total,
      returned: users.length,
      permissionLevel: adminVerification.permissionLevel
    });

    return {
      users,
      total,
      page,
      limit,
      totalPages
    };

  } catch (error) {
    console.error('getUsersList error:', error);
    throw error;
  }
}

// FORTRESS-LEVEL USER ROLE MODIFICATION WITH ENHANCED SECURITY
export async function updateUserRole(
  uid: string,
  role: string,
  justification?: string
): Promise<void> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for role modification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.USER_ROLE_MODIFY);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    const sanitizedRole = role?.trim()?.toUpperCase();
    if (!sanitizedRole) {
      throw new Error('INVALID_ROLE_INPUT');
    }

    // SECURITY LAYER 3: Role validation with hierarchical constraints
    const allowedRoles = ['MODERATOR', 'USER', 'EDITOR', 'VIEWER'];
    const adminRoles = ['ADMIN', 'SUPER_ADMIN'];
    
    if (!allowedRoles.includes(sanitizedRole) && !adminRoles.includes(sanitizedRole)) {
      await logSecurityEvent('INVALID_ROLE_ASSIGNMENT_ATTEMPT', adminVerification.adminId, {
        targetUserId: uid,
        attemptedRole: sanitizedRole,
        allowedRoles
      });
      throw new Error(`Invalid role. Only ${allowedRoles.join(', ')} roles are allowed.`);
    }

    // SECURITY LAYER 4: Admin role promotion requires SUPER_ADMIN privileges
    if (adminRoles.includes(sanitizedRole)) {
      if (adminVerification.permissionLevel !== AdminPermissionLevel.SUPER_ADMIN) {
        await logSecurityEvent('UNAUTHORIZED_ADMIN_PROMOTION_ATTEMPT', adminVerification.adminId, {
          targetUserId: uid,
          attemptedRole: sanitizedRole,
          adminLevel: adminVerification.permissionLevel
        });
        throw new Error('INSUFFICIENT_PRIVILEGES_FOR_ADMIN_PROMOTION');
      }
      
      // Require approval workflow for admin promotions
      const approval = await requestAdminApproval(
        CriticalOperation.ADMIN_PROMOTE,
        adminVerification.adminId,
        uid,
        justification || 'Admin role promotion',
        { newRole: sanitizedRole }
      );

      if (approval.requiresApproval) {
        await logSecurityEvent('ADMIN_PROMOTION_APPROVAL_REQUESTED', adminVerification.adminId, {
          targetUserId: uid,
          approvalId: approval.approvalId,
          newRole: sanitizedRole
        });
        throw new Error('ADMIN_PROMOTION_REQUIRES_APPROVAL');
      }
    }

    // SECURITY LAYER 5: Anti-self-modification and hierarchical validation
    await validateTargetUserModification(
      adminVerification.adminId,
      uid,
      CriticalOperation.USER_ROLE_MODIFY,
      adminVerification.permissionLevel
    );

    // SECURITY LAYER 6: Get current user state for audit trail
    const { data: currentUser, error: fetchError } = await supabase
      .from('profiles')
      .select('username, admin_level, role, is_admin')
      .eq('id', uid)
      .single();

    if (fetchError) {
      await logSecurityEvent('TARGET_USER_FETCH_FAILED', adminVerification.adminId, {
        targetUserId: uid,
        error: fetchError.message
      });
      throw new Error('TARGET_USER_NOT_FOUND');
    }

    // SECURITY LAYER 7: Secure role update with atomic transaction
    const updateData: any = {
      admin_level: sanitizedRole,
      role: sanitizedRole,
      updated_at: new Date().toISOString(),
      last_admin_action: new Date().toISOString()
    };

    // Set is_admin flag based on role
    updateData.is_admin = adminRoles.includes(sanitizedRole);

    const { error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', uid);

    if (error) {
      await logSecurityEvent('ROLE_UPDATE_FAILED', adminVerification.adminId, {
        targetUserId: uid,
        newRole: sanitizedRole,
        error: error.message
      });
      throw new Error(`Failed to update user role: ${error.message}`);
    }

    // SECURITY LAYER 8: Enhanced audit logging with complete context
    await logSecurityEvent('USER_ROLE_MODIFIED_SUCCESS', adminVerification.adminId, {
      targetUserId: uid,
      targetUsername: currentUser.username,
      previousRole: currentUser.admin_level || currentUser.role,
      newRole: sanitizedRole,
      justification: justification,
      adminLevel: adminVerification.permissionLevel,
      wasAdminPromotion: adminRoles.includes(sanitizedRole)
    });

    // SECURITY LAYER 9: Notification and cache invalidation
    revalidatePath('/admin/users');
    revalidatePath(`/u/${currentUser.username}`);

    // Log success for monitoring
    console.log(`🔒 SECURITY: Role updated successfully - ${currentUser.username}: ${currentUser.admin_level} → ${sanitizedRole} by ${adminVerification.adminId}`);

  } catch (error: any) {
    await logSecurityEvent('USER_ROLE_MODIFICATION_ERROR', '', {
      targetUserId: uid,
      attemptedRole: role,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: updateUserRole failed:', error);
    throw error;
  }
}

// FORTRESS-LEVEL USER SUSPENSION MANAGEMENT WITH ENHANCED SECURITY
export async function updateUserStatus(
  uid: string,
  suspended: boolean,
  reason?: string
): Promise<void> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for suspension operations
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.USER_SUSPEND);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Input validation and sanitization
    if (typeof suspended !== 'boolean') {
      throw new Error('INVALID_SUSPENSION_STATUS');
    }

    const sanitizedReason = reason?.trim();
    if (suspended && (!sanitizedReason || sanitizedReason.length < 5)) {
      throw new Error('SUSPENSION_REASON_REQUIRED');
    }

    // SECURITY LAYER 3: Anti-self-modification and hierarchical validation
    await validateTargetUserModification(
      adminVerification.adminId,
      uid,
      CriticalOperation.USER_SUSPEND,
      adminVerification.permissionLevel
    );

    // SECURITY LAYER 4: Get current user state for audit trail
    const { data: targetUser, error: fetchError } = await supabase
      .from('profiles')
      .select('username, is_admin, admin_level')
      .eq('id', uid)
      .single();

    if (fetchError) {
      await logSecurityEvent('SUSPENSION_TARGET_USER_FETCH_FAILED', adminVerification.adminId, {
        targetUserId: uid,
        error: fetchError.message
      });
      throw new Error('TARGET_USER_NOT_FOUND');
    }

    // SECURITY LAYER 5: Log suspension attempt before execution
    await logSecurityEvent('USER_SUSPENSION_ATTEMPT', adminVerification.adminId, {
      targetUserId: uid,
      targetUsername: targetUser.username,
      currentStatus: false, // Will be updated by database function
      newStatus: suspended,
      reason: sanitizedReason,
      adminLevel: adminVerification.permissionLevel,
      targetIsAdmin: targetUser.is_admin,
      targetAdminLevel: targetUser.admin_level
    });

    // SECURITY LAYER 6: Execute suspension update using database function
    const { data, error } = await supabase.rpc('admin_toggle_user_suspension', {
      p_target_user_id: uid,
      p_suspended: suspended,
      p_reason: sanitizedReason || (suspended ? 'Administrative action' : null)
    });

    if (error) {
      await logSecurityEvent('USER_SUSPENSION_FAILED', adminVerification.adminId, {
        targetUserId: uid,
        targetUsername: targetUser.username,
        error: error.message,
        suspended: suspended,
        reason: sanitizedReason
      });
      throw new Error(`Failed to update user status: ${error.message}`);
    }

    // SECURITY LAYER 7: Enhanced audit logging for suspension action
    await logSecurityEvent('USER_SUSPENSION_SUCCESS', adminVerification.adminId, {
      targetUserId: uid,
      targetUsername: targetUser.username,
      previousStatus: false, // Will be updated by database function
      newStatus: suspended,
      reason: sanitizedReason,
      adminLevel: adminVerification.permissionLevel,
      targetWasAdmin: targetUser.is_admin,
      actionType: suspended ? 'SUSPEND' : 'UNSUSPEND'
    });

    // SECURITY LAYER 8: Revalidate affected paths and cache
    revalidatePath('/admin/users');
    revalidatePath(`/u/${targetUser.username}`);
    revalidatePath('/dashboard'); // In case suspended user was viewing dashboard

    // Log success for monitoring
    const actionType = suspended ? 'suspended' : 'unsuspended';
    console.log(`🔒 SECURITY: User ${actionType} successfully - ${targetUser.username} by ${adminVerification.adminId}`);

  } catch (error: any) {
    await logSecurityEvent('USER_SUSPENSION_ERROR', '', {
      targetUserId: uid,
      suspended: suspended,
      reason: reason,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: updateUserStatus failed:', error);
    throw error;
  }
}

// SECURITY NOTICE: User deletion functionality removed for security
// Use user suspension instead of deletion to maintain data integrity and audit trails

// SECURE ADMIN PROFILE UPDATE WITH ENHANCED VALIDATION
export async function updateUserProfileAdmin(
  uid: string,
  data: { displayName?: string; bio?: string; avatarUrl?: string; bannerUrl?: string; theme?: string },
  justification?: string
): Promise<void> {
  try {
    // SECURITY LAYER 1: Enhanced admin verification for profile modification
    const adminVerification = await verifyAdminSessionEnhanced(CriticalOperation.BULK_USER_UPDATE);
    
    if (!adminVerification.isValid) {
      throw new Error('ADMIN_VERIFICATION_FAILED');
    }

    const supabase = await createServerClient();

    // SECURITY LAYER 2: Anti-self-modification validation
    await validateTargetUserModification(
      adminVerification.adminId,
      uid,
      CriticalOperation.BULK_USER_UPDATE,
      adminVerification.permissionLevel
    );

    // SECURITY LAYER 3: Input validation and sanitization
    const sanitizedData: any = {};
    
    if (data.displayName !== undefined) {
      const sanitized = data.displayName.trim().substring(0, 50);
      if (sanitized.length >= 2) sanitizedData.display_name = sanitized;
    }
    
    if (data.bio !== undefined) {
      const sanitized = data.bio.trim().substring(0, 500);
      sanitizedData.bio = sanitized;
    }
    
    if (data.avatarUrl !== undefined) {
      // Validate URL format
      try {
        new URL(data.avatarUrl);
        sanitizedData.avatar_url = data.avatarUrl;
      } catch {
        throw new Error('INVALID_AVATAR_URL');
      }
    }
    
    if (data.bannerUrl !== undefined) {
      // Validate URL format
      try {
        new URL(data.bannerUrl);
        sanitizedData.banner_url = data.bannerUrl;
      } catch {
        throw new Error('INVALID_BANNER_URL');
      }
    }
    
    if (data.theme !== undefined) {
      const allowedThemes = ['light', 'dark', 'auto', 'gaming', 'neon'];
      if (allowedThemes.includes(data.theme)) {
        sanitizedData.theme = data.theme;
      }
    }

    if (Object.keys(sanitizedData).length === 0) {
      throw new Error('NO_VALID_UPDATE_DATA');
    }

    sanitizedData.updated_at = new Date().toISOString();
    sanitizedData.last_admin_action = new Date().toISOString();

    // SECURITY LAYER 4: Get current user state for audit trail
    const { data: targetUser, error: fetchError } = await supabase
      .from('profiles')
      .select('username, display_name, bio, avatar_url, banner_url, theme')
      .eq('id', uid)
      .single();

    if (fetchError) {
      throw new Error('TARGET_USER_NOT_FOUND');
    }

    // SECURITY LAYER 5: Execute secure profile update
    const { error } = await supabase
      .from('profiles')
      .update(sanitizedData)
      .eq('id', uid);

    if (error) {
      await logSecurityEvent('ADMIN_PROFILE_UPDATE_FAILED', adminVerification.adminId, {
        targetUserId: uid,
        error: error.message,
        updateData: sanitizedData
      });
      throw new Error(`Failed to update user profile: ${error.message}`);
    }

    // SECURITY LAYER 6: Enhanced audit logging
    await logSecurityEvent('ADMIN_PROFILE_UPDATE_SUCCESS', adminVerification.adminId, {
      targetUserId: uid,
      targetUsername: targetUser.username,
      updatedFields: Object.keys(sanitizedData),
      justification: justification,
      adminLevel: adminVerification.permissionLevel
    });

    // SECURITY LAYER 7: Cache invalidation
    revalidatePath('/admin/users');
    revalidatePath(`/u/${targetUser.username}`);

    console.log(`🔒 SECURITY: Profile updated successfully - ${targetUser.username} by ${adminVerification.adminId}`);

  } catch (error: any) {
    await logSecurityEvent('ADMIN_PROFILE_UPDATE_ERROR', '', {
      targetUserId: uid,
      updateData: data,
      error: error.message
    });
    console.error('🚨 SECURITY ERROR: updateUserProfileAdmin failed:', error);
    throw error;
  }
}
