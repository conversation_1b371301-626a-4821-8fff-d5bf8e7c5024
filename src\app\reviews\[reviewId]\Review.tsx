'use client';

import React from 'react';
import Link from 'next/link';
import { ExtendedUserProfile } from '@/lib/types';

interface ReviewProps {
  user: ExtendedUserProfile;
}

export default function Review({ user }: ReviewProps) {
  return (
    <Link href={`/u/${user.rawDisplayName || user.slug}`}>
      {/* Your link content here */}
      <span>{user.displayName || user.userName}</span>
    </Link>
  );
}