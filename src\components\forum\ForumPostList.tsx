'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MessageCircle, Clock, User, Pin, TrendingUp, Plus, Minus, Flame, Zap, Crown, Skull, Flag, Star, Trophy, Target, Award, Shield, Hexagon, ChevronLeft, ChevronRight, MoreHorizontal, Ban } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { ForumPost } from '@/types/forum';
import { useForumMutations } from '@/hooks/useForumMutations';
import { ForumReportButton } from './ForumReportButton';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';

interface ForumPostListProps {
  posts: ForumPost[];
  isLoading: boolean;
  onJoinDiscussion: (postId: string) => void;
  currentUserId?: string;
  reviewAuthorId: string;
  highlightedCommentId?: string | null;
  hasActiveFilters?: boolean;
}

// Component to show banned tag under username
function BannedUserTag({ userId, reviewAuthorId }: { userId: string; reviewAuthorId: string }) {
  const { data: isBanned } = useQuery({
    queryKey: ['user-banned', userId, reviewAuthorId],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('user_blocks')
        .select('id')
        .eq('blocker_id', reviewAuthorId)
        .eq('blocked_id', userId)
        .maybeSingle();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking user ban status:', error);
        return false;
      }
      
      return !!data;
    },
    enabled: !!userId && !!reviewAuthorId && userId !== reviewAuthorId,
  });

  if (!isBanned) return null;

  return (
    <Badge variant="destructive" className="bg-red-900/30 text-red-300 border-red-800/40 text-xs px-1.5 py-0.5 ml-2">
      <Ban className="w-2 h-2 mr-1" />
      Banned
    </Badge>
  );
}

export function ForumPostList({
  posts,
  isLoading,
  onJoinDiscussion,
  currentUserId,
  reviewAuthorId,
  highlightedCommentId,
  hasActiveFilters = false
}: ForumPostListProps) {
  const { voteOnPost } = useForumMutations(posts[0]?.review_id || '');
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 8; // Gaming-appropriate number for quick loading
  
  // Calculate pagination
  const totalPages = Math.ceil(posts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = posts.slice(startIndex, endIndex);

  // Auto-navigate to correct page when highlighting a comment
  React.useEffect(() => {
    if (highlightedCommentId && posts.length > 0) {
      // Find the index of the highlighted post
      const postIndex = posts.findIndex(post => post.id === highlightedCommentId);
      
      if (postIndex !== -1) {
        // Calculate which page contains this post
        const targetPage = Math.floor(postIndex / postsPerPage) + 1;
        
        if (targetPage !== currentPage) {
          setCurrentPage(targetPage);
        }
      }
    }
  }, [highlightedCommentId, posts, postsPerPage, currentPage]);

  const handleVote = async (postId: string, currentVote: string | null, newVote: 'upvote' | 'downvote') => {
    const voteType = currentVote === newVote ? null : newVote;
    await voteOnPost.mutateAsync({ postId, voteType });
  };

  // Percentile-Based Gaming Rank Badge System
  const getPopularityBadge = (score: number, posts: ForumPost[]) => {
    // Get all scores and sort them descending
    const allScores = posts.map(p => p.upvotes - p.downvotes).sort((a, b) => b - a);
    const totalPosts = allScores.length;
    
    if (totalPosts === 0) {
      return {
        icon: Hexagon,
        rank: "-",
        textColor: "text-slate-500",
        bgColor: "bg-slate-600/15",
        borderColor: "border-slate-600/40"
      };
    }

    // Calculate percentile thresholds
    const getPercentileThreshold = (percentile: number) => {
      const index = Math.floor((percentile / 100) * totalPosts);
      return allScores[Math.min(index, totalPosts - 1)] || 0;
    };

    const thresholds = {
      SSS: getPercentileThreshold(0.1),   // Top 0.1%
      SS: getPercentileThreshold(1),      // Top 1% 
      S: getPercentileThreshold(5),       // Top 5%
      A: getPercentileThreshold(15),      // Top 15%
      B: getPercentileThreshold(35),      // Top 35%
      C: getPercentileThreshold(65),      // Top 65%
    };

    // Handle edge cases for small communities
    if (totalPosts < 10) {
      // For very small communities, use simpler thresholds
      if (score >= 10) {
        return {
          icon: Star,
          rank: "SSS",
          textColor: "text-pink-300",
          bgColor: "bg-gradient-to-r from-pink-500/20 to-purple-500/20",
          borderColor: "border-pink-400/60"
        };
      } else if (score >= 5) {
        return {
          icon: Crown,
          rank: "S",
          textColor: "text-yellow-400",
          bgColor: "bg-yellow-500/15",
          borderColor: "border-yellow-500/40"
        };
      }
    }

    // Apply percentile-based ranking
    if (score >= thresholds.SSS && score >= 15) { // Minimum threshold for SSS
      return {
        icon: Star,
        rank: "SSS",
        textColor: "text-pink-300",
        bgColor: "bg-gradient-to-r from-pink-500/20 to-purple-500/20",
        borderColor: "border-pink-400/60"
      };
    } else if (score >= thresholds.SS && score >= 10) { // Minimum threshold for SS
      return {
        icon: Crown,
        rank: "SS",
        textColor: "text-amber-300",
        bgColor: "bg-gradient-to-r from-amber-500/20 to-yellow-500/20",
        borderColor: "border-amber-400/60"
      };
    } else if (score >= thresholds.S && score >= 5) { // Minimum threshold for S
      return {
        icon: Trophy,
        rank: "S",
        textColor: "text-yellow-400",
        bgColor: "bg-yellow-500/15",
        borderColor: "border-yellow-500/40"
      };
    } else if (score >= thresholds.A && score >= 3) {
      return {
        icon: Award,
        rank: "A",
        textColor: "text-purple-400",
        bgColor: "bg-purple-500/15",
        borderColor: "border-purple-500/40"
      };
    } else if (score >= thresholds.B && score >= 2) {
      return {
        icon: Shield,
        rank: "B",
        textColor: "text-blue-400",
        bgColor: "bg-blue-500/15",
        borderColor: "border-blue-500/40"
      };
    } else if (score >= thresholds.C && score >= 1) {
      return {
        icon: Hexagon,
        rank: "C",
        textColor: "text-emerald-400",
        bgColor: "bg-emerald-500/15",
        borderColor: "border-emerald-500/40"
      };
    } else if (score >= 1) {
      return {
        icon: Hexagon,
        rank: "D",
        textColor: "text-slate-400",
        bgColor: "bg-slate-500/15",
        borderColor: "border-slate-500/40"
      };
    } else if (score <= -5) {
      return {
        icon: Target,
        rank: "?",
        textColor: "text-red-400",
        bgColor: "bg-red-500/15",
        borderColor: "border-red-500/40"
      };
    } else if (score < 0) {
      return {
        icon: Hexagon,
        rank: "F",
        textColor: "text-orange-400",
        bgColor: "bg-orange-500/15",
        borderColor: "border-orange-500/40"
      };
    } else {
      return {
        icon: Hexagon,
        rank: "-",
        textColor: "text-slate-500",
        bgColor: "bg-slate-600/15",
        borderColor: "border-slate-600/40"
      };
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="flex gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!posts.length) {
    return (
      <div className="relative">
        <div className="bg-slate-800/30 border border-slate-700/40 rounded-lg p-8 text-center">
          <div className="space-y-3">
            <div className="text-slate-400 font-mono text-sm">
              <span className="text-slate-500">//</span> {hasActiveFilters ? 'No posts found' : 'Talk about it!'}
            </div>
            <p className="text-slate-400 text-sm max-w-md mx-auto leading-relaxed">
              {hasActiveFilters
                ? 'Try adjusting your search or filters to find more discussions'
                : 'Start a discussion, ask questions, or share your own experience'
              }
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {currentPosts.map((post, index) => {
        const isAuthorPost = post.author_id === reviewAuthorId;
        const score = post.upvotes - post.downvotes;
        const badge = getPopularityBadge(score, posts);
        const BadgeIcon = badge.icon;
        const isHighlighted = highlightedCommentId === post.id;
        
        return (
          <motion.div
            key={post.id}
            id={`comment-${post.id}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05, ease: [0.4, 0, 0.2, 1] }}
          >
            <Card className={cn(
              "transition-all duration-200 cursor-pointer",
              isHighlighted 
                ? "bg-red-950/30 border-red-800/40 hover:bg-red-950/40 hover:border-red-700/50" 
                : "bg-slate-800/50 border-slate-700/50 hover:bg-slate-800/70 hover:border-slate-600/50"
            )}>
              <CardContent className="p-3">
                {/* Desktop Layout */}
                <div className="hidden sm:flex gap-3 items-center">
                  {/* Avatar */}
                  <img
                    src={post.author?.avatar_url || '/imgs/profile.svg'}
                    alt={post.author?.display_name || 'User'}
                    className="h-12 w-12 rounded-lg object-cover flex-shrink-0"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/imgs/profile.svg';
                    }}
                  />

                  {/* Content */}
                  <div 
                    className="flex-1 min-w-0"
                    onClick={() => onJoinDiscussion(post.id)}
                  >
                    {/* Title Section */}
                    <div className="flex items-center gap-2 mb-1 flex-wrap">
                      <h3 className="font-semibold text-slate-200 hover:text-purple-400 transition-colors truncate text-sm">
                        {post.title}
                      </h3>
                      
                      {post.is_hot && (
                        <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30 text-xs px-1.5 py-0.5 h-5">
                          <TrendingUp className="h-2.5 w-2.5 mr-0.5" />
                          Hot
                        </Badge>
                      )}
                      
                      {post.is_pinned && (
                        <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs px-1.5 py-0.5 h-5">
                          <Pin className="h-2.5 w-2.5 mr-0.5" />
                          Pinned
                        </Badge>
                      )}
                      
                      {isAuthorPost && (
                        <Badge variant="secondary" className="bg-slate-800 text-slate-200 border-slate-600 text-xs font-bold px-1.5 py-0.5 h-5 w-8 flex items-center justify-center rounded-md">
                          OP
                        </Badge>
                      )}
                    </div>

                    {/* Metadata Section */}
                    <div className="flex items-center gap-3 text-xs text-slate-500">
                      <div className="flex items-center gap-1">
                        <User className="w-2.5 h-2.5" />
                        <span className="truncate max-w-20">{post.author?.display_name || 'Anonymous'}</span>
                        <BannedUserTag userId={post.author_id} reviewAuthorId={reviewAuthorId} />
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-2.5 h-2.5" />
                        <span>{formatDistanceToNow(new Date(post.created_at))} ago</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-2.5 h-2.5" />
                        <span className="font-mono">{post.reply_count}</span>
                      </div>
                    </div>
                  </div>

                  {/* Right side actions */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {/* Clean Gaming Rank Badge */}
                    <div className="relative group/badge flex items-center justify-center w-8 h-8">
                      <div className={cn(
                        "relative flex flex-col items-center justify-center w-8 h-8 rounded-md border transition-colors duration-200 cursor-help",
                        badge.bgColor,
                        badge.borderColor
                      )}>
                        <BadgeIcon className={cn(
                          "w-2.5 h-2.5 mb-0.5",
                          badge.textColor
                        )} />
                        <span className={cn(
                          "text-xs font-bold font-mono leading-none",
                          badge.textColor
                        )}>
                          {badge.rank}
                        </span>
                        
                        {/* Simple tooltip - shows score only */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover/badge:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                          <div className="bg-slate-800 border border-slate-600 rounded px-2 py-1">
                            <p className="font-mono text-xs text-white whitespace-nowrap">
                              {score > 0 ? `+${score}` : score}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Report Button */}
                    <div onClick={(e) => e.stopPropagation()}>
                      <ForumReportButton
                        postId={post.id}
                        postTitle={post.title}
                        size="sm"
                      />
                    </div>
                  </div>
                </div>

                {/* Mobile Layout */}
                <div className="sm:hidden" onClick={() => onJoinDiscussion(post.id)}>
                  {/* Top Row - Avatar, Title, Score */}
                  <div className="flex gap-3 items-start mb-2">
                    <img
                      src={post.author?.avatar_url || '/imgs/profile.svg'}
                      alt={post.author?.display_name || 'User'}
                      className="h-10 w-10 rounded-lg object-cover flex-shrink-0"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/imgs/profile.svg';
                      }}
                    />
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-slate-200 text-sm leading-tight mb-1">
                        {post.title}
                      </h3>
                      
                      {/* Badge Row */}
                      <div className="flex items-center gap-1.5 flex-wrap">
                        {post.is_hot && (
                          <Badge variant="secondary" className="bg-red-500/20 text-red-400 border-red-500/30 text-xs px-1.5 py-0.5 h-4">
                            <TrendingUp className="h-2 w-2 mr-0.5" />
                            Hot
                          </Badge>
                        )}
                        
                        {post.is_pinned && (
                          <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs px-1.5 py-0.5 h-4">
                            <Pin className="h-2 w-2 mr-0.5" />
                            Pinned
                          </Badge>
                        )}
                        
                        {isAuthorPost && (
                          <Badge variant="secondary" className="bg-slate-800 text-slate-200 border-slate-600 text-xs font-bold px-1.5 py-0.5 h-4 w-7 flex items-center justify-center rounded-md">
                            OP
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Mobile Score & Actions */}
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <div className={cn(
                        "relative flex flex-col items-center justify-center w-7 h-7 rounded-md border transition-colors duration-200",
                        badge.bgColor,
                        badge.borderColor
                      )}>
                        <BadgeIcon className={cn(
                          "w-2 h-2 mb-0.5",
                          badge.textColor
                        )} />
                        <span className={cn(
                          "text-xs font-bold font-mono leading-none",
                          badge.textColor
                        )}>
                          {badge.rank}
                        </span>
                      </div>
                      
                      <div onClick={(e) => e.stopPropagation()}>
                        <ForumReportButton
                          postId={post.id}
                          postTitle={post.title}
                          size="xs"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Bottom Row - Metadata */}
                  <div className="flex items-center justify-between text-xs text-slate-500 pl-13">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <User className="w-2.5 h-2.5" />
                        <span className="truncate max-w-24">{post.author?.display_name || 'Anonymous'}</span>
                        <BannedUserTag userId={post.author_id} reviewAuthorId={reviewAuthorId} />
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-2.5 h-2.5" />
                        <span className="font-mono">{post.reply_count}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 text-xs text-slate-500">
                      <Clock className="w-2.5 h-2.5" />
                      <span>{formatDistanceToNow(new Date(post.created_at))} ago</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        );
        })}
      </div>

      {/* Gaming-Style Pagination */}
      {totalPages > 1 && (
        <div className="pt-4 border-t border-slate-700/40">
          {/* Mobile Pagination Info */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center gap-2 text-xs font-mono text-slate-400 justify-center sm:justify-start">
              <span>Page {currentPage} of {totalPages}</span>
              <span className="text-slate-600 hidden sm:inline">•</span>
              <span className="hidden sm:inline">{posts.length} posts total</span>
            </div>
          
            <div className="flex items-center gap-1 justify-center sm:justify-start">
            {/* Previous Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="h-8 w-8 p-0 text-slate-400 hover:text-white hover:bg-slate-700/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page Numbers */}
            {(() => {
              const pages = [];
              const showEllipsis = totalPages > 7;
              
              if (!showEllipsis) {
                // Show all pages if 7 or fewer
                for (let i = 1; i <= totalPages; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={currentPage === i ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(i)}
                      className={cn(
                        "h-8 w-8 p-0 text-xs font-mono transition-all duration-200",
                        currentPage === i
                          ? "bg-purple-600 text-white hover:bg-purple-700"
                          : "text-slate-400 hover:text-white hover:bg-slate-700/50"
                      )}
                    >
                      {i}
                    </Button>
                  );
                }
              } else {
                // Show condensed pagination with ellipsis
                pages.push(
                  <Button
                    key={1}
                    variant={currentPage === 1 ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setCurrentPage(1)}
                    className={cn(
                      "h-8 w-8 p-0 text-xs font-mono transition-all duration-200",
                      currentPage === 1
                        ? "bg-purple-600 text-white hover:bg-purple-700"
                        : "text-slate-400 hover:text-white hover:bg-slate-700/50"
                    )}
                  >
                    1
                  </Button>
                );

                if (currentPage > 3) {
                  pages.push(
                    <div key="ellipsis1" className="flex items-center justify-center h-8 w-8">
                      <MoreHorizontal className="h-3 w-3 text-slate-600" />
                    </div>
                  );
                }

                // Show current page and neighbors
                const start = Math.max(2, currentPage - 1);
                const end = Math.min(totalPages - 1, currentPage + 1);
                
                for (let i = start; i <= end; i++) {
                  if (i !== 1 && i !== totalPages) {
                    pages.push(
                      <Button
                        key={i}
                        variant={currentPage === i ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setCurrentPage(i)}
                        className={cn(
                          "h-8 w-8 p-0 text-xs font-mono transition-all duration-200",
                          currentPage === i
                            ? "bg-purple-600 text-white hover:bg-purple-700"
                            : "text-slate-400 hover:text-white hover:bg-slate-700/50"
                        )}
                      >
                        {i}
                      </Button>
                    );
                  }
                }

                if (currentPage < totalPages - 2) {
                  pages.push(
                    <div key="ellipsis2" className="flex items-center justify-center h-8 w-8">
                      <MoreHorizontal className="h-3 w-3 text-slate-600" />
                    </div>
                  );
                }

                if (totalPages > 1) {
                  pages.push(
                    <Button
                      key={totalPages}
                      variant={currentPage === totalPages ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      className={cn(
                        "h-8 w-8 p-0 text-xs font-mono transition-all duration-200",
                        currentPage === totalPages
                          ? "bg-purple-600 text-white hover:bg-purple-700"
                          : "text-slate-400 hover:text-white hover:bg-slate-700/50"
                      )}
                    >
                      {totalPages}
                    </Button>
                  );
                }
              }
              
              return pages;
            })()}

            {/* Next Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="h-8 w-8 p-0 text-slate-400 hover:text-white hover:bg-slate-700/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
