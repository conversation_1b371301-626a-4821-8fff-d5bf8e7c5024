/* src/components/style/gamerCard.css */

:root {
    /* Code/Gamer Card Theme Variables */
    --card-bg-overlay: rgba(16, 18, 27, 0.75);
    --card-bg-primary: rgba(30, 33, 45, 0.85);
    --card-bg-secondary: rgba(24, 27, 36, 0.80);
    --card-bg-tertiary: rgba(20, 22, 32, 0.70);
    
    /* Accent colors - matching auth modal */
    --card-accent-primary: #8b5cf6;
    --card-accent-secondary: #a855f7;
    --card-accent-tertiary: #c084fc;
    --card-accent-muted: rgba(139, 92, 246, 0.4);
    
    /* Text hierarchy */
    --card-text-primary: #f1f5f9;
    --card-text-secondary: #94a3b8;
    --card-text-muted: #64748b;
    --card-text-accent: #8b5cf6;
    
    /* Border system */
    --card-border-primary: rgba(71, 85, 105, 0.3);
    --card-border-secondary: rgba(71, 85, 105, 0.2);
    --card-border-focus: rgba(139, 92, 246, 0.5);
    --card-border-glow: rgba(139, 92, 246, 0.8);
    
    /* Fonts */
    --card-font-mono: 'Fira Code', 'SF Mono', 'Monaco', 'Courier New', monospace;
    --card-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  /* Main Card Container */
  .gamer-card-container {
    position: relative;
    width: 100%;
    border-radius: 16px;
    border: 1px solid var(--card-border-primary);
    background: var(--card-bg-primary);
    backdrop-filter: blur(12px);
    box-shadow: 
      0 20px 60px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(139, 92, 246, 0.05) inset;
    overflow: hidden;
    transition: all 0.3s ease;
  }
  
  .gamer-card-container:hover {
    border-color: var(--card-border-focus);
    box-shadow: 
      0 25px 80px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(139, 92, 246, 0.1) inset,
      0 0 20px rgba(139, 92, 246, 0.2);
  }
  
  /* Background pattern overlay */
  .gamer-card-pattern {
    position: absolute;
    inset: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Crect width='2' height='2' x='2' y='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    opacity: 0.6;
  }
  
  /* Header Section */
  .gamer-card-header {
    position: relative;
    padding: 20px 24px;
    border-bottom: 1px solid var(--card-border-secondary);
    background: var(--card-bg-tertiary);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .gamer-card-header:hover {
    background: rgba(20, 22, 32, 0.9);
    border-bottom-color: var(--card-border-primary);
  }
  
  .gamer-card-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .gamer-card-title-section {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .gamer-card-expand-icon {
    padding: 8px;
    border-radius: 8px;
    border: 1px solid var(--card-border-secondary);
    background: rgba(30, 32, 44, 0.8);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-expand-icon:hover {
    border-color: var(--card-accent-muted);
    background: rgba(139, 92, 246, 0.1);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
  }
  
  .gamer-card-expand-icon.expanded {
    transform: rotate(180deg);
    border-color: var(--card-accent-primary);
    background: rgba(139, 92, 246, 0.15);
  }
  
  /* Code Title Styling */
  .gamer-card-code-title {
    font-family: var(--card-font-mono);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--card-text-primary);
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .gamer-card-code-bracket {
    color: var(--card-accent-primary);
    opacity: 0.7;
    font-weight: 500;
  }
  
  .gamer-card-terminal-icon {
    color: var(--card-accent-primary);
    animation: pulse-glow 2s infinite;
  }
  
  @keyframes pulse-glow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
  
  /* Edit Button */
  .gamer-card-edit-button {
    padding: 8px;
    border-radius: 8px;
    border: 1px solid var(--card-border-secondary);
    background: rgba(30, 32, 44, 0.8);
    color: var(--card-accent-primary);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-edit-button:hover {
    border-color: var(--card-accent-muted);
    background: rgba(139, 92, 246, 0.15);
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
    transform: scale(1.05);
  }
  
  /* Content Area */
  .gamer-card-content {
    padding: 24px;
    background: var(--card-bg-secondary);
    backdrop-filter: blur(8px);
  }
  
  /* Section Headers */
  .gamer-card-section {
    margin-bottom: 32px;
  }
  
  .gamer-card-section:last-child {
    margin-bottom: 0;
  }
  
  .gamer-card-section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-family: var(--card-font-mono);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--card-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }
  
  .gamer-card-section-icon {
    color: var(--card-accent-primary);
    transition: all 0.2s ease;
  }
  
  .gamer-card-section-header:hover .gamer-card-section-icon {
    color: var(--card-accent-secondary);
    transform: scale(1.1);
  }
  
  .gamer-card-section-title {
    position: relative;
  }
  
  .gamer-card-section-title::before {
    content: '<';
    color: var(--card-accent-primary);
    opacity: 0.6;
    margin-right: 4px;
  }
  
  .gamer-card-section-title::after {
    content: '\002F\003E';
    color: var(--card-accent-primary);
    opacity: 0.6;
    margin-left: 4px;
  }
  
  /* About Me Section */
  .gamer-card-bio {
    padding: 16px 20px;
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid var(--card-border-secondary);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-bio:hover {
    border-color: var(--card-border-primary);
    background: rgba(30, 32, 44, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .gamer-card-bio-line {
    font-family: var(--card-font-mono);
    font-size: 0.85rem;
    color: var(--card-text-primary);
    line-height: 1.6;
    margin-bottom: 8px;
    transition: all 0.2s ease;
  }
  
  .gamer-card-bio-line:hover {
    color: var(--card-accent-primary);
    transform: translateX(4px);
  }
  
  .gamer-card-bio-comment {
    color: var(--card-accent-primary);
    opacity: 0.8;
    margin-right: 8px;
  }
  
  .gamer-card-bio-empty {
    font-family: var(--card-font-mono);
    font-size: 0.85rem;
    color: var(--card-text-muted);
    font-style: italic;
  }
  
  /* Profile Cards */
  .gamer-card-profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
  }
  
  .gamer-card-profile-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid var(--card-border-secondary);
    border-radius: 10px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
    cursor: pointer;
    text-decoration: none;
  }
  
  .gamer-card-profile-item:hover {
    border-color: var(--card-border-focus);
    background: rgba(30, 32, 44, 0.8);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
    transform: translateY(-2px);
  }
  
  .gamer-card-profile-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(30, 32, 44, 0.8);
    border: 1px solid var(--card-border-secondary);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-profile-item:hover .gamer-card-profile-icon {
    border-color: var(--card-accent-primary);
    background: rgba(139, 92, 246, 0.1);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
  }
  
  .gamer-card-profile-info {
    flex: 1;
    min-width: 0;
  }
  
  .gamer-card-profile-username {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--card-text-primary);
    margin-bottom: 2px;
    transition: color 0.2s ease;
  }
  
  .gamer-card-profile-item:hover .gamer-card-profile-username {
    color: var(--card-accent-primary);
  }
  
  .gamer-card-profile-platform {
    font-family: var(--card-font-mono);
    font-size: 0.75rem;
    color: var(--card-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .gamer-card-profile-external {
    color: var(--card-accent-primary);
    opacity: 0;
    transition: all 0.2s ease;
  }
  
  .gamer-card-profile-item:hover .gamer-card-profile-external {
    opacity: 1;
    transform: scale(1.1);
  }
  
  /* Gaming DNA Section */
  .gamer-card-dna {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
  
  .gamer-card-dna-section {
    padding: 16px;
    background: rgba(30, 32, 44, 0.6);
    border: 1px solid var(--card-border-secondary);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-dna-section:hover {
    border-color: var(--card-border-primary);
    background: rgba(30, 32, 44, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .gamer-card-dna-header {
    font-family: var(--card-font-mono);
    font-size: 0.8rem;
    color: var(--card-text-secondary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .gamer-card-dna-icon {
    color: var(--card-accent-secondary);
    transition: all 0.2s ease;
  }
  
  .gamer-card-dna-header:hover .gamer-card-dna-icon {
    color: var(--card-accent-primary);
    transform: scale(1.1);
  }
  
  /* Genre Tags */
  .gamer-card-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .gamer-card-genre-tag {
    padding: 6px 12px;
    background: rgba(20, 22, 32, 0.8);
    border: 1px solid var(--card-border-secondary);
    border-radius: 8px;
    backdrop-filter: blur(4px);
    font-family: var(--card-font-mono);
    font-size: 0.75rem;
    color: var(--card-text-primary);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
  }
  
  .gamer-card-genre-tag::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--card-accent-primary), var(--card-accent-secondary));
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  .gamer-card-genre-tag:hover {
    border-color: var(--card-accent-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
  }
  
  .gamer-card-genre-tag:hover::before {
    opacity: 0.8;
  }
  
  .gamer-card-genre-tag span {
    position: relative;
    z-index: 1;
  }
  
  /* Console Icons */
  .gamer-card-consoles {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .gamer-card-console-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(20, 22, 32, 0.8);
    border: 1px solid var(--card-border-secondary);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    cursor: pointer;
  }
  
  .gamer-card-console-icon:hover {
    border-color: var(--card-accent-secondary);
    background: rgba(168, 85, 247, 0.1);
    box-shadow: 0 0 15px rgba(168, 85, 247, 0.3);
    transform: translateY(-2px) scale(1.05);
  }
  
  /* Empty States */
  .gamer-card-empty-state {
    text-align: center;
    padding: 24px;
    background: rgba(30, 32, 44, 0.4);
    border: 2px dashed var(--card-border-secondary);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    transition: all 0.2s ease;
  }
  
  .gamer-card-empty-state:hover {
    border-color: var(--card-border-primary);
    background: rgba(30, 32, 44, 0.6);
  }
  
  .gamer-card-empty-text {
    font-family: var(--card-font-mono);
    font-size: 0.85rem;
    color: var(--card-text-muted);
    margin-bottom: 12px;
  }
  
  .gamer-card-empty-bracket {
    color: var(--card-accent-primary);
    opacity: 0.6;
  }
  
  .gamer-card-add-button {
    padding: 8px 16px;
    background: rgba(30, 32, 44, 0.8);
    border: 1px solid var(--card-border-primary);
    border-radius: 8px;
    color: var(--card-text-primary);
    font-family: var(--card-font-mono);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;
    cursor: pointer;
  }
  
  .gamer-card-add-button:hover {
    border-color: var(--card-accent-primary);
    background: rgba(139, 92, 246, 0.1);
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
    transform: scale(1.05);
  }
  
  /* Footer */
  .gamer-card-footer {
    text-align: center;
    padding: 16px 0;
    border-top: 1px solid var(--card-border-secondary);
    background: var(--card-bg-tertiary);
    backdrop-filter: blur(8px);
  }
  
  .gamer-card-footer-text {
    font-family: var(--card-font-mono);
    font-size: 0.75rem;
    color: var(--card-text-muted);
    transition: all 0.2s ease;
  }
  
  .gamer-card-footer-text:hover {
    color: var(--card-accent-primary);
    transform: scale(1.05);
  }
  
  .gamer-card-footer-bracket {
    color: var(--card-accent-primary);
    opacity: 0.5;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .gamer-card-content {
      padding: 16px;
    }
    
    .gamer-card-profile-grid {
      grid-template-columns: 1fr;
    }
    
    .gamer-card-dna {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    
    .gamer-card-header {
      padding: 16px 20px;
    }
    
    .gamer-card-code-title {
      font-size: 1.1rem;
    }
  }
  
  /* Animation Utilities */
  .gamer-card-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .gamer-card-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }