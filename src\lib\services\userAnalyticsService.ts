'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface UserViewsResponse {
  success: boolean;
  data?: {
    totalViews: number;
    uniqueViews: number;
    reviewCount: number;
    averageViewsPerReview: number;
  };
  error?: string;
}

interface UserTopReviewsResponse {
  success: boolean;
  data?: Array<{
    id: string;
    title: string;
    slug: string;
    game_name: string;
    view_count: number;
    like_count: number;
    overall_score: number;
  }>;
  error?: string;
}

/**
 * Get total views for all reviews by a user
 * Uses both the reviews.view_count and review_analytics for comprehensive data
 */
export async function getUserTotalViews(userId: string): Promise<UserViewsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Method 1: Sum view_count from reviews table (simpler and more reliable)
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('author_id', userId)
      .not('view_count', 'is', null);

    if (reviewsError) {
      console.error('Error fetching review views:', reviewsError);
      return { success: false, error: 'Failed to fetch review views' };
    }

    // Calculate totals from reviews table
    const totalViews = reviewsData?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;
    const reviewCount = reviewsData?.length || 0;
    const averageViewsPerReview = reviewCount > 0 ? Math.round(totalViews / reviewCount) : 0;

    // Method 2: Try to get more detailed analytics from review_analytics table
    let uniqueViews = 0;
    try {
      const { data: analyticsData, error: analyticsError } = await supabase
        .from('review_analytics')
        .select(`
          unique_views,
          review:reviews!inner(author_id)
        `)
        .eq('review.author_id', userId)
        .not('unique_views', 'is', null);

      if (!analyticsError && analyticsData) {
        uniqueViews = analyticsData.reduce((sum, analytics) => sum + (analytics.unique_views || 0), 0);
      }
    } catch (error) {
      // Analytics data might not be available, continue with basic view count
      console.log('Analytics data not available, using basic view count');
    }

    return {
      success: true,
      data: {
        totalViews,
        uniqueViews: uniqueViews || totalViews, // Fallback to total views if unique views not available
        reviewCount,
        averageViewsPerReview
      }
    };
  } catch (error) {
    console.error('Error getting user total views:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get user's most viewed reviews
 */
export async function getUserTopReviews(userId: string, limit: number = 5): Promise<UserTopReviewsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    const { data, error } = await supabase
      .from('reviews')
      .select(`
        id,
        title,
        slug,
        game_name,
        view_count,
        like_count,
        overall_score
      `)
      .eq('author_id', userId)
      .not('view_count', 'is', null)
      .order('view_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching top reviews:', error);
      return { success: false, error: 'Failed to fetch top reviews' };
    }

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error getting user top reviews:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get user analytics summary including views, likes, and engagement metrics
 */
export async function getUserAnalyticsSummary(userId: string): Promise<{
  success: boolean;
  data?: {
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalReviews: number;
    averageScore: number;
    engagementRate: number; // (likes + comments) / views
  };
  error?: string;
}> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    const { data, error } = await supabase
      .from('reviews')
      .select(`
        view_count,
        like_count,
        comment_count,
        overall_score
      `)
      .eq('author_id', userId);

    if (error) {
      console.error('Error fetching user analytics:', error);
      return { success: false, error: 'Failed to fetch analytics' };
    }

    const totalViews = data?.reduce((sum, review) => sum + (review.view_count || 0), 0) || 0;
    const totalLikes = data?.reduce((sum, review) => sum + (review.like_count || 0), 0) || 0;
    const totalComments = data?.reduce((sum, review) => sum + (review.comment_count || 0), 0) || 0;
    const totalReviews = data?.length || 0;
    const averageScore = totalReviews > 0 
      ? Math.round((data?.reduce((sum, review) => sum + (review.overall_score || 0), 0) || 0) / totalReviews * 10) / 10
      : 0;
    const engagementRate = totalViews > 0 
      ? Math.round(((totalLikes + totalComments) / totalViews) * 100 * 100) / 100
      : 0;

    return {
      success: true,
      data: {
        totalViews,
        totalLikes,
        totalComments,
        totalReviews,
        averageScore,
        engagementRate
      }
    };
  } catch (error) {
    console.error('Error getting user analytics summary:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}