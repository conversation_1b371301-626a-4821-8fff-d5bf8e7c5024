import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

interface ViewTrackingResponse {
  success: boolean;
  newView: boolean; // Whether this was a new unique view
  totalViews: number;
  error?: string;
}

/**
 * API route to track review views with daily unique user limitation
 * Only counts one view per user per review per day
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { reviewId, viewerIp } = body;

    if (!reviewId) {
      return NextResponse.json(
        { success: false, error: 'Review ID is required' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);

    // Get current user if authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError && userError.message !== 'Auth session missing!') {
      console.error('Error getting user:', userError);
    }

    // Create unique identifier for this viewer
    // Priority: user ID > IP address > session fallback
    let viewerIdentifier: string;
    if (user?.id) {
      viewerIdentifier = user.id;
    } else if (viewerIp && viewerIp !== 'unknown' && viewerIp !== 'localhost' && viewerIp !== '127.0.0.1') {
      viewerIdentifier = viewerIp;
    } else {
      // Generate a session-based identifier as fallback
      viewerIdentifier = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    let newView = false;
    
    // Debug logging for tracking
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 View Tracking Debug:', {
        reviewId,
        user: user ? 'authenticated' : 'anonymous',
        userId: user?.id,
        viewerIp,
        viewerIdentifier,
        today
      });
    }

    // Check if this user/IP has already viewed this review today
    const { data: existingView, error: checkError } = await supabase
      .from('review_view_tracking')
      .select('id')
      .eq('review_id', reviewId)
      .eq('viewer_identifier', viewerIdentifier)
      .eq('view_date', today)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error checking existing view:', checkError);
      return NextResponse.json(
        { success: false, error: 'Failed to check existing view' },
        { status: 500 }
      );
    }

    // If no view exists for today, record it
    if (!existingView) {
      const { error: insertError } = await supabase
        .from('review_view_tracking')
        .insert({
          review_id: reviewId,
          viewer_identifier: viewerIdentifier,
          viewer_user_id: user?.id || null,
          viewer_ip: viewerIp || null,
          view_date: today,
          is_authenticated: !!user,
          user_agent: request.headers.get('user-agent') || null,
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error inserting view tracking:', insertError);
        return NextResponse.json(
          { success: false, error: 'Failed to record view' },
          { status: 500 }
        );
      }

      newView = true;

      // Update the review's view count
      const { error: updateError } = await supabase
        .rpc('increment_review_view_count', { review_uuid: reviewId });

      if (updateError) {
        console.error('Error updating view count:', updateError);
        // Don't fail the whole operation if the increment fails
      }
    }

    // Get current total view count
    const { data: reviewData, error: reviewError } = await supabase
      .from('reviews')
      .select('view_count')
      .eq('id', reviewId)
      .single();

    const totalViews = reviewData?.view_count || 0;

    const response: ViewTrackingResponse = {
      success: true,
      newView,
      totalViews,
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ View tracking completed:', response);
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error tracking review view:', error);
    return NextResponse.json(
      { 
        success: false, 
        newView: false, 
        totalViews: 0, 
        error: 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
}

// Also support GET for testing
export async function GET() {
  return NextResponse.json({ 
    message: 'View tracking API endpoint. Use POST to track views.' 
  });
}
