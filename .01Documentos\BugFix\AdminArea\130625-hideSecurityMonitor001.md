# Hide Security Monitor Navigation Item (Version 001)

Date: 13/06/25

## Summary
The **Security Monitor** link has been hidden from the admin navigation to satisfy the request to remove the monitor from the UI while keeping the underlying route intact.

## Files Edited
| File | Line Range |
| --- | --- |
| `src/components/admin/AdminNavigation.tsx` | 78-82, 100-110 |

### Details
1. Added `disabled: true` to the **Security Monitor** navigation object.
2. Updated the navigation render loop to filter out items with `disabled` set to `true`.

No other functional changes were made.

---
*Task ID: hideSecurityMonitor001*
