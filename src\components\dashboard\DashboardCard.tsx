"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";

// DASHBOARD REDESIGN: Phase 2 - Component Styling
// Date: 15/06/2025  
// Task: dashboardStyleAdmin002
//
// Created reusable DashboardCard component with admin-style design patterns:
// - Applied glassmorphism effects with backdrop-filter blur
// - Implemented purple cosmic theme matching admin panel
// - Added enhanced hover animations and glow effects
// - Created configurable variants for different card types
// - Applied consistent spacing and border radius system

interface DashboardCardProps {
  children: React.ReactNode;
  title?: string;
  icon?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'highlighted' | 'compact';
  hoverable?: boolean;
  animated?: boolean;
  delay?: number;
}

export function DashboardCard({
  children,
  title,
  icon,
  className,
  variant = 'default',
  hoverable = true,
  animated = true,
  delay = 0
}: DashboardCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const cardClasses = cn(
    // Base structure matching admin components
    "h-full flex flex-col border overflow-hidden relative",
    
    // Simple transitions
    "transition-all duration-300",
    
    // Simple theme like admin cards with rounded borders
    "shadow-lg bg-slate-900/60 border-slate-700/50 backdrop-blur-sm rounded-xl",
    
    // Simple hover effects when enabled
    hoverable && [
      "hover:border-violet-500/50 transition-all duration-300"
    ],
    
    // Variant-specific styling
    variant === 'highlighted' && "border-violet-400/50",
    variant === 'compact' && "p-4",
    
    className
  );

  const MotionCard = animated ? motion.div : 'div';
  const motionProps = animated ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { delay, duration: 0.6, ease: "easeOut" }
  } : {};

  return (
    <MotionCard
      {...motionProps}
      className={cardClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >

      
      {/* Header section with icon and title */}
      {(title || icon) && (
        <CardHeader className="flex flex-row items-start space-x-4 pb-3 relative z-10">
          {icon && (
            <div className="p-3 rounded-lg flex items-center justify-center bg-violet-500/20">
              {icon}
            </div>
          )}
          {title && (
            <CardTitle className="text-xl text-white pt-1 font-mono">
              {title}
            </CardTitle>
          )}
        </CardHeader>
      )}
      
      {/* Content section */}
      <CardContent className="flex-1 relative z-10">
        {children}
      </CardContent>
    </MotionCard>
  );
} 