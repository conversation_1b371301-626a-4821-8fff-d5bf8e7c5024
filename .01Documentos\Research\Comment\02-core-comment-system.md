# Step 2: Core Comment System Components
## Comment System Implementation Guide - Phase 2

**Date:** 2025-01-20  
**Task:** Frontend Comment Components Development  
**Priority:** HIGH  
**Estimated Time:** 6-8 hours  

---

## 🎯 Overview & Objectives

This step builds the core frontend components for the comment system, including comment display, comment forms, reply functionality, and voting system. We'll integrate with the database schema created in Step 1.

### Key Objectives:
- [ ] Create comment display component with threading
- [ ] Build comment submission form with validation
- [ ] Implement reply system for nested comments
- [ ] Add voting functionality (upvote/downvote)
- [ ] Create comment moderation hooks
- [ ] Implement real-time updates
- [ ] Add responsive design and accessibility

---

## 📋 Prerequisites

- [ ] Step 1 completed successfully (database schema)
- [ ] Supabase client configured in Next.js
- [ ] React Query/TanStack Query setup
- [ ] Existing review page structure
- [ ] User authentication working

---

## 🧩 Component Architecture

### Core Components Structure
```
src/components/comments/
├── CommentSection.tsx          # Main container
├── CommentList.tsx            # List of comments
├── CommentItem.tsx            # Individual comment
├── CommentForm.tsx            # New comment form
├── ReplyForm.tsx              # Reply form
├── CommentVoting.tsx          # Vote buttons
├── CommentModeration.tsx      # Moderation controls
├── CommentThread.tsx          # Nested comment thread
└── hooks/
    ├── useComments.ts         # Comment data fetching
    ├── useCommentMutations.ts # Comment CRUD operations
    ├── useCommentVoting.ts    # Voting functionality
    └── useCommentModeration.ts # Moderation hooks
```

---

## 🔧 Implementation Details

### 1. Comment Data Types
```typescript
// src/types/comments.ts
export interface Comment {
  id: string;
  review_id: string;
  author_id: string;
  parent_id?: string;
  content: string;
  is_deleted: boolean;
  is_pinned: boolean;
  is_approved: boolean;
  upvotes: number;
  downvotes: number;
  flag_count: number;
  created_at: string;
  updated_at: string;
  moderated_by?: string;
  moderated_at?: string;
  moderation_notes?: string;
  
  // Joined data
  author?: {
    id: string;
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  replies?: Comment[];
  user_vote?: 'upvote' | 'downvote' | null;
  can_moderate?: boolean;
}

export interface CommentFormData {
  content: string;
  parent_id?: string;
}

export interface CommentModerationAction {
  action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin';
  reason?: string;
  notes?: string;
}
```

### 2. Comment Hooks Implementation

#### useComments Hook
```typescript
// src/hooks/useComments.ts
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';

export function useComments(reviewId: string) {
  const supabase = createClient();
  
  return useQuery({
    queryKey: ['comments', reviewId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          author:profiles!author_id(id, username, display_name, avatar_url),
          user_vote:comment_votes!comment_votes_comment_id_fkey(vote_type)
        `)
        .eq('review_id', reviewId)
        .eq('is_approved', true)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true });

      if (error) throw error;
      
      // Build comment tree
      return buildCommentTree(data || []);
    },
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

function buildCommentTree(comments: any[]): Comment[] {
  const commentMap = new Map();
  const rootComments: Comment[] = [];

  // First pass: create comment objects
  comments.forEach(comment => {
    commentMap.set(comment.id, {
      ...comment,
      replies: [],
      user_vote: comment.user_vote?.[0]?.vote_type || null
    });
  });

  // Second pass: build tree structure
  comments.forEach(comment => {
    const commentObj = commentMap.get(comment.id);
    if (comment.parent_id) {
      const parent = commentMap.get(comment.parent_id);
      if (parent) {
        parent.replies.push(commentObj);
      }
    } else {
      rootComments.push(commentObj);
    }
  });

  return rootComments;
}
```

#### useCommentMutations Hook
```typescript
// src/hooks/useCommentMutations.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';

export function useCommentMutations(reviewId: string) {
  const queryClient = useQueryClient();
  const supabase = createClient();

  const createComment = useMutation({
    mutationFn: async (data: CommentFormData) => {
      const { data: result, error } = await supabase
        .from('comments')
        .insert({
          review_id: reviewId,
          content: data.content,
          parent_id: data.parent_id,
          author_id: (await supabase.auth.getUser()).data.user?.id,
          is_approved: true, // Auto-approve for now
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments', reviewId] });
      toast({
        title: "Comment posted!",
        description: "Your comment has been added successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error posting comment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateComment = useMutation({
    mutationFn: async ({ id, content }: { id: string; content: string }) => {
      const { data, error } = await supabase
        .from('comments')
        .update({ content, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments', reviewId] });
      toast({
        title: "Comment updated!",
        description: "Your comment has been updated successfully.",
      });
    },
  });

  const deleteComment = useMutation({
    mutationFn: async (commentId: string) => {
      const { error } = await supabase
        .from('comments')
        .update({ is_deleted: true })
        .eq('id', commentId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['comments', reviewId] });
      toast({
        title: "Comment deleted",
        description: "Your comment has been deleted.",
      });
    },
  });

  return {
    createComment,
    updateComment,
    deleteComment,
  };
}
```

### 3. Main Comment Section Component
```typescript
// src/components/comments/CommentSection.tsx
'use client';

import React, { useState } from 'react';
import { useComments } from '@/hooks/useComments';
import { useAuthContext } from '@/contexts/AuthContext';
import { CommentList } from './CommentList';
import { CommentForm } from './CommentForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, Users, TrendingUp } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface CommentSectionProps {
  reviewId: string;
  reviewTitle: string;
  reviewAuthorId: string;
}

export function CommentSection({ 
  reviewId, 
  reviewTitle, 
  reviewAuthorId 
}: CommentSectionProps) {
  const { user } = useAuthContext();
  const { data: comments, isLoading, error } = useComments(reviewId);
  const [showForm, setShowForm] = useState(false);

  if (error) {
    return (
      <Card className="bg-slate-900/60 border-slate-700/50">
        <CardContent className="p-6">
          <div className="text-center text-slate-400">
            <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Unable to load comments. Please try again later.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalComments = comments?.reduce((count, comment) => {
    return count + 1 + (comment.replies?.length || 0);
  }, 0) || 0;

  return (
    <div className="space-y-6">
      {/* Comment Stats Header */}
      <Card className="bg-slate-900/60 border-slate-700/50">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-slate-200">
            <MessageSquare className="h-6 w-6 text-purple-400" />
            Discussion
            <span className="text-sm font-normal text-slate-400">
              ({totalComments} {totalComments === 1 ? 'comment' : 'comments'})
            </span>
          </CardTitle>
        </CardHeader>
        
        {/* Quick Stats */}
        <CardContent className="pt-0">
          <div className="flex items-center gap-6 text-sm text-slate-400">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>{comments?.length || 0} participants</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              <span>Active discussion</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comment Form */}
      {user ? (
        <CommentForm 
          reviewId={reviewId}
          onSuccess={() => setShowForm(false)}
          placeholder={`Share your thoughts about "${reviewTitle}"...`}
        />
      ) : (
        <Card className="bg-slate-900/60 border-slate-700/50">
          <CardContent className="p-6 text-center">
            <p className="text-slate-400 mb-4">
              Sign in to join the discussion
            </p>
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
              Sign In
            </button>
          </CardContent>
        </Card>
      )}

      {/* Comments List */}
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="bg-slate-900/60 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex gap-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <CommentList 
          comments={comments || []}
          reviewAuthorId={reviewAuthorId}
          currentUserId={user?.id}
        />
      )}
    </div>
  );
}
```

### 4. Comment Form Component
```typescript
// src/components/comments/CommentForm.tsx
'use client';

import React, { useState } from 'react';
import { useCommentMutations } from '@/hooks/useCommentMutations';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2 } from 'lucide-react';
import { useAuthContext } from '@/contexts/AuthContext';

interface CommentFormProps {
  reviewId: string;
  parentId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  placeholder?: string;
  compact?: boolean;
}

export function CommentForm({
  reviewId,
  parentId,
  onSuccess,
  onCancel,
  placeholder = "Write a comment...",
  compact = false
}: CommentFormProps) {
  const { user } = useAuthContext();
  const [content, setContent] = useState('');
  const { createComment } = useCommentMutations(reviewId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() || !user) return;

    try {
      await createComment.mutateAsync({
        content: content.trim(),
        parent_id: parentId,
      });
      
      setContent('');
      onSuccess?.();
    } catch (error) {
      console.error('Error posting comment:', error);
    }
  };

  if (!user) return null;

  return (
    <Card className="bg-slate-900/60 border-slate-700/50">
      <CardContent className={compact ? "p-4" : "p-6"}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-3">
            <img
              src={user.user_metadata?.avatar_url || '/default-avatar.png'}
              alt={user.user_metadata?.display_name || 'User'}
              className="h-10 w-10 rounded-full object-cover"
            />
            <div className="flex-1">
              <Textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={placeholder}
                className="min-h-[100px] bg-slate-800/50 border-slate-600 text-slate-200 placeholder:text-slate-400 resize-none"
                maxLength={1000}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-slate-400">
                  {content.length}/1000 characters
                </span>
                <div className="flex gap-2">
                  {onCancel && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={onCancel}
                      className="text-slate-400 hover:text-slate-200"
                    >
                      Cancel
                    </Button>
                  )}
                  <Button
                    type="submit"
                    size="sm"
                    disabled={!content.trim() || createComment.isPending}
                    className="bg-purple-600 hover:bg-purple-700 text-white"
                  >
                    {createComment.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        {parentId ? 'Reply' : 'Comment'}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
```

### 5. Comment List and Item Components
```typescript
// src/components/comments/CommentList.tsx
'use client';

import React from 'react';
import { CommentItem } from './CommentItem';
import { Comment } from '@/types/comments';

interface CommentListProps {
  comments: Comment[];
  reviewAuthorId: string;
  currentUserId?: string;
  level?: number;
}

export function CommentList({
  comments,
  reviewAuthorId,
  currentUserId,
  level = 0
}: CommentListProps) {
  if (!comments.length) {
    return (
      <div className="text-center py-12 text-slate-400">
        <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
        <p>No comments yet. Be the first to share your thoughts!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          reviewAuthorId={reviewAuthorId}
          currentUserId={currentUserId}
          level={level}
        />
      ))}
    </div>
  );
}
```

```typescript
// src/components/comments/CommentItem.tsx
'use client';

import React, { useState } from 'react';
import { Comment } from '@/types/comments';
import { CommentVoting } from './CommentVoting';
import { ReplyForm } from './ReplyForm';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Reply,
  Pin,
  Crown,
  MoreHorizontal,
  Flag,
  Edit,
  Trash2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface CommentItemProps {
  comment: Comment;
  reviewAuthorId: string;
  currentUserId?: string;
  level?: number;
}

export function CommentItem({
  comment,
  reviewAuthorId,
  currentUserId,
  level = 0
}: CommentItemProps) {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const isAuthor = comment.author_id === currentUserId;
  const isReviewOwner = reviewAuthorId === currentUserId;
  const isCommentByReviewOwner = comment.author_id === reviewAuthorId;
  const canModerate = isReviewOwner || isAuthor;

  return (
    <div className={`${level > 0 ? 'ml-8 border-l-2 border-slate-700 pl-4' : ''}`}>
      <Card className="bg-slate-900/60 border-slate-700/50 hover:border-slate-600/50 transition-colors">
        <CardContent className="p-4">
          {/* Comment Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <img
                src={comment.author?.avatar_url || '/default-avatar.png'}
                alt={comment.author?.display_name || 'User'}
                className="h-8 w-8 rounded-full object-cover"
              />
              <div className="flex items-center gap-2">
                <span className="font-medium text-slate-200">
                  {comment.author?.display_name || 'Anonymous'}
                </span>
                {isCommentByReviewOwner && (
                  <Badge variant="secondary" className="bg-purple-600/20 text-purple-300 border-purple-500/30">
                    <Crown className="h-3 w-3 mr-1" />
                    Author
                  </Badge>
                )}
                {comment.is_pinned && (
                  <Badge variant="secondary" className="bg-yellow-600/20 text-yellow-300 border-yellow-500/30">
                    <Pin className="h-3 w-3 mr-1" />
                    Pinned
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2 text-xs text-slate-400">
              <span>{formatDistanceToNow(new Date(comment.created_at))} ago</span>
              {canModerate && (
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Comment Content */}
          <div className="mb-4">
            <p className="text-slate-300 whitespace-pre-wrap leading-relaxed">
              {comment.content}
            </p>
            {comment.updated_at !== comment.created_at && (
              <p className="text-xs text-slate-500 mt-2 italic">
                (edited)
              </p>
            )}
          </div>

          {/* Comment Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <CommentVoting
                commentId={comment.id}
                upvotes={comment.upvotes}
                downvotes={comment.downvotes}
                userVote={comment.user_vote}
              />

              {currentUserId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReplyForm(!showReplyForm)}
                  className="text-slate-400 hover:text-slate-200 h-8 px-3"
                >
                  <Reply className="h-4 w-4 mr-1" />
                  Reply
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {!isAuthor && currentUserId && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-400 hover:text-red-400 h-8 px-3"
                >
                  <Flag className="h-4 w-4 mr-1" />
                  Report
                </Button>
              )}
            </div>
          </div>

          {/* Reply Form */}
          {showReplyForm && (
            <div className="mt-4">
              <ReplyForm
                reviewId={comment.review_id}
                parentId={comment.id}
                onSuccess={() => setShowReplyForm(false)}
                onCancel={() => setShowReplyForm(false)}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Nested Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4">
          <CommentList
            comments={comment.replies}
            reviewAuthorId={reviewAuthorId}
            currentUserId={currentUserId}
            level={level + 1}
          />
        </div>
      )}
    </div>
  );
}
```

### 6. Voting Component
```typescript
// src/components/comments/CommentVoting.tsx
'use client';

import React from 'react';
import { useCommentVoting } from '@/hooks/useCommentVoting';
import { Button } from '@/components/ui/button';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CommentVotingProps {
  commentId: string;
  upvotes: number;
  downvotes: number;
  userVote?: 'upvote' | 'downvote' | null;
}

export function CommentVoting({
  commentId,
  upvotes,
  downvotes,
  userVote
}: CommentVotingProps) {
  const { vote, isLoading } = useCommentVoting(commentId);

  const score = upvotes - downvotes;

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => vote(userVote === 'upvote' ? null : 'upvote')}
        disabled={isLoading}
        className={cn(
          "h-8 w-8 p-0 hover:bg-green-600/20",
          userVote === 'upvote' && "bg-green-600/20 text-green-400"
        )}
      >
        <ChevronUp className="h-4 w-4" />
      </Button>

      <span className={cn(
        "text-sm font-medium min-w-[2rem] text-center",
        score > 0 && "text-green-400",
        score < 0 && "text-red-400",
        score === 0 && "text-slate-400"
      )}>
        {score}
      </span>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => vote(userVote === 'downvote' ? null : 'downvote')}
        disabled={isLoading}
        className={cn(
          "h-8 w-8 p-0 hover:bg-red-600/20",
          userVote === 'downvote' && "bg-red-600/20 text-red-400"
        )}
      >
        <ChevronDown className="h-4 w-4" />
      </Button>
    </div>
  );
}
```

---

## ✅ Implementation Checklist

### Component Development
- [ ] Create comment types and interfaces
- [ ] Implement useComments hook with tree building
- [ ] Implement useCommentMutations hook
- [ ] Create CommentSection main component
- [ ] Create CommentForm component
- [ ] Create CommentList component
- [ ] Create CommentItem component
- [ ] Create CommentVoting component
- [ ] Create ReplyForm component

### Integration
- [ ] Integrate CommentSection into review pages
- [ ] Test comment creation and display
- [ ] Test reply functionality
- [ ] Test voting system
- [ ] Test real-time updates
- [ ] Test responsive design
- [ ] Test accessibility features

### Error Handling
- [ ] Handle network errors gracefully
- [ ] Show loading states appropriately
- [ ] Display user-friendly error messages
- [ ] Implement retry mechanisms
- [ ] Handle authentication errors

---

## 🧪 Testing Instructions

### 1. Component Testing
```typescript
// Test comment creation
// Test reply functionality
// Test voting system
// Test moderation controls
// Test responsive design
```

### 2. Integration Testing
```typescript
// Test with different user roles
// Test with large comment threads
// Test real-time updates
// Test error scenarios
```

---

## 📝 AI Implementation Prompts

### Prompt 1: Core Components
```
Implement the core comment system components using the provided TypeScript interfaces and React patterns. Focus on creating reusable, accessible components that integrate well with the existing CriticalPixel design system. Ensure proper error handling and loading states.
```

### Prompt 2: Hooks Implementation
```
Create the React Query hooks for comment data management. Implement proper caching strategies, optimistic updates, and error handling. Ensure the comment tree building logic works correctly for nested replies.
```

### Prompt 3: Integration Testing
```
Integrate the comment system into existing review pages and test thoroughly. Verify that comments display correctly, forms work properly, and the system handles edge cases gracefully. Test with different user authentication states.
```

---

## 🔄 Next Steps

Upon completion:
1. Proceed to **Step 3: Review Owner Moderation Dashboard**
2. Implement advanced moderation features
3. Add comment management interface
4. Test moderation workflows

---

**⚠️ IMPORTANT NOTES FOR AI:**
- Follow existing CriticalPixel design patterns
- Ensure accessibility compliance
- Test with different screen sizes
- Implement proper error boundaries
- Use existing UI components where possible
