declare module 'fast-average-color' {
  export interface FastAverageColorResult {
    value: [number, number, number, number];
    rgb: string;
    rgba: string;
    hex: string;
    hexa: string;
    isDark: boolean;
    isLight: boolean;
  }

  export class FastAverageColor {
    getColor(
      resource: HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | null,
      options?: {
        defaultColor?: [number, number, number, number];
        algorithm?: 'simple' | 'sqrt' | 'dominant';
        mode?: 'speed' | 'precision';
        step?: number;
        left?: number;
        top?: number;
        width?: number;
        height?: number;
      }
    ): FastAverageColorResult;
  }
}
