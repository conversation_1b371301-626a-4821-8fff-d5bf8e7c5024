'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogOverlay, DialogPortal } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSteamGridDB, SteamGridDBGame, SteamGridDBArtwork } from '@/hooks/useSteamGridDB';
import { 
  Search, 
  CheckCircle, 
  Gamepad2, 
  Image, 
  Star, 
  User, 
  ExternalLink,
  X,
  Download,
  AlertCircle
} from 'lucide-react';

interface SteamGridDBBannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBannerSelect: (bannerUrl: string) => void;
  gameName?: string;
}

export function SteamGridDBBannerModal({ 
  isOpen, 
  onClose, 
  onBannerSelect, 
  gameName 
}: SteamGridDBBannerModalProps) {
  const [searchQuery, setSearchQuery] = useState(gameName || '');
  const [selectedGame, setSelectedGame] = useState<SteamGridDBGame | null>(null);
  const [step, setStep] = useState<'search' | 'browse'>('search');
  const [isMounted, setIsMounted] = useState(false);
  
  const { loading, error, games, heroes, searchGames, getHeroes } = useSteamGridDB();

  // Ensure component only renders after mount (client-side only)
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Auto-search when modal opens if gameName is provided
  useEffect(() => {
    if (isOpen && gameName && !games.length) {
      setSearchQuery(gameName);
      searchGames(gameName);
    }
  }, [isOpen, gameName]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    await searchGames(searchQuery.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleGameSelect = async (game: SteamGridDBGame) => {
    setSelectedGame(game);
    setStep('browse');
    // Fetch hero banners for the selected game
    await getHeroes(game.id, { limit: 20 });
  };

  const handleBannerSelect = (banner: SteamGridDBArtwork) => {
    onBannerSelect(banner.url);
    onClose();
  };

  const handleClose = () => {
    setStep('search');
    setSelectedGame(null);
    onClose();
  };

  const renderSearchStep = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 rounded-md bg-slate-800/60">
          <Search className="h-4 w-4 text-slate-400" />
        </div>
        <div>
          <div className="font-mono text-sm">
            <span className="text-violet-400">//</span>
            <span className="text-slate-300 ml-1">Find Game</span>
          </div>
          <p className="text-slate-500 text-xs mt-1">
            Search for your game on SteamGridDB
          </p>
        </div>
      </div>

      {/* Search Input */}
      <div className="space-y-3">
        <div>
          <div className="font-mono text-xs text-slate-400 mb-2">Game search</div>
          <div className="flex gap-2">
            <Input
              placeholder="Enter game name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={loading}
              className="bg-slate-800/60 border-slate-600/40 text-slate-200 placeholder:text-slate-500
                       focus:border-violet-400/50 focus:ring-1 focus:ring-violet-400/20 font-mono text-sm flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={loading || !searchQuery.trim()}
              className="bg-violet-600/80 hover:bg-violet-500/80 text-white border-none"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert className="bg-red-500/10 border-red-500/20 text-red-300">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <div className="space-y-3">
          <div className="font-mono text-xs text-slate-400">Searching...</div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-3 bg-slate-800/40 border border-slate-700/40 rounded-lg">
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-[200px] bg-slate-700/50" />
                <Skeleton className="h-3 w-[100px] bg-slate-700/50" />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Results */}
      {!loading && games.length > 0 && (
        <div className="space-y-3">
          <div className="font-mono text-xs text-slate-400">
            Found {games.length} games
          </div>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {games.map((game) => (
              <div
                key={game.id}
                className="flex items-center justify-between p-3 bg-slate-800/40 border border-slate-700/40 rounded-lg hover:bg-slate-700/40 cursor-pointer transition-all group"
                onClick={() => handleGameSelect(game)}
              >
                <div className="flex items-center space-x-3">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm text-slate-300">{game.name}</span>
                    </div>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="text-slate-400 hover:text-violet-300 group-hover:bg-violet-600/20"
                >
                  <span className="text-violet-400">&lt;</span>
                  Select
                  <span className="text-violet-400">/&gt;</span>
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {!loading && searchQuery && games.length === 0 && !error && (
        <div className="text-center py-8">
          <Gamepad2 className="h-12 w-12 mx-auto mb-4 text-slate-600" />
          <div className="font-mono text-sm text-slate-400 mb-2">
            No results found
          </div>
          <p className="text-slate-500 text-sm">No games found for "{searchQuery}"</p>
          <p className="text-slate-600 text-xs mt-1">Try adjusting your search terms</p>
        </div>
      )}

      {/* Initial State */}
      {!loading && !searchQuery && games.length === 0 && !error && (
        <div className="text-center py-8">
          <Search className="h-12 w-12 mx-auto mb-4 text-slate-600" />
          <div className="font-mono text-sm text-slate-400 mb-2">
            Ready to search
          </div>
          <p className="text-slate-500 text-sm">Enter a game name to find banner artwork</p>
          <p className="text-slate-600 text-xs mt-1">Powered by SteamGridDB community</p>
        </div>
      )}
    </div>
  );

  const renderBrowseStep = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-md bg-slate-800/60">
            <Image className="h-4 w-4 text-slate-400" />
          </div>
          <div>
            <div className="font-mono text-sm">
              <span className="text-violet-400">//</span>
              <span className="text-slate-300 ml-1">Hero Banners</span>
            </div>
            <p className="text-slate-500 text-xs mt-1">
              Select a banner for {selectedGame?.name}
            </p>
          </div>
        </div>
        <Button
          onClick={() => setStep('search')}
          variant="outline"
          size="sm"
          className="border-slate-600/40 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300"
        >
          <Search className="h-3 w-3 mr-1" />
          Change Game
        </Button>
      </div>

      {/* Banner Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="aspect-[2/1] w-full rounded-lg bg-slate-700/50" />
              <Skeleton className="h-4 w-3/4 bg-slate-700/50" />
              <Skeleton className="h-3 w-1/2 bg-slate-700/50" />
            </div>
          ))}
        </div>
      ) : heroes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {heroes.map((banner) => (
            <div 
              key={banner.id} 
              className="bg-slate-800/40 border border-slate-700/40 rounded-lg overflow-hidden group cursor-pointer hover:border-violet-500/40 transition-all"
              onClick={() => handleBannerSelect(banner)}
            >
              <div className="relative aspect-[2/1]">
                <img
                  src={banner.thumb || banner.url}
                  alt="Banner preview"
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                  <Button
                    size="sm"
                    className="bg-violet-600/80 hover:bg-violet-500/80 text-white border-none"
                  >
                    <Download className="h-3 w-3 mr-1" />
                    Use Banner
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(banner.url, '_blank');
                    }}
                    className="bg-slate-700/80 hover:bg-slate-600/80"
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
                {banner.score > 0 && (
                  <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                    <Star className="h-3 w-3 fill-current text-yellow-400" />
                    {banner.score}
                  </div>
                )}
              </div>
              <div className="p-3">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs bg-slate-700/50 text-slate-400 border-slate-600/30">
                      {banner.style}
                    </Badge>
                    <span className="text-xs text-slate-500 font-mono">
                      {banner.width}×{banner.height}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-slate-500">
                    <User className="h-3 w-3" />
                    <a 
                      href={`https://www.steamgriddb.com/profile/${banner.author.steam64}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="truncate hover:text-violet-400 transition-colors"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {banner.author.name}
                    </a>
                  </div>
                  {banner.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {banner.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} className="text-xs bg-slate-700/30 text-slate-500 border-slate-600/20">
                          {tag}
                        </Badge>
                      ))}
                      {banner.tags.length > 2 && (
                        <Badge className="text-xs bg-slate-700/30 text-slate-500 border-slate-600/20">
                          +{banner.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Image className="h-12 w-12 mx-auto mb-4 text-slate-600" />
          <div className="font-mono text-sm text-slate-400 mb-2">
            No banners found
          </div>
          <p className="text-slate-500 text-sm">No hero banners available for this game</p>
          <p className="text-slate-600 text-xs mt-1">Try a different game or check back later</p>
        </div>
      )}
    </div>
  );

  if (!isMounted || !isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 z-50 bg-transparent backdrop-blur-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden bg-slate-800/90 border-slate-700/50 text-slate-200">
        <DialogHeader className="border-b border-slate-700/40 pb-4">
          <DialogTitle className="font-mono text-lg">
            <span className="text-violet-400">//</span>
            <span className="ml-2">SteamGridDB Banner Search</span>
          </DialogTitle>
        </DialogHeader>
        
        <div className="overflow-y-auto p-6">
          {step === 'search' ? renderSearchStep() : renderBrowseStep()}
        </div>

        {/* Footer */}
        <div className="border-t border-slate-700/40 pt-4 px-6 pb-4">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2 text-slate-500">
              <span className="font-mono">// powered by</span>
              <a 
                href="https://www.steamgriddb.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-violet-400 hover:text-violet-300 transition-colors flex items-center gap-1"
              >
                SteamGridDB
                <ExternalLink className="h-3 w-3" />
              </a>
              <span className="text-slate-600">and their awesome contributors</span>
            </div>
          </div>
        </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}