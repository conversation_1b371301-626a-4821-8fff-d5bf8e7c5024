import React from 'react';

const PlusIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M12 2v20M2 12h20" stroke="currentColor" strokeWidth="2" fill="none"/>
    <rect x="11" y="6" width="2" height="1"/>
    <rect x="11" y="17" width="2" height="1"/>
    <rect x="6" y="11" width="1" height="2"/>
    <rect x="17" y="11" width="1" height="2"/>
    <circle cx="8" cy="8" r="0.5"/>
    <circle cx="16" cy="8" r="0.5"/>
    <circle cx="8" cy="16" r="0.5"/>
    <circle cx="16" cy="16" r="0.5"/>
  </svg>
);

export default PlusIcon;