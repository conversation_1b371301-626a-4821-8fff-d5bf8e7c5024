'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Link, ExternalLink, AlertCircle } from 'lucide-react';

interface LinkInsertModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (url: string, text?: string) => void;
  initialUrl?: string;
  initialText?: string;
  isEditing?: boolean;
}

export default function LinkInsertModal({
  isOpen,
  onClose,
  onInsert,
  initialUrl = '',
  initialText = '',
  isEditing = false
}: LinkInsertModalProps) {
  const [url, setUrl] = useState(initialUrl);
  const [text, setText] = useState(initialText);
  const [error, setError] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setUrl(initialUrl);
      setText(initialText);
      setError('');
    }
  }, [isOpen, initialUrl, initialText]);

  // URL validation
  const validateUrl = (urlString: string): boolean => {
    try {
      const url = new URL(urlString);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch {
      return false;
    }
  };

  // Auto-add protocol if missing
  const normalizeUrl = (urlString: string): string => {
    if (!urlString) return '';
    
    // If it already has a protocol, return as is
    if (urlString.match(/^https?:\/\//)) {
      return urlString;
    }
    
    // Add https:// by default
    return `https://${urlString}`;
  };

  const handleInsert = () => {
    if (!url.trim()) {
      setError('URL is required');
      return;
    }

    const normalizedUrl = normalizeUrl(url.trim());
    
    if (!validateUrl(normalizedUrl)) {
      setError('Please enter a valid URL');
      return;
    }

    setIsValidating(true);
    
    // Simulate validation delay for better UX
    setTimeout(() => {
      onInsert(normalizedUrl, text.trim() || undefined);
      setIsValidating(false);
      onClose();
    }, 300);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleInsert();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900/95 border border-slate-700/50 backdrop-blur-md max-w-md">
        <DialogHeader>
          <DialogTitle className="text-slate-200 font-mono flex items-center gap-2">
            <Link className="h-4 w-4 text-violet-400" />
            <span className="text-violet-400">//</span>
            {isEditing ? 'Edit Link' : 'Insert Link'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="url" className="text-slate-300 font-mono text-sm">
              URL
            </Label>
            <Input
              id="url"
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => {
                setUrl(e.target.value);
                setError('');
              }}
              onKeyDown={handleKeyDown}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono"
              autoFocus
            />
          </div>

          {/* Link Text Input */}
          <div className="space-y-2">
            <Label htmlFor="text" className="text-slate-300 font-mono text-sm">
              Link Text <span className="text-slate-500">(optional)</span>
            </Label>
            <Input
              id="text"
              type="text"
              placeholder="Link description"
              value={text}
              onChange={(e) => setText(e.target.value)}
              onKeyDown={handleKeyDown}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono"
            />
            <p className="text-xs text-slate-500 font-mono">
              Leave empty to use the URL as display text
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm font-mono bg-red-900/20 border border-red-700/30 rounded-lg p-3">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              {error}
            </div>
          )}

          {/* URL Preview */}
          {url && !error && (
            <div className="bg-slate-800/30 border border-slate-700/30 rounded-lg p-3">
              <p className="text-xs text-slate-500 font-mono mb-1">Preview:</p>
              <div className="flex items-center gap-2 text-sm">
                <ExternalLink className="h-3 w-3 text-slate-400" />
                <span className="text-slate-300 font-mono break-all">
                  {normalizeUrl(url)}
                </span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-slate-800/50 border-slate-600/50 text-slate-300 hover:bg-slate-700/50 font-mono"
          >
            Cancel
          </Button>
          <Button
            onClick={handleInsert}
            disabled={!url.trim() || isValidating}
            className="bg-violet-600 hover:bg-violet-700 text-white font-mono"
          >
            {isValidating ? (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin" />
                Validating...
              </div>
            ) : (
              <>
                <Link className="h-4 w-4 mr-2" />
                {isEditing ? 'Update Link' : 'Insert Link'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
