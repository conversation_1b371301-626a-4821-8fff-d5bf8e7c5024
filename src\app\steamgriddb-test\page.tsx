'use client';

import { useState } from 'react';
import { SteamGridDBIntegration } from '@/components/steamgriddb/SteamGridDBIntegration';
import { SteamGridDBSearch } from '@/components/steamgriddb/SteamGridDBSearch';
import { SteamGridDBArtworkGallery } from '@/components/steamgriddb/SteamGridDBArtworkGallery';
import { SteamGridDBGame, SteamGridDBArtwork } from '@/hooks/useSteamGridDB';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export default function SteamGridDBTestPage() {
  const [selectedGame, setSelectedGame] = useState<SteamGridDBGame | null>(null);
  const [selectedArtwork, setSelectedArtwork] = useState<SteamGridDBArtwork | null>(null);

  const handleGameSelect = (game: SteamGridDBGame) => {
    setSelectedGame(game);
  };

  const handleArtworkSelect = (artwork: SteamGridDBArtwork, type: 'grid' | 'hero' | 'logo' | 'icon') => {
    setSelectedArtwork(artwork);
    console.log(`Selected ${type} artwork:`, artwork);
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">SteamGridDB Integration Test</h1>
        <p className="text-muted-foreground">
          Test the SteamGridDB API integration and browse custom game artwork
        </p>
      </div>

      <Separator />

      {/* Integrated Component */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Integrated Component</h2>
        <p className="text-muted-foreground">
          Complete SteamGridDB integration component with search and gallery
        </p>
        
        <SteamGridDBIntegration
          gameName="Half-Life 2"
          onArtworkSelect={handleArtworkSelect}
          selectedArtwork={selectedArtwork}
        />
      </div>

      <Separator />

      {/* Individual Components */}
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Individual Components</h2>
          <p className="text-muted-foreground mb-6">
            Test individual SteamGridDB components separately
          </p>
        </div>

        <div className="grid gap-6">
          {/* Search Component */}
          <Card>
            <CardHeader>
              <CardTitle>Game Search</CardTitle>
            </CardHeader>
            <CardContent>
              <SteamGridDBSearch onGameSelect={handleGameSelect} />
            </CardContent>
          </Card>

          {/* Gallery Component */}
          <Card>
            <CardHeader>
              <CardTitle>Artwork Gallery</CardTitle>
            </CardHeader>
            <CardContent>
              <SteamGridDBArtworkGallery 
                selectedGame={selectedGame}
                onArtworkSelect={handleArtworkSelect}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator />

      {/* Debug Information */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Debug Information</h2>
        
        <div className="grid md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Selected Game</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedGame ? (
                <div className="space-y-2">
                  <p><strong>ID:</strong> {selectedGame.id}</p>
                  <p><strong>Name:</strong> {selectedGame.name}</p>
                  <p><strong>Verified:</strong> {selectedGame.verified ? 'Yes' : 'No'}</p>
                  <p><strong>Types:</strong> {selectedGame.types.join(', ')}</p>
                </div>
              ) : (
                <p className="text-muted-foreground">No game selected</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Selected Artwork</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedArtwork ? (
                <div className="space-y-2">
                  <p><strong>ID:</strong> {selectedArtwork.id}</p>
                  <p><strong>Style:</strong> {selectedArtwork.style}</p>
                  <p><strong>Dimensions:</strong> {selectedArtwork.width}×{selectedArtwork.height}</p>
                  <p><strong>Score:</strong> {selectedArtwork.score}</p>
                  <p><strong>Author:</strong> {selectedArtwork.author.name}</p>
                  <p><strong>Tags:</strong> {selectedArtwork.tags.join(', ')}</p>
                  <p><strong>URL:</strong> <a href={selectedArtwork.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">View Image</a></p>
                </div>
              ) : (
                <p className="text-muted-foreground">No artwork selected</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}