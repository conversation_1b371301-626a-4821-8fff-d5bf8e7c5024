'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Users, Search, TrendingUp, ArrowRight, Home, RefreshCw } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { getSimilarProfiles, getFeaturedProfiles } from '@/app/u/actions';
import type { UserProfile } from '@/lib/types/profile';

interface ProfileNotFoundProps {
  searchedUsername: string;
  onRetry?: () => void;
}

interface ProfileCardProps {
  profile: UserProfile;
  index: number;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ profile, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: index * 0.1 }}
  >
    <Link href={`/u/${profile.username}`}>
      <Card className="bg-slate-900/60 border-slate-700/50 hover:border-violet-500/50 transition-all duration-300 cursor-pointer group">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="relative">
              {profile.avatar_url ? (
                <img
                  src={profile.avatar_url}
                  alt={`${profile.display_name || profile.username}'s avatar`}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
              )}
              {profile.level && profile.level > 1 && (
                <div className="absolute -bottom-1 -right-1 bg-violet-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {profile.level}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-slate-200 truncate group-hover:text-violet-400 transition-colors">
                {profile.display_name || profile.username}
              </h3>
              <p className="text-sm text-slate-400 truncate">@{profile.username}</p>
              {profile.review_count && profile.review_count > 0 && (
                <p className="text-xs text-violet-400">{profile.review_count} reviews</p>
              )}
            </div>
            <ArrowRight className="w-4 h-4 text-slate-500 group-hover:text-violet-400 transition-colors" />
          </div>
          {profile.bio && (
            <p className="text-sm text-slate-400 mt-2 line-clamp-2">{profile.bio}</p>
          )}
        </CardContent>
      </Card>
    </Link>
  </motion.div>
);

export default function ProfileNotFound({ searchedUsername, onRetry }: ProfileNotFoundProps) {
  const [similarProfiles, setSimilarProfiles] = useState<UserProfile[]>([]);
  const [featuredProfiles, setFeaturedProfiles] = useState<UserProfile[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSuggestions = async () => {
      setLoading(true);
      try {
        const [similar, featured] = await Promise.all([
          getSimilarProfiles(searchedUsername),
          getFeaturedProfiles()
        ]);
        setSimilarProfiles(similar);
        setFeaturedProfiles(featured);
      } catch (error) {
        console.error('Error loading profile suggestions:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSuggestions();
  }, [searchedUsername]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      window.location.href = `/u/${searchTerm.trim()}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Main 404 Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-br from-red-500/20 to-orange-500/20 flex items-center justify-center">
            <Users className="h-12 w-12 text-red-400" />
          </div>
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">
            Profile Not Found
          </h1>
          <p className="text-xl text-slate-300 mb-2">
            We couldn't find a profile for{' '}
            <span className="font-mono text-violet-400 bg-slate-800/50 px-2 py-1 rounded">
              @{searchedUsername}
            </span>
          </p>
          <p className="text-slate-400">
            The user might have changed their username, or the profile doesn't exist.
          </p>
        </motion.div>

        {/* Search Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-slate-900/60 border-slate-700/50">
            <CardContent className="p-6">
              <form onSubmit={handleSearch} className="flex gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                  <Input
                    type="text"
                    placeholder="Search for another profile..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder-slate-400"
                  />
                </div>
                <Button type="submit" className="bg-violet-600 hover:bg-violet-700">
                  Search
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-3 mb-8 justify-center"
        >
          <Link href="/">
            <Button variant="outline" className="flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-800">
              <Home className="w-4 h-4" />
              Go Home
            </Button>
          </Link>
          {onRetry && (
            <Button onClick={onRetry} variant="outline" className="flex items-center gap-2 border-slate-600 text-slate-300 hover:bg-slate-800">
              <RefreshCw className="w-4 h-4" />
              Try Again
            </Button>
          )}
          <Link href="/explore">
            <Button className="flex items-center gap-2 bg-violet-600 hover:bg-violet-700">
              <TrendingUp className="w-4 h-4" />
              Explore Profiles
            </Button>
          </Link>
        </motion.div>

        {loading ? (
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-violet-400"></div>
            <p className="text-slate-400 mt-2">Loading suggestions...</p>
          </div>
        ) : (
          <>
            {/* Similar Profiles */}
            {similarProfiles.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-semibold mb-4 text-slate-200">
                  Similar Profiles
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {similarProfiles.map((profile, index) => (
                    <ProfileCard key={profile.id} profile={profile} index={index} />
                  ))}
                </div>
              </motion.div>
            )}

            {/* Featured Profiles */}
            {featuredProfiles.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <h2 className="text-2xl font-semibold mb-4 text-slate-200 flex items-center gap-2">
                  <TrendingUp className="w-6 h-6 text-violet-400" />
                  Featured Profiles
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {featuredProfiles.slice(0, 6).map((profile, index) => (
                    <ProfileCard key={profile.id} profile={profile} index={index} />
                  ))}
                </div>
              </motion.div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
