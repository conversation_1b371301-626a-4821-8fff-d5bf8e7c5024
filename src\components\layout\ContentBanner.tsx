'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import { 
  getUserContentBanner, 
  trackContentBannerImpression, 
  trackContentBannerClick,
  type ContentBannerData 
} from '@/lib/services/contentBannerService';

interface ContentBannerProps {
  userId?: string;
  className?: string;
}

const ContentBanner: React.FC<ContentBannerProps> = ({
  userId,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [bannerData, setBannerData] = useState<ContentBannerData | null>(null);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (userId) {
      loadBannerData();
    } else {
      setIsLoading(false);
    }
  }, [userId]);

  const loadBannerData = async () => {
    try {
      setIsLoading(true);
      
      if (!userId) {
        console.warn('ContentBanner: Cannot load banner data, userId is undefined');
        setBannerData(null);
        setIsLoading(false);
        return;
      }

      console.log('ContentBanner: Loading banner data for userId:', userId);

      const data = await getUserContentBanner(userId);
      
      if (data && data.is_active) {
        console.log('ContentBanner: Found active banner data');
        setBannerData(data);
        
        // Track impression after a short delay to ensure banner is actually viewed
        setTimeout(() => {
          if (data.id) {
            const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
            const referrer = typeof document !== 'undefined' ? document.referrer : undefined;

            trackContentBannerImpression(data.id, userAgent, referrer)
              .then(success => {
                if (!success) console.warn('Failed to track impression for content banner', data.id);
              })
              .catch(err => console.error('Error tracking content banner impression:', err));
          }
        }, 1000);
      } else {
        setBannerData(null);
      }
    } catch (error) {
      console.error('Error loading content banner data:', error);
      setBannerData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBannerClick = () => {
    if (bannerData?.id) {
      console.log('ContentBanner: Tracking click for banner', bannerData.id);
      const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
      const referrer = typeof document !== 'undefined' ? document.referrer : undefined;

      trackContentBannerClick(bannerData.id, userAgent, referrer)
        .then(success => {
          if (!success) console.warn('Failed to track click for content banner', bannerData.id);
        })
        .catch(err => console.error('Error tracking content banner click:', err));
    } else {
      console.warn('ContentBanner: Cannot track click, banner ID is undefined');
    }
  };

  // Don't render anything if loading or no banner data
  if (isLoading || !bannerData) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`w-full overflow-hidden mb-6 ${className}`}
    >
      {/* Banner Display */}
      <div className="relative w-full max-w-full mx-auto">
        <a 
          href={bannerData.url}
          target="_blank"
          rel="noopener noreferrer sponsored"
          className="block w-full rounded-2xl overflow-hidden transition-all duration-300 focus:shadow-lg focus:shadow-blue-500/20 active:scale-[0.98]"
          title="Sponsored Content"
          onClick={handleBannerClick}
        >
          {/* Smart responsive container for different banner formats */}
          <div className="relative w-full bg-slate-800/60 border border-slate-700/50 rounded-2xl overflow-hidden">
            {/* 
              Smart height system:
              - Mobile: 80px (good for 728x90 banners)
              - Tablet: 90px 
              - Desktop: 100px
              - Large: 110px
              - XL (1280px+): 160px
              - 2XL (1536px+): 180px
              - Ultra-wide: 200px+
              Maintains proper aspect ratio across all screen sizes
            */}
            <div className="w-full h-20 sm:h-[90px] md:h-[100px] lg:h-[110px] xl:h-[160px] 2xl:h-[180px] min-[1920px]:h-[200px] min-[2560px]:h-[220px]">
              {/* Banner Image - Uses object-cover to fill container while maintaining aspect ratio */}
              {!imageError ? (
                <img
                  src={bannerData.img_url}
                  alt="Sponsored Content"
                  className="w-full h-full object-cover"
                  style={{
                    objectPosition: 'center center'
                  }}
                  onError={() => setImageError(true)}
                  loading="lazy"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-slate-800/80">
                  <div className="text-center">
                    <ExternalLink className="h-8 w-8 mx-auto mb-2 text-slate-500" />
                    <span className="text-sm text-slate-400">Sponsored Content</span>
                  </div>
                </div>
              )}

              {/* Sponsored Label */}
              <div className="absolute top-2 right-2 px-2 py-1 bg-black/60 backdrop-blur-sm rounded text-xs font-mono text-slate-300 border border-white/10 z-20">
                <span className="text-blue-400">&lt;</span>
                Sponsored
                <span className="text-blue-400">/&gt;</span>
              </div>
            </div>
          </div>
        </a>
      </div>
    </motion.div>
  );
};

export default ContentBanner;
