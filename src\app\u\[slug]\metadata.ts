import type { Metadata } from 'next';
import { getUserProfileBySlugOrUsername } from '@/app/u/actions';

interface MetadataProps {
  params: Promise<{ slug: string }>;
}

/**
 * Generate dynamic metadata for user profile pages
 * Provides SEO optimization with Open Graph and Twitter Card support
 */
export async function generateMetadata({ params }: MetadataProps): Promise<Metadata> {
  try {
    // Resolve params properly for Next.js App Router
    const { slug } = await params;

    if (!slug) {
      return {
        title: 'Profile Not Found',
        description: 'The requested profile could not be found.',
      };
    }

    // Get profile data
    const profile = await getUserProfileBySlugOrUsername(slug);

    if (!profile) {
      return {
        title: 'Profile Not Found',
        description: 'The requested profile could not be found.',
      };
    }

    const title = `${profile.display_name || profile.username} - CriticalPixel`;
    const description = profile.bio || `Check out ${profile.display_name || profile.username}'s gaming profile on CriticalPixel.`;

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        type: 'profile',
        images: profile.avatar_url ? [profile.avatar_url] : [],
        url: `https://criticalpixel.com/u/${slug}`,
      },
      twitter: {
        card: 'summary',
        title,
        description,
        images: profile.avatar_url ? [profile.avatar_url] : [],
      },
      alternates: {
        canonical: `https://criticalpixel.com/u/${slug}`,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Profile - CriticalPixel',
      description: 'Gaming profile on CriticalPixel.',
    };
  }
}

/**
 * Generate structured data for user profiles
 * Provides rich snippets for search engines
 */
export function generateProfileStructuredData(profile: any) {
  const profileUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://critical-pixel.com'}/u/${profile.slug}`;
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: profile.display_name || profile.username,
    alternateName: profile.username,
    description: profile.bio || `Gaming profile for ${profile.display_name || profile.username}`,
    url: profileUrl,
    image: profile.avatar_url || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://critical-pixel.com'}/imgs/profile.svg`,
    sameAs: [
      profileUrl,
      ...(profile.website ? [profile.website] : [])
    ],
    knowsAbout: [
      'Gaming',
      'Game Reviews',
      ...(profile.preferred_genres || []),
      ...(profile.favorite_consoles || [])
    ],
    memberOf: {
      '@type': 'Organization',
      name: 'CriticalPixel',
      url: process.env.NEXT_PUBLIC_SITE_URL || 'https://critical-pixel.com'
    },
    ...(profile.location && {
      homeLocation: {
        '@type': 'Place',
        name: profile.location
      }
    }),
    ...(profile.created_at && {
      memberSince: new Date(profile.created_at).toISOString()
    })
  };
}
