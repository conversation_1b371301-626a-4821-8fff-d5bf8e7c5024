'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Shield, Search, AlertTriangle, Activity, Database } from 'lucide-react';

/**
 * SECURITY AUDIT LOGS VIEWER
 * Date: June 14, 2025
 * Security Level: SUPER_ADMIN ONLY
 * Purpose: Monitor and analyze security events
 */

interface AuditLogEntry {
  id: string;
  event_type: string;
  user_id: string;
  admin_id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  risk_score: number;
  ip_address: string;
  created_at: string;
  event_data: any;
}

interface AuditStats {
  total: number;
  severityBreakdown: {
    CRITICAL: number;
    HIGH: number;
    MEDIUM: number;
    LOW: number;
  };
  topEvents: Array<{
    event_type: string;
    count: number;
  }>;
}

export default function AuditLogPage() {
  const [logs, setLogs] = useState<AuditLogEntry[]>([]);
  const [stats, setStats] = useState<AuditStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    severity: '',
    eventType: '',
    dateRange: '24h'
  });

  useEffect(() => {
    fetchAuditLogs();
  }, [filters]);

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (filters.severity) params.append('severity', filters.severity);
      if (filters.eventType) params.append('eventType', filters.eventType);
      if (filters.dateRange) params.append('dateRange', filters.dateRange);
      
      const response = await fetch(`/api/admin/audit-logs?${params}`);
      
      if (!response.ok) {
        if (response.status === 403) {
          setError('Access denied. Super admin privileges required.');
        } else {
          setError('Failed to fetch audit logs');
        }
        return;
      }
      
      const data = await response.json();
      setLogs(data.logs || []);
      setStats(data.stats || null);
      
    } catch (error) {
      console.error('Failed to fetch audit logs:', error);
      setError('Failed to fetch audit logs');
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      'LOW': 'bg-green-500 hover:bg-green-600',
      'MEDIUM': 'bg-yellow-500 hover:bg-yellow-600',
      'HIGH': 'bg-orange-500 hover:bg-orange-600',
      'CRITICAL': 'bg-red-500 hover:bg-red-600'
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-500';
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600 font-bold';
    if (score >= 60) return 'text-orange-600 font-semibold';
    if (score >= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (error) {
    return (
      <AdminLayout
        title="Security Audit Logs"
        description="Monitor and analyze security events"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Security', href: '/admin/security' },
          { label: 'Audit Logs' }
        ]}
      >
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Access Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline" 
              className="mt-4"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Security Audit Logs"
      description="Monitor and analyze security events"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Security', href: '/admin/security' },
        { label: 'Audit Logs' }
      ]}
    >
      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Events</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical Events</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.severityBreakdown.CRITICAL}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Risk Events</CardTitle>
              <Shield className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{stats.severityBreakdown.HIGH}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Database Status</CardTitle>
              <Database className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-sm font-bold text-green-600">Active</div>
              <div className="text-xs text-muted-foreground">Logging enabled</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Security Event Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <Select value={filters.severity} onValueChange={(value) => 
              setFilters(prev => ({ ...prev, severity: value }))}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="All Severities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Severities</SelectItem>
                <SelectItem value="CRITICAL">Critical</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>
            
            <Input 
              placeholder="Filter by event type..."
              value={filters.eventType}
              onChange={(e) => setFilters(prev => ({ ...prev, eventType: e.target.value }))}
              className="w-full md:w-60"
            />

            <Select value={filters.dateRange} onValueChange={(value) => 
              setFilters(prev => ({ ...prev, dateRange: value }))}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Last Hour</SelectItem>
                <SelectItem value="24h">Last 24 Hours</SelectItem>
                <SelectItem value="7d">Last 7 Days</SelectItem>
                <SelectItem value="30d">Last 30 Days</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={fetchAuditLogs} variant="outline">
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>Security Event Log</CardTitle>
          <p className="text-sm text-muted-foreground">
            Real-time security event monitoring with risk scoring
          </p>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              <span className="ml-2">Loading audit logs...</span>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No security events found for the current filters.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Event Type</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Risk Score</TableHead>
                    <TableHead>User ID</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log) => (
                    <TableRow key={log.id} className="hover:bg-muted/50">
                      <TableCell className="font-mono text-xs">
                        {new Date(log.created_at).toLocaleString()}
                      </TableCell>
                      <TableCell className="font-mono text-sm max-w-48 truncate">
                        {log.event_type}
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getSeverityColor(log.severity)} text-white border-0`}>
                          {log.severity}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={`font-bold ${getRiskScoreColor(log.risk_score)}`}>
                          {log.risk_score}
                        </span>
                      </TableCell>
                      <TableCell className="font-mono text-xs max-w-32 truncate">
                        {log.user_id || 'N/A'}
                      </TableCell>
                      <TableCell className="font-mono text-sm">
                        {log.ip_address || 'unknown'}
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-blue-600 hover:text-blue-800 text-xs"
                          onClick={() => {
                            // TODO: Open detailed view modal
                            console.log('View details for:', log);
                          }}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </AdminLayout>
  );
} 