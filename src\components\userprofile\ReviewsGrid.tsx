'use client';

import React, { useState } from 'react';
import ReviewCard from './ReviewCard';

interface ReviewsGridProps {
  reviews: any[];
  theme: any;
}

const ReviewsGrid: React.FC<ReviewsGridProps> = ({ reviews, theme }) => {
  const [imageColors, setImageColors] = useState<Record<string, { isDark: boolean; rgb: string }>>({});

  // Calculate grid layout - implementing 2x3 system for odd numbers
  const getGridLayout = (totalReviews: number) => {
    if (totalReviews <= 2) return 'grid-cols-1 md:grid-cols-2';
    if (totalReviews <= 4) return 'grid-cols-1 md:grid-cols-2';
    if (totalReviews <= 6) return 'grid-cols-1 md:grid-cols-2';
    return 'grid-cols-1 md:grid-cols-2';
  };

  // Handle image color updates from individual cards
  const handleImageColorChange = (reviewId: string, colorData: { isDark: boolean; rgb: string }) => {
    setImageColors(prev => ({
      ...prev,
      [reviewId]: colorData
    }));
  };

  return (
    <div className={`grid ${getGridLayout(reviews.length)} gap-6 w-full`}>
      {reviews.map((review, index) => (
        <ReviewCard
          key={review.id}
          review={review}
          theme={theme}
          index={index}
          totalReviews={reviews.length}
          variant="grid"
          onImageColorChange={handleImageColorChange}
        />
      ))}
    </div>
  );
};

ReviewsGrid.displayName = 'ReviewsGrid';

export default ReviewsGrid;