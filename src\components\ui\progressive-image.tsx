'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ProgressiveImageProps {
  src?: string;
  alt: string;
  className?: string;
  placeholder?: string;
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  priority?: boolean;
  style?: React.CSSProperties;
}

const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  alt,
  className,
  placeholder,
  blurDataURL,
  onLoad,
  onError,
  priority = false,
  style
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || blurDataURL || '');
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsError(true);
    onError?.();
  }, [onError]);

  const loadMainImage = useCallback(() => {
    if (!src || src.trim() === '') {
      setIsError(true);
      return;
    }
    
    const img = new Image();
    img.onload = () => {
      setCurrentSrc(src);
      handleLoad();
    };
    img.onerror = handleError;
    img.src = src;
  }, [src, handleLoad, handleError]);

  useEffect(() => {
    // Check if src is valid before proceeding
    if (!src || src.trim() === '') {
      setIsError(true);
      return;
    }

    // If priority is true, load immediately
    if (priority) {
      loadMainImage();
      return;
    }

    // Otherwise, use Intersection Observer for lazy loading
    if (!imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          loadMainImage();
          observerRef.current?.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [loadMainImage, priority, src]);

  if (isError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-gray-800 text-gray-400 text-sm",
          className
        )}
        style={style}
      >
        Failed to load image
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden" style={style}>
      {currentSrc && currentSrc.trim() !== '' ? (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={cn(
            "transition-all duration-500 ease-out",
            !isLoaded && blurDataURL ? "blur-sm scale-105" : "",
            isLoaded ? "blur-0 scale-100" : "",
            className
          )}
          style={{
            filter: !isLoaded && !blurDataURL ? 'blur(10px)' : undefined,
            opacity: 1
          }}
        />
      ) : null}
      
      {/* Loading overlay */}
      {!isLoaded && currentSrc && currentSrc.trim() !== '' && (
        <div className="absolute inset-0 bg-gray-900/20 animate-pulse" />
      )}
      
      {/* Placeholder for when no image is available yet */}
      {(!currentSrc || currentSrc.trim() === '') && (
        <div 
          className={cn(
            "absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-700 animate-pulse",
            className
          )}
        />
      )}
    </div>
  );
};

// Avatar component with progressive loading
interface ProgressiveAvatarProps {
  src?: string;
  alt: string;
  fallbackText: string;
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
}

const ProgressiveAvatar: React.FC<ProgressiveAvatarProps> = ({
  src,
  alt,
  fallbackText,
  className,
  style,
  priority = false
}) => {
  const [hasError, setHasError] = useState(false);

  if (!src || hasError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center text-3xl font-bold text-white",
          className
        )}
        style={style}
      >
        {fallbackText.charAt(0).toUpperCase()}
      </div>
    );
  }

  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      className={className}
      style={style}
      priority={priority}
      onError={() => setHasError(true)}
    />
  );
};

// Banner component with progressive loading
interface ProgressiveBannerProps {
  src?: string;
  alt: string;
  fallbackGradient: string;
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
}

const ProgressiveBanner: React.FC<ProgressiveBannerProps> = ({
  src,
  alt,
  fallbackGradient,
  className,
  style,
  priority = true
}) => {
  const [hasError, setHasError] = useState(false);

  if (!src || hasError) {
    return (
      <div 
        className={cn("w-full h-full", className)}
        style={{
          background: fallbackGradient,
          ...style
        }}
      />
    );
  }

  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      className={cn("w-full h-full object-cover", className)}
      style={style}
      priority={priority}
      onError={() => setHasError(true)}
    />
  );
};

export { ProgressiveImage, ProgressiveAvatar, ProgressiveBanner };