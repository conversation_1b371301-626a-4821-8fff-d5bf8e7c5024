// Admin User Service - Supabase Implementation
// Date: 16/01/2025
// Task: adminSystemImpl002 - Sprint 2 User Management System
// Security-First Implementation with Audit Logging

import { createClient } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/types';
import { UserProfile } from '@/lib/types';

type ProfileRow = Database['public']['Tables']['profiles']['Row'];

// Admin verification function
async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();
    
    // Use the is_admin() security function from database
    const { data, error } = await supabase
      .rpc('is_admin', { user_id: userId });
    
    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }
    
    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Audit logging function
async function logAdminAction(
  adminId: string, 
  action: string, 
  targetUserId?: string, 
  data?: any,
  result?: any
): Promise<void> {
  try {
    const supabase = createClient();
    
    // Log to a simple table or console for now
    // TODO: Create admin_audit_log table in future migration
    console.log('ADMIN AUDIT LOG:', {
      timestamp: new Date().toISOString(),
      admin_id: adminId,
      action,
      target_user_id: targetUserId,
      data,
      result,
      ip_address: 'TODO: Get from request',
      user_agent: 'TODO: Get from request'
    });
    
    // For now, we'll use console logging
    // In production, this should write to an audit table
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

// Input validation schemas
const validateUserUpdateData = (data: any) => {
  const allowedFields = ['display_name', 'bio', 'avatar_url', 'banner_url', 'theme'];
  const validatedData: any = {};
  
  for (const field of allowedFields) {
    if (data[field] !== undefined) {
      validatedData[field] = data[field];
    }
  }
  
  return validatedData;
};

// 1. getUserList - Lista paginada com filtros
export async function getUserList(
  adminId: string,
  page: number = 1,
  limit: number = 50,
  filters?: {
    search?: string;
    role?: string;
    status?: 'active' | 'suspended';
    dateFrom?: string;
    dateTo?: string;
  }
): Promise<{
  users: UserProfile[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(adminId);
  if (!isAuthorized) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const supabase = createClient();
    
    // Build query with filters
    let query = supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        email,
        avatar_url,
        banner_url,
        bio,
        is_admin,
        is_online,
        last_seen,
        level,
        experience,
        review_count,
        suspended,
        created_at,
        updated_at
      `, { count: 'exact' });

    // Apply search filter
    if (filters?.search) {
      query = query.or(`username.ilike.%${filters.search}%,display_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
    }

    // Apply role filter (admin status)
    if (filters?.role === 'admin') {
      query = query.eq('is_admin', true);
    } else if (filters?.role === 'user') {
      query = query.eq('is_admin', false);
    }

    // Apply date filters
    if (filters?.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }
    if (filters?.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    // Convert to UserProfile format
    const users: UserProfile[] = (data || []).map((profile: any) => ({
      uid: profile.id,
      email: profile.email || '',
      displayName: profile.display_name,
      photoURL: profile.avatar_url,
      username: profile.username,
      bio: profile.bio,
      isAdmin: profile.is_admin,
      isOnline: profile.is_online,
      lastSeen: profile.last_seen,
      level: profile.level,
      experience: profile.experience,
      reviewCount: profile.review_count,
      creationTime: profile.created_at,
      lastSignInTime: profile.last_seen, // Using last_seen as proxy
      role: profile.is_admin ? 'Admin' : 'User',
      disabled: profile.suspended || false
    }));

    const total = count || 0;
    const totalPages = Math.ceil(total / limit);

    // Log admin action
    await logAdminAction(adminId, 'getUserList', undefined, { page, limit, filters }, { total, returned: users.length });

    return {
      users,
      total,
      page,
      limit,
      totalPages
    };

  } catch (error) {
    await logAdminAction(adminId, 'getUserList', undefined, { page, limit, filters }, { error: error.message });
    throw error;
  }
}

// 2. searchUsers - Busca por texto com ranking de relevância
export async function searchUsers(
  adminId: string,
  query: string,
  limit: number = 20
): Promise<UserProfile[]> {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(adminId);
  if (!isAuthorized) {
    throw new Error('Unauthorized: Admin access required');
  }

  if (!query.trim()) {
    return [];
  }

  try {
    const supabase = createClient();
    
    // Search with text similarity ranking
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        display_name,
        email,
        avatar_url,
        is_admin,
        created_at
      `)
      .or(`username.ilike.%${query}%,display_name.ilike.%${query}%,email.ilike.%${query}%`)
      .limit(limit);

    if (error) {
      throw new Error(`Search failed: ${error.message}`);
    }

    // Convert to UserProfile format
    const users: UserProfile[] = (data || []).map((profile: any) => ({
      uid: profile.id,
      email: profile.email || '',
      displayName: profile.display_name,
      photoURL: profile.avatar_url,
      username: profile.username,
      isAdmin: profile.is_admin,
      creationTime: profile.created_at,
      role: profile.is_admin ? 'Admin' : 'User',
      disabled: false
    }));

    // Log admin action
    await logAdminAction(adminId, 'searchUsers', undefined, { query, limit }, { found: users.length });

    return users;

  } catch (error) {
    await logAdminAction(adminId, 'searchUsers', undefined, { query, limit }, { error: error.message });
    throw error;
  }
}

// 3. updateUserAsAdmin - Update com privilégios admin
export async function updateUserAsAdmin(
  adminId: string,
  targetUserId: string,
  updateData: {
    display_name?: string;
    bio?: string;
    avatar_url?: string;
    banner_url?: string;
    theme?: string;
  }
): Promise<void> {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(adminId);
  if (!isAuthorized) {
    throw new Error('Unauthorized: Admin access required');
  }

  // 2. Validate input data
  const validatedData = validateUserUpdateData(updateData);
  
  if (Object.keys(validatedData).length === 0) {
    throw new Error('No valid fields to update');
  }

  try {
    const supabase = createClient();
    
    // 3. Execute update in transaction
    const { error } = await supabase
      .from('profiles')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', targetUserId);

    if (error) {
      throw new Error(`Failed to update user: ${error.message}`);
    }

    // 4. Log admin action
    await logAdminAction(adminId, 'updateUserAsAdmin', targetUserId, validatedData, { success: true });

  } catch (error) {
    await logAdminAction(adminId, 'updateUserAsAdmin', targetUserId, validatedData, { error: error.message });
    throw error;
  }
}

// 4. toggleUserSuspension - Suspender/reativar
export async function toggleUserSuspension(
  adminId: string,
  targetUserId: string,
  suspended: boolean,
  reason?: string
): Promise<void> {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(adminId);
  if (!isAuthorized) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const supabase = createClient();

    // 2. Use the secure database function to toggle suspension
    const { data, error } = await supabase.rpc('admin_toggle_user_suspension', {
      p_target_user_id: targetUserId,
      p_suspended: suspended,
      p_reason: reason || (suspended ? 'Admin action' : null)
    });

    if (error) {
      throw new Error(`Failed to toggle user suspension: ${error.message}`);
    }

    // 3. Log successful action
    await logAdminAction(adminId, 'toggleUserSuspension', targetUserId, { suspended, reason }, {
      success: true,
      result: data
    });

  } catch (error: any) {
    await logAdminAction(adminId, 'toggleUserSuspension', targetUserId, { suspended, reason }, { error: error.message });
    throw error;
  }
}

// 5. toggleAdminPrivileges - Grant/revoke admin
export async function toggleAdminPrivileges(
  adminId: string,
  targetUserId: string,
  isAdmin: boolean
): Promise<void> {
  // 1. Verify admin permissions
  const isAuthorized = await verifyAdminPermissions(adminId);
  if (!isAuthorized) {
    throw new Error('Unauthorized: Admin access required');
  }

  // 2. Prevent self-demotion
  if (adminId === targetUserId && !isAdmin) {
    throw new Error('Cannot remove admin privileges from yourself');
  }

  try {
    const supabase = createClient();
    
    // 3. Execute admin privilege update
    const { error } = await supabase
      .from('profiles')
      .update({
        is_admin: isAdmin,
        updated_at: new Date().toISOString()
      })
      .eq('id', targetUserId);

    if (error) {
      throw new Error(`Failed to update admin privileges: ${error.message}`);
    }

    // 4. Log admin action
    await logAdminAction(adminId, 'toggleAdminPrivileges', targetUserId, { isAdmin }, { success: true });

  } catch (error) {
    await logAdminAction(adminId, 'toggleAdminPrivileges', targetUserId, { isAdmin }, { error: error.message });
    throw error;
  }
}

// Export all functions
export {
  verifyAdminPermissions,
  logAdminAction
};
