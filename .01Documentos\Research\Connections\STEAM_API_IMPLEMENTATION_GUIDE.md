# Guia Completo: Implementação da Steam API para Validação de Posse de Jogos

## Índice

1. [Visão Geral](#visão-geral)
2. [Pré-requisitos](#pré-requisitos)
3. [Configuração Inicial](#configuração-inicial)
4. [Arquitetura BFF (Backend-for-Frontend)](#arquitetura-bff)
5. [Implementação da Autenticação Steam](#implementação-da-autenticação-steam)
6. [Criação dos Serviços de API](#criação-dos-serviços-de-api)
7. [Interface de Conexões (Frontend)](#interface-de-conexões-frontend)
8. [Sistema de Validação de Posse](#sistema-de-validação-de-posse)
9. [Badge de Verificação](#badge-de-verificação)
10. [Estrutura do Banco de Dados](#estrutura-do-banco-de-dados)
11. [Considerações de Segurança](#considerações-de-segurança)
12. [Tratamento de Erros](#tratamento-de-erros)
13. [Implementação Passo a Passo](#implementação-passo-a-passo)

## Visão Geral

Este guia implementa um sistema completo de validação de posse de jogos através da Steam API, permitindo que usuários conectem suas contas Steam e tenham suas avaliações marcadas como "Verificadas" quando possuem o jogo que estão avaliando.

### Funcionalidades Principais

- **Conexão com Steam**: Autenticação através do OpenID 2.0 da Steam
- **Sincronização de Biblioteca**: Busca automática dos jogos possuídos pelo usuário
- **Validação de Posse**: Verificação se o usuário possui o jogo que está avaliando
- **Badge de Verificação**: Exibição visual para avaliações verificadas
- **Filtros de Avaliação**: Opção de visualizar apenas avaliações de proprietários verificados

## Pré-requisitos

### Ferramentas e Tecnologias

- Next.js 14+ (App Router)
- TypeScript
- NextAuth.js v4+
- Supabase (ou PostgreSQL)
- Steam Web API Key

### Conhecimentos Necessários

- React/Next.js
- Autenticação OAuth/OpenID
- APIs REST
- TypeScript/JavaScript
- SQL básico

## Configuração Inicial

### 1. Obtenção da Steam Web API Key

1. Acesse [Steam Community Developer](https://steamcommunity.com/dev/apikey)
2. Faça login com sua conta Steam
3. Preencha o domínio da aplicação
4. Aceite os termos de uso
5. Copie sua chave API de 32 caracteres

### 2. Configuração das Variáveis de Ambiente

```env
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=sua_secret_key_aqui_32_chars_minimo

# Steam API
STEAM_API_KEY=sua_steam_api_key_aqui

# Database (Supabase)
DATABASE_URL=sua_database_url
SUPABASE_ANON_KEY=sua_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=sua_supabase_service_role_key
```

### 3. Instalação de Dependências

```bash
npm install next-auth
npm install @hyperplay/next-auth-steam
npm install @supabase/supabase-js
npm install swr
npm install @types/node
```

## Arquitetura BFF

A arquitetura Backend-for-Frontend (BFF) é fundamental para este projeto, proporcionando:

- **Segurança**: Credenciais sensíveis ficam no servidor
- **Abstração**: Interface unificada para diferentes APIs
- **Performance**: Cache server-side para otimização
- **Manutenibilidade**: Lógica centralizada no backend

### Estrutura de Diretórios

```
src/
├── app/
│   ├── api/
│   │   ├── auth/
│   │   │   └── [...nextauth]/
│   │   │       └── route.ts
│   │   ├── steam/
│   │   │   ├── sync/
│   │   │   │   └── route.ts
│   │   │   └── verify/
│   │   │       └── route.ts
│   │   └── verification/
│   │       └── check/
│   │           └── route.ts
│   ├── connections/
│   │   └── page.tsx
│   └── layout.tsx
├── lib/
│   ├── services/
│   │   └── steam.service.ts
│   ├── auth/
│   │   └── providers/
│   │       └── steamProvider.ts
│   └── types/
│       └── steam.ts
└── components/
    ├── connections/
    │   └── SteamConnection.tsx
    └── reviews/
        └── VerificationBadge.tsx
```

## Implementação da Autenticação Steam

### 1. Configuração do Provider Steam

```typescript
// lib/auth/providers/steamProvider.ts
import { NextApiRequest } from "next";
import SteamProvider from "@hyperplay/next-auth-steam";

export const steamProvider = (req: NextApiRequest) => {
  return SteamProvider(req, {
    clientSecret: process.env.STEAM_API_KEY!,
    callbackUrl: `${process.env.NEXTAUTH_URL}/api/auth/callback/steam`,
  });
};
```

### 2. Configuração do NextAuth.js

```typescript
// app/api/auth/[...nextauth]/route.ts
import NextAuth, { AuthOptions } from "next-auth";
import { steamProvider } from "@/lib/auth/providers/steamProvider";
import { NextApiRequest, NextApiResponse } from "next";

const authOptions = (req: NextApiRequest): AuthOptions => ({
  providers: [
    steamProvider(req),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      if (account?.provider === "steam" && profile) {
        token.steam = {
          steamId: profile.steamid,
          personaName: profile.personaname,
          avatar: profile.avatarfull,
          profileUrl: profile.profileurl,
        };
      }
      return token;
    },
    async session({ session, token }) {
      if (token.steam) {
        session.user.steam = token.steam;
      }
      return session;
    },
  },
  pages: {
    signIn: '/connections',
  },
  secret: process.env.NEXTAUTH_SECRET,
});

async function handler(req: NextApiRequest, res: NextApiResponse) {
  return await NextAuth(req, res, authOptions(req));
}

export { handler as GET, handler as POST };
```

### 3. Tipos TypeScript

```typescript
// lib/types/steam.ts
export interface SteamProfile {
  steamId: string;
  personaName: string;
  avatar: string;
  profileUrl: string;
}

export interface SteamGame {
  appid: number;
  name: string;
  playtime_forever: number;
  img_icon_url: string;
  img_logo_url: string;
  playtime_2weeks?: number;
  has_community_visible_stats?: boolean;
}

export interface VerifiedGame {
  platform: 'steam';
  gameId: string;
  gameName: string;
  gameImageUrl: string;
  playtimeMinutes: number;
  lastPlayed: Date | null;
  isOwned: boolean;
  steamAppId: number;
}

// Extensão dos tipos do NextAuth
declare module "next-auth" {
  interface Session {
    user: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      steam?: SteamProfile;
    }
  }

  interface JWT {
    steam?: SteamProfile;
  }
}
```

## Criação dos Serviços de API

### 1. Serviço Steam

```typescript
// lib/services/steam.service.ts
import { VerifiedGame, SteamGame } from '@/lib/types/steam';

const STEAM_API_BASE = 'https://api.steampowered.com';
const STEAM_API_KEY = process.env.STEAM_API_KEY;

export class SteamService {
  static async getOwnedGames(steamId: string): Promise<VerifiedGame[]> {
    try {
      const url = `${STEAM_API_BASE}/IPlayerService/GetOwnedGames/v0001/`;
      const params = new URLSearchParams({
        key: STEAM_API_KEY!,
        steamid: steamId,
        include_appinfo: 'true',
        include_played_free_games: 'true',
        format: 'json'
      });

      const response = await fetch(`${url}?${params}`);
      
      if (!response.ok) {
        throw new Error(`Steam API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.response?.games) {
        // Perfil privado ou erro na API
        throw new Error('Perfil privado ou sem jogos encontrados');
      }

      return this.normalizeGames(data.response.games);
    } catch (error) {
      console.error('Erro ao buscar jogos do Steam:', error);
      throw error;
    }
  }

  static async getPlayerSummary(steamId: string) {
    try {
      const url = `${STEAM_API_BASE}/ISteamUser/GetPlayerSummaries/v0002/`;
      const params = new URLSearchParams({
        key: STEAM_API_KEY!,
        steamids: steamId,
        format: 'json'
      });

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      return data.response?.players?.[0] || null;
    } catch (error) {
      console.error('Erro ao buscar perfil do Steam:', error);
      return null;
    }
  }

  static async checkGameOwnership(steamId: string, appId: string): Promise<boolean> {
    try {
      const games = await this.getOwnedGames(steamId);
      return games.some(game => game.gameId === appId);
    } catch (error) {
      console.error('Erro ao verificar posse do jogo:', error);
      return false;
    }
  }

  private static normalizeGames(steamGames: SteamGame[]): VerifiedGame[] {
    return steamGames.map(game => ({
      platform: 'steam' as const,
      gameId: game.appid.toString(),
      gameName: game.name,
      gameImageUrl: `https://media.steampowered.com/steamcommunity/public/images/apps/${game.appid}/${game.img_icon_url}.jpg`,
      playtimeMinutes: game.playtime_forever,
      lastPlayed: null, // Steam API não fornece esta informação no GetOwnedGames
      isOwned: true,
      steamAppId: game.appid
    }));
  }

  static async getGameInfo(appId: string) {
    try {
      // Usar Steam Store API para informações do jogo
      const response = await fetch(`https://store.steampowered.com/api/appdetails?appids=${appId}`);
      const data = await response.json();
      
      return data[appId]?.data || null;
    } catch (error) {
      console.error('Erro ao buscar informações do jogo:', error);
      return null;
    }
  }
}
```

### 2. API Route para Sincronização

```typescript
// app/api/steam/sync/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { SteamService } from '@/lib/services/steam.service';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.steam?.steamId) {
      return NextResponse.json(
        { error: 'Steam não conectado' },
        { status: 401 }
      );
    }

    const steamId = session.user.steam.steamId;
    
    // Buscar jogos da Steam API
    const games = await SteamService.getOwnedGames(steamId);
    
    // Salvar no banco de dados
    const { error } = await supabase
      .from('verified_games')
      .upsert(
        games.map(game => ({
          user_id: session.user.id,
          platform: 'steam',
          game_id: game.gameId,
          game_name: game.gameName,
          game_image_url: game.gameImageUrl,
          playtime_minutes: game.playtimeMinutes,
          is_owned: true,
          steam_app_id: game.steamAppId,
          last_synced: new Date().toISOString()
        })),
        { 
          onConflict: 'user_id,platform,game_id',
          ignoreDuplicates: false 
        }
      );

    if (error) {
      console.error('Erro ao salvar jogos:', error);
      return NextResponse.json(
        { error: 'Erro ao salvar jogos' },
        { status: 500 }
      );
    }

    // Atualizar status da conexão
    await supabase
      .from('steam_connections')
      .upsert({
        user_id: session.user.id,
        steam_id: steamId,
        persona_name: session.user.steam.personaName,
        avatar_url: session.user.steam.avatar,
        last_sync: new Date().toISOString(),
        sync_status: 'SUCCESS'
      });

    return NextResponse.json({ 
      success: true, 
      gamesCount: games.length 
    });

  } catch (error) {
    console.error('Erro na sincronização:', error);
    
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

### 3. API Route para Verificação

```typescript
// app/api/verification/check/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gameId = searchParams.get('gameId');
    const userId = searchParams.get('userId');

    if (!gameId) {
      return NextResponse.json(
        { error: 'Game ID obrigatório' },
        { status: 400 }
      );
    }

    // Se não foi fornecido userId, usar o da sessão atual
    let targetUserId = userId;
    if (!targetUserId) {
      const session = await getServerSession();
      if (!session?.user?.id) {
        return NextResponse.json(
          { isVerified: false, playtimeMinutes: null }
        );
      }
      targetUserId = session.user.id;
    }

    // Verificar se o usuário possui o jogo
    const { data: verifiedGame, error } = await supabase
      .from('verified_games')
      .select('playtime_minutes, is_owned')
      .eq('user_id', targetUserId)
      .eq('platform', 'steam')
      .eq('game_id', gameId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Erro ao verificar jogo:', error);
      return NextResponse.json(
        { error: 'Erro interno' },
        { status: 500 }
      );
    }

    const isVerified = verifiedGame?.is_owned || false;
    const playtimeMinutes = verifiedGame?.playtime_minutes || null;

    return NextResponse.json({
      isVerified,
      playtimeMinutes,
      playtimeHours: playtimeMinutes ? Math.round(playtimeMinutes / 60) : null
    });

  } catch (error) {
    console.error('Erro na verificação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## Interface de Conexões (Frontend)

### 1. Página de Conexões

```tsx
// app/connections/page.tsx
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import SteamConnection from '@/components/connections/SteamConnection';

export default async function ConnectionsPage() {
  const session = await getServerSession();

  if (!session) {
    redirect('/api/auth/signin');
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Conectar Contas de Gaming</h1>
      
      <div className="space-y-6">
        <SteamConnection />
      </div>
    </div>
  );
}
```

### 2. Componente de Conexão Steam

```tsx
// components/connections/SteamConnection.tsx
'use client';

import { useSession, signIn } from 'next-auth/react';
import { useState } from 'react';
import Image from 'next/image';

export default function SteamConnection() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');

  const handleConnect = () => {
    signIn('steam', { callbackUrl: '/connections' });
  };

  const handleSync = async () => {
    setIsLoading(true);
    setSyncStatus('syncing');

    try {
      const response = await fetch('/api/steam/sync', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        setSyncStatus('success');
      } else {
        setSyncStatus('error');
      }
    } catch (error) {
      console.error('Erro na sincronização:', error);
      setSyncStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const isConnected = !!session?.user?.steam;

  return (
    <div className="border rounded-lg p-6 bg-white shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 relative">
            <Image
              src="/icons/gaming/steam-svgrepo-com.svg"
              alt="Steam"
              fill
              className="object-contain"
            />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold">Steam</h3>
            {isConnected ? (
              <div className="text-sm text-gray-600">
                <p>Conectado como <strong>{session.user.steam.personaName}</strong></p>
                <p className="text-xs text-gray-500">Steam ID: {session.user.steam.steamId}</p>
              </div>
            ) : (
              <p className="text-sm text-gray-600">
                Conecte sua conta Steam para verificar a posse de jogos
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {isConnected ? (
            <>
              <button
                onClick={handleSync}
                disabled={isLoading}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isLoading ? 'Sincronizando...' : 'Sincronizar Biblioteca'}
              </button>
              
              <div className="w-3 h-3 rounded-full bg-green-500" title="Conectado" />
            </>
          ) : (
            <>
              <button
                onClick={handleConnect}
                className="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 transition-colors"
              >
                Conectar Steam
              </button>
              
              <div className="w-3 h-3 rounded-full bg-gray-400" title="Desconectado" />
            </>
          )}
        </div>
      </div>

      {/* Status de Sincronização */}
      {syncStatus !== 'idle' && (
        <div className="mt-4">
          {syncStatus === 'syncing' && (
            <div className="text-sm text-blue-600 flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Sincronizando biblioteca Steam...
            </div>
          )}
          
          {syncStatus === 'success' && (
            <div className="text-sm text-green-600 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Biblioteca sincronizada com sucesso!
            </div>
          )}
          
          {syncStatus === 'error' && (
            <div className="text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              Erro na sincronização. Tente novamente.
            </div>
          )}
        </div>
      )}

      {/* Dicas de Privacidade */}
      {isConnected && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <div className="text-sm text-amber-800">
            <strong>Dica:</strong> Para sincronizar sua biblioteca, certifique-se de que seu perfil Steam 
            e detalhes de jogos estejam configurados como "Público" nas configurações de privacidade.
          </div>
        </div>
      )}
    </div>
  );
}
```

## Sistema de Validação de Posse

### 1. Hook para Verificação

```tsx
// hooks/useGameVerification.ts
import useSWR from 'swr';

interface VerificationResult {
  isVerified: boolean;
  playtimeMinutes: number | null;
  playtimeHours: number | null;
}

const fetcher = (url: string) => fetch(url).then(res => res.json());

export function useGameVerification(gameId: string, userId?: string) {
  const params = new URLSearchParams({ gameId });
  if (userId) params.set('userId', userId);
  
  const { data, error, isLoading } = useSWR<VerificationResult>(
    gameId ? `/api/verification/check?${params}` : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    }
  );

  return {
    isVerified: data?.isVerified || false,
    playtimeMinutes: data?.playtimeMinutes || null,
    playtimeHours: data?.playtimeHours || null,
    isLoading,
    error
  };
}
```

## Badge de Verificação

### 1. Componente de Badge

```tsx
// components/reviews/VerificationBadge.tsx
'use client';

import { useGameVerification } from '@/hooks/useGameVerification';

interface VerificationBadgeProps {
  gameId: string;
  userId?: string;
  className?: string;
}

export default function VerificationBadge({ 
  gameId, 
  userId, 
  className = '' 
}: VerificationBadgeProps) {
  const { isVerified, playtimeHours, isLoading } = useGameVerification(gameId, userId);

  if (isLoading) {
    return (
      <div className={`inline-flex items-center ${className}`}>
        <div className="animate-pulse bg-gray-200 h-6 w-20 rounded"></div>
      </div>
    );
  }

  if (!isVerified) {
    return null;
  }

  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path 
            fillRule="evenodd" 
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
            clipRule="evenodd" 
          />
        </svg>
        <span>Proprietário Verificado</span>
      </div>
      
      {playtimeHours && playtimeHours > 0 && (
        <span className="text-xs text-gray-600">
          {playtimeHours}h jogadas
        </span>
      )}
    </div>
  );
}
```

### 2. Integração em Reviews

```tsx
// components/reviews/ReviewCard.tsx
import VerificationBadge from './VerificationBadge';

interface ReviewCardProps {
  review: {
    id: string;
    content: string;
    rating: number;
    gameId: string;
    userId: string;
    user: {
      name: string;
      avatar: string;
    };
    createdAt: string;
  };
}

export default function ReviewCard({ review }: ReviewCardProps) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <img 
            src={review.user.avatar} 
            alt={review.user.name}
            className="w-10 h-10 rounded-full"
          />
          <div>
            <h4 className="font-medium">{review.user.name}</h4>
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-4 h-4 ${
                      i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <VerificationBadge 
                gameId={review.gameId}
                userId={review.userId}
              />
            </div>
          </div>
        </div>
        
        <span className="text-sm text-gray-500">
          {new Date(review.createdAt).toLocaleDateString('pt-BR')}
        </span>
      </div>
      
      <p className="text-gray-700 leading-relaxed">{review.content}</p>
    </div>
  );
}
```

## Estrutura do Banco de Dados

### 1. Tabelas Supabase

```sql
-- Conexões Steam dos usuários
CREATE TABLE steam_connections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  steam_id VARCHAR(20) NOT NULL,
  persona_name VARCHAR(255),
  avatar_url TEXT,
  profile_url TEXT,
  last_sync TIMESTAMPTZ,
  sync_status VARCHAR(20) DEFAULT 'PENDING',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id),
  UNIQUE(steam_id)
);

-- Jogos verificados dos usuários
CREATE TABLE verified_games (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform VARCHAR(20) NOT NULL DEFAULT 'steam',
  game_id VARCHAR(50) NOT NULL,
  game_name VARCHAR(255) NOT NULL,
  game_image_url TEXT,
  playtime_minutes INTEGER DEFAULT 0,
  last_played TIMESTAMPTZ,
  is_owned BOOLEAN DEFAULT true,
  steam_app_id INTEGER,
  last_synced TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, platform, game_id)
);

-- Índices para performance
CREATE INDEX idx_verified_games_user_game ON verified_games(user_id, game_id);
CREATE INDEX idx_verified_games_steam_app ON verified_games(steam_app_id);
CREATE INDEX idx_steam_connections_steam_id ON steam_connections(steam_id);

-- RLS (Row Level Security)
ALTER TABLE steam_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE verified_games ENABLE ROW LEVEL SECURITY;

-- Políticas RLS
CREATE POLICY "Users can view own steam connections" ON steam_connections
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own steam connections" ON steam_connections
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own steam connections" ON steam_connections
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own verified games" ON verified_games
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own verified games" ON verified_games
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Public can view verified games for verification" ON verified_games
  FOR SELECT USING (true);
```

### 2. Triggers para Updated_at

```sql
-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para steam_connections
CREATE TRIGGER update_steam_connections_updated_at 
  BEFORE UPDATE ON steam_connections 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Considerações de Segurança

### 1. Variáveis de Ambiente

```env
# Nunca commitar estas chaves
STEAM_API_KEY=sua_chave_steam_aqui
NEXTAUTH_SECRET=chave_super_secreta_32_chars_minimo
SUPABASE_SERVICE_ROLE_KEY=chave_service_role_supabase
```

### 2. Validação de Entrada

```typescript
// lib/validation/steam.ts
import { z } from 'zod';

export const steamIdSchema = z.string()
  .regex(/^\d{17}$/, 'Steam ID deve ter 17 dígitos');

export const gameIdSchema = z.string()
  .regex(/^\d+$/, 'Game ID deve ser numérico');

export const syncRequestSchema = z.object({
  force: z.boolean().optional().default(false)
});
```

### 3. Rate Limiting

```typescript
// lib/rate-limit.ts
import { NextRequest } from 'next/server';

const rateLimit = new Map();

export function checkRateLimit(req: NextRequest, limit = 10, window = 60000) {
  const ip = req.ip || 'unknown';
  const now = Date.now();
  const windowStart = now - window;

  const requests = rateLimit.get(ip) || [];
  const recentRequests = requests.filter((time: number) => time > windowStart);

  if (recentRequests.length >= limit) {
    return false;
  }

  recentRequests.push(now);
  rateLimit.set(ip, recentRequests);
  return true;
}
```

## Tratamento de Erros

### 1. Classes de Erro Customizadas

```typescript
// lib/errors/steam-errors.ts
export class SteamAPIError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public steamError?: string
  ) {
    super(message);
    this.name = 'SteamAPIError';
  }
}

export class PrivateProfileError extends SteamAPIError {
  constructor() {
    super(
      'Perfil Steam privado. Configure como público para sincronizar.',
      403,
      'PRIVATE_PROFILE'
    );
  }
}

export class NoGamesFoundError extends SteamAPIError {
  constructor() {
    super(
      'Nenhum jogo encontrado na conta Steam.',
      404,
      'NO_GAMES'
    );
  }
}
```

### 2. Middleware de Tratamento de Erros

```typescript
// lib/middleware/error-handler.ts
import { NextResponse } from 'next/server';
import { SteamAPIError } from '@/lib/errors/steam-errors';

export function handleError(error: unknown) {
  console.error('API Error:', error);

  if (error instanceof SteamAPIError) {
    return NextResponse.json(
      { 
        error: error.message,
        code: error.steamError,
        statusCode: error.statusCode
      },
      { status: error.statusCode }
    );
  }

  return NextResponse.json(
    { error: 'Erro interno do servidor' },
    { status: 500 }
  );
}
```

## Implementação Passo a Passo

### Passo 1: Configuração Inicial

1. **Instalar dependências**:
```bash
npm install next-auth @hyperplay/next-auth-steam @supabase/supabase-js swr
```

2. **Configurar variáveis de ambiente** (.env.local)

3. **Obter Steam API Key** em https://steamcommunity.com/dev/apikey

### Passo 2: Configuração da Autenticação

1. Criar `app/api/auth/[...nextauth]/route.ts`
2. Configurar provider Steam
3. Definir callbacks para JWT e session

### Passo 3: Configuração do Banco de Dados

1. Criar tabelas no Supabase
2. Configurar RLS e políticas
3. Criar índices para performance

### Passo 4: Serviços de Backend

1. Implementar `SteamService`
2. Criar API routes para sync e verificação
3. Adicionar tratamento de erros

### Passo 5: Interface de Frontend

1. Criar página de conexões
2. Implementar componente `SteamConnection`
3. Adicionar feedback visual

### Passo 6: Sistema de Verificação

1. Criar hook `useGameVerification`
2. Implementar `VerificationBadge`
3. Integrar nas páginas de reviews

### Passo 7: Testes e Refinamentos

1. Testar fluxo completo
2. Adicionar loading states
3. Implementar cache e otimizações

## Conclusão

Este guia fornece uma implementação completa de verificação de posse de jogos através da Steam API. O sistema é seguro, escalável e oferece uma excelente experiência do usuário, permitindo que os usuários demonstrem que realmente possuem os jogos que estão avaliando.

### Próximos Passos

- Implementar cache Redis para melhor performance
- Adicionar suporte para DLCs (usando endpoints não oficiais)
- Criar dashboard de estatísticas
- Implementar notificações de sincronização
- Adicionar mais plataformas (Xbox, PlayStation)

### Recursos Adicionais

- [Documentação Steam Web API](https://developer.valvesoftware.com/wiki/Steam_Web_API)
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Supabase Documentation](https://supabase.com/docs)
- [@hyperplay/next-auth-steam](https://www.npmjs.com/package/@hyperplay/next-auth-steam) 