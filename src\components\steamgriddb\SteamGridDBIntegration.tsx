'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { SteamGridDBSearch } from './SteamGridDBSearch';
import { SteamGridDBArtworkGallery } from './SteamGridDBArtworkGallery';
import { SteamGridDBGame, SteamGridDBArtwork } from '@/hooks/useSteamGridDB';
import { Image, Palette, ExternalLink } from 'lucide-react';

interface SteamGridDBIntegrationProps {
  gameName?: string;
  onArtworkSelect?: (artwork: SteamGridDBArtwork, type: 'grid' | 'hero' | 'logo' | 'icon') => void;
  selectedArtwork?: SteamGridDBArtwork | null;
  className?: string;
}

export function SteamGridDBIntegration({ 
  gameName, 
  onArtworkSelect, 
  selectedArtwork,
  className 
}: SteamGridDBIntegrationProps) {
  const [selectedGame, setSelectedGame] = useState<SteamGridDBGame | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleGameSelect = (game: SteamGridDBGame) => {
    setSelectedGame(game);
  };

  const handleArtworkSelect = (artwork: SteamGridDBArtwork, type: 'grid' | 'hero' | 'logo' | 'icon') => {
    onArtworkSelect?.(artwork, type);
    setIsDialogOpen(false);
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            SteamGridDB Integration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedArtwork ? (
            <div className="flex items-center space-x-4 p-4 border rounded-lg bg-accent/50">
              <div className="relative w-24 h-16 rounded overflow-hidden">
                <img
                  src={selectedArtwork.thumb || selectedArtwork.url}
                  alt="Selected artwork"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Selected Artwork</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline">{selectedArtwork.style}</Badge>
                  <span className="text-sm text-muted-foreground">
                    {selectedArtwork.width}×{selectedArtwork.height}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    by {selectedArtwork.author.name}
                  </span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(selectedArtwork.url, '_blank')}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-4 p-4 border rounded-lg border-dashed">
              <div className="w-24 h-16 bg-muted rounded flex items-center justify-center">
                <Image className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-muted-foreground">No artwork selected</h4>
                <p className="text-sm text-muted-foreground">
                  Choose custom artwork from SteamGridDB
                </p>
              </div>
            </div>
          )}

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full" variant="outline">
                <Palette className="h-4 w-4 mr-2" />
                Browse SteamGridDB Artwork
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>SteamGridDB Artwork Browser</DialogTitle>
              </DialogHeader>
              
              <div className="space-y-6">
                <SteamGridDBSearch 
                  onGameSelect={handleGameSelect}
                />
                
                <SteamGridDBArtworkGallery 
                  selectedGame={selectedGame}
                  onArtworkSelect={handleArtworkSelect}
                />
              </div>
            </DialogContent>
          </Dialog>

          <div className="flex gap-2">
            {gameName && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => {
                  setIsDialogOpen(true);
                }}
              >
                Search for "{gameName}"
              </Button>
            )}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => window.open('https://www.steamgriddb.com', '_blank')}
            >
              Visit SteamGridDB
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            <p>
              SteamGridDB provides custom artwork for games including grid covers, 
              heroes, logos, and icons created by the community.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}