# Step 4: Advanced Features & Security
## Comment System Implementation Guide - Phase 4

**Date:** 2025-01-20  
**Task:** Advanced Security, Spam Prevention & Features  
**Priority:** CRITICAL  
**Estimated Time:** 10-12 hours  

---

## 🎯 Overview & Objectives

This step implements advanced security measures, spam prevention, content filtering, and enhanced features to create a robust, production-ready comment system.

### Key Objectives:
- [ ] Implement comprehensive spam detection
- [ ] Add rate limiting and abuse prevention
- [ ] Create content filtering system
- [ ] Add real-time notifications
- [ ] Implement advanced moderation tools
- [ ] Add comment analytics and reporting
- [ ] Enhance security with IP tracking and monitoring

---

## 📋 Prerequisites

- [ ] Steps 1-3 completed successfully
- [ ] Comment system fully functional
- [ ] Moderation dashboard working
- [ ] Understanding of security best practices
- [ ] Access to Supabase Edge Functions (optional)

---

## 🛡️ Security Implementation

### 1. Rate Limiting System
```typescript
// src/lib/security/rateLimiting.ts
import { createClient } from '@/lib/supabase/client';

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  identifier: string; // user_id or ip_address
}

export class CommentRateLimiter {
  private supabase = createClient();

  async checkRateLimit(config: RateLimitConfig): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  }> {
    const windowStart = new Date(Date.now() - config.windowMs);
    
    // Count recent requests
    const { data: recentRequests, error } = await this.supabase
      .from('comment_rate_limits')
      .select('id')
      .eq('identifier', config.identifier)
      .gte('created_at', windowStart.toISOString());

    if (error) throw error;

    const requestCount = recentRequests?.length || 0;
    const remaining = Math.max(0, config.maxRequests - requestCount);
    const allowed = requestCount < config.maxRequests;

    if (allowed) {
      // Record this request
      await this.supabase
        .from('comment_rate_limits')
        .insert({
          identifier: config.identifier,
          created_at: new Date().toISOString(),
        });
    }

    return {
      allowed,
      remaining,
      resetTime: new Date(Date.now() + config.windowMs),
    };
  }

  async cleanupOldRecords(): Promise<void> {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    await this.supabase
      .from('comment_rate_limits')
      .delete()
      .lt('created_at', cutoff.toISOString());
  }
}

// Rate limiting table
/*
CREATE TABLE IF NOT EXISTS comment_rate_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  identifier TEXT NOT NULL, -- user_id or ip_address
  action_type TEXT DEFAULT 'comment_create',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE INDEX idx_comment_rate_limits_identifier ON comment_rate_limits(identifier, created_at);
*/
```

### 2. Spam Detection System
```typescript
// src/lib/security/spamDetection.ts
export interface SpamCheckResult {
  isSpam: boolean;
  confidence: number;
  reasons: string[];
  action: 'allow' | 'flag' | 'block';
}

export class SpamDetector {
  private bannedWords = [
    // Common spam words - load from database
    'viagra', 'casino', 'lottery', 'winner', 'congratulations',
    'click here', 'free money', 'make money fast', 'work from home'
  ];

  private suspiciousPatterns = [
    /https?:\/\/[^\s]+/gi, // URLs
    /\b\d{10,}\b/g, // Long numbers (phone numbers)
    /[A-Z]{5,}/g, // Excessive caps
    /(.)\1{4,}/g, // Repeated characters
  ];

  async checkSpam(content: string, authorId?: string, ip?: string): Promise<SpamCheckResult> {
    const reasons: string[] = [];
    let confidence = 0;

    // 1. Banned words check
    const lowerContent = content.toLowerCase();
    const foundBannedWords = this.bannedWords.filter(word => 
      lowerContent.includes(word.toLowerCase())
    );
    
    if (foundBannedWords.length > 0) {
      reasons.push(`Contains banned words: ${foundBannedWords.join(', ')}`);
      confidence += foundBannedWords.length * 0.3;
    }

    // 2. Suspicious patterns
    this.suspiciousPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        reasons.push(`Suspicious pattern detected: ${pattern.source}`);
        confidence += matches.length * 0.2;
      }
    });

    // 3. Length checks
    if (content.length < 10) {
      reasons.push('Content too short');
      confidence += 0.1;
    }

    if (content.length > 2000) {
      reasons.push('Content unusually long');
      confidence += 0.2;
    }

    // 4. Repetitive content check
    const words = content.split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = 1 - (uniqueWords.size / words.length);
    
    if (repetitionRatio > 0.7) {
      reasons.push('Highly repetitive content');
      confidence += 0.4;
    }

    // 5. Check user history (if available)
    if (authorId) {
      const userSpamScore = await this.getUserSpamScore(authorId);
      if (userSpamScore > 0.5) {
        reasons.push('User has high spam score');
        confidence += userSpamScore * 0.3;
      }
    }

    // 6. IP reputation check
    if (ip) {
      const ipReputation = await this.getIPReputation(ip);
      if (ipReputation < 0.3) {
        reasons.push('IP has poor reputation');
        confidence += 0.3;
      }
    }

    // Determine action based on confidence
    let action: 'allow' | 'flag' | 'block' = 'allow';
    if (confidence >= 0.8) {
      action = 'block';
    } else if (confidence >= 0.4) {
      action = 'flag';
    }

    return {
      isSpam: confidence >= 0.4,
      confidence: Math.min(confidence, 1),
      reasons,
      action,
    };
  }

  private async getUserSpamScore(userId: string): Promise<number> {
    // Calculate based on user's comment history
    const supabase = createClient();
    
    const { data } = await supabase
      .from('comments')
      .select('flag_count, is_deleted')
      .eq('author_id', userId)
      .limit(50);

    if (!data || data.length === 0) return 0;

    const totalComments = data.length;
    const flaggedComments = data.filter(c => c.flag_count > 0).length;
    const deletedComments = data.filter(c => c.is_deleted).length;

    return (flaggedComments + deletedComments * 2) / totalComments;
  }

  private async getIPReputation(ip: string): Promise<number> {
    // Simple IP reputation based on recent activity
    // In production, integrate with external IP reputation services
    const supabase = createClient();
    
    const { data } = await supabase
      .from('comment_audit_log')
      .select('action_type')
      .eq('ip_address', ip)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    if (!data || data.length === 0) return 0.5; // Neutral for new IPs

    const totalActions = data.length;
    const negativeActions = data.filter(a => 
      ['delete', 'flag', 'reject'].includes(a.action_type)
    ).length;

    return Math.max(0, 1 - (negativeActions / totalActions));
  }
}
```

### 3. Content Filtering System
```typescript
// src/lib/security/contentFilter.ts
export interface ContentFilterResult {
  isAllowed: boolean;
  filteredContent: string;
  warnings: string[];
  severity: 'low' | 'medium' | 'high';
}

export class ContentFilter {
  private profanityWords: string[] = [
    // Load from database or external service
  ];

  private toxicityPatterns = [
    /\b(hate|kill|die|stupid|idiot)\b/gi,
    // Add more patterns based on your community guidelines
  ];

  async filterContent(content: string): Promise<ContentFilterResult> {
    let filteredContent = content;
    const warnings: string[] = [];
    let severity: 'low' | 'medium' | 'high' = 'low';

    // 1. Profanity filtering
    this.profanityWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      if (regex.test(filteredContent)) {
        filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
        warnings.push('Profanity detected and filtered');
        severity = 'medium';
      }
    });

    // 2. Toxicity detection
    this.toxicityPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        warnings.push('Potentially toxic language detected');
        severity = 'high';
      }
    });

    // 3. Personal information detection
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g;
    
    if (emailPattern.test(content)) {
      filteredContent = filteredContent.replace(emailPattern, '[email protected]');
      warnings.push('Email address detected and masked');
    }

    if (phonePattern.test(content)) {
      filteredContent = filteredContent.replace(phonePattern, '[phone number]');
      warnings.push('Phone number detected and masked');
    }

    // 4. URL filtering (optional - allow or restrict)
    const urlPattern = /https?:\/\/[^\s]+/gi;
    const urls = content.match(urlPattern);
    if (urls) {
      // Check if URLs are from allowed domains
      const allowedDomains = ['youtube.com', 'twitch.tv', 'steam.com'];
      const suspiciousUrls = urls.filter(url => {
        return !allowedDomains.some(domain => url.includes(domain));
      });

      if (suspiciousUrls.length > 0) {
        warnings.push('Suspicious URLs detected');
        severity = 'medium';
      }
    }

    return {
      isAllowed: severity !== 'high',
      filteredContent,
      warnings,
      severity,
    };
  }
}
```

### 4. Advanced Moderation Tools
```typescript
// src/lib/moderation/advancedTools.ts
export class AdvancedModerationTools {
  private supabase = createClient();

  // Bulk moderation actions
  async bulkModerateComments(
    commentIds: string[],
    action: 'approve' | 'reject' | 'delete',
    moderatorId: string,
    reason?: string
  ): Promise<void> {
    const updates: any = {
      moderated_by: moderatorId,
      moderated_at: new Date().toISOString(),
    };

    switch (action) {
      case 'approve':
        updates.is_approved = true;
        break;
      case 'reject':
        updates.is_approved = false;
        break;
      case 'delete':
        updates.is_deleted = true;
        break;
    }

    const { error } = await this.supabase
      .from('comments')
      .update(updates)
      .in('id', commentIds);

    if (error) throw error;

    // Log bulk action
    await Promise.all(commentIds.map(id =>
      this.supabase.rpc('log_comment_action', {
        p_comment_id: id,
        p_action_type: action,
        p_reason: reason,
      })
    ));
  }

  // Auto-moderation rules
  async applyAutoModerationRules(reviewId: string): Promise<void> {
    const { data: settings } = await this.supabase
      .from('comment_moderation_settings')
      .select('*')
      .eq('review_id', reviewId)
      .single();

    if (!settings) return;

    // Get pending comments
    const { data: comments } = await this.supabase
      .from('comments')
      .select('*')
      .eq('review_id', reviewId)
      .eq('is_approved', false)
      .eq('is_deleted', false);

    if (!comments) return;

    const spamDetector = new SpamDetector();
    const contentFilter = new ContentFilter();

    for (const comment of comments) {
      // Check spam
      const spamResult = await spamDetector.checkSpam(comment.content, comment.author_id);
      
      // Check content
      const filterResult = await contentFilter.filterContent(comment.content);

      // Apply auto-moderation
      if (spamResult.action === 'block' || filterResult.severity === 'high') {
        await this.supabase
          .from('comments')
          .update({
            is_deleted: true,
            moderated_by: 'system',
            moderated_at: new Date().toISOString(),
            moderation_notes: `Auto-deleted: ${spamResult.reasons.join(', ')}`,
          })
          .eq('id', comment.id);
      } else if (spamResult.action === 'flag' || filterResult.severity === 'medium') {
        await this.supabase
          .from('comments')
          .update({
            flag_count: comment.flag_count + 1,
            moderation_notes: `Auto-flagged: ${spamResult.reasons.join(', ')}`,
          })
          .eq('id', comment.id);
      } else if (settings.auto_approve) {
        await this.supabase
          .from('comments')
          .update({
            is_approved: true,
            content: filterResult.filteredContent, // Use filtered content
          })
          .eq('id', comment.id);
      }
    }
  }

  // Comment similarity detection (duplicate detection)
  async detectDuplicateComments(content: string, reviewId: string): Promise<{
    isDuplicate: boolean;
    similarComments: string[];
  }> {
    const { data: recentComments } = await this.supabase
      .from('comments')
      .select('id, content')
      .eq('review_id', reviewId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (!recentComments) return { isDuplicate: false, similarComments: [] };

    const similarComments: string[] = [];
    const normalizedContent = content.toLowerCase().trim();

    for (const comment of recentComments) {
      const normalizedExisting = comment.content.toLowerCase().trim();
      const similarity = this.calculateSimilarity(normalizedContent, normalizedExisting);
      
      if (similarity > 0.8) {
        similarComments.push(comment.id);
      }
    }

    return {
      isDuplicate: similarComments.length > 0,
      similarComments,
    };
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Levenshtein distance-based similarity
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    );

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }
}
```

### 5. Real-time Notifications
```typescript
// src/lib/notifications/commentNotifications.ts
export class CommentNotificationService {
  private supabase = createClient();

  async sendCommentNotification(
    type: 'new_comment' | 'comment_reply' | 'comment_flagged' | 'comment_approved',
    data: {
      reviewId: string;
      reviewTitle: string;
      reviewAuthorId: string;
      commentId: string;
      commentAuthorId: string;
      commentContent: string;
      parentCommentId?: string;
    }
  ): Promise<void> {
    switch (type) {
      case 'new_comment':
        await this.notifyReviewOwner(data);
        break;
      case 'comment_reply':
        await this.notifyCommentAuthor(data);
        break;
      case 'comment_flagged':
        await this.notifyModerators(data);
        break;
      case 'comment_approved':
        await this.notifyCommentAuthor(data);
        break;
    }
  }

  private async notifyReviewOwner(data: any): Promise<void> {
    // Check if review owner wants notifications
    const { data: settings } = await this.supabase
      .from('user_notification_settings')
      .select('comment_notifications')
      .eq('user_id', data.reviewAuthorId)
      .single();

    if (!settings?.comment_notifications) return;

    // Create notification
    await this.supabase
      .from('notifications')
      .insert({
        user_id: data.reviewAuthorId,
        type: 'new_comment',
        title: 'New comment on your review',
        message: `${data.commentAuthorId} commented on "${data.reviewTitle}"`,
        data: {
          review_id: data.reviewId,
          comment_id: data.commentId,
        },
      });

    // Send real-time notification via Supabase realtime
    await this.supabase
      .channel('notifications')
      .send({
        type: 'broadcast',
        event: 'new_notification',
        payload: {
          user_id: data.reviewAuthorId,
          type: 'new_comment',
        },
      });
  }

  private async notifyCommentAuthor(data: any): Promise<void> {
    if (data.parentCommentId) {
      // Get parent comment author
      const { data: parentComment } = await this.supabase
        .from('comments')
        .select('author_id')
        .eq('id', data.parentCommentId)
        .single();

      if (parentComment && parentComment.author_id !== data.commentAuthorId) {
        await this.supabase
          .from('notifications')
          .insert({
            user_id: parentComment.author_id,
            type: 'comment_reply',
            title: 'Someone replied to your comment',
            message: `New reply on "${data.reviewTitle}"`,
            data: {
              review_id: data.reviewId,
              comment_id: data.commentId,
              parent_comment_id: data.parentCommentId,
            },
          });
      }
    }
  }

  private async notifyModerators(data: any): Promise<void> {
    // Notify review owner about flagged comment
    await this.supabase
      .from('notifications')
      .insert({
        user_id: data.reviewAuthorId,
        type: 'comment_flagged',
        title: 'Comment flagged for review',
        message: `A comment on "${data.reviewTitle}" has been flagged`,
        data: {
          review_id: data.reviewId,
          comment_id: data.commentId,
        },
      });
  }
}
```

---

## ✅ Implementation Checklist

### Security Features
- [ ] Implement rate limiting system
- [ ] Create spam detection algorithms
- [ ] Add content filtering capabilities
- [ ] Set up IP tracking and monitoring
- [ ] Create security audit logging
- [ ] Implement CAPTCHA for suspicious users

### Advanced Moderation
- [ ] Build bulk moderation tools
- [ ] Create auto-moderation rules
- [ ] Implement duplicate detection
- [ ] Add advanced filtering options
- [ ] Create moderation analytics
- [ ] Set up escalation workflows

### Notifications & Real-time
- [ ] Implement comment notifications
- [ ] Set up real-time updates
- [ ] Create notification preferences
- [ ] Add email notification system
- [ ] Implement push notifications (optional)

### Performance & Monitoring
- [ ] Add performance monitoring
- [ ] Implement caching strategies
- [ ] Create health check endpoints
- [ ] Set up error tracking
- [ ] Add usage analytics

---

## 📝 AI Implementation Prompts

### Prompt 1: Security Implementation
```
Implement the comprehensive security system for the comment platform including rate limiting, spam detection, and content filtering. Focus on creating robust protection against abuse while maintaining good user experience for legitimate users.
```

### Prompt 2: Advanced Moderation Tools
```
Create advanced moderation tools including bulk actions, auto-moderation rules, and duplicate detection. Ensure these tools integrate well with the existing moderation dashboard and provide clear feedback to moderators.
```

### Prompt 3: Notifications and Real-time Features
```
Implement the notification system and real-time updates for the comment system. Set up proper notification preferences and ensure real-time updates work smoothly without overwhelming the system.
```

---

## 🔄 Next Steps

Upon completion:
1. Proceed to **Step 5: Testing, Optimization & Documentation**
2. Comprehensive testing of all features
3. Performance optimization
4. Final documentation and deployment preparation

---

**⚠️ IMPORTANT NOTES FOR AI:**
- Test security features thoroughly
- Monitor performance impact of security checks
- Ensure false positive rates are acceptable
- Document all security configurations
- Test with various attack scenarios
