// Spam Detection System
// Date: 21/06/2025
// Task: Advanced Features & Security Implementation

import { createClient } from '@/lib/supabase/client';

export interface SpamCheckResult {
  isSpam: boolean;
  confidence: number;
  reasons: string[];
  action: 'allow' | 'flag' | 'block';
}

export class SpamDetector {
  private supabase = createClient();

  private bannedWords = [
    // Common spam words
    'viagra', 'casino', 'lottery', 'winner', 'congratulations',
    'click here', 'free money', 'make money fast', 'work from home',
    'buy now', 'limited time', 'act now', 'urgent', 'guaranteed',
    'no risk', 'amazing deal', 'incredible offer', 'once in lifetime',
    // Gaming-specific spam
    'free vbucks', 'free robux', 'game hack', 'cheat engine',
    'unlimited coins', 'free gems', 'account generator'
  ];

  private suspiciousPatterns = [
    /https?:\/\/[^\s]+/gi, // URLs
    /\b\d{10,}\b/g, // Long numbers (phone numbers)
    /[A-Z]{5,}/g, // Excessive caps
    /(.)\1{4,}/g, // Repeated characters
    /\b(buy|sell|cheap|discount)\b.*\b(now|today|urgent)\b/gi, // Sales language
    /\b(click|visit|check)\b.*\b(link|site|website)\b/gi, // Link promotion
  ];

  async checkSpam(content: string, authorId?: string, ip?: string): Promise<SpamCheckResult> {
    const reasons: string[] = [];
    let confidence = 0;

    // 1. Banned words check
    const lowerContent = content.toLowerCase();
    const foundBannedWords = this.bannedWords.filter(word => 
      lowerContent.includes(word.toLowerCase())
    );
    
    if (foundBannedWords.length > 0) {
      reasons.push(`Contains banned words: ${foundBannedWords.join(', ')}`);
      confidence += foundBannedWords.length * 0.3;
    }

    // 2. Suspicious patterns
    this.suspiciousPatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        const patternNames = [
          'URLs', 'Long numbers', 'Excessive caps', 'Repeated characters',
          'Sales language', 'Link promotion'
        ];
        reasons.push(`Suspicious pattern: ${patternNames[index] || 'Unknown'}`);
        confidence += matches.length * 0.2;
      }
    });

    // 3. Length checks
    if (content.length < 10) {
      reasons.push('Content too short');
      confidence += 0.1;
    }

    if (content.length > 2000) {
      reasons.push('Content unusually long');
      confidence += 0.2;
    }

    // 4. Repetitive content check
    const words = content.split(/\s+/);
    const uniqueWords = new Set(words);
    const repetitionRatio = 1 - (uniqueWords.size / words.length);
    
    if (repetitionRatio > 0.7) {
      reasons.push('Highly repetitive content');
      confidence += 0.4;
    }

    // 5. Check user history (if available)
    if (authorId) {
      const userSpamScore = await this.getUserSpamScore(authorId);
      if (userSpamScore > 0.5) {
        reasons.push('User has high spam score');
        confidence += userSpamScore * 0.3;
      }
    }

    // 6. IP reputation check
    if (ip) {
      const ipReputation = await this.getIPReputation(ip);
      if (ipReputation < 0.3) {
        reasons.push('IP has poor reputation');
        confidence += 0.3;
      }
    }

    // 7. Check for excessive emoji usage
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu) || []).length;
    if (emojiCount > 10) {
      reasons.push('Excessive emoji usage');
      confidence += 0.2;
    }

    // 8. Check for excessive punctuation
    const punctuationCount = (content.match(/[!?]{3,}/g) || []).length;
    if (punctuationCount > 0) {
      reasons.push('Excessive punctuation');
      confidence += 0.1;
    }

    // Determine action based on confidence
    let action: 'allow' | 'flag' | 'block' = 'allow';
    if (confidence >= 0.8) {
      action = 'block';
    } else if (confidence >= 0.4) {
      action = 'flag';
    }

    return {
      isSpam: confidence >= 0.4,
      confidence: Math.min(confidence, 1),
      reasons,
      action,
    };
  }

  private async getUserSpamScore(userId: string): Promise<number> {
    try {
      // Calculate based on user's comment history
      const { data } = await this.supabase
        .from('comments')
        .select('flag_count, is_deleted')
        .eq('author_id', userId)
        .limit(50);

      if (!data || data.length === 0) return 0;

      const totalComments = data.length;
      const flaggedComments = data.filter(c => c.flag_count > 0).length;
      const deletedComments = data.filter(c => c.is_deleted).length;

      return (flaggedComments + deletedComments * 2) / totalComments;
    } catch (error) {
      console.error('Error calculating user spam score:', error);
      return 0;
    }
  }

  private async getIPReputation(ip: string): Promise<number> {
    try {
      // Simple IP reputation based on recent activity
      const { data } = await this.supabase
        .from('comment_audit_log')
        .select('action_type')
        .eq('ip_address', ip)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

      if (!data || data.length === 0) return 0.5; // Neutral for new IPs

      const totalActions = data.length;
      const negativeActions = data.filter(a => 
        ['delete', 'flag', 'reject'].includes(a.action_type)
      ).length;

      return Math.max(0, 1 - (negativeActions / totalActions));
    } catch (error) {
      console.error('Error calculating IP reputation:', error);
      return 0.5; // Neutral on error
    }
  }
}
