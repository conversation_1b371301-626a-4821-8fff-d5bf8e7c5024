'use client';

// Comment Moderation Section Component
// Date: 21/06/2025
// Task: Comment Moderation Dashboard Implementation - Redesigned to match dashboard style

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { useCommentModeration } from '@/hooks/useCommentModeration';
import { useAuthContext } from '@/contexts/auth-context';
import { CommentsByReview } from './CommentsByReview';
import { BlockedUsersManager } from './BlockedUsersManager';
import { CommentAnalytics } from './CommentAnalytics';
import { FlaggedContentManager } from './FlaggedContentManager';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Import the CSS that contains adaptive-text-title styles
import '@/components/review-form/style/NewReview.css';

import {
  MessageSquare,
  Clock,
  Shield,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Users,
  Flag,
  ChevronDown,
  ChevronUp,
  Loader2
} from 'lucide-react';

interface CommentModerationSectionProps {
  isDarkBackground?: boolean;
}

export function CommentModerationSection({ isDarkBackground: propIsDarkBackground }: CommentModerationSectionProps = {}) {
  const { user } = useAuthContext();
  const { comments, isLoading } = useCommentModeration(user?.uid || '');
  const hookIsDarkBackground = useBackgroundBrightness();
  const isDarkBackground = propIsDarkBackground ?? hookIsDarkBackground;

  if (!user) return null;

  // Calculate stats for badges
  const pendingComments = comments.filter(c => !c.is_approved && !c.is_deleted);
  const flaggedComments = comments.filter(c => c.flag_count > 0);

  if (isLoading) {
    return (
      <div className="space-y-8">
        {/* Header */}
        <div className="border-l-4 border-purple-500 pl-4">
          <span className={`font-mono text-2xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            <span className="text-violet-400/60">&lt;</span>
            <span className="px-2">Comment Moderation</span>
            <span className="text-violet-400/60">/&gt;</span>
          </span>
          <p className="text-sm font-mono mt-1 adaptive-text-subtitle">
            Manage flagged content, blocked users, and review comment activity
          </p>
        </div>

        {/* Loading state */}
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center justify-center h-16">
              <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
              <span className="ml-3 font-mono text-sm text-slate-400">Loading comments...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="border-l-4 border-purple-500 pl-4">
        <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
          <span className="text-violet-400/60">&lt;</span>
          <span className="px-2">Comment Moderation</span>
          <span className="text-violet-400/60">/&gt;</span>
        </span>
      </div>

      {/* Moderation Sections */}
      <div className="space-y-6">
        {/* Flagged Content Section */}
        <FlaggedContentSection 
          userId={user.uid} 
          flaggedCount={flaggedComments.length}
        />

        {/* Comments by Review Section */}
        <CommentsByReviewSection 
          comments={comments} 
          isLoading={isLoading}
          totalComments={comments.length}
        />

        {/* Blocked Users Section */}
        <BlockedUsersSection userId={user.uid} />

        {/* Analytics Section */}
        <AnalyticsSection userId={user.uid} />
      </div>
    </div>
  );
}

// Individual expandable sections
function FlaggedContentSection({ userId, flaggedCount }: { userId: string; flaggedCount: number }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Flagged Content
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              Review and moderate flagged comments and reviews from your content
            </p>
          </div>
          <div className="flex items-center gap-3">
            {flaggedCount > 0 && (
              <Badge variant="outline" className="font-mono text-xs bg-red-500/10 text-red-300 border-red-500/30 hover:bg-red-500/20 hover:border-red-500/50">
                {flaggedCount} flagged
              </Badge>
            )}
            <div className="text-gray-400 hover:text-white">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent className="pt-0">
              <FlaggedContentManager userId={userId} />
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}

function CommentsByReviewSection({ comments, isLoading, totalComments }: { comments: any[]; isLoading: boolean; totalComments: number }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Comments by Review
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              Browse and manage all comments organized by your reviews
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="font-mono text-xs border-slate-600/50">
              {totalComments} comments
            </Badge>
            <div className="text-gray-400 hover:text-white">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent className="pt-0">
              <CommentsByReview comments={comments} isLoading={isLoading} />
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}

function BlockedUsersSection({ userId }: { userId: string }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Blocked Users
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              Manage users who are blocked from commenting on your content
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-gray-400 hover:text-white">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent className="pt-0">
              <BlockedUsersManager userId={userId} />
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}

function AnalyticsSection({ userId }: { userId: string }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Comment Analytics
            </CardTitle>
            <p className="font-mono text-xs text-slate-400 tracking-wide mt-1">
              View detailed analytics and insights about comment engagement
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-gray-400 hover:text-white">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent className="pt-0">
              <CommentAnalytics userId={userId} />
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}
