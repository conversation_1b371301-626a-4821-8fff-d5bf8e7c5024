'use client';

// Blocked User Modal Component
// Date: 22/06/2025
// Purpose: Inform users when they're blocked from commenting on specific content

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogOverlay,
} from '@/components/ui/alert-dialog';
import { UserX, Info } from 'lucide-react';

interface BlockedUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentOwnerName?: string;
}

export function BlockedUserModal({ 
  isOpen, 
  onClose, 
  contentOwnerName = 'this user' 
}: BlockedUserModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogOverlay className="bg-black/60 backdrop-blur-sm" />
      <AlertDialogContent className="bg-slate-900/95 border border-slate-700/50 max-w-md backdrop-blur-md shadow-2xl">
        <AlertDialogHeader className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-slate-800/60 rounded-lg border border-slate-600/30">
              <UserX className="h-4 w-4 text-slate-400" />
            </div>
            <AlertDialogTitle className="text-slate-200 font-mono text-sm">
              <span className="text-slate-500">//</span> Access Restricted
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-slate-300 leading-relaxed text-sm font-mono">
            Unable to comment on content by{' '}
            <span className="font-medium text-slate-100 bg-slate-800/40 px-1.5 py-0.5 rounded border border-slate-600/30">{contentOwnerName}</span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <div className="flex items-start gap-3 p-3 bg-slate-800/30 rounded-lg border border-slate-700/30 mt-4">
          <Info className="h-3 w-3 text-slate-500 mt-1 flex-shrink-0" />
          <div className="text-xs text-slate-400 leading-relaxed font-mono">
            <p className="mb-2">
              Restriction applies only to <span className="text-slate-300">{contentOwnerName}'s</span> content.
            </p>
            <p>
              You can still interact with other users' content normally.
            </p>
          </div>
        </div>
        
        <AlertDialogFooter className="mt-6">
          <AlertDialogAction
            onClick={onClose}
            className="bg-slate-700/60 hover:bg-slate-600/60 text-slate-200 border border-slate-600/30 font-mono text-xs transition-all duration-200 backdrop-blur-sm"
          >
            Understood
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}