'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  Link as LinkIcon,
  Image as ImageIcon,
  Loader2,
  Check,
  X,
  Monitor,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {
  getUserContentBanner,
  saveContentBanner,
  deactivateContentBanner,
  type ContentBannerData
} from '@/lib/services/contentBannerService';
import UnifiedBannerAnalytics from './UnifiedBannerAnalytics';

interface ContentBannerConfigProps {
  userId: string;
  className?: string;
}

interface ContentData {
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  id?: string;
  created_at?: string;
  updated_at?: string;
}

const ContentBannerConfig: React.FC<ContentBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [contentData, setContentData] = useState<ContentData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentBanner, setCurrentBanner] = useState<ContentBannerData | null>(null);
  const [errors, setErrors] = useState<{
    img_url?: string;
    url?: string;
  }>({});
  
  const { toast } = useToast();

  // Load user's content banner data on mount
  useEffect(() => {
    loadContentData();
  }, [userId]);

  const loadContentData = async () => {
    try {
      setIsLoading(true);
      
      if (!userId) {
        console.warn('Cannot load content banner data: userId is undefined');
        setIsLoading(false);
        return;
      }
      
      const data = await getUserContentBanner(userId);
      
      if (data) {
        setContentData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentBanner(data);
      }
    } catch (error) {
      console.error('Error loading content banner data:', error);
      toast({
        title: "Error",
        description: "Failed to load content banner data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: { img_url?: string; url?: string } = {};

    if (!contentData.img_url.trim()) {
      newErrors.img_url = 'Banner image URL is required';
    } else if (!isValidUrl(contentData.img_url)) {
      newErrors.img_url = 'Please enter a valid image URL';
    }

    if (!contentData.url.trim()) {
      newErrors.url = 'Target link URL is required';
    } else if (!isValidUrl(contentData.url)) {
      newErrors.url = 'Please enter a valid URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setIsSaving(true);
      
      const savedData = await saveContentBanner({
        userId,
        imgUrl: contentData.img_url,
        url: contentData.url,
        isActive: true
      });
      
      setCurrentBanner(savedData);
      setContentData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });
      
      toast({
        title: "Success",
        description: "Content banner settings saved successfully",
      });
    } catch (error) {
      console.error('Error saving content banner data:', error);
      toast({
        title: "Error",
        description: "Failed to save content banner settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeactivate = async () => {
    try {
      setIsSaving(true);
      
      if (currentBanner) {
        const updatedData = await deactivateContentBanner(userId);
        
        setCurrentBanner(updatedData);
        setContentData({
          ...contentData,
          is_active: false
        });
        
        toast({
          title: "Success",
          description: "Content banner deactivated",
        });
      }
    } catch (error) {
      console.error('Error deactivating content banner:', error);
      toast({
        title: "Error",
        description: "Failed to deactivate content banner",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-emerald-500" />
            <span className="ml-2 text-slate-400">Loading content banner settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Content Banner
              </CardTitle>
              <p className="font-mono text-xs text-gray-400 mt-1">
                Add a horizontal banner that will appear between your reviews and YouTube sections
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-4">
        {/* Current Banner Display */}
        {currentBanner && currentBanner.is_active && (
          <div className="p-4 bg-slate-800/50 border border-slate-700/50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-slate-200">Current Content Banner</h4>
              <Button
                onClick={handleDeactivate}
                disabled={isSaving}
                variant="outline"
                size="sm"
                className="text-red-400 border-red-400/50 hover:bg-red-400/10"
              >
                {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
                Deactivate
              </Button>
            </div>

            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className="relative w-32 h-32 mx-auto border border-slate-600 rounded-lg overflow-hidden bg-slate-800/80 flex items-center justify-center mb-3">
                  {currentBanner.img_url ? (
                    <img
                      src={currentBanner.img_url}
                      alt="Content Banner Preview"
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "/imgs/image-placeholder.svg";
                        (e.target as HTMLImageElement).classList.add("p-4");
                      }}
                    />
                  ) : (
                    <ImageIcon className="h-10 w-10 text-slate-500" />
                  )}
                </div>

                <div className="flex items-center justify-center gap-2">
                  <a
                    href={currentBanner.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-emerald-400 hover:text-emerald-300 hover:underline flex items-center gap-1"
                  >
                    {currentBanner.url.length > 30
                      ? `${currentBanner.url.substring(0, 30)}...`
                      : currentBanner.url}
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Configuration Form */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="img_url" className="text-slate-200">
              Banner Image URL
            </Label>
            <div className="relative mt-1">
              <ImageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                id="img_url"
                type="url"
                value={contentData.img_url}
                onChange={(e) => setContentData({ ...contentData, img_url: e.target.value })}
                placeholder="https://example.com/banner-image.jpg"
                className="pl-10 bg-slate-800/50 border-slate-700/50 text-slate-200"
              />
            </div>
            {errors.img_url && (
              <p className="text-red-400 text-sm mt-1">{errors.img_url}</p>
            )}
          </div>

          <div>
            <Label htmlFor="url" className="text-slate-200">
              Target Link URL
            </Label>
            <div className="relative mt-1">
              <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                id="url"
                type="url"
                value={contentData.url}
                onChange={(e) => setContentData({ ...contentData, url: e.target.value })}
                placeholder="https://example.com/affiliate-link"
                className="pl-10 bg-slate-800/50 border-slate-700/50 text-slate-200"
              />
            </div>
            {errors.url && (
              <p className="text-red-400 text-sm mt-1">{errors.url}</p>
            )}
          </div>
        </div>

        {/* Action Button */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-4 border-t border-slate-700/50"
        >
          <Button
            onClick={handleSave}
            disabled={isSaving || isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Check className="h-4 w-4 mr-2" />
            )}
            {currentBanner && currentBanner.is_active ? 'Update Content Banner' : 'Activate Content Banner'}
          </Button>
        </motion.div>

                {/* Analytics Section - Now inside the Content Banner container */}
                {currentBanner && currentBanner.is_active && currentBanner.id && (
                  <div className="pt-4 border-t border-gray-700/50">
                    <UnifiedBannerAnalytics
                      bannerId={currentBanner.id}
                      bannerType="content"
                    />
                  </div>
                )}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};

export default ContentBannerConfig;
