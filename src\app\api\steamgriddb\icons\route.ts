import { NextRequest, NextResponse } from 'next/server';
import { getSteamGridDBIcons, STEAMGRIDDB_STYLES, STEAMGRIDDB_DIMENSIONS } from '@/lib/steamgriddb-api';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  const gameId = searchParams.get('gameId');
  const styles = searchParams.get('styles')?.split(',').filter(Boolean) || [];
  const dimensions = searchParams.get('dimensions')?.split(',').filter(Boolean) || [];
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

  if (!gameId) {
    return NextResponse.json(
      { error: 'gameId parameter is required' },
      { status: 400 }
    );
  }

  if (isNaN(parseInt(gameId))) {
    return NextResponse.json(
      { error: 'gameId must be a valid number' },
      { status: 400 }
    );
  }

  // Validate styles
  const validStyles = styles.filter(style => STEAMGRIDDB_STYLES.includes(style as any));
  if (styles.length > 0 && validStyles.length === 0) {
    return NextResponse.json(
      { 
        error: 'Invalid styles provided',
        validStyles: STEAMGRIDDB_STYLES
      },
      { status: 400 }
    );
  }

  // Validate dimensions
  const validDimensions = dimensions.filter(dim => STEAMGRIDDB_DIMENSIONS.includes(dim as any));
  if (dimensions.length > 0 && validDimensions.length === 0) {
    return NextResponse.json(
      { 
        error: 'Invalid dimensions provided',
        validDimensions: STEAMGRIDDB_DIMENSIONS
      },
      { status: 400 }
    );
  }

  try {
    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      dimensions: validDimensions.length > 0 ? validDimensions : undefined,
      limit
    };

    const icons = await getSteamGridDBIcons(parseInt(gameId), options);

    return NextResponse.json({
      success: true,
      data: icons,
      metadata: {
        count: icons.length,
        gameId: parseInt(gameId),
        styles: validStyles,
        dimensions: validDimensions
      }
    });
  } catch (error) {
    console.error('SteamGridDB icons error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch icons from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gameId, styles, dimensions, limit } = body;

    if (!gameId) {
      return NextResponse.json(
        { error: 'gameId is required' },
        { status: 400 }
      );
    }

    // Validate styles if provided
    const validStyles = styles ? styles.filter((style: string) => STEAMGRIDDB_STYLES.includes(style as any)) : [];
    
    // Validate dimensions if provided
    const validDimensions = dimensions ? dimensions.filter((dim: string) => STEAMGRIDDB_DIMENSIONS.includes(dim as any)) : [];

    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      dimensions: validDimensions.length > 0 ? validDimensions : undefined,
      limit
    };

    const icons = await getSteamGridDBIcons(gameId, options);

    return NextResponse.json({
      success: true,
      data: icons,
      metadata: {
        count: icons.length,
        gameId,
        styles: validStyles,
        dimensions: validDimensions
      }
    });
  } catch (error) {
    console.error('SteamGridDB icons error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch icons from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}