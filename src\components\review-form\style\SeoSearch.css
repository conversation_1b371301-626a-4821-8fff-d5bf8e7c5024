/* ═══════════════════════════════════════════════════════════════════════════
   🔍 SEO SEARCH SECTION STYLES
   ═══════════════════════════════════════════════════════════════════════════ */

/* Core Variables */
:root {
  /* Colors - Match monetization config */
  --seo-text-primary: #ffffff;
  --seo-text-secondary: rgba(255, 255, 255, 0.8);
  --seo-text-muted: rgba(255, 255, 255, 0.6);
  --seo-border: rgba(100, 116, 139, 0.4);
  --seo-border-light: rgba(100, 116, 139, 0.2);
  --seo-bg-primary: rgba(15, 23, 42, 0.6);
  --seo-bg-secondary: rgba(30, 41, 59, 0.4);
  
  /* Accent Colors */
  --seo-accent-cyan: #06b6d4;
  --seo-accent-purple: #8b5cf6;
  --seo-accent-yellow: #eab308;
  
  /* Status Colors */
  --seo-success: #10b981;
  --seo-warning: #f59e0b;
  --seo-error: #ef4444;
  --seo-info: #3b82f6;
  
  /* Interactive states - match monetization */
  --seo-hover: rgba(100, 116, 139, 0.1);
  --seo-active: rgba(139, 92, 246, 0.1);
}

/* ═══════════════════════════════════════════════════════════════════════════
   CONTAINER & LAYOUT
   ═══════════════════════════════════════════════════════════════════════════ */

.seo-container {
  width: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--seo-text-primary);
}

.seo-main-card {
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s ease;
}

.seo-card-content {
  padding: 1.5rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
   FORM ELEMENTS
   ═══════════════════════════════════════════════════════════════════════════ */

.seo-field-group {
  margin-bottom: 1.5rem;
  background: var(--seo-bg-secondary);
  border: 1px solid var(--seo-border);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.seo-field-group:hover {
  border-color: var(--seo-accent-cyan);
  box-shadow: 0 0 0 1px var(--seo-accent-cyan);
}

.seo-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--seo-text-secondary);
}

.seo-icon-box {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  margin-right: 0.75rem;
  background: rgba(6, 182, 212, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.2);
}

.seo-icon-box-small {
  width: 1.75rem;
  height: 1.75rem;
}

.seo-icon-small {
  width: 1rem;
  height: 1rem;
}

.seo-code-brackets {
  color: var(--seo-accent-purple);
  margin: 0 0.25rem;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-weight: 400;
}

.seo-input-wrapper {
  position: relative;
  margin-bottom: 0.5rem;
}

.seo-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid var(--seo-border);
  border-radius: 0.375rem;
  color: var(--seo-text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 0.9375rem;
  line-height: 1.5;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.seo-input:focus {
  outline: none;
  border-color: var(--seo-accent-cyan);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
  background: rgba(15, 23, 42, 0.7);
}

.seo-input-dimmed {
  background: rgba(15, 23, 42, 0.6);
  color: var(--seo-text-muted);
}

.seo-input-dimmed:focus {
  color: var(--seo-text-primary);
  background: rgba(15, 23, 42, 0.8);
}

.seo-input-check {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}

.seo-check-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--seo-success);
}

.seo-hint {
  font-size: 0.8125rem;
  color: var(--seo-text-muted);
  margin-top: 0.5rem;
  line-height: 1.4;
}

.seo-hint-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: var(--seo-text-muted);
}

/* ═══════════════════════════════════════════════════════════════════════════
   NAVIGATION & ACTIONS
   ═══════════════════════════════════════════════════════════════════════════ */

.seo-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--seo-border-light);
}

.seo-status {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: var(--seo-text-muted);
}

.seo-status-ready {
  display: flex;
  align-items: center;
  color: var(--seo-success);
}

.seo-status-warning {
  display: flex;
  align-items: center;
  color: var(--seo-warning);
}

.seo-status-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
   BUTTON STYLES (Matching the rest of the app)
   ═══════════════════════════════════════════════════════════════════════════ */

.seo-continue-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.1));
  color: var(--seo-text-primary);
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.seo-continue-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(6, 182, 212, 0.15));
  border-color: rgba(139, 92, 246, 0.5);
}

.seo-continue-button:active:not(:disabled) {
  transform: translateY(0);
}

.seo-continue-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(71, 85, 105, 0.2);
  border-color: rgba(71, 85, 105, 0.3);
  color: var(--seo-text-muted);
}

.seo-continue-ready {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.2));
  border-color: rgba(139, 92, 246, 0.5);
}

.seo-continue-ready:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(6, 182, 212, 0.3));
  border-color: rgba(139, 92, 246, 0.7);
}

.seo-button-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1;
}

.seo-button-arrow {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
}

.seo-continue-button:hover .seo-button-arrow {
  transform: translateX(2px);
}

.seo-continue-button:disabled .seo-button-arrow {
  opacity: 0.6;
}

/* ═══════════════════════════════════════════════════════════════════════════
   ANIMATIONS & TRANSITIONS
   ═══════════════════════════════════════════════════════════════════════════ */

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   RESPONSIVE ADJUSTMENTS
   ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 640px) {
  .seo-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .seo-status {
    justify-content: center;
    margin-bottom: 0.5rem;
  }
  
  .seo-continue-button {
    width: 100%;
    margin-top: 0.5rem;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   UTILITY CLASSES
   ═══════════════════════════════════════════════════════════════════════════ */

.seo-text-cyan {
  color: var(--seo-accent-cyan);
}

.seo-text-yellow {
  color: var(--seo-accent-yellow);
}

.seo-text-purple {
  color: var(--seo-accent-purple);
}
