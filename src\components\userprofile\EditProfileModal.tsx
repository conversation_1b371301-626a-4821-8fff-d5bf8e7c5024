'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Info,
  Gamepad2,
  Link as LinkIcon,
  Plus,
  Trash,
  ImageIcon,
  Palette,
  ExternalLink,
  ArrowRight,
  ArrowLeft,
  Check,
  User,
  Globe,
  X,
  AlertTriangle,
  Save,
  Camera,
  Image as ImageIconLucide,
  Menu,
  Upload,
  Loader2
} from 'lucide-react';

// Import Supabase client
import { createClient } from '@/lib/supabase/client';

// Import enhanced components
import EnhancedThemePicker from './EnhancedThemePicker';
import { CustomColors } from './CustomColorPicker';

// Import the dedicated icon components
import SteamIcon from '@/components/ui/icons/gaming/SteamIcon';
import PlaystationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';
import NintendoSwitchIcon from '@/components/ui/icons/gaming/NintendoSwitchIcon';

// Import social media icons
import FacebookIcon from '@/components/ui/icons/socials/FacebookIcon';
import GithubIcon from '@/components/ui/icons/socials/GithubIcon';
import InstagramIcon from '@/components/ui/icons/socials/InstagramIcon';
import RedditIcon from '@/components/ui/icons/socials/RedditIcon';
import TikTokIcon from '@/components/ui/icons/socials/TikTokIcon';
import TwitchIcon from '@/components/ui/icons/socials/TwitchIcon';
import TwitterIcon from '@/components/ui/icons/socials/TwitterIcon';
import YouTubeIcon from '@/components/ui/icons/socials/YouTubeIcon';
import ChatSquareLikeIcon from '@/components/ui/icons/socials/ChatSquareLikeIcon';

import type { UnifiedUserProfile, GamingProfile, SocialMediaProfile } from '@/lib/types/profile';
import type { CustomColors as CustomColorsType } from '@/lib/types';
import '@/components/style/editProfileModal.css';

// Zod schema para validação do formulário
const profileSchema = z.object({
  bio: z.string().max(500, "Bio deve ter no máximo 500 caracteres").optional().or(z.literal('')),
  preferred_genres: z.array(z.string()).max(10, "Máximo 10 gêneros").optional(),
  favorite_consoles: z.array(z.string()).max(5, "Máximo 5 consoles").optional(),
  theme: z.string().optional(),
  avatar_url: z.union([
    z.string().url("URL inválida"),
    z.literal(''),
    z.undefined()
  ]).optional(),
  banner_url: z.union([
    z.string().url("URL inválida"),
    z.literal(''),
    z.undefined()
  ]).optional(),
  custom_colors: z.object({
    primary: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Cor inválida"),
    secondary: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Cor inválida"),
    accent: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Cor inválida")
  }).optional()
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface EditProfileModalProps {
  profile: UnifiedUserProfile;
  open: boolean;
  onClose: () => void;
  onSave: (updatedProfile: Partial<UnifiedUserProfile>) => void;
}

// Code Title Component - matching auth modal style
const CodeTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <span className={cn("edit-modal-title", className)}>
    <span className="edit-modal-title-comment">//</span>
    {children}
  </span>
);

// Emoji Widget Component
const EmojiWidget = ({ 
  onEmojiSelect, 
  isOpen, 
  onClose 
}: { 
  onEmojiSelect: (emoji: string) => void; 
  isOpen: boolean; 
  onClose: () => void; 
}) => {
  const emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
    '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾',
    '🤖', '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿',
    '😾', '❤️', '🧡', '💛', '💚', '💙', '💜', '🤎', '🖤', '🤍',
    '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟',
    '🎮', '🕹️', '🎯', '🎲', '🃏', '🎸', '🎵', '🎶', '🎤', '🎧',
    '👍', '👎', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈',
    '👉', '👆', '🖕', '👇', '☝️', '👏', '🙌', '👐', '🤲', '🤝',
    '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃',
    '🧠', '🫀', '🫁', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋'
  ];

  if (!isOpen) return null;

  return (
    <div className="edit-emoji-widget-overlay" onClick={onClose}>
      <div className="edit-emoji-widget" onClick={(e) => e.stopPropagation()}>
        <div className="edit-emoji-widget-header">
          <span className="edit-emoji-widget-title">Select Emoji</span>
          <button onClick={onClose} className="edit-emoji-widget-close" title="Close emoji picker">
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="edit-emoji-grid">
          {emojis.map((emoji, index) => (
            <button
              key={index}
              onClick={() => {
                onEmojiSelect(emoji);
                onClose();
              }}
              className="edit-emoji-button"
            >
              {emoji}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// Confirmation Dialog Component - Custom implementation to ensure proper z-index
const ConfirmationDialog = ({ 
  open, 
  onConfirm, 
  onCancel 
}: { 
  open: boolean; 
  onConfirm: () => void; 
  onCancel: () => void; 
}) => {
  if (!open) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center backdrop-blur-sm"
      style={{ zIndex: 1100 }}
      onClick={onCancel}
    >
      <motion.div
        className="edit-confirmation-dialog max-w-md w-full mx-4 p-6 rounded-lg"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="edit-confirmation-title mb-4">
          <AlertTriangle className="h-5 w-5" />
          UNSAVED CHANGES
        </div>
        <div className="edit-confirmation-content">
          <p>
            You have unsaved changes. Are you sure you want to close without saving?
          </p>
        </div>
        <div className="flex gap-2 mt-6 justify-end">
          <button 
            onClick={onCancel}
            className="edit-button outline"
          >
            Keep Editing
          </button>
          <button 
            onClick={onConfirm}
            className="edit-button" 
            style={{ 
              background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.8) 100%)',
              borderColor: 'rgba(239, 68, 68, 0.3)',
              boxShadow: '0 4px 12px rgba(239, 68, 68, 0.25)'
            }}
          >
            Close Without Saving
          </button>
        </div>
      </motion.div>
    </div>
  );
};

// Default theme
const defaultTheme = 'muted-dark';

// Platform options for gaming profiles
const platformOptions = [
  { id: 'steam', name: 'Steam', icon: SteamIcon },
  { id: 'playstation', name: 'PlayStation', icon: PlaystationIcon },
  { id: 'xbox', name: 'Xbox', icon: XboxIcon },
  { id: 'nintendo', name: 'Nintendo', icon: NintendoSwitchIcon },
  { id: 'epic', name: 'Epic Games', icon: Gamepad2 },
  { id: 'origin', name: 'Origin', icon: Gamepad2 },
  { id: 'uplay', name: 'Uplay', icon: Gamepad2 }
];

// Social media options
const socialMediaOptions = [
  { id: 'twitch', name: 'Twitch', icon: TwitchIcon },
  { id: 'youtube', name: 'YouTube', icon: YouTubeIcon },
  { id: 'twitter', name: 'X (formerly Twitter)', icon: TwitterIcon },
  { id: 'facebook', name: 'Facebook', icon: FacebookIcon },
  { id: 'instagram', name: 'Instagram', icon: InstagramIcon },
  { id: 'github', name: 'GitHub', icon: GithubIcon },
  { id: 'reddit', name: 'Reddit', icon: RedditIcon },
  { id: 'tiktok', name: 'TikTok', icon: TikTokIcon },
  { id: 'linkedin', name: 'LinkedIn', icon: ChatSquareLikeIcon },
  { id: 'discord', name: 'Discord', icon: ChatSquareLikeIcon }
];



// Image Upload Component with Supabase Storage
const ImageUploadCustomization = ({
  profileURL,
  bannerUrl,
  onProfileChange,
  onBannerChange,
  userId
}: {
  profileURL?: string;
  bannerUrl?: string;
  onProfileChange: (url: string) => void;
  onBannerChange: (url: string) => void;
  userId: string;
}) => {
  const [activeTab, setActiveTab] = useState<'profile' | 'banner'>('profile');
  const [uploading, setUploading] = useState<{profile: boolean, banner: boolean}>({
    profile: false,
    banner: false
  });
  const [compressing, setCompressing] = useState<{profile: boolean, banner: boolean}>({
    profile: false,
    banner: false
  });
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [compressionInfo, setCompressionInfo] = useState<string | null>(null);

  const supabase = createClient();

  // Client-side image compression function
  const compressImage = async (file: File, type: 'profile' | 'banner'): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Set compression settings based on image type
        const maxWidth = type === 'profile' ? 512 : 1200;  // Profile: 512px, Banner: 1200px
        const maxHeight = type === 'profile' ? 512 : 300;  // Profile: 512px, Banner: 300px
        const quality = 0.85; // 85% quality

        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = Math.round(width * ratio);
          height = Math.round(height * ratio);
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress image
        ctx?.drawImage(img, 0, 0, width, height);

        // Convert to blob with compression
        canvas.toBlob(
          (blob) => {
            if (blob) {
              // Create new file with compressed data
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg', // Convert to JPEG for better compression
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image for compression'));
      img.src = URL.createObjectURL(file);
    });
  };

  const uploadImage = async (file: File, type: 'profile' | 'banner') => {
    try {
      setUploading(prev => ({ ...prev, [type]: true }));
      setUploadError(null);
      setCompressionInfo(null);

      // Validate file
      if (!file.type.startsWith('image/')) {
        throw new Error('Please select an image file');
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        throw new Error('File size must be less than 10MB');
      }

      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('You must be logged in to upload images');
      }

      // Show compression progress
      setCompressing(prev => ({ ...prev, [type]: true }));

      // Compress image before upload
      const compressedFile = await compressImage(file, type);

      setCompressing(prev => ({ ...prev, [type]: false }));

      // Show compression results
      const compressionRatio = ((file.size - compressedFile.size) / file.size * 100).toFixed(1);
      const compressionText = `Compressed: ${(file.size / 1024).toFixed(1)}KB → ${(compressedFile.size / 1024).toFixed(1)}KB (${compressionRatio}% smaller)`;
      setCompressionInfo(compressionText);

      // Create file path that will overwrite existing files
      const fileName = `${userId}_${type}.jpg`; // Use .jpg since we convert to JPEG
      const filePath = fileName; // Put directly in bucket root for now to test overwriting

      // First, try to delete any existing files to ensure clean overwrite
      // Try to delete files with different extensions that might exist
      const possibleExtensions = ['jpg', 'jpeg', 'png', 'webp'];
      const baseFileName = `${userId}_${type}`;

      for (const ext of possibleExtensions) {
        try {
          const oldFilePath = `${baseFileName}.${ext}`;
          const deleteResult = await supabase.storage
            .from('user_images')
            .remove([oldFilePath]);
          console.log(`Delete result for ${oldFilePath}:`, deleteResult);
        } catch (deleteError) {
          // Ignore delete errors (file might not exist)
          console.log(`No existing file ${baseFileName}.${ext} to delete`);
        }
      }

      // Upload to Supabase Storage
      console.log(`Uploading new file to: ${filePath}`);
      const { data, error } = await supabase.storage
        .from('user_images')
        .upload(filePath, compressedFile, {
          contentType: 'image/jpeg' // Since we converted to JPEG
        });

      console.log('Upload result:', { data, error });

      if (error) {
        throw error;
      }

      // Log successful upload details
      console.log('✅ Upload successful!');
      console.log('📁 Bucket: user_images');
      console.log('📄 File path:', filePath);
      console.log('📊 Upload data:', data);

      // Get public URL with cache-busting parameter
      const { data: { publicUrl } } = supabase.storage
        .from('user_images')
        .getPublicUrl(filePath);

      console.log('🔗 Generated public URL:', publicUrl);

      // Add cache-busting timestamp to force browser to reload the image
      const cacheBustedUrl = `${publicUrl}?t=${Date.now()}`;
      console.log('🔗 Cache-busted URL:', cacheBustedUrl);

      // Update the form with the new URL
      if (type === 'profile') {
        onProfileChange(cacheBustedUrl);
      } else {
        onBannerChange(cacheBustedUrl);
      }

      console.log('✅ Image upload and URL update completed successfully!');

      // List bucket contents to verify file exists
      try {
        const { data: files, error: listError } = await supabase.storage
          .from('user_images')
          .list('', {
            limit: 100,
            offset: 0
          });

        if (listError) {
          console.error('❌ Error listing bucket contents:', listError);
        } else {
          console.log('📂 Current bucket contents:');
          files?.forEach(file => {
            console.log(`  - ${file.name} (${file.metadata?.size} bytes, updated: ${file.updated_at})`);
          });

          // Check if our file is in the list
          const ourFile = files?.find(f => f.name === filePath);
          if (ourFile) {
            console.log('✅ Our uploaded file found in bucket:', ourFile);
          } else {
            console.log('❌ Our uploaded file NOT found in bucket listing');
          }
        }
      } catch (listError) {
        console.error('❌ Error checking bucket contents:', listError);
      }

    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Upload failed');
      setCompressionInfo(null); // Clear compression info on error
    } finally {
      setUploading(prev => ({ ...prev, [type]: false }));
      setCompressing(prev => ({ ...prev, [type]: false }));
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>, type: 'profile' | 'banner') => {
    const file = event.target.files?.[0];
    if (file) {
      uploadImage(file, type);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  return (
    <div className="edit-image-customization">
      <div className="flex flex-col space-y-4 mb-4">
        <Label className="edit-form-label primary">
          <Camera className="h-5 w-5" />
          Profile Images
        </Label>
        <div className="edit-image-tabs-vertical">
          <button
            type="button"
            onClick={() => setActiveTab('profile')}
            className={cn("edit-image-tab-vertical", activeTab === 'profile' && "active")}
          >
            Profile Picture
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('banner')}
            className={cn("edit-image-tab-vertical", activeTab === 'banner' && "active")}
          >
            Banner Image
          </button>
        </div>
      </div>

      {uploadError && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <p className="text-red-400 text-sm">{uploadError}</p>
        </div>
      )}

      {compressionInfo && (
        <div className="mb-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
          <p className="text-green-400 text-sm">{compressionInfo}</p>
        </div>
      )}

      {activeTab === 'profile' ? (
        <div className="space-y-4">
          {profileURL ? (
            <div className="edit-image-preview profile">
              <img
                key={profileURL} // Force re-render when URL changes
                src={profileURL}
                alt="Profile Picture Preview"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 24 24' fill='none' stroke='%23475569' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E";
                }}
              />
              <div className="edit-image-overlay">
                <Button
                  type="button"
                  className="edit-button outline"
                  onClick={() => onProfileChange('')}
                >
                  Remove Image
                </Button>
              </div>
            </div>
          ) : (
            <div className="edit-image-placeholder profile">
              <User className="h-8 w-8" />
              <p className="text-xs">NO PROFILE IMAGE</p>
            </div>
          )}

          {/* File Upload Input */}
          <div className="edit-image-upload-container">
            <input
              type="file"
              id="profile-upload"
              accept="image/*"
              onChange={(e) => handleFileSelect(e, 'profile')}
              className="hidden"
              disabled={uploading.profile || compressing.profile}
            />
            <label
              htmlFor="profile-upload"
              className={cn(
                "edit-image-upload-button",
                (uploading.profile || compressing.profile) && "uploading"
              )}
            >
              {compressing.profile ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Compressing...
                </>
              ) : uploading.profile ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Upload Profile Picture
                </>
              )}
            </label>
          </div>
          <p className="edit-image-hint">RECOMMENDED: Square image (1:1 ratio), max 10MB</p>
        </div>
      ) : (
        <div className="space-y-4">
          {bannerUrl ? (
            <div className="edit-image-preview banner">
              <img
                key={bannerUrl} // Force re-render when URL changes
                src={bannerUrl}
                alt="Banner Preview"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='100' viewBox='0 0 24 24' fill='none' stroke='%23475569' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E";
                }}
              />
              <div className="edit-image-overlay">
                <Button
                  type="button"
                  className="edit-button outline"
                  onClick={() => onBannerChange('')}
                >
                  Remove Banner
                </Button>
              </div>
            </div>
          ) : (
            <div className="edit-image-placeholder banner">
              <ImageIconLucide className="h-6 w-6" />
              <p className="text-xs">NO BANNER IMAGE</p>
            </div>
          )}

          {/* File Upload Input */}
          <div className="edit-image-upload-container">
            <input
              type="file"
              id="banner-upload"
              accept="image/*"
              onChange={(e) => handleFileSelect(e, 'banner')}
              className="hidden"
              disabled={uploading.banner || compressing.banner}
            />
            <label
              htmlFor="banner-upload"
              className={cn(
                "edit-image-upload-button",
                (uploading.banner || compressing.banner) && "uploading"
              )}
            >
              {compressing.banner ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Compressing...
                </>
              ) : uploading.banner ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Upload Banner Image
                </>
              )}
            </label>
          </div>
          <p className="edit-image-hint">RECOMMENDED: Banner image (4:1 ratio, 1200×300px), max 10MB</p>
        </div>
      )}
    </div>
  );
};

// Navigation Sidebar Component
const NavigationSidebar = ({
  activeSection,
  onSectionChange,
  sections,
  isMobile = false,
  isCollapsed = false,
  onClose,
  onSave,
  hasUnsavedChanges = false
}: {
  activeSection: string;
  onSectionChange: (section: string) => void;
  sections: Array<{ id: string; title: string; icon: React.ReactNode; }>;
  isMobile?: boolean;
  isCollapsed?: boolean;
  onClose?: () => void;
  onSave?: () => void;
  hasUnsavedChanges?: boolean;
}) => {
  if (isMobile) {
    return (
      <div className="edit-mobile-nav">
        <div className="edit-mobile-nav-container">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => onSectionChange(section.id)}
              className={cn(
                "edit-mobile-nav-button",
                activeSection === section.id && "active"
              )}
            >
              <div className="edit-mobile-nav-icon">
                {section.icon}
              </div>
              <span className="edit-mobile-nav-label">{section.title}</span>
            </button>
          ))}
          
          {/* Unsaved changes indicator directly in mobile nav */}
          {hasUnsavedChanges && (
            <div className="edit-mobile-nav-button unsaved">
              <div className="edit-mobile-nav-icon">
                <AlertTriangle className="h-4 w-4" />
              </div>
              <span className="edit-mobile-nav-label">Unsaved</span>
            </div>
          )}
          
          {/* Action buttons directly in mobile nav */}
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="edit-mobile-nav-button action"
            >
              <div className="edit-mobile-nav-icon">
                <X className="h-4 w-4" />
              </div>
              <span className="edit-mobile-nav-label">Close</span>
            </button>
          )}
          {onSave && (
            <button
              type="button"
              onClick={onSave}
              disabled={!hasUnsavedChanges}
              className={cn("edit-mobile-nav-button action save", !hasUnsavedChanges && "disabled")}
            >
              <div className="edit-mobile-nav-icon">
                <Save className="h-4 w-4" />
              </div>
              <span className="edit-mobile-nav-label">Save</span>
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("edit-sidebar", isCollapsed && "collapsed")}>
      <nav className="edit-sidebar-nav">
        {sections.map((section) => (
          <button
            key={section.id}
            onClick={() => onSectionChange(section.id)}
            className={cn(
              "edit-sidebar-button",
              activeSection === section.id && "active"
            )}
          >
            <div className="edit-sidebar-icon">
              {section.icon}
            </div>
            <span className="edit-sidebar-label">{section.title}</span>
          </button>
        ))}
        
        {/* Action area with unsaved changes indicator */}
        <div className="edit-sidebar-actions">
          {/* Unsaved changes indicator right above action buttons */}
          {hasUnsavedChanges && (
            <div className="edit-sidebar-nav-unsaved">
              <p className="edit-unsaved-text nav">
                <AlertTriangle className="h-4 w-4" />
                Unsaved changes
              </p>
            </div>
          )}
          
          {/* Action buttons */}
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="edit-sidebar-button action"
            >
              <div className="edit-sidebar-icon">
                <X className="h-4 w-4" />
              </div>
              <span className="edit-sidebar-label">Close</span>
            </button>
          )}
          {onSave && (
            <button
              type="button"
              onClick={onSave}
              disabled={!hasUnsavedChanges}
              className={cn("edit-sidebar-button action save", !hasUnsavedChanges && "disabled")}
            >
              <div className="edit-sidebar-icon">
                <Save className="h-4 w-4" />
              </div>
              <span className="edit-sidebar-label">Save Changes</span>
            </button>
          )}
        </div>
      </nav>
    </div>
  );
};

const EditProfileModal: React.FC<EditProfileModalProps> = ({ profile, open, onClose, onSave }) => {
  // Setup react-hook-form with Zod validation
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      bio: profile.bio || '',
      preferred_genres: profile.preferredGenres || [],
      favorite_consoles: profile.favoriteConsoles || [],
      theme: profile.theme || defaultTheme,
      avatar_url: profile.avatarUrl || '',
      banner_url: profile.bannerUrl || '',
      custom_colors: profile.customColors || { 
        primary: '#8b5cf6', 
        secondary: '#7c3aed', 
        accent: '#ec4899' 
      }
    }
  });

  const { register, handleSubmit, watch, setValue, formState: { errors, isDirty } } = form;
  const watchedValues = watch();

  // Remove legacy formData state - using only react-hook-form state
  // Legacy gaming profiles and social media for UI that don't have Zod validation yet
  const [gamingProfiles, setGamingProfiles] = useState<GamingProfile[]>(profile.gamingProfiles || []);
  const [socialMedia, setSocialMedia] = useState<SocialMediaProfile[]>(profile.socialMedia || []);
  
  // For UI State
  const [newGenre, setNewGenre] = useState('');

  const [tempGamingProfile, setTempGamingProfile] = useState<{ platform?: string; username?: string; url?: string }>({});
  const [tempSocialProfile, setTempSocialProfile] = useState<{ platform?: string; username?: string; url?: string }>({});
  
  // Navigation state
  const [activeSection, setActiveSection] = useState('basic');
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Close protection state
  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Emoji widget state
  const [isEmojiWidgetOpen, setIsEmojiWidgetOpen] = useState(false);

  // Navigation sections
  const navigationSections = [
    { id: "basic", title: "Profile", icon: <User className="h-5 w-5" /> },
    { id: "gaming", title: "Gaming", icon: <Gamepad2 className="h-5 w-5" /> },
    { id: "social", title: "Social", icon: <Globe className="h-5 w-5" /> },
    { id: "appearance", title: "Settings", icon: <Palette className="h-5 w-5" /> }
  ];

  // Check for mobile viewport
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Reset form data when profile changes
  useEffect(() => {
    const newDefaults = {
      bio: profile.bio || '',
      preferred_genres: profile.preferredGenres || [],
      favorite_consoles: profile.favoriteConsoles || [],
      theme: profile.theme || defaultTheme,
      avatar_url: profile.avatarUrl || '',
      banner_url: profile.bannerUrl || '',
      custom_colors: profile.customColors || {
        primary: '#8b5cf6',
        secondary: '#7c3aed',
        accent: '#ec4899'
      }
    };

    form.reset(newDefaults);

    // Ensure we get the social media data from the correct source
    // Check both socialMedia (converted) and social_profiles (raw) fields
    const socialMediaData = profile.socialMedia ||
                           (profile as any).social_profiles ||
                           [];

    const gamingProfilesData = profile.gamingProfiles ||
                              (profile as any).gaming_profiles ||
                              [];

    setGamingProfiles(gamingProfilesData);
    setSocialMedia(socialMediaData);

    setActiveSection('basic');
    setHasUnsavedChanges(false);
  }, [profile, form]);
  
  // Check for changes using react-hook-form's isDirty
  useEffect(() => {
    setHasUnsavedChanges(isDirty);
  }, [isDirty]);
  
  // Block body scroll when modal is open
  useEffect(() => {
    if (open) {
      // Save current scroll position and styles
      const scrollY = window.scrollY;
      const body = document.body;
      const html = document.documentElement;
      
      // Store original styles to restore later
      const originalBodyOverflow = body.style.overflow;
      const originalBodyHeight = body.style.height;
      const originalHtmlOverflow = html.style.overflow;
      
      // Apply scroll lock without changing position
      body.style.overflow = 'hidden';
      body.style.height = '100%';
      html.style.overflow = 'hidden';
      
      // Store values for cleanup
      (body as any)._scrollLockData = {
        originalBodyOverflow,
        originalBodyHeight,
        originalHtmlOverflow,
        scrollY
      };
    } else {
      // Restore scroll and original styles
      const body = document.body;
      const html = document.documentElement;
      const scrollLockData = (body as any)._scrollLockData;
      
      if (scrollLockData) {
        body.style.overflow = scrollLockData.originalBodyOverflow || '';
        body.style.height = scrollLockData.originalBodyHeight || '';
        html.style.overflow = scrollLockData.originalHtmlOverflow || '';
        
        // Restore scroll position
        if (scrollLockData.scrollY) {
          window.scrollTo(0, scrollLockData.scrollY);
        }
        
        // Clean up stored data
        delete (body as any)._scrollLockData;
      }
    }

    // Cleanup function to ensure scroll is restored if component unmounts
    return () => {
      const body = document.body;
      const html = document.documentElement;
      const scrollLockData = (body as any)._scrollLockData;
      
      if (scrollLockData) {
        body.style.overflow = scrollLockData.originalBodyOverflow || '';
        body.style.height = scrollLockData.originalBodyHeight || '';
        html.style.overflow = scrollLockData.originalHtmlOverflow || '';
        
        if (scrollLockData.scrollY) {
          window.scrollTo(0, scrollLockData.scrollY);
        }
        
        delete (body as any)._scrollLockData;
      }
    };
  }, [open]);
  
  // Handle field change - sync with react-hook-form
  const handleChange = useCallback((name: string, value: any) => {
    if (name in profileSchema.shape) {
      setValue(name as keyof ProfileFormData, value, { shouldDirty: true });
    }
  }, [setValue]);
  
  // Handle custom colors change
  const handleCustomColorsChange = useCallback((colors: CustomColorsType) => {
    setValue('custom_colors', colors, { shouldDirty: true });
  }, [setValue]);
  
  // Handle close with protection
  const handleClose = useCallback(() => {
    if (hasUnsavedChanges) {
      setShowConfirmClose(true);
    } else {
      onClose();
    }
  }, [hasUnsavedChanges, onClose]);

  // Handle click outside modal
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      if (hasUnsavedChanges) {
        setShowConfirmClose(true);
      } else {
        onClose();
      }
    }
  }, [hasUnsavedChanges, onClose]);
  
  // Confirm close without saving
  const confirmClose = useCallback(() => {
    setShowConfirmClose(false);
    onClose();
  }, [onClose]);
  
  // Cancel close
  const cancelClose = useCallback(() => {
    setShowConfirmClose(false);
  }, []);
  
  // Handle save with form validation
  const onSubmit = useCallback((data: ProfileFormData) => {
    // Merge validated data with additional arrays
    const updatedProfile = {
      ...data,
      preferredGenres: data.preferred_genres,
      favoriteConsoles: data.favorite_consoles,
      avatarUrl: data.avatar_url,
      bannerUrl: data.banner_url,
      customColors: data.custom_colors,
      gamingProfiles: gamingProfiles,
      socialMedia: socialMedia
    };

    onSave(updatedProfile);
    setHasUnsavedChanges(false);
  }, [gamingProfiles, socialMedia, onSave]);
  
  // Add gaming genre
  const addGenre = useCallback(() => {
    if (newGenre.trim() && 
        newGenre.length <= 12 && 
        (watchedValues.preferred_genres?.length || 0) < 6 && 
        !watchedValues.preferred_genres?.includes(newGenre.trim())) {
      setValue('preferred_genres', [...(watchedValues.preferred_genres || []), newGenre.trim()], { shouldDirty: true });
      setNewGenre('');
    }
  }, [newGenre, watchedValues.preferred_genres, setValue]);
  
  // Remove gaming genre
  const removeGenre = useCallback((genre: string) => {
    setValue('preferred_genres', watchedValues.preferred_genres?.filter((g: string) => g !== genre), { shouldDirty: true });
  }, [watchedValues.preferred_genres, setValue]);
  

  
  // Add gaming profile
  const addGamingProfile = useCallback(() => {
    if (tempGamingProfile.platform && tempGamingProfile.username) {
      // Validate platform value
      const validPlatforms = ['steam', 'xbox', 'playstation', 'nintendo', 'epic', 'origin', 'uplay'] as const;
      const platform = tempGamingProfile.platform as typeof validPlatforms[number];

      if (!validPlatforms.includes(platform)) {
        console.error('Invalid gaming platform:', tempGamingProfile.platform);
        alert('Invalid platform selected. Please try again.');
        return;
      }

      const newProfile: GamingProfile = {
        platform: platform,
        username: tempGamingProfile.username.trim(),
        url: tempGamingProfile.url?.trim() || ''
      };
      setGamingProfiles(prev => [...prev, newProfile]);
      setTempGamingProfile({});
      setHasUnsavedChanges(true); // Enable save button when gaming profile is added
    }
  }, [tempGamingProfile]);
  
  // Remove gaming profile
  const removeGamingProfile = useCallback((index: number) => {
    setGamingProfiles(prev => prev.filter((_, i) => i !== index));
    setHasUnsavedChanges(true); // Enable save button when gaming profile is removed
  }, []);
  
  // Add social media profile
  const addSocialProfile = useCallback(() => {
    if (tempSocialProfile.platform && tempSocialProfile.username) {
      // Validate platform value
      const validPlatforms = ['twitter', 'facebook', 'instagram', 'youtube', 'twitch', 'github', 'linkedin', 'discord', 'reddit', 'tiktok'] as const;
      const platform = tempSocialProfile.platform as typeof validPlatforms[number];

      if (!validPlatforms.includes(platform)) {
        console.error('Invalid social media platform:', tempSocialProfile.platform);
        alert('Invalid platform selected. Please try again.');
        return;
      }

      const newProfile: SocialMediaProfile = {
        platform: platform,
        username: tempSocialProfile.username.trim(),
        url: tempSocialProfile.url?.trim() || ''
      };

      setSocialMedia(prev => [...prev, newProfile]);
      setTempSocialProfile({});
      setHasUnsavedChanges(true); // Enable save button when social profile is added
    }
  }, [tempSocialProfile]);
  
  // Remove social media profile
  const removeSocialProfile = useCallback((index: number) => {
    setSocialMedia(prev => prev.filter((_, i) => i !== index));
    setHasUnsavedChanges(true); // Enable save button when social profile is removed
  }, []);
  
  // Get platform icon component
  const getPlatformIcon = useCallback((platform: string) => {
    const platformOption = platformOptions.find(p => p.name.toLowerCase().includes(platform.toLowerCase()));
    if (!platformOption) return <Gamepad2 className="h-5 w-5" />;
    const IconComponent = platformOption.icon;
    return <IconComponent className="h-5 w-5" />;
  }, []);
  
  // Get social media icon component
  const getSocialIcon = useCallback((platform: string) => {
    const socialOption = socialMediaOptions.find(s => s.id === platform.toLowerCase() || platform.toLowerCase().includes(s.id));
    if (!socialOption) return <ChatSquareLikeIcon className="h-5 w-5" />;
    
    const IconComponent = socialOption.icon;
    return <IconComponent className="h-5 w-5" />;
  }, []);
  
  // Handle section change
  const handleSectionChange = useCallback((sectionId: string) => {
    setActiveSection(sectionId);
    if (isMobile) {
      setIsMobileNavOpen(false);
    }
  }, [isMobile]);

  // Handle emoji selection
  const handleEmojiSelect = useCallback((emoji: string) => {
    const currentBio = watchedValues.bio || '';
    setValue('bio', currentBio + emoji, { shouldDirty: true });
  }, [watchedValues.bio, setValue]);

  // Render section content
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'basic':
        return (
          <motion.div
            className="edit-section-content edit-fade-in"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="edit-section-header">
              <div className="edit-section-header-content">
                <h3><CodeTitle>Basic Profile Info</CodeTitle></h3>
                <p className="edit-section-subtitle">Tell other gamers who you are and what you're about</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="edit-section-close"
                title="Close Modal"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            <div className="edit-form-group">
              <div className="flex items-center justify-between mb-2">
                <label className="edit-form-label">
                  <Info className="h-4 w-4" />
                  About Me
                </label>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">
                    {(watchedValues.bio || '').length}/500
                  </span>
                  <button
                    type="button"
                    onClick={() => setIsEmojiWidgetOpen(true)}
                    className="edit-emoji-button-trigger"
                    title="Add emoji"
                  >
                    😊
                  </button>
                </div>
              </div>
              <textarea
                placeholder="Tell the gaming community about yourself..."
                value={watchedValues.bio || ''}
                onChange={(e) => setValue('bio', e.target.value, { shouldDirty: true })}
                className="edit-input edit-textarea"
                maxLength={500}
                style={{ whiteSpace: 'pre-wrap' }}
              />
              {errors.bio && (
                <span className="text-red-500 text-sm mt-1">{errors.bio.message}</span>
              )}
            </div>

            <ImageUploadCustomization
              profileURL={watchedValues.avatar_url}
              bannerUrl={watchedValues.banner_url}
              onProfileChange={(url: string) => setValue('avatar_url', url, { shouldDirty: true })}
              onBannerChange={(url: string) => setValue('banner_url', url, { shouldDirty: true })}
              userId={profile.id}
            />
          </motion.div>
        );
      case 'gaming':
        return (
          <motion.div
            className="edit-section-content edit-fade-in"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="edit-section-header">
              <div className="edit-section-header-content">
                <h3><CodeTitle>Gaming Preferences</CodeTitle></h3>
                <p className="edit-section-subtitle">Tell us what games and platforms you enjoy</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="edit-section-close"
                title="Close Modal"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            
            {/* Preferred Genres */}
            <div className="edit-form-group">
              <label className="edit-form-label primary">Preferred Genres</label>
              <p className="edit-form-label mb-3">Up to 6 genres, max 12 characters each</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {watchedValues.preferred_genres && watchedValues.preferred_genres.length > 0 ? (
                  watchedValues.preferred_genres.map((genre) => (
                    <div key={genre} className="edit-badge">
                      {genre}
                      <button 
                        onClick={() => removeGenre(genre)}
                        className="edit-badge-remove"
                      >
                        ×
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="edit-empty-state">No genres added</div>
                )}
              </div>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <input
                    placeholder="Add gaming genre..."
                    value={newGenre}
                    onChange={(e) => setNewGenre(e.target.value.slice(0, 12))}
                    maxLength={12}
                    className="edit-input"
                  />
                </div>
                <button 
                  type="button" 
                  onClick={addGenre}
                  disabled={!newGenre || (watchedValues.preferred_genres?.length || 0) >= 6}
                  className="edit-button secondary"
                >
                  Add Genre
                </button>
              </div>
              
              <div className="flex justify-between items-center mt-2">
                <span className="edit-form-label">
                  {watchedValues.preferred_genres?.length || 0}/6 genres
                </span>
                <span className="edit-form-label">
                  {newGenre.length}/12 characters
                </span>
              </div>
            </div>

            {/* Gaming Profiles */}
            <div className="edit-form-group">
              <label className="edit-form-label primary">Gaming Profiles</label>
              
              <div className="space-y-3">
                {gamingProfiles.length > 0 ? (
                  gamingProfiles.map((profile, index) => (
                    <div key={`${profile.platform}-${profile.username}-${index}`} className="edit-profile-card">
                      <div className="edit-profile-card-content">
                        <div className="edit-profile-card-icon">
                          {getPlatformIcon(profile.platform)}
                        </div>
                        <div className="edit-profile-card-info">
                          <h4>{profile.username}</h4>
                          <p>{profile.platform}</p>
                        </div>
                      </div>
                      <button 
                        onClick={() => removeGamingProfile(index)}
                        className="edit-profile-card-remove edit-button ghost"
                        aria-label={`Remove ${profile.platform} profile`}
                      >
                        <Trash className="h-4 w-4" />
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="edit-empty-state">No gaming profiles added</div>
                )}
                
                <div className="edit-add-profile-section">
                  <h4 className="edit-add-profile-title">Add Gaming Profile</h4>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="edit-form-label">Platform</label>
                      <select
                        value={tempGamingProfile.platform || ''}
                        onChange={(e) => setTempGamingProfile({...tempGamingProfile, platform: e.target.value})}
                        className="edit-select"
                        aria-label="Select Gaming Platform"
                      >
                        <option value="">Select platform</option>
                        {platformOptions.map(platform => (
                          <option key={platform.id} value={platform.id}>
                            {platform.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="edit-form-label">Username</label>
                      <input
                        placeholder="Your username on this platform"
                        value={tempGamingProfile.username || ''}
                        onChange={(e) => setTempGamingProfile({...tempGamingProfile, username: e.target.value})}
                        className="edit-input"
                      />
                    </div>
                    
                    <div>
                      <label className="edit-form-label">Profile URL (Optional)</label>
                      <input
                        placeholder="https://..."
                        value={tempGamingProfile.url || ''}
                        onChange={(e) => setTempGamingProfile({...tempGamingProfile, url: e.target.value})}
                        className="edit-input"
                      />
                    </div>
                    
                    <button 
                      type="button" 
                      onClick={addGamingProfile}
                      disabled={!tempGamingProfile.platform || !tempGamingProfile.username}
                      className="edit-button primary w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Gaming Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );
      case 'social':
        return (
          <motion.div
            className="edit-section-content edit-fade-in"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="edit-section-header">
              <div className="edit-section-header-content">
                <h3><CodeTitle>Social Media Profiles</CodeTitle></h3>
                <p className="edit-section-subtitle">Let others find and follow you across platforms</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="edit-section-close"
                title="Close Modal"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            
            <div className="edit-form-group">
              <label className="edit-form-label primary">Social Media Profiles</label>
              
              <div className="space-y-3">
                {socialMedia.length > 0 ? (
                  socialMedia.map((profile, index) => (
                    <div key={`${profile.platform}-${profile.username}-${index}`} className="edit-profile-card">
                      <div className="edit-profile-card-content">
                        <div className="edit-profile-card-icon">
                          {getSocialIcon(profile.platform)}
                        </div>
                        <div className="edit-profile-card-info">
                          <h4>{profile.username}</h4>
                          <p>{profile.platform}</p>
                        </div>
                      </div>
                      <button 
                        onClick={() => removeSocialProfile(index)}
                        className="edit-profile-card-remove edit-button ghost"
                        aria-label={`Remove ${profile.platform} profile`}
                      >
                        <Trash className="h-4 w-4" />
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="edit-empty-state">No social profiles added</div>
                )}
                
                <div className="edit-add-profile-section">
                  <h4 className="edit-add-profile-title">Add Social Media</h4>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="edit-form-label">Platform</label>
                      <select
                        value={tempSocialProfile.platform || ''}
                        onChange={(e) => setTempSocialProfile({...tempSocialProfile, platform: e.target.value})}
                        className="edit-select"
                        aria-label="Select Social Media Platform"
                      >
                        <option value="">Select platform</option>
                        {socialMediaOptions.map(platform => (
                          <option key={platform.id} value={platform.id}>
                            {platform.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="edit-form-label">Username Handle</label>
                      <input
                        placeholder="Your username or handle"
                        value={tempSocialProfile.username || ''}
                        onChange={(e) => setTempSocialProfile({...tempSocialProfile, username: e.target.value})}
                        className="edit-input"
                      />
                    </div>
                    
                    <div>
                      <label className="edit-form-label">Profile URL (Optional)</label>
                      <input
                        placeholder="https://..."
                        value={tempSocialProfile.url || ''}
                        onChange={(e) => setTempSocialProfile({...tempSocialProfile, url: e.target.value})}
                        className="edit-input"
                      />
                    </div>
                    
                    <button 
                      type="button" 
                      onClick={addSocialProfile}
                      disabled={!tempSocialProfile.platform || !tempSocialProfile.username}
                      className="edit-button primary w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Social Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );
      case 'appearance':
        return (
          <motion.div
            className="edit-section-content edit-fade-in"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="edit-section-header">
              <div className="edit-section-header-content">
                <h3><CodeTitle>Appearance Settings</CodeTitle></h3>
                <p className="edit-section-subtitle">Choose a theme or create your own custom colors</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="edit-section-close"
                title="Close Modal"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            
            {/* Enhanced Theme Selector with Custom Colors */}
            <EnhancedThemePicker
              value={watchedValues.theme || defaultTheme}
              onChange={(themeId) => handleChange('theme', themeId)}
            />
          </motion.div>
        );
      default:
        return (
          <motion.div
            className="edit-section-content edit-fade-in"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
          >
            <div className="edit-section-header">
              <div className="edit-section-header-content">
                <h3><CodeTitle>Basic Profile Info</CodeTitle></h3>
                <p className="edit-section-subtitle">Tell other gamers who you are and what you're about</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="edit-section-close"
                title="Close Modal"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            <div className="edit-form-group">
              <div className="flex items-center justify-between mb-2">
                <label className="edit-form-label">
                  <Info className="h-4 w-4" />
                  About Me
                </label>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">
                    {(watchedValues.bio || '').length}/500
                  </span>
                  <button
                    type="button"
                    onClick={() => setIsEmojiWidgetOpen(true)}
                    className="edit-emoji-button-trigger"
                    title="Add emoji"
                  >
                    😊
                  </button>
                </div>
              </div>
              <textarea
                placeholder="Tell the gaming community about yourself..."
                value={watchedValues.bio || ''}
                onChange={(e) => setValue('bio', e.target.value, { shouldDirty: true })}
                className="edit-input edit-textarea"
                maxLength={500}
                style={{ whiteSpace: 'pre-wrap' }}
              />
              {errors.bio && (
                <span className="text-red-500 text-sm mt-1">{errors.bio.message}</span>
              )}
            </div>

            <ImageUploadCustomization
              profileURL={watchedValues.avatar_url}
              bannerUrl={watchedValues.banner_url}
              onProfileChange={(url: string) => setValue('avatar_url', url, { shouldDirty: true })}
              onBannerChange={(url: string) => setValue('banner_url', url, { shouldDirty: true })}
              userId={profile.id}
            />
          </motion.div>
        );
    }
  };
  
  return (
    <>
      {/* Confirmation dialog */}
      <AnimatePresence>
        {showConfirmClose && (
          <ConfirmationDialog 
            open={showConfirmClose}
            onConfirm={confirmClose}
            onCancel={cancelClose}
          />
        )}
      </AnimatePresence>
      
      <AnimatePresence>
        {open && (
          <motion.div
            className="edit-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleOverlayClick}
          >
            <motion.div
              className="edit-modal-container"
              initial={{ opacity: 0, scale: 0.9, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              onClick={(e) => e.stopPropagation()}
            >


              {/* Mobile Navigation */}
              {isMobile && isMobileNavOpen && (
                <NavigationSidebar
                  activeSection={activeSection}
                  onSectionChange={handleSectionChange}
                  sections={navigationSections}
                  isMobile={true}
                  onClose={handleClose}
                  onSave={handleSubmit(onSubmit)}
                  hasUnsavedChanges={hasUnsavedChanges}
                />
              )}

              <div className="edit-modal-body">
                {/* Desktop Sidebar Navigation */}
                {!isMobile && (
                  <NavigationSidebar
                    activeSection={activeSection}
                    onSectionChange={handleSectionChange}
                    sections={navigationSections}
                    isMobile={false}
                    onClose={handleClose}
                    onSave={handleSubmit(onSubmit)}
                    hasUnsavedChanges={hasUnsavedChanges}
                  />
                )}

                {/* Content Area */}
                <div className="edit-modal-content edit-modal-scrollbar">
                  <AnimatePresence mode="wait">
                    {renderSectionContent()}
                  </AnimatePresence>
                </div>
              </div>


            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Emoji Widget */}
      <EmojiWidget
        onEmojiSelect={handleEmojiSelect}
        isOpen={isEmojiWidgetOpen}
        onClose={() => setIsEmojiWidgetOpen(false)}
      />
    </>
  );
};

export default EditProfileModal;