'use client';

import { GameData, GameStats as GameStatsType } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Star, 
  Users, 
  Eye, 
  Heart, 
  TrendingUp, 
  Monitor, 
  Calendar,
  Clock
} from 'lucide-react';

interface GameStatsProps {
  game: GameData;
  stats: GameStatsType;
}

export default function GameStats({ game, stats }: GameStatsProps) {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getActivityLevel = (count: number) => {
    if (count >= 20) return { level: 'Very High', color: 'text-green-400', progress: 100 };
    if (count >= 10) return { level: 'High', color: 'text-blue-400', progress: 75 };
    if (count >= 5) return { level: 'Moderate', color: 'text-yellow-400', progress: 50 };
    if (count >= 1) return { level: 'Low', color: 'text-orange-400', progress: 25 };
    return { level: 'None', color: 'text-gray-400', progress: 0 };
  };

  const activity = getActivityLevel(stats.recent_activity_count);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Community Rating - Enhanced Design */}
      <Card className="p-6 bg-gradient-to-br from-blue-900/40 to-blue-800/40 border border-blue-500/30 rounded-lg hover:border-blue-400/50 transition-all duration-300">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-3 bg-blue-600/30 rounded-xl shadow-lg">
            <Star className="w-6 h-6 text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-white text-sm">Community Rating</h3>
            <p className="text-xs text-blue-300/80">User Reviews</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-white">
              {stats.average_rating ? stats.average_rating.toFixed(1) : 'N/A'}
            </span>
            {stats.average_rating && (
              <span className="text-sm text-gray-400">/100</span>
            )}
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <Users className="w-4 h-4 text-blue-400" />
            <span className="text-gray-300">
              {stats.total_reviews} review{stats.total_reviews !== 1 ? 's' : ''}
            </span>
          </div>

          {stats.average_rating && (
            <Progress 
              value={stats.average_rating} 
              className="h-2 bg-gray-700"
              style={{
                background: `linear-gradient(to right, 
                  ${stats.average_rating >= 80 ? '#10b981' : 
                    stats.average_rating >= 60 ? '#f59e0b' : '#ef4444'} 
                  ${stats.average_rating}%, 
                  #374151 ${stats.average_rating}%)`
              }}
            />
          )}
        </div>
      </Card>

      {/* Performance Data - Enhanced Design */}
      <Card className="p-6 bg-gradient-to-br from-green-900/40 to-emerald-800/40 border border-green-500/30 rounded-lg hover:border-green-400/50 transition-all duration-300">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-3 bg-green-600/30 rounded-xl shadow-lg">
            <Monitor className="w-6 h-6 text-green-400" />
          </div>
          <div>
            <h3 className="font-semibold text-white text-sm">Performance Reports</h3>
            <p className="text-xs text-green-300/80">Hardware Data</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-white">
              {formatNumber(stats.total_performance_surveys)}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <TrendingUp className="w-4 h-4 text-green-400" />
            <span className="text-gray-300">
              {stats.platforms_count} platform{stats.platforms_count !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                style={{ 
                  width: `${Math.min(100, (stats.total_performance_surveys / 50) * 100)}%` 
                }}
              />
            </div>
            <span className="text-xs text-gray-400">
              {stats.total_performance_surveys >= 50 ? 'Excellent' : 
               stats.total_performance_surveys >= 20 ? 'Good' : 
               stats.total_performance_surveys >= 10 ? 'Fair' : 'Limited'} data
            </span>
          </div>
        </div>
      </Card>

      {/* Community Engagement - Enhanced Design */}
      <Card className="p-6 bg-gradient-to-br from-purple-900/40 to-purple-800/40 border border-purple-500/30 rounded-lg hover:border-purple-400/50 transition-all duration-300">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-3 bg-purple-600/30 rounded-xl shadow-lg">
            <Heart className="w-6 h-6 text-purple-400" />
          </div>
          <div>
            <h3 className="font-semibold text-white text-sm">Engagement</h3>
            <p className="text-xs text-purple-300/80">Community Interest</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-white">
              {formatNumber(stats.total_likes)}
            </span>
            <span className="text-sm text-gray-400">likes</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <Eye className="w-4 h-4 text-purple-400" />
            <span className="text-gray-300">
              {formatNumber(stats.total_views)} views
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400">Popularity</span>
            <span className={`text-xs font-medium ${
              stats.total_views > 10000 ? 'text-green-400' :
              stats.total_views > 1000 ? 'text-yellow-400' : 'text-gray-400'
            }`}>
              {stats.total_views > 10000 ? 'High' :
               stats.total_views > 1000 ? 'Medium' : 'Growing'}
            </span>
          </div>
        </div>
      </Card>

      {/* Recent Activity - Enhanced Design */}
      <Card className="p-6 bg-gradient-to-br from-orange-900/40 to-red-900/40 border border-orange-500/30 rounded-lg hover:border-orange-400/50 transition-all duration-300">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-3 bg-orange-600/30 rounded-xl shadow-lg">
            <Calendar className="w-6 h-6 text-orange-400" />
          </div>
          <div>
            <h3 className="font-semibold text-white text-sm">Recent Activity</h3>
            <p className="text-xs text-orange-300/80">Last 30 Days</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-white">
              {stats.recent_activity_count}
            </span>
            <span className="text-sm text-gray-400">reviews</span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <Clock className="w-4 h-4 text-orange-400" />
            <span className={`${activity.color} font-medium`}>
              {activity.level} Activity
            </span>
          </div>

          <Progress 
            value={activity.progress} 
            className="h-2 bg-gray-700"
          />
          
          {stats.recent_activity_count > 0 && (
            <p className="text-xs text-gray-400">
              This game is actively being reviewed by the community
            </p>
          )}
        </div>
      </Card>

      {/* IGDB Rating (if available) - Enhanced Design */}
      {game.aggregated_rating && (
        <Card className="p-6 bg-gradient-to-br from-yellow-900/40 to-amber-800/40 border border-yellow-500/30 rounded-lg md:col-span-2 lg:col-span-4 hover:border-yellow-400/50 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-yellow-600/30 rounded-xl shadow-lg">
                <Star className="w-6 h-6 text-yellow-400" />
              </div>
              <div>
                <h3 className="font-semibold text-white text-sm">IGDB Rating</h3>
                <p className="text-xs text-yellow-300/80">Industry & Press Scores</p>
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold text-white">
                  {game.aggregated_rating.toFixed(1)}
                </span>
                <span className="text-sm text-gray-400">/100</span>
              </div>
              <p className="text-sm text-gray-400">
                {game.aggregated_rating_count} ratings
              </p>
            </div>
          </div>
          
          <div className="mt-4">
            <Progress 
              value={game.aggregated_rating} 
              className="h-3 bg-gray-700"
              style={{
                background: `linear-gradient(to right, 
                  ${game.aggregated_rating >= 80 ? '#eab308' : 
                    game.aggregated_rating >= 60 ? '#f59e0b' : '#ef4444'} 
                  ${game.aggregated_rating}%, 
                  #374151 ${game.aggregated_rating}%)`
              }}
            />
          </div>
        </Card>
      )}
    </div>
  );
}