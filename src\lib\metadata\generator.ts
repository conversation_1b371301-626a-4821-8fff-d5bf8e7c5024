/**
 * Automated SEO and Social Media Metadata Generator
 * Integrates with CriticalPixel review form data to generate optimized metadata
 */

export interface ReviewData {
  // Basic Info
  reviewTitle: string;
  gameName: string;
  slug: string;
  authorName?: string;
  language: string;
  playedOn: string;
  datePlayed: string;

  // IGDB Data
  igdbSummary?: string;
  igdbDevelopers?: string[];
  igdbPublishers?: string[];
  igdbGameEngines?: string[];
  igdbPlayerPerspectives?: string[];
  igdbTimeToBeatNormally?: number;
  igdbTimeToBeatCompletely?: number;
  igdbAggregatedRating?: number;
  igdbAggregatedRatingCount?: number;
  igdbCoverUrl?: string;
  releaseDate?: Date;
  
  // Content
  reviewContentLexical?: string;
  
  // Ratings
  scoringCriteria?: Array<{
    id: string;
    name: string;
    score: number;
  }>;
  overallScore?: number;
  
  // Categories
  selectedPlatforms?: Set<string> | string[];
  selectedGenres?: Set<string> | string[];
  
  // Media
  mainImageUrl?: string;
  galleryImageUrls?: string[];
  videoUrl?: string;
  
  // Current SEO (for enhancement)
  metaTitle?: string;
  metaDescription?: string;
  focusKeyword?: string;
  reviewTags?: string[];
}

export interface GeneratedMetadata {
  title: string;
  description: string;
  keywords: string[];
  focusKeyword: string;
  socialTitle: string;
  socialDescription: string;
  confidence: number; // 0-100 confidence score
  suggestions: string[];
}

export interface SocialMetaTags {
  openGraph: {
    title: string;
    description: string;
    image?: string;
    type: string;
    siteName: string;
    url?: string;
  };
  twitter: {
    card: string;
    title: string;
    description: string;
    image?: string;
    site?: string;
  };
  discord: {
    title: string;
    description: string;
    image?: string;
    themeColor: string;
  };
}

export class MetadataGenerator {
  private readonly maxTitleLength = 60;
  private readonly maxDescriptionLength = 160;
  private readonly maxSocialTitleLength = 70;
  private readonly maxSocialDescriptionLength = 200;

  /**
   * Generate comprehensive SEO metadata from review data
   */
  async generateMetadata(reviewData: ReviewData): Promise<GeneratedMetadata> {
    const startTime = Date.now();
    
    try {
      // Extract content insights
      const contentInsights = await this.analyzeContent(reviewData);
      
      // Generate optimized title
      const title = this.generateTitle(reviewData, contentInsights);
      
      // Generate optimized description
      const description = this.generateDescription(reviewData, contentInsights);
      
      // Extract and generate keywords
      const keywords = this.generateKeywords(reviewData, contentInsights);
      
      // Determine focus keyword
      const focusKeyword = this.generateFocusKeyword(reviewData, contentInsights);
      
      // Generate social media optimized versions
      const socialTitle = this.generateSocialTitle(reviewData, contentInsights);
      const socialDescription = this.generateSocialDescription(reviewData, contentInsights);
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(reviewData, contentInsights);
      
      // Generate improvement suggestions
      const suggestions = this.generateSuggestions(reviewData, contentInsights);
      
      const processingTime = Date.now() - startTime;
      console.log(`[MetadataGenerator] Generated metadata in ${processingTime}ms`);
      
      return {
        title,
        description,
        keywords,
        focusKeyword,
        socialTitle,
        socialDescription,
        confidence,
        suggestions
      };
    } catch (error) {
      console.error('[MetadataGenerator] Error generating metadata:', error);
      return this.getFallbackMetadata(reviewData);
    }
  }

  /**
   * Generate platform-specific social media meta tags
   */
  generateSocialMetaTags(reviewData: ReviewData, metadata: GeneratedMetadata): SocialMetaTags {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const reviewUrl = `${baseUrl}/reviews/view/${reviewData.slug}`;
    const imageUrl = reviewData.mainImageUrl || `${baseUrl}/api/og-image/${reviewData.slug}`;

    return {
      openGraph: {
        title: metadata.socialTitle,
        description: metadata.socialDescription,
        image: imageUrl,
        type: 'article',
        siteName: 'CriticalPixel',
        url: reviewUrl
      },
      twitter: {
        card: 'summary_large_image',
        title: metadata.socialTitle,
        description: metadata.socialDescription,
        image: imageUrl,
        site: '@CriticalPixel'
      },
      discord: {
        title: metadata.socialTitle,
        description: metadata.socialDescription,
        image: imageUrl,
        themeColor: '#8B5CF6' // Violet theme color
      }
    };
  }

  /**
   * Analyze review content to extract insights
   */
  private async analyzeContent(reviewData: ReviewData): Promise<any> {
    const insights = {
      contentLength: 0,
      readabilityScore: 0,
      sentimentScore: 0,
      keyPhrases: [] as string[],
      gameAspects: [] as string[],
      platforms: [] as string[],
      genres: [] as string[]
    };

    // Analyze Lexical content if available
    if (reviewData.reviewContentLexical) {
      try {
        const lexicalData = JSON.parse(reviewData.reviewContentLexical);
        const textContent = this.extractTextFromLexical(lexicalData);
        
        insights.contentLength = textContent.length;
        insights.keyPhrases = this.extractKeyPhrases(textContent);
        insights.gameAspects = this.extractGameAspects(textContent);
        insights.readabilityScore = this.calculateReadability(textContent);
        insights.sentimentScore = this.analyzeSentiment(textContent);
      } catch (error) {
        console.warn('[MetadataGenerator] Error parsing Lexical content:', error);
      }
    }

    // Extract platform and genre information
    insights.platforms = Array.isArray(reviewData.selectedPlatforms) 
      ? reviewData.selectedPlatforms 
      : Array.from(reviewData.selectedPlatforms || []);
    
    insights.genres = Array.isArray(reviewData.selectedGenres)
      ? reviewData.selectedGenres
      : Array.from(reviewData.selectedGenres || []);

    return insights;
  }

  /**
   * Extract plain text from Lexical editor JSON structure
   */
  private extractTextFromLexical(lexicalData: any): string {
    const extractText = (node: any): string => {
      if (!node) return '';
      
      if (node.text) {
        return node.text;
      }
      
      if (node.children && Array.isArray(node.children)) {
        return node.children.map(extractText).join(' ');
      }
      
      return '';
    };

    try {
      return extractText(lexicalData.root).trim();
    } catch (error) {
      console.warn('[MetadataGenerator] Error extracting text from Lexical:', error);
      return '';
    }
  }

  /**
   * Generate optimized SEO title
   */
  private generateTitle(reviewData: ReviewData, insights: any): string {
    const { gameName, reviewTitle, overallScore, authorName } = reviewData;
    const siteName = 'CriticalPixel';
    const author = authorName || 'CriticalPixel User';

    // Calculate available space for core content (leaving room for author and site)
    const authorSuffix = ` by ${author} | ${siteName}`;
    const maxCoreLength = this.maxTitleLength - authorSuffix.length;

    // Priority order for core title generation
    const coreTitleTemplates = [
      // Use custom review title if provided and fits
      reviewTitle && reviewTitle.length <= maxCoreLength ? reviewTitle : null,

      // Game name + review + score (if available)
      overallScore ? `${gameName} Review - ${this.getScoreDescription(overallScore)} (${overallScore}/100)` : null,

      // Game name + review + platform
      insights.platforms.length > 0 ? `${gameName} Review - ${insights.platforms[0]} Gaming` : null,

      // Simple game name + review
      `${gameName} Review - Critical Analysis`,

      // Shorter fallbacks
      `${gameName} Review`,
      `${gameName} Game Review`
    ];

    // Find the best fitting core title
    let coreTitle = '';
    for (const template of coreTitleTemplates) {
      if (template && template.length <= maxCoreLength) {
        coreTitle = template;
        break;
      }
    }

    // If no template fits, truncate the game name
    if (!coreTitle) {
      const maxGameNameLength = maxCoreLength - ' Review'.length;
      const truncatedGameName = gameName.length <= maxGameNameLength
        ? gameName
        : gameName.substring(0, maxGameNameLength - 3) + '...';
      coreTitle = `${truncatedGameName} Review`;
    }

    // Combine core title with author and site name
    return `${coreTitle}${authorSuffix}`;
  }

  /**
   * Generate optimized meta description
   */
  private generateDescription(reviewData: ReviewData, insights: any): string {
    const { gameName, overallScore, igdbSummary } = reviewData;
    
    const elements = [];
    
    // Start with game name and score
    if (overallScore) {
      elements.push(`${gameName} earns ${overallScore}/100 in our detailed review.`);
    } else {
      elements.push(`Comprehensive ${gameName} review covering gameplay, story, and more.`);
    }
    
    // Add platform info if available
    if (insights.platforms.length > 0) {
      elements.push(`Played on ${insights.platforms.slice(0, 2).join(' and ')}.`);
    }
    
    // Add key aspects if found
    if (insights.gameAspects.length > 0) {
      elements.push(`Analysis of ${insights.gameAspects.slice(0, 2).join(' and ')}.`);
    }
    
    // Add call to action
    elements.push('Read our full analysis.');
    
    const description = elements.join(' ');
    
    return description.length <= this.maxDescriptionLength 
      ? description 
      : description.substring(0, this.maxDescriptionLength - 3) + '...';
  }

  /**
   * Generate relevant keywords
   */
  private generateKeywords(reviewData: ReviewData, insights: any): string[] {
    const keywords = new Set<string>();
    
    // Add game name variations
    keywords.add(reviewData.gameName.toLowerCase());
    keywords.add(`${reviewData.gameName.toLowerCase()} review`);
    keywords.add(`${reviewData.gameName.toLowerCase()} game`);
    
    // Add platform keywords
    insights.platforms.forEach((platform: string) => {
      keywords.add(`${platform.toLowerCase()} games`);
      keywords.add(`${reviewData.gameName.toLowerCase()} ${platform.toLowerCase()}`);
    });
    
    // Add genre keywords
    insights.genres.forEach((genre: string) => {
      keywords.add(`${genre.toLowerCase()} games`);
      keywords.add(`${genre.toLowerCase()} review`);
    });
    
    // Add developer/publisher keywords if available
    reviewData.igdbDevelopers?.forEach(dev => {
      keywords.add(`${dev.toLowerCase()} games`);
    });
    
    // Add game aspects
    insights.gameAspects.forEach((aspect: string) => {
      keywords.add(aspect.toLowerCase());
    });
    
    // Add score-related keywords
    if (reviewData.overallScore) {
      keywords.add(`${this.getScoreDescription(reviewData.overallScore).toLowerCase()} games`);
    }
    
    return Array.from(keywords).slice(0, 15); // Limit to 15 keywords
  }

  /**
   * Generate focus keyword
   */
  private generateFocusKeyword(reviewData: ReviewData, insights: any): string {
    // Primary focus should be game name + review
    return `${reviewData.gameName.toLowerCase()} review`;
  }

  /**
   * Generate social media optimized title
   */
  private generateSocialTitle(reviewData: ReviewData, insights: any): string {
    const { gameName, overallScore, authorName } = reviewData;
    const siteName = 'CriticalPixel';
    const author = authorName || 'CriticalPixel User';

    // For social media, we want shorter titles but still include attribution
    const authorSuffix = ` by ${author} | ${siteName}`;
    const maxCoreLength = this.maxSocialTitleLength - authorSuffix.length;

    let coreTitle = '';
    if (overallScore) {
      const scoreEmoji = this.getScoreEmoji(overallScore);
      coreTitle = `${scoreEmoji} ${gameName} Review - ${overallScore}/100`;
    } else {
      coreTitle = `🎮 ${gameName} Review`;
    }

    // Truncate core title if needed
    if (coreTitle.length > maxCoreLength) {
      const maxGameNameLength = maxCoreLength - (overallScore ? ' Review - /100'.length + 2 : ' Review'.length + 2); // +2 for emoji
      const truncatedGameName = gameName.length <= maxGameNameLength
        ? gameName
        : gameName.substring(0, maxGameNameLength - 3) + '...';

      if (overallScore) {
        const scoreEmoji = this.getScoreEmoji(overallScore);
        coreTitle = `${scoreEmoji} ${truncatedGameName} Review - ${overallScore}/100`;
      } else {
        coreTitle = `🎮 ${truncatedGameName} Review`;
      }
    }

    return `${coreTitle}${authorSuffix}`;
  }

  /**
   * Generate social media optimized description
   */
  private generateSocialDescription(reviewData: ReviewData, insights: any): string {
    const elements = [];
    
    if (reviewData.overallScore) {
      elements.push(`${this.getScoreDescription(reviewData.overallScore)} gaming experience!`);
    }
    
    if (insights.platforms.length > 0) {
      elements.push(`Available on ${insights.platforms.slice(0, 2).join(' & ')}.`);
    }
    
    if (insights.gameAspects.length > 0) {
      elements.push(`Deep dive into ${insights.gameAspects.slice(0, 2).join(' and ')}.`);
    }
    
    elements.push('Read the full review! 🎯');
    
    const description = elements.join(' ');
    return description.length <= this.maxSocialDescriptionLength 
      ? description 
      : description.substring(0, this.maxSocialDescriptionLength - 3) + '...';
  }

  // Helper methods
  private getScoreDescription(score: number): string {
    if (score >= 90) return 'Legendary';
    if (score >= 80) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Average';
    if (score >= 50) return 'Below Average';
    return 'Poor';
  }

  private getScoreEmoji(score: number): string {
    if (score >= 90) return '🏆';
    if (score >= 80) return '⭐';
    if (score >= 70) return '👍';
    if (score >= 60) return '👌';
    if (score >= 50) return '😐';
    return '👎';
  }

  private extractKeyPhrases(text: string): string[] {
    // Simple keyword extraction - can be enhanced with NLP libraries
    const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
    const frequency: { [key: string]: number } = {};
    
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private extractGameAspects(text: string): string[] {
    const aspects = ['gameplay', 'graphics', 'story', 'sound', 'music', 'controls', 'performance', 'multiplayer'];
    return aspects.filter(aspect => 
      text.toLowerCase().includes(aspect)
    );
  }

  private calculateReadability(text: string): number {
    // Simple readability calculation
    const sentences = text.split(/[.!?]+/).length;
    const words = text.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    
    // Score based on average sentence length (lower is better for readability)
    return Math.max(0, Math.min(100, 100 - (avgWordsPerSentence - 15) * 2));
  }

  private analyzeSentiment(text: string): number {
    // Simple sentiment analysis - can be enhanced with proper NLP
    const positiveWords = ['great', 'excellent', 'amazing', 'fantastic', 'love', 'perfect', 'outstanding'];
    const negativeWords = ['terrible', 'awful', 'hate', 'horrible', 'worst', 'disappointing', 'frustrating'];
    
    const words = text.toLowerCase().split(/\s+/);
    let score = 50; // Neutral
    
    words.forEach(word => {
      if (positiveWords.includes(word)) score += 5;
      if (negativeWords.includes(word)) score -= 5;
    });
    
    return Math.max(0, Math.min(100, score));
  }

  private calculateConfidence(reviewData: ReviewData, insights: any): number {
    let confidence = 0;
    
    // Base confidence from available data
    if (reviewData.gameName) confidence += 20;
    if (reviewData.reviewTitle) confidence += 15;
    if (insights.contentLength > 100) confidence += 20;
    if (reviewData.overallScore) confidence += 15;
    if (insights.platforms.length > 0) confidence += 10;
    if (insights.genres.length > 0) confidence += 10;
    if (reviewData.igdbSummary) confidence += 10;
    
    return Math.min(100, confidence);
  }

  private generateSuggestions(reviewData: ReviewData, insights: any): string[] {
    const suggestions = [];
    
    if (insights.contentLength < 200) {
      suggestions.push('Consider adding more detailed content for better SEO');
    }
    
    if (insights.platforms.length === 0) {
      suggestions.push('Add platform information to improve discoverability');
    }
    
    if (insights.genres.length === 0) {
      suggestions.push('Select relevant genres to enhance categorization');
    }
    
    if (!reviewData.overallScore) {
      suggestions.push('Add an overall score to improve social media appeal');
    }
    
    if (!reviewData.mainImageUrl) {
      suggestions.push('Add a main image for better social media sharing');
    }
    
    return suggestions;
  }

  private getFallbackMetadata(reviewData: ReviewData): GeneratedMetadata {
    const siteName = 'CriticalPixel';
    const author = reviewData.authorName || 'CriticalPixel User';
    const authorSuffix = ` by ${author} | ${siteName}`;

    const baseTitle = reviewData.reviewTitle || `${reviewData.gameName} Review`;
    const title = baseTitle.length + authorSuffix.length <= this.maxTitleLength
      ? `${baseTitle}${authorSuffix}`
      : `${reviewData.gameName} Review${authorSuffix}`;

    const baseSocialTitle = `🎮 ${reviewData.gameName} Review`;
    const socialTitle = baseSocialTitle.length + authorSuffix.length <= this.maxSocialTitleLength
      ? `${baseSocialTitle}${authorSuffix}`
      : `🎮 ${reviewData.gameName}${authorSuffix}`;

    return {
      title,
      description: `Read our review of ${reviewData.gameName}. Comprehensive analysis and rating.`,
      keywords: [reviewData.gameName.toLowerCase(), 'game review', 'gaming'],
      focusKeyword: `${reviewData.gameName.toLowerCase()} review`,
      socialTitle,
      socialDescription: `Check out our detailed review of ${reviewData.gameName}!`,
      confidence: 30,
      suggestions: ['Add more review content for better optimization']
    };
  }
}

// Export singleton instance
export const metadataGenerator = new MetadataGenerator();
