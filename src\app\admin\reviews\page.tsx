'use client';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { useAuthContext } from '@/hooks/use-auth-context';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { PlusCircle, Search, ChevronLeft, ChevronRight, Flag, CheckCircle, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import { getReviewsListSecure, moderateReviewSecure, batchModerateReviewsSecure, type SecureReviewModerationData, type SecureModerationAction } from './actions';
import { ReviewActionsDropdown } from '@/components/admin/ReviewActionsDropdown';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

type ReviewStatus = 'published' | 'draft' | 'pending' | 'flagged' | 'archived';

export default function AdminReviewsPage() {
  const { user, loading, isAdmin } = useAuthContext();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [reviews, setReviews] = useState<SecureReviewModerationData[]>([]);
  const [isLoadingReviews, setIsLoadingReviews] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string[]>(['pending', 'flagged', 'published']);
  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalReviews, setTotalReviews] = useState(0);
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'flag_count'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const reviewsPerPage = 20;

  // Load reviews for moderation
  const loadReviews = async () => {
    if (!user?.id || !isAdmin) return;

    setIsLoadingReviews(true);
    try {
      const result = await getReviewsListSecure({
        status: statusFilter,
        page: currentPage,
        limit: reviewsPerPage,
        sortBy,
        sortOrder,
        search: searchTerm
      });

      setReviews(result.reviews);
      setTotalReviews(result.total);
    } catch (error) {
      toast({
        title: "Error loading reviews",
        description: "Failed to load reviews for moderation",
        variant: "destructive"
      });
    } finally {
      setIsLoadingReviews(false);
    }
  };

  useEffect(() => {
    if (!loading && isAdmin && user?.id) {
      loadReviews();
    }
  }, [user, loading, isAdmin, statusFilter, currentPage, sortBy, sortOrder, searchTerm]);


  const getStatusBadgeVariant = (status: ReviewStatus): "default" | "secondary" | "outline" | "destructive" => {
    switch (status) {
      case 'published':
        return 'default'; // Theme primary (purple)
      case 'draft':
        return 'secondary'; // Theme secondary (charcoal/cyan text)
      case 'pending':
        return 'outline'; // Theme outline (default border/text)
      case 'flagged':
        return 'destructive'; // Red for flagged content
      case 'archived':
        return 'secondary'; // Gray for archived
      default:
        return 'outline';
    }
  };

  const getStatusDisplayName = (status: ReviewStatus): string => {
    switch (status) {
      case 'published': return 'Published';
      case 'draft': return 'Draft';
      case 'pending': return 'Pending Review';
      case 'flagged': return 'Flagged';
      case 'archived': return 'Archived';
      default: return status;
    }
  };

  const handleModerateReview = async (reviewId: string, action: SecureModerationAction) => {
    if (!user?.id) return;

    try {
      const result = await moderateReviewSecure(reviewId, action);

      if (result.success) {
        toast({
          title: "Review moderated successfully",
          description: `Review has been ${action.action}d`,
        });
        await loadReviews(); // Reload the list
      } else {
        toast({
          title: "Moderation failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Moderation error",
        description: "Failed to moderate review",
        variant: "destructive"
      });
    }
  };

  const handleBatchModerate = async (action: SecureModerationAction) => {
    if (!user?.id || selectedReviews.length === 0) return;

    try {
      const result = await batchModerateReviewsSecure(selectedReviews, action);

      toast({
        title: "Batch moderation completed",
        description: `Processed ${result.processed} reviews. ${result.errors.length} errors.`,
        variant: result.errors.length > 0 ? "destructive" : "default"
      });

      setSelectedReviews([]);
      await loadReviews();
    } catch (error) {
      toast({
        title: "Batch moderation failed",
        description: "Failed to process batch moderation",
        variant: "destructive"
      });
    }
  };

  const handleSelectAll = () => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([]);
    } else {
      setSelectedReviews(reviews.map(r => r.id));
    }
  };

  const handleSelectReview = (reviewId: string) => {
    setSelectedReviews(prev =>
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
  };

  // Initialize keyboard shortcuts after all functions are declared
  useKeyboardShortcuts({
    shortcuts: [
      {
        key: 'F5',
        action: loadReviews,
        description: 'Refresh reviews list'
      },
      {
        key: '/',
        action: () => {
          const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
          if (searchInput) {
            searchInput.focus();
          }
        },
        description: 'Focus search box'
      },
      {
        key: 'Escape',
        action: () => setSelectedReviews([]),
        description: 'Clear selection',
        disabled: selectedReviews.length === 0
      },
      {
        key: 'a',
        ctrlKey: true,
        shiftKey: true,
        action: handleSelectAll,
        description: 'Select all reviews'
      },
      {
        key: 'Enter',
        ctrlKey: true,
        action: () => selectedReviews.length > 0 && handleBatchModerate({ action: 'approve' }),
        description: 'Approve selected reviews',
        disabled: selectedReviews.length === 0
      }
    ],
    enableGlobal: true
  });

  return (
    <AdminLayout
      title="Content Moderation"
      description="Review, moderate, and manage game reviews"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Content Moderation' }
      ]}
    >
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <div className="flex gap-2">
          {selectedReviews.length > 0 && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchModerate({ action: 'approve' })}
                className="border-slate-600/50 hover:border-green-500/50 hover:bg-green-700/20 transition-colors font-mono"
              >
                <CheckCircle className="mr-2 h-4 w-4 text-green-400" />
                Approve ({selectedReviews.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBatchModerate({ action: 'flag' })}
                className="border-slate-600/50 hover:border-orange-500/50 hover:bg-orange-700/20 transition-colors font-mono"
              >
                <Flag className="mr-2 h-4 w-4 text-orange-400" />
                Flag ({selectedReviews.length})
              </Button>
            </div>
          )}
          <Button asChild className="bg-violet-600/80 hover:bg-violet-600 transition-colors font-mono">
            <Link href="/admin/reviews/new"><PlusCircle className="mr-2 h-5 w-5" /> Add New Review</Link>
          </Button>
          <Button asChild variant="outline" className="border-slate-600/50 hover:border-violet-500/50 transition-colors font-mono">
            <Link href="/admin/reviews/reports"><AlertTriangle className="mr-2 h-4 w-4" /> View Reports</Link>
          </Button>
        </div>
      </div>

      <Card className="shadow-lg glow-purple bg-slate-900/60 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-2xl font-mono tracking-tight">
            <span className="text-violet-400">&lt;</span>
            <span className="text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)]">Review Moderation Queue</span>
            <span className="text-violet-400">/&gt;</span>
          </CardTitle>
          <CardDescription className="text-muted-foreground font-mono text-sm">Moderate and manage game reviews that need attention.</CardDescription>
          <div className="flex flex-col md:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by title, game, or author..."
                className="pl-10 bg-slate-800/60 border-slate-600/50 focus:border-violet-500/50 transition-colors font-mono text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter.join(',')} onValueChange={(value) => setStatusFilter(value.split(','))}>
              <SelectTrigger className="w-[200px] bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                <SelectItem value="pending,flagged,published" className="font-mono text-sm">All Statuses</SelectItem>
                <SelectItem value="pending" className="font-mono text-sm">Pending Review</SelectItem>
                <SelectItem value="flagged" className="font-mono text-sm">Flagged</SelectItem>
                <SelectItem value="published" className="font-mono text-sm">Published</SelectItem>
                <SelectItem value="draft" className="font-mono text-sm">Draft</SelectItem>
                <SelectItem value="archived" className="font-mono text-sm">Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field as any);
              setSortOrder(order as any);
            }}>
              <SelectTrigger className="w-[200px] bg-slate-800/60 border-slate-600/50 font-mono text-sm">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900/95 border-slate-700/50 backdrop-blur-sm">
                <SelectItem value="created_at-desc" className="font-mono text-sm">Newest First</SelectItem>
                <SelectItem value="created_at-asc" className="font-mono text-sm">Oldest First</SelectItem>
                <SelectItem value="updated_at-desc" className="font-mono text-sm">Recently Updated</SelectItem>
                <SelectItem value="flag_count-desc" className="font-mono text-sm">Most Flagged</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table className="border-slate-700/30">
              <TableHeader>
                <TableRow className="border-slate-700/30 hover:bg-slate-800/30">
                  <TableHead className="w-[50px]">
                    <Checkbox
                      checked={selectedReviews.length === reviews.length && reviews.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="min-w-[200px] font-mono text-violet-300">Title</TableHead>
                  <TableHead className="min-w-[150px] font-mono text-violet-300">Game</TableHead>
                  <TableHead className="font-mono text-violet-300">Status</TableHead>
                  <TableHead className="font-mono text-violet-300">Author</TableHead>
                  <TableHead className="font-mono text-violet-300">Date</TableHead>
                  <TableHead className="text-center font-mono text-violet-300">Featured</TableHead>
                  <TableHead className="text-right w-[50px] font-mono text-violet-300">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reviews.length > 0 ? reviews.map((review) => (
                  <TableRow key={review.id} className="hover:bg-slate-800/20 transition-colors border-slate-700/20">
                    <TableCell>
                      <Checkbox
                        checked={selectedReviews.includes(review.id)}
                        onCheckedChange={() => handleSelectReview(review.id)}
                      />
                    </TableCell>
                    <TableCell className="font-medium font-mono">
                      {review.status === 'published' ? (
                        <Link
                          href={`/reviews/view/${review.slug}`}
                          target="_blank"
                          className="text-violet-400 hover:text-violet-300 transition-colors font-mono text-sm"
                        >
                          {review.title}
                        </Link>
                      ) : (
                        <span className="text-violet-400 font-mono text-sm">{review.title}</span>
                      )}
                    </TableCell>
                    <TableCell className="font-mono text-sm">{review.game_name}</TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(review.status)}>
                        {getStatusDisplayName(review.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{review.author_name}</TableCell>
                    <TableCell className="font-mono text-sm text-muted-foreground">
                      {review.publish_date
                        ? new Date(review.publish_date).toLocaleDateString()
                        : new Date(review.created_at).toLocaleDateString()
                      }
                    </TableCell>
                    <TableCell className="text-center">
                      <span className={`inline-block w-3 h-3 rounded-full ${review.is_featured ? 'bg-yellow-500' : 'bg-muted-foreground/30'}`}></span>
                    </TableCell>
                    <TableCell className="text-right">
                      <ReviewActionsDropdown
                        review={review}
                        onModerate={async (action) => await handleModerateReview(review.id, action)}
                        compact={true}
                      />
                    </TableCell>
                  </TableRow>
                )) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center text-muted-foreground py-8 font-mono text-sm">
                      No reviews found matching your search criteria.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between pt-4">
          <span className="text-sm text-muted-foreground font-mono">
            Showing <strong className="text-violet-400">{reviews.length}</strong> of <strong className="text-violet-400">{totalReviews}</strong> reviews
            {selectedReviews.length > 0 && (
              <span className="ml-2 text-violet-400">
                ({selectedReviews.length} selected)
              </span>
            )}
          </span>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              className="border-slate-600/50 hover:border-violet-500/50 hover:bg-slate-700/30 transition-colors"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Previous page</span>
            </Button>
            <span className="flex items-center px-3 text-sm font-mono text-muted-foreground">
              Page <span className="text-violet-400">{currentPage}</span> of <span className="text-violet-400">{Math.ceil(totalReviews / reviewsPerPage)}</span>
            </span>
            <Button
              variant="outline"
              size="icon"
              disabled={currentPage >= Math.ceil(totalReviews / reviewsPerPage)}
              onClick={() => setCurrentPage(prev => prev + 1)}
              className="border-slate-600/50 hover:border-violet-500/50 hover:bg-slate-700/30 transition-colors"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Next page</span>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </AdminLayout>
  );
}


