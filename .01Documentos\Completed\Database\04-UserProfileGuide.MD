# 🚀 Plano de Implementação Completa - Sistema de Perfil de Usuário

## 📋 **Visão Geral do Projeto**
**Objetivo:** Implementar um sistema completo de gerenciamento de perfis de usuário com Supabase e Next.js 
**Duração Estimada:** 2-3 dias (16-24 horas de desenvolvimento)
**Prioridade:** ALTA
**Status Atual:** 35% → Meta: 100%

## 🎯 **Arquitectura do Sistema**

### **Stack Tecnológica**
- **Frontend:** Next.js 14+ (App Router)
- **Backend:** Supabase (PostgreSQL + Storage + Auth)
- **Validação:** Zod Schema Validation
- **UI:** Components com TypeScript
- **Segurança:** Row Level Security (RLS)
- **Storage:** Supabase Storage com políticas de acesso

### **Estrutura de Arquivos**
```
src/
├── app/
│   ├── u/
│   │   ├── [slug]/
│   │   │   └── page.tsx (Página pública do perfil)
│   │   └── actions.ts (Server Actions)
│   └── api/ (fallback routes se necessário)
├── components/
│   └── userprofile/
│       ├── GamerCard.tsx
│       ├── EditProfileModal.tsx
│       ├── PrivacySettings.tsx
│       ├── UsernameValidator.tsx
│       └── ImageUpload.tsx
├── lib/
│   ├── supabase/
│   │   ├── client.ts
│   │   └── server.ts
│   ├── validations/
│   │   └── profile.ts (Zod schemas)
│   └── types/
│       └── profile.ts (TypeScript types)
└── utils/
    ├── image-optimization.ts
    └── profile-helpers.ts
```

## 📅 **Cronograma Detalhado de Implementação**

### **Dia 1: Fundação e Core Services (8 horas)**

#### **Manhã (4 horas) - Setup e Estrutura Base**

**Hora 1-2: Configuração Inicial**
- [ ] **1.1** Configurar variáveis de ambiente do Supabase
- [ ] **1.2** Criar clientes Supabase (server/client)
- [ ] **1.3** Definir tipos TypeScript para UserProfile
- [ ] **1.4** Configurar Zod schemas para validação

**Hora 3-4: Database e Storage Setup**
- [ ] **1.5** Revisar e ajustar schema da tabela `profiles`
- [ ] **1.6** Configurar buckets do Supabase Storage (avatars, banners)
- [ ] **1.7** Implementar políticas RLS para storage
- [ ] **1.8** Testar conexões e permissões

#### **Tarde (4 horas) - Core Profile Services**

**Hora 5-6: Profile Actions Fundamentais**
- [ ] **1.9** Implementar `getUserProfileByUsername()` (Server Action)
- [ ] **1.10** Implementar `updateUserProfile()` com validação Zod
- [ ] **1.11** Implementar `checkUsernameAvailability()`
- [ ] **1.12** Criar funções de validação de perfil

**Hora 7-8: Sistema de Username**
- [ ] **1.13** Implementar `generateUsernameSuggestions()`
- [ ] **1.14** Criar `changeUsername()` com slug update
- [ ] **1.15** Implementar validação de username em tempo real
- [ ] **1.16** Testes das funcionalidades core

### **Dia 2: Interface e Upload de Imagens (8 horas)**

#### **Manhã (4 horas) - Components de Interface**

**Hora 1-2: Página Pública do Perfil**
- [ ] **2.1** Implementar `/app/u/[slug]/page.tsx`
- [ ] **2.2** Adicionar geração de metadata SEO
- [ ] **2.3** Implementar tratamento 404 para perfis inexistentes
- [ ] **2.4** Integrar com server-side data fetching

**Hora 3-4: GamerCard Component**
- [ ] **2.5** Enhancear `GamerCard.tsx` com dados reais
- [ ] **2.6** Implementar privacy-aware data display
- [ ] **2.7** Adicionar suporte a temas customizáveis
- [ ] **2.8** Garantir responsividade mobile

#### **Tarde (4 horas) - Sistema de Upload de Imagens**

**Hora 5-6: Upload de Avatar**
- [ ] **2.9** Implementar `uploadAvatar()` com validação
- [ ] **2.10** Adicionar otimização automática de imagens
- [ ] **2.11** Configurar limpeza de imagens antigas
- [ ] **2.12** Implementar preview em tempo real

**Hora 7-8: Upload de Banner**
- [ ] **2.13** Implementar `uploadBanner()` com aspect ratio
- [ ] **2.14** Adicionar cropping automático
- [ ] **2.15** Integrar validação de tamanho/formato
- [ ] **2.16** Implementar drag & drop interface

### **Dia 3: Privacidade, Analytics e Finalização (8 horas)**

#### **Manhã (4 horas) - Sistema de Privacidade**

**Hora 1-2: Privacy Controls**
- [ ] **3.1** Implementar `PrivacySettingsComponent`
- [ ] **3.2** Criar `calculateProfilePermissions()`
- [ ] **3.3** Implementar granular privacy controls
- [ ] **3.4** Adicionar explicações claras de privacidade

**Hora 3-4: Edit Profile Modal**
- [ ] **3.5** Enhancear `EditProfileModal.tsx`
- [ ] **3.6** Integrar validação em tempo real
- [ ] **3.7** Adicionar feedback visual de estado
- [ ] **3.8** Implementar gerenciamento de estado do form

#### **Tarde (4 horas) - Analytics e Finalização**

**Hora 5-6: Profile Analytics**
- [ ] **3.9** Implementar `getProfileStatistics()`
- [ ] **3.10** Criar sistema de achievements
- [ ] **3.11** Adicionar tracking de visualizações
- [ ] **3.12** Implementar caching para estatísticas

**Hora 7-8: Testes e Polimento**
- [ ] **3.13** Executar testes de integração
- [ ] **3.14** Otimizar performance e loading
- [ ] **3.15** Verificar compliance de acessibilidade
- [ ] **3.16** Documentação final e deploy

## 🔧 **Especificações Técnicas Detalhadas**

### **1. Server Actions Implementation**

#### **Profile Core Actions (`/app/u/actions.ts`)**
```typescript
'use server';

import { createServerClient } from '@/lib/supabase/server';
import { createClient } from '@/lib/supabase/client';
import { ProfileSchema, UsernameSchema } from '@/lib/validations/profile';
import type { UserProfile, ProfileUpdateInput } from '@/lib/types/profile';
import { cookies } from 'next/headers';
import { revalidatePath } from 'next/cache';

// Implementação completa com error handling e validação
export async function getUserProfileByUsername(username: string): Promise<UserProfile | null> {
  try {
    // Validar username input
    const validatedUsername = UsernameSchema.parse(username);
    
    const supabase = createServerClient(cookies());
    
    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        full_name,
        bio,
        avatar_url,
        banner_url,
        website,
        location,
        updated_at,
        privacy_settings,
        user_stats
      `)
      .eq('username', validatedUsername)
      .single();
    
    if (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
    
    return profile;
  } catch (error) {
    console.error('Error in getUserProfileByUsername:', error);
    return null;
  }
}

export async function updateUserProfile(
  userId: string, 
  updates: ProfileUpdateInput
): Promise<{ success: boolean; profile?: UserProfile; error?: string }> {
  try {
    // Validar dados de entrada
    const validatedData = ProfileSchema.partial().parse(updates);
    
    const supabase = createServerClient(cookies());
    
    // Verificar se usuário pode atualizar este perfil
    const { data: currentUser } = await supabase.auth.getUser();
    if (!currentUser.user || currentUser.user.id !== userId) {
      return { success: false, error: 'Unauthorized: Cannot update other user profiles' };
    }
    
    // Atualizar perfil
    const { data: profile, error } = await supabase
      .from('profiles')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    
    // Revalidar cache se username mudou
    if (validatedData.username) {
      revalidatePath(`/u/${validatedData.username}`);
    }
    
    return { success: true, profile };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: 'Failed to update profile' };
  }
}
```

### **2. Image Upload System**

#### **Estratégia de Upload Seguro**
- **Método:** Signed Upload URLs (evita limite de 1MB do Next.js)
- **Validação:** Múltiplas camadas (cliente + servidor)
- **Otimização:** Automática com WebP conversion
- **Segurança:** RLS policies + file type validation

```typescript
// Image Upload com Signed URLs
export async function createSignedUploadUrl(
  userId: string,
  fileName: string,
  fileType: 'avatar' | 'banner'
): Promise<{ success: boolean; signedUrl?: string; path?: string; error?: string }> {
  try {
    const supabase = createServerClient(cookies());
    
    // Verificar autenticação
    const { data: user } = await supabase.auth.getUser();
    if (!user.user || user.user.id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }
    
    // Validar tipo de arquivo
    const allowedTypes = {
      avatar: ['image/jpeg', 'image/png', 'image/webp'],
      banner: ['image/jpeg', 'image/png', 'image/webp']
    };
    
    // Gerar path único
    const timestamp = Date.now();
    const path = `${fileType}s/${userId}/${timestamp}-${fileName}`;
    
    // Criar signed URL
    const { data, error } = await supabase.storage
      .from('user_images')
      .createSignedUploadUrl(path, {
        upsert: true
      });
    
    if (error) throw error;
    
    return { 
      success: true, 
      signedUrl: data.signedUrl,
      path: path
    };
  } catch (error) {
    console.error('Error creating signed URL:', error);
    return { success: false, error: 'Failed to create upload URL' };
  }
}
```

### **3. Validation Schemas (Zod)**

```typescript
// /lib/validations/profile.ts
import { z } from 'zod';

export const UsernameSchema = z
  .string()
  .min(3, 'Username deve ter pelo menos 3 caracteres')
  .max(30, 'Username deve ter no máximo 30 caracteres')
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    'Username só pode conter letras, números, underscore e hífen'
  )
  .refine(
    (username) => !['admin', 'api', 'www', 'root'].includes(username.toLowerCase()),
    'Username reservado pelo sistema'
  );

export const ProfileSchema = z.object({
  username: UsernameSchema,
  full_name: z
    .string()
    .min(1, 'Nome é obrigatório')
    .max(100, 'Nome deve ter no máximo 100 caracteres'),
  bio: z
    .string()
    .max(500, 'Bio deve ter no máximo 500 caracteres')
    .optional(),
  website: z
    .string()
    .url('URL inválida')
    .optional()
    .or(z.literal('')),
  location: z
    .string()
    .max(100, 'Localização deve ter no máximo 100 caracteres')
    .optional(),
  avatar_url: z.string().url().optional(),
  banner_url: z.string().url().optional(),
  privacy_settings: z.object({
    profile_visibility: z.enum(['public', 'friends', 'private']),
    show_online_status: z.boolean(),
    show_gaming_profiles: z.boolean(),
    show_achievements: z.boolean(),
    allow_contact: z.boolean()
  }).optional()
});

export const ImageUploadSchema = z.object({
  file: z.instanceof(File),
  maxSize: z.number().default(5 * 1024 * 1024), // 5MB
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])
}).refine(
  (data) => data.file.size <= data.maxSize,
  'Arquivo muito grande (máximo 5MB)'
).refine(
  (data) => data.allowedTypes.includes(data.file.type),
  'Tipo de arquivo não suportado'
);
```

### **4. Privacy Permission Engine**

```typescript
// /utils/profile-permissions.ts
export interface ProfileViewPermissions {
  canViewOnlineStatus: boolean;
  canViewGamingProfiles: boolean;
  canViewAchievements: boolean;
  canViewContactInfo: boolean;
  canViewFullProfile: boolean;
}

export function calculateProfilePermissions(
  profile: UserProfile,
  viewerId?: string
): ProfileViewPermissions {
  const isOwner = viewerId === profile.id;
  const privacy = profile.privacy_settings;
  
  // Owner sempre pode ver tudo
  if (isOwner) {
    return {
      canViewOnlineStatus: true,
      canViewGamingProfiles: true,
      canViewAchievements: true,
      canViewContactInfo: true,
      canViewFullProfile: true
    };
  }
  
  // Perfil privado
  if (privacy.profile_visibility === 'private') {
    return {
      canViewOnlineStatus: false,
      canViewGamingProfiles: false,
      canViewAchievements: false,
      canViewContactInfo: false,
      canViewFullProfile: false
    };
  }
  
  // Perfil público com configurações granulares
  return {
    canViewOnlineStatus: privacy.show_online_status,
    canViewGamingProfiles: privacy.show_gaming_profiles,
    canViewAchievements: privacy.show_achievements,
    canViewContactInfo: privacy.allow_contact,
    canViewFullProfile: privacy.profile_visibility === 'public'
  };
}
```

## 🔒 **Configurações de Segurança**

### **RLS Policies para Storage**

```sql
-- Política para uploads de avatar
CREATE POLICY "Users can upload their own avatars"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'user_images' 
  AND (storage.foldername(name))[1] = 'avatars'
  AND (storage.foldername(name))[2] = auth.uid()::text
);

-- Política para visualização pública de avatars
CREATE POLICY "Avatar images are publicly accessible"
ON storage.objects FOR SELECT
TO public
USING (
  bucket_id = 'user_images' 
  AND (storage.foldername(name))[1] = 'avatars'
);

-- Política para atualização de avatars próprios
CREATE POLICY "Users can update their own avatars"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'user_images' 
  AND (storage.foldername(name))[1] = 'avatars'
  AND (storage.foldername(name))[2] = auth.uid()::text
);
```

## 📊 **Critérios de Performance**

### **Benchmarks Esperados**
- **Profile page load:** < 1.5 segundos
- **Username availability check:** < 300ms
- **Profile update:** < 2 segundos  
- **Image upload (2MB):** < 5 segundos
- **Privacy setting updates:** < 500ms

### **Otimizações Implementadas**
- **Caching:** Next.js cache + Supabase cache headers
- **Image optimization:** WebP conversion + responsive images
- **Lazy loading:** Para componentes não críticos
- **Debouncing:** Para validação em tempo real
- **Batch operations:** Para múltiplas atualizações

## 🧪 **Estratégia de Testes**

### **Unit Tests**
- [ ] Validação de username (50+ casos)
- [ ] Profile data validation (edge cases)
- [ ] Image upload validation
- [ ] Privacy permission calculations

### **Integration Tests**  
- [ ] Complete profile update flow
- [ ] Username change com slug updates
- [ ] Image upload end-to-end
- [ ] Privacy settings enforcement

### **E2E Tests**
- [ ] Public profile page navigation
- [ ] Edit profile modal workflow
- [ ] Username availability checking
- [ ] Mobile responsiveness

## 🚀 **Deployment Checklist**

### **Pré-Deploy**
- [ ] Todas as migrations aplicadas
- [ ] RLS policies configuradas
- [ ] Storage buckets criados
- [ ] Environment variables configuradas
- [ ] TypeScript compilation sem erros

### **Post-Deploy**
- [ ] Testar profile creation
- [ ] Testar image uploads
- [ ] Verificar SEO metadata
- [ ] Testar mobile experience
- [ ] Monitorar performance metrics

## 📈 **Métricas de Sucesso**

### **Funcional**
- ✅ 100% dos core features implementados
- ✅ Todas as validações funcionando
- ✅ Privacy controls operacionais
- ✅ Image uploads estáveis

### **Técnico**
- ✅ Performance benchmarks atingidos
- ✅ 0 vulnerabilidades de segurança
- ✅ Acessibilidade WCAG AA compliant
- ✅ TypeScript strict mode sem erros

### **UX**
- ✅ Feedback visual em todas as ações
- ✅ Loading states implementados
- ✅ Error handling user-friendly
- ✅ Mobile experience otimizada

---

**Status:** 📋 PLANO PRONTO PARA EXECUÇÃO  
**Próximo passo:** Iniciar implementação seguindo cronograma  
**Responsável:** Senior Software Developer Team  
**Data de criação:** 7 de dezembro de 2025
