"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from "@/lib/utils";
import {
  useOptimizedFilter,
  LoadingSkeleton
} from "./DashboardOptimizations";
import {
  Search,
  Filter,
  Star,
  Calendar,
  Eye,
  MessageCircle,
  MoreVertical,
  Edit,
  Trash2,
  Shield,
  ShieldOff,
  SortAsc,
  SortDesc,
  Loader2,
  FileText,
  Clock,
  TrendingUp,
  Users,
  Target,
  Award
} from "lucide-react";
import { Button } from "@/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuthContext } from "@/contexts/auth-context";
import { CodeTitle, SectionCodeTitle, CodeBadge } from "./CodeTitle";
import { ReviewCard } from "./ReviewCard";
import { useToast } from "@/hooks/use-toast";
import { updateReviewPrivacy } from "@/lib/services/reviewSettingsService";

interface ModernReviewsSectionProps {
  reviews: any[];
  isLoading: boolean;
  onRefresh: () => void;
  isDarkBackground?: boolean;
}

type SortOption = 'date' | 'score' | 'title' | 'platform';
type SortDirection = 'asc' | 'desc';

export function ModernReviewsSection({
  reviews,
  isLoading,
  onRefresh,
  isDarkBackground: propIsDarkBackground = true
}: ModernReviewsSectionProps) {
  const isDarkBackground = useBackgroundBrightness();
  const { user } = useAuthContext();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [activeTab, setActiveTab] = useState<'published' | 'drafts' | 'private'>('published');

  // Infinite scrolling state
  const [displayedCount, setDisplayedCount] = useState(4);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Ensure reviews is an array to prevent undefined errors
  const safeReviews = reviews || [];

  // Separate reviews by status and privacy
  const publishedReviews = safeReviews.filter(review =>
    (review.status === 'published' || !review.status) && (review.is_private !== true)
  );
  const draftReviews = safeReviews.filter(review =>
    review.status === 'draft' && (review.is_private !== true)
  );
  const privateReviews = safeReviews.filter(review =>
    review.is_private === true
  );

  // Get current tab reviews
  const currentTabReviews = activeTab === 'published'
    ? publishedReviews
    : activeTab === 'drafts'
    ? draftReviews
    : privateReviews;

  // Filter and sort reviews
  const filteredReviews = currentTabReviews.filter((review: any) => {
    if (!searchQuery) return true;
    const searchTerm = searchQuery.toLowerCase();
    return !!(
      review.title?.toLowerCase().includes(searchTerm) ||
      (review.gameName || review.game_name)?.toLowerCase().includes(searchTerm) ||
      review.platform?.toLowerCase().includes(searchTerm)
    );
  }).sort((a: any, b: any) => {
    switch (sortBy) {
      case 'date':
        const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt || 0);
        const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt || 0);
        return sortDirection === 'desc' ? dateB.getTime() - dateA.getTime() : dateA.getTime() - dateB.getTime();
      case 'score':
        return sortDirection === 'desc' ? (b.overallScore || 0) - (a.overallScore || 0) : (a.overallScore || 0) - (b.overallScore || 0);
      case 'title':
        return sortDirection === 'desc' ? (b.title || '').localeCompare(a.title || '') : (a.title || '').localeCompare(b.title || '');
      case 'platform':
        return sortDirection === 'desc' ? (b.platform || '').localeCompare(a.platform || '') : (a.platform || '').localeCompare(b.platform || '');
      default:
        return 0;
    }
  });

  // Get currently displayed reviews
  const displayedReviews = useMemo(() => {
    return filteredReviews.slice(0, displayedCount);
  }, [filteredReviews, displayedCount]);

  // Reset displayed count when filters change
  useEffect(() => {
    setDisplayedCount(4);
  }, [searchQuery, sortBy, sortDirection]);

  // Infinite scroll handler
  const loadMore = useCallback(async () => {
    if (isLoadingMore || displayedCount >= filteredReviews.length) return;

    setIsLoadingMore(true);
    // Simulate loading delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    setDisplayedCount(prev => Math.min(prev + 4, filteredReviews.length));
    setIsLoadingMore(false);
  }, [isLoadingMore, displayedCount, filteredReviews.length]);

  // Scroll event listener for infinite scroll
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight) return;
      loadMore();
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadMore]);

  const handleSort = (option: SortOption) => {
    if (sortBy === option) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(option);
      setSortDirection('desc');
    }
  };

  // Privacy handlers
  const handlePrivacyToggle = async (reviewId: string, isPrivate: boolean) => {
    if (!user?.uid) return;

    try {
      const result = await updateReviewPrivacy(reviewId, user.uid, isPrivate);
      if (result.success) {
        toast({
          title: "Privacy Updated",
          description: `Review has been made ${isPrivate ? 'private' : 'public'}.`,
        });
        onRefresh();
      } else {
        throw new Error(result.error || 'Failed to update privacy');
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update review privacy. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSoftDelete = async (reviewId: string) => {
    if (!user?.uid) return;

    // For now, just show a toast that this feature is not implemented
    toast({
      title: "Feature Not Available",
      description: "Review deletion is temporarily disabled. Use the privacy toggle to hide reviews instead.",
      variant: "destructive"
    });
  };



  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <LoadingSkeleton count={3} height="h-8" />
        <div className="grid gap-4 grid-cols-1">
          {Array.from({ length: 4 }).map((_, i) => (
            <LoadingSkeleton key={i} height="h-20" />
          ))}
        </div>
      </div>
    );
  }

  const hasMoreReviews = displayedCount < filteredReviews.length;

  return (
    <div className="p-2 sm:p-3 lg:p-4 xl:p-6 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="border-l-4 border-purple-500 pl-4">
          <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            <span className="text-violet-400/60">&lt;</span>
            <span className="px-2">My Reviews</span>
            <span className="text-violet-400/60">/&gt;</span>
          </span>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-slate-800 p-1 rounded-lg border border-slate-700">
          <button
            type="button"
            onClick={() => setActiveTab('published')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
              activeTab === 'published'
                ? "bg-purple-600 text-white shadow-lg"
                : "text-slate-400 hover:text-slate-200 hover:bg-slate-700/50"
            )}
          >
            Published
            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
              activeTab === 'published'
                ? 'bg-purple-700 text-purple-100'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {publishedReviews.length}
            </span>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('drafts')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
              activeTab === 'drafts'
                ? "bg-purple-600 text-white shadow-lg"
                : "text-slate-400 hover:text-slate-200 hover:bg-slate-700/50"
            )}
          >
            Drafts
            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
              activeTab === 'drafts'
                ? 'bg-purple-700 text-purple-100'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {draftReviews.length}
            </span>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('private')}
            className={cn(
              "flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
              activeTab === 'private'
                ? "bg-purple-600 text-white shadow-lg"
                : "text-slate-400 hover:text-slate-200 hover:bg-slate-700/50"
            )}
          >
            Private
            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
              activeTab === 'private'
                ? 'bg-purple-700 text-purple-100'
                : 'bg-slate-600 text-slate-300'
            }`}>
              {privateReviews.length}
            </span>
          </button>
        </div>
      </div>

      {/* Container for Controls and Content */}
      <div className="border-gray-800 bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl border overflow-hidden">
        {/* Controls Header */}
        <div className="p-6 border-b border-gray-700/50 bg-gray-800/30">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search reviews..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full h-10 rounded-lg border border-slate-600/50 bg-slate-800/50 pl-9 pr-3 text-sm text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all font-['Lato'] backdrop-blur-sm"
              />
            </div>

            {/* Sort */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="font-['Lato'] border-slate-600/50 bg-slate-800/50 hover:bg-slate-700/50 backdrop-blur-sm">
                  {sortDirection === 'asc' ? <SortAsc className="w-4 h-4 mr-2" /> : <SortDesc className="w-4 h-4 mr-2" />}
                  Sort
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-slate-800/95 border-slate-600/50 backdrop-blur-sm">
                <DropdownMenuItem onClick={() => handleSort('date')} className="font-['Lato'] hover:bg-slate-700/50">
                  Date {sortBy === 'date' && (sortDirection === 'asc' ? '↑' : '↓')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleSort('score')} className="font-['Lato'] hover:bg-slate-700/50">
                  Score {sortBy === 'score' && (sortDirection === 'asc' ? '↑' : '↓')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleSort('title')} className="font-['Lato'] hover:bg-slate-700/50">
                  Title {sortBy === 'title' && (sortDirection === 'asc' ? '↑' : '↓')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleSort('platform')} className="font-['Lato'] hover:bg-slate-700/50">
                  Platform {sortBy === 'platform' && (sortDirection === 'asc' ? '↑' : '↓')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Content Area */}
        <div className="p-6">
          {/* Reviews List */}
          <div className="space-y-4">
            {/* Empty State */}
            {filteredReviews.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-slate-600" />
                </div>
                <h3 className="text-lg font-medium text-slate-300 mb-2">
                  {searchQuery ? 'No reviews match your search' : `No ${activeTab} reviews`}
                </h3>
                <p className="text-slate-500">
                  {searchQuery
                    ? 'Try adjusting your search criteria'
                    : `Your ${activeTab} reviews will appear here`
                  }
                </p>
              </div>
            )}

            {/* Reviews */}
            {displayedReviews.map((review: any) => (
              <ReviewCard
                key={review.id}
                review={review}
                viewMode="list"
                onRefresh={onRefresh}
                onPrivacyToggle={handlePrivacyToggle}
                onSoftDelete={handleSoftDelete}
              />
            ))}

            {/* Enhanced Load More Button with Theme Integration */}
            {hasMoreReviews && (
              <div className="flex justify-center pt-4">
                <Button
                  onClick={loadMore}
                  disabled={isLoadingMore}
                  className={cn(
                    "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
                    isDarkBackground
                      ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70'
                      : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
                  )}
                  style={{
                    backdropFilter: 'blur(8px)',
                    boxShadow: isDarkBackground
                      ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)'
                      : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
                  }}
                >
                  {isLoadingMore ? (
                    <motion.div
                      className="flex items-center gap-2"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Clock className="w-4 h-4" />
                      </motion.div>
                      Loading more reviews...
                    </motion.div>
                  ) : (
                    <span className="flex items-center gap-2">
                      Load More Reviews
                      <span className="text-xs opacity-70">
                        ({filteredReviews.length - displayedCount} remaining)
                      </span>
                    </span>
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ModernReviewsSection;
