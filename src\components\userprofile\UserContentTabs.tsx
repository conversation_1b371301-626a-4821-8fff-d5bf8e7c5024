'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Target,
  Tags,
  Activity,
  Hash,
  Trophy,
  Heart,
  FileText,
  Search,
  Eye,
  Gamepad2,
  ExternalLink
} from 'lucide-react';
import SurveysModule from './SurveysModule';
import SponsorBanner from '@/components/layout/SponsorBanner';
import { useUserStats } from '@/hooks/useUserStats';
import { getUserGamingProfiles } from '@/app/u/actions-profiles';
import { calculateProfilePermissions } from '@/utils/profile-permissions';
import type { GamingProfile } from '@/lib/types/profile';

// Gaming Platform Icons
import SteamIcon from '@/components/ui/icons/gaming/SteamIcon';
import PlaystationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';
import NintendoSwitchIcon from '@/components/ui/icons/gaming/NintendoSwitchIcon';

interface UserContentTabsProps {
  userId: string;
  currentUserId?: string;
  isOwnProfile?: boolean;
  theme?: any;
  showFilters?: boolean;
  setShowFilters?: (show: boolean) => void;
}

type VisibleModules = {
  [key: string]: boolean;
  content: boolean;
  'gaming-profiles': boolean;
  achievements: boolean;
  surveys: boolean;
  tags: boolean;
}

// Tab configuration
const tabs = [
  {
    id: 'content',
    label: 'Stats',
    icon: Activity,
    description: 'User statistics'
  },
  {
    id: 'gaming-profiles',
    label: 'Gaming Profiles',
    icon: Gamepad2,
    description: 'Connected gaming platforms'
  },
  {
    id: 'achievements',
    label: 'Achievements',
    icon: Trophy,
    description: 'User achievements and milestones'
  },
  {
    id: 'surveys',
    label: 'Surveys',
    icon: Target,
    description: 'Performance reports'
  },
  {
    id: 'tags',
    label: 'Tags',
    icon: Tags,
    description: 'Gaming preferences'
  }
];

// Placeholder component for User Tags
const UserTagsTab = ({ userId, theme, isOwnProfile }: { userId: string; theme: any; isOwnProfile: boolean }) => (
  <div className="space-y-6 w-full overflow-hidden">
    <div className="bg-gray-900 border border-gray-800 rounded-xl p-6 w-full">
      {/* Header */}
      <div className="flex items-center mb-6">
        <div className="h-px bg-gray-600 flex-1" />
        <span className="mx-3 text-xs font-mono text-primary-accent uppercase tracking-widest">
          <span className="text-gray-400">//</span> Tags System
        </span>
        <div className="h-px bg-gray-600 flex-1" />
      </div>

      <div className="text-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6"
        >
          <div className="w-16 h-16 mx-auto mb-4 rounded-xl bg-gray-800 border border-gray-700 flex items-center justify-center">
            <Hash className="h-8 w-8 text-primary-accent" />
          </div>
        </motion.div>

        <h3 className="text-lg font-mono font-bold text-white mb-3">
          tags.init() <span className="text-primary-accent animate-pulse">|</span>
        </h3>
        
        <p className="text-gray-400 font-mono text-sm mb-6 leading-relaxed">
          <span className="text-gray-500">// </span>User tag categorization system<br />
          <span className="text-gray-500">// </span>Gaming preferences & interests mapping
        </p>

        <div className="flex flex-wrap gap-2 justify-center mb-4">
          {['Action', 'RPG', 'Strategy', 'Indie', 'Multiplayer'].map((tag, index) => (
            <motion.div
              key={tag}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="px-3 py-1 bg-gray-800 border border-gray-700 rounded-lg font-mono text-xs text-gray-300 hover:bg-gray-700 hover:border-primary-accent transition-all duration-300"
            >
              #{tag.toLowerCase()}
            </motion.div>
          ))}
        </div>

        <div className="text-xs font-mono text-gray-500">
          // Feature deployment: v2.1.0
        </div>
      </div>
    </div>
  </div>
);

// Platform icon mapping
const PlatformIconDisplay = ({ platform, className, color }: { platform: string; className?: string; color?: string }) => {
  const key = platform.toLowerCase();
  const iconClass = cn('h-5 w-5', className);
  const style = color ? { color } : {};
  
  if (key.includes('steam') || key.includes('pc')) return <SteamIcon className={iconClass} style={style} />;
  if (key.includes('playstation') || key.includes('psn')) return <PlaystationIcon className={iconClass} style={style} />;
  if (key.includes('xbox')) return <XboxIcon className={iconClass} style={style} />;
  if (key.includes('nintendo')) return <NintendoSwitchIcon className={iconClass} style={style} />;
  return <Gamepad2 className={iconClass} style={style} />;
};

// Gaming Profiles Component
const UserGamingProfilesTab = ({ userId, theme, isOwnProfile, currentUserId }: { 
  userId: string; 
  theme: any; 
  isOwnProfile: boolean;
  currentUserId?: string;
}) => {
  const [gamingProfiles, setGamingProfiles] = React.useState<GamingProfile[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchGamingProfiles = async () => {
      try {
        setLoading(true);
        const profiles = await getUserGamingProfiles(userId);
        setGamingProfiles(profiles);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load gaming profiles');
      } finally {
        setLoading(false);
      }
    };

    fetchGamingProfiles();
  }, [userId]);

  if (loading) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6 w-full">
          <div className="flex items-center mb-6">
            <div className="h-px bg-gray-600 flex-1" />
            <span className="mx-3 text-xs font-mono text-primary-accent uppercase tracking-widest">
              <span className="text-gray-400">//</span> Loading...
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-3 p-3 rounded-xl bg-gray-800 border border-gray-700 animate-pulse">
                <div className="w-10 h-10 bg-gray-700 rounded-md" />
                <div className="flex-1">
                  <div className="w-20 h-4 bg-gray-700 rounded mb-1" />
                  <div className="w-16 h-3 bg-gray-700 rounded" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-6 w-full">
          <div className="text-center text-red-400 font-mono text-sm">
            Error: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full overflow-hidden">
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-6 w-full">
        {/* Header */}
        <div className="flex items-center mb-6">
          <div className="h-px bg-gray-600 flex-1" />
          <span 
            className="mx-3 text-xs font-mono uppercase tracking-widest"
            style={{ color: theme?.colors?.primary || '#00D4FF' }}
          >
            <span className="text-gray-400">//</span> Gaming Profiles
          </span>
          <div className="h-px bg-gray-600 flex-1" />
        </div>

        {gamingProfiles.length > 0 ? (
          <div className="space-y-3">
            {gamingProfiles.map((profile, index) => (
              <motion.a
                key={`${profile.platform}-${profile.username}-${index}`}
                href={profile.url || '#'}
                target={profile.url ? '_blank' : undefined}
                rel="noopener noreferrer"
                className="flex items-center gap-3 p-3 rounded-xl bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-200 group w-full"
                style={{
                  borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                }}
                whileHover={{
                  x: 2,
                  borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.3)`
                }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <div className="p-2 rounded-md bg-gray-900">
                  <PlatformIconDisplay 
                    platform={profile.platform} 
                    color={theme?.colors?.primary || '#00D4FF'} 
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white truncate font-mono">
                    {profile.username}
                  </div>
                  <div className="text-xs text-gray-500 font-mono">
                    {profile.platform}
                  </div>
                </div>
                {profile.url && (
                  <ExternalLink className="h-4 w-4 text-gray-500 group-hover:text-gray-400 transition-colors" />
                )}
              </motion.a>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-6"
            >
              <div className="w-16 h-16 mx-auto mb-4 rounded-xl bg-gray-800 border border-gray-700 flex items-center justify-center">
                <Gamepad2 className="h-8 w-8 text-gray-500" />
              </div>
            </motion.div>

            <h3 className="text-lg font-mono font-bold text-white mb-3">
              No gaming profiles linked
            </h3>
            
            <p className="text-gray-400 font-mono text-sm mb-6 leading-relaxed">
              <span className="text-gray-500">// </span>Connect your gaming accounts<br />
              <span className="text-gray-500">// </span>Steam, PlayStation, Xbox, Nintendo...
            </p>

            {isOwnProfile && (
              <div className="text-xs font-mono text-gray-500">
                // Add profiles in Edit Profile modal
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// User Stats Component
const UserContentTab = ({ userId, theme, isOwnProfile }: { userId: string; theme: any; isOwnProfile: boolean }) => {
  // Get real user stats
  const { stats, loading, error } = useUserStats(userId);

  const formatNumber = (n: number) =>
    n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
          <div className="flex items-center mb-4">
            <div className="h-px bg-gray-600 flex-1" />
            <span className="mx-3 text-xs font-mono uppercase tracking-widest text-gray-400">
              Loading Stats...
            </span>
            <div className="h-px bg-gray-600 flex-1" />
          </div>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between p-2 rounded-lg bg-gray-800 border border-gray-700">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 rounded bg-gray-700 animate-pulse" />
                  <div className="w-16 h-3 bg-gray-700 rounded animate-pulse" />
                </div>
                <div className="w-8 h-3 bg-gray-700 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6 w-full overflow-hidden">
        <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
          <div className="text-center text-red-400 font-mono text-sm">
            Error loading stats: {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full overflow-hidden">
      {/* Neural User Stats */}
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="h-px bg-gray-600 flex-1" />
          <span 
            className="mx-3 text-xs font-mono uppercase tracking-widest"
            style={{ color: theme?.colors?.primary || '#00D4FF' }}
          >
            <span className="text-gray-400">//</span> Stats
          </span>
          <div className="h-px bg-gray-600 flex-1" />
        </div>

        <div className="space-y-2">
          <motion.div
            className="flex items-center justify-between p-2 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-300 group"
            style={{
              borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
            }}
            whileHover={{
              x: 2,
              borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.3)`
            }}
          >
            <div className="flex items-center gap-2">
              <div
                className="w-5 h-5 rounded flex items-center justify-center transition-colors"
                style={{
                  backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                  color: theme?.colors?.primary || '#00D4FF'
                }}
              >
                <FileText size={10} />
              </div>
              <span className="text-xs text-white/60 font-mono">reviews</span>
            </div>
            <div className="text-xs font-mono font-bold text-white tabular-nums">{formatNumber(stats?.totalReviews || 0)}</div>
          </motion.div>

          <motion.div
            className="flex items-center justify-between p-2 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-300 group"
            style={{
              borderColor: `rgba(52, 211, 153, 0.2)`,
            }}
            whileHover={{
              x: 2,
              borderColor: `rgba(52, 211, 153, 0.3)`
            }}
          >
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded bg-green-800 flex items-center justify-center transition-colors">
                <Eye size={10} className="text-green-400" />
              </div>
              <span className="text-xs text-gray-400 font-mono">total views</span>
            </div>
            <div className="text-xs font-mono font-bold text-white tabular-nums">{formatNumber(stats?.totalViews || 0)}</div>
          </motion.div>

          <motion.div
            className="flex items-center justify-between p-2 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-300 group"
            style={{
              borderColor: `rgba(248, 113, 113, 0.2)`,
            }}
            whileHover={{
              x: 2,
              borderColor: `rgba(248, 113, 113, 0.3)`
            }}
          >
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded bg-red-800 flex items-center justify-center transition-colors">
                <Heart size={10} className="text-red-400" />
              </div>
              <span className="text-xs text-gray-400 font-mono">likes</span>
            </div>
            <div className="text-xs font-mono font-bold text-white tabular-nums">{formatNumber(stats?.totalLikes || 0)}</div>
          </motion.div>

          <motion.div
            className="flex items-center justify-between p-2 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 transition-all duration-300 group"
            style={{
              borderColor: `rgba(156, 163, 175, 0.2)`,
            }}
            whileHover={{
              x: 2,
              borderColor: `rgba(156, 163, 175, 0.3)`
            }}
          >
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded bg-gray-700 flex items-center justify-center transition-colors">
                <Target size={10} className="text-gray-400" />
              </div>
              <span className="text-xs text-gray-400 font-mono">comments</span>
            </div>
            <div className="text-xs font-mono font-bold text-white tabular-nums">{formatNumber(stats?.totalComments || 0)}</div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

// User Achievements Component
const UserAchievementsTab = ({ userId, theme, isOwnProfile }: { userId: string; theme: any; isOwnProfile: boolean }) => {
  const achievements = [
    {
      id: '1',
      name: 'Crítico Veterano',
      description: '100 reviews publicadas',
      rarity: 'legendary',
      unlocked_at: '2024-01-10'
    },
    {
      id: '2',
      name: 'Influenciador',
      description: '1000 likes recebidas',
      rarity: 'epic',
      unlocked_at: '2024-01-05'
    },
    {
      id: '3',
      name: 'Explorador',
      description: '50 jogos avaliados',
      rarity: 'rare',
      unlocked_at: '2023-12-20'
    }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-yellow-400';
      case 'epic': return 'text-purple-400';
      case 'rare': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6 w-full overflow-hidden">
      {/* Neural Achievements */}
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-4 w-full">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="h-px bg-gray-600 flex-1" />
          <span 
            className="mx-3 text-xs font-mono uppercase tracking-widest"
            style={{ color: theme?.colors?.primary || '#00D4FF' }}
          >
            <span className="text-gray-400">//</span> Achievements
          </span>
          <div className="h-px bg-gray-600 flex-1" />
        </div>

        <div className="space-y-2">
          {achievements.slice(0, 3).map((achievement, index) => (
            <motion.div 
              key={achievement.id} 
              className="flex items-center gap-3 p-3 rounded-lg bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:border-yellow-400 transition-all duration-300 group"
              whileHover={{ x: 2 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className="w-6 h-6 rounded bg-yellow-800 flex items-center justify-center flex-shrink-0 group-hover:bg-yellow-700 transition-colors">
                <Trophy size={12} className={getRarityColor(achievement.rarity)} />
              </div>
              <div className="min-w-0 flex-1">
                <div className="text-xs font-mono font-bold text-white truncate">{achievement.name}</div>
                <div className="text-xs text-gray-400 font-mono truncate">{achievement.description}</div>
              </div>
            </motion.div>
          ))}
          {achievements.length > 3 && (
            <div className="text-center pt-2">
              <span className="text-xs text-gray-500 font-mono">// +{achievements.length - 3} more unlocked</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default function UserContentTabs({
  userId,
  currentUserId,
  isOwnProfile = false,
  theme,
  showFilters = false,
  setShowFilters
}: UserContentTabsProps) {
  const [activeTab, setActiveTab] = useState('content');
  const [visibleModules, setVisibleModules] = useState<Record<string, boolean>>({
    content: false,
    'gaming-profiles': false,
    achievements: false,
    surveys: false,
    tags: false
  });

  const handleTabClick = (tabId: string) => {
    const isCurrentlyVisible = visibleModules[tabId];
    
    if (isCurrentlyVisible && activeTab === tabId) {
      // If clicking the active tab, toggle it off
      setVisibleModules(prev => ({
        ...prev,
        [tabId]: false
      }));
      
      // Switch to the first remaining visible tab
      const firstVisible = Object.entries(visibleModules).find(([id, visible]) => 
        visible && id !== tabId
      )?.[0];
      if (firstVisible) {
        setActiveTab(firstVisible);
      }
    } else if (isCurrentlyVisible) {
      // If clicking a visible but inactive tab, just switch to it
      setActiveTab(tabId);
    } else {
      // If clicking a hidden tab, make it visible and active
      setVisibleModules(prev => ({
        ...prev,
        [tabId]: true
      }));
      setActiveTab(tabId);
    }
  };

  const visibleTabs = tabs.filter(tab => visibleModules[tab.id]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'gaming-profiles':
        return (
          <UserGamingProfilesTab
            userId={userId}
            currentUserId={currentUserId}
            theme={theme}
            isOwnProfile={isOwnProfile}
          />
        );
      case 'surveys':
        return (
          <SurveysModule
            userId={userId}
            currentUserId={currentUserId}
            isOwnProfile={isOwnProfile}
            theme={theme}
          />
        );
      case 'achievements':
        return (
          <UserAchievementsTab
            userId={userId}
            theme={theme}
            isOwnProfile={isOwnProfile}
          />
        );
      case 'tags':
        return (
          <UserTagsTab
            userId={userId}
            theme={theme}
            isOwnProfile={isOwnProfile}
          />
        );
      case 'content':
        return (
          <UserContentTab
            userId={userId}
            theme={theme}
            isOwnProfile={isOwnProfile}
          />
        );
      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6 w-full overflow-hidden"
    >

      {/* Neural Tab Navigation */}
      <div className="bg-gray-900 border border-gray-800 rounded-xl p-3 w-full">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent flex-1" />
          <span 
            className="mx-3 text-xs font-mono uppercase tracking-widest"
            style={{ color: `${theme?.colors?.primary || '#00D4FF'}80` }}
          >
            <span className="text-gray-500">//</span> Navigation
          </span>
          <div className="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent flex-1" />
        </div>

        <div className="space-y-2">

          {/* Filter Toggle Button - Redesigned to be more delicate */}
          {setShowFilters && (
            <motion.button
              onClick={() => setShowFilters(!showFilters)}
              className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg font-mono text-xs transition-all duration-300 group ${
                showFilters
                  ? 'border backdrop-blur-sm'
                  : 'bg-gray-800/30 border border-gray-700/40 text-gray-400 hover:bg-gray-700/40 hover:border-gray-600/50 hover:text-gray-200'
              }`}
              style={showFilters ? {
                backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.08)`,
                borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                color: `${theme?.colors?.primary || '#00D4FF'}cc`
              } : {}}
              whileHover={{ x: 1, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div 
                className={`w-6 h-6 rounded-md flex items-center justify-center transition-all duration-300 ${
                  showFilters ? '' : 'bg-gray-700/50 group-hover:bg-gray-600/50'
                }`}
                style={showFilters ? {
                  backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.15)`,
                  boxShadow: `0 0 8px rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`
                } : {}}
              >
                <Search 
                  className="h-4 w-4 transition-all duration-300" 
                  style={showFilters ? { 
                    color: theme?.colors?.primary || '#00D4FF',
                    filter: 'drop-shadow(0 0 2px currentColor)'
                  } : {}}
                />
              </div>
              <span className="font-medium">Search</span>
              {showFilters && (
                <motion.div 
                  className="ml-auto w-1.5 h-1.5 rounded-full"
                  style={{ backgroundColor: theme?.colors?.primary || '#00D4FF' }}
                  animate={{ opacity: [0.4, 1, 0.4] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              )}
            </motion.button>
          )}

          {/* Navigation Tabs - Redesigned to be more delicate */}
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            const isVisible = visibleModules[tab.id];

            return (
              <motion.button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg font-mono text-xs transition-all duration-300 group ${
                  isActive && isVisible
                    ? 'border backdrop-blur-sm'
                    : 'bg-gray-800/30 border border-gray-700/40 text-gray-400 hover:bg-gray-700/40 hover:border-gray-600/50 hover:text-gray-200'
                }`}
                style={(isActive && isVisible) ? {
                  backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.08)`,
                  borderColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`,
                  color: `${theme?.colors?.primary || '#00D4FF'}cc`
                } : {}}
                whileHover={{ x: 1, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title={tab.description}
              >
                <div 
                  className={`w-6 h-6 rounded-md flex items-center justify-center transition-all duration-300 ${
                    (isActive && isVisible) ? '' : 'bg-gray-700/50 group-hover:bg-gray-600/50'
                  }`}
                  style={(isActive && isVisible) ? {
                    backgroundColor: `rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.15)`,
                    boxShadow: `0 0 8px rgba(${theme?.colors?.primaryRgb || '0, 212, 255'}, 0.2)`
                  } : {}}
                >
                  <Icon 
                    className="h-4 w-4 transition-all duration-300" 
                    style={(isActive && isVisible) ? { 
                      color: theme?.colors?.primary || '#00D4FF',
                      filter: 'drop-shadow(0 0 2px currentColor)'
                    } : {}}
                  />
                </div>
                <span className={`font-medium transition-opacity duration-300 ${isVisible ? '' : 'opacity-60'}`}>
                  {tab.label}
                </span>
                {(isActive && isVisible) && (
                  <motion.div 
                    className="ml-auto w-1.5 h-1.5 rounded-full"
                    style={{ backgroundColor: theme?.colors?.primary || '#00D4FF' }}
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      {visibleModules[activeTab] && (
        <div className="w-full overflow-hidden mt-[35px] mb-[35px]">
          {renderTabContent()}
        </div>
      )}

      {/* Sponsor Banner */}
      <div className="w-full overflow-hidden">
        <SponsorBanner userId={userId} />
      </div>
    </motion.div>
  );
}
