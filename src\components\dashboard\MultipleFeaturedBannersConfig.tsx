'use client';

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import Image from 'next/image';
import {
  Eye,
  Heart,
  MessageSquare,
  Loader2,
  Check,
  X,
  Trash2,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Edit3,
  Save,
  Gamepad2,
  Zap,
  Target,
  Layers,
  ShoppingCart,
  Link2,
  Info,
  Sparkles,
  Plus,
  Move,
  AlertCircle
} from 'lucide-react';
import EnebaIcon from '@/components/ui/icons/stores/EnebaIcon';
import G2AIcon from '@/components/ui/icons/stores/G2AIcon';
import GOGIcon from '@/components/ui/icons/stores/GOGIcon';
import HRKIcon from '@/components/ui/icons/stores/HRKIcon';
import InstantGamingIcon from '@/components/ui/icons/stores/InstantGamingIcon';
import KinguinIcon from '@/components/ui/icons/stores/KinguinIcon';
import NuuvemIcon from '@/components/ui/icons/stores/NuuvemIcon';
import { getUserReviews } from '@/lib/review-service';
import type { ContentFilters } from '@/types/user-content';

interface UserReview {
  id: string;
  user_id: string;
  game_name: string;
  game_image?: string;
  igdb_cover_url?: string;
  main_image_url?: string;
  mainImageUrl?: string;
  igdbCoverUrl?: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  is_public: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
  title: string;
  slug: string;
}

interface StoreLink {
  id?: string;
  store_name: string;
  price: string;
  original_price?: string;
  store_url: string;
  display_order: number;
  color_gradient?: string;
  is_active?: boolean;
  isEditing?: boolean;
}

interface FeaturedBanner {
  id?: string;
  user_id: string;
  review_id: string;
  display_order: number;
  is_active: boolean;
  review?: UserReview;
  store_links?: StoreLink[];
}

interface MultipleFeaturedBannersConfigProps {
  userId: string;
  className?: string;
}

interface StoreLinksSectionProps {
  bannerId: string;
  userId: string;
  displayOrder: number;
  storeLinks: StoreLink[];
  onStoreLinksUpdate: () => void;
}

// Store Links Section Component
const StoreLinksSection: React.FC<StoreLinksSectionProps> = ({
  bannerId,
  userId,
  displayOrder,
  storeLinks,
  onStoreLinksUpdate
}) => {
  const [localStoreLinks, setLocalStoreLinks] = useState<StoreLink[]>(storeLinks);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  

  // Available stores list
  const availableStores = [
    'Eneba', 'G2A', 'GOG', 'HRK', 'Instant Gaming', 'Kinguin', 'Nuuvem'
  ];

  // Store icon mapping function with error handling
  const getStoreIcon = (storeName: string) => {
    const iconProps = { className: "h-4 w-4" };
    try {
      switch (storeName) {
        case 'Eneba': return <EnebaIcon {...iconProps} />;
        case 'G2A': return <G2AIcon {...iconProps} />;
        case 'GOG': return <GOGIcon {...iconProps} />;
        case 'HRK': return <HRKIcon {...iconProps} />;
        case 'Instant Gaming': return <InstantGamingIcon {...iconProps} />;
        case 'Kinguin': return <KinguinIcon {...iconProps} />;
        case 'Nuuvem': return <NuuvemIcon {...iconProps} />;
        default: return <ShoppingCart {...iconProps} />;
      }
    } catch (error) {
      console.error('Error rendering store icon:', error);
      return <ShoppingCart {...iconProps} />;
    }
  };

  // Update local state when props change
  useEffect(() => {
    setLocalStoreLinks(storeLinks);
  }, [storeLinks]);

  const addStoreLink = () => {
    const newLink: StoreLink = {
      store_name: availableStores[0],
      price: '',
      original_price: '',
      store_url: '',
      display_order: localStoreLinks.length + 1,
      color_gradient: 'from-blue-500 to-purple-600',
      is_active: true,
      isEditing: true // New links always start in editing mode
    };
    setLocalStoreLinks([...localStoreLinks, newLink]);
  };

  const updateStoreLink = (index: number, updates: Partial<StoreLink>) => {
    const updatedLinks = [...localStoreLinks];
    updatedLinks[index] = { ...updatedLinks[index], ...updates };
    setLocalStoreLinks(updatedLinks);
  };

  const removeStoreLink = async (index: number) => {
    const linkToRemove = localStoreLinks[index];
    const updatedLinks = localStoreLinks.filter((_, i) => i !== index);
    // Reorder display_order
    updatedLinks.forEach((link, i) => {
      link.display_order = i + 1;
    });
    setLocalStoreLinks(updatedLinks);
    
    // If it's an existing link (has ID), save immediately to remove from database
    if (linkToRemove.id) {
      try {
        setIsSaving(true);
        const response = await fetch('/api/u/featured-banners', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'saveStoreLinks',
            userId,
            bannerId: bannerId,
            storeLinks: updatedLinks.map(link => ({
              store_name: link.store_name,
              price: link.price,
              original_price: link.original_price,
              store_url: link.store_url,
              color_gradient: link.color_gradient
            }))
          })
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            toast({
              title: "Success",
              description: `Store link removed successfully!`,
            });
            onStoreLinksUpdate(); // Refresh parent
          }
        }
      } catch (error) {
        console.error('Error removing store link:', error);
        toast({
          title: "Error",
          description: "Failed to remove store link",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    }
  };

  const saveStoreLinks = async () => {
    try {
      setIsSaving(true);
      
      
      const response = await fetch('/api/u/featured-banners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'saveStoreLinks',
          userId,
          bannerId: bannerId,
          storeLinks: localStoreLinks.map(link => ({
            store_name: link.store_name,
            price: link.price,
            original_price: link.original_price,
            store_url: link.store_url,
            color_gradient: link.color_gradient
          }))
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Success",
          description: `Store links for Banner ${displayOrder} saved successfully!`,
        });
        
        // Clear editing state for all links
        setLocalStoreLinks(prev => prev.map(link => ({ ...link, isEditing: false })));
        
        // Refresh parent component
        onStoreLinksUpdate();
      } else {
        throw new Error(result.error || 'Failed to save store links');
      }
    } catch (error) {
      console.error('Error saving store links:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to save store links',
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-gray-800">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Store Links for Banner {displayOrder}
            </CardTitle>
            <p className="text-xs text-gray-400 mt-1 font-mono">
              Configure sponsored links for this featured banner
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm font-mono text-slate-300">
              {localStoreLinks.length}/3 Links
            </div>
            <div className="text-xs text-slate-400 font-mono">
              {localStoreLinks.length === 3 ? 'Maximum reached' : `${3 - localStoreLinks.length} remaining`}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Store Links List */}
        {localStoreLinks.length > 0 ? (
          <div className="space-y-3">
            {localStoreLinks.map((link, index) => (
              <div key={index} className="bg-gray-800/30 border border-gray-700 rounded-lg overflow-hidden">
                {/* Store Header - Always Visible */}
                <div className="p-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStoreIcon(link.store_name)}
                    <div>
                      <div className="font-medium text-white">{link.store_name}</div>
                      <div className="text-sm text-gray-400">
                        {link.price && (
                          <span className="text-purple-400 font-mono">{link.price}</span>
                        )}
                        {link.original_price && (
                          <span className="ml-2 text-gray-500 line-through font-mono text-xs">{link.original_price}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {/* Edit Button - for existing links */}
                    {!link.isEditing && link.id && (
                      <Button
                        onClick={() => updateStoreLink(index, { isEditing: true })}
                        variant="outline"
                        size="sm"
                        className="border-gray-700 text-gray-400 hover:text-gray-300 hover:border-gray-600 hover:bg-gray-800/30"
                      >
                        <Edit3 className="h-4 w-4" />
                      </Button>
                    )}

                    {/* Remove Button */}
                    <Button
                      onClick={() => removeStoreLink(index)}
                      variant="outline"
                      size="sm"
                      className="border-gray-700 text-gray-400 hover:text-red-300 hover:border-red-400/50 hover:bg-red-900/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Editable Fields - Slide Down Animation */}
                <AnimatePresence>
                  {(link.isEditing || !link.id) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ 
                        duration: 0.3, 
                        ease: "easeInOut",
                        opacity: { duration: 0.2 }
                      }}
                      style={{ overflow: 'hidden' }}
                    >
                      <div className="px-4 pb-4 border-t border-gray-700/50 pt-4">
                        <div className="space-y-4">
                          {/* Store Selection */}
                          <div>
                            <Label className="text-xs text-gray-400 mb-2 block">Store</Label>
                            <Select
                              value={link.store_name}
                              onValueChange={(value) => updateStoreLink(index, { store_name: value, isEditing: true })}
                            >
                              <SelectTrigger className="bg-gray-800/50 border-gray-700">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {availableStores.map((store) => (
                                  <SelectItem key={store} value={store}>
                                    <div className="flex items-center gap-2">
                                      {getStoreIcon(store)}
                                      {store}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Current Price */}
                            <div>
                              <Label className="text-xs text-gray-400 mb-2 block">Current Price</Label>
                              <Input
                                placeholder="$19.99"
                                value={link.price}
                                onChange={(e) => updateStoreLink(index, { price: e.target.value, isEditing: true })}
                                className="bg-gray-800/50 border-gray-700"
                              />
                            </div>

                            {/* Original Price */}
                            <div>
                              <Label className="text-xs text-gray-400 mb-2 block">Original Price (Optional)</Label>
                              <Input
                                placeholder="$29.99"
                                value={link.original_price || ''}
                                onChange={(e) => updateStoreLink(index, { original_price: e.target.value, isEditing: true })}
                                className="bg-gray-800/50 border-gray-700"
                              />
                            </div>
                          </div>

                          {/* Affiliate URL */}
                          <div>
                            <Label className="text-xs text-gray-400 mb-2 block">Affiliate URL</Label>
                            <Input
                              placeholder="https://store.com/game"
                              value={link.store_url}
                              onChange={(e) => updateStoreLink(index, { store_url: e.target.value, isEditing: true })}
                              className="bg-gray-800/50 border-gray-700"
                            />
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-400">
            <ShoppingCart className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No store links configured</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-4 border-t border-gray-700">
          <Button
            onClick={addStoreLink}
            variant="outline"
            disabled={localStoreLinks.length >= 3}
            className="border-primary-accent text-primary-accent hover:bg-primary-accent/10 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4 mr-2" />
            {localStoreLinks.length >= 3 ? 'Maximum Links Reached' : 'Add Store Link'}
          </Button>
          
          {localStoreLinks.length > 0 && localStoreLinks.some(link => link.isEditing || !link.id) && (
            <Button
              onClick={saveStoreLinks}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold"
            >
              {isSaving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Store Links
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const MultipleFeaturedBannersConfig: React.FC<MultipleFeaturedBannersConfigProps> = ({
  userId,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [userReviews, setUserReviews] = useState<UserReview[]>([]);
  const [sortBy, setSortBy] = useState<'created_at' | 'views_count' | 'likes_count' | 'updated_at' | 'rating'>('created_at');
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Multiple banners management
  const [featuredBanners, setFeaturedBanners] = useState<FeaturedBanner[]>([]);
  const [activeBannerTab, setActiveBannerTab] = useState('1');
  const [selectedReviews, setSelectedReviews] = useState<{[key: number]: UserReview | null}>({});
  
  // Store links module state
  const [showConfirmButton, setShowConfirmButton] = useState<{[key: number]: boolean}>({});
  const [showStoreLinksModule, setShowStoreLinksModule] = useState<{[key: number]: boolean}>({});

  const { toast } = useToast();

  // Debug logging
  useEffect(() => {
    console.log('MultipleFeaturedBannersConfig rendered with userId:', userId);
  }, [userId]);

  // Available stores list
  const availableStores = [
    'Eneba', 'G2A', 'GOG', 'HRK', 'Instant Gaming', 'Kinguin', 'Nuuvem'
  ];

  // Store icon mapping function with error handling
  const getStoreIcon = (storeName: string) => {
    const iconProps = { className: "h-4 w-4" };
    try {
      switch (storeName) {
        case 'Eneba': return <EnebaIcon {...iconProps} />;
        case 'G2A': return <G2AIcon {...iconProps} />;
        case 'GOG': return <GOGIcon {...iconProps} />;
        case 'HRK': return <HRKIcon {...iconProps} />;
        case 'Instant Gaming': return <InstantGamingIcon {...iconProps} />;
        case 'Kinguin': return <KinguinIcon {...iconProps} />;
        case 'Nuuvem': return <NuuvemIcon {...iconProps} />;
        default: return <ShoppingCart {...iconProps} />;
      }
    } catch (error) {
      console.error('Error rendering store icon:', error);
      return <ShoppingCart {...iconProps} />;
    }
  };

  // Debounce search term
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const loadUserReviews = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('Loading user reviews for userId:', userId, 'with sort:', sortBy);

      // The review-service getUserReviews only takes userId and optional status
      const response = await getUserReviews(userId, 'published');

      if (response && Array.isArray(response)) {
        // Convert Review[] to UserReview[] format
        const convertedReviews: UserReview[] = response.map(review => ({
          id: review.id,
          user_id: review.authorId || '',
          game_name: review.gameName || '',
          game_image: undefined, // Not used in Review interface
          igdb_cover_url: undefined, // Not used in Review interface
          mainImageUrl: undefined, // Not available in Review interface
          igdbCoverUrl: review.igdbCoverUrl,
          rating: review.overallScore / 10, // Convert back to 0-10 scale
          review_text: JSON.stringify(review.contentLexical || {}),
          created_at: review.createdAt instanceof Date ? review.createdAt.toISOString() : review.createdAt.toString(),
          updated_at: review.createdAt instanceof Date ? review.createdAt.toISOString() : review.createdAt.toString(),
          likes_count: 0, // Not available in Review interface
          views_count: 0, // Not available in Review interface
          comments_count: 0, // Not available in Review interface
          is_featured: review.featuredHomepage || false,
          is_public: review.status === 'published',
          platform: review.platforms?.[0],
          playtime_hours: undefined,
          tags: review.tags || [],
          title: review.title || '',
          slug: review.slug || ''
        }));
        setUserReviews(convertedReviews);
      } else {
        toast({
          title: "Error",
          description: "Failed to load reviews. Please try again.",
          variant: "destructive",
        });
        setUserReviews([]);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load reviews');
      toast({
        title: "Error",
        description: "Failed to load reviews",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userId, sortBy, debouncedSearchTerm]);

  const loadFeaturedBanners = useCallback(async () => {
    try {
      
      const response = await fetch('/api/u/featured-banners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getBanners', userId })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.success) {
        // Update selected reviews state and load store links
        const newSelectedReviews: {[key: number]: UserReview | null} = {};
        const bannersWithStoreLinks = await Promise.all(
          result.data?.map(async (banner: FeaturedBanner) => {
            if (banner.review) {
              // Convert API format (snake_case) to UserReview format (camelCase)
              const apiReview = banner.review as any; // API response format
              const convertedReview: UserReview = {
                id: apiReview.id || '',
                user_id: banner.user_id,
                game_name: apiReview.game_name || '',
                game_image: undefined,
                igdb_cover_url: apiReview.igdb_cover_url,
                main_image_url: apiReview.main_image_url,
                mainImageUrl: apiReview.main_image_url, // Convert snake_case to camelCase
                igdbCoverUrl: apiReview.igdb_cover_url, // Convert snake_case to camelCase
                rating: (apiReview.overall_score || 0) / 10, // Convert back to 0-10 scale
                review_text: JSON.stringify(apiReview.content_lexical || {}),
                created_at: apiReview.created_at || new Date().toISOString(),
                updated_at: apiReview.created_at || new Date().toISOString(),
                likes_count: apiReview.like_count || 0,
                views_count: apiReview.view_count || 0,
                comments_count: 0, // Not available in API response
                is_featured: true, // This is a featured banner
                is_public: true, // Assumption since it's featured
                platform: apiReview.played_on,
                playtime_hours: undefined,
                tags: [],
                title: apiReview.title || '',
                slug: apiReview.slug || ''
              };
              
              newSelectedReviews[banner.display_order] = convertedReview;
            }
            
            // Load store links for each banner
            if (banner.id) {
              try {
                const storeLinksResponse = await fetch('/api/u/featured-banners', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ action: 'getStoreLinks', userId, bannerId: banner.id })
                });
                
                if (storeLinksResponse.ok) {
                  const storeLinksResult = await storeLinksResponse.json();
                  if (storeLinksResult.success) {
                    banner.store_links = storeLinksResult.data;
                  }
                }
              } catch (error) {
                // Silently handle store links loading errors
              }
            }
            
            return banner;
          }) || []
        );
        
        setFeaturedBanners(bannersWithStoreLinks);
        setSelectedReviews(newSelectedReviews);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load featured banners');
    }
  }, [userId]);

  // Load data on mount
  useEffect(() => {
    if (userId) {
      loadUserReviews();
      loadFeaturedBanners();
    }
  }, [userId, sortBy, loadUserReviews, loadFeaturedBanners]);

  // Reload when search changes
  useEffect(() => {
    if (userId && debouncedSearchTerm !== undefined) {
      loadUserReviews();
    }
  }, [debouncedSearchTerm, loadUserReviews, userId]);

  const handleSetFeaturedBanner = async (displayOrder: number) => {
    const selectedReview = selectedReviews[displayOrder];
    if (!selectedReview) return;

    try {
      setIsSaving(true);
      
      const response = await fetch('/api/u/featured-banners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'setBanner',
          userId,
          reviewId: selectedReview.id,
          displayOrder
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.success) {
        toast({
          title: "Success",
          description: `Banner ${displayOrder} updated with "${selectedReview.game_name}"!`,
        });
        
        // Reload banners
        await loadFeaturedBanners();
      } else {
        throw new Error(result.error || 'Failed to set featured banner');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to set featured banner',
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleRemoveFeaturedBanner = async (displayOrder: number) => {
    try {
      setIsSaving(true);
      
      const response = await fetch('/api/u/featured-banners', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'removeBanner',
          userId,
          displayOrder
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.success) {
        toast({
          title: "Success",
          description: `Banner ${displayOrder} removed successfully!`,
        });
        
        // Clear selection and reload
        setSelectedReviews(prev => ({ ...prev, [displayOrder]: null }));
        await loadFeaturedBanners();
      } else {
        throw new Error(result.error || 'Failed to remove featured banner');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to remove featured banner',
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update button states when banners change
  useEffect(() => {
    const newShowConfirmButton: {[key: number]: boolean} = {};
    const newShowStoreLinksModule: {[key: number]: boolean} = {};

    [1, 2, 3].forEach(displayOrder => {
      const selectedReview = selectedReviews[displayOrder];
      const currentBanner = featuredBanners.find(b => b.display_order === displayOrder);

      newShowConfirmButton[displayOrder] = !!selectedReview && !currentBanner;
      newShowStoreLinksModule[displayOrder] = !!currentBanner;
    });
    
    setShowConfirmButton(newShowConfirmButton);
    setShowStoreLinksModule(newShowStoreLinksModule);
  }, [selectedReviews, featuredBanners]);

  const handleConfirmReview = async (displayOrder: number) => {
    const selectedReview = selectedReviews[displayOrder];
    if (!selectedReview) return;
    
    try {
      setIsSaving(true);
      await handleSetFeaturedBanner(displayOrder);
      
      // Update states
      setShowConfirmButton(prev => ({ ...prev, [displayOrder]: false }));
      setShowStoreLinksModule(prev => ({ ...prev, [displayOrder]: true }));
    } catch (error) {
    } finally {
      setIsSaving(false);
    }
  };

  const renderBannerConfig = (displayOrder: number) => {
    const selectedReview = selectedReviews[displayOrder];
    const currentBanner = featuredBanners.find(b => b.display_order === displayOrder);
    
    // Get review IDs that are already featured in other banners
    const otherFeaturedReviewIds = featuredBanners
      .filter(banner => banner.display_order !== displayOrder)
      .map(banner => banner.review_id);
    
    // Filter out reviews that are already featured in other banners
    const availableReviews = userReviews.filter(review => 
      !otherFeaturedReviewIds.includes(review.id)
    );

    return (
      <div className="space-y-6">
        {/* Review Selection */}
        <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-gray-800">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Select Review for Banner {displayOrder}
              {currentBanner && (
                <Badge variant="secondary" className="ml-2 bg-purple-500/20 text-purple-400 border-purple-500/30 text-xs font-mono uppercase tracking-wide">Active</Badge>
              )}
            </CardTitle>
            <p className="text-xs text-gray-400 mt-1 font-mono">
              Choose which review to feature in this banner position
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Always show the selection interface */}
            <div>
              {/* Current Banner Display - if exists */}
              {currentBanner && currentBanner.review && (
                <div className="p-4 bg-slate-800/30 border border-slate-600/40 rounded-lg mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-16 h-20 bg-gray-800/50 rounded-lg border border-gray-700/30 flex items-center justify-center overflow-hidden flex-shrink-0">
                      {(() => {
                        // Use the exact same logic as the review list - get from selectedReviews, not currentBanner
                        const review = selectedReviews[displayOrder];
                        if (review) {
                          const isValidUrl = (url: any) => url && url !== 'undefined' && url !== 'null' && typeof url === 'string' && url.trim() !== '';
                          
                          // Try ALL possible image fields from both sources
                          const imageSource = 
                            (isValidUrl(review.igdbCoverUrl) && review.igdbCoverUrl) ||
                            (isValidUrl(review.igdb_cover_url) && review.igdb_cover_url) ||
                            (isValidUrl(review.mainImageUrl) && review.mainImageUrl) ||
                            (isValidUrl(review.main_image_url) && review.main_image_url) ||
                            (isValidUrl(review.game_image) && review.game_image) ||
                            null;



                          return imageSource ? (
                            <Image
                              src={imageSource}
                              alt={review.game_name || 'Game'}
                              width={64}
                              height={80}
                              className="w-full h-full object-cover"
                              unoptimized
                            />
                          ) : (
                            <Gamepad2 className="h-6 w-6 text-slate-400" />
                          );
                        }
                        return <Gamepad2 className="h-6 w-6 text-slate-400" />;
                      })()}
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-slate-200">{currentBanner.review.game_name}</div>
                      <div className="text-sm text-slate-300">{currentBanner.review.title}</div>
                    </div>
                    <Button
                      onClick={() => handleRemoveFeaturedBanner(displayOrder)}
                      variant="outline"
                      size="sm"
                      className="border-gray-700 text-gray-400 hover:text-red-300 hover:border-red-400/50 hover:bg-red-900/10"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Search and Sort - only show when no current banner exists */}
              {!currentBanner && (
                <>
                  <div className="flex gap-4 mb-6">
                    <div className="flex-1">
                      <Input
                        ref={searchInputRef}
                        placeholder="Search your reviews..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="bg-gray-800/50 border-gray-700 focus:border-primary-accent"
                      />
                    </div>
                    <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                      <SelectTrigger className="w-48 bg-gray-800/50 border-gray-700">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="created_at">Most Recent</SelectItem>
                        <SelectItem value="likes_count">Most Liked</SelectItem>
                        <SelectItem value="views_count">Most Viewed</SelectItem>
                        <SelectItem value="rating">Highest Rated</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Review List */}
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    
                    {isLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-primary-accent" />
                        <span className="ml-2 text-sm text-gray-400">Loading reviews...</span>
                      </div>
                    ) : availableReviews.length > 0 ? (
                      availableReviews
                        .filter(review => 
                          !debouncedSearchTerm || 
                          review.game_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
                          review.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
                        )
                        .map((review) => (
                          <div
                            key={review.id}
                            onClick={() => {
                              setSelectedReviews(prev => ({ ...prev, [displayOrder]: review }));
                            }}
                            className={`
                              p-3 rounded-lg border cursor-pointer transition-all duration-200
                              ${selectedReview?.id === review.id
                                ? 'border-primary-accent bg-primary-accent/10'
                                : 'border-gray-700 bg-gray-800/30 hover:border-gray-600 hover:bg-gray-800/50'
                              }
                            `}
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-16 h-20 bg-gray-800/50 rounded-lg border border-gray-700/30 flex items-center justify-center flex-shrink-0 overflow-hidden">
                                {(() => {
                                  // Helper function to check if a value is valid (not null, undefined, empty string, or string "undefined"/"null")
                                  const isValidUrl = (url: any) => url && url !== 'undefined' && url !== 'null' && typeof url === 'string' && url.trim() !== '';

                                  // Support both naming conventions and clean up undefined strings
                                  const igdbUrl = isValidUrl(review.igdbCoverUrl) ? review.igdbCoverUrl :
                                                 isValidUrl(review.igdb_cover_url) ? review.igdb_cover_url : null;
                                  const gameImage = isValidUrl(review.mainImageUrl) ? review.mainImageUrl :
                                                   isValidUrl(review.main_image_url) ? review.main_image_url : null;
                                  const imageSource = igdbUrl || gameImage;
                                  

                                  return imageSource ? (
                                    <Image
                                      src={imageSource}
                                      alt={review.game_name}
                                      width={64}
                                      height={80}
                                      className="w-full h-full object-cover"
                                      unoptimized
                                    />
                                  ) : (
                                    <Gamepad2 className="h-6 w-6 text-gray-500" />
                                  );
                                })()}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold text-sm truncate">{review.game_name}</div>
                                <div className="text-xs text-gray-400 truncate">{review.title}</div>
                                <div className="flex items-center gap-4 mt-1">
                                  <div className="flex items-center gap-1">
                                    <Heart className="h-3 w-3 text-red-400" />
                                    <span className="text-xs">{review.likes_count}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Eye className="h-3 w-3 text-blue-400" />
                                    <span className="text-xs">{review.views_count}</span>
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {new Date(review.created_at).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                              {selectedReview?.id === review.id && (
                                <Check className="h-5 w-5 text-primary-accent flex-shrink-0" />
                              )}
                            </div>
                          </div>
                        ))
                    ) : (
                      <div className="text-center py-8 text-gray-400">
                        <Gamepad2 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>{userReviews.length > 0 ? 'All reviews are already featured in other banners' : 'No reviews found'}</p>
                      </div>
                    )}
                  </div>

                  {/* Confirm Button - only show when review is selected and no banner exists */}
                  {showConfirmButton[displayOrder] && selectedReview && (
                    <div className="pt-4 border-t border-gray-700">
                      <Button
                        onClick={() => {
                          handleConfirmReview(displayOrder);
                        }}
                        disabled={isSaving}
                        className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold"
                        size="lg"
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Check className="h-4 w-4 mr-2" />
                        )}
                        Confirm Selection: "{selectedReview.game_name}"
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Store Links Module - only show when banner is confirmed */}
        {showStoreLinksModule[displayOrder] && currentBanner && (
          <>
            
            <StoreLinksSection 
              bannerId={currentBanner.id || ''}
              userId={userId}
              displayOrder={displayOrder}
              storeLinks={currentBanner.store_links || []}
              onStoreLinksUpdate={loadFeaturedBanners}
            />
          </>
        )}
      </div>
    );
  };

  // Error boundary display
  if (error) {
    return (
      <Card className={`bg-gradient-to-br from-gray-900 to-gray-800 border-gray-800 ${className}`}>
        <CardHeader>
          <CardTitle className="text-xl font-mono flex items-center gap-2 text-red-400">
            <AlertCircle className="h-6 w-6" />
            Featured Banners Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-red-300 mb-2">Failed to load featured banners configuration:</p>
            <p className="text-red-200 text-sm">{error}</p>
            <Button 
              onClick={() => setError(null)} 
              variant="outline" 
              className="mt-3 border-red-500 text-red-500 hover:bg-red-500/10"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

    return (
    <Card className={`bg-gradient-to-br from-gray-900 to-gray-800 border-gray-800 ${className}`}>
      <CardHeader 
        className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg text-white font-mono">
              <span className="text-purple-400 mr-1">//</span>
              Multiple Featured Banners
            </CardTitle>
            <p className="text-xs text-gray-400 mt-1 font-mono">
              Configure up to 3 featured banners for your profile with up to 3 store links each
            </p>
          </div>
          <div className="text-gray-400 hover:text-white ml-4">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </div>
        </div>
      </CardHeader>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ 
              duration: 0.3, 
              ease: "easeInOut",
              opacity: { duration: 0.2 }
            }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent>
              <Tabs value={activeBannerTab} onValueChange={setActiveBannerTab}>
                <TabsList className="grid w-full grid-cols-3 bg-gray-800/40 rounded-lg border border-gray-700/30">
                  <TabsTrigger 
                    value="1" 
                    className="font-mono text-xs uppercase tracking-wide transition-all duration-200 
                               data-[state=active]:bg-purple-600/80 data-[state=active]:text-white
                               data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-gray-200 
                               data-[state=inactive]:hover:bg-gray-700/30 rounded-md py-2"
                  >
                    Banner 1
                  </TabsTrigger>
                  <TabsTrigger 
                    value="2" 
                    className="font-mono text-xs uppercase tracking-wide transition-all duration-200 
                               data-[state=active]:bg-purple-600/80 data-[state=active]:text-white
                               data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-gray-200 
                               data-[state=inactive]:hover:bg-gray-700/30 rounded-md py-2"
                  >
                    Banner 2
                  </TabsTrigger>
                  <TabsTrigger 
                    value="3" 
                    className="font-mono text-xs uppercase tracking-wide transition-all duration-200 
                               data-[state=active]:bg-purple-600/80 data-[state=active]:text-white
                               data-[state=inactive]:text-gray-400 data-[state=inactive]:hover:text-gray-200 
                               data-[state=inactive]:hover:bg-gray-700/30 rounded-md py-2"
                  >
                    Banner 3
                  </TabsTrigger>
                </TabsList>
                
                <div className="relative mt-6 overflow-hidden">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeBannerTab}
                      initial={{ x: 300, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: -300, opacity: 0 }}
                      transition={{ 
                        type: "spring", 
                        stiffness: 300, 
                        damping: 30,
                        opacity: { duration: 0.2 }
                      }}
                      className="w-full"
                    >
                      {activeBannerTab === "1" && renderBannerConfig(1)}
                      {activeBannerTab === "2" && renderBannerConfig(2)}
                      {activeBannerTab === "3" && renderBannerConfig(3)}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </Tabs>

              {/* Information Box */}
              <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="text-xs">
                    <div className="font-medium text-blue-300 mb-2">Banner Configuration Guide</div>
                    <div className="text-blue-200/80 space-y-2">
                      <div className="space-y-1">
                        <div className="font-medium text-blue-300">Store Links:</div>
                        <div>• Each banner supports up to 3 store links maximum</div>
                        <div>• Add different stores to give users price comparison options</div>
                      </div>
                      <div className="space-y-1">
                        <div className="font-medium text-blue-300">Currency Support:</div>
                        <div>• USD: $19.99 | EUR: €18.50 | BRL: R$89,90</div>
                        <div>• GBP: £16.99 | CAD: C$24.99 | AUD: A$27.50</div>
                        <div>• Use any currency symbol - prices display exactly as entered</div>
                        <div>• Set your preferred currency in Account Configuration (coming soon)</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
};

export default MultipleFeaturedBannersConfig;