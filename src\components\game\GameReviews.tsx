'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import Link from 'next/link';
import { GameReviewData } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScoreCircle } from '@/components/ui/ScoreCircle';
import { 
  Heart, 
  Eye, 
  Calendar, 
  User, 
  ChevronRight,
  Star,
  Filter,
  SortDesc
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface GameReviewsProps {
  gameId: string;
  gameSlug: string;
  limit?: number;
  showPagination?: boolean;
}

type SortOption = 'newest' | 'oldest' | 'highest_rated' | 'lowest_rated' | 'most_liked' | 'most_viewed';

export default function GameReviews({ 
  gameId, 
  gameSlug, 
  limit = 10, 
  showPagination = true 
}: GameReviewsProps) {
  const [offset, setOffset] = useState(0);
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [allReviews, setAllReviews] = useState<GameReviewData[]>([]);

  const { data, isLoading, error } = useQuery({
    queryKey: ['game-reviews', gameSlug, offset, limit, sortBy],
    queryFn: async () => {
      const response = await fetch(
        `/api/games/by-slug/${gameSlug}/reviews?limit=${limit}&offset=${offset}&sort=${sortBy}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update all reviews when new data comes in
  useEffect(() => {
    if (data?.reviews) {
      if (offset === 0) {
        setAllReviews(data.reviews);
      } else {
        setAllReviews(prev => [...prev, ...data.reviews]);
      }
    }
  }, [data, offset]);

  const formatDate = (dateStr: string) => {
    try {
      return new Date(dateStr).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const handleLoadMore = () => {
    setOffset(prev => prev + limit);
  };

  const handleSortChange = (newSort: SortOption) => {
    setSortBy(newSort);
    setOffset(0);
    setAllReviews([]);
  };

  const getSortLabel = (sort: SortOption) => {
    switch (sort) {
      case 'newest': return 'Newest First';
      case 'oldest': return 'Oldest First';
      case 'highest_rated': return 'Highest Rated';
      case 'lowest_rated': return 'Lowest Rated';
      case 'most_liked': return 'Most Liked';
      case 'most_viewed': return 'Most Viewed';
      default: return 'Newest First';
    }
  };

  if (isLoading && allReviews.length === 0) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-6 bg-gray-800/50 border-gray-700 animate-pulse">
            <div className="flex space-x-4">
              <div className="w-16 h-16 bg-gray-700 rounded-lg" />
              <div className="flex-1 space-y-3">
                <div className="h-4 bg-gray-700 rounded w-3/4" />
                <div className="h-3 bg-gray-700 rounded w-1/2" />
                <div className="h-3 bg-gray-700 rounded w-full" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 bg-red-900/20 border-red-500/30">
        <div className="text-center text-red-300">
          <p>Failed to load reviews. Please try again later.</p>
        </div>
      </Card>
    );
  }

  if (!allReviews.length && !isLoading) {
    return (
      <Card className="p-8 bg-gray-800/50 border-gray-700">
        <div className="text-center">
          <div className="text-4xl text-gray-400 mb-4">📝</div>
          <h3 className="text-lg font-semibold text-white mb-2">No Reviews Yet</h3>
          <p className="text-gray-400 mb-4">
            Be the first to review this game and help other gamers!
          </p>
          <Button asChild className="bg-blue-600 hover:bg-blue-500">
            <Link href="/reviews/new">Write a Review</Link>
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Sort Options */}
      {showPagination && allReviews.length > 0 && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-xl font-bold text-white">Community Reviews</h2>
            <p className="text-sm text-gray-400">
              {allReviews.length} review{allReviews.length !== 1 ? 's' : ''} loaded
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <SortDesc className="w-4 h-4" />
              <span>Sort by:</span>
            </div>
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-40 bg-gray-800 border-gray-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="highest_rated">Highest Rated</SelectItem>
                <SelectItem value="lowest_rated">Lowest Rated</SelectItem>
                <SelectItem value="most_liked">Most Liked</SelectItem>
                <SelectItem value="most_viewed">Most Viewed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {allReviews.map((review, index) => (
          <Card 
            key={`${review.id}-${index}`} 
            className="p-6 bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors"
          >
            <div className="flex space-x-4">
              {/* Review Image */}
              <div className="flex-shrink-0">
                {review.main_image_url ? (
                  <div className="w-16 h-16 rounded-lg overflow-hidden">
                    <Image
                      src={review.main_image_url}
                      alt={review.title}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-16 h-16 bg-gray-700 rounded-lg flex items-center justify-center">
                    <Star className="w-6 h-6 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Review Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <Link 
                      href={`/reviews/view/${review.slug}`}
                      className="group"
                    >
                      <h3 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors line-clamp-1">
                        {review.title}
                      </h3>
                    </Link>
                    
                    {/* Author Info */}
                    <div className="flex items-center space-x-3 mt-2">
                      <div className="flex items-center space-x-2">
                        <Avatar className="w-6 h-6">
                          <AvatarImage src={review.author_avatar_url || undefined} />
                          <AvatarFallback className="bg-gray-600 text-xs">
                            {review.author_name?.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-gray-300">{review.author_name}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1 text-xs text-gray-400">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(review.created_at)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Score */}
                  {review.overall_score && (
                    <div className="ml-4">
                      <ScoreCircle score={review.overall_score} size="sm" />
                    </div>
                  )}
                </div>

                {/* Excerpt */}
                {review.excerpt && (
                  <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                    {review.excerpt}
                  </p>
                )}

                {/* Review Stats */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-xs text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Heart className="w-3 h-3" />
                      <span>{formatNumber(review.likes_count || 0)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3" />
                      <span>{formatNumber(review.views_count || 0)}</span>
                    </div>
                  </div>

                  <Link 
                    href={`/reviews/view/${review.slug}`}
                    className="flex items-center space-x-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <span>Read full review</span>
                    <ChevronRight className="w-3 h-3" />
                  </Link>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Load More Button */}
      {showPagination && data?.pagination?.hasMore && (
        <div className="text-center">
          <Button
            onClick={handleLoadMore}
            disabled={isLoading}
            variant="outline"
            className="bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
          >
            {isLoading ? 'Loading...' : 'Load More Reviews'}
          </Button>
        </div>
      )}

      {/* Call to Action */}
      {allReviews.length > 0 && (
        <Card className="p-6 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">Share Your Experience</h3>
            <p className="text-gray-300 mb-4">
              Have you played this game? Help the community with your review!
            </p>
            <Button asChild className="bg-blue-600 hover:bg-blue-500">
              <Link href="/reviews/new">Write Your Review</Link>
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}