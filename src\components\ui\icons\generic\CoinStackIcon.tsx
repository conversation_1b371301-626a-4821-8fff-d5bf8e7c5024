import React from 'react';

const CoinStackIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <ellipse cx="12" cy="18" rx="8" ry="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <ellipse cx="12" cy="15" rx="8" ry="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <ellipse cx="12" cy="12" rx="8" ry="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <ellipse cx="12" cy="9" rx="8" ry="2"/>
    <path d="M12 6v2M10 7h4M11 5h2"/>
    <rect x="8" y="11" width="1" height="1"/>
    <rect x="16" y="11" width="1" height="1"/>
    <rect x="8" y="14" width="1" height="1"/>
    <rect x="16" y="14" width="1" height="1"/>
    <rect x="8" y="17" width="1" height="1"/>
    <rect x="16" y="17" width="1" height="1"/>
  </svg>
);

export default CoinStackIcon;