'use client';

import React, { memo, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import '@/components/style/navColors.css';
import './style/reviewControls.css';
import {
  Save,
  Loader2,
  Sparkles,
  MessageCircle,
  Bell,
  Settings,
  Lock,
  Clock,
  ExternalLink,
  ShieldAlert
} from 'lucide-react';

// Simple Toggle Switch Component - Matching AddBattleVisuals style
const SimpleToggle = ({ checked, onChange, disabled = false }: {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}) => (
  <button
    type="button"
    onClick={() => !disabled && onChange(!checked)}
    disabled={disabled}
    aria-label={checked ? 'Disable setting' : 'Enable setting'}
    className={`
      relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ease-in-out
      ${checked
        ? 'bg-violet-600/60'
        : 'bg-slate-600/50'
      }
      ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:ring-offset-2 focus:ring-offset-slate-900
    `}
  >
    <span
      className={`
        inline-block h-3 w-3 rounded-full bg-white shadow-sm transition-transform duration-200
        ${checked ? 'translate-x-5' : 'translate-x-1'}
      `}
    />
  </button>
);



const ReviewCreationNavbar = memo(({
  formCompletion,
  isPublishing,
  handlePublishReview,
  reviewSettings,
  onReviewSettingsChange,
  isDraftSaving,
  isPublishingWithModal,
  isEditMode
}: {
  formCompletion: number;
  isPublishing: boolean;
  handlePublishReview: (intendedStatus: 'publish' | 'draft') => void;
  reviewSettings?: {
    enableComments: boolean;
    enableNotifications: boolean;
    makePrivate: boolean;
  };
  onReviewSettingsChange?: (settings: {
    enableComments: boolean;
    enableNotifications: boolean;
    makePrivate: boolean;
  }) => void;
  isDraftSaving?: boolean;
  isPublishingWithModal?: boolean;
  isEditMode?: boolean;
}) => {

  // Settings state with props fallback - CHANGED: Comments now default to FALSE
  const [enableComments, setEnableComments] = useState(reviewSettings?.enableComments ?? false);
  const [enableNotifications, setEnableNotifications] = useState(reviewSettings?.enableNotifications ?? true);
  const [makePrivate, setMakePrivate] = useState(reviewSettings?.makePrivate ?? false);

  // Modal states for comment moderation warning
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);

  // Handle comment toggle with moderation warning
  const handleCommentToggle = (value: boolean) => {
    if (value && !enableComments) {
      // User is trying to enable comments - show warning modal
      setIsCommentModalOpen(true);
    } else {
      // User is disabling comments - allow directly
      handleSettingChange('comments', value);
    }
  };

  // Handle comment enablement after user confirms responsibility
  const handleConfirmCommentModeration = () => {
    setIsCommentModalOpen(false);
    setEnableComments(true);
    
    const newSettings = {
      enableComments: true,
      enableNotifications,
      makePrivate
    };
    
    // Notify parent component of changes
    onReviewSettingsChange?.(newSettings);
    
    // Show confirmation modal with dashboard link
    setIsConfirmationModalOpen(true);
  };

  // Handle setting changes and notify parent
  const handleSettingChange = (settingId: string, value: boolean) => {
    let newSettings = {
      enableComments,
      enableNotifications,
      makePrivate
    };

    switch (settingId) {
      case 'comments':
        setEnableComments(value);
        newSettings.enableComments = value;
        break;
      case 'notifications':
        setEnableNotifications(value);
        newSettings.enableNotifications = value;
        break;
      case 'private':
        setMakePrivate(value);
        newSettings.makePrivate = value;
        break;
    }

    // Notify parent component of changes
    onReviewSettingsChange?.(newSettings);
  };

  const reviewSettingsConfig = [
    {
      id: 'comments',
      label: 'Enable Comments',
      description: 'Allow users to comment on your review',
      icon: MessageCircle,
      value: enableComments,
      onChange: handleCommentToggle,
      disabled: false
    },
    {
      id: 'notifications',
      label: 'Enable Notifications',
      description: 'Get notified of interactions',
      icon: Bell,
      value: enableNotifications,
      onChange: (value: boolean) => handleSettingChange('notifications', value),
      disabled: true, // Disabled with "Soon" badge
      badge: 'Soon'
    },
    {
      id: 'private',
      label: 'Make Private',
      description: 'Only you can see this review',
      icon: Lock,
      value: makePrivate,
      onChange: (value: boolean) => handleSettingChange('private', value),
      disabled: false
    }
  ];

  return (
    <div className="review-creation-controls">
      {/* Review Settings */}
      <div className="controls-section">
        <div className="section-header-minimal">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-md bg-slate-800/60">
              <Settings size={14} className="text-slate-400" />
            </div>
            <div>
              <div className="font-mono text-sm">
                <span className="text-violet-400">//</span>
                <span className="text-slate-300 ml-1">Review Settings</span>
              </div>
              <p className="text-slate-500 text-xs mt-1">
                Configure review behavior and visibility
              </p>
            </div>
          </div>
        </div>

        <div className="settings-content-inner">
          <div className="settings-grid">
            {reviewSettingsConfig.map((setting) => {
              const IconComponent = setting.icon;
              return (
                <div key={setting.id} className="setting-item">
                  <div className="setting-info">
                    <div className="setting-header">
                      <IconComponent size={16} className="setting-icon" />
                      <span className="setting-label">{setting.label}</span>
                      {setting.badge && (
                        <Badge
                          variant="secondary"
                          className="ml-2 text-xs bg-violet-500/20 text-violet-300 border-violet-500/30"
                        >
                          {setting.badge}
                        </Badge>
                      )}
                    </div>
                    <span className="setting-description">{setting.description}</span>
                  </div>
                  <SimpleToggle
                    checked={setting.value}
                    onChange={setting.onChange}
                    disabled={setting.disabled}
                  />
                </div>
              );
            })}
          </div>


        </div>
      </div>

      {/* Action Buttons Footer */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 gap-3">
        <div className="flex items-center space-x-2 min-w-0">
          <div className="w-2 h-2 bg-cyan-400/60 rounded-full animate-pulse flex-shrink-0" />
          <span className="text-sm text-slate-400/80 font-mono break-words">
            // Review settings configured - {formCompletion}% complete
          </span>
        </div>

        <div className="flex items-center justify-end gap-3 flex-shrink-0">
          <Button
            onClick={() => handlePublishReview('draft')}
            disabled={isPublishing || isDraftSaving}
            className="draft-button"
          >
            {isDraftSaving ? (
              <>
                <Loader2 size={16} className="mr-2 animate-spin" />
                <span className="text-slate-400/60">&lt;</span>
                Saving...
                <span className="text-slate-400/60">/&gt;</span>
              </>
            ) : (
              <>
                <Save size={16} className="mr-2" />
                <span className="text-slate-400/60">&lt;</span>
                Save Draft
                <span className="text-slate-400/60">/&gt;</span>
              </>
            )}
          </Button>

          <Button
            onClick={() => handlePublishReview('publish')}
            disabled={isPublishing || isDraftSaving || isPublishingWithModal || formCompletion < 50}
            className="review-continue-button"
          >
            <div className="review-button-content">
              {isPublishingWithModal ? (
                <>
                  <Loader2 className="review-button-arrow animate-spin" />
                  <span>{isEditMode ? 'Saving...' : 'Publishing...'}</span>
                </>
              ) : isPublishing ? (
                <>
                  <Loader2 className="review-button-arrow animate-spin" />
                  <span>{isEditMode ? 'Saving...' : 'Publishing...'}</span>
                </>
              ) : (
                <>
                  <span className="review-code-brackets">&lt;</span>
                  {isEditMode ? 'Save Changes' : 'Publish Review'}
                  <span className="review-code-brackets">/&gt;</span>
                  <Sparkles className="review-button-arrow" />
                </>
              )}
            </div>
          </Button>
        </div>
      </div>

      {/* Comment Moderation Responsibility Modal */}
      <Dialog open={isCommentModalOpen} onOpenChange={setIsCommentModalOpen}>
        <DialogContent className="bg-slate-900/95 backdrop-blur-xl border border-orange-500/30 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-orange-300 font-mono flex items-center gap-2">
              <ShieldAlert size={20} className="text-orange-400" />
              <span className="text-orange-400/60">&lt;</span>
              Comment Moderation
              <span className="text-orange-400/60">/&gt;</span>
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              By enabling comments, you are responsible for moderating all comments on your review.
              <br /><br />
              <span className="text-orange-200 font-medium">
                This includes managing inappropriate content, spam, and ensuring a positive community experience.
              </span>
              <br /><br />
              Are you ready to take on this responsibility?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsCommentModalOpen(false)}
              className="border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmCommentModeration}
              className="bg-orange-600 hover:bg-orange-700 text-white border-orange-600"
            >
              <MessageCircle size={14} className="mr-2" />
              Yes, I Understand
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Modal with Dashboard Link */}
      <Dialog open={isConfirmationModalOpen} onOpenChange={setIsConfirmationModalOpen}>
        <DialogContent className="bg-slate-900/95 backdrop-blur-xl border border-green-500/30 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-green-300 font-mono flex items-center gap-2">
              <MessageCircle size={20} className="text-green-400" />
              <span className="text-green-400/60">&lt;</span>
              Comments Enabled
              <span className="text-green-400/60">/&gt;</span>
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              Comments have been enabled for your review.
              <br /><br />
              <span className="text-green-200 font-medium">
                You can moderate comments within your dashboard.
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsConfirmationModalOpen(false)}
              className="border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white"
            >
              Got it
            </Button>
            <Button
              onClick={() => {
                window.open('/u/dashboard?tab=comments', '_blank');
                setIsConfirmationModalOpen(false);
              }}
              className="bg-green-600 hover:bg-green-700 text-white border-green-600"
            >
              <ExternalLink size={14} className="mr-2" />
              Manage Comments
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
});

ReviewCreationNavbar.displayName = 'ReviewCreationNavbar';

export default ReviewCreationNavbar;
