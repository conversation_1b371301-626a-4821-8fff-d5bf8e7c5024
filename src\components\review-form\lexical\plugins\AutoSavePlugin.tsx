'use client';

import { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot } from 'lexical';
import { editorAutoSave, type AutoSaveOptions } from '@/lib/utils/editorAutoSave';
import { Save, Clock, AlertCircle } from 'lucide-react';

interface AutoSavePluginProps {
  userId: string;
  reviewId?: string;
  gameName?: string;
  reviewTitle?: string;
  debounceMs?: number;
  showIndicator?: boolean;
  isEnabled?: boolean;
  onStatusChange?: (status: 'idle' | 'pending' | 'saving' | 'saved' | 'error') => void;
}

export default function AutoSavePlugin({
  userId,
  reviewId,
  gameName,
  reviewTitle,
  debounceMs = 10000, // 10 seconds for circular progress
  showIndicator = true,
  isEnabled = true,
  onStatusChange,
}: AutoSavePluginProps) {
  const [editor] = useLexicalComposerContext();
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'pending' | 'saving' | 'saved' | 'error'>('idle');

  // Notify parent component of status changes
  useEffect(() => {
    onStatusChange?.(saveStatus);
  }, [saveStatus, onStatusChange]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const lastContentRef = useRef<string>('');
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const periodicSaveRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize autoSaveOptions to prevent unnecessary re-renders
  const autoSaveOptions: AutoSaveOptions = useMemo(() => ({
    userId,
    reviewId,
    gameName,
    reviewTitle,
    debounceMs,
  }), [userId, reviewId, gameName, reviewTitle, debounceMs]);

  // Load existing auto-save on mount
  useEffect(() => {
    if (!isEnabled || !userId) return;
    
    const existingSave = editorAutoSave.load(autoSaveOptions);
    if (existingSave) {
      setLastSaved(editorAutoSave.getTimeSinceLastSave(autoSaveOptions));
      
      console.log('🔍 AutoSavePlugin: Found existing auto-save from:', new Date(existingSave.timestamp));
      console.log('📝 Content length:', existingSave.content.length);
      console.log('🔑 Auto-save key components:', {
        userId: autoSaveOptions.userId,
        reviewId: autoSaveOptions.reviewId,
        gameName: autoSaveOptions.gameName,
        reviewTitle: autoSaveOptions.reviewTitle
      });
    } else {
      console.log('📭 AutoSavePlugin: No existing auto-save found');
    }
  }, [isEnabled, userId]);

  // Get current editor content
  const getCurrentContent = useCallback(() => {
    let currentContent = '';
    editor.read(() => {
      const root = $getRoot();
      currentContent = JSON.stringify(editor.getEditorState().toJSON());
    });
    return currentContent;
  }, [editor]);

  // Perform actual save operation
  const performSave = useCallback(async (content?: string) => {
    if (!isEnabled) {
      console.log('Auto-save skipped: disabled');
      return;
    }

    try {
      setSaveStatus('saving');
      const contentToSave = content || getCurrentContent();
      console.log('Auto-saving content...', {
        contentLength: contentToSave.length,
        userId: autoSaveOptions.userId,
        reviewId: autoSaveOptions.reviewId
      });
      editorAutoSave.saveImmediate(contentToSave, autoSaveOptions);
      lastContentRef.current = contentToSave;
      setSaveStatus('saved');
      console.log('Auto-save completed successfully');
    } catch (error) {
      console.error('Auto-save failed:', error);
      setSaveStatus('error');
    }
  }, [isEnabled, autoSaveOptions, getCurrentContent]);

  // Listen for editor changes (just to track content, no auto-save on changes)
  useEffect(() => {
    if (!isEnabled) return;
    
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const root = $getRoot();
        const content = JSON.stringify(editorState.toJSON());
        
        // Check if content actually changed
        if (content !== lastContentRef.current) {
          lastContentRef.current = content;
          setHasUnsavedChanges(true);
          // Note: No auto-save on changes - only periodic saves
        }
      });
    });
  }, [editor, isEnabled]);

  // Periodic auto-save timer (every 10 seconds regardless of changes)
  useEffect(() => {
    if (!isEnabled) return;

    let isCancelled = false;
    let cycleTimeout: NodeJS.Timeout | null = null;
    let saveTimeout: NodeJS.Timeout | null = null;

    const startPeriodicSave = () => {
      if (isCancelled) return;
      
      setSaveStatus('pending');

      // Start 10-second timer for the save
      saveTimeout = setTimeout(async () => {
        if (isCancelled) return;

        try {
          setSaveStatus('saving');
          const contentToSave = getCurrentContent();
          
          // Check if content is empty/default
          const emptyContent = '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
          const isEmptyContent = !contentToSave || contentToSave === emptyContent;
          
          if (isEmptyContent) {
            console.log('⏭️ AutoSavePlugin: Skipping save - content is empty');
            if (!isCancelled) {
              setSaveStatus('idle');
            }
            return;
          }
          
          console.log('💾 AutoSavePlugin: Auto-saving content...', {
            contentLength: contentToSave.length,
            userId: autoSaveOptions.userId,
            reviewId: autoSaveOptions.reviewId,
            gameName: autoSaveOptions.gameName,
            reviewTitle: autoSaveOptions.reviewTitle
          });
          
          editorAutoSave.saveImmediate(contentToSave, autoSaveOptions);
          lastContentRef.current = contentToSave;
          
          if (!isCancelled) {
            setSaveStatus('saved');
            console.log('✅ AutoSavePlugin: Auto-save completed successfully');
          }
        } catch (error) {
          console.error('❌ AutoSavePlugin: Auto-save failed:', error);
          if (!isCancelled) {
            setSaveStatus('error');
          }
        }

        // After 2 seconds, restart the cycle
        cycleTimeout = setTimeout(() => {
          if (!isCancelled) {
            setSaveStatus('idle');
            // Brief delay before restarting to avoid rapid cycles
            setTimeout(() => {
              if (!isCancelled) {
                startPeriodicSave();
              }
            }, 100);
          }
        }, 2000);
      }, debounceMs);
    };

    // Start the first cycle
    startPeriodicSave();

    return () => {
      isCancelled = true;
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
      if (cycleTimeout) {
        clearTimeout(cycleTimeout);
      }
      if (periodicSaveRef.current) {
        clearTimeout(periodicSaveRef.current);
      }
    };
  }, [isEnabled, debounceMs]); // Removed performSave dependency to prevent effect restart

  // Update lastSaved when status changes to saved
  useEffect(() => {
    if (saveStatus === 'saved') {
      setHasUnsavedChanges(false);
      setTimeout(() => {
        setLastSaved(editorAutoSave.getTimeSinceLastSave(autoSaveOptions));
      }, 100);
    }
  }, [saveStatus, autoSaveOptions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (periodicSaveRef.current) {
        clearTimeout(periodicSaveRef.current);
      }
      editorAutoSave.clearPendingSaves();
    };
  }, []);

  // Save immediately when user leaves page
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isEnabled && hasUnsavedChanges && lastContentRef.current) {
        editorAutoSave.saveImmediate(lastContentRef.current, autoSaveOptions);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges, isEnabled, autoSaveOptions]);

  // Manual save function (can be called from parent)
  const saveNow = () => {
    if (lastContentRef.current) {
      setSaveStatus('saving');
      editorAutoSave.saveImmediate(lastContentRef.current, autoSaveOptions);
      setSaveStatus('saved');
    }
  };

  // Restore from auto-save function
  const restoreFromAutoSave = () => {
    const existingSave = editorAutoSave.load(autoSaveOptions);
    if (existingSave) {
      try {
        const editorState = editor.parseEditorState(existingSave.content);
        editor.setEditorState(editorState);
        setHasUnsavedChanges(false);
        setSaveStatus('saved');
      } catch (error) {
        console.error('Failed to restore auto-save:', error);
        setSaveStatus('error');
      }
    }
  };

  // Clear auto-save function
  const clearAutoSave = () => {
    editorAutoSave.remove(autoSaveOptions);
    setLastSaved(null);
    setHasUnsavedChanges(false);
    setSaveStatus('idle');
  };

  if (!showIndicator) {
    return null;
  }

  return (
    <div className="auto-save-indicator">
      {/* Auto-save status indicator */}
      <div className="flex items-center gap-2 text-xs font-mono">
        {saveStatus === 'pending' && (
          <div className="flex items-center gap-1 text-blue-400">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            <span>Pending...</span>
          </div>
        )}
        
        {saveStatus === 'saving' && (
          <div className="flex items-center gap-1 text-yellow-400">
            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
            <span>Saving...</span>
          </div>
        )}
        
        {saveStatus === 'saved' && (
          <div className="flex items-center gap-1 text-green-400">
            <Save className="w-3 h-3" />
            <span>Auto-saved</span>
          </div>
        )}
        
        {saveStatus === 'error' && (
          <div className="flex items-center gap-1 text-red-400">
            <AlertCircle className="w-3 h-3" />
            <span>Save failed</span>
          </div>
        )}
        
        {saveStatus === 'idle' && lastSaved && (
          <div className="flex items-center gap-1 text-slate-500">
            <Clock className="w-3 h-3" />
            <span>Saved {lastSaved}</span>
          </div>
        )}
        
        {saveStatus === 'idle' && hasUnsavedChanges && (
          <div className="flex items-center gap-1 text-slate-400">
            <div className="w-2 h-2 bg-slate-400 rounded-full" />
            <span>Unsaved changes</span>
          </div>
        )}
      </div>

      {/* Auto-save disabled message */}
      {!isEnabled && (
        <div className="flex items-center gap-1 text-slate-500">
          <AlertCircle className="w-3 h-3" />
          <span>Auto-save disabled</span>
        </div>
      )}

    </div>
  );
}
