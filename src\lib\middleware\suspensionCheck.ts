// src/lib/middleware/suspensionCheck.ts
// SUSPENSION ENFORCEMENT MIDDLEWARE
// Middleware para verificar status de suspensão e proteger APIs

import { createClient } from '@/lib/supabase/client';

export interface SuspensionCheckResult {
  isSuspended: boolean;
  suspensionReason?: string;
  suspendedAt?: string;
  suspendedBy?: string;
}

/**
 * Verifica se um usuário está suspenso
 * @param userId - ID do usuário a ser verificado
 * @returns Resultado da verificação de suspensão
 */
export async function checkUserSuspension(userId: string): Promise<SuspensionCheckResult> {
  const supabase = createClient();
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('suspended, suspension_reason, suspended_at, suspended_by')
      .eq('id', userId)
      .maybeSingle();
      
    if (error) {
      console.error('Suspension check error:', error);
      return { isSuspended: false };
    }
    
    if (!data) {
      return { isSuspended: false };
    }
    
    return {
      isSuspended: (data as any)?.suspended || false,
      suspensionReason: (data as any)?.suspension_reason,
      suspendedAt: (data as any)?.suspended_at,
      suspendedBy: (data as any)?.suspended_by
    };
  } catch (error) {
    console.error('Suspension check failed:', error);
    return { isSuspended: false };
  }
}

/**
 * Higher-order function que adiciona verificação de suspensão a qualquer handler
 * @param handler - Função a ser protegida
 * @returns Handler protegido que bloqueia usuários suspensos
 */
export function withSuspensionCheck<T extends any[], R>(
  handler: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T) => {
    const supabase = createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      throw new Error('Authentication required');
    }
    
    const suspensionStatus = await checkUserSuspension(user.id);
    
    if (suspensionStatus.isSuspended) {
      throw new Error(
        `Account suspended: ${suspensionStatus.suspensionReason || 'Terms violation'}. You can view content but cannot create or edit posts.`
      );
    }
    
    return handler(...args);
  };
}

/**
 * Valida que um usuário não está suspenso (para use em server actions)
 * @param userId - ID do usuário a ser validado
 * @throws Error se o usuário estiver suspenso
 */
export async function validateUserNotSuspended(userId: string): Promise<void> {
  const suspensionStatus = await checkUserSuspension(userId);
  
  if (suspensionStatus.isSuspended) {
    throw new Error(
      `Account suspended: ${suspensionStatus.suspensionReason || 'Terms violation'}. Contact support for assistance.`
    );
  }
}

/**
 * Middleware específico para Next.js API routes
 * @param request - Request object
 * @returns Suspension status ou erro
 */
export async function apiSuspensionMiddleware(request: Request): Promise<SuspensionCheckResult> {
  const supabase = createClient();
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      throw new Error('Authentication required');
    }
    
    const suspensionStatus = await checkUserSuspension(user.id);
    
    if (suspensionStatus.isSuspended) {
      throw new Error(
        `Account suspended: ${suspensionStatus.suspensionReason || 'Terms violation'}`
      );
    }
    
    return suspensionStatus;
  } catch (error) {
    console.error('API suspension middleware error:', error);
    throw error;
  }
}

/**
 * Retorna resposta de erro padronizada para usuários suspensos
 * @param suspensionReason - Motivo da suspensão
 * @returns Response object com erro de suspensão
 */
export function createSuspensionErrorResponse(
  suspensionReason?: string,
  status: number = 403
): Response {
  return new Response(
    JSON.stringify({
      error: 'Account suspended',
      reason: suspensionReason || 'Terms violation',
      suspended: true,
      message: 'Your account has been suspended. You can view content but cannot create or edit posts.',
      support_contact: 'Contact support for assistance.'
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}

/**
 * Wrapper para server actions que precisam de proteção contra suspensão
 * @param action - Server action a ser protegida
 * @returns Server action protegida
 */
export function withSuspensionProtection<T extends any[], R>(
  action: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const supabase = createClient();
    
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error || !user) {
        throw new Error('Authentication required');
      }
      
      await validateUserNotSuspended(user.id);
      
      return await action(...args);
    } catch (error: any) {
      // Re-throw suspension errors with specific handling
      if (error.message.includes('suspended')) {
        const suspensionError = new Error(error.message);
        (suspensionError as any).suspensionError = true;
        throw suspensionError;
      }
      throw error;
    }
  };
}

/**
 * Utilitário para logging de tentativas de ação por usuários suspensos
 * @param userId - ID do usuário suspenso
 * @param action - Ação tentada
 * @param metadata - Metadados adicionais
 */
export async function logSuspendedUserAttempt(
  userId: string,
  action: string,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    // Log simples no console por enquanto
    console.warn('SUSPENDED USER ATTEMPT:', {
      userId,
      action,
      metadata,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error logging suspended user attempt:', error);
  }
}

/**
 * Types para resposta de erro de suspensão
 */
export interface SuspensionErrorResponse {
  error: string;
  reason: string;
  suspended: boolean;
  message: string;
  support_contact: string;
}

/**
 * Type guard para verificar se um erro é de suspensão
 */
export function isSuspensionError(error: any): error is Error & { suspensionError: boolean } {
  return error && error.suspensionError === true;
} 