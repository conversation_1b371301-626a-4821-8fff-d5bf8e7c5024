'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import NewsletterForm from '@/components/layout/footer/NewsletterForm';
import FooterAd from '@/components/monetization/footerAd';
import Top20HeatIndex from '@/components/layout/footer/Top20HeatIndex';
import TopRow from '@/components/layout/footer/TopRow';
import CommunityStats from '@/components/layout/footer/CommunityStats';
import '@/components/style/navColors.css';

import { Facebook, Twitter, Instagram, Youtube, Twitch, ChevronUp, Mail } from 'lucide-react';

// TypeScript interfaces
interface SocialLink {
  name: string;
  href: string;
  icon: JSX.Element;
}

const AnimatedFooter = () => {
  // State for interactive elements
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);
  const footerRef = useRef<HTMLDivElement>(null);
  
  // Social media links
  const socialLinks: SocialLink[] = [
    { name: 'Facebook', href: '#', icon: <Facebook size={16} /> },
    { name: 'Twitter', href: '#', icon: <Twitter size={16} /> },
    { name: 'Instagram', href: '#', icon: <Instagram size={16} /> },
    { name: 'Youtube', href: '#', icon: <Youtube size={16} /> },
    { name: 'Twitch', href: '#', icon: <Twitch size={16} /> },
  ];

  // Code title component with glitch effect
  const CodeTitle = ({ children, className = "", onMouseEnter, onMouseLeave }: { 
    children: React.ReactNode; 
    className?: string;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
  }) => (
    <span 
      className={`font-mono relative inline-block ${className}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <span className="text-violet-400/60">&lt;</span>
      <span className="mx-1 relative">
        {children}
        <span className="absolute inset-0 bg-gradient-to-r from-violet-400/0 via-violet-400/30 to-violet-400/0 opacity-0 hover:opacity-100 transition-opacity duration-200 animate-pulse" />
      </span>
      <span className="text-violet-400/60">/&gt;</span>
    </span>
  );

  // Scroll event listener for back-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
      
      // Check if footer is in viewport
      if (footerRef.current) {
        const rect = footerRef.current.getBoundingClientRect();
        const isInViewport = rect.top < window.innerHeight && rect.bottom >= 0;
        setIsVisible(isInViewport);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Mouse move event for lantern effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!footerRef.current) return;
      
      const rect = footerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      setMousePosition({ x, y });
    };
    
    if (isHovering && footerRef.current) {
      window.addEventListener('mousemove', handleMouseMove);
    }
    
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [isHovering]);

  // Handle back to top click
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer 
      ref={footerRef}
      className={`relative overflow-hidden footer-container transition-all duration-700 ${isVisible ? 'opacity-100' : 'opacity-80'}`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <style jsx>{`
        .glitch:hover {
          animation: glitch 0.3s ease-in-out;
        }
        @keyframes glitch {
          0%, 100% { transform: translate(0); }
          10% { transform: translate(-1px, -1px); }
          20% { transform: translate(1px, 1px); }
          30% { transform: translate(-1px, 1px); }
          40% { transform: translate(1px, -1px); }
          50% { transform: translate(-1px, -1px); }
          60% { transform: translate(1px, 1px); }
          70% { transform: translate(-1px, 1px); }
          80% { transform: translate(1px, -1px); }
          90% { transform: translate(-1px, -1px); }
        }
        .brazil-flag {
          filter: drop-shadow(0 0 2px rgba(0, 255, 0, 0.3));
          transition: all 0.3s ease;
        }
        .brazil-flag:hover {
          filter: drop-shadow(0 0 4px rgba(0, 255, 0, 0.5));
          transform: scale(1.1);
        }
      `}</style>

      {/* Radial gradient lantern background */}
      <div 
        className={`absolute inset-0 pointer-events-none ${isHovering ? 'footer-gradient-active' : 'footer-gradient'}`}
        style={isHovering ? { '--x': `${mousePosition.x}px`, '--y': `${mousePosition.y}px` } as React.CSSProperties : {}}
      />
      
      {/* Back to top button */}
      <button
        onClick={scrollToTop}
        className={`fixed right-6 bottom-6 p-3 rounded-full shadow-lg transform transition-all duration-300 z-50 footer-back-to-top ${
          showBackToTop ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'
        }`}
        aria-label="Back to top"
      >
        <ChevronUp size={24} />
      </button>
      
      {/* Main footer content - bp1360:w-4/5 on custom breakpoint, 100% below */}
      <div className="w-full bp1360:w-4/5 mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Footer Ad Component */}
        <FooterAd />

        {/* Top Row Component */}
        <TopRow />

        {/* Top 20 Heat Index Component */}
        <Top20HeatIndex />
        
        {/* About & Newsletter Section - Combined */}
        <div className="footer-section rounded-xl overflow-hidden mb-8">
          <div className="p-6 md:p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
              
              {/* About Section - Moved from TopRow */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2 mb-2">
                  <div>
                    <h1 className="text-md font-bold text-white">
                      <CodeTitle className="glitch">CriticalPixel</CodeTitle>
                    </h1>
                  </div>
                </div>
                <p className="text-slate-300 text-xs leading-relaxed">
                  The definitive destination for honest game reviews, community discussions,
                  and the latest gaming insights. Join thousands of gamers sharing their passion.
                </p>
                <div className="flex flex-wrap gap-2 text-[0.6rem] leading-tight text-slate-400 font-mono">
                  <span className="bg-slate-800/50 px-1.5 py-0.5 rounded">
                    <span className="text-violet-400">//</span> Global Gaming Network
                  </span>
                  <span className="bg-slate-800/50 px-1.5 py-0.5 rounded">
                    <span className="text-violet-400">contact:</span> <EMAIL>
                  </span>
                  <span className="bg-slate-800/50 px-1.5 py-0.5 rounded">
                    <span className="text-violet-400">since</span> 2023
                  </span>
                </div>

                {/* Community Stats */}
                <div className="mt-3">
                  <CommunityStats />
                </div>
              </div>
              
              {/* Newsletter Section */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2 mb-2">
                  <div>
                    <h4 className="text-sm font-semibold text-white">
                      <CodeTitle className="glitch">Newsletter</CodeTitle>
                    </h4>
                  </div>
                </div>
                
                {/* Newsletter Form */}
                <NewsletterForm />
                
                {/* Social Links - Integrated with newsletter */}
                <div className="flex items-center space-x-4 pt-2">
                  <span className="text-xs text-slate-500 uppercase tracking-wider">Follow Us</span>
                  <div className="flex space-x-2">
                    {socialLinks.map((social, index) => (
                      <Link 
                        key={index} 
                        href={social.href}
                        className="group relative"
                        onMouseEnter={() => setHoveredSocial(social.name)}
                        onMouseLeave={() => setHoveredSocial(null)}
                      >
                  <div className="w-5 h-5 rounded-md flex items-center justify-center transition-all duration-300 transform hover:scale-110 footer-social-icon">
                    <div className="text-slate-400 group-hover:text-white transition-colors duration-300">
                      {React.cloneElement(social.icon, {size: 12})}
                    </div>
                  </div>
                        
                        {/* Tooltip */}
                        <div className={`absolute -top-8 left-1/2 transform -translate-x-1/2 transition-all duration-200 pointer-events-none ${
                          hoveredSocial === social.name ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
                        }`}>
                          <div className="bg-slate-900/95 backdrop-blur-sm text-white text-xs px-2 py-1 rounded border border-white/10 whitespace-nowrap">
                            {social.name}
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Compact Bottom Section - Slimmed down with no border/background */}
        <div className="py-2 px-3 md:py-3 md:px-4 rounded-lg">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 md:gap-4">
            
            {/* Copyright with Brazil reference */}
            <div className="text-center md:text-left flex flex-wrap items-center justify-center md:justify-start gap-x-2">
              <p className="text-sm text-gray-400 flex items-center flex-wrap gap-x-2">
                &copy; {new Date().getFullYear()}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-cyan-400 font-bold">
                  CriticalPixel
                </span>
                <span className="text-gray-400 text-xs">•</span>
                <span className="flex items-center gap-x-2">
                  <span className="text-xs text-emerald-400">is we</span>
                  {/* Brazilian flag with pixelated/gaming style */}
                  <svg 
                    className="w-5 h-3.5 brazil-flag inline-block" 
                    viewBox="0 0 640 480" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g fillRule="evenodd" strokeWidth="1pt">
                      <path fill="#229e45" d="M0 0h640v480H0z"/>
                      <path fill="#f8e509" d="M321.4 436l301.5-195.7L319.6 44 17.1 240.7 321.4 436z"/>
                      <circle cx="321.4" cy="240" r="124.2" fill="#2b49a3"/>
                      <path fill="#ffffef" d="M273.4 315.6c-3.2-3-8.7-1.5-17.9-2.2-7.7-.3-11.9-.4-20.5-.8-1.4-7.3-1.6-10.6-3-20.5 3-11.8 4.5-17.7 8.4-29.1 9.7 1.4 14.4 2.3 24.3 3 5.8.9 10.3 2.2 13 4.3 0 0 6 3.2 4.6 10.6-1.3 7.4-9 34.7-9 34.7zm-54.2-52.5c-5.7-9.3-15.9-11.6-19.7-11.5-3.7.1-6.7.8-7 8.3-.3 7.5.1 11.5.6 17.3.5 5.8 1.1 8.9 2.5 15.5 5 1.3 7.6 1.9 13.2 3 8.3 1.7 12.5 2.3 21.7 3 0 0-.7-4.5-1.5-10.2-.8-5.7-1.2-8.5-1.5-14.4-5.8-2.5-8.8-3.6-15.4-5.5-2.8-4.2-3.8-6.3-6.4-12 11.8 3.9 17.8 6.4 29.4 12 .5-4.1.6-6.1.9-10.2.3-4.2.5-6.3.6-10.6-7.6-2.8-11.3-3.9-19.4-5.8-5.5 7.1-7.9 10.8-13.4 18.2 1.7-6.3 2.7-9.4 5.1-15.5 7.8 1.9 11.7 3.1 19.8 5.9.1-4 0-6-.4-10-.4-4.3-.5-6.5-1.5-10.7-9.5-3.4-14.2-4.6-24.2-6.1 0 0-4.8 13.6-7.6 23.2-2.8 9.5-4.3 14.2-6.6 24.3 7 1.5 10.6 2.3 17.8 4.2 4.4-7.5 6.1-11.4 11.2-18.4zm-45.5-8.4c-7.4-9.7-21.9-15.9-33.2-16.8-1.6 7.4-2.2 11.1-3 18.5 11.4 1.4 17.2 2.6 28.5 5.7 0 0-4.9 13.7-9.1 20.1-4.2 6.4-6.3 9.6-12.7 14.6-6.4 5-9.7 7.3-17.4 9.9-7.7 2.6-11.6 3.7-20.4 3.9-2.2 4.3-3.2 6.6-4.6 11.6 13.7.1 20.6-.7 34.1-4.2 7.3-2.2 11-3.5 19-7.8s11.9-6.5 19.6-13.5c7.7-7 11.5-10.7 18.3-19.4 0 0-1.4-7.3-3.1-13.4-1.6-5.9-2.5-8.8-5.3-14.3-4.8 5.3-7 8-12.2 14.1 1.4-3.1 2.2-4.6 3.7-7.8-2.2 0-3.3 0-5.5-.1.1 3.1.3 4.6.7 7.7-2.3-2.9-3.4-4.3-5.4-7.2 0 0-4.6 1-9.7 1.4-4.8.6-7.2.8-12.3.9 2 3.5 2.9 5.3 4.3 9.1 2.9-.5 4.3-.8 7.2-1.4-1.1 2.4-1.6 3.6-2.5 6 5.2-1.3 7.7-2.1 12.7-3.8 6.4-2.1 9.5-3.5 15.3-6.7z"/>
                    </g>
                  </svg>
                </span>
              </p>
            </div>
            
            {/* Legal Links - Horizontal on desktop, wrap on mobile */}
            <div className="flex flex-wrap justify-center md:justify-end gap-x-3 gap-y-1 text-xs">
              {[
                { href: '/privacy', label: 'Privacy Policy' },
                { href: '/terms', label: 'Terms of Service' },
                { href: '/cookies', label: 'Cookie Policy' },
                { href: '/sitemap', label: 'Sitemap' },
                { href: '/advertise', label: 'Advertise' }
              ].map((link, idx) => (
                <React.Fragment key={idx}>
                  {idx > 0 && <span className="text-slate-600 hidden md:inline mx-1">•</span>}
                  <Link 
                    href={link.href} 
                    className="text-slate-500 hover:text-violet-400 transition-colors whitespace-nowrap"
                  >
                    {link.label}
                  </Link>
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default AnimatedFooter;
