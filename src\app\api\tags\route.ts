// src/app/api/tags/route.ts
// Main tags API endpoint for CRUD operations and search

import { NextRequest, NextResponse } from 'next/server';
import { createServerTagService } from '@/lib/services/tagService';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tagService = createServerTagService();

    // Parse query parameters
    const query = searchParams.get('q') || undefined;
    const category = searchParams.get('category') || undefined;
    const status = searchParams.get('status') || undefined;
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') as any || 'usage_count';
    const sortOrder = searchParams.get('sortOrder') as any || 'desc';

    // Handle special endpoints
    const endpoint = searchParams.get('endpoint');

    switch (endpoint) {
      case 'trending':
        const trendingLimit = parseInt(searchParams.get('limit') || '20');
        const trendingResult = await tagService.getTrendingTags(trendingLimit);
        
        if (!trendingResult.success) {
          return NextResponse.json(
            { error: trendingResult.error },
            { status: 400 }
          );
        }

        return NextResponse.json({
          success: true,
          tags: trendingResult.tags,
          total: trendingResult.tags?.length || 0
        });

      case 'featured':
        const featuredLimit = parseInt(searchParams.get('limit') || '10');
        const featuredResult = await tagService.getFeaturedTags(featuredLimit);
        
        if (!featuredResult.success) {
          return NextResponse.json(
            { error: featuredResult.error },
            { status: 400 }
          );
        }

        return NextResponse.json({
          success: true,
          tags: featuredResult.tags,
          total: featuredResult.tags?.length || 0
        });

      case 'popular':
        const popularLimit = parseInt(searchParams.get('limit') || '50');
        const popularResult = await tagService.getPopularTags(popularLimit);
        
        if (!popularResult.success) {
          return NextResponse.json(
            { error: popularResult.error },
            { status: 400 }
          );
        }

        return NextResponse.json({
          success: true,
          tags: popularResult.tags,
          total: popularResult.tags?.length || 0
        });

      case 'categories':
        const categoriesResult = await tagService.getTagsByCategory();
        
        if (!categoriesResult.success) {
          return NextResponse.json(
            { error: categoriesResult.error },
            { status: 400 }
          );
        }

        return NextResponse.json({
          success: true,
          categories: categoriesResult.categories
        });

      default:
        // Regular search
        const searchResult = await tagService.searchTags({
          query,
          category,
          status,
          limit,
          offset,
          sortBy,
          sortOrder
        });

        if (!searchResult.success) {
          return NextResponse.json(
            { error: searchResult.error },
            { status: 400 }
          );
        }

        return NextResponse.json({
          success: true,
          tags: searchResult.tags,
          total: searchResult.total,
          pagination: {
            limit,
            offset,
            hasMore: (searchResult.total || 0) > offset + limit
          }
        });
    }
  } catch (error) {
    console.error('Error in tags GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const tagService = createServerTagService();

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check admin privileges for creating tags
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single();

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Admin privileges required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, category, is_featured } = body;

    // Validate required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Tag name is required' },
        { status: 400 }
      );
    }

    if (name.length > 100) {
      return NextResponse.json(
        { error: 'Tag name must be less than 100 characters' },
        { status: 400 }
      );
    }

    const result = await tagService.createTag(
      {
        name: name.trim(),
        description: description?.trim(),
        category,
        is_featured
      },
      user.id
    );

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      tag: result.tag
    }, { status: 201 });

  } catch (error) {
    console.error('Error in tags POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}