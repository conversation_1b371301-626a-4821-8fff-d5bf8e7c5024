import React from 'react';

const ArrowRightIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M4 12h16m-6-6l6 6-6 6" stroke="currentColor" strokeWidth="2" fill="none"/>
    <rect x="2" y="11" width="1" height="2"/>
    <rect x="6" y="11" width="1" height="2"/>
    <rect x="10" y="11" width="1" height="2"/>
    <rect x="14" y="11" width="1" height="2"/>
    <circle cx="17" cy="9" r="0.5"/>
    <circle cx="17" cy="15" r="0.5"/>
  </svg>
);

export default ArrowRightIcon;