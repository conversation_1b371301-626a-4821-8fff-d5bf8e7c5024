// Dashboard-specific TypeScript interfaces

export interface DashboardStats {
  totalReviews: number;
  totalSurveys: number;
  averageScore: number;
  joinDate: Date | null;
  lastActivity: Date | null;
  publishedReviews: number;
  draftReviews: number;
}

export interface DashboardFilters {
  reviews: {
    platform?: string;
    genre?: string;
    scoreRange?: [number, number];
    dateRange?: [Date, Date];
    status?: 'published' | 'draft' | 'all';
    sortBy?: 'publishDate' | 'createdAt' | 'title' | 'overallScore';
    sortOrder?: 'asc' | 'desc';
  };
  surveys: {
    gameTitle?: string;
    deviceType?: string;
    platform?: string;
    dateRange?: [Date, Date];
    sortBy?: 'created_at' | 'game_title' | 'device_type';
    sortOrder?: 'asc' | 'desc';
  };
}

export interface DashboardState {
  activeTab: string;
  filters: DashboardFilters;
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  href?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

export interface DashboardSection {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}

export interface EmptyStateConfig {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  title: string;
  description: string;
  actionLabel?: string;
  actionHref?: string;
  onAction?: () => void;
}

export interface LoadingStateConfig {
  message?: string;
  showProgress?: boolean;
  progress?: number;
}

export interface ErrorStateConfig {
  title: string;
  description: string;
  actionLabel?: string;
  onRetry?: () => void;
}

// Activity tracking
export interface ActivityItem {
  id: string;
  type: 'review_created' | 'review_published' | 'review_updated' | 'survey_completed';
  title: string;
  description?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Dashboard preferences
export interface DashboardPreferences {
  defaultTab: string;
  itemsPerPage: number;
  showWelcomeMessage: boolean;
  enableRealTimeUpdates: boolean;
  compactView: boolean;
  theme: 'system' | 'light' | 'dark';
}

// Export/Import functionality
export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf';
  includeReviews: boolean;
  includeSurveys: boolean;
  dateRange?: [Date, Date];
  fields?: string[];
}

export interface ImportResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
}
