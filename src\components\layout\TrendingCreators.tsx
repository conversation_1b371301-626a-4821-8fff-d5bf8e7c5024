'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Crown, Star, TrendingUp } from 'lucide-react';
import '../style/trendingSidebar.css';

interface TrendingCreator {
  id: string;
  userName: string;
  photoURL: string;
  reviewCount: number;
  tier: 'legendary' | 'elite' | 'rising';
  percentage: number;
}

const TrendingCreators = () => {
  const [hoveredCreator, setHoveredCreator] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const trendingCreators: TrendingCreator[] = [
    { id: "user1", userName: "PixelMaster", photoURL: "https://randomuser.me/api/portraits/men/32.jpg", reviewCount: 125, tier: 'legendary', percentage: 100 },
    { id: "user2", userName: "GameGuru88", photoURL: "https://randomuser.me/api/portraits/women/45.jpg", reviewCount: 98, tier: 'legendary', percentage: 78 },
    { id: "user3", userName: "CritKnight", photoURL: "https://randomuser.me/api/portraits/lego/1.jpg", reviewCount: 76, tier: 'elite', percentage: 61 },
    { id: "user4", userName: "ReviewQueen", photoURL: "https://randomuser.me/api/portraits/women/60.jpg", reviewCount: 55, tier: 'elite', percentage: 44 },
    { id: "user5", userName: "ByteBard", photoURL: "https://randomuser.me/api/portraits/men/71.jpg", reviewCount: 42, tier: 'rising', percentage: 34 },
  ];

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'legendary': return Crown;
      case 'elite': return Star;
      case 'rising': return TrendingUp;
      default: return Star;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'legendary': return 'text-yellow-400';
      case 'elite': return 'text-purple-400';
      case 'rising': return 'text-cyan-400';
      default: return 'text-slate-400';
    }
  };

  return (
    <div className="trending-sidebar-container">
      <div className="trending-sidebar-header">
        <div className="trending-sidebar-header-flex">
          <div className="trending-sidebar-divider" />
          <span className="trending-sidebar-title trending-sidebar-title-purple">
            <span className="trending-sidebar-title-accent">&gt;</span> Top Creators
          </span>
          <div className="trending-sidebar-divider" />
        </div>
      </div>

      <div className="trending-sidebar-main-container">
        <div className="trending-sidebar-list">
          {trendingCreators.map((creator, idx) => {
            const TierIcon = getTierIcon(creator.tier);
            const tierColor = getTierColor(creator.tier);
            const animDelay = idx * 75;
            
            return (
              <Link
                key={creator.id}
                href={`/u/${creator.userName}`}
                className={`trending-sidebar-item ${mounted ? 'trending-sidebar-animate-fade-slide' : ''}`}
                onMouseEnter={() => setHoveredCreator(creator.id)}
                onMouseLeave={() => setHoveredCreator(null)}
                style={{
                  animationDelay: `${animDelay}ms`
                }}
              >
                <div className="trending-sidebar-item-content">
                  <div className="trending-sidebar-item-left">
                    <div className="trending-sidebar-icon-container">
                      <Avatar 
                        className="trending-sidebar-avatar"
                        style={{
                          width: '28px',
                          height: '28px',
                          border: 'none',
                          borderRadius: '8px'
                        }}
                      >
                        <AvatarImage src={creator.photoURL} alt={`${creator.userName}'s avatar`} />
                        <AvatarFallback>{creator.userName.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div className={`trending-sidebar-badge ${creator.tier === 'legendary' ? 'legendary-glow' : ''}`}>
                        <TierIcon 
                          size={5} 
                          className={tierColor}
                        />
                      </div>
                    </div>
                    <span className="trending-sidebar-name">{creator.userName}</span>
                  </div>
                </div>
                <div className="trending-sidebar-progress-container">
                  <div
                    className="trending-sidebar-progress-bar"
                    style={{ 
                      width: hoveredCreator === creator.id ? `${creator.percentage}%` : '0%'
                    }}
                  />
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TrendingCreators;