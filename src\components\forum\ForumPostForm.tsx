'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Smile } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForumMutations, UserBlockedError } from '@/hooks/useForumMutations';
import { BlockedUserModal } from '@/components/moderation/BlockedUserModal';
import { toast } from 'sonner';

interface ForumPostFormProps {
  reviewId: string;
  onSuccess: (post: any) => void;
  onCancel: () => void;
}

// Gaming-focused emojis for quick access
const commonEmojis = [
  '🎮', '🕹️', '🎯', '🏆', '👑', '💎', '🔥', '💥',
  '⚡', '🚀', '🎲', '🎪', '🌟', '💀', '👾', '🤖',
  '😤', '😎', '🤩', '🥵', '🤯', '😈', '💪', '👊'
];

export function ForumPostForm({ reviewId, onSuccess, onCancel }: ForumPostFormProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showBlockedModal, setShowBlockedModal] = useState(false);
  const [blockedByUserName, setBlockedByUserName] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { createPost } = useForumMutations(reviewId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !content.trim()) {
      toast.error('Please fill in both title and content');
      return;
    }

    try {
      const post = await createPost.mutateAsync({
        title: title.trim(),
        content: content.trim(),
        category: null // Category removed as requested
      });

      toast.success('Post created successfully!');
      onSuccess(post);
    } catch (error) {
      console.error('Error creating post:', error);
      
      if (error instanceof UserBlockedError) {
        setBlockedByUserName(error.contentOwnerName);
        setShowBlockedModal(true);
      } else {
        toast.error('Failed to create post. Please try again.');
      }
    }
  };

  const insertEmoji = (emoji: string) => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + emoji + content.substring(end);
      setContent(newContent);

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
      }, 0);
    }
    setShowEmojiPicker(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  return (
    <div className="w-full bg-slate-800/50 border border-slate-700/50 rounded-xl overflow-hidden shadow-lg">
      {/* Content */}
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title Field */}
          <div className="space-y-2">
            <label className="font-mono text-sm text-slate-300">
              <span className="text-slate-500">//</span> Title
            </label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="What would you like to discuss?"
              className="bg-slate-800/50 border-slate-700/50 text-slate-200 placeholder:text-slate-500 font-medium"
              maxLength={200}
            />
            <div className="text-xs text-slate-500 font-mono">
              {title.length}/200 characters
            </div>
          </div>

          {/* Content Field with Rich Text Features */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="font-mono text-sm text-slate-300">
                <span className="text-slate-500">//</span> Content
              </label>
              <div className="flex items-center gap-2 relative">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className="text-slate-400 hover:text-white h-8 px-2"
                >
                  <Smile className="w-4 h-4" />
                </Button>
                
                {/* Emoji Picker */}
                <AnimatePresence>
                  {showEmojiPicker && (
                    <motion.div
                      initial={{ opacity: 0, x: 10, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, y: 0, scale: 1 }}
                      exit={{ opacity: 0, x: 10, y: 10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className="absolute top-full right-0 mt-2 bg-slate-800 border border-slate-500 rounded-xl p-4 shadow-2xl z-50 w-72"
                    >
                      <div className="mb-2">
                        <h3 className="font-mono text-xs text-slate-400">
                          <span className="text-slate-500">//</span> Quick Emojis
                        </h3>
                      </div>
                      <div className="grid grid-cols-8 gap-1">
                        {commonEmojis.map((emoji) => (
                          <button
                            key={emoji}
                            type="button"
                            onClick={() => insertEmoji(emoji)}
                            className="w-8 h-8 flex items-center justify-center text-lg hover:bg-slate-700/70 rounded-lg transition-all duration-200 hover:scale-110 active:scale-95"
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                      <div className="mt-3 pt-2 border-t border-slate-700/50">
                        <p className="font-mono text-xs text-slate-500 text-center">
                          Click an emoji to insert
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            <div className="relative">
              <textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Share your thoughts, questions, or insights...&#10;&#10;Tips:&#10;• Press Enter twice for new paragraph&#10;• Click 😀 to add emojis"
                className="w-full bg-slate-800/50 border border-slate-700/50 rounded-lg p-4 text-slate-200 placeholder:text-slate-500 resize-none focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-200 leading-relaxed"
                rows={8}
                maxLength={2000}
              />
            </div>

            <div className="flex items-center justify-between text-xs font-mono">
              <span className="text-slate-500">
                {content.length}/2000 characters
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={createPost.isPending || !title.trim() || !content.trim()}
              className="bg-slate-700 hover:bg-slate-600 text-white flex-1 transition-all duration-200"
            >
              {createPost.isPending ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Create Post
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              className="text-slate-400 hover:text-white border border-slate-700/50 hover:border-slate-600"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>

      {/* Blocked User Modal */}
      <BlockedUserModal
        isOpen={showBlockedModal}
        onClose={() => setShowBlockedModal(false)}
        contentOwnerName={blockedByUserName}
      />
    </div>
  );
}
