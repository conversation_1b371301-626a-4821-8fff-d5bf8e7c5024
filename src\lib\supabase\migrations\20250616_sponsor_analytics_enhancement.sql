-- Sponsor Banner Analytics Enhancement Migration
-- Date: 2025-06-16
-- Purpose: Add detailed analytics tracking for sponsor banners

-- Add timestamp fields to existing user_sponsor_banners table
ALTER TABLE user_sponsor_banners 
ADD COLUMN IF NOT EXISTS last_impression_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_click_at TIMESTAMP WITH TIME ZONE;

-- Create detailed analytics tracking table for individual events
CREATE TABLE IF NOT EXISTS sponsor_banner_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  banner_id UUID NOT NULL REFERENCES user_sponsor_banners(id) ON DELETE CASCADE,
  event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('impression', 'click')),
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_sponsor_banner_analytics_banner_id ON sponsor_banner_analytics(banner_id);
CREATE INDEX IF NOT EXISTS idx_sponsor_banner_analytics_event_type ON sponsor_banner_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_sponsor_banner_analytics_created_at ON sponsor_banner_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_sponsor_banner_analytics_banner_event ON sponsor_banner_analytics(banner_id, event_type);

-- Enable RLS on the analytics table
ALTER TABLE sponsor_banner_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for sponsor_banner_analytics
-- Allow users to read analytics for their own banners
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'sponsor_banner_analytics' AND policyname = 'sponsor_analytics_select'
  ) THEN
    CREATE POLICY sponsor_analytics_select ON sponsor_banner_analytics
      FOR SELECT USING (
        banner_id IN (
          SELECT id FROM user_sponsor_banners WHERE user_id = auth.uid()
        )
      );
  END IF;
END
$$;

-- Allow system to insert analytics data (no user restriction for tracking)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_policies WHERE tablename = 'sponsor_banner_analytics' AND policyname = 'sponsor_analytics_insert'
  ) THEN
    CREATE POLICY sponsor_analytics_insert ON sponsor_banner_analytics
      FOR INSERT WITH CHECK (true);
  END IF;
END
$$;

-- Update the tracking functions to include detailed analytics and timestamps
CREATE OR REPLACE FUNCTION increment_sponsor_impression(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Update the main counter and timestamp
  UPDATE user_sponsor_banners
  SET 
    impression_count = impression_count + 1,
    last_impression_at = NOW()
  WHERE id = banner_id;
  
  -- Insert detailed analytics record
  INSERT INTO sponsor_banner_analytics (
    banner_id, 
    event_type, 
    user_agent, 
    ip_address, 
    referrer
  ) VALUES (
    banner_id, 
    'impression', 
    user_agent_param, 
    ip_address_param, 
    referrer_param
  );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_sponsor_click(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Update the main counter and timestamp
  UPDATE user_sponsor_banners
  SET 
    click_count = click_count + 1,
    last_click_at = NOW()
  WHERE id = banner_id;
  
  -- Insert detailed analytics record
  INSERT INTO sponsor_banner_analytics (
    banner_id, 
    event_type, 
    user_agent, 
    ip_address, 
    referrer
  ) VALUES (
    banner_id, 
    'click', 
    user_agent_param, 
    ip_address_param, 
    referrer_param
  );
END;
$$ LANGUAGE plpgsql;

-- Create analytics aggregation functions
CREATE OR REPLACE FUNCTION get_sponsor_banner_daily_stats(
  banner_id UUID,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE(
  date DATE,
  impressions BIGINT,
  clicks BIGINT,
  ctr NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(sba.created_at) as date,
    COUNT(CASE WHEN sba.event_type = 'impression' THEN 1 END) as impressions,
    COUNT(CASE WHEN sba.event_type = 'click' THEN 1 END) as clicks,
    CASE 
      WHEN COUNT(CASE WHEN sba.event_type = 'impression' THEN 1 END) > 0 
      THEN ROUND(
        (COUNT(CASE WHEN sba.event_type = 'click' THEN 1 END)::NUMERIC / 
         COUNT(CASE WHEN sba.event_type = 'impression' THEN 1 END)::NUMERIC) * 100, 
        2
      )
      ELSE 0
    END as ctr
  FROM sponsor_banner_analytics sba
  WHERE sba.banner_id = get_sponsor_banner_daily_stats.banner_id
    AND sba.created_at >= NOW() - INTERVAL '1 day' * days_back
  GROUP BY DATE(sba.created_at)
  ORDER BY date DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to get banner performance summary
CREATE OR REPLACE FUNCTION get_sponsor_banner_summary(banner_id UUID)
RETURNS TABLE(
  total_impressions BIGINT,
  total_clicks BIGINT,
  ctr NUMERIC,
  last_impression TIMESTAMP WITH TIME ZONE,
  last_click TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  days_active INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    usb.impression_count::BIGINT as total_impressions,
    usb.click_count::BIGINT as total_clicks,
    CASE 
      WHEN usb.impression_count > 0 
      THEN ROUND((usb.click_count::NUMERIC / usb.impression_count::NUMERIC) * 100, 2)
      ELSE 0
    END as ctr,
    usb.last_impression_at as last_impression,
    usb.last_click_at as last_click,
    usb.created_at,
    EXTRACT(DAY FROM NOW() - usb.created_at)::INTEGER as days_active
  FROM user_sponsor_banners usb
  WHERE usb.id = get_sponsor_banner_summary.banner_id;
END;
$$ LANGUAGE plpgsql;
