'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useSteamGridDBIcon } from '@/hooks/useSteamGridDBIcon';
import {
  Monitor,
  Gamepad2,
  Laptop,
  Heart,
  Edit,
  EyeOff,
  MoreHorizontal,
  Gauge,
  Shield,
  ShieldOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Image from 'next/image';

interface PerformanceSurvey {
  id?: string;
  game_title?: string;
  device_type: string;
  platform: string;
  fps_average?: number | null;
  created_at?: string;
  is_private?: boolean;
  // Future implementation for likes
  likes_count?: number;
}

interface PerformanceReviewCardProps {
  survey: PerformanceSurvey;
  onHideSurvey?: (surveyId: string) => void;
  onEditSurvey?: (surveyId: string) => void;
  onPrivacyToggle?: (isPrivate: boolean) => void;
}

const deviceIcons = {
  desktop: Monitor,
  pc: Monitor,
  notebook: Laptop,
  laptop: Laptop,
  handheld: Gamepad2
};

const deviceLabels = {
  desktop: 'Desktop',
  pc: 'Desktop',
  notebook: 'Notebook',
  laptop: 'Notebook',
  handheld: 'Handheld'
};

export function PerformanceReviewCard({
  survey,
  onHideSurvey,
  onEditSurvey,
  onPrivacyToggle
}: PerformanceReviewCardProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Fetch SteamGridDB icon for the game
  const { iconUrl: steamGridIcon, isLoading: iconLoading } = useSteamGridDBIcon(survey.game_title || '');

  // Get device info
  const deviceType = survey.device_type?.toLowerCase() as keyof typeof deviceIcons;
  const DeviceIcon = deviceIcons[deviceType] || Monitor;
  const deviceLabel = deviceLabels[deviceType] || 'Desktop';

  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return 'Unknown';
    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      return dateObj.toLocaleDateString();
    } catch {
      return 'Invalid Date';
    }
  };



  const handleEdit = () => {
    if (!survey.id || !onEditSurvey) return;
    onEditSurvey(survey.id);
  };

  const handlePrivacyToggle = async () => {
    if (!onPrivacyToggle) return;

    setIsProcessing(true);
    try {
      await onPrivacyToggle(!survey.is_private);
    } catch (error) {
      console.error('Failed to toggle privacy:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.15 }}
      className="group bg-white/70 dark:bg-slate-800/50 border border-slate-200/80 dark:border-slate-700/40 rounded-lg overflow-hidden hover:border-purple-300/80 dark:hover:border-purple-500/40 hover:bg-white/90 dark:hover:bg-slate-800/70 transition-all duration-200 shadow-sm hover:shadow-xs"
    >
      <div className="p-4">
        <div className="flex items-center gap-4">
          {/* Game Icon - 45x45 - same size as reviews */}
          <div className="flex-shrink-0 w-[45px] h-[45px] bg-slate-800/60 dark:bg-slate-700/60 border border-slate-700/50 dark:border-slate-600/50 rounded-lg overflow-hidden">
            {steamGridIcon ? (
              <Image
                src={steamGridIcon}
                alt={survey.game_title || 'Game'}
                width={45}
                height={45}
                className="w-full h-full object-cover"
                sizes="45px"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 dark:from-slate-700 dark:via-slate-600 dark:to-slate-700 relative">
                {/* Background pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-blue-900/20"></div>
                
                {/* Main performance icon */}
                <div className="relative z-10">
                  <Gauge className="text-slate-400 dark:text-slate-500" size={18} />
                </div>
                
                {/* Decorative elements */}
                <div className="absolute top-1 right-1 w-1 h-1 bg-purple-500/60 rounded-full"></div>
                <div className="absolute bottom-1 left-1 w-1 h-1 bg-blue-500/60 rounded-full"></div>
                <div className="absolute top-1 left-1 w-0.5 h-0.5 bg-slate-500/60 rounded-full"></div>
              </div>
            )}
          </div>

          {/* Content - Vertically Centered */}
          <div className="flex-1 min-w-0 flex items-center">
            <div className="flex-1 min-w-0">
              {/* Title Row */}
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-base font-semibold text-slate-800 dark:text-slate-200 truncate" title={survey.game_title}>
                  {survey.game_title || 'Unknown Game'}
                </h3>
              </div>
              
              {/* Metadata Row */}
              <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400">
                {/* FPS */}
                <div className="flex items-center gap-1">
                  <Gauge size={12} className="text-slate-400 dark:text-slate-500" />
                  <span className="font-medium text-slate-600 dark:text-slate-300">
                    {survey.fps_average || 'N/A'} FPS
                  </span>
                </div>
                
                <span className="text-slate-400 dark:text-slate-500">•</span>
                
                {/* Platform (Device Type) */}
                <div className="flex items-center gap-1">
                  <DeviceIcon size={12} className="text-slate-400 dark:text-slate-500" />
                  <span className="text-slate-600 dark:text-slate-300 font-medium">{deviceLabel}</span>
                </div>
                
                <span className="text-slate-400 dark:text-slate-500">•</span>
                
                {/* Date */}
                <span>{formatDate(survey.created_at)}</span>
                
                <span className="text-slate-400 dark:text-slate-500">•</span>
                
                {/* Likes - placeholder for future implementation */}
                <span className="flex items-center gap-1">
                  <Heart size={12} className="text-slate-400 dark:text-slate-500" />
                  {survey.likes_count || 0}
                </span>
              </div>
            </div>
            
            {/* Right side: Actions Dropdown */}
            <div className="flex-shrink-0 ml-4 flex items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9 p-0 text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 transition-all border border-slate-300/50 dark:border-slate-600/50 rounded-lg shadow-sm"
                  >
                    <MoreHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  align="end" 
                  className="w-56 bg-slate-900/80 dark:bg-slate-900/80 backdrop-blur-md border border-slate-700/50 shadow-xl rounded-xl p-2"
                  sideOffset={5}
                >
                  {/* Primary Actions */}
                  <div className="space-y-1">
                    <DropdownMenuItem 
                      onClick={handleEdit}
                      className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors"
                    >
                      <Edit size={16} className="mr-3 text-slate-400" />
                      Edit Survey
                    </DropdownMenuItem>
                    
                    {onPrivacyToggle && (
                      <DropdownMenuItem 
                        onClick={handlePrivacyToggle}
                        disabled={isProcessing}
                        className="flex items-center cursor-pointer rounded-lg px-3 py-2 text-sm font-mono text-slate-300 hover:bg-slate-800/50 transition-colors disabled:opacity-50"
                      >
                        {survey.is_private ? (
                          <>
                            <ShieldOff size={16} className="mr-3 text-slate-400" />
                            Make Public
                          </>
                        ) : (
                          <>
                            <Shield size={16} className="mr-3 text-slate-400" />
                            Make Private
                          </>
                        )}
                      </DropdownMenuItem>
                    )}
                  </div>

                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}