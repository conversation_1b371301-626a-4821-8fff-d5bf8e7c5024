/**
 * Admin Ad Management Service
 * Provides comprehensive ad management functionality for admin dashboard
 * Created: 20/01/2025 - Admin System Completion
 */

import { createClient } from '@/lib/supabase/client';
import { logAdminAction, AdminAction } from '@/lib/audit/adminActions';

// Ad management interfaces
export interface AdConfiguration {
  id: string;
  name: string;
  type: 'banner' | 'sidebar' | 'inline' | 'popup' | 'affiliate';
  position: 'header' | 'footer' | 'sidebar-left' | 'sidebar-right' | 'content-top' | 'content-bottom' | 'between-reviews';
  content: string; // HTML content or script
  image_url?: string;
  link_url?: string;
  affiliate_code?: string;
  is_active: boolean;
  priority: number; // Higher number = higher priority
  target_audience?: 'all' | 'registered' | 'anonymous';
  device_targeting?: 'all' | 'desktop' | 'mobile' | 'tablet';
  start_date?: string;
  end_date?: string;
  click_count: number;
  impression_count: number;
  revenue: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  last_modified_by?: string;
}

export interface AdPerformanceMetrics {
  totalAds: number;
  activeAds: number;
  totalClicks: number;
  totalImpressions: number;
  totalRevenue: number;
  averageCTR: number; // Click-through rate
  topPerformingAds: {
    id: string;
    name: string;
    clicks: number;
    impressions: number;
    ctr: number;
    revenue: number;
  }[];
  revenueByType: {
    type: string;
    revenue: number;
    percentage: number;
  }[];
  performanceOverTime: {
    date: string;
    clicks: number;
    impressions: number;
    revenue: number;
  }[];
}

export interface AffiliateLink {
  id: string;
  name: string;
  partner: string;
  url: string;
  affiliate_code: string;
  commission_rate: number;
  category: string;
  is_active: boolean;
  click_count: number;
  conversion_count: number;
  revenue: number;
  created_at: string;
  updated_at: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Use the is_admin() RPC function for verification
    const { data, error } = await supabase.rpc('is_admin', { user_id: userId });

    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Get all ad configurations
export async function getAdConfigurations(
  adminUserId: string,
  options: {
    type?: string;
    position?: string;
    isActive?: boolean;
    sortBy?: 'name' | 'priority' | 'created_at' | 'click_count' | 'revenue';
    sortOrder?: 'asc' | 'desc';
  } = {}
): Promise<AdConfiguration[]> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // For now, return mock data since we don't have ads table in database
    // In a real implementation, this would query the ads table
    const mockAds: AdConfiguration[] = [
      {
        id: '1',
        name: 'Header Gaming Banner',
        type: 'banner',
        position: 'header',
        content: '<div class="ad-banner">Gaming Gear Sale - 50% Off!</div>',
        image_url: 'https://example.com/gaming-banner.jpg',
        link_url: 'https://example.com/gaming-gear',
        affiliate_code: 'GAMING50',
        is_active: true,
        priority: 10,
        target_audience: 'all',
        device_targeting: 'all',
        start_date: '2025-01-01',
        end_date: '2025-12-31',
        click_count: 1250,
        impression_count: 45000,
        revenue: 2500.00,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-20T00:00:00Z',
        created_by: adminUserId,
        last_modified_by: adminUserId
      },
      {
        id: '2',
        name: 'Sidebar Game Store',
        type: 'sidebar',
        position: 'sidebar-right',
        content: '<div class="ad-sidebar">Latest Games - Check them out!</div>',
        image_url: 'https://example.com/game-store.jpg',
        link_url: 'https://example.com/game-store',
        affiliate_code: 'GAMES2025',
        is_active: true,
        priority: 8,
        target_audience: 'registered',
        device_targeting: 'desktop',
        click_count: 890,
        impression_count: 32000,
        revenue: 1780.00,
        created_at: '2025-01-05T00:00:00Z',
        updated_at: '2025-01-18T00:00:00Z',
        created_by: adminUserId,
        last_modified_by: adminUserId
      },
      {
        id: '3',
        name: 'Mobile Gaming App',
        type: 'inline',
        position: 'content-bottom',
        content: '<div class="ad-inline">Download our mobile app!</div>',
        image_url: 'https://example.com/mobile-app.jpg',
        link_url: 'https://example.com/mobile-app',
        is_active: false,
        priority: 5,
        target_audience: 'all',
        device_targeting: 'mobile',
        click_count: 450,
        impression_count: 18000,
        revenue: 900.00,
        created_at: '2025-01-10T00:00:00Z',
        updated_at: '2025-01-15T00:00:00Z',
        created_by: adminUserId
      }
    ];

    // Apply filters
    let filteredAds = mockAds;

    if (options.type) {
      filteredAds = filteredAds.filter(ad => ad.type === options.type);
    }

    if (options.position) {
      filteredAds = filteredAds.filter(ad => ad.position === options.position);
    }

    if (options.isActive !== undefined) {
      filteredAds = filteredAds.filter(ad => ad.is_active === options.isActive);
    }

    // Apply sorting
    const { sortBy = 'priority', sortOrder = 'desc' } = options;
    filteredAds.sort((a, b) => {
      let aValue: any = a[sortBy as keyof AdConfiguration];
      let bValue: any = b[sortBy as keyof AdConfiguration];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_QUEUE, {
      resource: 'ads',
      filters: options,
      count: filteredAds.length
    });

    return filteredAds;

  } catch (error) {
    console.error('Error in getAdConfigurations:', error);
    throw error;
  }
}

// Get ad performance metrics
export async function getAdPerformanceMetrics(
  adminUserId: string
): Promise<AdPerformanceMetrics> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Mock performance data
    const metrics: AdPerformanceMetrics = {
      totalAds: 3,
      activeAds: 2,
      totalClicks: 2590,
      totalImpressions: 95000,
      totalRevenue: 5180.00,
      averageCTR: 2.73,
      topPerformingAds: [
        {
          id: '1',
          name: 'Header Gaming Banner',
          clicks: 1250,
          impressions: 45000,
          ctr: 2.78,
          revenue: 2500.00
        },
        {
          id: '2',
          name: 'Sidebar Game Store',
          clicks: 890,
          impressions: 32000,
          ctr: 2.78,
          revenue: 1780.00
        }
      ],
      revenueByType: [
        { type: 'banner', revenue: 2500.00, percentage: 48.3 },
        { type: 'sidebar', revenue: 1780.00, percentage: 34.4 },
        { type: 'inline', revenue: 900.00, percentage: 17.4 }
      ],
      performanceOverTime: [
        { date: '2025-01-15', clicks: 120, impressions: 4500, revenue: 240.00 },
        { date: '2025-01-16', clicks: 135, impressions: 4800, revenue: 270.00 },
        { date: '2025-01-17', clicks: 142, impressions: 5100, revenue: 284.00 },
        { date: '2025-01-18', clicks: 158, impressions: 5400, revenue: 316.00 },
        { date: '2025-01-19', clicks: 165, impressions: 5600, revenue: 330.00 },
        { date: '2025-01-20', clicks: 172, impressions: 5800, revenue: 344.00 }
      ]
    };

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_ANALYTICS, {
      resource: 'ad_performance',
      metrics: {
        totalRevenue: metrics.totalRevenue,
        totalClicks: metrics.totalClicks,
        averageCTR: metrics.averageCTR
      }
    });

    return metrics;

  } catch (error) {
    console.error('Error in getAdPerformanceMetrics:', error);
    throw error;
  }
}

// Create or update ad configuration
export async function saveAdConfiguration(
  adminUserId: string,
  adData: Partial<AdConfiguration>
): Promise<{ success: boolean; error?: string; adId?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    // Validate required fields
    if (!adData.name || !adData.type || !adData.position) {
      return { success: false, error: 'Name, type, and position are required' };
    }

    // In a real implementation, this would save to the database
    // For now, we'll just simulate success
    const adId = adData.id || `ad_${Date.now()}`;

    // Log admin action
    await logAdminAction(adminUserId, adData.id ? AdminAction.UPDATE_SYSTEM_CONFIG : AdminAction.VIEW_SYSTEM_CONFIG, {
      resource: 'ad_configuration',
      resourceId: adId,
      action: adData.id ? 'update' : 'create',
      data: {
        name: adData.name,
        type: adData.type,
        position: adData.position,
        isActive: adData.is_active
      }
    });

    return { success: true, adId };

  } catch (error) {
    console.error('Error in saveAdConfiguration:', error);
    return { success: false, error: 'Failed to save ad configuration' };
  }
}

// Delete ad configuration
export async function deleteAdConfiguration(
  adminUserId: string,
  adId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    // In a real implementation, this would delete from the database
    // For now, we'll just simulate success

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_SYSTEM_CONFIG, {
      resource: 'ad_configuration',
      resourceId: adId,
      action: 'delete'
    });

    return { success: true };

  } catch (error) {
    console.error('Error in deleteAdConfiguration:', error);
    return { success: false, error: 'Failed to delete ad configuration' };
  }
}

// Get affiliate links
export async function getAffiliateLinks(
  adminUserId: string
): Promise<AffiliateLink[]> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // Mock affiliate links data
    const affiliateLinks: AffiliateLink[] = [
      {
        id: '1',
        name: 'Steam Store',
        partner: 'Steam',
        url: 'https://store.steampowered.com',
        affiliate_code: 'STEAM_AFFILIATE_123',
        commission_rate: 5.0,
        category: 'Game Store',
        is_active: true,
        click_count: 2500,
        conversion_count: 125,
        revenue: 1250.00,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-20T00:00:00Z'
      },
      {
        id: '2',
        name: 'Epic Games Store',
        partner: 'Epic Games',
        url: 'https://store.epicgames.com',
        affiliate_code: 'EPIC_AFFILIATE_456',
        commission_rate: 4.5,
        category: 'Game Store',
        is_active: true,
        click_count: 1800,
        conversion_count: 90,
        revenue: 810.00,
        created_at: '2025-01-05T00:00:00Z',
        updated_at: '2025-01-18T00:00:00Z'
      },
      {
        id: '3',
        name: 'Gaming Headset Store',
        partner: 'TechGear',
        url: 'https://techgear.com/gaming',
        affiliate_code: 'TECHGEAR_789',
        commission_rate: 8.0,
        category: 'Hardware',
        is_active: false,
        click_count: 650,
        conversion_count: 32,
        revenue: 512.00,
        created_at: '2025-01-10T00:00:00Z',
        updated_at: '2025-01-15T00:00:00Z'
      }
    ];

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_QUEUE, {
      resource: 'affiliate_links',
      count: affiliateLinks.length
    });

    return affiliateLinks;

  } catch (error) {
    console.error('Error in getAffiliateLinks:', error);
    throw error;
  }
}
