export interface ForumAuthor {
  id: string;
  username: string;
  display_name: string | null;
  avatar_url: string | null;
}

export interface ForumPost {
  id: string;
  review_id: string;
  author_id: string;
  title: string;
  content: string;
  category: string | null;
  upvotes: number;
  downvotes: number;
  reply_count: number;
  is_pinned: boolean;
  is_hot: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  author?: ForumAuthor;
  user_vote?: 'upvote' | 'downvote' | null;
}

export interface ForumReply {
  id: string;
  post_id: string;
  author_id: string;
  content: string;
  upvotes: number;
  downvotes: number;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  author?: ForumAuthor;
  user_vote?: 'upvote' | 'downvote' | null;
}

export interface ForumThread {
  post: ForumPost;
  replies: ForumReply[];
}

export interface ForumStats {
  total_posts: number;
  active_users: number;
  recent_activity: string | null;
}

export interface ForumVote {
  id: string;
  post_id?: string;
  reply_id?: string;
  user_id: string;
  vote_type: 'upvote' | 'downvote';
  created_at: string;
}
