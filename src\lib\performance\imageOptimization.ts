// src/lib/performance/imageOptimization.ts
import sharp from 'sharp';

export interface OptimizationOptions {
  quality: number;
  format: 'webp' | 'jpeg' | 'png' | 'auto';
  width?: number;
  height?: number;
  progressive: boolean;
  stripMetadata: boolean;
}

export interface OptimizedImage {
  buffer: Buffer;
  format: string;
  size: number;
  dimensions: { width: number; height: number };
  originalSize: number;
  compressionRatio: number;
}

/**
 * Advanced image optimization with multiple variants
 */
export class ImageOptimizer {
  private static readonly DEFAULT_OPTIONS: OptimizationOptions = {
    quality: 85,
    format: 'webp',
    progressive: true,
    stripMetadata: true,
  };

  static async createVariants(
    inputBuffer: Buffer,
    filename: string
  ): Promise<{ 
    original: OptimizedImage;
    thumbnail: OptimizedImage;
    medium: OptimizedImage;
   }> {
    const originalSize = inputBuffer.length;
    
    // Original optimized version
    const original = await this.optimizeImage(inputBuffer, {
      ...this.DEFAULT_OPTIONS,
      width: 2048,
      height: 2048,
    });

    // Medium size for cards/previews
    const medium = await this.optimizeImage(inputBuffer, {
      ...this.DEFAULT_OPTIONS,
      width: 800,
      height: 600,
      quality: 80,
    });

    // Thumbnail for lists
    const thumbnail = await this.optimizeImage(inputBuffer, {
      ...this.DEFAULT_OPTIONS,
      width: 200,
      height: 200,
      quality: 75,
    });

    return { original, medium, thumbnail };
  }

  static async optimizeImage(
    inputBuffer: Buffer,
    options: Partial<OptimizationOptions> = {}
  ): Promise<OptimizedImage> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      let image = sharp(inputBuffer);
      const metadata = await image.metadata();
      const originalSize = inputBuffer.length;

      // Strip metadata if requested
      if (opts.stripMetadata) {
        image = image.rotate(); // rotate() strips EXIF data
      }

      // Resize if dimensions specified
      if (opts.width || opts.height) {
        image = image.resize(opts.width, opts.height, {
          fit: 'inside',
          withoutEnlargement: true,
        });
      }

      // Apply format and quality settings
      let outputBuffer: Buffer;
      let outputFormat: string;

      switch (opts.format) {
        case 'webp':
          outputBuffer = await image.webp({ 
            quality: opts.quality,
            effort: 4,
            progressive: opts.progressive,
          }).toBuffer();
          outputFormat = 'webp';
          break;
        
        case 'jpeg':
          outputBuffer = await image.jpeg({ 
            quality: opts.quality,
            progressive: opts.progressive,
            mozjpeg: true,
          }).toBuffer();
          outputFormat = 'jpeg';
          break;
        
        case 'png':
          outputBuffer = await image.png({ 
            quality: opts.quality,
            progressive: opts.progressive,
            compressionLevel: 9,
          }).toBuffer();
          outputFormat = 'png';
          break;
        
        default: // 'auto'
          // Choose best format based on input
          if (metadata.hasAlpha) {
            outputBuffer = await image.png({ quality: opts.quality }).toBuffer();
            outputFormat = 'png';
          } else {
            outputBuffer = await image.webp({ quality: opts.quality }).toBuffer();
            outputFormat = 'webp';
          }
      }

      const finalMetadata = await sharp(outputBuffer).metadata();

      return {
        buffer: outputBuffer,
        format: outputFormat,
        size: outputBuffer.length,
        dimensions: {
          width: finalMetadata.width || 0,
          height: finalMetadata.height || 0,
        },
        originalSize,
        compressionRatio: originalSize / outputBuffer.length,
      };
    } catch (error) {
      throw new Error(`Image optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * CDN URL generation with optimization parameters
 */
export class CDNUrlGenerator {
  private static readonly B2_ENDPOINT = process.env.B2_ENDPOINT;
  private static readonly BUCKET_NAME = process.env.B2_BUCKET_NAME;

  static generateOptimizedUrl(
    key: string,
    variant: 'original' | 'medium' | 'thumbnail' = 'original',
    options: {
      quality?: number;
      format?: string;
      width?: number;
      height?: number;
    } = {}
  ): string {
    const baseUrl = `https://${this.B2_ENDPOINT}/${this.BUCKET_NAME}/${key}`;
    
    // For now, return base URL. In production, you might use:
    // - Cloudflare Image Optimization
    // - AWS CloudFront with Lambda@Edge
    // - Custom image optimization service
    
    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);
    
    const queryString = params.toString();
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }

  static getVariantKey(originalKey: string, variant: string): string {
    const parts = originalKey.split('.');
    const extension = parts.pop();
    const base = parts.join('.');
    
    return `${base}_${variant}.${extension}`;
  }
}
