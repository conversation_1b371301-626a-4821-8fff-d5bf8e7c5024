import { MoreH<PERSON>zontal, CheckCircle, X, Eye, AlertTriangle, Ban, Unlock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import Link from 'next/link';
import { useState } from 'react';
import { type ContentFlag, type ReportModerationAction } from '@/app/admin/reviews/actions';

interface ReportsActionsDropdownProps {
  report: ContentFlag;
  onResolveReport: (reportId: string, action: ReportModerationAction) => Promise<void>;
  onBlockReview?: (reviewId: string, action: { action: 'block' | 'unblock' }) => Promise<void>;
  disabled?: boolean;
}

export function ReportsActionsDropdown({ 
  report, 
  onResolveReport, 
  onBlockReview,
  disabled = false 
}: ReportsActionsDropdownProps) {
  const [open, setOpen] = useState(false);
  const [showResolveDialog, setShowResolveDialog] = useState(false);
  const [showDismissDialog, setShowDismissDialog] = useState(false);
  const [showBlockDialog, setShowBlockDialog] = useState(false);
  const [showUnblockDialog, setShowUnblockDialog] = useState(false);

  const isPending = report.status === 'pending';

  const handleResolve = async () => {
    await onResolveReport(report.id, { action: 'resolve' });
    setShowResolveDialog(false);
    setOpen(false);
  };

  const handleDismiss = async () => {
    await onResolveReport(report.id, { action: 'dismiss' });
    setShowDismissDialog(false);
    setOpen(false);
  };

  return (
    <TooltipProvider>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-muted focus-visible:ring-1"
                disabled={disabled}
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Abrir menu de ações do report</span>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Ações de Moderação</p>
          </TooltipContent>
        </Tooltip>

        <DropdownMenuContent align="end" className="w-56">
          {/* Visualizar Review */}
          <DropdownMenuItem asChild>
            <Link 
              href={`/admin/reviews/edit/${report.content_id}`}
              className="flex items-center gap-2 w-full"
            >
              <Eye className="h-4 w-4" />
              <span>Visualizar Review</span>
            </Link>
          </DropdownMenuItem>

          {/* Link para Review Público (se disponível) */}
          {report.review_slug && (
            <DropdownMenuItem asChild>
              <Link 
                href={`/reviews/view/${report.review_slug}`}
                target="_blank"
                className="flex items-center gap-2 w-full"
              >
                <Eye className="h-4 w-4" />
                <span>Ver Review Público</span>
              </Link>
            </DropdownMenuItem>
          )}

          {/* Separador se há ações pendentes */}
          {isPending && <DropdownMenuSeparator />}

          {/* Ações de Moderação (apenas para reports pendentes) */}
          {isPending && (
            <>
              {/* Resolver Report */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuItem
                    onClick={() => setShowResolveDialog(true)}
                    className="flex items-center gap-2 text-green-600 hover:text-green-700 hover:bg-green-50"
                  >
                    <CheckCircle className="h-4 w-4" />
                    <span>Resolver Report</span>
                  </DropdownMenuItem>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>Marca o report como resolvido - problema foi tratado adequadamente</p>
                </TooltipContent>
              </Tooltip>

              {/* Descartar Report */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuItem
                    onClick={() => setShowDismissDialog(true)}
                    className="flex items-center gap-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                  >
                    <X className="h-4 w-4" />
                    <span>Descartar Report</span>
                  </DropdownMenuItem>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <p>Marca o report como inválido ou que não requer ação</p>
                </TooltipContent>
              </Tooltip>

              {/* Bloquear/Desbloquear Review */}
              {report.content_id && (
                <>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuItem
                        onClick={() => setShowBlockDialog(true)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                      >
                        <Ban className="h-4 w-4 mr-2" />
                        Bloquear Review
                      </DropdownMenuItem>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Bloqueia o review tornando-o inacessível para todos</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuItem
                        onClick={() => setShowUnblockDialog(true)}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer"
                      >
                        <Unlock className="h-4 w-4 mr-2" />
                        Desbloquear Review
                      </DropdownMenuItem>
                    </TooltipTrigger>
                    <TooltipContent side="left">
                      <p>Desbloqueia o review para voltar a ser acessível</p>
                    </TooltipContent>
                  </Tooltip>
                </>
              )}
            </>
          )}

          {/* Status para reports já processados */}
          {!isPending && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className="flex items-center gap-2 text-muted-foreground">
                <AlertTriangle className="h-4 w-4" />
                <span>
                  Report {report.status === 'resolved' ? 'Resolvido' : 'Descartado'}
                </span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Dialog de Confirmação - Resolver */}
      <AlertDialog open={showResolveDialog} onOpenChange={setShowResolveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Resolver Report
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja marcar este report como <strong>resolvido</strong>?
              <br /><br />
              Esta ação indicará que o problema reportado foi tratado adequadamente e o conteúdo foi moderado conforme necessário.
              <br /><br />
              <strong>Report:</strong> {report.reason}
              <br />
              <strong>Review:</strong> {report.review_title}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleResolve}
              className="bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Resolver Report
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Confirmação - Descartar */}
      <AlertDialog open={showDismissDialog} onOpenChange={setShowDismissDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <X className="h-5 w-5 text-orange-600" />
              Descartar Report
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja <strong>descartar</strong> este report?
              <br /><br />
              Esta ação indicará que o report não é válido, é um falso positivo, ou não requer ação de moderação.
              <br /><br />
              <strong>Report:</strong> {report.reason}
              <br />
              <strong>Review:</strong> {report.review_title}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDismiss}
              className="bg-orange-600 hover:bg-orange-700"
            >
              <X className="mr-2 h-4 w-4" />
              Descartar Report
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Confirmação - Bloquear Review */}
      <AlertDialog open={showBlockDialog} onOpenChange={setShowBlockDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5 text-red-600" />
              Bloquear Review
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja <strong>bloquear</strong> este review? Ele ficará inacessível para todos os usuários, mas não será deletado.<br /><br />
              <strong>Review:</strong> {report.review_title}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                setShowBlockDialog(false);
                if (report.content_id && typeof onBlockReview === 'function') {
                  await onBlockReview(report.content_id, { action: 'block' });
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              <Ban className="mr-2 h-4 w-4" />
              Bloquear Review
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de Confirmação - Desbloquear Review */}
      <AlertDialog open={showUnblockDialog} onOpenChange={setShowUnblockDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Unlock className="h-5 w-5 text-blue-600" />
              Desbloquear Review
            </AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja <strong>desbloquear</strong> este review? Ele voltará a ficar acessível conforme o status.<br /><br />
              <strong>Review:</strong> {report.review_title}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                setShowUnblockDialog(false);
                if (report.content_id && typeof onBlockReview === 'function') {
                  await onBlockReview(report.content_id, { action: 'unblock' });
                }
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Unlock className="mr-2 h-4 w-4" />
              Desbloquear Review
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </TooltipProvider>
  );
} 