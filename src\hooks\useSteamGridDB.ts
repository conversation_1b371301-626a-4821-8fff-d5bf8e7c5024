import { useState, useCallback } from 'react';

// Types for SteamGridDB data
export interface SteamGridDBGame {
  id: number;
  name: string;
  types: string[];
  verified: boolean;
}

export interface SteamGridDBArtwork {
  id: number;
  url: string;
  thumb: string;
  tags: string[];
  style: string;
  width: number;
  height: number;
  score: number;
  author: {
    name: string;
    steam64?: string;
    avatar?: string;
  };
  notes?: string;
}

export interface SteamGridDBHook {
  // State
  loading: boolean;
  error: string | null;
  games: SteamGridDBGame[];
  grids: SteamGridDBArtwork[];
  heroes: SteamGridDBArtwork[];
  logos: SteamGridDBArtwork[];
  icons: SteamGridDBArtwork[];

  // Actions
  searchGames: (query: string) => Promise<void>;
  getGrids: (gameId: number, options?: GridOptions) => Promise<void>;
  getGridsBySteamId: (steamId: number, options?: GridOptions) => Promise<void>;
  getHeroes: (gameId: number, options?: ArtworkOptions) => Promise<void>;
  getLogos: (gameId: number, options?: ArtworkOptions) => Promise<void>;
  getIcons: (gameId: number, options?: IconOptions) => Promise<void>;
  clearData: () => void;
}

export interface GridOptions {
  styles?: string[];
  dimensions?: string[];
  limit?: number;
}

export interface ArtworkOptions {
  styles?: string[];
  limit?: number;
}

export interface IconOptions extends ArtworkOptions {
  dimensions?: string[];
}

export const useSteamGridDB = (): SteamGridDBHook => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [games, setGames] = useState<SteamGridDBGame[]>([]);
  const [grids, setGrids] = useState<SteamGridDBArtwork[]>([]);
  const [heroes, setHeroes] = useState<SteamGridDBArtwork[]>([]);
  const [logos, setLogos] = useState<SteamGridDBArtwork[]>([]);
  const [icons, setIcons] = useState<SteamGridDBArtwork[]>([]);

  const handleError = useCallback((error: any, defaultMessage: string) => {
    console.error('SteamGridDB error:', error);
    const message = error instanceof Error ? error.message : defaultMessage;
    setError(message);
  }, []);

  const searchGames = useCallback(async (query: string) => {
    if (!query.trim()) {
      setError('Search query cannot be empty');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/steamgriddb/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setGames(data.data);
      } else {
        throw new Error(data.error || 'Failed to search games');
      }
    } catch (error) {
      handleError(error, 'Failed to search games');
      setGames([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const getGrids = useCallback(async (gameId: number, options: GridOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        gameId: gameId.toString(),
        ...(options.styles && { styles: options.styles.join(',') }),
        ...(options.dimensions && { dimensions: options.dimensions.join(',') }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/steamgriddb/grids?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setGrids(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch grids');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch grids');
      setGrids([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const getGridsBySteamId = useCallback(async (steamId: number, options: GridOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        steamId: steamId.toString(),
        ...(options.styles && { styles: options.styles.join(',') }),
        ...(options.dimensions && { dimensions: options.dimensions.join(',') }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/steamgriddb/grids?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setGrids(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch grids');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch grids');
      setGrids([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const getHeroes = useCallback(async (gameId: number, options: ArtworkOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        gameId: gameId.toString(),
        ...(options.styles && { styles: options.styles.join(',') }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/steamgriddb/heroes?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setHeroes(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch heroes');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch heroes');
      setHeroes([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const getLogos = useCallback(async (gameId: number, options: ArtworkOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        gameId: gameId.toString(),
        ...(options.styles && { styles: options.styles.join(',') }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/steamgriddb/logos?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setLogos(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch logos');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch logos');
      setLogos([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const getIcons = useCallback(async (gameId: number, options: IconOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        gameId: gameId.toString(),
        ...(options.styles && { styles: options.styles.join(',') }),
        ...(options.dimensions && { dimensions: options.dimensions.join(',') }),
        ...(options.limit && { limit: options.limit.toString() }),
      });

      const response = await fetch(`/api/steamgriddb/icons?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setIcons(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch icons');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch icons');
      setIcons([]);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const clearData = useCallback(() => {
    setGames([]);
    setGrids([]);
    setHeroes([]);
    setLogos([]);
    setIcons([]);
    setError(null);
  }, []);

  return {
    loading,
    error,
    games,
    grids,
    heroes,
    logos,
    icons,
    searchGames,
    getGrids,
    getGridsBySteamId,
    getHeroes,
    getLogos,
    getIcons,
    clearData,
  };
};