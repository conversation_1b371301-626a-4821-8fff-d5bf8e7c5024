/* src/styles/lexical.css - Code/Gamer Theme Editor Styles */

/* Editor Container */
.lexical-editor-container {
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  background: linear-gradient(to bottom right, rgb(15, 23, 42), rgb(30, 41, 59));
  overflow: hidden;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure container has defined height */
  max-height: 100%; /* Prevent container from growing beyond parent */
}

.lexical-editor-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1; /* Allow wrapper to fill container */
  min-height: 0; /* Allow flex child to shrink */
}

.lexical-editor-content {
  background: linear-gradient(to bottom right, rgb(0, 0, 0), rgb(15, 23, 42));
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  flex: 1;
  min-height: 0; /* Critical: Allow content to shrink and enable scrolling */
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Focus Mode Specific Styles */
.focus-mode-editor-wrapper .lexical-editor-content {
  min-height: 100%;
  max-height: 100%;
  height: 100%;
}

.lexical-content-editable {
  padding: 32px;
  color: #ffffff;
  font-size: 16px;
  line-height: 1.6;
  font-family: system-ui, -apple-system, sans-serif;
  outline: none;
  flex: 1;
  overflow-y: auto;
  position: relative;
  z-index: 2;
  height: 100%;
  min-height: 0; /* Critical: Allow flex child to shrink below content size */
  max-height: 100%; /* Prevent content from growing beyond container */
}

/* Focus Mode: Ensure editor fills entire available height */
.focus-mode-editor-wrapper .lexical-content-editable {
  height: 100%;
  max-height: none;
}

.lexical-content-editable:focus {
  outline: none;
}

/* Custom Scrollbar Styling */
.lexical-content-editable::-webkit-scrollbar {
  width: 8px;
}

.lexical-content-editable::-webkit-scrollbar-track {
  background: transparent;
}

.lexical-content-editable::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(139, 92, 246), rgb(6, 182, 212));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.lexical-content-editable::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(139, 92, 246), rgb(6, 182, 212));
}

.lexical-content-editable::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar */
.lexical-content-editable {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.6) transparent;
}

.lexical-placeholder {
  position: absolute;
  top: 32px;
  left: 32px;
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
  pointer-events: none;
  user-select: none;
  z-index: 1;
  display: block;
}

.lexical-editor-content {
  position: relative;
}

.lexical-editor-content .lexical-placeholder {
  position: absolute;
  top: 32px;
  left: 32px;
  right: 24px;
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
  pointer-events: none;
  user-select: none;
  z-index: 1;
}

/* Hide placeholder when content exists */
.lexical-content-editable:not(:empty) + .lexical-placeholder {
  display: none;
}

/* Paragraph */
.editor-paragraph {
  margin: 0 0 24px 0;
  padding: 0;
  position: relative;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.7;
  font-size: 16px;
}

/* Element alignment styles - Using Lexical's format attribute */
.editor-paragraph[data-lexical-align="left"],
.editor-heading-h1[data-lexical-align="left"],
.editor-heading-h2[data-lexical-align="left"],
.editor-heading-h3[data-lexical-align="left"],
.editor-heading-h4[data-lexical-align="left"],
.editor-heading-h5[data-lexical-align="left"],
.editor-heading-h6[data-lexical-align="left"],
.editor-quote[data-lexical-align="left"],
.editor-list-ol[data-lexical-align="left"],
.editor-list-ul[data-lexical-align="left"] {
  text-align: left !important;
}

.editor-paragraph[data-lexical-align="center"],
.editor-heading-h1[data-lexical-align="center"],
.editor-heading-h2[data-lexical-align="center"],
.editor-heading-h3[data-lexical-align="center"],
.editor-heading-h4[data-lexical-align="center"],
.editor-heading-h5[data-lexical-align="center"],
.editor-heading-h6[data-lexical-align="center"],
.editor-quote[data-lexical-align="center"],
.editor-list-ol[data-lexical-align="center"],
.editor-list-ul[data-lexical-align="center"] {
  text-align: center !important;
}

.editor-paragraph[data-lexical-align="right"],
.editor-heading-h1[data-lexical-align="right"],
.editor-heading-h2[data-lexical-align="right"],
.editor-heading-h3[data-lexical-align="right"],
.editor-heading-h4[data-lexical-align="right"],
.editor-heading-h5[data-lexical-align="right"],
.editor-heading-h6[data-lexical-align="right"],
.editor-quote[data-lexical-align="right"],
.editor-list-ol[data-lexical-align="right"],
.editor-list-ul[data-lexical-align="right"] {
  text-align: right !important;
}

/* Fallback styles using style attribute */
.editor-paragraph[style*="text-align: left"],
.editor-heading-h1[style*="text-align: left"],
.editor-heading-h2[style*="text-align: left"],
.editor-heading-h3[style*="text-align: left"],
.editor-heading-h4[style*="text-align: left"],
.editor-heading-h5[style*="text-align: left"],
.editor-heading-h6[style*="text-align: left"],
.editor-quote[style*="text-align: left"],
.editor-list-ol[style*="text-align: left"],
.editor-list-ul[style*="text-align: left"] {
  text-align: left !important;
}

.editor-paragraph[style*="text-align: center"],
.editor-heading-h1[style*="text-align: center"],
.editor-heading-h2[style*="text-align: center"],
.editor-heading-h3[style*="text-align: center"],
.editor-heading-h4[style*="text-align: center"],
.editor-heading-h5[style*="text-align: center"],
.editor-heading-h6[style*="text-align: center"],
.editor-quote[style*="text-align: center"],
.editor-list-ol[style*="text-align: center"],
.editor-list-ul[style*="text-align: center"] {
  text-align: center !important;
}

.editor-paragraph[style*="text-align: right"],
.editor-heading-h1[style*="text-align: right"],
.editor-heading-h2[style*="text-align: right"],
.editor-heading-h3[style*="text-align: right"],
.editor-heading-h4[style*="text-align: right"],
.editor-heading-h5[style*="text-align: right"],
.editor-heading-h6[style*="text-align: right"],
.editor-quote[style*="text-align: right"],
.editor-list-ol[style*="text-align: right"],
.editor-list-ul[style*="text-align: right"] {
  text-align: right !important;
}

/* Responsive text scaling for high resolution displays */
@media (min-height: 1440px) {
  .lexical-content-editable {
    font-size: 18px;
    padding: 32px;
  }
  
  .editor-paragraph {
    font-size: 18px;
    margin: 0 0 28px 0;
    line-height: 1.8;
  }
}

.editor-paragraph:last-child {
  margin-bottom: 0;
}

/* Line breaks within paragraphs maintain normal line-height */
.editor-paragraph br {
  line-height: 1.7;
}

/* Headings */
.editor-heading-h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 36px 0 28px 0;
  color: #ffffff;
  position: relative;
}

.editor-heading-h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #8b5cf6, #06b6d4);
  border-radius: 2px;
}

.editor-heading-h2 {
  font-size: 2rem;
  font-weight: 600;
  margin: 32px 0 22px 0;
  color: #ffffff;
  position: relative;
}

.editor-heading-h2::before {
  content: '##';
  position: absolute;
  left: -36px;
  color: rgba(139, 92, 246, 0.4);
  font-family: ui-monospace, SFMono-Regular, monospace;
  font-size: 1.2rem;
}

.editor-heading-h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 28px 0 18px 0;
  color: rgba(255, 255, 255, 0.95);
  position: relative;
}

.editor-heading-h3::before {
  content: '###';
  position: absolute;
  left: -48px;
  color: rgba(139, 92, 246, 0.3);
  font-family: ui-monospace, SFMono-Regular, monospace;
  font-size: 1rem;
}

.editor-heading-h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 24px 0 16px 0;
  color: rgba(255, 255, 255, 0.9);
}

.editor-heading-h5 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 20px 0 14px 0;
  color: rgba(255, 255, 255, 0.85);
}

.editor-heading-h6 {
  font-size: 1rem;
  font-weight: 600;
  margin: 18px 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
}

/* Responsive heading scaling for high resolution displays */
@media (min-height: 1440px) {
  .editor-heading-h1 {
    font-size: 3rem;
    margin: 42px 0 32px 0;
  }
  
  .editor-heading-h2 {
    font-size: 2.25rem;
    margin: 36px 0 26px 0;
  }
  
  .editor-heading-h2::before {
    font-size: 1.4rem;
  }
  
  .editor-heading-h3 {
    font-size: 1.75rem;
    margin: 32px 0 22px 0;
  }
  
  .editor-heading-h3::before {
    font-size: 1.2rem;
  }
  
  .editor-heading-h4 {
    font-size: 1.5rem;
    margin: 28px 0 20px 0;
  }
  
  .editor-heading-h5 {
    font-size: 1.375rem;
    margin: 24px 0 18px 0;
  }
  
  .editor-heading-h6 {
    font-size: 1.25rem;
    margin: 22px 0 16px 0;
  }
  
  /* Scale other text elements */
  .editor-quote {
    font-size: 18px;
    margin: 32px 0;
    padding: 24px 28px;
  }
  
  .editor-quote::before {
    font-size: 3.5rem;
  }
  
  .editor-listitem {
    font-size: 18px;
    margin: 12px 0;
    line-height: 1.7;
  }
  
  .editor-list-ol, .editor-list-ul {
    margin: 24px 0;
    padding-left: 28px;
  }
}

/* Quote */
.editor-quote {
  margin: 28px 0;
  padding: 20px 24px;
  border-left: 4px solid rgba(139, 92, 246, 0.6);
  background: linear-gradient(to right, rgba(139, 92, 246, 0.05), transparent);
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 0 8px 8px 0;
  position: relative;
}

.editor-quote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 16px;
  font-size: 3rem;
  color: rgba(139, 92, 246, 0.3);
  font-family: Georgia, serif;
}

/* Lists */
.editor-list-ol {
  padding-left: 24px;
  margin: 20px 0;
  color: rgba(255, 255, 255, 0.9);
}

.editor-list-ul {
  padding-left: 24px;
  margin: 20px 0;
  color: rgba(255, 255, 255, 0.9);
}

.editor-listitem {
  margin: 10px 0;
  line-height: 1.6;
}

.editor-list-ul .editor-listitem::marker {
  color: rgba(139, 92, 246, 0.7);
  font-size: 1.2em;
}

.editor-list-ol .editor-listitem::marker {
  color: rgba(6, 182, 212, 0.7);
  font-weight: 600;
}

.editor-nested-listitem {
  list-style-type: none;
}

/* Links */
.editor-link {
  color: #06b6d4;
  text-decoration: none;
  cursor: pointer;
  position: relative;
  border-bottom: 1px solid rgba(6, 182, 212, 0.3);
  transition: all 0.3s ease;
}

.editor-link:hover {
  color: #22d3ee;
  border-bottom-color: rgba(6, 182, 212, 0.6);
  text-shadow: 0 0 8px rgba(6, 182, 212, 0.4);
}

.editor-link::after {
  content: '↗';
  font-size: 0.8em;
  margin-left: 4px;
  opacity: 0.6;
}

/* Text formatting */
.editor-text-bold {
  font-weight: 700;
  color: #ffffff;
}

.editor-text-italic {
  font-style: italic;
  color: rgba(255, 255, 255, 0.95);
}

.editor-text-underline {
  text-decoration: underline;
  text-decoration-color: rgba(139, 92, 246, 0.5);
  text-underline-offset: 3px;
}

.editor-text-strikethrough {
  text-decoration: line-through;
  text-decoration-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 0.6);
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
  text-decoration-color: rgba(139, 92, 246, 0.5);
  text-underline-offset: 3px;
}

.editor-text-code {
  background: linear-gradient(135deg, rgb(139, 92, 246), rgb(6, 182, 212));
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 6px;
  padding: 4px 8px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
  color: #22d3ee;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Code blocks */
.editor-code {
  background: linear-gradient(135deg, rgb(0, 0, 0), rgb(15, 23, 42));
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 12px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  margin: 28px 0;
  padding: 24px;
  tab-size: 4;
  white-space: pre-wrap;
  overflow-x: auto;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
}

.editor-code::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
}

/* Code syntax highlighting */
.editor-tokenComment {
  color: rgba(156, 163, 175, 0.8);
  font-style: italic;
}

.editor-tokenPunctuation {
  color: rgba(255, 255, 255, 0.7);
}

.editor-tokenProperty {
  color: #22d3ee;
}

.editor-tokenSelector {
  color: #a78bfa;
}

.editor-tokenOperator {
  color: #f472b6;
}

.editor-tokenAttr {
  color: #34d399;
}

.editor-tokenVariable {
  color: #fbbf24;
}

.editor-tokenFunction {
  color: #a78bfa;
}

/* Selection styling */
.lexical-content-editable ::selection {
  background: rgba(139, 92, 246, 0.3);
}

.lexical-content-editable ::-moz-selection {
  background: rgba(139, 92, 246, 0.3);
}

/* Focus states */
.lexical-content-editable:focus .editor-paragraph:empty::before {
  content: '';
}

/* Regular Mode Height Constraints */
.lexical-editor-container:not(.focus-mode-editor-wrapper .lexical-editor-container) {
  height: 100%;
  max-height: 100%; /* Prevent container from growing beyond parent */
}

.lexical-editor-container:not(.focus-mode-editor-wrapper .lexical-editor-container) .lexical-editor-content {
  flex: 1;
  min-height: 0; /* Allow content to shrink and enable scrolling */
  max-height: 100%; /* Prevent content from growing beyond container */
}

/* Ensure editor content is scrollable in regular mode */
.lexical-editor-container:not(.focus-mode-editor-wrapper .lexical-editor-container) .lexical-content-editable {
  flex: 1;
  min-height: 0; /* Allow content to shrink */
  max-height: 100%; /* Enable scrolling when content exceeds container */
  overflow-y: auto; /* Ensure scrollbar appears when needed */
}

/* Focus Mode: Remove height constraints and fill container */
.focus-mode-editor-wrapper .lexical-editor-container {
  height: 100%;
  flex: 1;
}

.focus-mode-editor-wrapper .lexical-editor-wrapper {
  height: 100%;
  flex: 1;
}

/* Ensure focus mode editor content fills properly */
.focus-mode-editor-wrapper .lexical-editor-content {
  height: 100%;
  flex: 1;
  min-height: 100%;
  max-height: none;
}

/* Focus mode content editable styling */
.focus-mode-editor-wrapper .lexical-content-editable {
  height: 100%;
  min-height: 100%;
  max-height: none;
  flex: 1;
}

/* Focus mode toolbar styling */
.focus-mode-editor-wrapper .lexical-toolbar {
  flex-shrink: 0;
}

/* Ensure editor respects parent container height constraints */
.lexical-editor-container {
  max-height: inherit; /* Inherit height constraints from parent */
  overflow: hidden; /* Prevent container from expanding beyond bounds */
}

/* Ensure toolbar always stays visible */
.lexical-toolbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgb(30, 41, 59);
}

/* Hashtag styling */
.editor-hashtag {
  background: linear-gradient(135deg, rgb(139, 92, 246), rgb(6, 182, 212));
  color: #a78bfa;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid rgba(139, 92, 246, 0.3);
  transition: all 0.2s ease;
}

.editor-hashtag:hover {
  background: linear-gradient(135deg, rgb(139, 92, 246), rgb(6, 182, 212));
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
}

/* Table styling */
.editor-table {
  border-collapse: collapse;
  border-spacing: 0;
  max-width: 100%;
  overflow-x: auto;
  margin: 20px 0;
  background: linear-gradient(135deg, rgb(0, 0, 0), rgb(15, 23, 42));
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 12px;
  overflow: hidden;
}

.editor-table-cell {
  border: 1px solid rgba(139, 92, 246, 0.15);
  padding: 12px 16px;
  text-align: left;
  vertical-align: top;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  min-width: 100px;
}

.editor-table-cell-header {
  border: 1px solid rgba(139, 92, 246, 0.3);
  padding: 12px 16px;
  text-align: left;
  vertical-align: top;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.05));
  color: #ffffff;
  font-weight: 600;
  min-width: 100px;
}

.editor-table-cell:hover,
.editor-table-cell-header:hover {
  background: rgba(139, 92, 246, 0.05);
}

/* Image Node Styles - Minimal Figure Container */
.lexical-image-figure {
  margin: 48px auto !important; /* Perfect spacing from text content - force override */
  max-width: 1200px; /* Perfect size for visibility */
  display: table; /* Modern technique for proper figure/figcaption alignment */
  position: relative;
}

/* Force consistent spacing in light mode */
.light-mode-content .lexical-image-figure {
  margin: 48px auto !important;
}

/* Override prose image styles that interfere with our custom image containers */
.prose .lexical-image-figure,
.light-mode-content .prose .lexical-image-figure {
  margin: 48px auto !important;
}

.prose .lexical-image-figure img,
.light-mode-content .prose .lexical-image-figure img {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}


.lexical-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px; /* Keep rounded corners for image container */
}

.lexical-image {
  display: block;
  width: 100%;
  height: auto;
  max-width: 100%;
  border-radius: 12px; /* Keep rounded corners for image */
}

.lexical-image-caption {
  display: table-caption;
  caption-side: bottom;
  margin-top: 8px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.4;
  color: rgba(148, 163, 184, 0.9);
  text-align: center;
  letter-spacing: 0.3px;
  max-width: 100%;
  word-wrap: break-word;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .lexical-image-figure {
    max-width: 100%;
    margin: 32px 0 !important; /* Better mobile spacing - force override */
  }
  
  .light-mode-content .lexical-image-figure,
  .prose .lexical-image-figure,
  .light-mode-content .prose .lexical-image-figure {
    margin: 32px 0 !important;
  }
  
  .lexical-image-caption {
    font-size: 10px;
    padding: 4px 8px 3px;
  }
}

/* Clickable image styles (for read-only views) */
.lexical-image-clickable .lexical-image-container {
  cursor: pointer;
  transition: all 0.3s ease;
}

.lexical-image-clickable .lexical-image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.lexical-image-clickable .lexical-image-container:hover::after {
  content: '🔍';
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgb(0, 0, 0);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

/* YouTube Video Styles - Uses same structure as images for consistency */
.editor-youtube {
  display: block;
  margin: 1rem 0;
}

/* YouTube videos inherit all image figure styles for consistency */
.lexical-image-figure:has(iframe) {
  margin: 1rem 0;
  position: relative;
  display: block;
}

.lexical-image-container:has(iframe) {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

/* Ensure iframe takes full container */
.lexical-image-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none;
  display: block;
}

/* Special hover effects for YouTube videos */
.lexical-image-clickable .lexical-image-container:has(iframe):hover::after {
  content: '▶️';
  font-size: 16px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

/* High resolution displays */
@media (min-height: 1440px) {
  .lexical-image-figure {
    max-width: 1200px;
    margin: 56px auto !important; /* Even more generous spacing for high-res - force override */
  }
  
  .light-mode-content .lexical-image-figure,
  .prose .lexical-image-figure,
  .light-mode-content .prose .lexical-image-figure {
    margin: 56px auto !important;
  }
  
  .lexical-image-caption {
    font-size: 12px;
    padding: 8px 16px 6px;
  }
}

/* Auto-save indicator styles */
.auto-save-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background: rgb(15, 23, 42);
  border: 1px solid rgb(71, 85, 105);
  border-radius: 6px;
  padding: 4px 8px;
}