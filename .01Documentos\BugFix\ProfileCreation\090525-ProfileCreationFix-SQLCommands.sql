-- ====================================================================
-- Bug Fix SQL Commands: Profile Creation Trigger Repair
-- Issue: ProfileCreationFix001
-- Date: 11/01/25
-- ====================================================================

-- PHASE 1: DIAGNOSTIC QUERIES
-- Run these first to understand current state

-- 1.1: Check existing trigger
SELECT trigger_name, event_manipulation, action_statement, action_timing
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- 1.2: Check existing function
SELECT 
    proname as function_name,
    proargnames as argument_names,
    prosecdef as is_security_definer,
    provolatile as volatility,
    prosrc as source_code
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- 1.3: Check users without profiles
SELECT 
    u.id, 
    u.email, 
    u.created_at,
    u.raw_user_meta_data->'username' as username_metadata
FROM auth.users u 
LEFT JOIN profiles p ON u.id = p.id 
WHERE p.id IS NULL;

-- 1.4: Check existing RLS policies on profiles
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'profiles';

-- ====================================================================
-- PHASE 2: BACKUP EXISTING DATA (SAFETY MEASURE)
-- ====================================================================

-- 2.1: Create backup of existing profiles (if any)
CREATE TABLE IF NOT EXISTS profiles_backup_090525 AS 
SELECT * FROM profiles;

-- ====================================================================
-- PHASE 3: REMOVE EXISTING FAULTY TRIGGER/FUNCTION
-- ====================================================================

-- 3.1: Drop trigger if exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 3.2: Drop function if exists  
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- ====================================================================
-- PHASE 4: CREATE NEW ROBUST TRIGGER SYSTEM
-- ====================================================================

-- 4.1: Create enhanced function with proper security and error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER 
SET search_path = public
AS $$
DECLARE
    generated_username TEXT;
    generated_slug TEXT;
    counter INTEGER := 1;
BEGIN
    -- Generate base username from metadata or email
    generated_username := COALESCE(
        NEW.raw_user_meta_data->>'username',
        SPLIT_PART(NEW.email, '@', 1),
        'user_' || SUBSTRING(NEW.id::text, 1, 8)
    );
    
    -- Clean username (remove special characters)
    generated_username := REGEXP_REPLACE(generated_username, '[^a-zA-Z0-9_]', '', 'gi');
    
    -- Ensure minimum length
    IF LENGTH(generated_username) < 3 THEN
        generated_username := 'user_' || SUBSTRING(NEW.id::text, 1, 8);
    END IF;
    
    -- Generate slug from username
    generated_slug := LOWER(REGEXP_REPLACE(generated_username, '[^a-z0-9]', '', 'gi'));
    
    -- Check for username conflicts and resolve them
    WHILE EXISTS (SELECT 1 FROM profiles WHERE username = generated_username) LOOP
        generated_username := COALESCE(
            NEW.raw_user_meta_data->>'username',
            SPLIT_PART(NEW.email, '@', 1),
            'user'
        ) || '_' || counter;
        generated_slug := LOWER(REGEXP_REPLACE(generated_username, '[^a-z0-9]', '', 'gi'));
        counter := counter + 1;
        
        -- Prevent infinite loop
        IF counter > 100 THEN
            generated_username := 'user_' || SUBSTRING(NEW.id::text, 1, 8) || '_' || EXTRACT(EPOCH FROM NOW())::INTEGER;
            generated_slug := LOWER(generated_username);
            EXIT;
        END IF;
    END LOOP;
    
    -- Insert profile with all required fields
    INSERT INTO public.profiles (
        id,
        username,
        display_name,
        slug,
        slug_lower,
        theme,
        privacy_settings,
        is_admin,
        is_online,
        level,
        experience,
        review_count,
        created_at,
        updated_at
    )
    VALUES (
        NEW.id,
        generated_username,
        COALESCE(
            NEW.raw_user_meta_data->>'display_name',
            NEW.raw_user_meta_data->>'full_name', 
            NEW.raw_user_meta_data->>'username',
            generated_username
        ),
        generated_slug,
        generated_slug,
        'muted-dark',
        jsonb_build_object(
            'profile_visibility', 'public',
            'show_online_status', true,
            'show_gaming_profiles', true,
            'show_achievements', true,
            'allow_contact', true,
            'allow_friend_requests', true
        ),
        false,
        true,
        1,
        0,
        0,
        NOW(),
        NOW()
    );
    
    -- Log successful profile creation (for debugging)
    RAISE NOTICE 'Profile created for user: % with username: %', NEW.email, generated_username;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Failed to create profile for user %: %', NEW.email, SQLERRM;
        RETURN NEW;
END;
$$;

-- 4.2: Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- ====================================================================
-- PHASE 5: ENSURE PROPER RLS POLICIES
-- ====================================================================

-- 5.1: Create policy for profile insertion (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'profiles' 
        AND policyname = 'Enable insert for service role'
    ) THEN
        CREATE POLICY "Enable insert for service role" 
        ON public.profiles
        AS PERMISSIVE FOR INSERT
        TO service_role
        WITH CHECK (true);
    END IF;
END $$;

-- 5.2: Create policy for authenticated users to insert their own profile
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'profiles' 
        AND policyname = 'Users can insert their own profile'
    ) THEN
        CREATE POLICY "Users can insert their own profile" 
        ON public.profiles
        AS PERMISSIVE FOR INSERT
        TO authenticated
        WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- ====================================================================
-- PHASE 6: CREATE PROFILES FOR EXISTING USERS WITHOUT PROFILES
-- ====================================================================

-- 6.1: Create profiles for existing users who don't have one
INSERT INTO profiles (
    id, 
    username, 
    display_name, 
    slug, 
    slug_lower, 
    theme, 
    is_admin, 
    is_online, 
    level, 
    experience, 
    review_count, 
    privacy_settings,
    created_at,
    updated_at
)
SELECT 
    u.id,
    COALESCE(
        u.raw_user_meta_data->>'username',
        REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
        'user_' || SUBSTRING(u.id::text, 1, 8)
    ) || CASE 
        WHEN EXISTS (
            SELECT 1 FROM profiles p2 
            WHERE p2.username = COALESCE(
                u.raw_user_meta_data->>'username',
                REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
                'user_' || SUBSTRING(u.id::text, 1, 8)
            )
        ) THEN '_' || EXTRACT(EPOCH FROM u.created_at)::INTEGER
        ELSE ''
    END as username,
    COALESCE(
        u.raw_user_meta_data->>'display_name',
        u.raw_user_meta_data->>'full_name',
        u.raw_user_meta_data->>'username',
        'User'
    ) as display_name,
    LOWER(REGEXP_REPLACE(
        COALESCE(
            u.raw_user_meta_data->>'username',
            REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
            'user_' || SUBSTRING(u.id::text, 1, 8)
        ), '[^a-z0-9]', '', 'gi'
    )) || CASE 
        WHEN EXISTS (
            SELECT 1 FROM profiles p3 
            WHERE p3.slug = LOWER(REGEXP_REPLACE(
                COALESCE(
                    u.raw_user_meta_data->>'username',
                    REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
                    'user_' || SUBSTRING(u.id::text, 1, 8)
                ), '[^a-z0-9]', '', 'gi'
            ))
        ) THEN EXTRACT(EPOCH FROM u.created_at)::INTEGER::TEXT
        ELSE ''
    END as slug,
    LOWER(REGEXP_REPLACE(
        COALESCE(
            u.raw_user_meta_data->>'username',
            REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
            'user_' || SUBSTRING(u.id::text, 1, 8)
        ), '[^a-z0-9]', '', 'gi'
    )) || CASE 
        WHEN EXISTS (
            SELECT 1 FROM profiles p4 
            WHERE p4.slug_lower = LOWER(REGEXP_REPLACE(
                COALESCE(
                    u.raw_user_meta_data->>'username',
                    REGEXP_REPLACE(SPLIT_PART(u.email, '@', 1), '[^a-zA-Z0-9_]', '', 'gi'),
                    'user_' || SUBSTRING(u.id::text, 1, 8)
                ), '[^a-z0-9]', '', 'gi'
            ))
        ) THEN EXTRACT(EPOCH FROM u.created_at)::INTEGER::TEXT
        ELSE ''
    END as slug_lower,
    'muted-dark',
    false,
    true,
    1,
    0,
    0,
    jsonb_build_object(
        'profile_visibility', 'public',
        'show_online_status', true,
        'show_gaming_profiles', true,
        'show_achievements', true,
        'allow_contact', true,
        'allow_friend_requests', true
    ),
    u.created_at,
    NOW()
FROM auth.users u 
LEFT JOIN profiles p ON u.id = p.id 
WHERE p.id IS NULL;

-- ====================================================================
-- PHASE 7: VERIFICATION QUERIES
-- ====================================================================

-- 7.1: Verify trigger exists and is active
SELECT 
    trigger_name, 
    event_manipulation, 
    action_timing,
    action_statement 
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- 7.2: Verify function exists with proper security
SELECT 
    proname as function_name,
    prosecdef as is_security_definer,
    provolatile as volatility
FROM pg_proc 
WHERE proname = 'handle_new_user';

-- 7.3: Check that all users now have profiles
SELECT 
    COUNT(*) as users_without_profiles
FROM auth.users u 
LEFT JOIN profiles p ON u.id = p.id 
WHERE p.id IS NULL;

-- 7.4: Verify recent profiles creation
SELECT 
    username,
    display_name,
    created_at,
    'Created by fix' as source
FROM profiles 
WHERE created_at >= NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;

-- ====================================================================
-- PHASE 8: TEST THE TRIGGER (MANUAL TEST)
-- ====================================================================

-- COMMENT OUT THE FOLLOWING SECTION AFTER REAL TESTING
/*
-- 8.1: Test trigger with dummy user (REMOVE AFTER TESTING)
DO $$
DECLARE
    test_user_id UUID := gen_random_uuid();
BEGIN
    -- Insert test user
    INSERT INTO auth.users (
        id, 
        email, 
        encrypted_password, 
        raw_user_meta_data,
        created_at, 
        updated_at
    )
    VALUES (
        test_user_id,
        'test_' || EXTRACT(EPOCH FROM NOW())::INTEGER || '@example.com',
        'test_encrypted_password',
        jsonb_build_object(
            'username', 'testuser_' || EXTRACT(EPOCH FROM NOW())::INTEGER,
            'display_name', 'Test User Fix'
        ),
        NOW(),
        NOW()
    );
    
    -- Check if profile was created
    IF EXISTS (SELECT 1 FROM profiles WHERE id = test_user_id) THEN
        RAISE NOTICE '✅ Trigger test PASSED - Profile created automatically';
    ELSE
        RAISE NOTICE '❌ Trigger test FAILED - Profile NOT created';
    END IF;
    
    -- Clean up test data
    DELETE FROM profiles WHERE id = test_user_id;
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;
*/

-- ====================================================================
-- COMPLETION MESSAGE
-- ====================================================================

SELECT 
    'Profile creation trigger has been fixed!' as status,
    NOW() as completed_at;

-- ====================================================================
-- MONITORING QUERIES (FOR FUTURE USE)
-- ====================================================================

-- Monitor for users without profiles (should always return 0)
-- Run this periodically: 
-- SELECT COUNT(*) FROM auth.users u LEFT JOIN profiles p ON u.id = p.id WHERE p.id IS NULL;

-- Monitor trigger function errors:
-- Check PostgreSQL logs in Supabase Dashboard > Logs > Postgres Logs
-- Look for NOTICE/WARNING messages from handle_new_user function 