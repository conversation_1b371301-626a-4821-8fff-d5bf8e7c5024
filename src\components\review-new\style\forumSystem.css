/* Forum System CSS - Fully Responsive */

:root {
  /* Color variables */
  --forum-bg-primary: rgb(15, 23, 42);
  --forum-bg-secondary: rgb(30, 41, 59);
  --forum-bg-tertiary: rgb(51, 65, 85);
  --forum-border-primary: rgb(51, 65, 85);
  --forum-border-secondary: rgb(71, 85, 105);
  --forum-text-primary: rgb(226, 232, 240);
  --forum-text-secondary: rgb(203, 213, 225);
  --forum-text-muted: rgb(148, 163, 184);
  --forum-text-dark: rgb(100, 116, 139);
  
  /* Spacing variables */
  --forum-spacing-xs: 0.25rem;
  --forum-spacing-sm: 0.5rem;
  --forum-spacing-md: 0.75rem;
  --forum-spacing-lg: 1rem;
  --forum-spacing-xl: 1.5rem;
  --forum-spacing-2xl: 2rem;
  --forum-spacing-3xl: 3rem;
  
  /* Font sizes */
  --forum-text-xs: 0.75rem;
  --forum-text-sm: 0.875rem;
  --forum-text-base: 1rem;
  --forum-text-lg: 1.125rem;
  --forum-text-xl: 1.25rem;
  --forum-text-2xl: 1.5rem;
  
  /* Avatar sizes */
  --forum-avatar-sm: 1.5rem;
  --forum-avatar-md: 2rem;
  --forum-avatar-lg: 2.5rem;
  --forum-avatar-xl: 3rem;
  
  /* Border radius */
  --forum-radius-sm: 0.25rem;
  --forum-radius-md: 0.5rem;
  --forum-radius-lg: 0.75rem;
  --forum-radius-full: 9999px;
}

/* Base container */
.forum-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--forum-spacing-sm);
}

/* Main forum wrapper */
.forum-main {
  background: var(--forum-bg-primary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-primary);
  overflow: hidden;
  position: relative;
  max-width: 100%;
}

/* Loading overlay */
.forum-loading-overlay {
  position: absolute;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0, 0, 0);
}

.forum-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 2rem;
  width: 2rem;
  border-bottom: 2px solid var(--forum-text-secondary);
}

/* Header section */
.forum-header {
  border-bottom: 1px solid var(--forum-border-secondary);
  padding: var(--forum-spacing-lg);
}

.forum-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--forum-spacing-lg);
  gap: var(--forum-spacing-md);
  flex-wrap: wrap;
}

.forum-header-left {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-lg);
  flex: 1;
  min-width: 0;
}

.forum-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--forum-text-muted);
  flex-shrink: 0;
}

.forum-title {
  font-size: var(--forum-text-xl);
  font-weight: 700;
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  line-height: 1.2;
  word-break: break-word;
}

.forum-badge {
  background: var(--forum-bg-secondary);
  color: var(--forum-text-muted);
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  border-radius: var(--forum-radius-full);
  font-size: var(--forum-text-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
  flex-shrink: 0;
}

.forum-header-right {
  display: flex;
  gap: var(--forum-spacing-md);
  flex-shrink: 0;
}

/* Buttons */
.forum-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  padding: var(--forum-spacing-sm) var(--forum-spacing-lg);
  background: var(--forum-bg-secondary);
  color: var(--forum-text-primary);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  transition: all 0.2s;
  border: 1px solid var(--forum-border-secondary);
  cursor: pointer;
  white-space: nowrap;
  min-height: 2.5rem;
}

.forum-btn:hover {
  background: var(--forum-bg-tertiary);
}

.forum-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.forum-btn-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Filter buttons */
.forum-filters {
  display: flex;
  gap: var(--forum-spacing-sm);
  flex-wrap: wrap;
}

.forum-filter-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  transition: all 0.2s;
  border: 1px solid;
  cursor: pointer;
  white-space: nowrap;
  min-height: 2rem;
}

.forum-filter-btn.active {
  background: var(--forum-bg-tertiary);
  border-color: rgb(71, 85, 105);
  color: rgb(241, 245, 249);
}

.forum-filter-btn:not(.active) {
  background: var(--forum-bg-secondary);
  border-color: var(--forum-border-primary);
  color: var(--forum-text-muted);
}

.forum-filter-btn:not(.active):hover {
  background: var(--forum-bg-secondary);
  color: var(--forum-text-secondary);
}

.forum-filter-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

/* New discussion form */
.forum-new-discussion {
  border-bottom: 1px solid var(--forum-border-secondary);
  padding: var(--forum-spacing-xl);
  background: var(--forum-bg-primary);
}

.forum-form-space {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-lg);
}

/* Form inputs */
.forum-input,
.forum-textarea {
  width: 100%;
  padding: var(--forum-spacing-lg);
  background: var(--forum-bg-secondary);
  border: 1px solid rgb(71, 85, 105);
  border-radius: var(--forum-radius-md);
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  box-sizing: border-box;
  resize: vertical;
  min-height: 2.5rem;
}

.forum-textarea {
  min-height: 6rem;
  resize: vertical;
}

.forum-input::placeholder,
.forum-textarea::placeholder {
  color: var(--forum-text-muted);
}

.forum-input:focus,
.forum-textarea:focus {
  outline: none;
  ring: 2px solid var(--forum-border-secondary);
  border-color: rgb(71, 85, 105);
}

/* Form footer */
.forum-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--forum-spacing-lg);
  flex-wrap: wrap;
}

.forum-user-info {
  font-size: var(--forum-text-xs);
  color: var(--forum-text-muted);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
}

.forum-user-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.forum-form-actions {
  display: flex;
  gap: var(--forum-spacing-md);
  flex-shrink: 0;
}

.forum-btn-cancel {
  padding: var(--forum-spacing-sm) var(--forum-spacing-lg);
  color: var(--forum-text-muted);
  transition: color 0.2s;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  background: none;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  min-height: 2.5rem;
}

.forum-btn-cancel:hover {
  color: var(--forum-text-primary);
}

.forum-btn-primary {
  padding: var(--forum-spacing-sm) var(--forum-spacing-xl);
  background: rgba(255, 255, 255, 0.15);
  color: var(--forum-text-primary);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  transition: all 0.2s;
  border: 1px solid var(--forum-border-secondary);
  cursor: pointer;
  white-space: nowrap;
  min-height: 2.5rem;
}

.forum-btn-primary:hover {
  background: rgba(255, 255, 255, 0.25);
}

.forum-btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Content area */
.forum-content {
  padding: var(--forum-spacing-xl);
}

/* Discussion list */
.forum-discussions-list {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-md);
}

/* Empty state */
.forum-empty-state {
  text-align: center;
  padding: var(--forum-spacing-3xl) var(--forum-spacing-lg);
}

.forum-empty-icon {
  width: 3rem;
  height: 3rem;
  color: var(--forum-text-muted);
  margin: 0 auto var(--forum-spacing-lg);
}

.forum-empty-text {
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-base);
}

/* Discussion items */
.forum-discussion-item {
  padding: var(--forum-spacing-lg);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-primary);
  cursor: pointer;
  transition: all 0.3s;
}

.forum-discussion-item:hover {
  background: var(--forum-bg-secondary);
  border-color: rgba(255, 255, 255, 0.3);
}

.forum-discussion-content {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-lg);
}

/* Avatars */
.forum-avatar {
  width: var(--forum-avatar-lg);
  height: var(--forum-avatar-lg);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  overflow: hidden;
  flex-shrink: 0;
}

.forum-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-avatar-large {
  width: var(--forum-avatar-xl);
  height: var(--forum-avatar-xl);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  overflow: hidden;
  flex-shrink: 0;
}

.forum-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-avatar-small {
  width: var(--forum-avatar-xl);
  height: var(--forum-avatar-xl);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  overflow: hidden;
  flex-shrink: 0;
}

.forum-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-avatar-comment {
  width: var(--forum-avatar-md);
  height: var(--forum-avatar-md);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  overflow: hidden;
  flex-shrink: 0;
}

.forum-avatar-comment img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.forum-avatar-nested {
  width: var(--forum-avatar-sm);
  height: var(--forum-avatar-sm);
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forum-text-secondary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  overflow: hidden;
  flex-shrink: 0;
}

.forum-avatar-nested img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Discussion info */
.forum-discussion-info {
  flex: 1;
  min-width: 0;
}

.forum-discussion-title {
  font-weight: 600;
  color: var(--forum-text-primary);
  margin-bottom: var(--forum-spacing-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-base);
  line-height: 1.4;
  word-break: break-word;
}

.forum-discussion-item:hover .forum-discussion-title {
  color: white;
}

.forum-discussion-meta {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-lg);
  font-size: var(--forum-text-xs);
  color: var(--forum-text-muted);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  flex-wrap: wrap;
}

.forum-meta-item {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  white-space: nowrap;
}

.forum-meta-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.forum-discussion-arrow {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.forum-arrow-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--forum-text-muted);
  transition: color 0.2s;
}

.forum-discussion-item:hover .forum-arrow-icon {
  color: var(--forum-text-secondary);
}

/* Pagination */
.forum-pagination {
  margin-top: var(--forum-spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--forum-spacing-lg);
  flex-wrap: wrap;
}

.forum-pagination-info {
  font-size: var(--forum-text-sm);
  color: var(--forum-text-muted);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  flex-wrap: wrap;
}

.forum-pagination-btn {
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  background: var(--forum-bg-secondary);
  color: var(--forum-text-secondary);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  transition: all 0.2s;
  border: 1px solid var(--forum-border-primary);
  cursor: pointer;
  white-space: nowrap;
  min-height: 2rem;
}

.forum-pagination-btn:hover {
  background: var(--forum-bg-secondary);
}

.forum-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.forum-pagination-pages {
  display: flex;
  gap: var(--forum-spacing-xs);
  flex-wrap: wrap;
}

.forum-pagination-page {
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  transition: all 0.2s;
  border: 1px solid;
  cursor: pointer;
  min-width: 2rem;
  text-align: center;
}

.forum-pagination-page.active {
  background: var(--forum-bg-tertiary);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgb(241, 245, 249);
}

.forum-pagination-page:not(.active) {
  background: var(--forum-bg-secondary);
  border-color: var(--forum-border-primary);
  color: var(--forum-text-muted);
}

.forum-pagination-page:not(.active):hover {
  background: var(--forum-bg-secondary);
  color: var(--forum-text-secondary);
}

.forum-pagination-ellipsis {
  padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
  color: var(--forum-text-dark);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
}

/* Thread view */
.forum-thread-view {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-xl);
}

/* Original post */
.forum-original-post {
  background: var(--forum-bg-tertiary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-secondary);
  padding: var(--forum-spacing-xl);
}

.forum-post-content {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-lg);
}

.forum-post-body {
  flex: 1;
  min-width: 0;
}

.forum-post-header {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-md);
  margin-bottom: var(--forum-spacing-md);
  flex-wrap: wrap;
}

.forum-author-name {
  font-weight: 600;
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-base);
}

.forum-op-badge {
  background: rgba(255, 255, 255, 0.15);
  color: var(--forum-text-secondary);
  padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
  border-radius: var(--forum-radius-sm);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
}

.forum-post-time {
  color: var(--forum-text-muted);
  font-size: var(--forum-text-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-post-text-container {
  background: var(--forum-bg-tertiary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-secondary);
  padding: var(--forum-spacing-lg);
  margin-bottom: var(--forum-spacing-lg);
}

.forum-post-text {
  color: var(--forum-text-primary);
  line-height: 1.6;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  white-space: pre-wrap;
  word-break: break-word;
}

/* Post actions */
.forum-post-actions {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-lg);
  flex-wrap: wrap;
}

/* Like buttons */
.forum-like-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  font-size: var(--forum-text-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  transition: color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
  border-radius: var(--forum-radius-md);
}

.forum-like-btn.liked {
  color: var(--forum-text-primary);
}

.forum-like-btn.liked:hover {
  color: white;
}

.forum-like-btn:not(.liked) {
  color: var(--forum-text-muted);
}

.forum-like-btn:not(.liked):hover {
  color: var(--forum-text-secondary);
}

.forum-like-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.forum-like-icon.filled {
  fill: currentColor;
}

/* New comment */
.forum-new-comment {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-lg);
}

.forum-comment-form {
  flex: 1;
  background: var(--forum-bg-tertiary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-secondary);
  padding: var(--forum-spacing-lg);
}

.forum-comment-textarea {
  width: 100%;
  padding: var(--forum-spacing-md);
  background: var(--forum-bg-secondary);
  border: 1px solid rgb(71, 85, 105);
  border-radius: var(--forum-radius-md);
  resize: vertical;
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  min-height: 4rem;
  box-sizing: border-box;
}

.forum-comment-textarea::placeholder {
  color: var(--forum-text-muted);
}

.forum-comment-textarea:focus {
  outline: none;
  ring: 2px solid var(--forum-border-secondary);
  border-color: rgb(71, 85, 105);
}

.forum-comment-submit {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--forum-spacing-md);
}

/* Comments section */
.forum-comments-section {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-lg);
}

.forum-comments-header {
  font-weight: 600;
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  font-size: var(--forum-text-lg);
}

.forum-comments-list {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-lg);
}

.forum-comment-thread {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-md);
}

/* Comment */
.forum-comment {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-lg);
}

.forum-comment-body {
  flex: 1;
  background: var(--forum-bg-secondary);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-primary);
  padding: var(--forum-spacing-lg);
  min-width: 0;
}

.forum-comment-header {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  margin-bottom: var(--forum-spacing-sm);
  flex-wrap: wrap;
}

.forum-comment-author {
  font-weight: 600;
  color: var(--forum-text-primary);
  font-size: var(--forum-text-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-comment-op-badge {
  background: rgba(255, 255, 255, 0.15);
  color: var(--forum-text-secondary);
  padding: 0.125rem var(--forum-spacing-xs);
  border-radius: var(--forum-radius-sm);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
}

.forum-comment-time {
  color: var(--forum-text-muted);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-comment-text {
  color: var(--forum-text-primary);
  font-size: var(--forum-text-sm);
  margin-bottom: var(--forum-spacing-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Comment actions */
.forum-comment-actions {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-lg);
  flex-wrap: wrap;
}

.forum-comment-like-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  transition: color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--forum-spacing-xs);
  border-radius: var(--forum-radius-md);
}

.forum-comment-like-btn.liked {
  color: var(--forum-text-primary);
}

.forum-comment-like-btn.liked:hover {
  color: white;
}

.forum-comment-like-btn:not(.liked) {
  color: var(--forum-text-muted);
}

.forum-comment-like-btn:not(.liked):hover {
  color: var(--forum-text-secondary);
}

.forum-comment-like-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.forum-comment-like-icon.filled {
  fill: currentColor;
}

/* Reply button */
.forum-reply-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  font-size: var(--forum-text-xs);
  color: var(--forum-text-muted);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  transition: color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--forum-spacing-xs);
  border-radius: var(--forum-radius-md);
}

.forum-reply-btn:hover {
  color: var(--forum-text-secondary);
}

.forum-reply-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

/* Reply form */
.forum-reply-form {
  margin-top: var(--forum-spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-md);
}

.forum-reply-textarea {
  width: 100%;
  padding: var(--forum-spacing-md);
  background: var(--forum-bg-secondary);
  border: 1px solid rgb(71, 85, 105);
  border-radius: var(--forum-radius-md);
  resize: vertical;
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
  min-height: 3rem;
  box-sizing: border-box;
}

.forum-reply-textarea::placeholder {
  color: var(--forum-text-muted);
}

.forum-reply-textarea:focus {
  outline: none;
  ring: 2px solid var(--forum-border-secondary);
  border-color: rgb(71, 85, 105);
}

.forum-reply-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--forum-spacing-sm);
}

.forum-reply-cancel {
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  color: var(--forum-text-muted);
  transition: color 0.2s;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: var(--forum-radius-md);
  white-space: nowrap;
}

.forum-reply-cancel:hover {
  color: var(--forum-text-primary);
}

.forum-reply-submit {
  padding: var(--forum-spacing-xs) var(--forum-spacing-lg);
  background: rgba(255, 255, 255, 0.15);
  color: var(--forum-text-primary);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  transition: all 0.2s;
  border: 1px solid var(--forum-border-secondary);
  cursor: pointer;
  white-space: nowrap;
}

.forum-reply-submit:hover {
  background: rgba(255, 255, 255, 0.25);
}

.forum-reply-submit:disabled {
  opacity: 0.5;
}

/* Nested replies */
.forum-nested-replies {
  display: flex;
  flex-direction: column;
  gap: var(--forum-spacing-md);
}

.forum-nested-comment {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-md);
  margin-left: var(--forum-spacing-2xl);
}

.forum-nested-comment-body {
  flex: 1;
  background: var(--forum-bg-primary);
  border-radius: var(--forum-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.05);
  padding: var(--forum-spacing-md);
  min-width: 0;
}

.forum-nested-comment-header {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-sm);
  margin-bottom: var(--forum-spacing-sm);
  flex-wrap: wrap;
}

.forum-nested-comment-author {
  font-weight: 600;
  color: var(--forum-text-primary);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-nested-comment-op-badge {
  background: rgba(255, 255, 255, 0.15);
  color: var(--forum-text-secondary);
  padding: var(--forum-spacing-xs);
  border-radius: var(--forum-radius-sm);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
}

.forum-nested-comment-time {
  color: var(--forum-text-muted);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.forum-nested-comment-text {
  color: var(--forum-text-primary);
  font-size: 0.8rem;
  margin-bottom: var(--forum-spacing-sm);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.forum-nested-comment-actions {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-md);
  flex-wrap: wrap;
}

.forum-nested-like-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  transition: color 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--forum-spacing-xs);
  border-radius: var(--forum-radius-md);
}

.forum-nested-like-btn.liked {
  color: var(--forum-text-primary);
}

.forum-nested-like-btn.liked:hover {
  color: white;
}

.forum-nested-like-btn:not(.liked) {
  color: var(--forum-text-muted);
}

.forum-nested-like-btn:not(.liked):hover {
  color: var(--forum-text-secondary);
}

.forum-nested-like-icon {
  width: 0.625rem;
  height: 0.625rem;
  flex-shrink: 0;
}

.forum-nested-like-icon.filled {
  fill: currentColor;
}

/* Loading text */
.forum-loading-text {
  margin-left: var(--forum-spacing-md);
  color: var(--forum-text-primary);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-sm);
}

/* Auth required */
.forum-auth-required {
  background: var(--forum-bg-primary);
  backdrop-filter: blur(16px);
  border-radius: var(--forum-radius-md);
  border: 1px solid var(--forum-border-primary);
  padding: var(--forum-spacing-2xl);
  text-align: center;
  max-width: 100%;
}

.forum-auth-icon {
  width: 3rem;
  height: 3rem;
  color: var(--forum-text-secondary);
  margin: 0 auto var(--forum-spacing-lg);
}

.forum-auth-title {
  font-size: var(--forum-text-xl);
  font-weight: 600;
  color: var(--forum-text-primary);
  margin-bottom: var(--forum-spacing-sm);
}

.forum-auth-description {
  color: var(--forum-text-secondary);
  margin-bottom: var(--forum-spacing-lg);
  font-size: var(--forum-text-base);
}

.forum-auth-btn {
  padding: var(--forum-spacing-sm) var(--forum-spacing-xl);
  background: var(--forum-bg-secondary);
  color: var(--forum-text-primary);
  border-radius: var(--forum-radius-md);
  font-weight: 500;
  transition: all 0.3s;
  border: 1px solid var(--forum-border-secondary);
  cursor: pointer;
  font-size: var(--forum-text-base);
}

.forum-auth-btn:hover {
  background: var(--forum-bg-tertiary);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation keyframes */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Media Queries */

/* Tablet and smaller desktop */
@media (max-width: 1024px) {
  .forum-container {
    padding: 0 var(--forum-spacing-md);
  }
  
  .forum-header {
    padding: var(--forum-spacing-lg) var(--forum-spacing-lg);
  }
  
  .forum-content {
    padding: var(--forum-spacing-lg) var(--forum-spacing-lg);
  }
  
  .forum-new-discussion {
    padding: var(--forum-spacing-lg) var(--forum-spacing-lg);
  }
  
  .forum-original-post {
    padding: var(--forum-spacing-lg);
  }
}

/* Large mobile devices */
@media (max-width: 768px) {
  :root {
    --forum-spacing-xl: 1.25rem;
    --forum-spacing-2xl: 1.75rem;
    --forum-spacing-3xl: 2.5rem;
    --forum-avatar-lg: 2rem;
    --forum-avatar-xl: 2.5rem;
  }
  
  .forum-container {
    padding: 0 var(--forum-spacing-sm);
  }
  
  .forum-header {
    padding: var(--forum-spacing-lg);
  }
  
  .forum-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--forum-spacing-lg);
  }
  
  .forum-header-left {
    width: 100%;
    justify-content: space-between;
  }
  
  .forum-header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .forum-title {
    font-size: var(--forum-text-lg);
  }
  
  .forum-badge {
    font-size: 0.75rem;
  }
  
  .forum-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--forum-spacing-xs);
  }
  
  .forum-filter-btn {
    flex-shrink: 0;
  }
  
  .forum-content {
    padding: var(--forum-spacing-lg);
  }
  
  .forum-new-discussion {
    padding: var(--forum-spacing-lg);
  }
  
  .forum-discussion-content {
    gap: var(--forum-spacing-md);
  }
  
  .forum-discussion-meta {
    flex-wrap: wrap;
    gap: var(--forum-spacing-md);
  }
  
  .forum-post-content {
    gap: var(--forum-spacing-md);
  }
  
  .forum-original-post {
    padding: var(--forum-spacing-lg);
  }
  
  .forum-new-comment {
    gap: var(--forum-spacing-md);
  }
  
  .forum-comment {
    gap: var(--forum-spacing-md);
  }
  
  .forum-nested-comment {
    margin-left: var(--forum-spacing-xl);
    gap: var(--forum-spacing-sm);
  }
  
  .forum-pagination {
    flex-direction: column;
    gap: var(--forum-spacing-md);
    text-align: center;
  }
  
  .forum-pagination-controls {
    justify-content: center;
  }
  
  .forum-form-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--forum-spacing-md);
  }
  
  .forum-form-actions {
    align-self: flex-end;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  :root {
    --forum-spacing-lg: 0.875rem;
    --forum-spacing-xl: 1rem;
    --forum-spacing-2xl: 1.5rem;
    --forum-spacing-3xl: 2rem;
    --forum-avatar-md: 1.75rem;
    --forum-avatar-lg: 1.875rem;
    --forum-avatar-xl: 2.25rem;
    --forum-text-base: 0.875rem;
    --forum-text-lg: 1rem;
    --forum-text-xl: 1.125rem;
  }
  
  .forum-container {
    padding: 0 var(--forum-spacing-xs);
  }
  
  .forum-header {
    padding: var(--forum-spacing-md);
  }
  
  .forum-content {
    padding: var(--forum-spacing-md);
  }
  
  .forum-new-discussion {
    padding: var(--forum-spacing-md);
  }
  
  .forum-original-post {
    padding: var(--forum-spacing-md);
  }
  
  .forum-title {
    font-size: var(--forum-text-base);
    line-height: 1.3;
  }
  
  .forum-discussion-title {
    font-size: var(--forum-text-sm);
    line-height: 1.4;
  }
  
  .forum-post-content {
    flex-direction: column;
    gap: var(--forum-spacing-md);
  }
  
  .forum-avatar-large {
    align-self: flex-start;
  }
  
  .forum-new-comment {
    flex-direction: column;
    gap: var(--forum-spacing-md);
  }
  
  .forum-avatar-small {
    align-self: flex-start;
  }
  
  .forum-comment {
    flex-direction: column;
    gap: var(--forum-spacing-md);
  }
  
  .forum-avatar-comment {
    align-self: flex-start;
  }
  
  .forum-nested-comment {
    margin-left: var(--forum-spacing-lg);
    flex-direction: column;
    gap: var(--forum-spacing-sm);
  }
  
  .forum-avatar-nested {
    align-self: flex-start;
  }
  
  .forum-pagination-pages {
    max-width: 100%;
    overflow-x: auto;
    justify-content: center;
  }
  
  .forum-filters {
    gap: var(--forum-spacing-xs);
  }
  
  .forum-filter-btn {
    padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
    font-size: 0.7rem;
  }
  
  .forum-discussion-meta {
    gap: var(--forum-spacing-sm);
  }
  
  .forum-meta-item {
    font-size: 0.7rem;
  }
  
  .forum-btn {
    padding: var(--forum-spacing-xs) var(--forum-spacing-md);
    font-size: var(--forum-text-xs);
    min-height: 2rem;
  }
  
  .forum-btn-primary {
    padding: var(--forum-spacing-xs) var(--forum-spacing-md);
    font-size: var(--forum-text-xs);
    min-height: 2rem;
  }
  
  .forum-input,
  .forum-textarea {
    padding: var(--forum-spacing-md);
    font-size: var(--forum-text-xs);
  }
  
  .forum-comment-textarea,
  .forum-reply-textarea {
    padding: var(--forum-spacing-sm);
    font-size: var(--forum-text-xs);
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  :root {
    --forum-spacing-md: 0.5rem;
    --forum-spacing-lg: 0.75rem;
    --forum-spacing-xl: 0.875rem;
    --forum-spacing-2xl: 1.25rem;
    --forum-avatar-sm: 1.25rem;
    --forum-avatar-md: 1.5rem;
    --forum-avatar-lg: 1.75rem;
    --forum-avatar-xl: 2rem;
  }
  
  .forum-header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--forum-spacing-sm);
  }
  
  .forum-badge {
    font-size: 0.7rem;
    padding: 0.125rem var(--forum-spacing-sm);
  }
  
  .forum-nested-comment {
    margin-left: var(--forum-spacing-md);
  }
  
  .forum-discussion-arrow {
    display: none;
  }
  
  .forum-filter-btn {
    padding: 0.125rem var(--forum-spacing-xs);
    font-size: 0.65rem;
  }
  
  .forum-pagination-page,
  .forum-pagination-btn {
    padding: 0.125rem var(--forum-spacing-xs);
    font-size: var(--forum-text-xs);
    min-width: 1.5rem;
  }
}

/* ==============================================
   MODERATION SYSTEM CSS
   ============================================== */

/* Moderation filter buttons */
.forum-moderation-filters {
  display: flex;
  gap: var(--forum-spacing-sm);
  flex-wrap: wrap;
  margin-top: var(--forum-spacing-md);
  padding-top: var(--forum-spacing-md);
  border-top: 1px solid var(--forum-border-primary);
}

.forum-mod-filter-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  transition: all 0.2s;
  border: 1px solid;
  cursor: pointer;
  white-space: nowrap;
  min-height: 2rem;
  background: var(--forum-bg-secondary);
  color: var(--forum-text-muted);
  border-color: var(--forum-border-primary);
}

.forum-mod-filter-btn:hover {
  background: var(--forum-bg-tertiary);
  color: var(--forum-text-secondary);
  border-color: var(--forum-border-secondary);
}

.forum-mod-filter-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
  color: rgb(199, 210, 254);
}

/* Pending indicator */
.forum-pending-indicator {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  background: rgba(245, 158, 11, 0.2);
  color: rgb(251, 191, 36);
  padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
  border-radius: var(--forum-radius-full);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.forum-pending-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

/* Status badges */
.forum-status-badge {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  padding: var(--forum-spacing-xs) var(--forum-spacing-sm);
  border-radius: var(--forum-radius-sm);
  font-size: var(--forum-text-xs);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid;
}

.forum-status-badge.approved {
  background: rgba(34, 197, 94, 0.15);
  color: rgb(134, 239, 172);
  border-color: rgba(34, 197, 94, 0.3);
}

.forum-status-badge.pending {
  background: rgba(245, 158, 11, 0.15);
  color: rgb(251, 191, 36);
  border-color: rgba(245, 158, 11, 0.3);
}

.forum-status-badge.hidden {
  background: rgba(239, 68, 68, 0.15);
  color: rgb(252, 165, 165);
  border-color: rgba(239, 68, 68, 0.3);
}

.forum-status-icon {
  width: 0.75rem;
  height: 0.75rem;
  flex-shrink: 0;
}

.forum-status-icon.approved {
  color: rgb(34, 197, 94);
}

.forum-status-icon.pending {
  color: rgb(245, 158, 11);
}

.forum-status-icon.hidden {
  color: rgb(239, 68, 68);
}

/* Discussion title row for status badges */
.forum-discussion-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--forum-spacing-md);
  margin-bottom: var(--forum-spacing-sm);
}

.forum-discussion-title-row .forum-discussion-title {
  margin-bottom: 0;
  flex: 1;
  min-width: 0;
}

/* Moderated content styling */
.forum-discussion-item.moderated {
  opacity: 0.75;
  border-style: dashed;
}

.forum-original-post.moderated,
.forum-comment.moderated,
.forum-nested-comment.moderated {
  opacity: 0.8;
  border-style: dashed;
  background: var(--forum-bg-primary);
}

/* Individual moderation controls */
.forum-moderation-controls,
.forum-discussion-moderation,
.forum-post-moderation,
.forum-comment-moderation,
.forum-nested-comment-moderation {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  flex-shrink: 0;
}

.forum-discussion-moderation {
  margin-left: auto;
  padding-left: var(--forum-spacing-md);
}

.forum-post-moderation {
  margin-left: auto;
}

.forum-comment-moderation,
.forum-nested-comment-moderation {
  margin-left: auto;
}

.forum-mod-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: var(--forum-radius-md);
  transition: all 0.2s;
  border: 1px solid;
  cursor: pointer;
  padding: 0;
  background: transparent;
}

.forum-mod-btn svg {
  width: 0.875rem;
  height: 0.875rem;
  flex-shrink: 0;
}

.forum-mod-btn.approve {
  color: rgb(34, 197, 94);
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
}

.forum-mod-btn.approve:hover {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.5);
}

.forum-mod-btn.hide {
  color: rgb(245, 158, 11);
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(245, 158, 11, 0.1);
}

.forum-mod-btn.hide:hover {
  background: rgba(245, 158, 11, 0.2);
  border-color: rgba(245, 158, 11, 0.5);
}

.forum-mod-btn.delete {
  color: rgb(239, 68, 68);
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
}

.forum-mod-btn.delete:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
}

.forum-mod-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Batch moderation */
.forum-batch-moderation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--forum-spacing-lg);
  padding: var(--forum-spacing-lg);
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: var(--forum-radius-md);
  margin-bottom: var(--forum-spacing-lg);
  flex-wrap: wrap;
}

.forum-batch-info {
  font-size: var(--forum-text-sm);
  color: rgb(199, 210, 254);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-weight: 600;
}

.forum-batch-actions {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-md);
  flex-wrap: wrap;
}

.forum-batch-btn {
  display: flex;
  align-items: center;
  gap: var(--forum-spacing-xs);
  padding: var(--forum-spacing-xs) var(--forum-spacing-md);
  border-radius: var(--forum-radius-md);
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: var(--forum-text-xs);
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid;
  cursor: pointer;
  white-space: nowrap;
  min-height: 2rem;
}

.forum-batch-btn svg {
  width: 0.875rem;
  height: 0.875rem;
  flex-shrink: 0;
}

.forum-batch-btn.approve {
  background: rgba(34, 197, 94, 0.15);
  color: rgb(134, 239, 172);
  border-color: rgba(34, 197, 94, 0.3);
}

.forum-batch-btn.approve:hover {
  background: rgba(34, 197, 94, 0.25);
  border-color: rgba(34, 197, 94, 0.5);
}

.forum-batch-btn.hide {
  background: rgba(245, 158, 11, 0.15);
  color: rgb(251, 191, 36);
  border-color: rgba(245, 158, 11, 0.3);
}

.forum-batch-btn.hide:hover {
  background: rgba(245, 158, 11, 0.25);
  border-color: rgba(245, 158, 11, 0.5);
}

.forum-batch-btn.cancel {
  background: rgba(156, 163, 175, 0.15);
  color: rgb(209, 213, 219);
  border-color: rgba(156, 163, 175, 0.3);
}

.forum-batch-btn.cancel:hover {
  background: rgba(156, 163, 175, 0.25);
  border-color: rgba(156, 163, 175, 0.5);
}

/* Checkboxes for batch selection */
.forum-discussion-item {
  display: flex;
  align-items: flex-start;
  gap: var(--forum-spacing-md);
}

.forum-discussion-checkbox {
  display: flex;
  align-items: center;
  padding-top: var(--forum-spacing-md);
  flex-shrink: 0;
}

.forum-mod-checkbox {
  width: 1rem;
  height: 1rem;
  background: var(--forum-bg-secondary);
  border: 2px solid var(--forum-border-secondary);
  border-radius: var(--forum-radius-sm);
  cursor: pointer;
  appearance: none;
  transition: all 0.2s;
  position: relative;
}

.forum-mod-checkbox:checked {
  background: rgba(99, 102, 241, 0.8);
  border-color: rgb(99, 102, 241);
}

.forum-mod-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

.forum-mod-checkbox:hover {
  border-color: rgba(99, 102, 241, 0.5);
}

/* Active moderation button styling */
.forum-btn.active {
  background: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
  color: rgb(199, 210, 254);
}

.forum-btn.active:hover {
  background: rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.6);
}

/* Pulse animation for pending indicator */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile responsive adjustments for moderation */
@media (max-width: 768px) {
  .forum-moderation-filters {
    overflow-x: auto;
    padding-bottom: var(--forum-spacing-xs);
  }
  
  .forum-mod-filter-btn {
    flex-shrink: 0;
    font-size: 0.7rem;
    padding: 0.125rem var(--forum-spacing-xs);
  }
  
  .forum-batch-moderation {
    flex-direction: column;
    align-items: stretch;
    gap: var(--forum-spacing-md);
  }
  
  .forum-batch-actions {
    justify-content: center;
  }
  
  .forum-discussion-checkbox {
    padding-top: var(--forum-spacing-sm);
  }
  
  .forum-discussion-moderation {
    margin-left: 0;
    padding-left: 0;
    padding-top: var(--forum-spacing-sm);
    justify-content: flex-end;
  }
  
  .forum-pending-indicator {
    font-size: 0.7rem;
    padding: 0.125rem var(--forum-spacing-xs);
  }
  
  .forum-status-badge {
    font-size: 0.7rem;
    padding: 0.125rem var(--forum-spacing-xs);
  }
  
  .forum-mod-btn {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .forum-mod-btn svg {
    width: 0.75rem;
    height: 0.75rem;
  }
}

@media (max-width: 480px) {
  .forum-discussion-title-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--forum-spacing-sm);
  }
  
  .forum-moderation-controls,
  .forum-comment-moderation,
  .forum-nested-comment-moderation {
    gap: var(--forum-spacing-xs);
  }
  
  .forum-batch-btn {
    font-size: 0.7rem;
    padding: 0.125rem var(--forum-spacing-sm);
    min-height: 1.75rem;
  }
  
  .forum-mod-filter-btn {
    font-size: 0.65rem;
    padding: 0.125rem 0.25rem;
  }
}

@media (max-width: 360px) {
  .forum-discussion-item {
    flex-direction: column;
    gap: var(--forum-spacing-sm);
  }
  
  .forum-discussion-checkbox {
    align-self: flex-end;
    padding-top: 0;
  }
  
  .forum-discussion-moderation {
    align-self: flex-end;
    padding-top: 0;
  }
  
  .forum-batch-moderation {
    padding: var(--forum-spacing-md);
  }
  
  .forum-batch-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--forum-spacing-sm);
  }
  
  .forum-batch-btn {
    justify-content: center;
  }
}