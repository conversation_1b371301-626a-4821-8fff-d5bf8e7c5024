# CriticalPixel Typography System

## 🔤 Font Stack

CriticalPixel uses a **dual-font system** combining modern sans-serif with monospace fonts to create a professional gaming aesthetic.

### Primary Font Stack
```css
/* Geist Sans - Primary UI font */
--font-primary: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

/* Geist Mono - Code and technical elements */
--font-mono: 'Geist Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Courier New', monospace;

/* Fallback system fonts */
--font-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
```

### Font Loading
```tsx
// Next.js font configuration
import { <PERSON>ei<PERSON>, <PERSON>eist_Mon<PERSON> } from 'next/font/google';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});
```

## 📏 Typography Scale

### Heading Hierarchy
```css
/* H1 - Page titles */
.text-h1 {
  font-size: 2.5rem;      /* 40px */
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

/* H2 - Section titles */
.text-h2 {
  font-size: 2rem;        /* 32px */
  line-height: 1.25;
  font-weight: 600;
  letter-spacing: -0.02em;
  color: var(--text-primary);
}

/* H3 - Subsection titles */
.text-h3 {
  font-size: 1.5rem;      /* 24px */
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: -0.015em;
  color: var(--text-primary);
}

/* H4 - Component titles */
.text-h4 {
  font-size: 1.25rem;     /* 20px */
  line-height: 1.35;
  font-weight: 600;
  color: var(--text-primary);
}

/* H5 - Small headings */
.text-h5 {
  font-size: 1.125rem;    /* 18px */
  line-height: 1.4;
  font-weight: 600;
  color: var(--text-primary);
}

/* H6 - Micro headings */
.text-h6 {
  font-size: 1rem;        /* 16px */
  line-height: 1.4;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
```

### Body Text Styles
```css
/* Large body text */
.text-lg {
  font-size: 1.125rem;    /* 18px */
  line-height: 1.6;
  color: var(--text-primary);
}

/* Standard body text */
.text-base {
  font-size: 1rem;        /* 16px */
  line-height: 1.6;
  color: var(--text-primary);
}

/* Small body text */
.text-sm {
  font-size: 0.875rem;    /* 14px */
  line-height: 1.5;
  color: var(--text-secondary);
}

/* Extra small text */
.text-xs {
  font-size: 0.75rem;     /* 12px */
  line-height: 1.4;
  color: var(--text-muted);
}
```

### Display Text
```css
/* Hero/display text */
.text-display {
  font-size: 3.5rem;      /* 56px */
  line-height: 1.1;
  font-weight: 800;
  letter-spacing: -0.03em;
  color: var(--text-primary);
}

@media (min-width: 768px) {
  .text-display {
    font-size: 4.5rem;    /* 72px */
  }
}
```

## 🎨 Text Color System

### Color Hierarchy
```css
/* Primary text - main content */
.text-primary {
  color: #f1f5f9;         /* slate-100 */
}

/* Secondary text - supporting content */
.text-secondary {
  color: #94a3b8;         /* slate-400 */
}

/* Muted text - less important content */
.text-muted {
  color: #64748b;         /* slate-500 */
}

/* Accent text - brand/interactive elements */
.text-accent {
  color: #8b5cf6;         /* purple-500 */
}

/* Success text */
.text-success {
  color: #10b981;         /* emerald-500 */
}

/* Warning text */
.text-warning {
  color: #f59e0b;         /* amber-500 */
}

/* Error text */
.text-error {
  color: #ef4444;         /* red-500 */
}
```

## 🔧 Specialized Typography

### Code & Technical Text
```css
/* Inline code */
.text-code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background: rgba(71, 85, 105, 0.3);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  color: var(--text-primary);
}

/* Code blocks */
.text-code-block {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
  background: rgba(15, 23, 42, 0.8);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(71, 85, 105, 0.3);
  color: var(--text-primary);
  overflow-x: auto;
}

/* Terminal/command text */
.text-terminal {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  background: #000;
  color: #00ff00;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid rgba(0, 255, 0, 0.3);
}
```

### Gaming-Specific Typography
```css
/* Game titles */
.text-game-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
}

/* Platform labels */
.text-platform {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--text-secondary);
}

/* Score displays */
.text-score {
  font-size: 2rem;
  font-weight: 800;
  line-height: 1;
  color: var(--text-primary);
}

.text-score-large {
  font-size: 3rem;
  font-weight: 900;
  line-height: 1;
  color: var(--text-primary);
}

/* Review metadata */
.text-meta {
  font-family: var(--font-mono);
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
```

### Form Typography
```css
/* Form labels */
.text-label {
  font-family: var(--font-mono);
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
}

/* Form descriptions */
.text-description {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-muted);
}

/* Error messages */
.text-error-message {
  font-size: 0.875rem;
  color: var(--text-error);
  font-weight: 500;
}

/* Success messages */
.text-success-message {
  font-size: 0.875rem;
  color: var(--text-success);
  font-weight: 500;
}
```

## 📱 Responsive Typography

### Mobile Adjustments
```css
/* Responsive heading scales */
@media (max-width: 640px) {
  .text-h1 {
    font-size: 2rem;      /* 32px */
  }
  
  .text-h2 {
    font-size: 1.75rem;   /* 28px */
  }
  
  .text-h3 {
    font-size: 1.375rem;  /* 22px */
  }
  
  .text-display {
    font-size: 2.5rem;    /* 40px */
  }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  .text-display {
    font-size: 3rem;      /* 48px */
  }
}
```

### Reading Experience
```css
/* Optimized reading content */
.prose {
  font-size: 1.125rem;    /* 18px */
  line-height: 1.7;
  color: var(--text-primary);
  max-width: 65ch;        /* Optimal reading width */
}

.prose p {
  margin-bottom: 1.5rem;
}

.prose h2 {
  margin-top: 2.5rem;
  margin-bottom: 1rem;
}

.prose h3 {
  margin-top: 2rem;
  margin-bottom: 0.75rem;
}

.prose ul, .prose ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}
```

## 🎯 Typography Utilities

### Text Alignment
```css
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }
```

### Text Decoration
```css
.text-underline { text-decoration: underline; }
.text-no-underline { text-decoration: none; }
.text-line-through { text-decoration: line-through; }
```

### Text Transform
```css
.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }
.text-normal-case { text-transform: none; }
```

### Font Weights
```css
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }
```

### Letter Spacing
```css
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }
```

## 📋 Typography Best Practices

### Content Hierarchy
1. **Page Title (H1)**: Single per page, largest size
2. **Section Headers (H2)**: Major content sections
3. **Subsection Headers (H3)**: Content subdivisions
4. **Component Titles (H4-H6)**: UI component labels

### Accessibility Guidelines
- **Contrast**: Minimum 4.5:1 ratio for normal text
- **Line Height**: 1.5+ for body text, 1.2+ for headings
- **Font Size**: Minimum 16px for body text
- **Line Length**: 45-75 characters for optimal reading

### Performance Considerations
- **Font Loading**: Use `font-display: swap` for web fonts
- **Subset Loading**: Load only required character sets
- **Preload**: Critical fonts should be preloaded
- **Fallbacks**: Always provide system font fallbacks

---

*This typography system ensures consistent, accessible, and performant text rendering across all CriticalPixel interfaces while maintaining the gaming-focused aesthetic.*
