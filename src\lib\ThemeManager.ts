// src/lib/ThemeManager.ts
// Simplified theme manager for component-focused theming

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
}

export interface ThemeOption {
  id: string;
  name: string;
  description: string;
  colors: ThemeColors;
}

// 6 carefully selected themes that complement the site's dark aesthetic
export const profileThemes: ThemeOption[] = [
  {
    id: 'default',
    name: 'Cosmic',
    description: 'Purple tones matching the site aesthetic',
    colors: {
      primary: '#8b5cf6',
      secondary: '#7c3aed',
      accent: '#a78bfa'
    }
  },
  {
    id: 'ocean',
    name: 'Ocean',
    description: 'Calming blue tones',
    colors: {
      primary: '#3b82f6',
      secondary: '#2563eb',
      accent: '#60a5fa'
    }
  },
  {
    id: 'forest',
    name: 'Forest',
    description: 'Natural green hues',
    colors: {
      primary: '#10b981',
      secondary: '#059669',
      accent: '#34d399'
    }
  },
  {
    id: 'crimson',
    name: 'Crimson',
    description: 'Bold red accents',
    colors: {
      primary: '#ef4444',
      secondary: '#dc2626',
      accent: '#f87171'
    }
  },
  {
    id: 'silver',
    name: 'Silver',
    description: 'Subtle gray tones',
    colors: {
      primary: '#6b7280',
      secondary: '#4b5563',
      accent: '#9ca3af'
    }
  },
  {
    id: 'amber',
    name: 'Amber',
    description: 'Warm golden hues',
    colors: {
      primary: '#f59e0b',
      secondary: '#d97706',
      accent: '#fbbf24'
    }
  }
];

// Default theme
export const defaultTheme = profileThemes[0];

export class ThemeManager {
  // Get a theme by ID
  static getTheme(themeId: string): ThemeOption {
    return profileThemes.find(theme => theme.id === themeId) || defaultTheme;
  }

  // Get all available themes
  static getThemes(): ThemeOption[] {
    return profileThemes;
  }

  // Apply theme CSS variables to an element
  static applyTheme(element: HTMLElement, themeId: string): void {
    const theme = this.getTheme(themeId);
    const variables = this.getThemeVariables(theme);
    
    // Apply CSS variables
    Object.entries(variables).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
    
    // Add theme class
    element.classList.add(`theme-${themeId}`);
  }

  // Get CSS variables for a theme
  static getThemeVariables(theme: ThemeOption): Record<string, string> {
    const hexToRgb = (hex: string): string => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result 
        ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` 
        : '0, 0, 0';
    };
    
    return {
      '--theme-primary': theme.colors.primary,
      '--theme-secondary': theme.colors.secondary,
      '--theme-accent': theme.colors.accent,
      '--theme-primary-rgb': hexToRgb(theme.colors.primary),
      '--theme-secondary-rgb': hexToRgb(theme.colors.secondary),
      '--theme-accent-rgb': hexToRgb(theme.colors.accent),
    };
  }

  // Apply theme to a component (adds themed class)
  static applyComponentTheme(element: HTMLElement, themeId: string): void {
    // Remove any existing theme classes
    profileThemes.forEach(theme => {
      element.classList.remove(`theme-${theme.id}`);
    });
    
    // Add new theme class
    element.classList.add(`theme-${themeId}`);
    element.classList.add('themed');
  }

  // Get theme color value
  static getThemeColor(themeId: string, colorType: 'primary' | 'secondary' | 'accent'): string {
    const theme = this.getTheme(themeId);
    return theme.colors[colorType];
  }

  // Check if a theme ID is valid
  static isValidTheme(themeId: string): boolean {
    return profileThemes.some(theme => theme.id === themeId);
  }

  // Get theme by name (case insensitive)
  static getThemeByName(name: string): ThemeOption | undefined {
    return profileThemes.find(theme => 
      theme.name.toLowerCase() === name.toLowerCase()
    );
  }

  // Apply subtle theme accents to specific elements
  static applyThemeAccents(container: HTMLElement, themeId: string): void {
    const theme = this.getTheme(themeId);
    
    // Apply to avatars
    const avatars = container.querySelectorAll('.avatar-ring');
    avatars.forEach(avatar => {
      (avatar as HTMLElement).style.borderColor = theme.colors.primary;
    });
    
    // Apply to buttons
    const buttons = container.querySelectorAll('.theme-button');
    buttons.forEach(button => {
      (button as HTMLElement).style.setProperty('--button-accent', theme.colors.primary);
    });
    
    // Apply to cards
    const cards = container.querySelectorAll('.theme-card');
    cards.forEach(card => {
      (card as HTMLElement).style.setProperty('--card-glow', theme.colors.primary);
    });
  }

  // Generate inline styles for dynamic theme application
  static getInlineThemeStyles(themeId: string): string {
    const theme = this.getTheme(themeId);
    return `
      --theme-primary: ${theme.colors.primary};
      --theme-secondary: ${theme.colors.secondary};
      --theme-accent: ${theme.colors.accent};
    `;
  }

  // Create a theme preview element
  static createThemePreview(themeId: string): HTMLElement {
    const theme = this.getTheme(themeId);
    const preview = document.createElement('div');
    preview.className = 'theme-preview';
    preview.innerHTML = `
      <div class="theme-preview-colors">
        <div class="theme-preview-color" style="background-color: ${theme.colors.primary}"></div>
        <div class="theme-preview-color" style="background-color: ${theme.colors.secondary}"></div>
        <div class="theme-preview-color" style="background-color: ${theme.colors.accent}"></div>
      </div>
      <div class="theme-preview-name">${theme.name}</div>
    `;
    return preview;
  }
}

// Utility function to get theme-aware classes
export const getThemeClasses = (baseClass: string, themeId: string): string => {
  return `${baseClass} theme-${themeId} themed`;
};

// Utility function for theme-aware inline styles
export const getThemeStyle = (themeId: string): React.CSSProperties => {
  const theme = ThemeManager.getTheme(themeId);
  return {
    '--theme-primary': theme.colors.primary,
    '--theme-secondary': theme.colors.secondary,
    '--theme-accent': theme.colors.accent,
  } as React.CSSProperties;
};

export default ThemeManager;