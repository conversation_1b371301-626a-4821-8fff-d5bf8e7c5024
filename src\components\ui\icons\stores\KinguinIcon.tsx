import React from 'react';

interface IconProps {
  className?: string;
  [key: string]: any;
}

const KinguinIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 32 32"
    fill="currentColor"
    {...props}
  >
    <title>Kinguin</title>
    {/* Container background to match other store icons */}
    <rect width="32" height="32" fill="#FFD700" rx="6"/>
    {/* Kinguin logo centered */}
    <image 
      href="/icons/stores/kinguin.svg" 
      x="4" 
      y="4" 
      width="24" 
      height="24"
    />
  </svg>
);

export default KinguinIcon;