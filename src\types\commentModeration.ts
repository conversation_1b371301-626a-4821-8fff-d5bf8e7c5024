// Comment Moderation Types
// Date: 21/06/2025
// Task: Comment Moderation Dashboard Implementation

export interface CommentModerationData {
  id: string;
  review_id: string;
  review_title: string;
  review_slug: string;
  game_name: string;
  review_created_at: string;
  author_id: string;
  author_name: string;
  author_username: string;
  content: string;
  created_at: string;
  is_approved: boolean;
  is_pinned: boolean;
  is_deleted: boolean;
  flag_count: number;
  upvotes: number;
  downvotes: number;
  moderation_notes?: string;
  moderated_by?: string;
  moderated_at?: string;
  reports?: CommentReport[];
  author?: {
    id: string;
    username: string;
    display_name: string;
    avatar_url?: string;
  };
  review?: {
    id: string;
    title: string;
    slug: string;
    author_id: string;
    game_name: string;
    created_at: string;
  };
}

export interface CommentReport {
  id: string;
  reporter_id: string;
  reporter_name: string;
  reason: string;
  description?: string;
  created_at: string;
  status: 'pending' | 'resolved' | 'dismissed';
  reporter?: {
    username: string;
    display_name: string;
  };
}

export interface CommentModerationSettings {
  review_id: string;
  auto_approve: boolean;
  require_approval: boolean;
  allow_anonymous: boolean;
  max_comment_length: number;
  rate_limit_minutes: number;
  blocked_users: string[];
  blocked_words: string[];
}

export interface CommentAnalytics {
  total_comments: number;
  pending_comments: number;
  approved_comments: number;
  flagged_comments: number;
  comments_this_week: number;
  comments_this_month: number;
  top_commenters: Array<{
    user_id: string;
    username: string;
    comment_count: number;
  }>;
  engagement_rate: number;
}

export interface CommentModerationAction {
  commentId: string;
  action: 'approve' | 'reject' | 'delete' | 'pin' | 'unpin';
  reason?: string;
  notes?: string;
}

export interface BlockUserAction {
  reviewId: string;
  userIdToBlock: string;
  reason?: string;
}
