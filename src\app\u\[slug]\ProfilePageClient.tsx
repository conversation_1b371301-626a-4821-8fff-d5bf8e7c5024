'use client';

import React, { useState, useTransition, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GamerCard from '@/components/userprofile/GamerCard';
import EditProfileModal from '@/components/userprofile/EditProfileModal';
import type { UserProfile, UnifiedUserProfile } from '@/lib/types/profile';
import type { ExtendedUserProfile } from '@/lib/types';
import { convertToExtendedProfile, convertFromExtendedProfile } from '@/utils/profile-conversion';
import { ThemeManager } from '@/lib/ThemeManager';
import { useAuthContext } from '@/contexts/auth-context';
import { updateUserProfile, getUserProfileByUsername } from '@/app/u/actions';
import { saveUserGamingProfiles, saveUserSocialMediaProfiles, getCompleteUserProfile } from '@/app/u/actions-profiles';
import { getUserYouTubeSettings, getReviewDisplaySettings } from '@/app/u/dashboard/actions';
import { useRouter } from 'next/navigation';
import EnhancedContentDisplay from '@/components/userprofile/EnhancedContentDisplay';
import YouTubeModule from '@/components/userprofile/YouTubeModule';
import UserContentTabs from '@/components/userprofile/UserContentTabs';
import ContentBanner from '@/components/layout/ContentBanner';

import { useUserContent } from '@/hooks/useUserContent';
import { getUserReviews } from '@/app/u/actions-content';
import SimpleFeaturedReview from '@/components/userprofile/SimpleFeaturedReview';
import FeaturedBannersCarousel from '@/components/userprofile/FeaturedBannersCarousel';
import { ReviewsSection, FilterControls } from '@/components/userprofile/ReviewCards';
import { ReviewsSectionList } from '@/components/userprofile/ReviewCardsList';
import ReviewsSectionExcerpt from '@/components/userprofile/ReviewsSectionExcerpt';
import { Gamepad2, Trophy, ExternalLink, Star, Eye, MessageSquare, Heart, Calendar, Search, Grid, List, Filter, ChevronLeft, ChevronRight, Settings, X } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import {
  ProfileHeaderSkeleton,
  YouTubeModuleSkeleton
} from '@/components/ui/skeleton-improved';
import { ProgressiveAvatar, ProgressiveBanner } from '@/components/ui/progressive-image';
import { useTouchGestures } from '@/hooks/use-touch-gestures';

// Social Media Icons
import TwitterIcon from '@/components/ui/icons/socials/TwitterIcon';
import FacebookIcon from '@/components/ui/icons/socials/FacebookIcon';
import InstagramIcon from '@/components/ui/icons/socials/InstagramIcon';
import YouTubeIcon from '@/components/ui/icons/socials/YouTubeIcon';
import TwitchIcon from '@/components/ui/icons/socials/TwitchIcon';
import GithubIcon from '@/components/ui/icons/socials/GithubIcon';
import RedditIcon from '@/components/ui/icons/socials/RedditIcon';
import TikTokIcon from '@/components/ui/icons/socials/TikTokIcon';
import DiscordIcon from '@/components/ui/icons/socials/DiscordIcon';

interface ProfilePageClientProps {
  profileData: UserProfile;
  currentUserId: string | null;
  initialDisplaySettings?: {
    viewMode: 'grid' | 'list' | 'excerpt';
    itemsPerPage: number;
    showFilters: boolean;
    defaultSort: 'date' | 'rating' | 'title';
  };
  initialYouTubeChannelUrl?: string;
}

// Simple format date function for user profiles

// Format date function
const formatDate = (date: Date | string | null | undefined) => {
  if (!date) return 'Unknown';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch {
    return 'Unknown';
  }
};

// Social Media Icon Component - Memoized for performance
const SocialMediaIcon = React.memo(({ platform, url, theme }: { platform: string; url: string; theme: any }) => {
  const iconClass = "h-5 w-5 transition-colors";
  const iconColor = theme.colors.accent;
  
  const getIcon = () => {
    switch (platform.toLowerCase()) {
      case 'twitter':
      case 'x':
        return <TwitterIcon className={iconClass} style={{ color: iconColor }} />;
      case 'facebook':
        return <FacebookIcon className={iconClass} style={{ color: iconColor }} />;
      case 'instagram':
        return <InstagramIcon className={iconClass} style={{ color: iconColor }} />;
      case 'youtube':
        return <YouTubeIcon className={iconClass} style={{ color: iconColor }} />;
      case 'twitch':
        return <TwitchIcon className={iconClass} style={{ color: iconColor }} />;
      case 'github':
        return <GithubIcon className={iconClass} style={{ color: iconColor }} />;
      case 'reddit':
        return <RedditIcon className={iconClass} style={{ color: iconColor }} />;
      case 'tiktok':
        return <TikTokIcon className={iconClass} style={{ color: iconColor }} />;
      case 'discord':
        return <DiscordIcon className={iconClass} style={{ color: iconColor }} />;
      case 'linkedin':
        return <ExternalLink className={iconClass} style={{ color: iconColor }} />;
      default:
        return <ExternalLink className={iconClass} style={{ color: iconColor }} />;
    }
  };

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="p-2 rounded-xl transition-all duration-200 hover:scale-110 hover:shadow-lg"
      style={{ 
        backgroundColor: `${theme.colors.primary}20`,
        borderColor: `${theme.colors.accent}30`
      }}
      title={`Visit ${platform} profile`}
    >
      {getIcon()}
    </a>
  );
});

// Social Media Buttons Component - Memoized for performance
const SocialMediaButtons = React.memo(({ socialProfiles, theme }: { socialProfiles: any[]; theme: any }) => {
  if (!socialProfiles || socialProfiles.length === 0) return null;

  return (
    <div className="flex items-center gap-2 flex-wrap justify-center sm:justify-end">
      {socialProfiles.map((profile, index) => (
        <SocialMediaIcon
          key={`${profile.platform}-${index}`}
          platform={profile.platform}
          url={profile.url}
          theme={theme}
        />
      ))}
    </div>
  );
});

// Profile Header Component with dynamic text color - Memoized for performance
const ProfileHeader = React.memo(({ 
  profileData, 
  formatDate,
  onEditProfile
}: { 
  profileData: UserProfile;
  formatDate: (date: Date | string | null | undefined) => string;
  onEditProfile?: () => void;
}) => {
  const theme = ThemeManager.getTheme(profileData.theme || 'muted-dark');
  const headerRef = useRef<HTMLDivElement>(null);
  
  // Add touch gestures for mobile interaction
  const touchRef = useTouchGestures({
    onDoubleTap: onEditProfile,
    enabled: !!onEditProfile
  });
  
  return (
    <div 
      ref={(el) => {
        // @ts-ignore - Need to assign to ref for touch gestures
        if (headerRef.current !== el) headerRef.current = el;
        // @ts-ignore - Need to assign to ref for touch gestures
        if (touchRef.current !== el) touchRef.current = el;
      }}
      className="relative overflow-hidden rounded-xl border border-gray-800 bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur touch-manipulation"
      style={{
        background: `linear-gradient(135deg, ${theme.colors.primary}30, ${theme.colors.secondary}20)`,
        borderColor: `${theme.colors.accent}30`
      }}
    >
      {/* Banner - max height 450px */}
      <div 
        className="w-full overflow-hidden relative h-32 sm:h-48 md:h-64 lg:h-80 xl:h-96" 
        style={{ maxHeight: '450px' }}
      >
        <ProgressiveBanner
          src={profileData.banner_url || undefined}
          alt={`${profileData.display_name || profileData.username}'s banner`}
          fallbackGradient={`linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary})`}
          className="w-full h-full"
          priority={true}
        />
      </div>

      {/* User Info Container - Below Banner */}
      <div className="relative p-4 sm:p-6 bg-gray-900/80 backdrop-blur-sm">
        <div className="flex flex-col lg:flex-row items-center lg:items-center gap-4 lg:gap-6">
          {/* Left Section: Avatar + Profile Info */}
          <div className="flex flex-col sm:flex-row items-center sm:items-center gap-4 sm:gap-6 flex-1">
            {/* Avatar with fun mouseover effect */}
            <div 
              className="relative h-24 w-24 rounded-full border-4 border-gray-900 bg-gray-800 overflow-hidden group cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-violet-500/20 -mt-12 mx-auto sm:mx-0 flex-shrink-0"
              style={{ borderColor: theme.colors.accent }}
            >
              <div className="absolute inset-0 bg-gradient-to-tr from-violet-500/30 to-blue-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
              
              <ProgressiveAvatar
                src={profileData.avatar_url || undefined}
                alt={`${profileData.display_name || profileData.username}'s avatar`}
                fallbackText={(profileData.display_name || profileData.username)}
                className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                style={{ backgroundColor: !profileData.avatar_url ? theme.colors.primary : undefined }}
                priority={true}
              />
              
              <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20"></div>
            </div>

            {/* Profile Info - Vertically centered with avatar */}
            <div className="flex-1 min-w-0 text-center sm:text-left flex flex-col justify-center">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                <h1 
                  className="text-2xl sm:text-3xl font-bold truncate font-mono text-white drop-shadow-[0_2px_3px_rgba(0,0,0,0.8)]"
                >
                  <span style={{ color: theme.colors.accent }} className="mr-1">&lt;</span>
                  {profileData.display_name || profileData.username}
                  <span style={{ color: theme.colors.accent }} className="ml-1">/&gt;</span>
                </h1>
                {profileData.is_online && (
                  <div className="flex items-center gap-1 justify-center sm:justify-start">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-xs text-green-400 font-medium font-mono">Online</span>
                  </div>
                )}
              </div>

              <div className="flex flex-wrap justify-center sm:justify-start items-center gap-4 text-sm text-gray-400 font-mono">
                <div className="flex items-center gap-1.5">
                  <span>Joined {formatDate(profileData.created_at)}</span>
                </div>
                {profileData.last_seen && (
                  <div className="flex items-center gap-1.5">
                    <span>Last seen {formatDate(profileData.last_seen)}</span>
                  </div>
                )}
                {profileData.favorite_consoles && profileData.favorite_consoles.length > 0 && (
                  <div className="flex items-center gap-1.5">
                    <Gamepad2 className="h-3.5 w-3.5" style={{ color: theme.colors.accent }} />
                    <span>{profileData.favorite_consoles.length} platforms</span>
                  </div>
                )}
                {profileData.preferred_genres && profileData.preferred_genres.length > 0 && (
                  <div className="flex items-center gap-1.5">
                    <Trophy className="h-3.5 w-3.5" style={{ color: theme.colors.accent }} />
                    <span>{profileData.preferred_genres.length} genres</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Section: Social Media Buttons */}
          <div className="flex items-center justify-center lg:justify-end mt-4 lg:mt-0">
            <SocialMediaButtons 
              socialProfiles={profileData.social_profiles || []} 
              theme={theme} 
            />
          </div>
        </div>
      </div>
    </div>
  );
});




export default function ProfilePageClient({ profileData, currentUserId, initialDisplaySettings, initialYouTubeChannelUrl }: ProfilePageClientProps) {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [localProfileData, setLocalProfileData] = useState<UserProfile>(profileData);
  const [youtubeChannelUrl, setYoutubeChannelUrl] = useState<string | undefined>(initialYouTubeChannelUrl);
  // Removed featuredReview state - using SimpleFeaturedReview component now
  const [isPending, startTransition] = useTransition();
  const [isLoadingContent, setIsLoadingContent] = useState(true);
  const [setupMessageHidden, setSetupMessageHidden] = useState(false);
  // Filter toggle state
  const [showFilters, setShowFilters] = useState(initialDisplaySettings?.showFilters || false);
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPlatform, setFilterPlatform] = useState<string>('all');
  const [reviewDisplayMode, setReviewDisplayMode] = useState<'grid' | 'list' | 'excerpt'>(initialDisplaySettings?.viewMode || 'grid');
  const [reviewItemsPerPage, setReviewItemsPerPage] = useState(initialDisplaySettings?.itemsPerPage || 6);
  const [reviewDefaultSort, setReviewDefaultSort] = useState<'date' | 'rating' | 'title'>(initialDisplaySettings?.defaultSort || 'date');
  const { user: authUser } = useAuthContext();
  const router = useRouter();
  
  // Format date function - Memoized for performance
  const formatDateMemo = useCallback((date: Date | string | null | undefined) => {
    if (!date) return 'Unknown';
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  }, []);
  
  // Determine if current user is viewing their own profile - Memoized for performance
  // Use both server-side currentUserId and client-side authUser for reliability
  const isOwnProfile = useMemo(() => Boolean(
    (currentUserId && currentUserId === profileData.id) ||
    (authUser && authUser.id === profileData.id)
  ), [currentUserId, profileData.id, authUser]);

  // Check localStorage for setup message visibility on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hidden = localStorage.getItem(`setup-message-hidden-${profileData.id}`);
      setSetupMessageHidden(hidden === 'true');
    }
  }, [profileData.id]);

  // Function to permanently hide setup message
  const hideSetupMessage = useCallback(() => {
    setSetupMessageHidden(true);
    if (typeof window !== 'undefined') {
      localStorage.setItem(`setup-message-hidden-${profileData.id}`, 'true');
    }
  }, [profileData.id]);

  // No longer needed - settings are now fetched on server and passed as props
  useEffect(() => {
    // Just set loading to false since we already have initial data
    setIsLoadingContent(false);
  }, []);

  // Use YouTube data hook only when channel URL exists - Memoized for performance
  const {
    youtubeData,
    isLoading: youtubeLoading,
    error: youtubeError,
    data: userContentData
  } = useUserContent(profileData.id, currentUserId || undefined, youtubeChannelUrl);

  // Get available platforms for filter options (from where user actually played)
  const availablePlatforms = useMemo(() => {
    if (!userContentData?.reviews) return [];
    const platforms = new Set<string>();
    userContentData.reviews.forEach(review => {
      // Use type assertion to access played_on property if it exists
      const playedOn = (review as any).played_on;
      if (playedOn) platforms.add(playedOn);
    });
    return Array.from(platforms).sort();
  }, [userContentData?.reviews]);

  // Convert UserProfile to a format compatible with EditProfileModal - Memoized for performance
  // Make sure we preserve the id and other required fields
  const extendedProfileData = useMemo(() => ({
    ...convertToExtendedProfile(localProfileData),
    // Ensure these fields are explicitly available for EditProfileModal
    id: localProfileData.id,
    username: localProfileData.username,
    slug: localProfileData.slug || localProfileData.username,
    slug_lower: localProfileData.slug_lower || localProfileData.username.toLowerCase(),
    display_name: localProfileData.display_name || localProfileData.username
  }), [localProfileData]);

  const handleEditProfile = useCallback(() => {
    setIsEditModalOpen(true);
  }, []);

  const handleSaveProfile = async (updatedProfile: Partial<UnifiedUserProfile>) => {
    if (!isOwnProfile || !localProfileData.id) {
      console.error('Unauthorized: Cannot update profile');
      return;
    }

    try {
      startTransition(async () => {
        // Convert UnifiedUserProfile to ProfileUpdateInput format for server action
        const profileUpdateData: any = {};
        
        // Only include defined values, converting null to undefined to match ProfileUpdateInput
        if (updatedProfile.displayName !== undefined || updatedProfile.display_name !== undefined) {
          profileUpdateData.display_name = updatedProfile.displayName || updatedProfile.display_name;
        }
        if (updatedProfile.bio !== undefined) {
          profileUpdateData.bio = updatedProfile.bio || undefined;
        }
        if (updatedProfile.website !== undefined) {
          profileUpdateData.website = updatedProfile.website || undefined;
        }
        if (updatedProfile.location !== undefined) {
          profileUpdateData.location = updatedProfile.location || undefined;
        }
        if (updatedProfile.avatarUrl !== undefined || updatedProfile.avatar_url !== undefined) {
          // Handle empty string as null for image removal
          const avatarValue = updatedProfile.avatarUrl ?? updatedProfile.avatar_url;
          console.log('🖼️ Avatar processing:', { 
            original: updatedProfile.avatarUrl || updatedProfile.avatar_url,
            avatarValue,
            isEmptyString: avatarValue === '',
            finalValue: avatarValue === '' ? null : avatarValue
          });
          profileUpdateData.avatar_url = avatarValue === '' ? null : avatarValue;
        }
        if (updatedProfile.bannerUrl !== undefined || updatedProfile.banner_url !== undefined) {
          // Handle empty string as null for image removal
          const bannerValue = updatedProfile.bannerUrl ?? updatedProfile.banner_url;
          console.log('🎌 Banner processing:', { 
            original: updatedProfile.bannerUrl || updatedProfile.banner_url,
            bannerValue,
            isEmptyString: bannerValue === '',
            finalValue: bannerValue === '' ? null : bannerValue
          });
          profileUpdateData.banner_url = bannerValue === '' ? null : bannerValue;
        }
        if (updatedProfile.preferredGenres !== undefined || updatedProfile.preferred_genres !== undefined) {
          profileUpdateData.preferred_genres = updatedProfile.preferredGenres || updatedProfile.preferred_genres;
        }
        if (updatedProfile.favoriteConsoles !== undefined || updatedProfile.favorite_consoles !== undefined) {
          profileUpdateData.favorite_consoles = updatedProfile.favoriteConsoles || updatedProfile.favorite_consoles;
        }
        if (updatedProfile.theme !== undefined) {
          profileUpdateData.theme = updatedProfile.theme;
        }
        if (updatedProfile.customColors !== undefined || updatedProfile.custom_colors !== undefined) {
          profileUpdateData.custom_colors = updatedProfile.customColors || updatedProfile.custom_colors;
        }
        // Enhanced privacy settings handling with proper key format
        if (updatedProfile.privacySettings !== undefined) {
          // Retrieve existing privacy_settings to merge with new values
          const existingSettings = localProfileData.privacy_settings || {};
          
          // Extract existing settings with type safety
          const existingProfileVisibility = typeof existingSettings === 'object' && existingSettings !== null ? 
            (existingSettings as any).profile_visibility : 'public';
          const existingShowOnlineStatus = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).show_online_status) : true;
          const existingShowGamingProfiles = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).show_gaming_profiles) : true;
          const existingShowSocialProfiles = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).show_social_profiles) : true;
          const existingShowAchievements = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).show_achievements) : true;
          const existingAllowContact = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).allow_contact) : true;
          const existingAllowFriendRequests = typeof existingSettings === 'object' && existingSettings !== null ? 
            Boolean((existingSettings as any).allow_friend_requests) : true;
          
          // Get new values from user input or fall back to existing values
          const showOnlineStatus = updatedProfile.privacySettings.showOnlineStatus ?? existingShowOnlineStatus;
          const showGamingProfiles = updatedProfile.privacySettings.showGamingProfiles ?? existingShowGamingProfiles;
          // Handle potential missing properties with safe fallbacks
          const showSocialProfiles = (updatedProfile.privacySettings as any)?.showSocialProfiles ?? existingShowSocialProfiles;
          const allowContact = (updatedProfile.privacySettings as any)?.allowContact ?? existingAllowContact;
          
          profileUpdateData.privacy_settings = {
            profile_visibility: existingProfileVisibility,
            show_online_status: showOnlineStatus,
            show_gaming_profiles: showGamingProfiles,
            show_social_profiles: showSocialProfiles,
            show_achievements: updatedProfile.privacySettings.showAchievements ?? existingShowAchievements,
            allow_contact: allowContact,
            allow_friend_requests: updatedProfile.privacySettings.allowFriendRequests ?? existingAllowFriendRequests
          };
          
          console.log('Formatted privacy_settings for database:', profileUpdateData.privacy_settings);
        }
        
        // First, update the main profile data
        const result = await updateUserProfile(localProfileData.id, profileUpdateData);
        
        if (!result.success) {
          console.error('Failed to update profile:', result.error);
          alert(`Failed to update profile: ${result.error}`);
          return; // Stop here if main profile update fails
        }
        
        // Save gaming profiles to separate table
        if (updatedProfile.gamingProfiles !== undefined) {
          console.log('Attempting to save gaming profiles:', updatedProfile.gamingProfiles);
          console.log('User ID for gaming profiles:', localProfileData.id);
          
          const gamingResult = await saveUserGamingProfiles(localProfileData.id, updatedProfile.gamingProfiles);
          if (!gamingResult.success) {
            console.error('Failed to save gaming profiles:', gamingResult.error);
            alert(`Failed to save gaming profiles: ${gamingResult.error}`);
            // Continue with other operations despite this error
          } else {
            console.log('Gaming profiles saved successfully');
          }
        }
        
        // Save social media profiles to separate table
        if (updatedProfile.socialMedia !== undefined) {
          console.log('Attempting to save social media profiles:', updatedProfile.socialMedia);
          console.log('User ID for social media profiles:', localProfileData.id);
          
          const socialResult = await saveUserSocialMediaProfiles(localProfileData.id, updatedProfile.socialMedia);
          if (!socialResult.success) {
            console.error('Failed to save social media profiles:', socialResult.error);
            alert(`Failed to save social media profiles: ${socialResult.error}`);
            // Continue with other operations despite this error
          } else {
            console.log('Social media profiles saved successfully');
          }
        }

        if (result.data) {
          // After saving all data, fetch the complete updated profile with related tables
          try {
            // Get complete profile data including gaming and social profiles
            const completeProfData = await getUserProfileByUsername(profileData.username);
            if (completeProfData) {
              // Update local state with new complete data including related tables
              setLocalProfileData(completeProfData);
            } else {
              // Fallback to using just the main profile data if complete fetch fails
              setLocalProfileData(result.data);
            }
          } catch (error) {
            console.error('Error fetching complete profile after update:', error);
            // Use the available data from the main profile update
            setLocalProfileData(result.data);
          }
          
          setIsEditModalOpen(false);
          // Refresh the page to ensure all components reflect the changes
          router.refresh();
        }
      });
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('An unexpected error occurred while saving the profile.');
    }
  };

  // Featured review logic moved to SimpleFeaturedReview component



  const media = [
    {
      id: '1',
      type: 'image',
      url: '/api/placeholder/800/600',
      title: 'Night City Vista',
      game_name: 'Cyberpunk 2077',
      description: 'Uma das vistas mais incríveis do jogo'
    },
    {
      id: '2',
      type: 'video',
      url: '/api/placeholder/video/gameplay.mp4',
      title: 'Gameplay épico',
      game_name: 'Baldur\'s Gate 3',
      description: 'Combate estratégico em ação'
    },
    {
      id: '3',
      type: 'image',
      url: '/api/placeholder/800/600',
      title: 'Character Build',
      game_name: 'Elden Ring',
      description: 'Minha build favorita'
    }
  ];

  // Obter o tema do perfil - Memoized for performance
  const theme = useMemo(() => 
    ThemeManager.getTheme(localProfileData.theme || 'muted-dark'), 
    [localProfileData.theme]
  );

  return (
    <div className="mx-auto px-4 w-full xl:w-4/5">
      {/* Profile Header with Banner and User Info */}
      <header role="banner" aria-label="User profile information">
        {localProfileData ? (
          <ProfileHeader
            profileData={profileData}
            formatDate={formatDateMemo}
            onEditProfile={isOwnProfile ? handleEditProfile : undefined}
          />
        ) : (
          <ProfileHeaderSkeleton theme={theme} />
        )}
      </header>

      {/* GamerCard with proper authentication */}
      <section className="mt-6" aria-label="Gamer profile details">
        <GamerCard
          profileData={profileData}
          isOwnProfile={isOwnProfile}
          viewerId={currentUserId || authUser?.id}
          onEdit={handleEditProfile}
        />
      </section>

      {/* Setup Message - Only show for own profile and if not hidden */}
      {isOwnProfile && !setupMessageHidden && !youtubeChannelUrl && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="mt-6"
        >
          <Card className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-xl bg-purple-500/20">
                    <Settings className="h-5 w-5 text-purple-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-2">
                      Configure seus módulos de conteúdo
                    </h3>
                    <p className="text-gray-300 mb-4">
                      Personalize seu perfil adicionando módulos como YouTube, galeria de imagens, 
                      banners patrocinados e muito mais. Acesse o dashboard para começar.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button
                        onClick={() => router.push('/u/dashboard')}
                        className="bg-purple-600 hover:bg-purple-700 text-white"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Ir para Dashboard
                      </Button>
                      <Button
                        variant="outline"
                        onClick={hideSetupMessage}
                        className="border-gray-600 text-gray-300 hover:bg-gray-800"
                      >
                        Não mostrar novamente
                      </Button>
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={hideSetupMessage}
                  className="text-gray-400 hover:text-white p-1"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Conteúdo Destacado - Seção Premium */}
      <main className="mt-8" role="main" aria-label="User content">
        <div className="w-full xl:w-3/4 mx-auto">
          <div className="space-y-8">
            {/* Featured Banners Carousel Component */}
            <FeaturedBannersCarousel userId={profileData.id} />

            {/* Reviews and Content Tabs Side-by-Side Layout */}
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Left Column: Reviews (75%) */}
              <div className="flex-1 lg:w-[75%] space-y-6">
                {/* Filter Controls */}
                <FilterControls
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  filterPlatform={filterPlatform}
                  setFilterPlatform={setFilterPlatform}
                  availablePlatforms={availablePlatforms}
                  isVisible={showFilters}
                />

                {reviewDisplayMode === 'grid' ? (
                  <ReviewsSection
                    profileData={localProfileData}
                    currentUserId={currentUserId}
                    isOwnProfile={isOwnProfile}
                    theme={theme}
                    searchTerm={searchTerm}
                    filterPlatform={filterPlatform}
                    itemsPerPage={reviewItemsPerPage}
                    defaultSort={reviewDefaultSort}
                  />
                ) : reviewDisplayMode === 'list' ? (
                  <ReviewsSectionList
                    profileData={localProfileData}
                    currentUserId={currentUserId}
                    isOwnProfile={isOwnProfile}
                    theme={theme}
                    searchTerm={searchTerm}
                    filterPlatform={filterPlatform}
                    itemsPerPage={reviewItemsPerPage}
                    defaultSort={reviewDefaultSort}
                  />
                ) : (
                  <ReviewsSectionExcerpt
                    profileData={localProfileData}
                    currentUserId={currentUserId}
                    isOwnProfile={isOwnProfile}
                    theme={theme}
                    searchTerm={searchTerm}
                    filterPlatform={filterPlatform}
                    itemsPerPage={reviewItemsPerPage}
                    defaultSort={reviewDefaultSort}
                  />
                )}
              </div>

              {/* Right Column: User Content Tabs (25%) - Sticky */}
              <div className="lg:w-[25%]">
                <div className="sticky top-20">
                  <UserContentTabs
                    userId={localProfileData.id}
                    currentUserId={currentUserId ? currentUserId : undefined}
                    isOwnProfile={isOwnProfile}
                    theme={theme}
                    showFilters={showFilters}
                    setShowFilters={setShowFilters}
                  />
                </div>
              </div>
            </div>

            {/* Content Banner - Positioned between reviews/sidenav and YouTube module */}
            <div className="w-full">
              <ContentBanner userId={localProfileData.id} />
            </div>

            {/* YouTube Module (if enabled and configured) */}
            {youtubeChannelUrl && (
              <div className="w-full max-w-full overflow-hidden">
                {youtubeLoading || isLoadingContent ? (
                  <YouTubeModuleSkeleton theme={theme} />
                ) : youtubeData ? (
                  <YouTubeModule
                    youtubeData={youtubeData}
                    isLoading={youtubeLoading}
                    error={youtubeError}
                    theme={theme?.name?.toLowerCase() as any}
                    className="w-full max-w-full overflow-hidden"
                  />
                ) : null}
              </div>
            )}

          </div>
        </div>
      </main>



      {/* Edit Profile Modal */}
      <AnimatePresence>
        {isEditModalOpen && (
          <EditProfileModal
            profile={extendedProfileData}
            open={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSave={handleSaveProfile}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
