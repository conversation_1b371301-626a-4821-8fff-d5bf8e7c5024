'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { GameData, GameStats } from '@/lib/services/gameService';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Star, Users, Eye, Heart } from 'lucide-react';
import { ScoreCircle } from '@/components/ui/ScoreCircle';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';

interface GameHeroProps {
  game: GameData;
  stats: GameStats;
}

export default function GameHero({ game, stats }: GameHeroProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string | null>(null);
  
  // Use the background brightness hook for dynamic text colors
  const { brightness } = useBackgroundBrightness(
    game.supabase_cover_url || game.cover_url || ''
  );

  useEffect(() => {
    // Set background image for hero section
    if (game.supabase_cover_url || game.cover_url) {
      setBackgroundImageUrl(game.supabase_cover_url || game.cover_url);
    }
  }, [game.supabase_cover_url, game.cover_url]);

  const formatReleaseDate = (dateStr: string | null) => {
    if (!dateStr) return 'TBA';
    try {
      return new Date(dateStr).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const formatPlayTime = (minutes: number | null) => {
    if (!minutes) return null;
    const hours = Math.round(minutes / 60 * 10) / 10;
    return `${hours}h`;
  };

  return (
    <div className="relative min-h-[700px] overflow-hidden">
      {/* Background Image with Enhanced Overlay */}
      {backgroundImageUrl && (
        <div className="absolute inset-0 z-0">
          <Image
            src={backgroundImageUrl}
            alt={`${game.name} background`}
            fill
            className="object-cover object-center scale-110 blur-sm"
            onLoad={() => setImageLoaded(true)}
            priority
          />
          {/* Enhanced gradient overlays for better sophistication */}
          <div className="absolute inset-0 bg-gradient-to-r from-slate-950/90 via-slate-900/70 to-slate-950/90" />
          <div className="absolute inset-0 bg-gradient-to-t from-slate-950 via-slate-900/50 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-slate-950/80" />

          {/* Subtle pattern overlay for texture */}
          <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]" />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
          {/* Game Cover - Enhanced Design */}
          <div className="lg:col-span-1">
            <div className="relative group">
              {/* Glow effect behind cover */}
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500" />

              <Card className="relative overflow-hidden bg-slate-900/60 backdrop-blur-sm border border-slate-700/50 rounded-xl shadow-2xl">
                <div className="aspect-[3/4] relative">
                  {game.supabase_cover_url || game.cover_url ? (
                    <Image
                      src={game.supabase_cover_url || game.cover_url}
                      alt={`${game.name} cover`}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-105"
                      priority
                    />
                  ) : (
                    <div className="w-full h-full bg-slate-800 flex items-center justify-center">
                      <div className="text-slate-400 text-center">
                        <div className="text-4xl mb-2">🎮</div>
                        <div>No Cover Available</div>
                      </div>
                    </div>
                  )}

                  {/* Subtle overlay for better text readability */}
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/20 via-transparent to-transparent" />
                </div>
              </Card>
            </div>
          </div>

          {/* Game Information - Enhanced Typography */}
          <div className="lg:col-span-2 space-y-8">
            {/* Title and Rating */}
            <div className="space-y-6">
              <h1
                className="text-4xl lg:text-6xl font-bold text-white leading-tight"
                style={{ textShadow: '2px 3px 0px rgba(0,0,0,1)' }}
              >
                {game.name}
              </h1>
              
              {/* Rating and Stats */}
              <div className="flex flex-wrap items-center gap-6">
                {stats.average_rating && (
                  <div className="flex items-center space-x-3">
                    <ScoreCircle 
                      score={stats.average_rating} 
                      size="lg"
                      showLabel={false}
                    />
                    <div>
                      <div className="text-lg font-bold text-white">
                        {stats.average_rating.toFixed(1)}/100
                      </div>
                      <div className="text-sm text-gray-400">
                        {stats.total_reviews} review{stats.total_reviews !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                )}

                {game.aggregated_rating && (
                  <div className="flex items-center space-x-2">
                    <Star className="w-5 h-5 text-yellow-400 fill-current" />
                    <span className="text-white font-medium">
                      {game.aggregated_rating.toFixed(1)}
                    </span>
                    <span className="text-gray-400 text-sm">
                      ({game.aggregated_rating_count} IGDB ratings)
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Game Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Release Date */}
              <div className="flex items-center space-x-3 text-gray-300">
                <Calendar className="w-5 h-5 text-blue-400" />
                <div>
                  <div className="text-sm text-gray-400">Release Date</div>
                  <div className="font-medium">{formatReleaseDate(game.release_date)}</div>
                </div>
              </div>

              {/* Playtime */}
              {(game.time_to_beat_normally || game.time_to_beat_completely) && (
                <div className="flex items-center space-x-3 text-gray-300">
                  <Clock className="w-5 h-5 text-green-400" />
                  <div>
                    <div className="text-sm text-gray-400">Playtime</div>
                    <div className="font-medium">
                      {game.time_to_beat_normally && formatPlayTime(game.time_to_beat_normally)} 
                      {game.time_to_beat_normally && game.time_to_beat_completely && ' / '}
                      {game.time_to_beat_completely && formatPlayTime(game.time_to_beat_completely)}
                      {game.time_to_beat_completely && ' (100%)'}
                    </div>
                  </div>
                </div>
              )}

              {/* Community Stats */}
              <div className="flex items-center space-x-3 text-gray-300">
                <Users className="w-5 h-5 text-purple-400" />
                <div>
                  <div className="text-sm text-gray-400">Community</div>
                  <div className="font-medium">{stats.total_reviews} reviews</div>
                </div>
              </div>

              {/* Views */}
              <div className="flex items-center space-x-3 text-gray-300">
                <Eye className="w-5 h-5 text-gray-400" />
                <div>
                  <div className="text-sm text-gray-400">Total Views</div>
                  <div className="font-medium">{stats.total_views.toLocaleString()}</div>
                </div>
              </div>
            </div>

            {/* Platforms */}
            {game.platforms && game.platforms.length > 0 && (
              <div>
                <div className="text-sm text-gray-400 mb-2">Available on</div>
                <div className="flex flex-wrap gap-2">
                  {game.platforms.map((platform, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary"
                      className="bg-blue-600/20 text-blue-300 border-blue-500/30"
                    >
                      {platform}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Genres */}
            {game.genres && game.genres.length > 0 && (
              <div>
                <div className="text-sm text-gray-400 mb-2">Genres</div>
                <div className="flex flex-wrap gap-2">
                  {game.genres.map((genre, index) => (
                    <Badge 
                      key={index} 
                      variant="outline"
                      className="border-gray-600 text-gray-300 hover:bg-gray-700/50"
                    >
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Developers and Publishers */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {game.developers && game.developers.length > 0 && (
                <div>
                  <div className="text-sm text-gray-400 mb-2">Developer{game.developers.length > 1 ? 's' : ''}</div>
                  <div className="space-y-1">
                    {game.developers.map((dev, index) => (
                      <div key={index} className="text-white font-medium">{dev}</div>
                    ))}
                  </div>
                </div>
              )}

              {game.publishers && game.publishers.length > 0 && (
                <div>
                  <div className="text-sm text-gray-400 mb-2">Publisher{game.publishers.length > 1 ? 's' : ''}</div>
                  <div className="space-y-1">
                    {game.publishers.map((pub, index) => (
                      <div key={index} className="text-white font-medium">{pub}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}