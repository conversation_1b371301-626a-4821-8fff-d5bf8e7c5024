'use client';

import React, { useState, useEffect } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Image, AlertCircle, Loader2 } from 'lucide-react';

interface ImageInsertModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (src: string, altText: string, caption?: string) => void;
}

export default function ImageInsertModal({
  isOpen,
  onClose,
  onInsert,
}: ImageInsertModalProps) {
  const [src, setSrc] = useState('');
  const [altText, setAltText] = useState('');
  const [caption, setCaption] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [previewLoaded, setPreviewLoaded] = useState(false);
  const [previewError, setPreviewError] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSrc('');
      setAltText('');
      setCaption('');
      setError('');
      setPreviewLoaded(false);
      setPreviewError(false);
    }
  }, [isOpen]);

  // URL validation
  const validateImageUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') &&
             /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(urlObj.pathname);
    } catch {
      return false;
    }
  };

  // Auto-add protocol if missing
  const normalizeUrl = (urlString: string): string => {
    if (!urlString) return '';
    
    // If it already has a protocol, return as is
    if (urlString.match(/^https?:\/\//)) {
      return urlString;
    }
    
    // Add https:// by default
    return `https://${urlString}`;
  };

  const handleInsert = () => {
    if (!src.trim()) {
      setError('Image URL is required');
      return;
    }

    if (!altText.trim()) {
      setError('Alt text is required for accessibility');
      return;
    }

    const normalizedSrc = normalizeUrl(src.trim());
    
    if (!validateImageUrl(normalizedSrc)) {
      setError('Please enter a valid image URL (jpg, jpeg, png, gif, webp, svg)');
      return;
    }

    setIsLoading(true);
    
    // Simulate processing delay for better UX
    setTimeout(() => {
      onInsert(normalizedSrc, altText.trim(), caption.trim() || undefined);
      setIsLoading(false);
      onClose();
    }, 300);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleInsert();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const handleSrcChange = (value: string) => {
    setSrc(value);
    setError('');
    setPreviewLoaded(false);
    setPreviewError(false);
  };

  const handleImageLoad = () => {
    setPreviewLoaded(true);
    setPreviewError(false);
  };

  const handleImageError = () => {
    setPreviewLoaded(false);
    setPreviewError(true);
  };

  const normalizedSrc = src ? normalizeUrl(src) : '';
  const showPreview = normalizedSrc && validateImageUrl(normalizedSrc);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-900/95 border border-slate-700/50 backdrop-blur-md max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-slate-200 font-mono flex items-center gap-2">
            <Image className="h-4 w-4 text-violet-400" />
            <span className="text-violet-400">//</span>
            Insert Image
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Image URL Input */}
          <div className="space-y-2">
            <Label htmlFor="src" className="text-slate-300 font-mono text-sm">
              Image URL *
            </Label>
            <Input
              id="src"
              type="url"
              placeholder="https://example.com/image.jpg"
              value={src}
              onChange={(e) => handleSrcChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono"
              autoFocus
            />
            <p className="text-xs text-slate-500 font-mono">
              Supported formats: JPG, JPEG, PNG, GIF, WebP, SVG
            </p>
          </div>

          {/* Alt Text Input */}
          <div className="space-y-2">
            <Label htmlFor="altText" className="text-slate-300 font-mono text-sm">
              Alt Text * <span className="text-slate-500">(for accessibility)</span>
            </Label>
            <Input
              id="altText"
              type="text"
              placeholder="Describe the image content"
              value={altText}
              onChange={(e) => setAltText(e.target.value)}
              onKeyDown={handleKeyDown}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono"
            />
          </div>

          {/* Caption Input */}
          <div className="space-y-2">
            <Label htmlFor="caption" className="text-slate-300 font-mono text-sm">
              Caption <span className="text-slate-500">(optional)</span>
            </Label>
            <Textarea
              id="caption"
              placeholder="Optional caption text"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              className="bg-slate-800/50 border-slate-600/50 text-slate-200 placeholder:text-slate-500 font-mono resize-none"
              rows={2}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 text-red-400 text-sm font-mono bg-red-900/20 border border-red-700/30 rounded-lg p-3">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              {error}
            </div>
          )}

          {/* Image Preview */}
          {showPreview && (
            <div className="bg-slate-800/30 border border-slate-700/30 rounded-lg p-4">
              <p className="text-xs text-slate-500 font-mono mb-3">Preview:</p>
              <div className="relative">
                {!previewLoaded && !previewError && (
                  <div className="flex items-center justify-center h-32 bg-slate-700/30 rounded-lg">
                    <Loader2 className="h-6 w-6 text-slate-400 animate-spin" />
                  </div>
                )}
                
                {previewError && (
                  <div className="flex items-center justify-center h-32 bg-red-900/20 border border-red-700/30 rounded-lg">
                    <div className="text-center">
                      <AlertCircle className="h-6 w-6 text-red-400 mx-auto mb-2" />
                      <p className="text-red-400 text-sm font-mono">Failed to load image</p>
                    </div>
                  </div>
                )}
                
                <img
                  src={normalizedSrc}
                  alt={altText || 'Preview'}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                  className={`max-w-full max-h-64 object-contain rounded-lg ${
                    previewLoaded ? 'block' : 'hidden'
                  }`}
                />
                
                {caption && previewLoaded && (
                  <p className="text-sm text-slate-400 font-mono mt-2 italic text-center">
                    {caption}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-slate-800/50 border-slate-600/50 text-slate-300 hover:bg-slate-700/50 font-mono"
          >
            Cancel
          </Button>
          <Button
            onClick={handleInsert}
            disabled={!src.trim() || !altText.trim() || isLoading}
            className="bg-violet-600 hover:bg-violet-700 text-white font-mono"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                Inserting...
              </div>
            ) : (
              <>
                <Image className="h-4 w-4 mr-2" />
                Insert Image
              </>
            )}
          </Button>
        </DialogFooter>

        <div className="text-xs text-slate-500 font-mono text-center border-t border-slate-700/30 pt-3">
          Tip: Press Ctrl+Enter to insert, Esc to cancel
        </div>
      </DialogContent>
    </Dialog>
  );
}
