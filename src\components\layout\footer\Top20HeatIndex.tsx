'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, ShoppingCart, Flame, Zap, Star } from 'lucide-react';

// TypeScript interfaces
interface TrendingGame {
  id: string;
  title: string;
  score: string;
  coverUrl: string;
  slug: string;
  dealPrice?: string;
  originalPrice?: string;
  dealUrl?: string;
  platform?: string;
}

const Top20HeatIndex: React.FC = () => {
  // State
  const [hoveredGame, setHoveredGame] = useState<string | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isClient, setIsClient] = useState(false);
  // Initialize gamesPerSlide to a default value (e.g., 5)
  const [gamesPerSlide, setGamesPerSlide] = useState(5);
  
  // Sample images
  const gameCovers = [
    "https://cdn2.steamgriddb.com/thumb/a4095daac959ffde59002a36e557cb1e.jpg",
    "https://cdn2.steamgriddb.com/thumb/c2252b0b105881548dbfb8575d738580.jpg",
    "https://cdn2.steamgriddb.com/thumb/c6b76e75cf16c517c528cd49d542f1eb.jpg",
    "https://cdn2.steamgriddb.com/thumb/f2181dcc33a20714a50dcf3c13a79ccd.jpg",
    "https://cdn2.steamgriddb.com/thumb/430042511e0ec1662aa24e2d85947c31.jpg"
  ];

  // All games data
  const allGames: TrendingGame[] = [
    { id:"1", title:"Elden Ring DLC", score:"98", coverUrl:gameCovers[1], slug:"elden-ring-dlc", dealPrice:"$39.99", dealUrl:"https://store.example.com/elden-ring-dlc", platform:"Epic Games Store" },
    { id:"2", title:"Baldur's Gate 3", score:"96", coverUrl:gameCovers[3], slug:"baldurs-gate-3", dealPrice:"$59.99", dealUrl:"https://store.example.com/bg3", platform:"Steam" },
    { id:"3", title:"BOTW 2", score:"95", coverUrl:gameCovers[3], slug:"botw-2", dealPrice:"$54.99", dealUrl:"https://store.example.com/botw-2", platform:"Nintendo" },
    { id:"4", title:"Resident Evil 4", score:"94", coverUrl:gameCovers[2], slug:"re4-remake", dealPrice:"$39.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/re4", platform:"Steam" },
    { id:"5", title:"Ghost of Tsushima", score:"94", coverUrl:gameCovers[2], slug:"ghost-of-tsushima", dealPrice:"$19.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/ghost-tsushima", platform:"PlayStation" },
    { id:"6", title:"The Last of Us Part I", score:"93", coverUrl:gameCovers[3], slug:"last-of-us-1", dealPrice:"$39.99", originalPrice:"$69.99", dealUrl:"https://store.example.com/tlou1", platform:"PlayStation Store" },
    { id:"7", title:"The Witcher 3", score:"93", coverUrl:gameCovers[1], slug:"witcher-3", dealPrice:"$19.99", originalPrice:"$39.99", dealUrl:"https://store.example.com/witcher-3", platform:"Steam" },
    { id:"8", title:"Forza Horizon 5", score:"92", coverUrl:gameCovers[1], slug:"forza-horizon-5", dealPrice:"$34.99", dealUrl:"https://store.example.com/fh5", platform:"Xbox" },
    { id:"9", title:"Monster Hunter Portable 3rd", score:"92", coverUrl:gameCovers[0], slug:"monster-hunter-portable-3rd", dealPrice:"$49.99", originalPrice:"$69.99", dealUrl:"https://store.example.com/mhp3rd", platform:"Steam" },
    { id:"10", title:"God of War", score:"91", coverUrl:gameCovers[2], slug:"god-of-war", dealPrice:"$34.99", dealUrl:"https://store.example.com/god-of-war", platform:"Steam" },
    { id:"11", title:"Cyberpunk Phantom Liberty", score:"91", coverUrl:gameCovers[2], slug:"phantom-liberty", dealPrice:"$29.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/phantom-liberty", platform:"Epic Games Store" },
    { id:"12", title:"Street Fighter 6", score:"91", coverUrl:gameCovers[0], slug:"sf6", dealPrice:"$44.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/sf6", platform:"Steam" },
    { id:"13", title:"Hades II", score:"90", coverUrl:gameCovers[3], slug:"hades-2", dealPrice:"$29.99", dealUrl:"https://store.example.com/hades-2", platform:"Epic Games Store" },
    { id:"14", title:"Sekiro: Shadows Die Twice", score:"90", coverUrl:gameCovers[3], slug:"sekiro", dealPrice:"$29.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/sekiro", platform:"Steam" },
    { id:"15", title:"Cyberpunk 2077", score:"89", coverUrl:gameCovers[4], slug:"cyberpunk-2077", dealPrice:"$29.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/cyberpunk", platform:"Steam" },
    { id:"16", title:"Dead Space Remake", score:"89", coverUrl:gameCovers[4], slug:"dead-space", dealPrice:"$34.99", dealUrl:"https://store.example.com/dead-space", platform:"Steam" },
    { id:"17", title:"Horizon Zero Dawn", score:"89", coverUrl:gameCovers[4], slug:"horizon-zero-dawn", dealPrice:"$14.99", originalPrice:"$49.99", dealUrl:"https://store.example.com/hzd", platform:"Steam" },
    { id:"18", title:"Final Fantasy XVI", score:"88", coverUrl:gameCovers[4], slug:"ff16", dealPrice:"$49.99", originalPrice:"$69.99", dealUrl:"https://store.example.com/ff16", platform:"PlayStation Store" },
    { id:"19", title:"Alan Wake 2", score:"88", coverUrl:gameCovers[4], slug:"alan-wake-2", dealPrice:"$49.99", dealUrl:"https://store.example.com/alan-wake-2", platform:"Epic Games Store" },
    { id:"20", title:"Mass Effect Legendary Ed.", score:"88", coverUrl:gameCovers[0], slug:"mass-effect-le", dealPrice:"$39.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/me-le", platform:"Steam" },
    { id:"21", title:"Metroid Dread", score:"88", coverUrl:gameCovers[4], slug:"metroid-dread", dealPrice:"$39.99", dealUrl:"https://store.example.com/metroid-dread", platform:"Nintendo" },
    { id:"22", title:"Spider-Man 2", score:"87", coverUrl:gameCovers[2], slug:"spider-man-2", dealPrice:"$59.99", originalPrice:"$79.99", dealUrl:"https://store.example.com/spider-man-2", platform:"PlayStation" },
    { id:"23", title:"Armored Core VI", score:"87", coverUrl:gameCovers[3], slug:"ac6", dealPrice:"$54.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/ac6", platform:"Steam" },
    { id:"24", title:"Diablo IV", score:"86", coverUrl:gameCovers[1], slug:"diablo-4", dealPrice:"$49.99", dealUrl:"https://store.example.com/diablo-4", platform:"Battle.net" },
    { id:"25", title:"Death Stranding", score:"86", coverUrl:gameCovers[1], slug:"death-stranding", dealPrice:"$24.99", dealUrl:"https://store.example.com/death-stranding", platform:"Steam" },
    { id:"26", title:"Hogwarts Legacy", score:"85", coverUrl:gameCovers[0], slug:"hogwarts-legacy", dealPrice:"$44.99", dealUrl:"https://store.example.com/hogwarts", platform:"Epic Games Store" },
    { id:"27", title:"Lies of P", score:"85", coverUrl:gameCovers[2], slug:"lies-of-p", dealPrice:"$39.99", dealUrl:"https://store.example.com/lies-of-p", platform:"Xbox Game Pass" },
    { id:"28", title:"Valorant", score:"85", coverUrl:gameCovers[0], slug:"valorant", dealPrice:"Free", dealUrl:"https://store.example.com/valorant", platform:"Riot Games" },
    { id:"29", title:"Mortal Kombat 1", score:"84", coverUrl:gameCovers[1], slug:"mk1", dealPrice:"$49.99", dealUrl:"https://store.example.com/mk1", platform:"PlayStation Store" },
    { id:"30", title:"Starfield", score:"83", coverUrl:gameCovers[0], slug:"starfield", dealPrice:"$55.99", originalPrice:"$69.99", dealUrl:"https://store.example.com/starfield", platform:"Xbox" },
    { id:"31", title:"Doom Eternal", score:"90", coverUrl:gameCovers[0], slug:"doom-eternal", dealPrice:"$14.99", originalPrice:"$39.99", dealUrl:"https://store.example.com/doom-eternal", platform:"Steam" },
    { id:"32", title:"Star Wars Jedi: Survivor", score:"83", coverUrl:gameCovers[1], slug:"jedi-survivor", dealPrice:"$34.99", originalPrice:"$59.99", dealUrl:"https://store.example.com/jedi-survivor", platform:"Epic Games Store" }
  ];

  // Responsive games per slide
  const getGamesPerSlide = () => {
    if (typeof window === 'undefined') return 5;
    if (window.innerWidth >= 1024) return 5;  // lg+: 5 per row * 1 row
    if (window.innerWidth >= 768) return 4;   // md: 2 per row * 2 rows
    return 2; // sm: 1 per row * 2 rows
  };

  useEffect(() => {
    setIsClient(true);
    // Update gamesPerSlide based on window.innerWidth only on the client
    const handleResize = () => {
      if (window.innerWidth >= 1024) setGamesPerSlide(5);
      else if (window.innerWidth >= 768) setGamesPerSlide(4);
      else setGamesPerSlide(2);
    };

    handleResize(); // Set initial value on the client

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const totalSlides = Math.ceil(allGames.length / gamesPerSlide);

  const nextSlide = () => setCurrentSlide((i) => (i + 1) % totalSlides);
  const prevSlide = () => setCurrentSlide((i) => (i - 1 + totalSlides) % totalSlides);

  const getCurrentGames = () => {
    const start = currentSlide * gamesPerSlide;
    return allGames.slice(start, start + gamesPerSlide);
  };

  const calculateDiscount = (deal: string, orig?: string) => {
    if (!orig || deal === "Free") return 0;
    const d = parseFloat(deal.replace('$',''));
    const o = parseFloat(orig.replace('$',''));
    return Math.round((1 - d / o) * 100);
  };

  const getScoreColor = (s: number) => {
    if (s >= 90) return 'from-emerald-400 to-cyan-400';
    if (s >= 80) return 'from-blue-400 to-indigo-400';
    if (s >= 70) return 'from-yellow-400 to-orange-400';
    return 'from-orange-400 to-red-400';
  };

  const handleBuyClick = (e: React.MouseEvent, url: string) => {
    e.preventDefault(); 
    e.stopPropagation();
    if (isClient) window.open(url, '_blank');
  };

  return (
    <div className="mb-8">
      <div className="border border-white/5 bg-gradient-to-br from-slate-900/50 to-slate-800/30 backdrop-blur-xl rounded-xl overflow-hidden">
        {/* Header */}
        <div className="border-b border-white/5 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-white/10 flex items-center justify-center group hover:scale-110 transition-transform duration-300">
                  <Flame size={18} className="text-orange-400 group-hover:text-orange-300 transition-colors duration-300"/>
                </div>
                <h2 className="text-xl font-bold text-white">
                  <span className="font-mono relative inline-block">
                    <span className="text-violet-400/60">&lt;</span>
                    <span className="mx-1">Heat Index</span>
                    <span className="text-violet-400/60">/&gt;</span>
                  </span>
                </h2>
              </div>
            </div>

            <div className="flex justify-center sm:justify-end space-x-3 mt-2 sm:mt-0">
              <button
                onClick={prevSlide}
                disabled={currentSlide === 0}
                className="p-2.5 w-14 sm:w-auto rounded-lg bg-slate-800/50 border border-white/5 hover:bg-slate-700/50 hover:border-white/10 transition duration-200 disabled:opacity-30 group hover:scale-105"
              >
                <ChevronLeft size={16} className="mx-auto text-slate-400 group-hover:text-white transition-colors"/>
              </button>
              <span className="px-3 py-2.5 text-sm text-slate-400 font-mono">
                {currentSlide + 1} / {totalSlides}
              </span>
              <button
                onClick={nextSlide}
                disabled={currentSlide === totalSlides - 1}
                className="p-2.5 w-14 sm:w-auto rounded-lg bg-slate-800/50 border border-white/5 hover:bg-slate-700/50 hover:border-white/10 transition duration-200 disabled:opacity-30 group hover:scale-105"
              >
                <ChevronRight size={16} className="mx-auto text-slate-400 group-hover:text-white transition-colors"/>
              </button>
            </div>
          </div>
        </div>

        {/* Games Grid */}
        <div className="p-4 sm:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {getCurrentGames().map((game, index) => {
              const score = parseInt(game.score, 10);
              const discount = calculateDiscount(game.dealPrice!, game.originalPrice);
              const ranking = currentSlide * gamesPerSlide + index + 1;

              return (
                <div
                  key={game.id}
                  onMouseEnter={() => setHoveredGame(game.id)}
                  onMouseLeave={() => setHoveredGame(null)}
                  className="group relative bg-slate-900/40 rounded-xl border border-white/5 overflow-hidden transition-all duration-300 hover:bg-slate-800/50 hover:border-white/10 hover:shadow-xl hover:shadow-violet-500/10 hover:scale-[1.02] w-full"
                >
                  <Link href={`/games/${game.slug}`} className="block">
                    {/* Cover Image with overlay info */}
                    <div className="relative aspect-[4/5] overflow-hidden">
                      <img 
                        src={game.coverUrl} 
                        alt={game.title} 
                        className="w-full h-full object-cover object-center scale-95 transition-transform duration-300 group-hover:scale-100 rounded-t-lg"
                      />
                      
                      {/* Strong gradient overlay for text readability */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/60 to-transparent"/>
                      
                      {/* Additional bottom gradient for extra readability */}
                      <div className="absolute bottom-0 left-0 right-0 h-2/3 bg-gradient-to-t from-black/95 via-black/70 to-transparent"/>
                      
                      {/* Ranking badge */}
                      <div className="absolute top-2 left-2 bg-black/80 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/20">
                        <span className="text-xs font-bold text-white">#{ranking}</span>
                      </div>

                      {/* Score badge */}
                      <div className="absolute top-2 right-2 bg-black/80 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/20">
                        <div className="flex items-center space-x-1">
                          <Star size={10} className="text-yellow-400 fill-current"/>
                          <span className="text-xs font-bold text-white">{score}</span>
                        </div>
                      </div>

                      {/* Discount badge */}
                      {discount > 0 && (
                        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-red-600 to-orange-600 rounded-lg px-2 py-1 shadow-lg">
                          <span className="text-xs font-bold text-white">-{discount}%</span>
                        </div>
                      )}

                      {/* Game Info Overlay - Bottom Section */}
                      <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4">
                        {/* Title with strong text shadow */}
                        <h4 className="text-sm sm:text-base font-bold text-white mb-1 sm:mb-2 line-clamp-2 leading-tight drop-shadow-[0_2px_8px_rgba(0,0,0,0.8)]">
                          {game.title}
                        </h4>
                        
                        {/* Platform */}
                        {game.platform && (
                          <div className="text-xs sm:text-sm text-slate-200 font-medium mb-2 sm:mb-3 drop-shadow-[0_1px_4px_rgba(0,0,0,0.8)]">
                            {game.platform}
                          </div>
                        )}

                        {/* Price section */}
                        <div className="flex items-baseline justify-between mb-2 sm:mb-3">
                          <div className="flex items-baseline space-x-2">
                            <span className="text-lg sm:text-xl font-bold text-white drop-shadow-[0_2px_6px_rgba(0,0,0,0.8)]">
                              {game.dealPrice}
                            </span>
                            {game.originalPrice && game.dealPrice !== "Free" && (
                              <span className="text-xs sm:text-sm text-slate-300 line-through drop-shadow-[0_1px_4px_rgba(0,0,0,0.8)]">
                                {game.originalPrice}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Buy button with enhanced styling */}
                        <button
                          onClick={(e) => handleBuyClick(e, game.dealUrl!)}
                          className="w-full flex items-center justify-center space-x-1.5 py-2 sm:py-2.5 bg-gradient-to-r from-violet-600 to-cyan-600 hover:from-violet-500 hover:to-cyan-500 text-white font-bold text-xs sm:text-sm rounded-lg border border-white/20 transition-all duration-200 hover:scale-[1.02] hover:shadow-lg hover:shadow-violet-500/25 backdrop-blur-sm"
                        >
                          <ShoppingCart size={12}/>
                          <span>BUY NOW</span>
                        </button>
                      </div>

                      {/* Hover overlay */}
                      {hoveredGame === game.id && (
                        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent animate-fadeIn"/>
                      )}
                    </div>
                  </Link>
                </div>
              );
            })}
          </div>
        </div>

        {/* Slide indicators */}
        <div className="flex justify-center pb-6 space-x-2">
          {Array.from({ length: totalSlides }).map((_, i) => (
            <button
              key={i}
              onClick={() => setCurrentSlide(i)}
              className={`h-1.5 rounded-full transition-all duration-300 hover:scale-110 ${
                i === currentSlide
                  ? 'w-8 bg-gradient-to-r from-violet-500 to-cyan-500 shadow-lg shadow-violet-500/50'
                  : 'w-1.5 bg-slate-600 hover:bg-slate-500'
              }`}
            />
          ))}
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          0% { opacity: 0; }
          100% { opacity: 1; }
        }
        .animate-fadeIn { animation: fadeIn 0.3s ease-out forwards; }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default Top20HeatIndex;