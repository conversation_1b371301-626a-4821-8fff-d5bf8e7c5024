'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { 
  getUserYouTubeData, 
  updateYouTubeChannel,
  fetchYouTubeChannelData 
} from '@/app/u/actions-content';

interface YouTubeModuleSettings {
  enabled: boolean;
  visibility: 'public' | 'friends' | 'private';
  maxVideos: number;
  showStats: boolean;
  showDescription: boolean;
  autoplay: boolean;
}

interface ReviewDisplaySettings {
  viewMode: 'grid' | 'list' | 'excerpt';
  itemsPerPage: number;
  showFilters: boolean;
  defaultSort: 'date' | 'rating' | 'title';
}



interface SaveYouTubeChannelResponse {
  success: boolean;
  data?: any;
  error?: string;
}

interface GetYouTubeSettingsResponse {
  success: boolean;
  data?: {
    channelUrl?: string;
    channelData?: any;
    settings: YouTubeModuleSettings;
  };
  error?: string;
}

interface SaveReviewDisplayResponse {
  success: boolean;
  data?: ReviewDisplaySettings;
  error?: string;
}

interface GetReviewDisplayResponse {
  success: boolean;
  data?: ReviewDisplaySettings;
  error?: string;
}

interface PrivacySettings {
  profile_visibility: 'public' | 'private';
  show_reviews: 'public' | 'private';
  show_activity: 'public' | 'private';
  show_favorites: 'public' | 'private';
  show_performance_data: 'public' | 'private';
  show_gaming_profiles: 'public' | 'private';
  allow_friend_requests: boolean;
  show_online_status: boolean;
}

interface GetPrivacySettingsResponse {
  success: boolean;
  data?: PrivacySettings;
  error?: string;
}

interface SavePrivacySettingsResponse {
  success: boolean;
  error?: string;
}

// Get Supabase client
async function getSupabaseClient() {
  const cookieStore = await cookies();
  return createServerClient(cookieStore);
}

// Get Supabase client with service role for public read operations
async function getSupabaseServiceClient() {
  const { createClient } = await import('@supabase/supabase-js');
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

/**
 * Save YouTube channel configuration for a user
 */
export async function saveYouTubeChannel(
  userId: string, 
  channelUrl: string, 
  settings: YouTubeModuleSettings
): Promise<SaveYouTubeChannelResponse> {
  try {
    if (!userId || !channelUrl) {
      return { success: false, error: 'User ID and channel URL are required' };
    }

    const supabase = await getSupabaseClient();

    // First, validate and fetch channel data
    const channelResult = await updateYouTubeChannel(userId, channelUrl);
    
    if (!channelResult.success) {
      return { 
        success: false, 
        error: channelResult.error || 'Failed to validate YouTube channel' 
      };
    }

    // Save or update user content preferences
    const { data: existingPrefs, error: fetchError } = await supabase
      .from('user_content_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user preferences:', fetchError);
      return { success: false, error: 'Failed to fetch user preferences' };
    }

    // Force maxVideos to always be 4
    const settingsWithFixed = {
      ...settings,
      maxVideos: 4
    };

    const preferencesData = {
      user_id: userId,
      youtube_module: settingsWithFixed,
      updated_at: new Date().toISOString()
    };

    if (existingPrefs) {
      // Update existing preferences
      const { error: updateError } = await supabase
        .from('user_content_preferences')
        .update(preferencesData)
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating user preferences:', updateError);
        return { success: false, error: 'Failed to update preferences' };
      }
    } else {
      // Create new preferences
      const { error: insertError } = await supabase
        .from('user_content_preferences')
        .insert({
          ...preferencesData,
          enabled_modules: ['reviews', 'surveys', 'activity', 'youtube'],
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error creating user preferences:', insertError);
        return { success: false, error: 'Failed to create preferences' };
      }
    }

    return { 
      success: true, 
      data: { 
        channelData: channelResult.data,
        settings 
      } 
    };
  } catch (error) {
    console.error('Error saving YouTube channel:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get YouTube settings for a user
 */
export async function getUserYouTubeSettings(userId: string): Promise<GetYouTubeSettingsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // Get user preferences
    const { data: preferences, error: prefsError } = await supabase
      .from('user_content_preferences')
      .select('youtube_module')
      .eq('user_id', userId)
      .single();

    if (prefsError && prefsError.code !== 'PGRST116') {
      console.error('Error fetching user preferences:', prefsError);
      return { success: false, error: 'Failed to fetch user preferences' };
    }

    // Get YouTube channel data
    const { data: youtubeData, error: youtubeError } = await supabase
      .from('user_youtube_data')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (youtubeError && youtubeError.code !== 'PGRST116') {
      console.error('Error fetching YouTube data:', youtubeError);
      return { success: false, error: 'Failed to fetch YouTube data' };
    }

    const defaultSettings: YouTubeModuleSettings = {
      enabled: false,
      visibility: 'public',
      maxVideos: 4,
      showStats: true,
      showDescription: true,
      autoplay: false
    };

    // Force maxVideos to always be 4 for any existing settings
    const currentSettings = preferences?.youtube_module || defaultSettings;
    const settingsWithFixed = {
      ...currentSettings,
      maxVideos: 4
    };

    return {
      success: true,
      data: {
        channelUrl: youtubeData?.channel_url,
        channelData: youtubeData,
        settings: settingsWithFixed
      }
    };
  } catch (error) {
    console.error('Error getting YouTube settings:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Update YouTube module settings only
 */
export async function updateYouTubeSettings(
  userId: string, 
  settings: YouTubeModuleSettings
): Promise<SaveYouTubeChannelResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // Force maxVideos to always be 4
    const settingsWithFixed = {
      ...settings,
      maxVideos: 4
    };

    const { error } = await supabase
      .from('user_content_preferences')
      .update({
        youtube_module: settingsWithFixed,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating YouTube settings:', error);
      return { success: false, error: 'Failed to update settings' };
    }

    return { success: true, data: { settings } };
  } catch (error) {
    console.error('Error updating YouTube settings:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Remove YouTube channel from user profile
 */
export async function removeYouTubeChannel(userId: string): Promise<SaveYouTubeChannelResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // Remove YouTube data
    const { error: youtubeError } = await supabase
      .from('user_youtube_data')
      .delete()
      .eq('user_id', userId);

    if (youtubeError) {
      console.error('Error removing YouTube data:', youtubeError);
      return { success: false, error: 'Failed to remove YouTube data' };
    }

    // Remove YouTube videos cache
    const { error: videosError } = await supabase
      .from('user_youtube_videos')
      .delete()
      .eq('user_id', userId);

    if (videosError) {
      console.error('Error removing YouTube videos:', videosError);
      // Don't fail the operation for this
    }

    // Update preferences to disable YouTube module
    const { error: prefsError } = await supabase
      .from('user_content_preferences')
      .update({
        youtube_module: {
          enabled: false,
          visibility: 'public',
          maxVideos: 4,
          showStats: true,
          showDescription: true,
          autoplay: false
        },
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (prefsError) {
      console.error('Error updating preferences:', prefsError);
      // Don't fail the operation for this
    }

    return { success: true };
  } catch (error) {
    console.error('Error removing YouTube channel:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Refresh YouTube channel data
 */
export async function refreshYouTubeData(userId: string): Promise<SaveYouTubeChannelResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // Get current channel URL
    const { data: youtubeData, error } = await supabase
      .from('user_youtube_data')
      .select('channel_url')
      .eq('user_id', userId)
      .single();

    if (error || !youtubeData?.channel_url) {
      return { success: false, error: 'No YouTube channel found to refresh' };
    }

    // Refresh the data
    const result = await updateYouTubeChannel(userId, youtubeData.channel_url);
    
    return result;
  } catch (error) {
    console.error('Error refreshing YouTube data:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Save review display preferences for a user
 */
export async function saveReviewDisplaySettings(
  userId: string, 
  settings: ReviewDisplaySettings
): Promise<SaveReviewDisplayResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // Get existing preferences
    const { data: existingPrefs, error: fetchError } = await supabase
      .from('user_content_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error fetching user preferences:', fetchError);
      return { success: false, error: 'Failed to fetch user preferences' };
    }

    const preferencesData = {
      user_id: userId,
      review_display: settings,
      updated_at: new Date().toISOString()
    };

    if (existingPrefs) {
      // Update existing preferences
      const { error: updateError } = await supabase
        .from('user_content_preferences')
        .update(preferencesData)
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating user preferences:', updateError);
        return { success: false, error: 'Failed to update preferences' };
      }
    } else {
      // Create new preferences
      const { error: insertError } = await supabase
        .from('user_content_preferences')
        .insert({
          ...preferencesData,
          enabled_modules: ['reviews', 'surveys', 'activity'],
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error creating user preferences:', insertError);
        return { success: false, error: 'Failed to create preferences' };
      }
    }

    return { success: true, data: settings };
  } catch (error) {
    console.error('Error saving review display settings:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get review display preferences for a user - supports anonymous access
 */
export async function getReviewDisplaySettings(userId: string): Promise<GetReviewDisplayResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    // Try with service client first for public read access
    const supabaseService = await getSupabaseServiceClient();

    // Get user preferences - use service role for public display settings
    const { data: preferences, error: prefsError } = await supabaseService
      .from('user_content_preferences')
      .select('review_display')
      .eq('user_id', userId)
      .single();

    if (prefsError && prefsError.code !== 'PGRST116') {
      console.error('Error fetching user preferences with service client:', prefsError);
      
      // Fallback to regular client
      try {
        const supabase = await getSupabaseClient();
        const { data: fallbackPrefs, error: fallbackError } = await supabase
          .from('user_content_preferences')
          .select('review_display')
          .eq('user_id', userId)
          .single();

        if (fallbackError && fallbackError.code !== 'PGRST116') {
          console.error('Error with fallback client:', fallbackError);
          // Return default settings for anonymous users
          const defaultSettings: ReviewDisplaySettings = {
            viewMode: 'grid',
            itemsPerPage: 6,
            showFilters: false,
            defaultSort: 'date'
          };
          
          return {
            success: true,
            data: defaultSettings
          };
        }

        const currentSettings = fallbackPrefs?.review_display || {
          viewMode: 'grid' as const,
          itemsPerPage: 6,
          showFilters: false,
          defaultSort: 'date' as const
        };

        return {
          success: true,
          data: currentSettings
        };
      } catch (fallbackErr) {
        console.error('Fallback client failed:', fallbackErr);
        // Return default settings
        const defaultSettings: ReviewDisplaySettings = {
          viewMode: 'grid',
          itemsPerPage: 6,
          showFilters: false,
          defaultSort: 'date'
        };
        
        return {
          success: true,
          data: defaultSettings
        };
      }
    }

    const defaultSettings: ReviewDisplaySettings = {
      viewMode: 'grid',
      itemsPerPage: 6,
      showFilters: false,
      defaultSort: 'date'
    };

    const currentSettings = preferences?.review_display || defaultSettings;

    return {
      success: true,
      data: currentSettings
    };
  } catch (error) {
    console.error('Error getting review display settings:', error);
    // Return default settings for anonymous users
    const defaultSettings: ReviewDisplaySettings = {
      viewMode: 'grid',
      itemsPerPage: 6,
      showFilters: false,
      defaultSort: 'date'
    };
    
    return {
      success: true,
      data: defaultSettings
    };
  }
}

/**
 * Get user privacy settings from the profiles table
 */
export async function getPrivacySettings(userId: string): Promise<GetPrivacySettingsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('privacy_settings')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching privacy settings:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Default privacy settings if none exist
    const defaultSettings: PrivacySettings = {
      profile_visibility: 'public',
      show_reviews: 'public',
      show_activity: 'public',
      show_favorites: 'public',
      show_performance_data: 'public',
      show_gaming_profiles: 'public',
      allow_friend_requests: true,
      show_online_status: true
    };

    // Merge existing settings with defaults
    const privacySettings = data?.privacy_settings 
      ? { ...defaultSettings, ...data.privacy_settings }
      : defaultSettings;

    return {
      success: true,
      data: privacySettings
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update user privacy settings in the profiles table
 */
export async function savePrivacySettings(
  userId: string, 
  settings: PrivacySettings
): Promise<SavePrivacySettingsResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();
    
    const { error } = await supabase
      .from('profiles')
      .update({ 
        privacy_settings: settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating privacy settings:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}


