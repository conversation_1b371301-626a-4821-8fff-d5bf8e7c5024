/* ═══════════════════════════════════════════════════════════════════════════
   🎮 REVIEW CARDS & COMPONENTS - CODE/GAMER THEME (TitleYourReview CSS Removed)
   ═══════════════════════════════════════════════════════════════════════════ */

/* Core Variables for Other Components */
:root {
  /* Color Palette */
  --review-bg-primary: #000000;
  --review-bg-secondary: rgba(24, 28, 42, 0.96);
  --review-bg-tertiary: rgba(36, 41, 61, 0.88);
  --review-gradient-primary: linear-gradient(135deg, rgba(45, 35, 66, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
  --review-gradient-active: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 100%);
  --review-gradient-button: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  --review-gradient-button-hover: linear-gradient(135deg, #a78bfa 0%, #22d3ee 100%);
  
  /* Text Colors */
  --review-text-primary: #ffffff;
  --review-text-secondary: rgba(255, 255, 255, 0.8);
  --review-text-muted: rgba(255, 255, 255, 0.757);
  --review-text-accent: #8b5cf6;
  --review-text-cyan: #06b6d4;
  --review-text-warning: #f59e0b;
  --review-text-success: #10b981;
  --review-text-error: #ef4444;
  
  /* Borders & Effects */
  --review-border-primary: rgba(2, 64, 97, 0.451);
  --review-border-secondary: rgba(255, 255, 255, 0.1);
  --review-border-accent: rgba(6, 182, 212, 0.3);
  --review-shadow-primary: 0 4px 12px rgba(0, 0, 0, 0.4);
  --review-shadow-glow: 0 0 20px rgba(31, 6, 91, 0.1);
  --review-blur: blur(12px);
  
  /* Spacing & Sizing */
  --review-spacing-xs: 0.25rem;
  --review-spacing-sm: 0.5rem;
  --review-spacing-md: 1rem;
  --review-spacing-lg: 1.5rem;
  --review-spacing-xl: 2rem;
  --review-spacing-2xl: 3rem;
  
  /* Transitions */
  --review-transition-fast: 0.15s ease-out;
  --review-transition-normal: 0.3s ease-out;
  --review-transition-slow: 0.5s ease-out;
  --review-easing-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);
  --review-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* IGDB Search Dropdown specific scrollbar */
.game-search-combobox .react-window {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.game-search-combobox .react-window::-webkit-scrollbar {
  width: 6px;
}

.game-search-combobox .react-window::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.4);
  border-radius: 3px;
}

.game-search-combobox .react-window::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.6);
}

/* ═══════════════════════════════════════════════════════════════════════════
 🎬 MEDIA & VISUALS COMPONENT - COMPLETE CSS
 ═══════════════════════════════════════════════════════════════════════════ */

/* Header Controls */
.media-header-controls {
display: flex;
justify-content: flex-end;
align-items: center;
margin-bottom: var(--review-spacing-lg);
gap: var(--review-spacing-md);
}

.media-toggle-message {
flex: 1;
padding: var(--review-spacing-sm) var(--review-spacing-md);
background: var(--review-bg-tertiary);
border: 1px solid var(--review-border-secondary);
border-radius: 0.375rem;
opacity: 0.9;
animation: media-message-fade 5s ease-out forwards;
}

.media-message-text {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.75rem;
color: var(--review-text-secondary);
}

.media-toggle-container {
position: relative;
}

.media-toggle-button {
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
padding: var(--review-spacing-sm) var(--review-spacing-md);
border-radius: 0.375rem;
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.75rem;
font-weight: 500;
transition: all var(--review-transition-normal);
cursor: pointer;
border: none;
}

.media-toggle-hide {
background: rgba(156, 163, 175, 0.1);
color: var(--review-text-muted);
border: 1px solid rgba(156, 163, 175, 0.2);
}

.media-toggle-hide:hover {
background: rgba(156, 163, 175, 0.15);
color: var(--review-text-secondary);
border-color: rgba(156, 163, 175, 0.3);
}

.media-toggle-show {
background: rgba(156, 163, 175, 0.1);
color: var(--review-text-muted);
border: 1px solid rgba(156, 163, 175, 0.2);
position: relative;
overflow: hidden;
}

.media-toggle-show:hover {
background: rgba(156, 163, 175, 0.15);
color: var(--review-text-secondary);
border-color: rgba(156, 163, 175, 0.3);
transform: translateY(-1px);
}

.media-premium-pulse {
position: absolute;
top: -2px;
left: -2px;
right: -2px;
bottom: -2px;
background: linear-gradient(45deg, var(--review-text-accent), var(--review-text-cyan));
border-radius: 0.5rem;
opacity: 0.3;
animation: media-pulse 2s ease-in-out infinite;
z-index: -1;
}

.media-toggle-icon {
width: 0.875rem;
height: 0.875rem;
}

.media-timer {
font-size: 0.625rem;
opacity: 0.8;
margin-left: var(--review-spacing-xs);
}

/* Tier Sections */
.media-tier-section {
background: var(--review-bg-tertiary);
border: 1px solid var(--review-border-secondary);
border-radius: 0.75rem;
overflow: hidden;
transition: all var(--review-transition-normal);
margin-bottom: var(--review-spacing-lg);
}

.media-tier-section:hover {
border-color: var(--review-border-primary);
}

.media-premium-section {
border-color: rgba(139, 92, 246, 0.2);
background: linear-gradient(135deg, rgba(139, 92, 246, 0.03), rgba(6, 182, 212, 0.02));
}

.media-premium-section:hover {
border-color: rgba(139, 92, 246, 0.4);
}

.media-creator-section {
border-color: rgba(6, 182, 212, 0.2);
background: linear-gradient(135deg, rgba(6, 182, 212, 0.03), rgba(139, 92, 246, 0.02));
}

.media-creator-section:hover {
border-color: rgba(6, 182, 212, 0.4);
}

/* Tier Headers */
.media-tier-header {
display: flex;
align-items: center;
justify-content: space-between;
padding: var(--review-spacing-lg);
}

.media-tier-clickable {
cursor: pointer;
transition: background var(--review-transition-normal);
}

.media-tier-clickable:hover {
background: rgba(255, 255, 255, 0.02);
}

.media-tier-title {
display: flex;
align-items: center;
gap: var(--review-spacing-md);
flex: 1;
}

.media-section-title {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-weight: 600;
font-size: 1rem;
color: var(--review-text-primary);
margin: 0;
}

.media-tier-features {
display: flex;
gap: var(--review-spacing-sm);
align-items: center;
}

.media-feature-badge {
font-size: 0.625rem;
padding: 0.125rem 0.375rem;
background: rgba(139, 92, 246, 0.1);
border: 1px solid rgba(139, 92, 246, 0.2);
border-radius: 0.25rem;
color: var(--review-text-accent);
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-weight: 500;
}

.media-tier-controls {
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
}

.media-expand-icon {
width: 1.25rem;
height: 1.25rem;
color: var(--review-text-muted);
transition: transform var(--review-transition-normal);
}

.media-expand-open {
transform: rotate(180deg);
}

/* Badges */
.media-free-badge {
background: rgba(16, 185, 129, 0.15);
color: var(--review-text-success);
border: 1px solid rgba(16, 185, 129, 0.3);
font-size: 0.75rem;
padding: 0.25rem 0.5rem;
}

.media-price-badge {
font-size: 0.75rem;
padding: 0.25rem 0.5rem;
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-weight: 600;
}

.media-supporter-price {
background: rgba(139, 92, 246, 0.15);
color: var(--review-text-accent);
border: 1px solid rgba(139, 92, 246, 0.3);
}

.media-creator-price {
background: rgba(6, 182, 212, 0.15);
color: var(--review-text-cyan);
border: 1px solid rgba(6, 182, 212, 0.3);
}

/* Tier Content */
.media-tier-content {
border-top: 1px solid var(--review-border-secondary);
padding: var(--review-spacing-lg);
}

.media-free-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
gap: var(--review-spacing-lg);
margin-bottom: var(--review-spacing-xl);
}

.media-free-card {
background: var(--review-bg-secondary);
border: 1px solid var(--review-border-secondary);
border-radius: 0.5rem;
padding: var(--review-spacing-md);
transition: all var(--review-transition-normal);
}

.media-free-card:hover {
border-color: var(--review-border-primary);
background: rgba(36, 41, 61, 0.95);
}

.media-card-header {
display: flex;
align-items: center;
margin-bottom: var(--review-spacing-md);
}

.media-card-title {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-weight: 600;
font-size: 0.875rem;
color: var(--review-text-secondary);
}

.media-card-content {
display: flex;
flex-direction: column;
gap: var(--review-spacing-sm);
}

/* Image Previews */
.media-image-preview {
position: relative;
aspect-ratio: 3/1;
border-radius: 0.375rem;
overflow: hidden;
border: 1px solid var(--review-border-secondary);
background: var(--review-bg-primary);
}

.media-preview-image {
width: 100%;
height: 100%;
object-fit: cover;
}

.media-remove-button {
position: absolute;
top: var(--review-spacing-sm);
right: var(--review-spacing-sm);
width: 2rem;
height: 2rem;
background: rgba(0, 0, 0, 0.7);
border: 1px solid rgba(239, 68, 68, 0.5);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: var(--review-text-error);
cursor: pointer;
transition: all var(--review-transition-normal);
}

.media-remove-button:hover {
background: rgba(239, 68, 68, 0.2);
border-color: var(--review-text-error);
}

.media-video-preview {
aspect-ratio: 16/9;
border-radius: 0.375rem;
overflow: hidden;
border: 1px solid var(--review-border-secondary);
}

.media-youtube-iframe {
border-radius: 0.375rem;
}

/* Services Section */
.media-services-section {
background: rgba(15, 23, 42, 0.3);
border: 1px solid var(--review-border-secondary);
border-radius: 0.5rem;
padding: var(--review-spacing-md);
}

.media-services-header {
display: flex;
align-items: center;
margin-bottom: var(--review-spacing-md);
}

.media-services-title {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.875rem;
color: var(--review-text-secondary);
font-weight: 500;
}

.media-services-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--review-spacing-sm);
margin-bottom: var(--review-spacing-md);
}

.media-service-card {
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
padding: var(--review-spacing-sm) var(--review-spacing-md);
background: var(--review-bg-tertiary);
border: 1px solid var(--review-border-secondary);
border-radius: 0.375rem;
color: var(--review-text-secondary);
text-decoration: none;
transition: all var(--review-transition-normal);
}

.media-service-card:hover {
border-color: var(--review-border-primary);
background: var(--review-bg-secondary);
color: var(--review-text-primary);
transform: translateY(-1px);
}

.media-service-info {
flex: 1;
display: flex;
align-items: center;
}

.media-service-name {
font-weight: 500;
font-size: 0.875rem;
color: inherit;
}

.media-service-link {
width: 0.875rem;
height: 0.875rem;
opacity: 0.7;
}

.media-services-note {
display: flex;
align-items: center;
font-size: 0.75rem;
color: var(--review-text-cyan);
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

/* Premium Container - Vertical Stack */
.media-premium-container {
display: flex;
flex-direction: column;
gap: var(--review-spacing-lg);
}

.media-premium-expanded .media-tier-section {
margin-bottom: 0;
}

/* Showcase Designs */
.media-supporter-showcase,
.media-creator-showcase {
display: flex;
flex-direction: column;
gap: var(--review-spacing-lg);
}

.media-showcase-header {
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
padding-bottom: var(--review-spacing-sm);
border-bottom: 1px solid var(--review-border-secondary);
}

.media-showcase-icon {
width: 1.25rem;
height: 1.25rem;
color: var(--review-text-accent);
}

.media-showcase-title {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.875rem;
font-weight: 600;
color: var(--review-text-secondary);
}

/* Upload Showcase */
.media-upload-showcase {
display: flex;
flex-direction: column;
gap: var(--review-spacing-md);
}

.media-upload-demo-area {
border: 2px dashed var(--review-border-secondary);
border-radius: 0.5rem;
padding: var(--review-spacing-lg);
cursor: pointer;
transition: all var(--review-transition-normal);
position: relative;
background: rgba(15, 23, 42, 0.2);
min-height: 120px;
}

.media-upload-demo-area:hover:not(.media-upload-locked) {
border-color: var(--review-text-accent);
background: rgba(139, 92, 246, 0.05);
}

.media-upload-locked {
cursor: not-allowed;
opacity: 0.7;
}

.media-upload-input {
display: none;
}

.media-upload-visual {
display: flex;
flex-direction: column;
align-items: center;
gap: var(--review-spacing-sm);
}

.media-upload-icon-container {
position: relative;
display: flex;
flex-direction: column;
align-items: center;
gap: var(--review-spacing-sm);
}

.media-upload-large-icon {
width: 3rem;
height: 3rem;
color: var(--review-text-accent);
opacity: 0.8;
}

.media-upload-progress {
width: 3rem;
height: 4px;
background: rgba(139, 92, 246, 0.2);
border-radius: 2px;
overflow: hidden;
}

.media-progress-bar {
height: 100%;
background: var(--review-text-accent);
border-radius: 2px;
animation: media-upload-pulse 2s ease-in-out infinite;
}

.media-upload-status {
font-size: 0.875rem;
color: var(--review-text-secondary);
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
text-align: center;
}

.media-upload-overlay {
position: absolute;
inset: 0;
background: rgba(0, 0, 0, 0.6);
display: flex;
align-items: center;
justify-content: center;
border-radius: 0.5rem;
}

.media-overlay-lock {
width: 2rem;
height: 2rem;
color: var(--review-text-muted);
}

/* Floating Cards for Creator Showcase */
.media-gallery-showcase {
display: flex;
flex-direction: column;
gap: var(--review-spacing-md);
}

.media-floating-cards {
position: relative;
height: 140px;
display: flex;
align-items: center;
justify-content: center;
margin: var(--review-spacing-md) 0;
}

.media-floating-card {
position: absolute;
width: 100px;
height: 100px;
border-radius: 0.75rem;
background: var(--review-bg-secondary);
border: 1px solid var(--review-border-secondary);
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
gap: var(--review-spacing-xs);
transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
cursor: pointer;
}

.media-floating-card.media-card-active {
border-color: var(--review-text-cyan);
background: var(--review-bg-tertiary);
}

.media-card-glow {
position: absolute;
inset: -2px;
background: linear-gradient(45deg, var(--review-text-accent), var(--review-text-cyan));
border-radius: 0.75rem;
opacity: 0;
transition: opacity 0.3s ease;
z-index: -1;
}

.media-floating-card.media-card-active .media-card-glow {
opacity: 0.3;
}

.media-card-content {
display: flex;
flex-direction: column;
align-items: center;
gap: var(--review-spacing-xs);
text-align: center;
}

.media-card-content svg {
width: 1.5rem;
height: 1.5rem;
color: var(--review-text-accent);
}

.media-card-text {
font-size: 0.625rem;
color: var(--review-text-secondary);
font-weight: 500;
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

.media-gallery-info {
text-align: center;
}

.media-info-text {
font-size: 0.875rem;
color: var(--review-text-secondary);
margin-bottom: var(--review-spacing-sm);
}

.media-carousel-dots {
display: flex;
justify-content: center;
gap: var(--review-spacing-xs);
}

.media-dot {
width: 0.5rem;
height: 0.5rem;
border-radius: 50%;
border: none;
background: rgba(255, 255, 255, 0.3);
cursor: pointer;
transition: all var(--review-transition-normal);
}

.media-dot-active {
background: var(--review-text-cyan);
transform: scale(1.2);
}

/* Benefits Grid */
.media-benefits-grid {
display: grid;
grid-template-columns: 1fr 1fr;
gap: var(--review-spacing-sm);
}

.media-benefit-item {
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
font-size: 0.75rem;
color: var(--review-text-secondary);
padding: var(--review-spacing-xs);
}

.media-benefit-icon {
width: 0.875rem;
height: 0.875rem;
color: var(--review-text-success);
flex-shrink: 0;
}

.media-creator-icon {
color: var(--review-text-cyan);
}

/* Unlock Buttons */
.media-unlock-button {
background: var(--review-gradient-button);
color: var(--review-text-primary);
border: none;
padding: var(--review-spacing-sm) var(--review-spacing-lg);
border-radius: 0.375rem;
font-weight: 500;
transition: all var(--review-transition-normal);
display: flex;
align-items: center;
gap: var(--review-spacing-sm);
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.875rem;
}

.media-unlock-button:hover {
background: var(--review-gradient-button-hover);
transform: translateY(-1px);
box-shadow: var(--review-shadow-glow);
}

.media-supporter-unlock {
background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(139, 92, 246, 0.6));
}

.media-supporter-unlock:hover {
background: linear-gradient(135deg, var(--review-text-accent), rgba(139, 92, 246, 0.8));
}

.media-creator-unlock {
background: linear-gradient(135deg, rgba(6, 182, 212, 0.8), rgba(6, 182, 212, 0.6));
}

.media-creator-unlock:hover {
background: linear-gradient(135deg, var(--review-text-cyan), rgba(6, 182, 212, 0.8));
}

.media-arrow-icon {
width: 1rem;
height: 1rem;
}

/* Premium Hidden State */
.media-premium-hidden {
background: rgba(156, 163, 175, 0.05);
border: 1px solid rgba(156, 163, 175, 0.2);
border-radius: 0.5rem;
padding: var(--review-spacing-xl);
text-align: center;
margin-bottom: var(--review-spacing-lg);
}

.media-hidden-content {
display: flex;
align-items: center;
justify-content: center;
gap: var(--review-spacing-sm);
}

.media-hidden-icon {
width: 1.25rem;
height: 1.25rem;
color: var(--review-text-muted);
}

.media-hidden-text {
font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
font-size: 0.875rem;
color: var(--review-text-muted);
}

.media-hidden-timer {
color: var(--review-text-accent);
font-weight: 500;
}

/* Animations */
@keyframes media-pulse {
0%, 100% {
  opacity: 0.3;
}
50% {
  opacity: 0.6;
}
}

@keyframes media-upload-pulse {
0%, 100% {
  transform: scaleX(0.3);
}
50% {
  transform: scaleX(1);
}
}

@keyframes media-message-fade {
0% {
  opacity: 0.9;
}
80% {
  opacity: 0.9;
}
100% {
  opacity: 0;
}
}

/* Responsive Design */
@media (max-width: 768px) {
.media-free-grid {
  grid-template-columns: 1fr;
}

.media-services-grid {
  grid-template-columns: 1fr;
}

.media-tier-features {
  display: none;
}

.media-header-controls {
  flex-direction: column;
  align-items: stretch;
}

.media-toggle-message {
  order: 2;
  margin-top: var(--review-spacing-sm);
}

.media-benefits-grid {
  grid-template-columns: 1fr;
}

.media-floating-cards {
  height: 100px;
}

.media-floating-card {
  width: 80px;
  height: 80px;
}
}

/* ═══════════════════════════════════════════════════════════════════════════
 🎮 ELEGANT RATING SECTION - ENHANCED LAYOUT CSS
 ═══════════════════════════════════════════════════════════════════════════ */

/* Compact Overall Score - Original Styles */
.rating-overall-compact {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(6, 182, 212, 0.05) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 8px;
  margin-bottom: 20px;
}

.rating-gauge-mini {
  flex: 1;
  height: 12px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.rating-segments-mini {
  display: flex;
  height: 100%;
  gap: 1px;
}

.rating-segment-mini {
  flex: 1;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
}

.rating-segment-active-mini {
  background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%);
  animation: segmentGlow 0.5s ease-out;
}

.rating-score-compact {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 180px;
  justify-content: flex-end;
}

.rating-number-compact {
  font-size: 2rem;
  font-weight: 700;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  color: var(--review-text-primary);
  transition: all 0.3s ease;
}

.rating-animate-compact {
  transform: scale(1.05);
  color: #06b6d4;
  text-shadow: 0 0 8px rgba(6, 182, 212, 0.4);
}

.rating-details-compact {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.rating-tier-compact {
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

.rating-separator-compact {
  color: var(--review-text-muted);
  font-size: 0.75rem;
  display: none;
}

.rating-rank-compact {
  font-size: 0.7rem;
  font-weight: 500;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  opacity: 0.8;
}

/* Compact Rating Controls - Original */
.rating-controls-compact {
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.rating-controls-header-compact {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.rating-controls-title-compact {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--review-text-secondary);
}

.rating-criteria-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

/* Elegant Overall Score - Enhanced Styles */
.rating-overall-elegant {
  padding: 24px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.12) 0%, rgba(6, 182, 212, 0.08) 100%);
  border: 1px solid rgba(139, 92, 246, 0.25);
  border-radius: 12px;
  margin-bottom: 32px;
}

.rating-overall-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.rating-gauge-elegant {
  flex: 1;
  height: 16px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
}

.rating-segments-elegant {
  display: flex;
  height: 100%;
  gap: 1px;
}

.rating-segment-elegant {
  flex: 1;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.4s ease;
}

.rating-segment-active-elegant {
  background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%);
  animation: segmentGlowElegant 0.6s ease-out;
}

.rating-score-elegant {
  display: flex;
  align-items: center;
  gap: 16px;
  min-width: 200px;
  justify-content: flex-end;
}

.rating-number-elegant {
  font-size: 3rem;
  font-weight: 700;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  color: var(--review-text-primary);
  transition: all 0.4s ease;
  line-height: 1;
}

.rating-animate-elegant {
  transform: scale(1.08);
  color: #06b6d4;
  text-shadow: 0 0 12px rgba(6, 182, 212, 0.5);
}

.rating-details-elegant {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.rating-tier-elegant {
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  line-height: 1.2;
}

.rating-rank-elegant {
  font-size: 0.75rem;
  font-weight: 500;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  opacity: 0.8;
  color: var(--review-text-muted);
  line-height: 1.2;
}

/* Elegant Rating Controls */
.rating-controls-elegant {
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.rating-controls-header-elegant {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 12px;
}

.rating-controls-title-elegant {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 1rem;
  font-weight: 600;
  color: var(--review-text-secondary);
}

/* 2x2 Grid Layout - Always 2 columns */
.rating-criteria-grid-2x2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  align-items: start;
}

/* Individual Criterion Cards - Compact (Original) */
.rating-criterion-compact {
  background: rgba(36, 41, 61, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s ease;
}

.rating-criterion-compact:hover {
  border-color: rgba(139, 92, 246, 0.25);
  background: rgba(36, 41, 61, 0.5);
}

.rating-criterion-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rating-criterion-info-compact {
  display: flex;
  align-items: center;
  gap: 6px;
}

.rating-criterion-label-compact {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--review-text-secondary);
  margin: 0;
}

.rating-score-display-compact {
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 8px;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.7rem;
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}

/* Compact Slider Styling (Original) */
.rating-slider-container-compact {
  position: relative;
  height: 24px;
}

.rating-slider-track-compact {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 6px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 3px;
  transform: translateY(-50%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
}

.rating-slider-fill-compact {
  height: 100%;
  background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%);
  border-radius: 2px;
  transition: width 0.2s ease;
  position: relative;
}

.rating-slider-fill-compact::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: miniShimmer 2s linear infinite;
}

.rating-slider-input-compact {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

.rating-slider-thumb-compact {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: #ffffff;
  border: 2px solid #8b5cf6;
  border-radius: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  box-shadow: 0 1px 4px rgba(139, 92, 246, 0.4);
  transition: left 0.2s ease;
}

/* Score Tier Colors */
.score-legendary { color: #e879f9; }
.score-epic { color: #c084fc; }
.score-excellent { color: #8b5cf6; }
.score-great { color: #06b6d4; }
.score-good { color: #10b981; }
.score-decent { color: #22d3ee; }
.score-average { color: #fbbf24; }
.score-below { color: #fb923c; }
.score-mediocre { color: #f97316; }
.score-poor { color: #ef4444; }
.score-terrible { color: #dc2626; }

/* Animations */
@keyframes segmentGlow {
  0% {
    opacity: 0.5;
    transform: scaleY(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.1);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes segmentGlowElegant {
  0% {
    opacity: 0.5;
    transform: scaleY(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.15);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}

@keyframes miniShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* Responsive Design - Maintain 2x2 Grid */
@media (max-width: 1360px) {
  .rating-criteria-horizontal {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  .rating-overall-content {
    gap: 16px;
  }
  
  .rating-number-elegant {
    font-size: 2.5rem;
  }
  
  .rating-criteria-grid-2x2 {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .rating-criteria-horizontal {
    grid-template-columns: 1fr;
  }

  .rating-overall-compact {
    flex-direction: column;
    gap: 12px;
  }

  .rating-score-compact {
    min-width: auto;
    justify-content: center;
  }

  .rating-number-compact {
    font-size: 1.75rem;
  }
  
  .rating-overall-elegant {
    padding: 20px;
  }
  
  .rating-overall-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .rating-score-elegant {
    min-width: auto;
    justify-content: center;
  }
  
  .rating-number-elegant {
    font-size: 2.25rem;
  }
  
  .rating-details-elegant {
    align-items: center;
  }
  
  .rating-controls-elegant {
    padding: 20px;
  }
  
  .rating-criteria-grid-2x2 {
    gap: 12px;
  }
  
  .rating-criterion-compact {
    padding: 14px;
  }
}

@media (max-width: 640px) {
  /* Only break to single column on very small screens */
  .rating-criteria-grid-2x2 {
    grid-template-columns: 1fr;
  }
}