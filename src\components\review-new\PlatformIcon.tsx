import React from 'react';
import { Monitor, Smartphone } from 'lucide-react';
import SteamIconSimple from '@/components/ui/icons/gaming/SteamIconSimple';
import PlaystationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';
import NintendoSwitchIconSimple from '@/components/ui/icons/gaming/NintendoSwitchIconSimple';
import EpicGamesIcon from '@/components/ui/icons/gaming/EpicGamesIcon';

interface PlatformIconProps {
  platform: string;
}

const PlatformIcon = ({ platform }: PlatformIconProps) => {
  const p = platform.toLowerCase();
  
  // Steam platforms
  if (p.includes('steam') || p.includes('pc (steam)')) {
    return <SteamIconSimple className="w-3 h-3" />;
  }
  
  // Epic Games
  if (p.includes('epic')) {
    return <EpicGamesIcon className="w-3 h-3" />;
  }
  
  // PlayStation platforms
  if (p.includes('playstation') || p.includes('ps')) {
    return <PlaystationIcon className="w-3 h-3" />;
  }
  
  // Xbox platforms
  if (p.includes('xbox')) {
    return <XboxIcon className="w-3 h-3" />;
  }
  
  // Nintendo platforms
  if (p.includes('switch') || p.includes('nintendo')) {
    return <NintendoSwitchIconSimple className="w-3 h-3" />;
  }
  
  // Mobile platforms
  if (p.includes('mobile') || p.includes('android') || p.includes('ios')) {
    return <Smartphone className="w-3 h-3" />;
  }
  
  // PC/Windows (default for PC platforms)
  if (p.includes('pc') || p.includes('windows') || p.includes('gog') || p.includes('linux') || p.includes('mac')) {
    return <Monitor className="w-3 h-3" />;
  }
  
  // Default fallback
  return <Monitor className="w-3 h-3" />;
};

export default PlatformIcon;
