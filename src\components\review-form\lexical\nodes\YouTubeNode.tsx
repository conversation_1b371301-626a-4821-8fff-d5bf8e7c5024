// src/components/review-form/lexical/nodes/YouTubeNode.tsx
import React from 'react';
import type {
  DOMConversionMap,
  DOMConversionOutput,
  DOMExportOutput,
  EditorConfig,
  ElementFormatType,
  LexicalNode,
  NodeKey,
  Spread,
} from 'lexical';

import { $applyNodeReplacement, DecoratorNode } from 'lexical';

export interface YouTubePayload {
  videoId: string;
  key?: NodeKey;
  showCaption?: boolean;
}

function convertYouTubeElement(domNode: Node): null | DOMConversionOutput {
  if (domNode instanceof HTMLIFrameElement) {
    const src = domNode.getAttribute('src');
    if (src && src.includes('youtube.com/embed/')) {
      const videoId = src.split('/embed/')[1]?.split('?')[0];
      if (videoId) {
        const node = $createYouTubeNode({ videoId, showCaption: false });
        return { node };
      }
    }
  }
  return null;
}

export type SerializedYouTubeNode = Spread<
  {
    videoId: string;
  },
  Spread<
    {
      type: 'youtube';
      version: 1;
    },
    any
  >
>;

export class YouTubeNode extends DecoratorNode<JSX.Element> {
  __videoId: string;
  __showCaption: boolean;

  static getType(): string {
    return 'youtube';
  }

  static clone(node: YouTubeNode): YouTubeNode {
    return new YouTubeNode(
      node.__videoId,
      node.__showCaption,
      node.__key,
    );
  }

  static importJSON(serializedNode: SerializedYouTubeNode): YouTubeNode {
    const { videoId } = serializedNode;
    const node = $createYouTubeNode({
      videoId,
      showCaption: false,
    });
    return node;
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement('iframe');
    element.setAttribute('src', `https://www.youtube.com/embed/${this.__videoId}`);
    element.setAttribute('width', '560');
    element.setAttribute('height', '315');
    element.setAttribute('frameborder', '0');
    element.setAttribute('allowfullscreen', 'true');
    element.setAttribute('title', 'YouTube video');
    return { element };
  }

  static importDOM(): DOMConversionMap | null {
    return {
      iframe: (node: Node) => ({
        conversion: convertYouTubeElement,
        priority: 0,
      }),
    };
  }

  constructor(
    videoId: string,
    showCaption?: boolean,
    key?: NodeKey,
  ) {
    super(key);
    this.__videoId = videoId;
    this.__showCaption = showCaption || false;
  }

  exportJSON(): SerializedYouTubeNode {
    return {
      videoId: this.getVideoId(),
      type: 'youtube',
      version: 1,
    };
  }

  // View

  createDOM(config: EditorConfig): HTMLElement {
    const span = document.createElement('span');
    const theme = config.theme;
    const className = theme.youtube;
    if (className !== undefined) {
      span.className = className;
    }
    return span;
  }

  updateDOM(): false {
    return false;
  }

  getVideoId(): string {
    return this.__videoId;
  }

  decorate(): JSX.Element {
    return (
      <YouTubeComponent
        videoId={this.__videoId}
        nodeKey={this.getKey()}
      />
    );
  }
}

export function $createYouTubeNode({
  videoId,
  showCaption,
  key,
}: YouTubePayload): YouTubeNode {
  return $applyNodeReplacement(
    new YouTubeNode(
      videoId,
      showCaption,
      key,
    ),
  );
}

export function $isYouTubeNode(
  node: LexicalNode | null | undefined,
): node is YouTubeNode {
  return node instanceof YouTubeNode;
}

// React component for rendering the YouTube video
interface YouTubeComponentProps {
  videoId: string;
  nodeKey: NodeKey;
}

function YouTubeComponent({
  videoId,
  nodeKey,
}: YouTubeComponentProps): JSX.Element {
  // Detect if we're in a read-only context (rendered view) vs editor
  const isReadOnly = React.useMemo(() => {
    const editorContainer = document.querySelector('.lexical-content-editable');
    return editorContainer?.getAttribute('contenteditable') === 'false';
  }, []);

  return (
    <figure className={`lexical-image-figure ${isReadOnly ? 'lexical-image-clickable' : ''}`}>
      <div
        className="lexical-image-container"
        style={{
          cursor: 'default',
          aspectRatio: '16/9',
          position: 'relative',
          backgroundColor: '#000',
          minHeight: '200px'
        }}
      >
        <iframe
          src={`https://www.youtube.com/embed/${videoId}`}
          title="YouTube video"
          className="lexical-image"
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            border: 'none'
          }}
          frameBorder="0"
          allowFullScreen
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        />
      </div>
    </figure>
  );
}