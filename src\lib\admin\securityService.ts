import { createClient } from '@/lib/supabase/client';
import { logAdminAction } from '@/lib/audit/adminActions';

// Types for security monitoring
export interface SecurityEvent {
  id: string;
  type: 'FAILED_LOGIN' | 'SUSPICIOUS_ACTIVITY' | 'PRIVILEGE_ESCALATION' | 'UNAUTHORIZED_ACCESS' | 'RATE_LIMIT_EXCEEDED' | 'MALICIOUS_REQUEST';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  description: string;
  details: Record<string, any>;
  timestamp: string;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
  notes?: string;
}

export interface ThreatAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  activeThreats: number;
  resolvedThreats: number;
  recentEvents: SecurityEvent[];
  riskFactors: {
    factor: string;
    level: 'low' | 'medium' | 'high';
    description: string;
  }[];
  recommendations: string[];
  lastUpdated: string;
}

export interface AccessPattern {
  userId: string;
  username: string;
  loginCount: number;
  lastLogin: string;
  ipAddresses: string[];
  userAgents: string[];
  suspiciousActivity: boolean;
  riskScore: number;
  locations: string[];
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsToday: number;
  eventsThisWeek: number;
  failedLogins: number;
  blockedRequests: number;
  suspiciousUsers: number;
  averageRiskScore: number;
  topThreats: {
    type: string;
    count: number;
    percentage: number;
  }[];
  lastUpdated: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(adminId: string): Promise<boolean> {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .rpc('is_admin', { user_id: adminId });
    
    if (error) {
      console.error('Error verifying admin permissions:', error);
      return false;
    }
    
    return data === true;
  } catch (error) {
    console.error('Error in verifyAdminPermissions:', error);
    return false;
  }
}

// Get security events
export async function getSecurityEvents(
  adminId: string,
  filters?: {
    type?: string;
    severity?: string;
    resolved?: boolean;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
    offset?: number;
  }
): Promise<SecurityEvent[]> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // In a real implementation, this would come from a security_events table
    // For now, generate mock security events
    const mockEvents: SecurityEvent[] = [
      {
        id: '1',
        type: 'FAILED_LOGIN',
        severity: 'medium',
        userId: 'unknown',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        description: 'Multiple failed login attempts from same IP',
        details: {
          attempts: 5,
          timespan: '5 minutes',
          targetUser: '<EMAIL>'
        },
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        resolved: false
      },
      {
        id: '2',
        type: 'SUSPICIOUS_ACTIVITY',
        severity: 'high',
        userId: 'user123',
        ipAddress: '*********',
        userAgent: 'curl/7.68.0',
        description: 'Unusual API access pattern detected',
        details: {
          requests: 1000,
          timespan: '1 minute',
          endpoints: ['/api/admin/users', '/api/admin/reviews']
        },
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        resolved: true,
        resolvedBy: adminId,
        resolvedAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        notes: 'Confirmed as legitimate automated testing'
      },
      {
        id: '3',
        type: 'RATE_LIMIT_EXCEEDED',
        severity: 'low',
        ipAddress: '************',
        userAgent: 'Python-requests/2.25.1',
        description: 'Rate limit exceeded for API requests',
        details: {
          limit: 100,
          requests: 150,
          timespan: '1 hour'
        },
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
        resolved: false
      },
      {
        id: '4',
        type: 'UNAUTHORIZED_ACCESS',
        severity: 'critical',
        userId: 'user456',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        description: 'Attempt to access admin panel without privileges',
        details: {
          attemptedUrl: '/admin/users',
          userRole: 'user',
          redirected: true
        },
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
        resolved: false
      }
    ];

    // Apply filters
    let filteredEvents = mockEvents;
    
    if (filters?.type) {
      filteredEvents = filteredEvents.filter(event => event.type === filters.type);
    }
    
    if (filters?.severity) {
      filteredEvents = filteredEvents.filter(event => event.severity === filters.severity);
    }
    
    if (filters?.resolved !== undefined) {
      filteredEvents = filteredEvents.filter(event => event.resolved === filters.resolved);
    }

    // Apply pagination
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 50;
    filteredEvents = filteredEvents.slice(offset, offset + limit);

    await logAdminAction(adminId, 'VIEW_SECURITY_EVENTS', 'security', undefined, {
      filters,
      event_count: filteredEvents.length
    });

    return filteredEvents;
  } catch (error) {
    console.error('Error getting security events:', error);
    throw new Error('Failed to retrieve security events');
  }
}

// Get threat assessment
export async function getThreatAssessment(adminId: string): Promise<ThreatAssessment> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const events = await getSecurityEvents(adminId, { limit: 100 });
    
    const activeThreats = events.filter(e => !e.resolved);
    const resolvedThreats = events.filter(e => e.resolved);
    const recentEvents = events.slice(0, 10);
    
    // Calculate overall risk based on active threats
    const criticalThreats = activeThreats.filter(e => e.severity === 'critical').length;
    const highThreats = activeThreats.filter(e => e.severity === 'high').length;
    const mediumThreats = activeThreats.filter(e => e.severity === 'medium').length;
    
    let overallRisk: 'low' | 'medium' | 'high' | 'critical' = 'low';
    if (criticalThreats > 0) {
      overallRisk = 'critical';
    } else if (highThreats > 2) {
      overallRisk = 'high';
    } else if (highThreats > 0 || mediumThreats > 3) {
      overallRisk = 'medium';
    }

    const assessment: ThreatAssessment = {
      overallRisk,
      activeThreats: activeThreats.length,
      resolvedThreats: resolvedThreats.length,
      recentEvents,
      riskFactors: [
        {
          factor: 'Failed Login Attempts',
          level: activeThreats.filter(e => e.type === 'FAILED_LOGIN').length > 0 ? 'medium' : 'low',
          description: `${activeThreats.filter(e => e.type === 'FAILED_LOGIN').length} active failed login events`
        },
        {
          factor: 'Suspicious Activity',
          level: activeThreats.filter(e => e.type === 'SUSPICIOUS_ACTIVITY').length > 0 ? 'high' : 'low',
          description: `${activeThreats.filter(e => e.type === 'SUSPICIOUS_ACTIVITY').length} suspicious activity events`
        },
        {
          factor: 'Unauthorized Access',
          level: activeThreats.filter(e => e.type === 'UNAUTHORIZED_ACCESS').length > 0 ? 'high' : 'low',
          description: `${activeThreats.filter(e => e.type === 'UNAUTHORIZED_ACCESS').length} unauthorized access attempts`
        }
      ],
      recommendations: [
        'Monitor failed login attempts and consider implementing account lockout',
        'Review and update rate limiting configurations',
        'Implement IP-based blocking for repeated offenders',
        'Enable two-factor authentication for admin accounts',
        'Regular security audits and penetration testing'
      ],
      lastUpdated: new Date().toISOString()
    };

    await logAdminAction(adminId, 'VIEW_THREAT_ASSESSMENT', 'security', undefined, {
      overall_risk: overallRisk,
      active_threats: activeThreats.length,
      resolved_threats: resolvedThreats.length
    });

    return assessment;
  } catch (error) {
    console.error('Error getting threat assessment:', error);
    throw new Error('Failed to retrieve threat assessment');
  }
}

// Get access patterns
export async function getAccessPatterns(adminId: string): Promise<AccessPattern[]> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // In a real implementation, this would analyze actual user login data
    // For now, generate mock access patterns
    const patterns: AccessPattern[] = [
      {
        userId: 'user123',
        username: 'john_doe',
        loginCount: 45,
        lastLogin: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        ipAddresses: ['*************', '*********'],
        userAgents: ['Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'curl/7.68.0'],
        suspiciousActivity: true,
        riskScore: 75,
        locations: ['New York, US', 'London, UK']
      },
      {
        userId: 'user456',
        username: 'jane_smith',
        loginCount: 12,
        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        ipAddresses: ['************'],
        userAgents: ['Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'],
        suspiciousActivity: false,
        riskScore: 25,
        locations: ['San Francisco, US']
      },
      {
        userId: 'user789',
        username: 'admin_user',
        loginCount: 156,
        lastLogin: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        ipAddresses: ['*************', '***********'],
        userAgents: ['Mozilla/5.0 (X11; Linux x86_64)'],
        suspiciousActivity: false,
        riskScore: 15,
        locations: ['Seattle, US']
      }
    ];

    await logAdminAction(adminId, 'VIEW_ACCESS_PATTERNS', 'security', undefined, {
      pattern_count: patterns.length,
      suspicious_users: patterns.filter(p => p.suspiciousActivity).length
    });

    return patterns;
  } catch (error) {
    console.error('Error getting access patterns:', error);
    throw new Error('Failed to retrieve access patterns');
  }
}

// Get security metrics
export async function getSecurityMetrics(adminId: string): Promise<SecurityMetrics> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    const events = await getSecurityEvents(adminId, { limit: 1000 });
    const accessPatterns = await getAccessPatterns(adminId);
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const eventsToday = events.filter(e => new Date(e.timestamp) >= today).length;
    const eventsThisWeek = events.filter(e => new Date(e.timestamp) >= weekAgo).length;
    
    // Count by type
    const threatCounts = events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topThreats = Object.entries(threatCounts)
      .map(([type, count]) => ({
        type,
        count,
        percentage: Math.round((count / events.length) * 100)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const metrics: SecurityMetrics = {
      totalEvents: events.length,
      eventsToday,
      eventsThisWeek,
      failedLogins: events.filter(e => e.type === 'FAILED_LOGIN').length,
      blockedRequests: events.filter(e => e.type === 'RATE_LIMIT_EXCEEDED').length,
      suspiciousUsers: accessPatterns.filter(p => p.suspiciousActivity).length,
      averageRiskScore: Math.round(accessPatterns.reduce((sum, p) => sum + p.riskScore, 0) / accessPatterns.length),
      topThreats,
      lastUpdated: new Date().toISOString()
    };

    await logAdminAction(adminId, 'VIEW_SECURITY_METRICS', 'security', undefined, {
      total_events: metrics.totalEvents,
      events_today: metrics.eventsToday,
      suspicious_users: metrics.suspiciousUsers
    });

    return metrics;
  } catch (error) {
    console.error('Error getting security metrics:', error);
    throw new Error('Failed to retrieve security metrics');
  }
}

// Resolve security event
export async function resolveSecurityEvent(
  adminId: string,
  eventId: string,
  notes?: string
): Promise<SecurityEvent> {
  const isAdmin = await verifyAdminPermissions(adminId);
  if (!isAdmin) {
    throw new Error('Unauthorized: Admin access required');
  }

  try {
    // In a real implementation, this would update the security_events table
    // For now, simulate the resolution
    const events = await getSecurityEvents(adminId);
    const event = events.find(e => e.id === eventId);
    
    if (!event) {
      throw new Error('Security event not found');
    }

    const resolvedEvent: SecurityEvent = {
      ...event,
      resolved: true,
      resolvedBy: adminId,
      resolvedAt: new Date().toISOString(),
      notes: notes || 'Resolved by admin'
    };

    await logAdminAction(adminId, 'RESOLVE_SECURITY_EVENT', 'security', eventId, {
      event_type: event.type,
      severity: event.severity,
      notes
    });

    return resolvedEvent;
  } catch (error) {
    console.error('Error resolving security event:', error);
    throw new Error('Failed to resolve security event');
  }
}
