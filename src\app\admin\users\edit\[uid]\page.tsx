'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ShieldAlert, AlertTriangle, Save, UserCircle, ArrowLeft } from 'lucide-react';
import { useAuthContext } from '@/hooks/use-auth-context';

interface UserData {
  uid: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  disabled?: boolean;
  customClaims?: {
    admin?: boolean;
    editor?: boolean;
    [key: string]: any;
  };
}

interface PageProps {
  params: { uid: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

export default function AdminUserEditPage({ params }: PageProps) {
  const router = useRouter();
  const { isAdmin, loading: authLoading } = useAuthContext();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [user, setUser] = useState<UserData | null>(null);
  const [formData, setFormData] = useState<Partial<UserData>>({
    customClaims: {}
  });
  const { toast } = useToast();

  // SAFETY CHECK: Firebase admin functionality disabled
  // Admin access is completely disabled until Supabase implementation
  useEffect(() => {
    if (!authLoading && !isAdmin) {
      router.push('/');
    }
  }, [authLoading, isAdmin, router]);

  // Fetch user data from API
  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/users/${params.uid}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }
      
      const data = await response.json();
      setUser(data.user);
      setFormData({
        email: data.user.email || '',
        displayName: data.user.displayName || '',
        disabled: data.user.disabled || false,
        customClaims: { ...(data.user.customClaims || {}) }
      });
    } catch (error) {
      console.error('Error fetching user:', error);
      toast({
        title: 'Error',
        description: 'Failed to load user data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleRole = (role: 'admin' | 'editor') => {
    setFormData(prev => ({
      ...prev,
      customClaims: {
        ...prev.customClaims,
        [role]: !prev.customClaims?.[role]
      }
    }));
  };

  useEffect(() => {
    fetchUser();
  }, [params.uid]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    if (name.startsWith('customClaims.')) {
      const claimKey = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        customClaims: {
          ...prev.customClaims,
          [claimKey]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) return;
    
    setSaving(true);
    
    try {
      const response = await fetch(`/api/admin/users/${params.uid}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          // Only include changed fields
          ...(formData.email === user.email ? { email: undefined } : {}),
          ...(formData.displayName === user.displayName ? { displayName: undefined } : {}),
          ...(formData.disabled === user.disabled ? { disabled: undefined } : {}),
          // Only update custom claims if they've changed
          ...(JSON.stringify(formData.customClaims) === JSON.stringify(user.customClaims || {}) 
            ? {} 
            : { customClaims: formData.customClaims })
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update user');
      }
      
      // Refresh user data
      await fetchUser();
      
      toast({
        title: 'Success',
        description: data.message || 'User updated successfully',
      });
      
    } catch (error) {
      console.error('Error saving user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update user',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
        <Card className="w-full max-w-md shadow-lg glow-purple">
          <CardHeader className="items-center">
            <ShieldAlert className="h-16 w-16 text-destructive mb-4" />
            <CardTitle className="text-3xl font-bold text-destructive">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">You don't have admin privileges.</p>
            <p className="text-muted-foreground text-sm mt-2">Contact an administrator if you believe this is an error.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/')} className="w-full">Go to Homepage</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>User not found</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit User</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Back to Users
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Details</CardTitle>
          <CardDescription>Update user information and permissions</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  name="displayName"
                  value={formData.displayName || ''}
                  onChange={handleInputChange}
                  disabled={saving}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={handleInputChange}
                  disabled={saving}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="disabled"
                  checked={formData.disabled || false}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, disabled: checked }))
                  }
                  disabled={saving}
                />
                <Label htmlFor="disabled">Account Disabled</Label>
              </div>
            </div>

            <div className="space-y-4 pt-4 border-t">
              <h3 className="font-medium">User Roles</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="admin-role"
                    checked={!!formData.customClaims?.admin}
                    onCheckedChange={() => toggleRole('admin')}
                    disabled={saving}
                  />
                  <Label htmlFor="admin-role">Administrator</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="editor-role"
                    checked={!!formData.customClaims?.editor}
                    onCheckedChange={() => toggleRole('editor')}
                    disabled={saving}
                  />
                  <Label htmlFor="editor-role">Editor</Label>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.back()}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
