-- ===========================
-- Community Stats System Migration
-- Created: 2025-01-28
-- Purpose: Track comprehensive site statistics including users, reviews, surveys, and lines
-- ===========================

-- 1. Add additional metrics to site_statistics table
INSERT INTO site_statistics (metric_name, metric_value) VALUES 
('total_reviews', 0),
('total_surveys', 0),
('total_users', 0)
ON CONFLICT (metric_name) DO NOTHING;

-- 2. Create function to update all site statistics
CREATE OR REPLACE FUNCTION update_all_site_statistics()
RETURNS VOID AS $$
DECLARE
  total_reviews_count BIGINT;
  total_surveys_count BIGINT;
  total_users_count BIGINT;
  total_lines_count BIGINT;
BEGIN
  -- Count total published reviews
  SELECT COUNT(*) INTO total_reviews_count
  FROM reviews 
  WHERE status = 'published';
  
  -- Count total surveys
  SELECT COUNT(*) INTO total_surveys_count
  FROM performance_surveys
  WHERE is_deleted = false;
  
  -- Count total registered users
  SELECT COUNT(*) INTO total_users_count
  FROM profiles;
  
  -- Count total lines (recalculate for accuracy)
  SELECT COALESCE(SUM(line_count), 0) INTO total_lines_count
  FROM reviews 
  WHERE line_count > 0 AND status = 'published';
  
  -- Update all statistics
  UPDATE site_statistics 
  SET 
    metric_value = CASE 
      WHEN site_statistics.metric_name = 'total_reviews' THEN total_reviews_count
      WHEN site_statistics.metric_name = 'total_surveys' THEN total_surveys_count
      WHEN site_statistics.metric_name = 'total_users' THEN total_users_count
      WHEN site_statistics.metric_name = 'total_review_lines' THEN total_lines_count
      ELSE metric_value
    END,
    last_updated = NOW()
  WHERE site_statistics.metric_name IN ('total_reviews', 'total_surveys', 'total_users', 'total_review_lines');
END;
$$ LANGUAGE plpgsql;

-- 3. Create trigger functions for automatic updates

-- Function to update user count
CREATE OR REPLACE FUNCTION update_user_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE site_statistics 
    SET metric_value = metric_value + 1, last_updated = NOW()
    WHERE metric_name = 'total_users';
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE site_statistics 
    SET metric_value = GREATEST(metric_value - 1, 0), last_updated = NOW()
    WHERE metric_name = 'total_users';
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to update review count
CREATE OR REPLACE FUNCTION update_review_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.status = 'published' THEN
    UPDATE site_statistics 
    SET metric_value = metric_value + 1, last_updated = NOW()
    WHERE metric_name = 'total_reviews';
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle status changes
    IF OLD.status != 'published' AND NEW.status = 'published' THEN
      UPDATE site_statistics 
      SET metric_value = metric_value + 1, last_updated = NOW()
      WHERE metric_name = 'total_reviews';
    ELSIF OLD.status = 'published' AND NEW.status != 'published' THEN
      UPDATE site_statistics 
      SET metric_value = GREATEST(metric_value - 1, 0), last_updated = NOW()
      WHERE metric_name = 'total_reviews';
    END IF;
  ELSIF TG_OP = 'DELETE' AND OLD.status = 'published' THEN
    UPDATE site_statistics 
    SET metric_value = GREATEST(metric_value - 1, 0), last_updated = NOW()
    WHERE metric_name = 'total_reviews';
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to update survey count
CREATE OR REPLACE FUNCTION update_survey_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.is_deleted = false THEN
    UPDATE site_statistics 
    SET metric_value = metric_value + 1, last_updated = NOW()
    WHERE metric_name = 'total_surveys';
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle deletion status changes
    IF OLD.is_deleted = true AND NEW.is_deleted = false THEN
      UPDATE site_statistics 
      SET metric_value = metric_value + 1, last_updated = NOW()
      WHERE metric_name = 'total_surveys';
    ELSIF OLD.is_deleted = false AND NEW.is_deleted = true THEN
      UPDATE site_statistics 
      SET metric_value = GREATEST(metric_value - 1, 0), last_updated = NOW()
      WHERE metric_name = 'total_surveys';
    END IF;
  ELSIF TG_OP = 'DELETE' AND OLD.is_deleted = false THEN
    UPDATE site_statistics 
    SET metric_value = GREATEST(metric_value - 1, 0), last_updated = NOW()
    WHERE metric_name = 'total_surveys';
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 4. Update the review line count trigger to work with new system
CREATE OR REPLACE FUNCTION update_review_line_count()
RETURNS TRIGGER AS $$
DECLARE
  old_line_count INTEGER := 0;
  new_line_count INTEGER := 0;
  line_diff INTEGER := 0;
BEGIN
  -- Calculate new line count
  IF NEW.content_lexical IS NOT NULL THEN
    new_line_count := count_lines_from_lexical(NEW.content_lexical);
  END IF;
  
  -- Update the line_count column
  NEW.line_count := new_line_count;
  
  -- Calculate difference for global line counter update (only for published reviews)
  IF NEW.status = 'published' THEN
    IF TG_OP = 'UPDATE' AND OLD.status = 'published' THEN
      old_line_count := COALESCE(OLD.line_count, 0);
      line_diff := new_line_count - old_line_count;
    ELSIF TG_OP = 'INSERT' THEN
      line_diff := new_line_count;
    ELSIF TG_OP = 'UPDATE' AND OLD.status != 'published' THEN
      -- Review was just published
      line_diff := new_line_count;
    END IF;
  ELSIF TG_OP = 'UPDATE' AND OLD.status = 'published' AND NEW.status != 'published' THEN
    -- Review was unpublished
    line_diff := -COALESCE(OLD.line_count, 0);
  END IF;
  
  -- Update global line counter if there's a difference
  IF line_diff != 0 THEN
    UPDATE site_statistics 
    SET 
      metric_value = metric_value + line_diff,
      last_updated = NOW()
    WHERE metric_name = 'total_review_lines';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create triggers for automatic statistics updates

-- User count trigger
DROP TRIGGER IF EXISTS trigger_update_user_count ON profiles;
CREATE TRIGGER trigger_update_user_count
  AFTER INSERT OR DELETE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_user_count();

-- Review count trigger
DROP TRIGGER IF EXISTS trigger_update_review_count ON reviews;
CREATE TRIGGER trigger_update_review_count
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_review_count();

-- Survey count trigger
DROP TRIGGER IF EXISTS trigger_update_survey_count ON performance_surveys;
CREATE TRIGGER trigger_update_survey_count
  AFTER INSERT OR UPDATE OR DELETE ON performance_surveys
  FOR EACH ROW
  EXECUTE FUNCTION update_survey_count();

-- 6. Initialize all statistics with current data
SELECT update_all_site_statistics();

-- 7. Add comments for documentation
COMMENT ON FUNCTION update_all_site_statistics() IS 'Updates all site statistics with current counts from database';
COMMENT ON FUNCTION update_user_count() IS 'Trigger function to update total user count';
COMMENT ON FUNCTION update_review_count() IS 'Trigger function to update total review count';
COMMENT ON FUNCTION update_survey_count() IS 'Trigger function to update total survey count';

-- Migration complete
-- All site statistics are now tracked automatically
