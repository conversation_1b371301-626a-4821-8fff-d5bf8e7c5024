"use client";

import React, { memo, useMemo, useCallback, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

// Performance optimization utilities
export const ITEMS_PER_PAGE = 12;
export const VIRTUAL_ITEM_HEIGHT = 120;

// Memoized components for performance
export const MemoizedCard = memo(({ children, className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("bg-slate-900/40 border border-slate-700/30 rounded-xl backdrop-blur-sm", className)} {...props}>
    {children}
  </div>
));
MemoizedCard.displayName = "MemoizedCard";

// Virtual scrolling hook for large lists
export function useVirtualScroll<T>(
  items: T[],
  containerHeight: number,
  itemHeight: number = VIRTUAL_ITEM_HEIGHT
) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, scrollTop, containerHeight, itemHeight]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    ...visibleItems,
    handleScroll
  };
}

// Pagination hook with URL state
export function usePagination(totalItems: number, itemsPerPage: number = ITEMS_PER_PAGE) {
  const [currentPage, setCurrentPage] = useState(1);
  
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  
  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);
  
  const nextPage = useCallback(() => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  }, [currentPage, totalPages]);
  
  const prevPage = useCallback(() => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  }, [currentPage]);
  
  return {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    nextPage,
    prevPage,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1
  };
}

// Debounced search hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Optimized search and filter hook
export function useOptimizedFilter<T>(
  items: T[],
  searchQuery: string,
  filterFn?: (item: T, query: string) => boolean,
  sortFn?: (a: T, b: T) => number
) {
  const debouncedQuery = useDebounce(searchQuery, 300);
  
  return useMemo(() => {
    let filtered = items;
    
    // Apply search filter
    if (debouncedQuery && filterFn) {
      filtered = items.filter(item => filterFn(item, debouncedQuery));
    }
    
    // Apply sorting
    if (sortFn) {
      filtered = [...filtered].sort(sortFn);
    }
    
    return filtered;
  }, [items, debouncedQuery, filterFn, sortFn]);
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      options
    );

    observer.observe(element);
    return () => observer.disconnect();
  }, [ref, options]);

  return isIntersecting;
}

// Lazy loading image component
export const LazyImage = memo(({ 
  src, 
  alt, 
  className, 
  fallback,
  ...props 
}: React.ImgHTMLAttributes<HTMLImageElement> & { fallback?: React.ReactNode }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);
  const isVisible = useIntersectionObserver(imgRef, { threshold: 0.1 });

  return (
    <div ref={imgRef} className={cn("relative overflow-hidden", className)}>
      <AnimatePresence>
        {!isLoaded && !hasError && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-slate-800/50 animate-pulse"
          />
        )}
      </AnimatePresence>
      
      {isVisible && (
        <img
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          onError={() => setHasError(true)}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          {...props}
        />
      )}
      
      {hasError && fallback && (
        <div className="absolute inset-0 flex items-center justify-center bg-slate-800/50">
          {fallback}
        </div>
      )}
    </div>
  );
});
LazyImage.displayName = "LazyImage";

// Optimized pagination component
export const PaginationControls = memo(({ 
  currentPage, 
  totalPages, 
  onPageChange,
  className 
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}) => {
  const getVisiblePages = useCallback(() => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  }, [currentPage, totalPages]);

  if (totalPages <= 1) return null;

  return (
    <div className={cn("flex items-center justify-center gap-2", className)}>
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="px-3 py-2 text-sm rounded-lg bg-slate-800/50 text-slate-300 hover:bg-slate-700/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Previous
      </button>
      
      {getVisiblePages().map((page, index) => (
        <button
          key={index}
          onClick={() => typeof page === 'number' && onPageChange(page)}
          disabled={page === '...'}
          className={cn(
            "px-3 py-2 text-sm rounded-lg transition-colors",
            page === currentPage
              ? "bg-purple-600 text-white"
              : "bg-slate-800/50 text-slate-300 hover:bg-slate-700/50",
            page === '...' && "cursor-default"
          )}
        >
          {page}
        </button>
      ))}
      
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="px-3 py-2 text-sm rounded-lg bg-slate-800/50 text-slate-300 hover:bg-slate-700/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Next
      </button>
    </div>
  );
});
PaginationControls.displayName = "PaginationControls";

// Loading skeleton component
export const LoadingSkeleton = memo(({ 
  className, 
  count = 1,
  height = "h-4"
}: { 
  className?: string; 
  count?: number;
  height?: string;
}) => (
  <div className={cn("space-y-3", className)}>
    {Array.from({ length: count }).map((_, i) => (
      <div
        key={i}
        className={cn(
          "bg-slate-800/50 rounded animate-pulse",
          height
        )}
      />
    ))}
  </div>
));
LoadingSkeleton.displayName = "LoadingSkeleton";

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Only log if performance is severely degraded (more than 100ms)
      if (renderTime > 100) {
        console.warn(`${componentName} took ${renderTime.toFixed(2)}ms to render`);
      }
    };
  });
}

export default {
  MemoizedCard,
  useVirtualScroll,
  usePagination,
  useDebounce,
  useOptimizedFilter,
  useIntersectionObserver,
  LazyImage,
  PaginationControls,
  LoadingSkeleton,
  usePerformanceMonitor
};
