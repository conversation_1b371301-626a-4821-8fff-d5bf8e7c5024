'use client';

import { AdminLayout } from '@/components/admin/AdminLayout';
import { useState, useEffect } from 'react';
import { useAuthContext } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Eye,
  Clock,
  MapPin,
  Monitor,
  Activity,
  TrendingUp,
  Users,
  Lock,
  Zap
} from 'lucide-react';
import {
  SecurityEvent,
  ThreatAssessment,
  AccessPattern,
  SecurityMetrics,
  getSecurityEvents,
  getThreatAssessment,
  getAccessPatterns,
  getSecurityMetrics,
  resolveSecurityEvent
} from '@/lib/admin/securityService';

export default function SecurityMonitoringPage() {
  const { user, isAdmin } = useAuthContext();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [threatAssessment, setThreatAssessment] = useState<ThreatAssessment | null>(null);
  const [accessPatterns, setAccessPatterns] = useState<AccessPattern[]>([]);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load security data on user change
  useEffect(() => {
    if (user?.id && isAdmin) {
      loadSecurityData();
    }
  }, [user?.id, isAdmin]);

  const loadSecurityData = async () => {
    if (!user?.id) return;
    
    try {
      setError(null);
      const [events, assessment, patterns, metrics] = await Promise.all([
        getSecurityEvents(user.id, { limit: 50 }),
        getThreatAssessment(user.id),
        getAccessPatterns(user.id),
        getSecurityMetrics(user.id)
      ]);
      
      setSecurityEvents(events);
      setThreatAssessment(assessment);
      setAccessPatterns(patterns);
      setSecurityMetrics(metrics);
    } catch (err) {
      console.error('Error loading security data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load security data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSecurityData();
  };

  const handleResolveEvent = async (eventId: string) => {
    if (!user?.id) return;
    
    try {
      await resolveSecurityEvent(user.id, eventId, 'Resolved by admin');
      // Refresh events to show updated status
      const updatedEvents = await getSecurityEvents(user.id, { limit: 50 });
      setSecurityEvents(updatedEvents);
      
      // Refresh threat assessment
      const updatedAssessment = await getThreatAssessment(user.id);
      setThreatAssessment(updatedAssessment);
    } catch (err) {
      console.error('Error resolving security event:', err);
      setError(err instanceof Error ? err.message : 'Failed to resolve security event');
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (error) {
    return (
      <AdminLayout
        title="Security Monitoring"
        description="Monitor security events, threats, and access patterns"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Security Monitoring' }
        ]}
      >
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <XCircle className="h-12 w-12 text-red-500" />
          <h2 className="text-xl font-semibold text-red-600">Security Error</h2>
          <p className="text-muted-foreground text-center max-w-md">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Security Monitoring"
      description="Monitor security events, threats, and access patterns"
      breadcrumbs={[
        { label: 'Admin', href: '/admin' },
        { label: 'Security Monitoring' }
      ]}
    >
      <div className="flex flex-col md:flex-row justify-end items-center gap-4 mb-6">
        <Button onClick={handleRefresh} disabled={refreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh Data
        </Button>
      </div>

      {/* Threat Assessment Overview */}
      <Card className={`border-2 ${getRiskColor(threatAssessment?.overallRisk || 'low')}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Threat Assessment
            <Badge variant={threatAssessment?.overallRisk === 'critical' ? 'destructive' : 
                           threatAssessment?.overallRisk === 'high' ? 'secondary' : 'default'}>
              {threatAssessment?.overallRisk} risk
            </Badge>
          </CardTitle>
          <CardDescription>
            Current security posture and threat level analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{threatAssessment?.activeThreats}</div>
              <div className="text-sm text-muted-foreground">Active Threats</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{threatAssessment?.resolvedThreats}</div>
              <div className="text-sm text-muted-foreground">Resolved Threats</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{threatAssessment?.recentEvents.length}</div>
              <div className="text-sm text-muted-foreground">Recent Events</div>
            </div>
          </div>
          
          {threatAssessment?.recommendations && threatAssessment.recommendations.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">Security Recommendations:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {threatAssessment.recommendations.slice(0, 3).map((rec, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="events" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="patterns">Access Patterns</TabsTrigger>
          <TabsTrigger value="assessment">Risk Assessment</TabsTrigger>
        </TabsList>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Recent Security Events
              </CardTitle>
              <CardDescription>
                Latest security events and incidents requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {securityEvents.map((event) => (
                  <div key={event.id} className="flex items-start justify-between p-4 border rounded-lg">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        {getSeverityIcon(event.severity)}
                        <h4 className="font-medium">{event.description}</h4>
                        <Badge variant="outline" className="text-xs">
                          {event.type.replace('_', ' ').toLowerCase()}
                        </Badge>
                        {event.resolved && (
                          <Badge variant="default" className="text-xs bg-green-500">
                            Resolved
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div className="flex items-center gap-4">
                          <span>Time: {new Date(event.timestamp).toLocaleString()}</span>
                          {event.ipAddress && <span>IP: {event.ipAddress}</span>}
                          {event.userId && <span>User: {event.userId}</span>}
                        </div>
                        {event.notes && (
                          <div className="text-green-600">Notes: {event.notes}</div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <div className={`w-3 h-3 rounded-full ${getSeverityColor(event.severity)}`} />
                      {!event.resolved && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleResolveEvent(event.id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Resolve
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-6">
          {/* Security Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Events</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityMetrics?.totalEvents}</div>
                <p className="text-xs text-muted-foreground">
                  {securityMetrics?.eventsToday} today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Logins</CardTitle>
                <Lock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityMetrics?.failedLogins}</div>
                <p className="text-xs text-muted-foreground">
                  Authentication failures
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Blocked Requests</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityMetrics?.blockedRequests}</div>
                <p className="text-xs text-muted-foreground">
                  Rate limited
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Suspicious Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityMetrics?.suspiciousUsers}</div>
                <p className="text-xs text-muted-foreground">
                  Avg risk: {securityMetrics?.averageRiskScore}%
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Top Threats Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Top Security Threats
              </CardTitle>
              <CardDescription>
                Most common security events by type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {securityMetrics?.topThreats.map((threat, index) => (
                  <div key={threat.type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-sm font-medium">{index + 1}.</span>
                      <span className="text-sm">{threat.type.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">{threat.count} events</span>
                      <span className="text-sm font-medium">{threat.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                User Access Patterns
              </CardTitle>
              <CardDescription>
                Analysis of user login patterns and suspicious activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {accessPatterns.map((pattern) => (
                  <div key={pattern.userId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{pattern.username}</h4>
                        {pattern.suspiciousActivity && (
                          <Badge variant="destructive" className="text-xs">
                            Suspicious
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          Risk: {pattern.riskScore}%
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div className="flex items-center gap-4">
                          <span>Logins: {pattern.loginCount}</span>
                          <span>Last: {new Date(pattern.lastLogin).toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          <span>Locations: {pattern.locations.join(', ')}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Eye className="h-3 w-3" />
                          <span>IPs: {pattern.ipAddresses.length} unique</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <div className={`w-3 h-3 rounded-full ${
                        pattern.riskScore > 70 ? 'bg-red-500' :
                        pattern.riskScore > 40 ? 'bg-yellow-500' : 'bg-green-500'
                      }`} />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assessment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Risk Factors Analysis
              </CardTitle>
              <CardDescription>
                Detailed analysis of current security risk factors
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {threatAssessment?.riskFactors.map((factor, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <h4 className="font-medium">{factor.factor}</h4>
                      <p className="text-sm text-muted-foreground">{factor.description}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={factor.level === 'high' ? 'destructive' : 
                                   factor.level === 'medium' ? 'secondary' : 'default'}>
                        {factor.level} risk
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security Recommendations</CardTitle>
              <CardDescription>
                Recommended actions to improve security posture
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {threatAssessment?.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                    <span className="text-sm">{recommendation}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
}
