// Content Moderation Service for Admin System
// Handles review moderation, flagging, and content management

import { createClient } from '@/lib/supabase/client';
import { logAdminAction, AdminAction } from '@/lib/audit/adminActions';

// Types for content moderation
export interface ReviewModerationData {
  id: string;
  title: string;
  slug: string;
  game_name: string;
  author_id: string;
  author_name: string;
  status: 'draft' | 'published' | 'pending' | 'flagged' | 'archived';
  overall_score: number;
  content_lexical: any;
  created_at: string;
  updated_at: string;
  publish_date?: string;
  flag_count?: number;
  moderation_notes?: string;
  last_moderated_by?: string;
  last_moderated_at?: string;
  platforms?: string[];
  tags?: string[];
  view_count?: number;
  like_count?: number;
  comment_count?: number;
  is_featured?: boolean;
}

export interface ModerationAction {
  action: 'approve' | 'reject' | 'flag' | 'feature' | 'unfeature' | 'archive';
  reason?: string;
  notes?: string;
}

export interface ContentFlag {
  id: string;
  content_id: string;
  content_type: 'review' | 'comment';
  reporter_id: string;
  reason: string;
  description?: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
  resolved_by?: string;
  resolved_at?: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Use the is_admin() RPC function for verification
    const { data, error } = await supabase.rpc('is_admin', { user_id: userId });

    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Get reviews for moderation queue with filters
export async function getReviewsForModeration(
  adminUserId: string,
  options: {
    status?: string[];
    page?: number;
    limit?: number;
    sortBy?: 'created_at' | 'updated_at' | 'flag_count';
    sortOrder?: 'asc' | 'desc';
    search?: string;
  } = {}
): Promise<{ reviews: ReviewModerationData[]; total: number; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { reviews: [], total: 0, error: 'Unauthorized: Admin access required' };
    }

    const supabase = createClient();
    const {
      status = ['pending', 'flagged', 'published'],
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search = ''
    } = options;

    // Build query for reviews
    let query = supabase
      .from('reviews')
      .select(`
        id,
        title,
        slug,
        game_name,
        author_id,
        author_name,
        status,
        overall_score,
        content_lexical,
        created_at,
        updated_at,
        publish_date,
        platforms,
        tags,
        view_count,
        like_count,
        comment_count,
        is_featured,
        profiles:author_id (
          username,
          display_name,
          avatar_url
        )
      `, { count: 'exact' });

    // Apply status filter
    if (status.length > 0) {
      query = query.in('status', status);
    }

    // Apply search filter
    if (search.trim()) {
      query = query.or(`title.ilike.%${search}%,game_name.ilike.%${search}%,author_name.ilike.%${search}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: reviews, error, count } = await query;

    if (error) {
      console.error('Error fetching reviews for moderation:', error);
      return { reviews: [], total: 0, error: error.message };
    }

    // Transform data for moderation interface
    const moderationReviews: ReviewModerationData[] = (reviews || []).map(review => ({
      id: review.id,
      title: review.title,
      slug: review.slug,
      game_name: review.game_name,
      author_id: review.author_id,
      author_name: review.author_name,
      status: review.status as any,
      overall_score: review.overall_score * 10, // Convert to 0-100 scale
      content_lexical: review.content_lexical,
      created_at: review.created_at,
      updated_at: review.updated_at,
      publish_date: review.publish_date,
      platforms: review.platforms || [],
      tags: review.tags || [],
      view_count: review.view_count || 0,
      like_count: review.like_count || 0,
      comment_count: review.comment_count || 0,
      is_featured: review.is_featured || false,
      flag_count: 0, // TODO: Implement flag counting
      moderation_notes: '', // TODO: Add moderation_notes column
      last_moderated_by: '', // TODO: Add last_moderated_by column
      last_moderated_at: '' // TODO: Add last_moderated_at column
    }));

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_QUEUE, {
      filters: options,
      results_count: moderationReviews.length
    });

    return {
      reviews: moderationReviews,
      total: count || 0
    };

  } catch (error) {
    console.error('Error in getReviewsForModeration:', error);
    return {
      reviews: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Moderate a review (approve, reject, flag, etc.)
export async function moderateReview(
  adminUserId: string,
  reviewId: string,
  action: ModerationAction
): Promise<{ success: boolean; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    const supabase = createClient();

    // Prepare update data based on action
    let updateData: any = {
      updated_at: new Date().toISOString()
    };

    switch (action.action) {
      case 'approve':
        updateData.status = 'published';
        updateData.publish_date = new Date().toISOString();
        break;
      case 'reject':
        updateData.status = 'draft';
        break;
      case 'flag':
        updateData.status = 'flagged';
        break;
      case 'feature':
        updateData.is_featured = true;
        break;
      case 'unfeature':
        updateData.is_featured = false;
        break;
      case 'archive':
        updateData.status = 'archived';
        break;
    }

    // Update the review
    const { error } = await supabase
      .from('reviews')
      .update(updateData)
      .eq('id', reviewId);

    if (error) {
      console.error('Error moderating review:', error);
      return { success: false, error: error.message };
    }

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.MODERATE_CONTENT, {
      review_id: reviewId,
      action: action.action,
      reason: action.reason,
      notes: action.notes
    });

    return { success: true };

  } catch (error) {
    console.error('Error in moderateReview:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Get review details for admin editing
export async function getReviewForAdmin(
  adminUserId: string,
  reviewId: string
): Promise<{ review?: ReviewModerationData; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { error: 'Unauthorized: Admin access required' };
    }

    const supabase = createClient();

    const { data: review, error } = await supabase
      .from('reviews')
      .select(`
        *,
        profiles:author_id (
          username,
          display_name,
          avatar_url,
          slug
        ),
        games:game_id (
          name,
          cover_url,
          igdb_id
        )
      `)
      .eq('id', reviewId)
      .single();

    if (error) {
      console.error('Error fetching review for admin:', error);
      return { error: error.message };
    }

    if (!review) {
      return { error: 'Review not found' };
    }

    // Transform for admin interface
    const moderationReview: ReviewModerationData = {
      id: review.id,
      title: review.title,
      slug: review.slug,
      game_name: review.game_name,
      author_id: review.author_id,
      author_name: review.author_name,
      status: review.status,
      overall_score: review.overall_score * 10, // Convert to 0-100 scale
      content_lexical: review.content_lexical,
      created_at: review.created_at,
      updated_at: review.updated_at,
      publish_date: review.publish_date,
      platforms: review.platforms || [],
      tags: review.tags || [],
      view_count: review.view_count || 0,
      like_count: review.like_count || 0,
      comment_count: review.comment_count || 0,
      is_featured: review.is_featured || false,
      flag_count: 0, // TODO: Implement flag counting
      moderation_notes: '', // TODO: Add moderation_notes column
      last_moderated_by: '', // TODO: Add last_moderated_by column
      last_moderated_at: '' // TODO: Add last_moderated_at column
    };

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_CONTENT_DETAILS, {
      review_id: reviewId
    });

    return { review: moderationReview };

  } catch (error) {
    console.error('Error in getReviewForAdmin:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Batch moderate multiple reviews
export async function batchModerateReviews(
  adminUserId: string,
  reviewIds: string[],
  action: ModerationAction
): Promise<{ success: boolean; processed: number; errors: string[] }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, processed: 0, errors: ['Unauthorized: Admin access required'] };
    }

    const results = [];
    const errors = [];

    // Process each review individually to handle partial failures
    for (const reviewId of reviewIds) {
      const result = await moderateReview(adminUserId, reviewId, action);
      if (result.success) {
        results.push(reviewId);
      } else {
        errors.push(`Review ${reviewId}: ${result.error}`);
      }
    }

    // Log batch action
    await logAdminAction(adminUserId, AdminAction.BATCH_MODERATE_CONTENT, {
      review_ids: reviewIds,
      action: action.action,
      processed: results.length,
      errors: errors.length
    });

    return {
      success: errors.length === 0,
      processed: results.length,
      errors
    };

  } catch (error) {
    console.error('Error in batchModerateReviews:', error);
    return {
      success: false,
      processed: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}
