'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Twitter, 
  MessageSquare, 
  Facebook, 
  Linkedin,
  ExternalLink,
  Calendar,
  User
} from 'lucide-react';
import { type SocialPreview } from '@/lib/social/meta-generator';

interface SocialMediaPreviewProps {
  previews: SocialPreview[];
  className?: string;
}

const SocialMediaPreview: React.FC<SocialMediaPreviewProps> = ({ 
  previews, 
  className = '' 
}) => {
  if (!previews || previews.length === 0) {
    return null;
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return <Twitter className="h-4 w-4" />;
      case 'discord':
        return <MessageSquare className="h-4 w-4" />;
      case 'facebook':
        return <Facebook className="h-4 w-4" />;
      case 'linkedin':
        return <Linkedin className="h-4 w-4" />;
      default:
        return <ExternalLink className="h-4 w-4" />;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'discord':
        return 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30';
      case 'facebook':
        return 'bg-blue-600/20 text-blue-400 border-blue-600/30';
      case 'linkedin':
        return 'bg-blue-700/20 text-blue-400 border-blue-700/30';
      default:
        return 'bg-slate-500/20 text-slate-400 border-slate-500/30';
    }
  };

  const formatPlatformName = (platform: string) => {
    return platform.charAt(0).toUpperCase() + platform.slice(1);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <div className="p-2 rounded-md bg-slate-800/60">
          <ExternalLink className="h-4 w-4 text-cyan-400" />
        </div>
        <div>
          <div className="font-mono text-sm text-slate-300">Social Media Previews</div>
          <p className="text-slate-500 text-xs">
            How your review will appear when shared
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {previews.map((preview, index) => (
          <Card 
            key={`${preview.platform}-${index}`}
            className="bg-slate-900/60 border-slate-700/50 hover:border-slate-600/50 transition-colors"
          >
            <CardContent className="p-4">
              {/* Platform Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Badge 
                    variant="outline" 
                    className={`${getPlatformColor(preview.platform)} font-mono text-xs`}
                  >
                    {getPlatformIcon(preview.platform)}
                    <span className="ml-1">{formatPlatformName(preview.platform)}</span>
                  </Badge>
                </div>
                {preview.timestamp && (
                  <div className="flex items-center gap-1 text-xs text-slate-500">
                    <Calendar className="h-3 w-3" />
                    {preview.timestamp}
                  </div>
                )}
              </div>

              {/* Preview Content */}
              <div className="space-y-3">
                {/* Author */}
                {preview.author && (
                  <div className="flex items-center gap-2 text-xs text-slate-400">
                    <User className="h-3 w-3" />
                    {preview.author}
                  </div>
                )}

                {/* Title */}
                <h4 className="font-semibold text-slate-200 text-sm leading-tight">
                  {preview.title}
                </h4>

                {/* Description */}
                <p className="text-slate-400 text-xs leading-relaxed">
                  {preview.description}
                </p>

                {/* Image Preview */}
                {preview.image && (
                  <div className="relative rounded-md overflow-hidden bg-slate-800/50 border border-slate-700/50">
                    <div className="aspect-video flex items-center justify-center">
                      <div className="text-slate-500 text-xs font-mono">
                        📷 Image Preview
                      </div>
                    </div>
                  </div>
                )}

                {/* URL Preview */}
                {preview.url && (
                  <div className="text-xs text-slate-500 font-mono truncate">
                    {preview.url}
                  </div>
                )}

                {/* Platform-specific elements */}
                {preview.platform === 'twitter' && (
                  <div className="flex items-center gap-4 text-xs text-slate-500 pt-2 border-t border-slate-700/50">
                    <span>💬 Reply</span>
                    <span>🔄 Retweet</span>
                    <span>❤️ Like</span>
                    <span>📤 Share</span>
                  </div>
                )}

                {preview.platform === 'discord' && (
                  <div className="flex items-center gap-4 text-xs text-slate-500 pt-2 border-t border-slate-700/50">
                    <span>👍 React</span>
                    <span>💬 Reply</span>
                    <span>📌 Pin</span>
                  </div>
                )}

                {preview.platform === 'facebook' && (
                  <div className="flex items-center gap-4 text-xs text-slate-500 pt-2 border-t border-slate-700/50">
                    <span>👍 Like</span>
                    <span>💬 Comment</span>
                    <span>📤 Share</span>
                  </div>
                )}

                {preview.platform === 'linkedin' && (
                  <div className="flex items-center gap-4 text-xs text-slate-500 pt-2 border-t border-slate-700/50">
                    <span>👍 Like</span>
                    <span>💬 Comment</span>
                    <span>📤 Repost</span>
                    <span>📨 Send</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tips */}
      <div className="bg-slate-800/30 border border-slate-700/30 rounded-md p-3">
        <div className="text-xs text-slate-400">
          <span className="text-slate-300 font-mono">💡 Tips:</span>
          <ul className="mt-1 space-y-1 ml-4">
            <li>• Keep titles under 70 characters for better visibility</li>
            <li>• Use engaging descriptions that encourage clicks</li>
            <li>• Add relevant images to increase engagement</li>
            <li>• Test your content across different platforms</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SocialMediaPreview;
