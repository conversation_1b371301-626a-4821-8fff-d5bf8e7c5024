// src/app/api/tags/[id]/reviews/route.ts
// Get reviews for a specific tag

import { NextRequest, NextResponse } from 'next/server';
import { createServerTagService } from '@/lib/services/tagService';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tagService = createServerTagService();

    const tagId = params.id;
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // First, verify the tag exists and get its info
    let tagResult;
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tagId);
    
    if (isUUID) {
      tagResult = await tagService.getTagById(tagId);
    } else {
      tagResult = await tagService.getTagBySlug(tagId);
    }

    if (!tagResult.success || !tagResult.tag) {
      return NextResponse.json(
        { error: 'Tag not found' },
        { status: 404 }
      );
    }

    // Get reviews for this tag
    const reviewsResult = await tagService.getTagReviews(
      tagResult.tag.id,
      limit,
      offset
    );

    if (!reviewsResult.success) {
      return NextResponse.json(
        { error: reviewsResult.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      tag: tagResult.tag,
      reviews: reviewsResult.reviews,
      total: reviewsResult.total,
      pagination: {
        limit,
        offset,
        hasMore: (reviewsResult.total || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Error in tag reviews API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}