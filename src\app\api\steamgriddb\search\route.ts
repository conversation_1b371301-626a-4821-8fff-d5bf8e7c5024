import { NextRequest, NextResponse } from 'next/server';
import { searchSteamGridDBGames } from '@/lib/steamgriddb-api';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Invalid search query' },
        { status: 400 }
      );
    }

    const games = await searchSteamGridDBGames(query);

    return NextResponse.json({
      success: true,
      data: games
    });
  } catch (error) {
    console.error('SteamGridDB search error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to search games on SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query) {
    return NextResponse.json(
      { error: 'Missing search query parameter' },
      { status: 400 }
    );
  }

  try {
    const games = await searchSteamGridDBGames(query);

    return NextResponse.json({
      success: true,
      data: games
    });
  } catch (error) {
    console.error('SteamGridDB search error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to search games on SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}