-- User Images Table Migration for B2 Cloud Storage Integration
-- Date: January 25, 2025
-- Purpose: Create database structure for tracking uploaded images with B2 cloud storage

-- Create the main user_images table
CREATE TABLE IF NOT EXISTS user_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- B2 Storage Information
  b2_file_id TEXT NOT NULL,
  b2_url TEXT NOT NULL,
  b2_key TEXT NOT NULL,
  
  -- File Metadata
  original_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  mime_type TEXT NOT NULL,
  checksum TEXT,
  
  -- Image Variants (for optimization)
  variants JSONB DEFAULT '{}',
  
  -- Security and Validation
  security_scan_result JSONB,
  is_safe BOOLEAN DEFAULT true,
  
  -- Usage Tracking
  usage_context TEXT, -- 'review', 'profile', 'comment', etc.
  reference_id UUID, -- ID of the review, profile, etc. that uses this image
  
  -- Additional Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_images_user_id ON user_images(user_id);
CREATE INDEX IF NOT EXISTS idx_user_images_created_at ON user_images(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_images_checksum ON user_images(checksum);
CREATE INDEX IF NOT EXISTS idx_user_images_b2_key ON user_images(b2_key);
CREATE INDEX IF NOT EXISTS idx_user_images_reference ON user_images(usage_context, reference_id);

-- Enable Row Level Security
ALTER TABLE user_images ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own images" ON user_images
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own images" ON user_images
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own images" ON user_images
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own images" ON user_images
  FOR DELETE USING (auth.uid() = user_id);

-- Admin policies for moderation
CREATE POLICY "Admins can view all images" ON user_images
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.admin_level IN ('SUPER_ADMIN', 'ADMIN', 'MODERATOR')
    )
  );

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_user_images_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_images_updated_at
  BEFORE UPDATE ON user_images
  FOR EACH ROW
  EXECUTE FUNCTION update_user_images_updated_at();

-- Create function to clean up orphaned images
CREATE OR REPLACE FUNCTION cleanup_orphaned_images()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete images older than 30 days with no reference
  DELETE FROM user_images 
  WHERE reference_id IS NULL 
    AND created_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Verify table creation
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_images') THEN
        RAISE EXCEPTION 'user_images table was not created';
    END IF;
    
    RAISE NOTICE 'User images table migration completed successfully!';
END
$$;
