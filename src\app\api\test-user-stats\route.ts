import { NextRequest, NextResponse } from 'next/server';
import { getUserStats } from '@/lib/services/userStatsService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'userId is required' }, { status: 400 });
    }

    console.log('🧪 Testing user stats for userId:', userId);
    
    const stats = await getUserStats(userId);
    
    console.log('📊 User stats result:', stats);
    
    return NextResponse.json({ 
      success: true, 
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in test-user-stats API:', error);
    return NextResponse.json({ 
      error: 'Failed to get user stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
