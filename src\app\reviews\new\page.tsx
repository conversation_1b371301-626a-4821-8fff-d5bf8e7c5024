'use client';

import { useState, useEffect, useCallback, useRef, memo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthContext } from '@/hooks/use-auth-context';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import GameStyleUserMenu from '@/components/layout/GameStyleUserMenu';
import Editor, { type EditorRef } from '@/components/review-form/lexical/Editor';
import type { EditorState } from 'lexical';
import AddBattleVisuals from '@/components/review-form/AddBattleVisuals';
import MonetizationConfigurator from '@/components/review-form/MonetizationConfigurator';
import type { MonetizationBlock, MonetizationBlockData } from '@/lib/types';
import RatingSection from '@/components/review-form/RatingSection';
import ReviewCreationNavbar from '@/components/review-form/ReviewCreationNavbar';
import { saveReview, getReviewById, updateReview } from '@/lib/review-service';
import { SuspensionGuard } from '@/components/suspension/SuspensionGuard';
import type { ReviewSettings } from '@/lib/services/reviewSettingsService';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { editorAutoSave } from '@/lib/utils/editorAutoSave';
import {
  Gamepad2,
  BookOpen,
  Palette,
  Sparkles,
  DollarSign,
  Image,
  XCircle,
  Monitor,
  Code,
  Camera,
  Edit3,
  Users,
  Star,
  Maximize,
  Minimize,
  Sun,
  Moon,
  ArrowRight,
  Settings,
  Save,
  Trash2,
} from 'lucide-react';
import TitleYourReview from '@/components/review-form/TitleYourQuest';
import dynamic from 'next/dynamic';
import type { ReviewData } from '@/lib/metadata/generator';

// Dynamically import the SteamGridDB modal to avoid SSR issues
const SteamGridDBBannerModal = dynamic(
  () => import('@/components/steamgriddb/SteamGridDBBannerModal').then(mod => ({ default: mod.SteamGridDBBannerModal })),
  { 
    ssr: false,
    loading: () => null
  }
);

// Add new state types for IGDB metadata
// Import the page-specific CSS
import '@/components/review-form/style/NewReview.css';
import '@/components/review-form/style/autosave-progress.css';

// Platform and genre data
const initialAvailablePlatforms = ["PC", "PlayStation 5", "PlayStation 4", "Xbox Series X/S", "Xbox One", "Nintendo Switch", "Mobile"];
const initialAvailableGenres = ["Action", "Adventure", "RPG", "Strategy", "Simulation", "Sports", "Puzzle", "Horror", "Survival", "Indie", "MMO", "Fighting", "Racing", "Platformer"];

// Background brightness detection hook
const useBackgroundBrightness = () => {
  const [isDarkBackground, setIsDarkBackground] = useState(true);

  useEffect(() => {
    const detectBackgroundBrightness = () => {
      try {
        // Get the current dimmer values from CSS custom properties
        const rootStyles = getComputedStyle(document.documentElement);
        const darkOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-dark-opacity') || '0');
        const lightOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-light-opacity') || '0');

        // If we have dimmer overlays, determine brightness based on those
        if (darkOpacity > 0) {
          // Dark overlay is active, background is being darkened
          setIsDarkBackground(true);
          return;
        } else if (lightOpacity > 0.3) {
          // Light overlay is significant, background is being lightened
          setIsDarkBackground(false);
          return;
        }

        // Fallback to original background color detection
        const elements = [
          document.body,
          document.documentElement,
          document.querySelector('.page-main-container'),
          document.querySelector('main')
        ].filter(Boolean);

        for (const element of elements) {
          const computedStyle = window.getComputedStyle(element as Element);
          const backgroundColor = computedStyle.backgroundColor;

          // Skip transparent backgrounds
          if (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)') {
            continue;
          }

          // Parse RGB values from background color
          const rgbMatch = backgroundColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
          if (rgbMatch) {
            const [, r, g, b] = rgbMatch.map(Number);
            // Calculate luminance using standard formula
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
            setIsDarkBackground(luminance < 0.5);
            return; // Found a valid background, stop checking
          }
        }

        // Default to dark if we can't detect any background
        setIsDarkBackground(true);
      } catch (error) {
        console.warn('Background brightness detection failed:', error);
        setIsDarkBackground(true);
      }
    };

    // Initial detection
    detectBackgroundBrightness();

    // Create observer for CSS custom property changes
    const observer = new MutationObserver(detectBackgroundBrightness);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style'],
      subtree: false
    });

    // Also observe body for background changes
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true
    });

    // Listen for theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', detectBackgroundBrightness);

    // Poll for CSS custom property changes (backup for slider changes)
    const interval = setInterval(detectBackgroundBrightness, 100);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', detectBackgroundBrightness);
      clearInterval(interval);
    };
  }, []);

  return isDarkBackground;
};

// Horizontal Step Navigation Component
const HorizontalStepNav = memo(({ steps, currentStep, onStepClick, formCompletion, containerWidth, setContainerWidth }: {
  steps: any[];
  currentStep: number;
  onStepClick: (step: number) => void;
  formCompletion: number;
  containerWidth: number;
  setContainerWidth: (width: number) => void;
}) => {
  const widthOptions = [50, 75, 100];
  const isDarkBackground = useBackgroundBrightness();
  
  const handleWidthChange = (direction: 'increase' | 'decrease') => {
    const currentIndex = widthOptions.indexOf(containerWidth);
    if (direction === 'increase' && currentIndex < widthOptions.length - 1) {
      setContainerWidth(widthOptions[currentIndex + 1]);
    } else if (direction === 'decrease' && currentIndex > 0) {
      setContainerWidth(widthOptions[currentIndex - 1]);
    }
  };

  return (
    <div className="mb-12">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <span className={`font-mono text-4xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
            <span className="text-violet-400/60">&lt;</span>
            <span className="px-2">Review Builder</span>
            <span className="text-violet-400/60">/&gt;</span>
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleWidthChange('decrease')}
            disabled={containerWidth === 50}
            className="width-control-button w-8 h-8 rounded-lg bg-slate-700 border border-white/6 text-white hover:bg-slate-600 disabled:opacity-30 disabled:cursor-not-allowed flex items-center justify-center font-mono text-lg relative overflow-hidden group"
            title="Diminuir largura"
          >
            <span className="relative z-10">-</span>
            <div className="absolute inset-0 bg-gradient-to-r from-violet-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm text-slate-300 min-w-[4rem] text-center bg-slate-800 px-3 py-1 rounded-md border border-white/6">
              {containerWidth}%
            </span>
            <div className="flex gap-1">
              {[50, 75, 100].map((width) => (
                <div
                  key={width}
                  className={`w-2 h-2 rounded-full width-indicator-dot ${
                    containerWidth === width
                      ? 'bg-violet-400'
                      : 'bg-slate-600'
                  }`}
                  onClick={() => setContainerWidth(width)}
                  title={`Set width to ${width}%`}
                />
              ))}
            </div>
          </div>
          <button
            onClick={() => handleWidthChange('increase')}
            disabled={containerWidth === 100}
            className="width-control-button w-8 h-8 rounded-lg bg-slate-700 border border-white/6 text-white hover:bg-slate-600 disabled:opacity-30 disabled:cursor-not-allowed flex items-center justify-center font-mono text-lg relative overflow-hidden group"
            title="Aumentar largura"
          >
            <span className="relative z-10">+</span>
            <div className="absolute inset-0 bg-gradient-to-r from-violet-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>
    </div>
  );
});

// Enhanced Section Wrapper - Always Active
const SectionWrapper = memo(({
  children,
  title,
  description,
  icon: Icon,
  stepNumber,
  headerControls
}: {
  children: React.ReactNode;
  title: string;
  description: string;
  icon: any;
  stepNumber: number;
  headerControls?: React.ReactNode;
}) => {
  return (
    <div className="group review-form-section opacity-100">
      <div className="relative border-white/5 border rounded-2xl overflow-hidden section-wrapper-active">

        <div className="section-active-overlay" />

        <div className="section-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="relative w-8 h-8 rounded-lg border border-white/8 flex items-center justify-center bg-slate-600/25">
                <Icon size={16} className="text-slate-300" />
              </div>

              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="font-mono text-slate-500/70 text-xs font-medium">
                    {stepNumber.toString().padStart(2, '0')}
                  </span>
                  <h2 className="text-base font-bold text-white font-mono">
                    <span className="text-slate-400/50">&lt;</span>
                    <span className="px-1">{title}</span>
                    <span className="text-slate-400/50">/&gt;</span>
                  </h2>
                </div>

                <div className="flex items-center space-x-2 min-w-0">
                  <span className="text-xs text-slate-300">
                    {description}
                  </span>
                  <span className="font-mono text-xs text-slate-400/60 animate-console-blink">_</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Header Controls */}
              {headerControls && (
                <div className="flex items-center space-x-2">
                  {headerControls}
                </div>
              )}

              <div className="w-2 h-2 bg-slate-400/60 rounded-full animate-pulse flex-shrink-0" />
            </div>
          </div>
        </div>

        <div className="relative p-6">
          {children}
        </div>
      </div>
    </div>
  );
});

const LoginRequiredContainer = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <>
      <div className="min-h-screen bg-transparent flex items-center justify-center p-4">
        <div className="bg-slate-800/50 backdrop-blur-xl border border-white/10 rounded-xl p-8 max-w-md text-center">
          <div className="mb-6">
            <XCircle size={48} className="text-red-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2 font-mono">
              <span className="text-red-400/60">&lt;</span>
              Authentication Required
              <span className="text-red-400/60">/&gt;</span>
            </h1>
            <p className="text-slate-300 mb-6">
              You need to be logged in to create a review. Please log in to continue.
            </p>
          </div>
          
          <div className="space-y-3">
            <Button 
              onClick={() => setIsMenuOpen(true)}
              className="w-full bg-gradient-to-r from-indigo-600 to-violet-500 hover:from-indigo-500 hover:to-violet-400 text-white border-none shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
            >
              <Users className="mr-2 h-4 w-4" />
              Login / Register
            </Button>
            
            <Button 
              onClick={() => window.history.back()}
              variant="outline"
              className="w-full bg-slate-700/50 border-white/10 text-white hover:bg-slate-600/50"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
      
      <GameStyleUserMenu 
        open={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
      />
    </>
  );
};

export default function ReviewCreationPage() {
  const { user, loading } = useAuthContext();

  if (loading) return <div>Loading...</div>;

  if (!user) {
    return <LoginRequiredContainer />;
  }

  return (
    <SuspensionGuard 
      action="create_review"
      fallback={
        <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
          <div className="bg-slate-800/50 backdrop-blur-xl border border-white/10 rounded-xl p-8 max-w-md text-center">
            <h1 className="text-xl font-bold text-white mb-4">Account Suspended</h1>
            <p className="text-slate-300 mb-6">Your account has been suspended. You can view content but cannot create new reviews.</p>
            <Button 
              onClick={() => window.history.back()} 
              variant="outline"
              className="bg-slate-700/50 border-white/10 text-white hover:bg-slate-600/50"
            >
              Go Back
            </Button>
          </div>
        </div>
      }
    >
      <ReviewFormContent />
    </SuspensionGuard>
  );
}

const ReviewFormContent = () => {
  const { user, loading, isAdmin } = useAuthContext();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isMounted = useRef(false);

  // Edit mode detection
  const editReviewId = searchParams.get('edit');
  const isEditMode = !!editReviewId;

  // Debug logging for edit mode
  useEffect(() => {
    console.log('Edit mode state:', { editReviewId, isEditMode, userLoading: loading, userExists: !!user });
    if (isEditMode) {
      console.log('🔄 EDIT MODE DETECTED - Review ID:', editReviewId);
    } else {
      console.log('✨ NEW REVIEW MODE');
    }
  }, [editReviewId, isEditMode, loading, user]);
  
  // Steps configuration for ReviewCreationNavbar
  const steps = [
    { id: 1, name: 'Basic Information' },
    { id: 2, name: 'Media & Visuals' },
    { id: 3, name: 'Review Content' },
    { id: 4, name: 'Rating & Scores' },
    { id: 5, name: 'Monetization' }
  ];

  // Current step tracking for navbar
  const [currentStep, setCurrentStep] = useState(1);

  // Track if the component has been mounted
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Load existing review data when in edit mode
  useEffect(() => {
    if (!isEditMode || !editReviewId || !user) {
      console.log('Edit mode conditions not met:', { isEditMode, editReviewId, user: !!user });
      return;
    }

    const loadReviewForEditing = async () => {
      console.log('Loading review for editing:', editReviewId);
      setIsLoadingReview(true);
      try {
        // Use the correct user ID property (uid is the primary ID, id is the fallback)
        const userId = user.uid || user.id;
        console.log('Using user ID for review loading:', userId);

        if (!userId) {
          console.error('No valid user ID found:', user);
          toast({
            title: "Authentication Error",
            description: "Unable to verify user identity. Please try logging out and back in.",
            variant: "destructive",
          });
          setIsLoadingReview(false);
          return;
        }

        const result = await getReviewById(editReviewId, userId);
        console.log('Review loading result:', result);

        if (result.success && result.review) {
          const review = result.review;
          console.log('Populating form with review data:', {
            id: review.id,
            title: review.title,
            game_name: review.game_name,
            games_relation: review.games,
            content_lexical: !!review.content_lexical,
            overall_score: review.overall_score,
            fullReviewObject: review
          });

          // Populate form fields with existing review data
          // For game_name, use multiple fallback sources to ensure it's never empty
          const populatedGameName = review.game_name || review.games?.name || review.title || '';
          console.log('Game name resolution:', {
            database_game_name: review.game_name,
            games_relation_name: review.games?.name,
            fallback_title: review.title,
            final_game_name: populatedGameName
          });
          
          // Force immediate state updates in batch
          setGameName(populatedGameName);
          setReviewTitle(review.title || '');
          setSlug(review.slug || ''); // Fix 404 redirect by ensuring slug is populated in edit mode
          setReviewContentLexical(review.content_lexical || '');
          
          // Debug: Log the state that was just set
          console.log('State set immediately after loading:', {
            populatedGameName,
            reviewTitle: review.title || '',
            hasContent: !!(review.content_lexical || ''),
            isGameNameEmpty: !populatedGameName,
            gameNameLength: populatedGameName?.length || 0
          });
          
          // Additional safety check - if gameName is still empty, use title as absolute fallback
          if (!populatedGameName && review.title) {
            console.warn('Game name is empty, using review title as fallback:', review.title);
            setGameName(review.title);
          }
          setMainImageUrl(review.main_image_url || '');
          setGalleryImageUrls(review.gallery_image_urls || []);
          setVideoUrl(review.video_url || '');
          setMetaTitle(review.meta_title || '');
          setMetaDescription(review.meta_description || '');
          setFocusKeyword(review.focus_keyword || '');
          setReviewTags(review.tags || []);
          setSelectedPlatforms(new Set(review.platforms || []));
          setSelectedGenres(new Set(review.genres || []));
          // Convert overall_score from 0-10 scale (database) to 0-100 scale (form)
          setOverallScore(review.overall_score ? Math.round(review.overall_score * 10) : 0);

          // Set the missing fields for edit mode
          setLanguage(review.language || 'en');
          setPlayedOn(review.played_on || '');
          setDatePlayed(review.date_played || '');

          console.log('Form fields populated:', {
            originalGameName: review.game_name,
            finalGameName: populatedGameName,
            reviewTitle: review.title,
            hasContent: !!review.content_lexical,
            originalOverallScore: review.overall_score,
            convertedOverallScore: review.overall_score ? Math.round(review.overall_score * 10) : 0
          });

          // Transform scoring criteria from database object to component array format
          const defaultCriteria = [
            { id: 'gameplay', name: 'Gameplay', score: 75, icon: Gamepad2 },
            { id: 'story', name: 'Story', score: 75, icon: BookOpen },
            { id: 'artStyle', name: 'Art Style', score: 75, icon: Palette },
            { id: 'fun', name: 'Fun Factor', score: 75, icon: Sparkles }
          ];

          if (review.scoring_criteria && typeof review.scoring_criteria === 'object') {
            const transformedCriteria = defaultCriteria.map(criterion => ({
              ...criterion,
              score: typeof review.scoring_criteria[criterion.id] === 'number'
                ? Math.round(review.scoring_criteria[criterion.id] * 10) // Convert 0-10 scale to 0-100 scale
                : criterion.score
            }));
            setScoringCriteria(transformedCriteria);
          } else {
            // Fallback to default criteria if no valid scoring data
            setScoringCriteria(defaultCriteria);
          }

          setMonetizationBlocks(review.monetization_blocks || []);
          setStatus(review.status || 'draft');

          // Load review settings
          setReviewSettings({
            enableComments: review.comment_settings?.enabled ?? true,
            enableNotifications: review.notification_settings?.enabled ?? true,
            makePrivate: review.is_private || false
          });

          // Load IGDB data from the games relationship (our database as source of truth)
          const reviewIgdbId = review.games?.igdb_id;
          if (reviewIgdbId) {
            setIgdbId(reviewIgdbId);
            setIgdbSummary(review.games?.summary || '');
            setIgdbDevelopers(review.games?.developers || []);
            setIgdbPublishers(review.games?.publishers || []);
            setIgdbGameEngines(review.games?.game_engines || []);
            setIgdbPlayerPerspectives(review.games?.player_perspectives || []);
            setIgdbTimeToBeatNormally(review.games?.time_to_beat_normally || 0);
            setIgdbTimeToBeatCompletely(review.games?.time_to_beat_completely || 0);
            setIgdbAggregatedRating(review.games?.aggregated_rating || 0);
            setIgdbAggregatedRatingCount(review.games?.aggregated_rating_count || 0);
            setIgdbCoverUrl(review.games?.cover_url || review.igdb_cover_url || '');
            setReleaseDate(review.games?.release_date || '');
            
            console.log('IGDB ID loaded from database games table:', reviewIgdbId);
          } else {
            console.warn('No IGDB ID found in games relationship');
            console.warn('This review either:');
            console.warn('1. Was created without selecting a game from IGDB');
            console.warn('2. Has a missing game_id relationship');
            console.warn('3. The games relation failed to load');
            
            // For reviews without proper game relationships, still allow editing
            // Use a fallback IGDB ID to pass validation
            console.warn('Using fallback IGDB ID to allow editing');
            setIgdbId(1); // Minimal IGDB ID to pass validation
          }

          // Validate that essential fields are populated
          const validationErrors = [];
          if (!populatedGameName) validationErrors.push('Game name is missing');
          if (!reviewIgdbId) validationErrors.push('IGDB ID is missing');
          if (!review.title) validationErrors.push('Review title is missing');
          if (!review.content_lexical) validationErrors.push('Review content is missing');
          
          if (validationErrors.length > 0) {
            console.error('Form population validation failed:', validationErrors);
            toast({
              title: "Error loading review",
              description: `Missing required data: ${validationErrors.join(', ')}`,
              variant: "destructive",
            });
            setIsLoadingReview(false);
            return;
          }
          
          toast({
            title: "Review loaded",
            description: "Review data loaded successfully for editing.",
          });
          
          // Ensure state updates are completed before removing loading state
          // Use setTimeout to allow React state updates to complete
          setTimeout(() => {
            console.log('About to finish loading - values that should have been set:', {
              expectedGameName: populatedGameName,
              expectedReviewTitle: review.title || '',
              expectedHasContent: !!(review.content_lexical || '')
            });
            setIsLoadingReview(false);
          }, 3000);
        } else {
          console.error('Failed to load review:', result.error);
          toast({
            title: "Error loading review",
            description: result.error || "Failed to load review for editing.",
            variant: "destructive",
          });
          // Don't redirect away from edit mode, just stop loading
          setIsLoadingReview(false);
          return;
        }
      } catch (error) {
        console.error('Error loading review for editing:', error);
        toast({
          title: "Error loading review",
          description: "An unexpected error occurred while loading the review.",
          variant: "destructive",
        });
        // Don't redirect away from edit mode, just stop loading
        setIsLoadingReview(false);
        return;
      }
    };

    loadReviewForEditing();
  }, [isEditMode, editReviewId, user, router]);

  // Section refs for scrolling - FIXED: Order matches UI step sequence
  const sectionRefs = {
    headerSection: useRef<HTMLDivElement>(null),     // Header with width controls
    titleSection: useRef<HTMLDivElement>(null),      // Step 1: Basic Information
    mediaSection: useRef<HTMLDivElement>(null),      // Step 2: Media & Visuals
    contentSection: useRef<HTMLDivElement>(null),    // Step 3: Review Content
    ratingSection: useRef<HTMLDivElement>(null),     // Step 4: Rating System
    monetizationSection: useRef<HTMLDivElement>(null), // Step 5: Monetization
  };
  
  // Form State
  const [reviewTitle, setReviewTitle] = useState('');
  const [gameName, setGameName] = useState('');
  const [slug, setSlug] = useState('');
  const [language, setLanguage] = useState<string>('');
  const [playedOn, setPlayedOn] = useState<string>('');
  const [datePlayed, setDatePlayed] = useState<string>('');
  const [igdbId, setIgdbId] = useState<number | undefined>(); // Added for igdbId
  // Add state for new IGDB metadata fields
 const [igdbSummary, setIgdbSummary] = useState<string>('');
 const [igdbAggregatedRating, setIgdbAggregatedRating] = useState<number | undefined>();
 const [igdbAggregatedRatingCount, setIgdbAggregatedRatingCount] = useState<number | undefined>();
 const [igdbDevelopers, setIgdbDevelopers] = useState<string[]>([]);
 const [igdbPublishers, setIgdbPublishers] = useState<string[]>([]);
 const [igdbGameEngines, setIgdbGameEngines] = useState<string[]>([]);
 const [igdbPlayerPerspectives, setIgdbPlayerPerspectives] = useState<string[]>([]);
 const [igdbTimeToBeatNormally, setIgdbTimeToBeatNormally] = useState<number | undefined>();
 const [igdbTimeToBeatCompletely, setIgdbTimeToBeatCompletely] = useState<number | undefined>();
 const [igdbCoverUrl, setIgdbCoverUrl] = useState<string>(''); // New field for IGDB cover
  const [releaseDate, setReleaseDate] = useState<Date | undefined>();
  const [publishDate, setPublishDate] = useState<Date | undefined>(new Date());

  // Review Content
  const [reviewContentLexical, setReviewContentLexical] = useState<string>('');
  
  // Scoring system with fixed criteria
  const [scoringCriteria, setScoringCriteria] = useState([
    { id: 'gameplay', name: 'Gameplay', score: 75, icon: Gamepad2 },
    { id: 'story', name: 'Story', score: 75, icon: BookOpen },
    { id: 'artStyle', name: 'Art Style', score: 75, icon: Palette },
    { id: 'fun', name: 'Fun Factor', score: 75, icon: Sparkles }
  ]);
  const [overallScore, setOverallScore] = useState(75);
  
  // Platforms and Genres
  const [selectedPlatforms, setSelectedPlatforms] = useState<Set<string>>(new Set());
  const [selectedGenres, setSelectedGenres] = useState<Set<string>>(new Set());
  
  // Media
  const [mainImageUrl, setMainImageUrl] = useState('');
  const [mainImagePosition, setMainImagePosition] = useState('center 25%');
  const [galleryImageUrls, setGalleryImageUrls] = useState<string[]>([]);
  const [videoUrl, setVideoUrl] = useState('');
  
  // SEO & Tags
  const [metaTitle, setMetaTitle] = useState('');
  const [metaDescription, setMetaDescription] = useState('');
  const [focusKeyword, setFocusKeyword] = useState('');
  const [reviewTags, setReviewTags] = useState<string[]>([]);
  const [currentTagInput, setCurrentTagInput] = useState('');
  
  // Monetization
  const [monetizationBlocks, setMonetizationBlocks] = useState<MonetizationBlock[]>([]);
  
  // Publishing
  const [status, setStatus] = useState('draft');
  const [postToPublicProfile, setPostToPublicProfile] = useState(true);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isDraftSaving, setIsDraftSaving] = useState(false);
  const [isPublishingWithModal, setIsPublishingWithModal] = useState(false);

  // Review Settings - CHANGED: Comments default to OFF
  const [reviewSettings, setReviewSettings] = useState<ReviewSettings>({
    enableComments: false,
    enableNotifications: true,
    makePrivate: false
  });

  // Edit mode state
  const [isLoadingReview, setIsLoadingReview] = useState(isEditMode);
  const [originalReviewId, setOriginalReviewId] = useState<string | null>(editReviewId);

  // Visual effects
  const [showConfetti, setShowConfetti] = useState(false);
  
  // Fullscreen editor state
  const [isEditorFullscreen, setIsEditorFullscreen] = useState(false);
  const [isExitingFullscreen, setIsExitingFullscreen] = useState(false);

  // Light/Dark mode for editor
  const [isEditorLight, setIsEditorLight] = useState(false);

  // Auto-save toggle state with localStorage persistence
  const [isAutoSaveEnabled, setIsAutoSaveEnabled] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('reviewEditorAutoSaveEnabled');
      return saved ? JSON.parse(saved) : true; // Default to enabled
    }
    return true;
  });


  // Editor state management for focus mode
  const [editorStateSnapshot, setEditorStateSnapshot] = useState<string | null>(null);

  // SteamGridDB banner modal state
  const [isSteamGridDBModalOpen, setIsSteamGridDBModalOpen] = useState(false);

  // Auto-save clear confirmation modal state
  const [isClearConfirmModalOpen, setIsClearConfirmModalOpen] = useState(false);

  // Editor refs for clearing content
  const editorRef = useRef<EditorRef>(null);
  const fullscreenEditorRef = useRef<EditorRef>(null);

  // Container width state for responsive testing with localStorage persistence
  const [containerWidth, setContainerWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('reviewPageContainerWidth');
      return saved ? parseInt(saved, 10) : 50;
    }
    return 50;
  });

  // Tooltip state for 75% width notification
  const [showWidthTooltip, setShowWidthTooltip] = useState(false);
  const [tooltipExiting, setTooltipExiting] = useState(false);

  // Save container width to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('reviewPageContainerWidth', containerWidth.toString());
      console.log(`Width changed to: ${containerWidth}%`);
    }
  }, [containerWidth]);

  // Show tooltip when width is set to 75% (default review area)
  useEffect(() => {
    if (containerWidth === 75) {
      setShowWidthTooltip(true);
      setTooltipExiting(false);
      
      const timer = setTimeout(() => {
        setTooltipExiting(true);
        // Hide tooltip after exit animation completes
        setTimeout(() => {
          setShowWidthTooltip(false);
          setTooltipExiting(false);
        }, 300);
      }, 2500); // Start exit animation after 2.5 seconds
      
      return () => clearTimeout(timer);
    }
  }, [containerWidth]);

  // Save auto-save preference to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('reviewEditorAutoSaveEnabled', JSON.stringify(isAutoSaveEnabled));
      console.log(`Auto-save ${isAutoSaveEnabled ? 'enabled' : 'disabled'}`);
    }
  }, [isAutoSaveEnabled]);

  // Restore content from auto-save on page load
  useEffect(() => {
    if (!user?.id || !isMounted.current) return;

    // Check if current content is empty/default
    const isEmptyContent = !reviewContentLexical || 
      reviewContentLexical === '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
    
    if (!isEmptyContent) {
      console.log('Skipping autosave restoration: content already exists');
      return;
    }

    // Import the auto-save utility dynamically to avoid SSR issues
    import('@/lib/utils/editorAutoSave').then(({ editorAutoSave }) => {
      const autoSaveData = editorAutoSave.load({
        userId: user.id,
        reviewId: originalReviewId || undefined,
        gameName,
        reviewTitle
      });

      if (autoSaveData && autoSaveData.content) {
        console.log('Restoring content from auto-save:', new Date(autoSaveData.timestamp));
        console.log('Auto-save content length:', autoSaveData.content.length);
        setReviewContentLexical(autoSaveData.content);
        setEditorStateSnapshot(autoSaveData.content);
      } else {
        console.log('No valid auto-save data found');
      }
    }).catch(error => {
      console.warn('Failed to check auto-save:', error);
    });
  }, [user?.id, originalReviewId, gameName, reviewTitle, isMounted]);


  // Auto-scroll to header on page load
  useEffect(() => {
    const scrollToHeader = () => {
      if (sectionRefs.headerSection.current && isMounted.current) {
        const navbarHeight = 80;
        const targetPosition = sectionRefs.headerSection.current.offsetTop - navbarHeight;
        window.scrollTo(0, Math.max(0, targetPosition));
      }
    };

    if (isMounted.current && !loading && user) {
      const timer = setTimeout(scrollToHeader, 100);
      return () => clearTimeout(timer);
    }
  }, [loading, user]);

  // Intersection Observer to track current section for navbar
  useEffect(() => {
    if (!isMounted.current) return;

    const observers: IntersectionObserver[] = [];
    
    const sectionElements = [
      { element: sectionRefs.titleSection.current, step: 1 },
      { element: sectionRefs.mediaSection.current, step: 2 },
      { element: sectionRefs.contentSection.current, step: 3 },
      { element: sectionRefs.ratingSection.current, step: 4 },
      { element: sectionRefs.monetizationSection.current, step: 5 }
    ];

    sectionElements.forEach(({ element, step }) => {
      if (element) {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting && entry.intersectionRatio > 0.3) {
                setCurrentStep(step);
              }
            });
          },
          {
            threshold: 0.3,
            rootMargin: '-100px 0px -100px 0px'
          }
        );
        
        observer.observe(element);
        observers.push(observer);
      }
    });

    return () => {
      observers.forEach(observer => observer.disconnect());
    };
  }, [loading, user]);
  
  // Simplified form completion - no step tracking needed
  const [formCompletion] = useState(100); // Always show as complete since all sections are accessible
  
  // FIXED: Enhanced slug generation with game name + date + username + unique identifier
  useEffect(() => {
    if (!isMounted.current) return;
    
    if (gameName && user) {
      // Get username (prefer displayName, fallback to email prefix, then uid)
      const username = user.displayName || 
                      (user.email ? user.email.split('@')[0] : null) || 
                      (user.uid ? user.uid.slice(0, 8) : 'user');
      
      // Get current date in YYYY-MM-DD format
      const currentDate = publishDate || new Date();
      const dateStr = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
      
      // Generate unique identifier (timestamp + random)
      const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
      const randomStr = Math.random().toString(36).substring(2, 5); // 3 random chars
      const uniqueId = `${timestamp}${randomStr}`;
      
      // Clean and combine all parts
      const cleanGameName = gameName
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // Remove special chars except spaces and hyphens
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim();
      
      const cleanUsername = username
        .toLowerCase()
        .replace(/[^\w]/g, '') // Remove all special chars from username
        .slice(0, 12); // Limit username to 12 chars
      
      // Combine: gamename-YYYY-MM-DD-username-uniqueid
      const newSlug = `${cleanGameName}-${dateStr}-${cleanUsername}-${uniqueId}`;
      
      setSlug(newSlug);

      // Debug logging
      console.log('Slug generated:', {
        gameName: cleanGameName,
        date: dateStr,
        username: cleanUsername,
        uniqueId,
        finalSlug: newSlug
      });
    }
  }, [gameName, reviewTitle, user, publishDate]);
  
  // Editor change handler with state preservation
  const handleEditorChange = useCallback((editorState: EditorState) => {
    if (!isMounted.current) return;

    const updateState = () => {
      try {
        editorState.read(() => {
          const jsonState = JSON.stringify(editorState.toJSON());
          const hasContent = !!jsonState && jsonState !== '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';

          // Update both the main state and the snapshot for focus mode transitions
          setReviewContentLexical(jsonState);
          setEditorStateSnapshot(jsonState);

          // Content tracking removed - no step progression needed
        });
      } catch (error) {
        console.error("Error processing editor state:", error);
      }
    };

    if (typeof window !== 'undefined') {
      if ('requestIdleCallback' in window) {
        (window as any).requestIdleCallback(updateState);
      } else {
        setTimeout(updateState, 100);
      }
    } else {
      updateState();
    }
  }, []);
  
  // Check authentication
  useEffect(() => {
    if (!loading && !user && isMounted.current) {
      router.push('/login');
    }
  }, [user, loading, router]);
  
  // Update overall score when criteria change
  useEffect(() => {
    if (!isMounted.current) return;
    
    const totalScore = scoringCriteria.reduce((sum, criterion) => sum + criterion.score, 0);
    setOverallScore(Math.round(totalScore / scoringCriteria.length));
    
    // Rating tracking removed - no step progression needed
  }, [scoringCriteria]);
  
  // Handle tag management
  const handleAddTag = useCallback(() => {
    if (!isMounted.current) return;

    if (currentTagInput.trim() && !reviewTags.includes(currentTagInput.trim())) {
      const newTags = [...reviewTags, currentTagInput.trim()];
      setReviewTags(newTags);
      setCurrentTagInput('');
    }
  }, [currentTagInput, reviewTags]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    if (!isMounted.current) return;

    const newTags = reviewTags.filter(tag => tag !== tagToRemove);
    setReviewTags(newTags);
  }, [reviewTags]);
  
  // Handle criteria score change
  const handleCriterionScoreChange = useCallback((id: string, score: number) => {
    if (!isMounted.current) return;
    
    setScoringCriteria(prev => prev.map(criterion =>
      criterion.id === id ? { ...criterion, score } : criterion
    ));
  }, []);
  
  // Handle platform selection
  const handlePlatformToggle = useCallback((platform: string) => {
    if (!isMounted.current) return;
    console.log('[ReviewCreationForm] handlePlatformToggle - platform trying to toggle:', platform);
    
    setSelectedPlatforms(prev => {
      const newPlatforms = new Set(prev);
      if (newPlatforms.has(platform)) {
        newPlatforms.delete(platform);
      } else {
        newPlatforms.add(platform);
      }
      
      if (isMounted.current) {
        console.log('[ReviewCreationForm] handlePlatformToggle - selectedPlatforms state after update:', JSON.stringify(Array.from(newPlatforms), null, 2));
        // Platform tracking removed - no step progression needed
      }
      
      return newPlatforms;
    });
  }, [mainImageUrl]);
  
  // Handle genre selection
  const handleGenreToggle = useCallback((genre: string) => {
    if (!isMounted.current) return;
    console.log('[ReviewCreationForm] handleGenreToggle - genre trying to toggle:', genre);

    setSelectedGenres(prev => {
      const newGenres = new Set(prev);
      if (newGenres.has(genre)) {
        newGenres.delete(genre);
      } else {
        newGenres.add(genre);
      }
      console.log('[ReviewCreationForm] handleGenreToggle - selectedGenres state after update:', JSON.stringify(Array.from(newGenres), null, 2));
      return newGenres;
    });
  }, []);

  // Handle review settings changes
  const handleReviewSettingsChange = useCallback((newSettings: ReviewSettings) => {
    if (!isMounted.current) return;

    setReviewSettings(newSettings);

    // Note: Privacy setting (makePrivate) is handled separately from status
    // The handlePublishReview function will handle the final status logic
    // This keeps the UI logic simple and consistent

    console.log('[ReviewCreationForm] Review settings updated:', newSettings);
  }, []);
  
  // Handle main image change
  const handleMainImageChange = useCallback((url: string) => {
    if (!isMounted.current) return;
    
    setMainImageUrl(url);

    // Image tracking removed - no step progression needed
  }, [selectedPlatforms.size]);
  
  // Handle fullscreen editor toggle with smooth animations and state preservation
  const toggleEditorFullscreen = useCallback(() => {
    if (isEditorFullscreen) {
      // Ensure state is synchronized before exiting focus mode
      setEditorStateSnapshot(reviewContentLexical);

      // Start exit animation
      setIsExitingFullscreen(true);
      setTimeout(() => {
        setIsEditorFullscreen(false);
        setIsExitingFullscreen(false);
      }, 300); // Match animation duration
    } else {
      // Ensure state is synchronized before entering focus mode
      setEditorStateSnapshot(reviewContentLexical);
      setIsEditorFullscreen(true);
    }
  }, [isEditorFullscreen, reviewContentLexical]);

  // ESC key handler for focus mode
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditorFullscreen) {
        toggleEditorFullscreen();
      }
    };

    if (isEditorFullscreen) {
      document.addEventListener('keydown', handleEscKey);
      return () => document.removeEventListener('keydown', handleEscKey);
    }
  }, [isEditorFullscreen, toggleEditorFullscreen]);

  // Auto-generate SEO metadata automatically in the background
  const autoGenerateSeoMetadata = useCallback(async () => {
    // Auto-generate when we have enough content and SEO fields are empty
    if (gameName && reviewContentLexical && (!metaTitle || !metaDescription || !focusKeyword)) {
      try {
        const { metadataGenerator } = await import('@/lib/metadata/generator');
        const reviewData = {
          reviewTitle,
          gameName,
          slug,
          authorName: user?.userName || user?.displayName || 'CriticalPixel User',
          language,
          playedOn,
          datePlayed,
          igdbSummary,
          igdbDevelopers,
          igdbPublishers,
          igdbGameEngines,
          igdbPlayerPerspectives,
          igdbTimeToBeatNormally,
          igdbTimeToBeatCompletely,
          igdbAggregatedRating,
          igdbAggregatedRatingCount,
          igdbCoverUrl,
          releaseDate,
          reviewContentLexical,
          scoringCriteria,
          overallScore,
          selectedPlatforms,
          selectedGenres,
          mainImageUrl,
          galleryImageUrls,
          videoUrl,
          metaTitle,
          metaDescription,
          focusKeyword,
          reviewTags
        };

        const generatedMetadata = await metadataGenerator.generateMetadata(reviewData);

        // Apply generated metadata automatically
        if (!metaTitle) setMetaTitle(generatedMetadata.title);
        if (!metaDescription) setMetaDescription(generatedMetadata.description);
        if (!focusKeyword) setFocusKeyword(generatedMetadata.focusKeyword);

        console.log('[AutoSEO] Auto-generated metadata in background:', generatedMetadata);

        return generatedMetadata;
      } catch (error) {
        console.error('[AutoSEO] Error auto-generating metadata:', error);
      }
    }
    return null;
  }, [gameName, reviewContentLexical, reviewTitle, slug, user, language, playedOn, datePlayed,
      igdbSummary, igdbDevelopers, igdbPublishers, igdbGameEngines, igdbPlayerPerspectives,
      igdbTimeToBeatNormally, igdbTimeToBeatCompletely, igdbAggregatedRating, igdbAggregatedRatingCount,
      igdbCoverUrl, releaseDate, scoringCriteria, overallScore, selectedPlatforms, selectedGenres,
      mainImageUrl, galleryImageUrls, videoUrl, metaTitle, metaDescription, focusKeyword, reviewTags]);

  // Initialize editor state snapshot when component mounts
  useEffect(() => {
    if (reviewContentLexical && !editorStateSnapshot) {
      setEditorStateSnapshot(reviewContentLexical);
    }
  }, [reviewContentLexical, editorStateSnapshot]);

  // Auto-generate SEO metadata in the background when content is ready
  useEffect(() => {
    if (!isMounted.current) return;

    // Debounce the SEO generation to avoid too many calls
    const timeoutId = setTimeout(() => {
      if (gameName && reviewContentLexical && reviewContentLexical !== '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}') {
        autoGenerateSeoMetadata();
      }
    }, 2000); // Wait 2 seconds after content changes

    return () => clearTimeout(timeoutId);
  }, [gameName, reviewContentLexical, autoGenerateSeoMetadata]);

  // Handle editor light/dark mode toggle
  const toggleEditorLightMode = useCallback(() => {
    setIsEditorLight(prev => !prev);
  }, []);

  // Toggle auto-save function
  const toggleAutoSave = useCallback(() => {
    setIsAutoSaveEnabled(prev => !prev);
  }, []);


  // Clear auto-save function
  const clearAutoSave = useCallback(() => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    const autoSaveOptions = {
      userId: user.id,
      reviewId: originalReviewId || undefined,
      gameName,
      reviewTitle
    };

    // Clear auto-saved data from localStorage
    editorAutoSave.remove(autoSaveOptions);
    
    // Clear the current editor content using the editor ref
    if (editorRef.current) {
      editorRef.current.clearEditor();
    }
    if (fullscreenEditorRef.current) {
      fullscreenEditorRef.current.clearEditor();
    }
    
    // Also clear the state for consistency
    const emptyEditorState = '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}';
    setReviewContentLexical(emptyEditorState);
    setEditorStateSnapshot(emptyEditorState);
    
    setIsClearConfirmModalOpen(false);
    toast({
      title: "Content Cleared",
      description: "Editor content and auto-saved data have been cleared.",
    });
  }, [user?.id, originalReviewId, gameName, reviewTitle]);

  // Show toast notifications when auto-save state changes (less intrusive)
  useEffect(() => {
    // Skip toast on initial mount
    if (!isMounted.current) return;
    
    if (isAutoSaveEnabled) {
      toast({
        title: "Auto-save Enabled",
        description: "Content will be saved locally to protect your work.",
        variant: "default",
        duration: 2000, // Shorter duration
      });
    } else {
      toast({
        title: "Auto-save Disabled", 
        description: "Remember to save manually.",
        variant: "default",
        duration: 2000, // Shorter duration
      });
    }
  }, [isAutoSaveEnabled]);

  // Handle escape key to exit fullscreen with smooth animation
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isEditorFullscreen && !isExitingFullscreen) {
        toggleEditorFullscreen();
      }
    };

    if (isEditorFullscreen) {
      document.addEventListener('keydown', handleEscapeKey);
      // Note: Removed body overflow manipulation to prevent navbar layout issues
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isEditorFullscreen, isExitingFullscreen, toggleEditorFullscreen]);
  
  // Navigation handler for ReviewCreationNavbar
  const goToStep = useCallback((step: number) => {
    if (!isMounted.current) return;

    setCurrentStep(step);

    if (typeof window !== 'undefined') {
      // Map step numbers to section refs
      const sectionMapping = {
        1: sectionRefs.titleSection,      // Basic Information
        2: sectionRefs.mediaSection,      // Media & Visuals
        3: sectionRefs.contentSection,    // Review Content
        4: sectionRefs.ratingSection,     // Rating & Scores
        5: sectionRefs.monetizationSection // Monetization
      };

      const sectionRef = sectionMapping[step as keyof typeof sectionMapping];
      if (sectionRef && sectionRef.current && isMounted.current) {
        const navbarHeight = 80;
        const targetPosition = sectionRef.current.offsetTop - navbarHeight;

        window.scrollTo({
          top: Math.max(0, targetPosition),
          behavior: 'smooth'
        });
      }
    }
  }, []);
  


  // Handle review publishing with enhanced validation
  const handlePublishReview = async (intendedStatus: 'publish' | 'draft') => {
    if (!isMounted.current) return;

    if (!user) {
      toast({ title: "Authentication Required", description: "Please log in to publish.", variant: "destructive" });
      return;
    }

    // Prevent validation during data loading in edit mode
    if (isEditMode && isLoadingReview) {
      console.log('Attempted to save while loading review data');
      toast({ 
        title: "Still Loading", 
        description: "Please wait for review data to finish loading before saving.", 
        variant: "default" 
      });
      return;
    }
    
    // Enhanced validation with detailed field checking
    const missingFields = [];
    if (!reviewTitle?.trim()) missingFields.push("Review Title");
    if (!igdbId) missingFields.push("Game Selection");
    if (!reviewContentLexical || reviewContentLexical === '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}') {
      missingFields.push("Review Content");
    }

    if (missingFields.length > 0) {
      console.log("Validation failed:", {
        reviewTitle: !!reviewTitle,
        reviewTitleValue: reviewTitle,
        igdbId: !!igdbId,
        igdbIdValue: igdbId,
        gameName: !!gameName,
        gameNameValue: gameName,
        reviewContentLexical: !!reviewContentLexical,
        reviewContentLexicalValue: reviewContentLexical,
        isEditMode: isEditMode,
        isLoadingReview: isLoadingReview,
        missingFields: missingFields
      });
      
      toast({ 
        title: "Missing Required Fields", 
        description: `Please fill in: ${missingFields.join(", ")}`, 
        variant: "destructive" 
      });
      return;
    }
    
    console.log('[ReviewCreationForm] handlePublishReview - Current states:');
    console.log('[ReviewCreationForm] user:', JSON.stringify(user, null, 2));
    console.log('[ReviewCreationForm] reviewTitle:', reviewTitle);
    console.log('[ReviewCreationForm] gameName:', gameName);
    console.log('[ReviewCreationForm] language:', language);
    console.log('[ReviewCreationForm] playedOn:', playedOn);
    console.log('[ReviewCreationForm] datePlayed:', datePlayed);
    console.log('[ReviewCreationForm] mainImageUrl:', mainImageUrl);
    console.log('[ReviewCreationForm] igdbId:', igdbId);
    console.log('[ReviewCreationForm] igdbSummary:', igdbSummary);
    console.log('[ReviewCreationForm] igdbDevelopers:', JSON.stringify(igdbDevelopers, null, 2));
    console.log('[ReviewCreationForm] igdbPublishers:', JSON.stringify(igdbPublishers, null, 2));
    console.log('[ReviewCreationForm] igdbGameEngines:', JSON.stringify(igdbGameEngines, null, 2));
    console.log('[ReviewCreationForm] igdbPlayerPerspectives:', JSON.stringify(igdbPlayerPerspectives, null, 2));
    console.log('[ReviewCreationForm] igdbTimeToBeatNormally:', igdbTimeToBeatNormally);
    console.log('[ReviewCreationForm] igdbTimeToBeatCompletely:', igdbTimeToBeatCompletely);
    console.log('[ReviewCreationForm] igdbAggregatedRating:', igdbAggregatedRating);
    console.log('[ReviewCreationForm] igdbAggregatedRatingCount:', igdbAggregatedRatingCount);
    console.log('[ReviewCreationForm] reviewTags:', JSON.stringify(reviewTags, null, 2)); 
    console.log('[ReviewCreationForm] selectedPlatforms:', JSON.stringify(Array.from(selectedPlatforms), null, 2));
    console.log('[ReviewCreationForm] selectedGenres:', JSON.stringify(Array.from(selectedGenres), null, 2));

    // Set different loading states based on intent
    if (intendedStatus === 'draft') {
      setIsDraftSaving(true);
    } else {
      setIsPublishing(true);
      setIsPublishingWithModal(true);
    }

    try {
      // Auto-generate SEO metadata if needed before saving
      await autoGenerateSeoMetadata();
      const authorName = user?.userName || user?.displayName || 'A Critical Pixel User';
      
      let finalStatus: 'draft' | 'published' | 'archived' = 'draft'; // Default to draft

      if (intendedStatus === 'publish') {
        // Check privacy setting - if private, keep as draft even when publishing
        finalStatus = reviewSettings.makePrivate ? 'draft' : 'published';
      } else { // intendedStatus === 'draft'
        finalStatus = 'draft';
      }

      const reviewData = {
        title: reviewTitle,
        gameName: gameName,
        slug,
        authorId: user.uid, // Ensured
        authorName: authorName, // Ensured

        // User filled fields - ensure no undefined values
        language: language || '',
        playedOn: playedOn || '',
        datePlayed: datePlayed || '',

        // IGDB data - only include if values are defined
        mainImageUrl: mainImageUrl || '',
        mainImagePosition: mainImagePosition || 'center 25%',
        ...(igdbCoverUrl && { igdbCoverUrl }),

        // Structure IGDB data correctly for ReviewFormData interface
        ...(typeof igdbId === 'number' && { 
          igdbId,
          igdbData: {
            id: igdbId,
            name: gameName,
            summary: igdbSummary || undefined,
            aggregated_rating: typeof igdbAggregatedRating === 'number' ? igdbAggregatedRating : undefined,
            aggregated_rating_count: typeof igdbAggregatedRatingCount === 'number' ? igdbAggregatedRatingCount : undefined,
            first_release_date: releaseDate ? Math.floor(releaseDate.getTime() / 1000) : undefined,
            developers: igdbDevelopers.length > 0 ? igdbDevelopers.map(dev => ({ name: dev })) : undefined,
            publishers: igdbPublishers.length > 0 ? igdbPublishers.map(pub => ({ name: pub })) : undefined,
            game_engines: igdbGameEngines.length > 0 ? igdbGameEngines : undefined,
            player_perspectives: igdbPlayerPerspectives.length > 0 ? igdbPlayerPerspectives : undefined,
            time_to_beat: (typeof igdbTimeToBeatNormally === 'number' || typeof igdbTimeToBeatCompletely === 'number') ? {
              ...(typeof igdbTimeToBeatNormally === 'number' && { normally: igdbTimeToBeatNormally }),
              ...(typeof igdbTimeToBeatCompletely === 'number' && { completely: igdbTimeToBeatCompletely })
            } : undefined,
            cover: igdbCoverUrl ? { url: igdbCoverUrl } : undefined,
            platforms: Array.from(selectedPlatforms).length > 0 ? Array.from(selectedPlatforms).map(platform => ({ name: platform })) : undefined,
            genres: Array.from(selectedGenres).length > 0 ? Array.from(selectedGenres).map(genre => ({ name: genre })) : undefined
          }
        }),

        // Existing fields - ensure no undefined values with proper date validation
        ...(releaseDate && !isNaN(releaseDate.getTime()) && { releaseDate: releaseDate.toISOString() }),
        ...(publishDate && !isNaN(publishDate.getTime()) && { publishDate: publishDate.toISOString() }),
        scoringCriteria: scoringCriteria.map(criterion => ({ id: criterion.id, name: criterion.name, score: criterion.score })),
        overallScore,
        platforms: Array.from(selectedPlatforms), 
        genres: Array.from(selectedGenres), 
        contentLexical: reviewContentLexical,
        // Add IGDB metadata fields to main review data
        ...(igdbDevelopers.length > 0 && { developers: igdbDevelopers }),
        ...(igdbPublishers.length > 0 && { publishers: igdbPublishers }),
        ...(releaseDate && { first_release_date: Math.floor(releaseDate.getTime() / 1000) }),
        metaTitle: metaTitle || reviewTitle,
        metaDescription: metaDescription || '',
        focusKeyword: focusKeyword || '',
        tags: reviewTags || [],
        // Add metadata about SEO generation
        seoGenerated: !!(metaTitle || metaDescription || focusKeyword),
        seoGeneratedAt: (metaTitle || metaDescription || focusKeyword) ? new Date().toISOString() : undefined,
        galleryImageUrls: galleryImageUrls || [], 
        videoUrl: videoUrl || '', 
        monetizationBlocks: monetizationBlocks || [],
        status: finalStatus,
        featuredHomepage: false,
        // Comment settings
        enableComments: reviewSettings.enableComments,
        // Privacy settings - IMPORTANT: Add the is_private field
        isPrivate: reviewSettings.makePrivate
      };

      console.log('[ReviewCreationForm] handlePublishReview - reviewData to be saved:', JSON.stringify(reviewData, null, 2));
      console.log('[ReviewCreationForm] Edit mode:', isEditMode, 'Original Review ID:', originalReviewId);

      let result;

      if (isEditMode && originalReviewId) {
        // Update existing review
        if (typeof updateReview !== 'function') {
          throw new Error("Review update functionality is not available");
        }
        
        // Transform reviewData for update: convert scoringCriteria array to detailedScores object
        const updateData = {
          ...reviewData,
          detailedScores: scoringCriteria.reduce((acc, criterion) => {
            acc[criterion.id] = criterion.score;
            return acc;
          }, {} as Record<string, number>),
          enableComments: reviewSettings.enableComments,
          isPrivate: reviewSettings.makePrivate
        };
        // Remove the array version since updateReview expects detailedScores
        delete updateData.scoringCriteria;
        
        result = await updateReview(originalReviewId, updateData, user.id);
      } else {
        // Create new review
        if (typeof saveReview !== 'function') {
          throw new Error("Review saving functionality is not available");
        }
        result = await saveReview(reviewData);
      }
      
      if (!result.success) {
        throw new Error(result.error || "Failed to save review");
      }
      
      // slug here refers to the component's state variable
      if (result.success && slug) { 
        if (isMounted.current) {
            setShowConfetti(true);
        }

        if (intendedStatus === 'publish') {
            const actionText = isEditMode ? "updated" : "published";
            toast({
              title: "Success!",
              description: `Your review has been ${actionText} successfully! Redirecting to your review...`
            });
            
            // Add a 5-second delay for the fake loading effect, then redirect to the published review
            setTimeout(() => {
                if (isMounted.current) {
                    router.push(`/reviews/view/${slug}`);
                }
            }, 5000);
        } else { // intendedStatus === 'draft'
            const actionText = isEditMode ? "updated" : "saved";
            toast({
              title: "Draft Saved!",
              description: `Your review has been ${actionText} as a draft. Redirecting to My Reviews...`
            });
            
            // Add a 5-second delay for the fake loading effect, then redirect to My Reviews
            setTimeout(() => {
                if (isMounted.current) {
                    router.push(`/u/dashboard?tab=drafts`);
                }
            }, 5000);
        }
      } else if (result.success && !slug) {
        // Review saved successfully but client-side slug is missing
        console.error("Review saved but slug is missing on the client-side.");
        toast({
            title: "Error",
            description: "Review saved, but there was an issue with the slug. Cannot redirect.",
            variant: "destructive"
        });
        // No redirect here, user stays on page, isPublishing will be set to false in finally
      } else {
        // Handle saveReview failure (result.success is false or an error was thrown)
        // This specific 'else' block will be hit if saveReview returns { success: false }
        // If saveReview throws an error, the catch block handles it.
        toast({
            title: "Error",
            description: result.error || "Failed to save your review. Please try again.",
            variant: "destructive"
        });
        
        // Reset loading states on failure so user can try again
        if (intendedStatus === 'draft') {
          setIsDraftSaving(false);
        } else {
          setIsPublishingWithModal(false);
        }
      }
    } catch (error) {
      console.error("Error publishing/saving review:", error); // Catch errors from saveReview or other issues
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      toast({ 
        title: "Error", 
        description: `An unexpected error occurred: ${errorMessage}. Please try again.`, 
        variant: "destructive" 
      });
      
      // Reset loading states on error so user can try again
      if (intendedStatus === 'draft') {
        setIsDraftSaving(false);
      } else {
        setIsPublishingWithModal(false);
      }
    } finally {
      if (isMounted.current) {
        setIsPublishing(false);
        // Note: Don't reset isDraftSaving here on success - let the redirect handle it
        // Only reset on publish operations or if there's an error (handled in catch block)
      }
    }
  };

  // Debug logging for state changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("Form state updated:", {
        reviewTitle: !!reviewTitle,
        gameName: !!gameName,
        reviewContentLexical: !!reviewContentLexical,
        slug
      });
    }
  }, [reviewTitle, gameName, reviewContentLexical, slug]);



  // Show loading state when loading review for editing (after all hooks)
  if (isLoadingReview) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-slate-800/50 backdrop-blur-xl border border-white/10 rounded-xl p-8 max-w-md text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-400 mx-auto mb-4"></div>
          <h1 className="text-xl font-bold text-white mb-2">Loading Review</h1>
          <p className="text-slate-300">Loading review data for editing...</p>
        </div>
      </div>
    );
  }

  // Loading state (after all hooks)
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-content">
          <div className="loading-spinner">
            <div className="loading-spinner-primary"/>
            <div className="loading-spinner-secondary"/>
          </div>
          <div className="loading-text">
            <p className="loading-title">
              <span className="text-violet-400/60">&lt;</span>
              <span className="px-1">Initializing</span>
              <span className="text-violet-400/60">/&gt;</span>
            </p>
            <div className="loading-dots">
              <div className="loading-dot-1"/>
              <div className="loading-dot-2"/>
              <div className="loading-dot-3"/>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Not authenticated state (after all hooks)
  if (!user) {
    return (
      <div className="auth-container">
        <div className="auth-card-wrapper">
          <div className="auth-card-glow"/>
          <div className="auth-card">
            <div className="auth-icon-container">
              <XCircle size={40} className="text-red-400" />
            </div>
            <h2 className="auth-title">
              <span className="font-mono">
                <span className="text-red-400/60">&lt;</span>
                <span className="px-1">Access Denied</span>
                <span className="text-red-400/60">/&gt;</span>
              </span>
            </h2>
            <p className="text-slate-400 mb-8">Please log in to access the review builder.</p>
            <Button
              onClick={() => router.push('/login')}
              className="w-full bg-gradient-to-r from-indigo-600 to-violet-500 hover:from-indigo-500 hover:to-violet-400 text-white border-none shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
            >
              <Users className="mr-2 h-4 w-4" />
              Login to Continue
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Main component render
  return (
    <>
    <div className="page-main-container">
      {/* Dynamic width styles - NO TRANSITIONS */}
      <style jsx>{`
        /* Instant width changes - no transitions */
        .page-main-container .review-page-container {
          transition: none !important;
          will-change: auto !important;
        }

        /* Dynamic width control for screens above 1360px */
        @media (min-width: 1361px) {
          .page-main-container .review-page-container {
            width: ${containerWidth === 75 ? '1430px' : `${containerWidth}%`} !important;
            max-width: ${containerWidth === 75 ? '1430px' : `${containerWidth}%`} !important;
            margin-left: auto !important;
            margin-right: auto !important;
            ${containerWidth === 75 ? 'padding-left: 0 !important; padding-right: 0 !important;' : ''}
          }
        }

        /* Force 100% width on smaller screens */
        @media (max-width: 1360px) {
          .page-main-container .review-page-container {
            width: 100% !important;
            max-width: 100% !important;
          }
        }

        /* Clean styling without any effects */
        .page-main-container .review-page-container {
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
          transform: none !important;
        }

        .page-main-container .review-page-container:hover {
          transform: none !important;
          box-shadow: none !important;
        }


      `}</style>
      
      {/* Animated background elements */}
      <div className="page-background">
        <div className="background-gradient-1"/>
        <div className="background-gradient-2"/>
      </div>
      
      {/* FIXED: Match site-wide responsive behavior exactly */}
      <div className="review-page-container px-4 sm:px-6 lg:px-8 py-0" data-width={containerWidth}>

        {/* Width Control Header */}
        <div ref={sectionRefs.headerSection} className="relative">
          <HorizontalStepNav
            steps={[]}
            currentStep={1}
            onStepClick={() => {}}
            formCompletion={100}
            containerWidth={containerWidth}
            setContainerWidth={setContainerWidth}
          />
          
          {/* Tooltip sliding down from the percentage number */}
          {showWidthTooltip && (
            <div className={`absolute top-[52px] right-[88px] z-50 transition-all duration-300 ease-out ${tooltipExiting ? 'opacity-0 -translate-y-2' : 'opacity-100 translate-y-0'}`}>
              <div className="bg-slate-900/95 backdrop-blur-sm border border-violet-400/30 rounded-md px-2 py-1">
                <p className="text-violet-200 text-xs font-medium whitespace-nowrap">
                  Default width
                </p>
                {/* Arrow pointing up to the percentage number */}
                <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                  <div className="w-0 h-0 border-l-[4px] border-r-[4px] border-b-[4px] border-l-transparent border-r-transparent border-b-violet-400/30"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content Sections */}
        <div className="space-y-16">
          {/* Step 1: Basic Information */}
          <section ref={sectionRefs.titleSection}>
            <SectionWrapper
              title={isEditMode ? "Edit Review - Basic Information" : "Basic Information"}
              description={isEditMode ? "Update your review fundamentals" : "Let's start with the fundamentals"}
              icon={Monitor}
              stepNumber={1}
            >
              {/* Debug log for edit mode props */}
              {isEditMode && console.log('[ReviewPage] Passing props to TitleYourReview in edit mode:', {
                gameName,
                igdbId,
                igdbSummary,
                igdbAggregatedRating,
                igdbAggregatedRatingCount,
                isEditMode
              })}

              <TitleYourReview
                reviewTitle={reviewTitle}
                setReviewTitle={setReviewTitle}
                gameName={gameName}
                setGameName={setGameName}
                // Pass state setters for new IGDB metadata
                setIgdbSummary={setIgdbSummary}
                setIgdbAggregatedRating={setIgdbAggregatedRating}
                setIgdbAggregatedRatingCount={setIgdbAggregatedRatingCount}
                setIgdbDevelopers={setIgdbDevelopers}
                setIgdbPublishers={setIgdbPublishers}
                setIgdbGameEngines={setIgdbGameEngines}
                setIgdbPlayerPerspectives={setIgdbPlayerPerspectives}
                setIgdbTimeToBeatNormally={setIgdbTimeToBeatNormally}
                setIgdbTimeToBeatCompletely={setIgdbTimeToBeatCompletely}
                setMainImageUrl={setMainImageUrl}
                setIgdbCoverUrl={setIgdbCoverUrl}
                setIgdbId={setIgdbId}
                setReleaseDate={setReleaseDate} // ADD THIS LINE
                onTagsChange={setReviewTags}
                currentTags={reviewTags} // Pass current tags for edit mode
                language={language}
                setLanguage={setLanguage}
                playedOn={playedOn}
                setPlayedOn={setPlayedOn}
                datePlayed={datePlayed}
                setDatePlayed={setDatePlayed}
                // Pass state values (optional, for display or future use)
                igdbId={igdbId}
                igdbSummary={igdbSummary}
                igdbAggregatedRating={igdbAggregatedRating}
                igdbAggregatedRatingCount={igdbAggregatedRatingCount}
                initialAvailablePlatforms={initialAvailablePlatforms}
                initialAvailableGenres={initialAvailableGenres}
                // New props for platform and genre multi-select
                selectedPlatforms={selectedPlatforms}
                onPlatformToggle={handlePlatformToggle}
                selectedGenres={selectedGenres}
                onGenreToggle={handleGenreToggle}
                // Edit mode prop
                isEditMode={isEditMode}
              />
            </SectionWrapper>
          </section>
          
          {/* Step 2: Media & Visuals */}
          <section ref={sectionRefs.mediaSection} data-media-section>
            <SectionWrapper
              title="Media & Visuals"
              description="Add images and videos to enhance your review"
              icon={Camera}
              stepNumber={2}
            >
              <AddBattleVisuals
                bannerImageUrl={mainImageUrl}
                bannerImagePosition={mainImagePosition}
                videoUrl={videoUrl}
                onBannerImageUrlChange={setMainImageUrl}
                onBannerImagePositionChange={setMainImagePosition}
                onVideoUrlChange={setVideoUrl}
                onFindBannerClick={() => setIsSteamGridDBModalOpen(true)}
                gameName={gameName}
                handleNextStep={() => sectionRefs.contentSection.current?.scrollIntoView({ behavior: 'smooth', block: 'start' })}
              />
            </SectionWrapper>
          </section>
          
          {/* Step 3: Review Content */}
          <section ref={sectionRefs.contentSection}>
            <SectionWrapper
              title="Review Content"
              description="Share your detailed thoughts and experience"
              icon={Edit3}
              stepNumber={3}
              headerControls={
                <>
                  <Button
                    onClick={toggleAutoSave}
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white transition-all duration-200 group"
                  >
                    <Save size={12} className="mr-1" />
                    {isAutoSaveEnabled ? 'On' : 'Off'}
                  </Button>
                  <Button
                    onClick={() => setIsClearConfirmModalOpen(true)}
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white transition-all duration-200 group"
                  >
                    <Trash2 size={12} className="mr-1" />
                    Clear
                  </Button>
                  <Button
                    onClick={toggleEditorLightMode}
                    variant="outline"
                    size="sm"
                    className={`h-7 px-2 text-xs border-slate-600 transition-all duration-200 group ${
                      isEditorLight
                        ? 'bg-yellow-500/20 border-yellow-500/60 text-gray-800 hover:bg-yellow-500/30'
                        : 'bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white'
                    }`}
                  >
                    {isEditorLight ? <Sun size={12} /> : <Moon size={12} />}
                  </Button>
                  <Button
                    onClick={toggleEditorFullscreen}
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white transition-all duration-200 group"
                  >
                    <Maximize size={12} className="group-hover:scale-110 transition-transform" />
                  </Button>
                </>
              }
            >
              <div style={{ paddingTop: '0px !important' }}>
                {/* Dynamic CSS for Editor Light/Dark Mode */}
                <style
                  dangerouslySetInnerHTML={{
                    __html: `
                      .lexical-editor-container.editor-light-mode .lexical-toolbar {
                        background: linear-gradient(to bottom right, rgba(255, 248, 220, 0.95), rgba(254, 252, 232, 0.9)) !important;
                        border-bottom: 1px solid rgba(251, 191, 36, 0.3) !important;
                      }
                      .lexical-editor-container.editor-light-mode .toolbar-button {
                        background: rgba(251, 191, 36, 0.1) !important;
                        border-color: rgba(251, 191, 36, 0.2) !important;
                        color: rgb(92, 55, 19) !important;
                      }
                      .lexical-editor-container.editor-light-mode .toolbar-button:hover:not(.disabled) {
                        background: rgba(251, 191, 36, 0.2) !important;
                        border-color: rgba(251, 191, 36, 0.4) !important;
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .toolbar-button.active {
                        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
                        color: white !important;
                        border-color: rgba(251, 191, 36, 0.6) !important;
                      }
                      .lexical-editor-container.editor-light-mode .toolbar-separator {
                        background: rgba(251, 191, 36, 0.3) !important;
                      }
                      .lexical-editor-container.editor-light-mode .lexical-editor-content {
                        background: linear-gradient(to bottom right, rgba(255, 248, 220, 0.95), rgba(254, 252, 232, 0.9)) !important;
                      }
                      .lexical-editor-container.editor-light-mode .lexical-content-editable {
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .lexical-content-editable * {
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-paragraph {
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-heading-h1,
                      .lexical-editor-container.editor-light-mode .editor-heading-h2,
                      .lexical-editor-container.editor-light-mode .editor-heading-h3,
                      .lexical-editor-container.editor-light-mode .editor-heading-h4,
                      .lexical-editor-container.editor-light-mode .editor-heading-h5,
                      .lexical-editor-container.editor-light-mode .editor-heading-h6 {
                        color: rgb(41, 37, 36) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-quote {
                        border-left-color: rgb(147, 51, 234) !important;
                        background: rgba(147, 51, 234, 0.08) !important;
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-link {
                        color: rgb(37, 99, 235) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-text-code {
                        background: rgb(251, 191, 36) !important;
                        color: rgb(92, 55, 19) !important;
                      }
                      .lexical-editor-container.editor-light-mode .editor-code {
                        background: rgb(254, 252, 232) !important;
                        border-color: rgb(251, 191, 36) !important;
                        color: rgb(55, 65, 81) !important;
                      }
                      .lexical-editor-container.editor-light-mode .lexical-placeholder {
                        color: rgba(55, 65, 81, 0.6) !important;
                      }
                    `,
                  }}
                />


                {/* Single Editor Container - Conditionally Rendered */}
                {!isEditorFullscreen && (
                  <div className="relative border border-white/6 rounded-xl overflow-hidden bg-slate-900/30 h-[70vh] flex flex-col" style={{ marginTop: '0px !important', paddingTop: '0px !important' }}>
                    <Editor
                      ref={editorRef}
                      key="editor-regular"
                      onChange={handleEditorChange}
                      initialEditorState={editorStateSnapshot || reviewContentLexical || null}
                      placeholder="Write your review here..."
                      isLightMode={isEditorLight}
                      userId={user?.id}
                      reviewId={originalReviewId || undefined}
                      gameName={gameName}
                      reviewTitle={reviewTitle}
                      enableAutoSave={isAutoSaveEnabled}
                    />
                  </div>
                )}
                
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-4 border-t border-slate-700/50 gap-4">
                  <div className="flex items-center space-x-2 min-w-0">
                    <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse flex-shrink-0" />
                    <span className="text-sm text-slate-400/80 font-mono break-words">
                      {reviewContentLexical && reviewContentLexical !== '{"root":{"children":[{"children":[],"direction":null,"format":"","indent":0,"type":"paragraph","version":1}],"direction":null,"format":"","indent":0,"type":"root","version":1}}'
                        ? '// Content completed'
                        : '// Add your review content'
                      }
                    </span>
                  </div>

                  <div className="flex items-center space-x-3 flex-shrink-0">
                    <Button
                      onClick={() => sectionRefs.ratingSection.current?.scrollIntoView({ behavior: 'smooth', block: 'center' })}
                      className="review-continue-button review-continue-ready"
                    >
                      <div className="review-button-content">
                        <span className="review-code-brackets">&lt;</span>
                        Continue
                        <span className="review-code-brackets">/&gt;</span>
                        <ArrowRight className="review-button-arrow" />
                      </div>
                    </Button>
                  </div>
                </div>
              </div>
            </SectionWrapper>
          </section>

          {/* Step 4: Rating System */}
          <section ref={sectionRefs.ratingSection}>
            <SectionWrapper
              title="Rating & Scores"
              description="Rate different aspects of the game"
              icon={Star}
              stepNumber={4}
            >
              <div className="space-y-6">
                <RatingSection
                  scoringCriteria={scoringCriteria}
                  overallScore={overallScore}
                  handleCriterionScoreChange={handleCriterionScoreChange}
                  currentStep={4}
                  handleNextStep={() => sectionRefs.monetizationSection.current?.scrollIntoView({ behavior: 'smooth', block: 'center' })}
                />
              </div>
            </SectionWrapper>
          </section>

          {/* Step 5: Monetization */}
          <section ref={sectionRefs.monetizationSection}>
            <SectionWrapper
              title="Monetization"
              description="Configure revenue and advertising options"
              icon={DollarSign}
              stepNumber={5}
            >
              <MonetizationConfigurator
                monetizationBlocks={monetizationBlocks}
                onMonetizationBlocksChange={setMonetizationBlocks}
              />
            </SectionWrapper>
          </section>

          {/* Step 6: Final Controls */}
          <section>
            <SectionWrapper
              title="Review Controls"
              description="Final settings and publishing options"
              icon={Settings}
              stepNumber={6}
            >
              <ReviewCreationNavbar
                formCompletion={formCompletion}
                isPublishing={isPublishing || isDraftSaving}
                handlePublishReview={handlePublishReview}
                reviewSettings={reviewSettings}
                onReviewSettingsChange={handleReviewSettingsChange}
                isDraftSaving={isDraftSaving}
                isPublishingWithModal={isPublishingWithModal}
                isEditMode={isEditMode}
              />
            </SectionWrapper>
          </section>

          {/* Auto-SEO Status Indicator */}
          {(metaTitle || metaDescription || focusKeyword) && (
            <div className="mb-8">
              <div className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <div className="font-mono text-sm text-green-300">
                    <span className="text-green-400">&lt;</span>
                    SEO_Optimized
                    <span className="text-green-400">/&gt;</span>
                  </div>
                  <span className="text-xs text-green-400/70">
                    Metadata automatically generated and applied
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* SteamGridDB Banner Search Modal */}
      <SteamGridDBBannerModal
        isOpen={isSteamGridDBModalOpen}
        onClose={() => setIsSteamGridDBModalOpen(false)}
        onBannerSelect={(bannerUrl) => {
          setMainImageUrl(bannerUrl);
          setIsSteamGridDBModalOpen(false);
        }}
        gameName={gameName}
      />

      {/* Auto-save Clear Confirmation Modal */}
      <Dialog open={isClearConfirmModalOpen} onOpenChange={setIsClearConfirmModalOpen}>
        <DialogContent className="bg-slate-900/95 backdrop-blur-xl border border-red-500/30 text-white">
          <DialogHeader>
            <DialogTitle className="text-red-300 font-mono">
              <span className="text-red-400/60">&lt;</span>
              Clear Auto-Save
              <span className="text-red-400/60">/&gt;</span>
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              Are you sure you want to clear all auto-saved content? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsClearConfirmModalOpen(false)}
              className="border-slate-600 bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={clearAutoSave}
              className="bg-red-600 hover:bg-red-700 text-white border-red-600"
            >
              <Trash2 size={14} className="mr-2" />
              Clear Auto-Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>

    {/* Fullscreen Editor Overlay - MOVED OUTSIDE CONTAINER */}
    {isEditorFullscreen && (
      <div className={`focus-mode-overlay ${isExitingFullscreen ? 'exiting' : ''}`}>
        <div className="focus-mode-container">
          {/* Fullscreen Header */}
          <div className="focus-mode-header">
            <div className="flex items-center space-x-3">
              <Edit3 size={20} className="text-violet-400" />
              <h3 className="font-mono text-white text-lg font-bold">
                <span className="text-violet-400/60">&lt;</span>
                <span className="px-2">Focus Mode</span>
                <span className="text-violet-400/60">/&gt;</span>
              </h3>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-slate-400 font-mono">Press ESC to exit</span>
              <Button
                onClick={toggleEditorLightMode}
                variant="outline"
                size="sm"
                className={`h-7 px-2 text-xs border-slate-600 transition-all duration-200 group ${
                  isEditorLight
                    ? 'bg-yellow-500/20 border-yellow-500/60 text-gray-800 hover:bg-yellow-500/30'
                    : 'bg-slate-800/50 hover:bg-slate-700/50 text-slate-300 hover:text-white'
                }`}
              >
                {isEditorLight ? <Sun size={12} /> : <Moon size={12} />}
              </Button>
              <Button
                onClick={toggleEditorFullscreen}
                variant="outline"
                size="sm"
                className="h-7 px-2 text-xs border-slate-600 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white transition-all duration-200"
              >
                <Minimize size={12} />
              </Button>
            </div>
          </div>

          {/* Fullscreen Editor */}
          <div className="focus-mode-editor-area">
            <div className="focus-mode-editor-wrapper">
              <Editor
                ref={fullscreenEditorRef}
                key="editor-focus"
                onChange={handleEditorChange}
                initialEditorState={editorStateSnapshot || reviewContentLexical || null}
                placeholder="Write your review here in focus mode..."
                isLightMode={isEditorLight}
                userId={user?.id}
                reviewId={originalReviewId || undefined}
                gameName={gameName}
                reviewTitle={reviewTitle}
                enableAutoSave={true}
              />
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Draft Saving Loading Overlay */}
    {isDraftSaving && (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center">
        {/* Backdrop with blur */}
        <div className="absolute inset-0 bg-slate-900/80 backdrop-blur-sm" />
        
        {/* Loading content */}
        <div className="relative z-10 bg-slate-800/95 backdrop-blur-xl border border-violet-500/30 rounded-xl p-8 max-w-md text-center">
          <div className="mb-6">
            {/* Animated spinner */}
            <div className="relative mx-auto mb-4 w-16 h-16">
              <div className="absolute inset-0 rounded-full border-4 border-violet-500/20"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-violet-500 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-cyan-400 animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1s' }}></div>
            </div>
            
            <h2 className="text-xl font-bold text-white mb-2 font-mono">
              <span className="text-violet-400/60">&lt;</span>
              <span className="px-1">Saving Draft</span>
              <span className="text-violet-400/60">/&gt;</span>
            </h2>
            
            <p className="text-slate-300 mb-4">
              Your review is being saved...
            </p>
            
            {/* Animated dots */}
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </div>
          
          <p className="text-slate-400 text-sm">
            Redirecting to My Reviews...
          </p>
        </div>
      </div>
    )}

    {/* Publishing Loading Overlay */}
    {isPublishingWithModal && (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center">
        {/* Backdrop with blur */}
        <div className="absolute inset-0 bg-slate-900/80 backdrop-blur-sm" />
        
        {/* Loading content */}
        <div className="relative z-10 bg-slate-800/95 backdrop-blur-xl border border-emerald-500/30 rounded-xl p-8 max-w-md text-center">
          <div className="mb-6">
            {/* Animated spinner */}
            <div className="relative mx-auto mb-4 w-16 h-16">
              <div className="absolute inset-0 rounded-full border-4 border-emerald-500/20"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-emerald-500 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-cyan-400 animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1s' }}></div>
            </div>
            
            <h2 className="text-xl font-bold text-white mb-2 font-mono">
              <span className="text-emerald-400/60">&lt;</span>
              <span className="px-1">Publishing Review</span>
              <span className="text-emerald-400/60">/&gt;</span>
            </h2>
            
            <p className="text-slate-300 mb-4">
              Your review is being published...
            </p>
            
            {/* Animated dots */}
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </div>
          
          <p className="text-slate-400 text-sm">
            Redirecting to your review...
          </p>
        </div>
      </div>
    )}
    </>
  );
}