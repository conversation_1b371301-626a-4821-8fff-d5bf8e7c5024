import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { verifyAdminSessionEnhanced } from '@/lib/admin/security';
import { AdminPermissionLevel } from '@/lib/admin/security-utils';

/**
 * AUDIT LOGS API ROUTE
 * Provides access to security audit logs for super admins
 * 
 * Date: June 14, 2025
 * Security Level: CRITICAL
 * Purpose: Enable audit log monitoring and forensic analysis
 */

export async function GET(request: NextRequest) {
  try {
    // LAYER 1: Verify admin session and permissions
    const verification = await verifyAdminSessionEnhanced();
    
    if (!verification.isValid) {
      return NextResponse.json(
        { error: 'Admin verification failed' },
        { status: 401 }
      );
    }

    // LAYER 2: Require SUPER_ADMIN level for audit log access
    if (verification.permissionLevel !== AdminPermissionLevel.SUPER_ADMIN) {
      return NextResponse.json(
        { error: 'Super admin privileges required for audit log access' },
        { status: 403 }
      );
    }

    // LAYER 3: Parse query parameters
    const { searchParams } = new URL(request.url);
    const severity = searchParams.get('severity');
    const eventType = searchParams.get('eventType');
    const dateRange = searchParams.get('dateRange') || '24h';
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    // LAYER 4: Build query with filters
    const supabase = await createServerClient();
    let query = supabase
      .from('security_audit_log')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply severity filter
    if (severity && severity !== '') {
      query = query.eq('severity', severity);
    }

    // Apply event type filter
    if (eventType && eventType !== '') {
      query = query.ilike('event_type', `%${eventType}%`);
    }

    // Apply date range filter
    const now = new Date();
    let dateFilter: Date;
    switch (dateRange) {
      case '1h':
        dateFilter = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        dateFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        dateFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    query = query.gte('created_at', dateFilter.toISOString());

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    // LAYER 5: Execute query
    const { data: logs, error, count } = await query;

    if (error) {
      console.error('🚨 AUDIT: Failed to fetch audit logs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch audit logs' },
        { status: 500 }
      );
    }

    // LAYER 6: Get summary statistics
    const { data: summary } = await supabase
      .from('security_audit_log')
      .select('severity, event_type')
      .gte('created_at', dateFilter.toISOString());

    // Calculate statistics
    const stats = {
      total: logs?.length || 0,
      severityBreakdown: {
        CRITICAL: summary?.filter(s => s.severity === 'CRITICAL').length || 0,
        HIGH: summary?.filter(s => s.severity === 'HIGH').length || 0,
        MEDIUM: summary?.filter(s => s.severity === 'MEDIUM').length || 0,
        LOW: summary?.filter(s => s.severity === 'LOW').length || 0,
      },
      topEvents: getTopEvents(summary || [])
    };

    // LAYER 7: Log audit log access
    console.info('✅ AUDIT: Audit logs accessed', {
      adminId: verification.adminId,
      filters: { severity, eventType, dateRange },
      resultCount: logs?.length || 0,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      logs: logs || [],
      stats,
      pagination: {
        limit,
        offset,
        hasMore: (logs?.length || 0) === limit
      }
    });

  } catch (error) {
    console.error('🚨 CRITICAL: Audit logs API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getTopEvents(summary: any[]): Array<{event_type: string, count: number}> {
  const eventCounts = summary.reduce((acc, item) => {
    acc[item.event_type] = (acc[item.event_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(eventCounts)
    .map(([event_type, count]) => ({ event_type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
} 