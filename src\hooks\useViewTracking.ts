'use client';

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

// Helper function to get or create anonymous session ID
function getAnonymousSessionId(): string {
  if (typeof window === 'undefined') return '';

  let sessionId = localStorage.getItem('anon_session_id');
  if (!sessionId) {
    sessionId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('anon_session_id', sessionId);
  }
  return sessionId;
}

// API function to track review views
async function trackReviewView(reviewId: string, viewerIp?: string) {
  const response = await fetch('/api/track-view', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      reviewId,
      viewerIp,
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to track view: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}

interface UseViewTrackingOptions {
  enabled?: boolean;
  delay?: number; // Delay before tracking (in ms)
  threshold?: number; // Minimum time on page before tracking (in ms)
  reviewSlug?: string; // Review slug for cache invalidation
}

/**
 * Hook to track review views with daily uniqueness
 * Only tracks once per user per review per day
 */
export function useViewTracking(
  reviewId: string | null,
  options: UseViewTrackingOptions = {}
) {
  const {
    enabled = true,
    delay = 1000, // 1 second delay
    threshold = 3000, // 3 seconds minimum time
    reviewSlug
  } = options;

  const queryClient = useQueryClient();
  const hasTracked = useRef(false);
  const startTime = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debug logging for hook initialization
  console.log('🪝 useViewTracking hook initialized:', { 
    reviewId, 
    enabled, 
    delay, 
    threshold, 
    reviewSlug,
    hasTracked: hasTracked.current 
  });

  useEffect(() => {
    console.log('⚡ useViewTracking effect triggered:', { 
      reviewId, 
      enabled, 
      hasTracked: hasTracked.current,
      shouldReturn: !reviewId || !enabled || hasTracked.current
    });
    
    if (!reviewId || !enabled || hasTracked.current) {
      console.log('🚫 useViewTracking effect early return');
      return;
    }

    startTime.current = Date.now();
    console.log('⏱️ Starting view tracking timer:', { delay, threshold, reviewId });

    // Set up tracking with delay
    timeoutRef.current = setTimeout(async () => {
      console.log('🔥 View tracking timer fired! Starting tracking process...');
      const timeOnPage = Date.now() - startTime.current;
      console.log('⏰ Time on page check:', { timeOnPage, threshold, passes: timeOnPage >= threshold });
      
      // Only track if user has been on page for minimum threshold
      if (timeOnPage >= threshold) {
        console.log('✅ Time threshold passed, proceeding with tracking...');
        try {
          // Get user's IP for anonymous tracking (optional)
          let userIp: string | undefined;
          try {
            const response = await fetch('/api/get-ip');
            if (response.ok) {
              const data = await response.json();
              userIp = data.ip;
              
              // If we got localhost, try to get external IP for development
              if (userIp === '127.0.0.1' || userIp === 'localhost' || userIp === 'unknown') {
                try {
                  const externalResponse = await fetch('https://api.ipify.org?format=json');
                  if (externalResponse.ok) {
                    const externalData = await externalResponse.json();
                    userIp = externalData.ip;
                    console.log('🌐 Using external IP for tracking:', userIp);
                  }
                } catch {
                  console.log('⚠️ External IP detection failed, using localhost');
                }
              }
            }
          } catch {
            // IP detection failed, continue without it
          }

          // Use session-based ID if no proper IP is available
          const finalUserIp = userIp || getAnonymousSessionId();
          const result = await trackReviewView(reviewId, finalUserIp);
          
          if (result.success) {
            hasTracked.current = true;
            console.log(`View tracked for review ${reviewId}:`, {
              newView: result.newView,
              totalViews: result.totalViews
            });
            
            // Invalidate review cache to refresh view count in UI
            if (result.newView && reviewSlug) {
              console.log(`🔄 Invalidating cache for review: ${reviewSlug}`);
              queryClient.invalidateQueries({ queryKey: ['review', reviewSlug] });
              queryClient.invalidateQueries({ queryKey: ['userReviews'] });
              console.log(`✅ Cache invalidated - UI should update soon`);
            } else if (result.newView) {
              console.log(`⚠️ New view tracked but no reviewSlug provided for cache invalidation`);
            } else {
              console.log(`ℹ️ View already tracked today - no increment needed`);
            }
          }
        } catch (error) {
          console.error('Failed to track view:', error);
        }
      }
    }, delay);

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [reviewId, enabled, delay, threshold, reviewSlug, queryClient]);

  // Track when user leaves page if they've been there long enough
  useEffect(() => {
    if (!reviewId || !enabled) return;

    const handleBeforeUnload = async () => {
      const timeOnPage = Date.now() - startTime.current;
      
      if (timeOnPage >= threshold && !hasTracked.current) {
        // Use navigator.sendBeacon for reliable tracking on page unload
        try {
          let userIp: string | undefined;
          try {
            const response = await fetch('/api/get-ip');
            if (response.ok) {
              const data = await response.json();
              userIp = data.ip;
            }
          } catch {
            // Continue without IP
          }

          // Use session-based ID if no proper IP is available
          const finalUserIp = userIp || getAnonymousSessionId();
          const result = await trackReviewView(reviewId, finalUserIp);
          if (result.success) {
            hasTracked.current = true;
          }
        } catch {
          // Ignore errors on page unload
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [reviewId, enabled, threshold]);
}

/**
 * Hook for intersection observer based view tracking
 * Tracks when the review content comes into view
 */
export function useIntersectionViewTracking(
  reviewId: string | null,
  targetRef: React.RefObject<Element>,
  options: UseViewTrackingOptions & {
    rootMargin?: string;
    threshold?: number;
  } = {}
) {
  const {
    enabled = true,
    delay = 1000,
    threshold = 0.5, // 50% of element must be visible
    rootMargin = '0px',
    reviewSlug
  } = options;

  const queryClient = useQueryClient();
  const hasTracked = useRef(false);
  const visibilityStartTime = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!reviewId || !enabled || !targetRef.current || hasTracked.current) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        
        if (entry.isIntersecting) {
          visibilityStartTime.current = Date.now();
          
          // Set up delayed tracking
          timeoutRef.current = setTimeout(async () => {
            if (hasTracked.current) return;
            
            try {
              let userIp: string | undefined;
              try {
                const response = await fetch('/api/get-ip');
                if (response.ok) {
                  const data = await response.json();
                  userIp = data.ip;
                  
                  // If we got localhost, try to get external IP for development
                  if (userIp === '127.0.0.1' || userIp === 'localhost' || userIp === 'unknown') {
                    try {
                      const externalResponse = await fetch('https://api.ipify.org?format=json');
                      if (externalResponse.ok) {
                        const externalData = await externalResponse.json();
                        userIp = externalData.ip;
                        console.log('🌐 Using external IP for intersection tracking:', userIp);
                      }
                    } catch {
                      console.log('⚠️ External IP detection failed for intersection tracking');
                    }
                  }
                }
              } catch {
                // Continue without IP
              }

              // Use session-based ID if no proper IP is available
          const finalUserIp = userIp || getAnonymousSessionId();
          const result = await trackReviewView(reviewId, finalUserIp);
              
              if (result.success) {
                hasTracked.current = true;
                console.log(`Intersection view tracked for review ${reviewId}:`, {
                  newView: result.newView,
                  totalViews: result.totalViews
                });
                
                // Invalidate review cache to refresh view count in UI
                if (result.newView && reviewSlug) {
                  queryClient.invalidateQueries({ queryKey: ['review', reviewSlug] });
                  queryClient.invalidateQueries({ queryKey: ['userReviews'] });
                }
              }
            } catch (error) {
              console.error('Failed to track intersection view:', error);
            }
          }, delay);
        } else {
          // Element is no longer visible, clear timeout
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
          }
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(targetRef.current);

    return () => {
      observer.disconnect();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [reviewId, enabled, delay, threshold, rootMargin, targetRef]);
}