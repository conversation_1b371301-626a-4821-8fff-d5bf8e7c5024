import { z } from 'zod';

// Username validation schema with comprehensive rules
export const UsernameSchema = z
  .string()
  .min(3, 'Username deve ter pelo menos 3 caracteres')
  .max(30, 'Username deve ter no máximo 30 caracteres')
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    'Username só pode conter letras, números, underscore e hífen'
  )
  .refine(
    (username) => !['admin', 'api', 'www', 'root', 'support', 'help', 'about', 'contact', 'terms', 'privacy', 'dashboard', 'settings', 'profile', 'user', 'users', 'game', 'games', 'review', 'reviews'].includes(username.toLowerCase()),
    'Username reservado pelo sistema'
  );

// Gaming profile schema
export const GamingProfileSchema = z.object({
  platform: z.enum(['steam', 'xbox', 'playstation', 'nintendo', 'epic', 'origin', 'uplay']),
  username: z.string().min(1, 'Username é obrigatório').max(50),
  url: z.string().url().optional().or(z.literal(''))
});

// Social media profile schema  
export const SocialMediaProfileSchema = z.object({
  platform: z.enum(['twitter', 'facebook', 'instagram', 'youtube', 'twitch', 'github', 'linkedin', 'discord', 'reddit', 'tiktok']),
  username: z.string().min(1, 'Username é obrigatório').max(50),
  url: z.string().url().optional().or(z.literal(''))
});

// Privacy settings schema
export const PrivacySettingsSchema = z.object({
  profile_visibility: z.enum(['public', 'friends', 'private']).default('public'),
  show_online_status: z.boolean().default(true),
  show_gaming_profiles: z.boolean().default(true),
  show_social_profiles: z.boolean().default(true),
  show_achievements: z.boolean().default(true),
  allow_contact: z.boolean().default(true),
  allow_friend_requests: z.boolean().default(true)
});

// Main profile schema
export const ProfileSchema = z.object({
  username: UsernameSchema,
  display_name: z
    .string()
    .min(1, 'Nome de exibição é obrigatório')
    .max(100, 'Nome de exibição deve ter no máximo 100 caracteres'),
  bio: z
    .string()
    .max(500, 'Bio deve ter no máximo 500 caracteres')
    .optional(),
  website: z
    .string()
    .url('URL inválida')
    .optional()
    .or(z.literal('')),
  location: z
    .string()
    .max(100, 'Localização deve ter no máximo 100 caracteres')
    .optional(),
  avatar_url: z
    .union([z.string(), z.null()])
    .optional()
    .refine(
      (val) => !val || val === '' || val === null || z.string().url().safeParse(val).success,
      'URL de avatar inválida'
    ),
  banner_url: z
    .union([z.string(), z.null()])
    .optional()
    .refine(
      (val) => !val || val === '' || val === null || z.string().url().safeParse(val).success,
      'URL de banner inválida'
    ),
  preferred_genres: z.array(z.string()).optional(),
  favorite_consoles: z.array(z.string()).optional(),
  theme: z.string().optional(),
  privacy_settings: PrivacySettingsSchema.optional()
});

// Profile update input schema (partial updates allowed)
export const ProfileUpdateSchema = ProfileSchema.partial().omit({ username: true });

// Username change schema (separate from profile updates)
export const UsernameChangeSchema = z.object({
  username: UsernameSchema,
  current_password: z.string().min(1, 'Senha atual é obrigatória para mudança de username')
});

// Image upload validation schema
export const ImageUploadSchema = z.object({
  file: z.instanceof(File),
  maxSize: z.number().default(5 * 1024 * 1024), // 5MB
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])
}).refine(
  (data) => data.file.size <= data.maxSize,
  'Arquivo muito grande (máximo 5MB)'
).refine(
  (data) => data.allowedTypes.includes(data.file.type),
  'Tipo de arquivo não suportado'
);

// Avatar specific validation (smaller size, square aspect ratio preferred)
export const AvatarUploadSchema = z.object({
  file: z.instanceof(File),
  maxSize: z.number().default(2 * 1024 * 1024), // 2MB for avatars
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])
}).refine(
  (data) => data.file.size <= data.maxSize,
  'Avatar deve ter no máximo 2MB'
).refine(
  (data) => data.allowedTypes.includes(data.file.type),
  'Tipo de arquivo não suportado'
);

// Banner specific validation (larger size, 16:9 aspect ratio preferred)
export const BannerUploadSchema = z.object({
  file: z.instanceof(File),
  maxSize: z.number().default(10 * 1024 * 1024), // 10MB for banners
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/webp'])
}).refine(
  (data) => data.file.size <= data.maxSize,
  'Banner deve ter no máximo 10MB'
).refine(
  (data) => data.allowedTypes.includes(data.file.type),
  'Tipo de arquivo não suportado'
);

// Username availability check schema
export const UsernameAvailabilitySchema = z.object({
  username: UsernameSchema,
  exclude_user_id: z.string().uuid().optional() // Exclude current user when checking
});

// Profile search/filter schema
export const ProfileSearchSchema = z.object({
  query: z.string().min(1).max(100).optional(),
  genres: z.array(z.string()).optional(),
  consoles: z.array(z.string()).optional(),
  online_only: z.boolean().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0)
});

// Export types for TypeScript
export type UsernameInput = z.infer<typeof UsernameSchema>;
export type PrivacySettings = z.infer<typeof PrivacySettingsSchema>;
export type ProfileInput = z.infer<typeof ProfileSchema>;
export type ProfileUpdateInput = z.infer<typeof ProfileUpdateSchema>;
export type UsernameChangeInput = z.infer<typeof UsernameChangeSchema>;
export type ImageUploadInput = z.infer<typeof ImageUploadSchema>;
export type AvatarUploadInput = z.infer<typeof AvatarUploadSchema>;
export type BannerUploadInput = z.infer<typeof BannerUploadSchema>;
export type UsernameAvailabilityInput = z.infer<typeof UsernameAvailabilitySchema>;
export type ProfileSearchInput = z.infer<typeof ProfileSearchSchema>;
