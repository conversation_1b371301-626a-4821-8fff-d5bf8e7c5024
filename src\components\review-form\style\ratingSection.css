/* ═══════════════════════════════════════════════════════════════════════════
   🎮 RATING SECTION COMPONENT - SOPHISTICATED FLOW DESIGN
   ═══════════════════════════════════════════════════════════════════════════ */

/* Core Variables for Rating Section */
:root {
    /* Rating Section Color Palette - Sophisticated & Muted */
    --rs-bg-primary: #0f172a;
    --rs-bg-secondary: rgba(30, 41, 59, 0.9);
    --rs-bg-tertiary: rgba(51, 65, 85, 0.7);
    --rs-bg-card: rgba(30, 41, 59, 0.5);
    --rs-bg-solid: #1e293b;
    --rs-gradient-subtle: linear-gradient(135deg, rgba(100, 116, 139, 0.1) 0%, rgba(71, 85, 105, 0.05) 100%);
    
    /* Rating Section Text Colors - Refined */
    --rs-text-primary: #f8fafc;
    --rs-text-secondary: #cbd5e1;
    --rs-text-muted: #94a3b8;
    --rs-text-accent: #8b7355; /* Sophisticated bronze-gray */
    --rs-text-accent-hover: #a0845c; /* Warmer on hover */
    --rs-text-success: #10b981;
    --rs-text-warning: #f59e0b;
    --rs-text-error: #ef4444;
    --rs-text-violet: #8b5cf6;
    --rs-text-cyan: #06b6d4;
    
    /* Rating Section Borders & Effects - Elegant */
    --rs-border-primary: rgba(100, 116, 139, 0.2);
    --rs-border-secondary: rgba(100, 116, 139, 0.15);
    --rs-border-accent: rgba(160, 132, 92, 0.3); /* Bronze accent */
    --rs-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    --rs-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    --rs-shadow-lg: 0 8px 12px -3px rgba(0, 0, 0, 0.25);
    --rs-shadow-score: 0 8px 25px -5px rgba(139, 92, 246, 0.15);
    
    /* Rating Section Spacing & Sizing */
    --rs-spacing-xs: 0.25rem;
    --rs-spacing-sm: 0.5rem;
    --rs-spacing-md: 0.75rem;
    --rs-spacing-lg: 1rem;
    --rs-spacing-xl: 1.5rem;
    --rs-spacing-2xl: 2rem;
    
    /* Rating Section Transitions - Smooth & Professional */
    --rs-transition: 0.15s ease-out;
    --rs-transition-spring: 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --rs-transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ═══════════════════════════════════════════════════════════════════════════
   🏗️ MAIN CONTAINER
   ═══════════════════════════════════════════════════════════════════════════ */

.rs-container {
    width: 100%;
    margin: 0 auto;
    padding: var(--rs-spacing-xl);
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

.rs-main-content {
    width: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: var(--rs-spacing-xl);
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 SCORE DISPLAY SECTION
   ═══════════════════════════════════════════════════════════════════════════ */

.rs-score-section {
    background: var(--rs-bg-solid);
    border: 1px solid var(--rs-border-secondary);
    border-radius: 0.75rem;
    padding: var(--rs-spacing-2xl);
    backdrop-filter: blur(12px);
    box-shadow: var(--rs-shadow-score);
    transition: var(--rs-transition-smooth);
    position: relative;
    overflow: hidden;
}

.rs-score-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--rs-gradient-subtle);
    opacity: 0.6;
    transition: var(--rs-transition-smooth);
}

.rs-score-section:hover {
    border-color: var(--rs-border-accent);
    box-shadow: var(--rs-shadow-lg), 0 0 25px rgba(139, 92, 246, 0.1);
    transform: translateY(-2px);
}

.rs-score-section:hover::before {
    opacity: 0.8;
}

.rs-score-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--rs-spacing-xl);
}

/* Score Display Circle */
.rs-score-display {
    display: flex;
    align-items: center;
    gap: var(--rs-spacing-xl);
}

.rs-score-circle-container {
    position: relative;
    flex-shrink: 0;
}

.rs-score-circle {
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    background: var(--rs-bg-solid);
    border: 2px solid var(--rs-border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: var(--rs-transition-spring);
    backdrop-filter: blur(8px);
}

.rs-score-circle.rs-score-changed {
    transform: scale(1.1);
    border-color: var(--rs-text-violet);
}

.rs-score-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--rs-text-primary);
    font-family: inherit;
    transition: var(--rs-transition-spring);
    z-index: 3;
    position: relative;
}

.rs-score-circle.rs-score-changed .rs-score-number {
    color: var(--rs-text-violet);
    text-shadow: 0 0 8px rgba(139, 92, 246, 0.4);
}

/* Animated Progress Ring */
.rs-progress-ring {
    position: absolute;
    inset: 0;
    width: 5rem;
    height: 5rem;
    transform: rotate(-90deg);
}

.rs-progress-ring-bg {
    fill: none;
    stroke: var(--rs-border-primary);
    stroke-width: 2;
}

.rs-progress-ring-progress {
    fill: none;
    stroke: var(--rs-text-violet);
    stroke-width: 2;
    stroke-linecap: round;
    transition: stroke-dasharray var(--rs-transition-smooth);
    filter: drop-shadow(0 0 4px rgba(139, 92, 246, 0.3));
}

/* Score Tier Info */
.rs-score-info {
    display: flex;
    flex-direction: column;
    gap: var(--rs-spacing-sm);
}

.rs-tier-display {
    display: flex;
    align-items: center;
    gap: var(--rs-spacing-sm);
}

.rs-tier-icon {
    transition: var(--rs-transition-spring);
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.1));
}

.rs-tier-name {
    font-size: 1.25rem;
    font-weight: 700;
    font-family: inherit;
    transition: var(--rs-transition);
}

.rs-tier-rank {
    font-size: 0.875rem;
    color: var(--rs-text-muted);
    font-family: inherit;
    background: rgba(100, 116, 139, 0.15);
    padding: var(--rs-spacing-xs) var(--rs-spacing-sm);
    border-radius: 0.375rem;
    border: 1px solid var(--rs-border-secondary);
    font-weight: 600;
}

.rs-tier-description {
    font-size: 0.875rem;
    color: var(--rs-text-secondary);
    font-family: inherit;
}

/* Quick Stats */
.rs-quick-stats {
    text-align: right;
    display: flex;
    flex-direction: column;
    gap: var(--rs-spacing-xs);
}

.rs-stat-item {
    font-size: 0.75rem;
    color: var(--rs-text-muted);
    font-family: inherit;
    background: rgba(100, 116, 139, 0.1);
    padding: var(--rs-spacing-xs) var(--rs-spacing-sm);
    border-radius: 0.25rem;
    border: 1px solid var(--rs-border-secondary);
    font-weight: 500;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎛️ CRITERIA SECTION
   ═══════════════════════════════════════════════════════════════════════════ */

.rs-criteria-section {
    display: flex;
    flex-direction: column;
    gap: var(--rs-spacing-lg);
}

.rs-section-header {
    display: flex;
    align-items: center;
    gap: var(--rs-spacing-sm);
    padding-bottom: var(--rs-spacing-sm);
    border-bottom: 1px solid var(--rs-border-secondary);
}

.rs-section-icon {
    color: var(--rs-text-cyan);
    flex-shrink: 0;
}

.rs-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--rs-text-accent);
    font-family: inherit;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.rs-section-title .rs-bracket {
    color: var(--rs-text-violet);
    opacity: 0.8;
    font-weight: 400;
}

/* Criteria Grid */
.rs-criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--rs-spacing-lg);
}

/* Individual Criterion Slider */
.rs-criterion {
    background: var(--rs-bg-card);
    border: 1px solid var(--rs-border-secondary);
    border-radius: 0.75rem;
    padding: var(--rs-spacing-lg);
    transition: var(--rs-transition-smooth);
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
    animation: rs-slide-in var(--rs-transition-smooth) forwards;
}

.rs-criterion::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(6, 182, 212, 0.02) 100%);
    opacity: 0;
    transition: var(--rs-transition);
}

.rs-criterion:hover {
    border-color: var(--rs-border-accent);
    transform: translateY(-2px);
    box-shadow: var(--rs-shadow-md);
}

.rs-criterion:hover::before {
    opacity: 1;
}

/* Criterion Header */
.rs-criterion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--rs-spacing-lg);
}

.rs-criterion-info {
    display: flex;
    align-items: center;
    gap: var(--rs-spacing-sm);
}

.rs-criterion-icon-container {
    padding: var(--rs-spacing-sm);
    border-radius: 0.5rem;
    background: rgba(100, 116, 139, 0.15);
    border: 1px solid var(--rs-border-secondary);
    transition: var(--rs-transition);
}

.rs-criterion:hover .rs-criterion-icon-container {
    background: rgba(139, 92, 246, 0.15);
    border-color: var(--rs-text-violet);
}

.rs-criterion-icon {
    color: var(--rs-text-violet);
    transition: var(--rs-transition);
}

.rs-criterion-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--rs-text-secondary);
    font-family: inherit;
}

.rs-criterion-name .rs-bracket {
    color: var(--rs-text-violet);
    opacity: 0.8;
}

.rs-criterion-score {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--rs-text-primary);
    font-family: inherit;
    min-width: 3rem;
    text-align: right;
    transition: var(--rs-transition-spring);
}

/* Slider Controls */
.rs-slider-container {
    position: relative;
}

.rs-slider-track {
    height: 0.5rem;
    background: rgba(100, 116, 139, 0.2);
    border-radius: 0.25rem;
    overflow: hidden;
    border: 1px solid var(--rs-border-secondary);
    position: relative;
}

.rs-slider-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--rs-text-violet) 0%, var(--rs-text-cyan) 100%);
    border-radius: 0.25rem;
    transition: width var(--rs-transition-smooth);
    position: relative;
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.2);
}

.rs-slider-progress::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: rs-shimmer 2s infinite;
}

.rs-slider-input {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.rs-slider-input:hover + .rs-slider-track {
    border-color: var(--rs-border-accent);
}

.rs-slider-input:hover + .rs-slider-track .rs-slider-progress {
    box-shadow: 0 0 12px rgba(139, 92, 246, 0.4);
}

/* Slider Markers */
.rs-slider-markers {
    display: flex;
    justify-content: space-between;
    margin-top: var(--rs-spacing-sm);
    padding: 0 var(--rs-spacing-xs);
}

.rs-slider-marker {
    font-size: 0.75rem;
    color: var(--rs-text-muted);
    font-family: inherit;
    font-weight: 500;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🧭 NAVIGATION SECTION
   ═══════════════════════════════════════════════════════════════════════════ */

.rs-navigation {
    display: flex;
    flex-direction: column;
    gap: var(--rs-spacing-lg);
    padding-top: var(--rs-spacing-xl);
    border-top: 1px solid var(--rs-border-secondary);
}

/* Status Indicator */
.rs-status-container {
    display: flex;
    align-items: center;
    gap: var(--rs-spacing-sm);
    min-width: 0;
}

.rs-status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    background: var(--rs-text-warning);
    border-radius: 50%;
    animation: rs-pulse 2s infinite;
    flex-shrink: 0;
    opacity: 0.8;
}

.rs-status-text {
    font-size: 0.875rem;
    color: var(--rs-text-muted);
    font-family: inherit;
    overflow-wrap: break-word;
    word-break: break-word;
}

.rs-status-text .rs-bracket {
    color: var(--rs-text-violet);
    opacity: 0.8;
}

/* Button Container */
.rs-button-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--rs-spacing-lg);
    flex-shrink: 0;
}

/* Navigation Buttons */
.rs-button {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: var(--rs-spacing-md) var(--rs-spacing-xl);
    font-family: inherit;
    border: 1px solid var(--rs-border-secondary);
    cursor: pointer;
    transition: var(--rs-transition-smooth);
    background: var(--rs-bg-solid);
    backdrop-filter: blur(8px);
    min-width: 120px;
}

.rs-button-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--rs-spacing-sm);
    font-weight: 600;
}

.rs-button-icon {
    width: 1rem;
    height: 1rem;
    transition: var(--rs-transition);
}

.rs-button-brackets {
    color: var(--rs-text-violet);
    opacity: 0.7;
    transition: var(--rs-transition);
    font-weight: 400;
}

/* Button States */
.rs-button-secondary {
    background: rgba(51, 65, 85, 0.6);
    color: var(--rs-text-secondary);
}

.rs-button-secondary:hover:not(:disabled) {
    background: rgba(51, 65, 85, 0.8);
    border-color: var(--rs-border-accent);
    color: var(--rs-text-primary);
    transform: translateY(-1px);
    box-shadow: var(--rs-shadow-md);
}

.rs-button-primary {
    background: linear-gradient(135deg, var(--rs-text-violet) 0%, var(--rs-text-cyan) 100%);
    color: var(--rs-text-primary);
    border-color: var(--rs-text-violet);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.rs-button-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #a78bfa 0%, #22d3ee 100%);
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
    transform: translateY(-2px);
}

.rs-button-primary:hover:not(:disabled) .rs-button-brackets {
    opacity: 1;
    color: rgba(255, 255, 255, 0.9);
}

.rs-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    background: rgba(55, 65, 81, 0.5) !important;
    color: var(--rs-text-muted) !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎬 ANIMATIONS
   ═══════════════════════════════════════════════════════════════════════════ */

@keyframes rs-slide-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rs-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(200%);
    }
}

@keyframes rs-pulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
}

/* Staggered Animation for Criteria */
.rs-criterion:nth-child(1) { animation-delay: 0ms; }
.rs-criterion:nth-child(2) { animation-delay: 100ms; }
.rs-criterion:nth-child(3) { animation-delay: 200ms; }
.rs-criterion:nth-child(4) { animation-delay: 300ms; }
.rs-criterion:nth-child(5) { animation-delay: 400ms; }
.rs-criterion:nth-child(6) { animation-delay: 500ms; }

/* ═══════════════════════════════════════════════════════════════════════════
   📱 RESPONSIVE DESIGN
   ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1024px) {
    .rs-criteria-grid {
        grid-template-columns: 1fr;
    }
    
    .rs-score-content {
        flex-direction: column;
        text-align: center;
        gap: var(--rs-spacing-lg);
    }
    
    .rs-quick-stats {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .rs-container {
        padding: var(--rs-spacing-lg);
    }
    
    .rs-score-section {
        padding: var(--rs-spacing-xl);
    }
    
    .rs-score-display {
        flex-direction: column;
        text-align: center;
        gap: var(--rs-spacing-lg);
    }
    
    .rs-navigation {
        gap: var(--rs-spacing-md);
    }
    
    .rs-button-container {
        flex-direction: column-reverse;
        align-items: stretch;
        gap: var(--rs-spacing-md);
    }
    
    .rs-status-container {
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 640px) {
    .rs-criteria-grid {
        grid-template-columns: 1fr;
        gap: var(--rs-spacing-md);
    }
    
    .rs-criterion {
        padding: var(--rs-spacing-md);
    }
    
    .rs-criterion-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--rs-spacing-sm);
    }
    
    .rs-criterion-score {
        align-self: flex-end;
        min-width: auto;
    }
    
    .rs-button {
        min-width: auto;
        width: 100%;
    }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🔧 UTILITIES
   ═══════════════════════════════════════════════════════════════════════════ */

.rs-text-legendary { color: #facc15; }
.rs-text-epic { color: #a855f7; }
.rs-text-excellent { color: #8b5cf6; }
.rs-text-great { color: #3b82f6; }
.rs-text-good { color: #10b981; }
.rs-text-decent { color: #06b6d4; }
.rs-text-average { color: #64748b; }
.rs-text-below-average { color: #f97316; }
.rs-text-mediocre { color: #f59e0b; }
.rs-text-poor { color: #ef4444; }
.rs-text-terrible { color: #dc2626; }

/* High contrast mode support */
@media (prefers-contrast: high) {
    .rs-criterion {
        border-width: 2px;
    }
    
    .rs-button {
        border-width: 2px;
        font-weight: 700;
    }
    
    .rs-slider-track {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .rs-criterion {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎛️ MODERN SLIDER STYLING
   ═══════════════════════════════════════════════════════════════════════════ */

.slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.slider-thumb::-webkit-slider-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider-thumb::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 255, 255, 0.3);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
}

.slider-thumb::-webkit-slider-thumb:hover {
  transform: scale(1.25);
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 255, 255, 0.5);
  background: #ffffff;
}

.slider-thumb::-webkit-slider-thumb:active {
  transform: scale(1.15);
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 2px 12px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(255, 255, 255, 0.6);
}

.slider-thumb::-moz-range-track {
  background: transparent;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider-thumb::-moz-range-thumb {
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  border: 3px solid #ffffff;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 255, 255, 0.3);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-appearance: none;
}

.slider-thumb::-moz-range-thumb:hover {
  transform: scale(1.25);
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 255, 255, 0.5);
  background: #ffffff;
}

/* Focus styles */
.slider-thumb:focus {
  outline: none;
}

.slider-thumb:focus::-webkit-slider-thumb {
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 0 0 4px rgba(139, 92, 246, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 255, 255, 0.3);
}

.slider-thumb:focus::-moz-range-thumb {
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.1),
    0 0 0 4px rgba(139, 92, 246, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 255, 255, 0.3);
}

/* Smooth gradient transitions - Muted/Matte colors */
.slider-gradient-track {
  background: linear-gradient(
    to right,
    #7f1d1d 0%,    /* red-900 - Terrible (muted) */
    #991b1b 10%,   /* red-800 - Poor (muted) */
    #92400e 20%,   /* amber-800 - Mediocre (muted) */
    #9a3412 30%,   /* orange-800 - Below Average (muted) */
    #64748b 40%,   /* slate-500 - Average (muted) */
    #155e75 50%,   /* cyan-800 - Decent (muted) */
    #065f46 60%,   /* emerald-800 - Good (muted) */
    #1e40af 70%,   /* blue-800 - Great (muted) */
    #5b21b6 80%,   /* violet-800 - Excellent (muted) */
    #6b21a8 90%,   /* purple-800 - Epic (muted) */
    #a16207 100%   /* yellow-700 - Legendary (muted) */
  );
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

/* Smooth overlay transitions */
.slider-overlay {
  transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 MODERN MINIMAL RATING SYSTEM
   ═══════════════════════════════════════════════════════════════════════════ */

/* Ultra-thin modern slider styling */
.slider-ultra-thin {
  -webkit-appearance: none;
  background: transparent;
  cursor: pointer;
  height: 4px;
  border-radius: 2px;
  outline: none;
}

.slider-ultra-thin::-webkit-slider-track {
  background: transparent;
  border: none;
  height: 4px;
  border-radius: 2px;
}

.slider-ultra-thin::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #8b5cf6;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
}

.slider-ultra-thin::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.4);
  border-color: #a78bfa;
}

.slider-ultra-thin::-webkit-slider-thumb:active {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.5);
}

.slider-ultra-thin::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #8b5cf6;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-appearance: none;
  border: none;
}

.slider-ultra-thin::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.4);
}

.slider-ultra-thin::-moz-range-track {
  background: transparent;
  border: none;
  height: 4px;
  border-radius: 2px;
}

/* Focus states */
.slider-ultra-thin:focus {
  outline: none;
}

.slider-ultra-thin:focus::-webkit-slider-thumb {
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25), 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.slider-ultra-thin:focus::-moz-range-thumb {
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25), 0 0 0 3px rgba(139, 92, 246, 0.2);
}

/* Elegant hover tooltip animation */
.rating-tooltip {
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  transform: translateX(-50%) translateY(-2px);
  pointer-events: none;
}

.rating-tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Smooth gradient backgrounds for sliders - No Green */
.slider-track-bg {
  background: linear-gradient(to right, 
    #dc2626 0%,   /* red-600 - Terrible */
    #ef4444 15%,  /* red-500 - Poor */
    #f59e0b 30%,  /* amber-500 - Mediocre */
    #f97316 45%,  /* orange-500 - Below Average */
    #64748b 50%,  /* slate-500 - Average */
    #06b6d4 60%,  /* cyan-500 - Decent */
    #22d3ee 70%,  /* cyan-400 - Good (no green) */
    #3b82f6 80%,  /* blue-500 - Great */
    #8b5cf6 90%,  /* violet-500 - Excellent */
    #a855f7 95%,  /* purple-500 - Epic */
    #fbbf24 100%  /* yellow-400 - Legendary */
  );
}