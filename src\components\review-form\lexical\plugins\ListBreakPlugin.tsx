'use client';

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect, useRef } from 'react';
import {
  $getSelection,
  $isRangeSelection,
  KEY_ENTER_COMMAND,
  COMMAND_PRIORITY_CRITICAL,
  $createParagraphNode,
} from 'lexical';
import {
  $isListItemNode,
  $isListNode,
  REMOVE_LIST_COMMAND,
} from '@lexical/list';

export default function ListBreakPlugin() {
  const [editor] = useLexicalComposerContext();
  const lastEnterTime = useRef<number>(0);
  const lastListItemKey = useRef<string | null>(null);

  useEffect(() => {
    return editor.registerCommand(
      KEY_ENTER_COMMAND,
      (event, editor) => {
        const selection = $getSelection();
        
        if (!$isRangeSelection(selection)) {
          return false;
        }

        const anchorNode = selection.anchor.getNode();
        let listItemNode = anchorNode;

        // Find the list item node
        while (listItemNode && !$isListItemNode(listItemNode)) {
          listItemNode = listItemNode.getParent();
        }

        if (!$isListItemNode(listItemNode)) {
          lastEnterTime.current = 0;
          lastListItemKey.current = null;
          return false;
        }

        const textContent = listItemNode.getTextContent();
        const isEmptyListItem = textContent.trim() === '' || textContent === '\n' || textContent.length <= 1;

        // Check if the previous list item is empty (regardless of current item's content)
        const previousSibling = listItemNode.getPreviousSibling();
        let shouldBreakList = false;

        if ($isListItemNode(previousSibling)) {
          const prevTextContent = previousSibling.getTextContent();
          const isPrevEmpty = prevTextContent.trim() === '' || prevTextContent === '\n' || prevTextContent.length <= 1;
          
          if (isPrevEmpty) {
            shouldBreakList = true;
          }
        }

        // For empty items, also check for double-enter
        if (isEmptyListItem) {
          const currentTime = Date.now();
          const currentKey = listItemNode.getKey();
          const timeSinceLastEnter = currentTime - lastEnterTime.current;

          if (lastListItemKey.current === currentKey && timeSinceLastEnter < 1000) {
            shouldBreakList = true;
          }

          if (!shouldBreakList) {
            // Track this enter for potential double-enter
            lastEnterTime.current = currentTime;
            lastListItemKey.current = currentKey;
          }
        } else {
          // Reset tracking if not empty
          lastEnterTime.current = 0;
          lastListItemKey.current = null;
        }

        if (shouldBreakList) {
          event?.preventDefault();

          // Break out of list by creating a paragraph after the list
          editor.update(() => {
            const listNode = listItemNode.getParent();
            
            if ($isListNode(listNode)) {
              // Remove empty previous item if it exists
              if ($isListItemNode(previousSibling)) {
                const prevTextContent = previousSibling.getTextContent();
                const isPrevEmpty = prevTextContent.trim() === '' || prevTextContent === '\n' || prevTextContent.length <= 1;
                if (isPrevEmpty) {
                  previousSibling.remove();
                }
              }
              
              // Create a new paragraph with current item's content
              const newParagraph = $createParagraphNode();
              
              // If current item has content, move it to the paragraph
              if (!isEmptyListItem) {
                // Get all children from the list item and move them to paragraph
                const children = listItemNode.getChildren();
                children.forEach(child => {
                  newParagraph.append(child);
                });
              }
              
              // Remove the current list item
              listItemNode.remove();
              
              // Insert paragraph after the list
              listNode.insertAfter(newParagraph);
              
              // Focus the new paragraph
              newParagraph.select();
            }
          });
          
          // Reset tracking
          lastEnterTime.current = 0;
          lastListItemKey.current = null;
          
          return true;
        }

        return false;
      },
      COMMAND_PRIORITY_CRITICAL
    );
  }, [editor]);

  return null;
}