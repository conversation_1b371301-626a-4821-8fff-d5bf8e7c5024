'use server';

import { createServerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import type { 
  UserReview, 
  UserSurvey, 
  UserActivity, 
  UserAchievement, 
  UserMedia, 
  UserStats,
  UserContentData,
  ContentFilters,
  ContentResponse,
  UserContentPreferences,
  UserYouTubeData,
  GetUserYouTubeDataResponse,
  UpdateYouTubeChannelResponse
} from '@/types/user-content';

// Função para obter client do Supabase com fallback para anon access
async function getSupabaseClient() {
  try {
    const cookieStore = await cookies();
    const supabase = await createServerClient(cookieStore);
    return supabase;
  } catch (error) {
    console.log('Creating Supabase client for anonymous user - no authentication required');
    // For anonymous users, create a client without authentication
    try {
      const supabase = await createServerClient();
      return supabase;
    } catch (fallbackError) {
      console.error('Error creating fallback Supabase client:', fallbackError);
      throw new Error('Failed to create Supabase client');
    }
  }
}

// Buscar reviews do usuário
export async function getUserReviews(
  userId: string,
  filters?: ContentFilters
): Promise<ContentResponse<UserReview[]>> {
  try {
    const supabase = await getSupabaseClient();

    // Use any to bypass TypeScript issues for now
    const { data, error } = await (supabase as any)
      .from('reviews')
      .select(`
        id,
        title,
        slug,
        game_name,
        author_id,
        author_name,
        overall_score,
        platforms,
        played_on,
        date_played,
        tags,
        main_image_url,
        igdb_cover_url,
        status,
        is_featured,
        view_count,
        like_count,
        comment_count,
        created_at,
        updated_at,
        content_lexical
      `)
      .eq('author_id', userId)
      .eq('status', 'published')
      .eq('is_blocked', false)
      .order('created_at', { ascending: false })
      .limit(filters?.limit || 20);

    if (error) {
      console.error('Error fetching user reviews:', error);
      return { success: false, error: error.message };
    }

    // Helper function to normalize IGDB image URLs
    const normalizeIGDBImageUrl = (url: string | null | undefined): string | undefined => {
      if (!url || url === 'undefined' || url === 'null') return undefined;
      
      // Handle double protocol issue in database
      if (url.startsWith('https:https://')) {
        url = url.replace('https:https://', 'https://');
      }
      
      // If URL already has protocol, use as is
      if (url.startsWith('https://') || url.startsWith('http://')) {
        // Convert IGDB thumbnail URLs to cover_big size for better quality
        return url.replace('t_thumb', 't_cover_big');
      }
      
      // Convert protocol-relative URLs to absolute HTTPS URLs
      let normalizedUrl = url.startsWith('//') ? `https:${url}` : `https://${url}`;
      
      // Convert IGDB thumbnail URLs to cover_big size for better quality
      return normalizedUrl.replace('t_thumb', 't_cover_big');
    };

    // Transform data to match UserReview interface
    const transformedData = (data || []).map((review: any) => ({
      id: review.id,
      user_id: review.author_id,
      game_name: review.game_name,
      game_image: review.main_image_url,
      igdb_cover_url: normalizeIGDBImageUrl(review.igdb_cover_url),
      rating: parseFloat(review.overall_score) || 0,
      review_text: typeof review.content_lexical === 'object' ? JSON.stringify(review.content_lexical) : review.content_lexical,
      created_at: review.created_at,
      updated_at: review.updated_at,
      likes_count: review.like_count || 0,
      views_count: review.view_count || 0,
      comments_count: review.comment_count || 0,
      view_count: review.view_count || 0,
      like_count: review.like_count || 0,
      comment_count: review.comment_count || 0,
      is_featured: review.is_featured || false,
      is_public: true, // Published reviews are public
      platform: review.platforms?.[0] || '',
      played_on: review.played_on || '',
      playtime_hours: undefined,
      tags: review.tags || [],
      title: review.title,
      slug: review.slug
    }));

    return { success: true, data: transformedData };
  } catch (error) {
    console.error('Error in getUserReviews:', error);
    return { success: false, error: 'Failed to fetch reviews' };
  }
}

// Buscar surveys do usuário
export async function getUserSurveys(
  userId: string, 
  filters?: ContentFilters
): Promise<ContentResponse<UserSurvey[]>> {
  try {
    const supabase = await getSupabaseClient();
    
    let query = (supabase as any)
      .from('performance_surveys')
      .select(`
        id,
        user_id,
        game_title as game_name,
        platform,
        device_type,
        cpu,
        gpu,
        total_memory,
        fps_average,
        smoothness as performance_score,
        resolution,
        created_at,
        updated_at
      `)
      .eq('user_id', userId);

    // Aplicar filtros similares aos reviews
    if (filters?.game_name) {
      query = query.ilike('game_title', `%${filters.game_name}%`);
    }
    if (filters?.date_from) {
      query = query.gte('created_at', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('created_at', filters.date_to);
    }

    const sortBy = filters?.sort_by || 'created_at';
    const sortOrder = filters?.sort_order || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user surveys:', error);
      return { success: false, error: error.message };
    }

    // Transform data to match UserSurvey interface
    const transformedData = (data || []).map((survey: any) => ({
      id: survey.id,
      user_id: survey.user_id,
      game_name: survey.game_name,
      game_image: undefined, // No image in performance_surveys table
      performance_score: survey.performance_score || 0,
      fps_average: survey.fps_average || 0,
      resolution: survey.resolution || '',
      graphics_settings: `${survey.device_type} - ${survey.cpu} / ${survey.gpu}`,
      created_at: survey.created_at,
      hardware_used: `${survey.cpu} / ${survey.gpu} / ${survey.total_memory}GB RAM`,
      notes: undefined,
      is_public: true,
      is_verified: false
    }));

    return { success: true, data: transformedData };
  } catch (error) {
    console.error('Error in getUserSurveys:', error);
    return { success: false, error: 'Failed to fetch surveys' };
  }
}

// Buscar atividades do usuário
export async function getUserActivities(
  userId: string,
  limit: number = 20
): Promise<ContentResponse<UserActivity[]>> {
  try {
    // Table user_activities doesn't exist yet, return empty array
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error in getUserActivities:', error);
    return { success: false, error: 'Failed to fetch activities' };
  }
}

// Buscar achievements do usuário
export async function getUserAchievements(
  userId: string
): Promise<ContentResponse<UserAchievement[]>> {
  try {
    // Table user_achievements exists but may have different schema, return empty array for now
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error in getUserAchievements:', error);
    return { success: false, error: 'Failed to fetch achievements' };
  }
}

// Buscar mídia do usuário
export async function getUserMedia(
  userId: string,
  filters?: ContentFilters
): Promise<ContentResponse<UserMedia[]>> {
  try {
    // Table user_media doesn't exist yet, return empty array
    return { success: true, data: [] };
  } catch (error) {
    console.error('Error in getUserMedia:', error);
    return { success: false, error: 'Failed to fetch media' };
  }
}

// Buscar estatísticas do usuário
export async function getUserStats(userId: string): Promise<ContentResponse<UserStats>> {
  try {
    const supabase = await getSupabaseClient();

    // Get reviews count from the actual reviews table
    const { data: reviewsData } = await (supabase as any)
      .from('reviews')
      .select('id, like_count, view_count, comment_count, is_featured')
      .eq('author_id', userId)
      .eq('status', 'published')
      .eq('is_blocked', false);

    // Get surveys count from performance_surveys table
    const { data: surveysData } = await (supabase as any)
      .from('performance_surveys')
      .select('id')
      .eq('user_id', userId);

    // Get profile data
    const { data: profileData } = await (supabase as any)
      .from('profiles')
      .select('created_at, last_seen')
      .eq('id', userId)
      .single();

    const reviews = reviewsData || [];
    const surveys = surveysData || [];

    // Calculate statistics
    const totalLikes = reviews.reduce((sum: number, review: any) => sum + (review.like_count || 0), 0);
    const totalViews = reviews.reduce((sum: number, review: any) => sum + (review.view_count || 0), 0);
    const totalComments = reviews.reduce((sum: number, review: any) => sum + (review.comment_count || 0), 0);
    const featuredContent = reviews.filter((review: any) => review.is_featured).length;

    const stats: UserStats = {
      user_id: userId,
      reviews_count: reviews.length,
      surveys_count: surveys.length,
      achievements_count: 0, // No achievements table data yet
      media_count: 0, // No media table data yet
      total_likes_received: totalLikes,
      total_views_received: totalViews,
      total_comments_received: totalComments,
      total_points: totalLikes + totalViews + (reviews.length * 10), // Simple point calculation
      level: Math.floor((totalLikes + totalViews + (reviews.length * 10)) / 1000) + 1,
      rank: getRankFromPoints(totalLikes + totalViews + (reviews.length * 10)),
      join_date: profileData?.created_at || new Date().toISOString(),
      last_activity: profileData?.last_seen || new Date().toISOString(),
      featured_content_count: featuredContent,
      verified_content_count: 0 // No verification system yet
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return { success: false, error: 'Failed to fetch user stats' };
  }
}

// Função auxiliar para determinar rank baseado em pontos
function getRankFromPoints(points: number): string {
  if (points >= 50000) return 'Legendary Gamer';
  if (points >= 25000) return 'Elite Critic';
  if (points >= 10000) return 'Expert Reviewer';
  if (points >= 5000) return 'Veteran Player';
  if (points >= 2500) return 'Seasoned Gamer';
  if (points >= 1000) return 'Active Member';
  if (points >= 500) return 'Rising Star';
  if (points >= 100) return 'Contributor';
  return 'Newcomer';
}

// Buscar todos os dados de conteúdo do usuário
export async function getUserContentData(userId: string): Promise<ContentResponse<UserContentData>> {
  try {
    const [reviewsRes, surveysRes, activitiesRes, achievementsRes, mediaRes, statsRes] = await Promise.allSettled([
      getUserReviews(userId, { limit: 20 }),
      getUserSurveys(userId, { limit: 20 }),
      getUserActivities(userId, 10),
      getUserAchievements(userId),
      getUserMedia(userId, { limit: 12 }),
      getUserStats(userId)
    ]);

    const data: UserContentData = {
      reviews: reviewsRes.status === 'fulfilled' && reviewsRes.value.success ? reviewsRes.value.data || [] : [],
      surveys: surveysRes.status === 'fulfilled' && surveysRes.value.success ? surveysRes.value.data || [] : [],
      activities: activitiesRes.status === 'fulfilled' && activitiesRes.value.success ? activitiesRes.value.data || [] : [],
      achievements: achievementsRes.status === 'fulfilled' && achievementsRes.value.success ? achievementsRes.value.data || [] : [],
      media: mediaRes.status === 'fulfilled' && mediaRes.value.success ? mediaRes.value.data || [] : [],
      stats: statsRes.status === 'fulfilled' && statsRes.value.success ? statsRes.value.data || {
        user_id: userId,
        reviews_count: 0,
        surveys_count: 0,
        achievements_count: 0,
        media_count: 0,
        total_likes_received: 0,
        total_views_received: 0,
        total_comments_received: 0,
        total_points: 0,
        level: 1,
        rank: 'Newcomer',
        join_date: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        featured_content_count: 0,
        verified_content_count: 0
      } : {
        user_id: userId,
        reviews_count: 0,
        surveys_count: 0,
        achievements_count: 0,
        media_count: 0,
        total_likes_received: 0,
        total_views_received: 0,
        total_comments_received: 0,
        total_points: 0,
        level: 1,
        rank: 'Newcomer',
        join_date: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        featured_content_count: 0,
        verified_content_count: 0
      },
      preferences: {
        user_id: userId,
        enabled_modules: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    return { success: true, data };
  } catch (error) {
    console.error('Error in getUserContentData:', error);
    return { success: false, error: 'Failed to fetch user content data' };
  }
}

// Buscar review em destaque do usuário
export async function getFeaturedReview(userId: string): Promise<ContentResponse<UserReview | null>> {
  try {
    console.log('🔍 getFeaturedReview called with userId:', userId);
    const supabase = await getSupabaseClient();

    // First, let's check if there are any reviews for this user at all
    const { data: allReviews, error: allReviewsError } = await (supabase as any)
      .from('reviews')
      .select('id, title, is_featured, status, is_blocked, author_id')
      .eq('author_id', userId);
    
    console.log('📋 All reviews for user:', allReviews);
    console.log('❌ All reviews error:', allReviewsError);
    
    if (allReviewsError) {
      console.error('Error fetching all reviews:', allReviewsError);
      return { success: false, error: `Database error: ${allReviewsError.message}` };
    }
    
    // Check if user has any featured reviews
    const featuredReviews = allReviews?.filter(review => review.is_featured === true);
    console.log('⭐ Featured reviews found:', featuredReviews);
    
    // Check if user has any published reviews
    const publishedReviews = allReviews?.filter(review => review.status === 'published' && !review.is_blocked);
    console.log('📖 Published reviews found:', publishedReviews);

    const { data, error } = await (supabase as any)
      .from('reviews')
      .select(`
        id,
        title,
        slug,
        game_name,
        author_id,
        author_name,
        overall_score,
        platforms,
        date_played,
        tags,
        main_image_url,
        igdb_cover_url,
        status,
        is_featured,
        view_count as views_count,
        like_count as likes_count,
        comment_count as comments_count,
        created_at,
        updated_at,
        content_lexical as review_text
      `)
      .eq('author_id', userId)
      .eq('is_featured', true)
      .eq('status', 'published')
      .eq('is_blocked', false)
      .order('like_count', { ascending: false })
      .limit(1);

    console.log('🎯 Featured review query result:', data);
    console.log('❌ Featured review query error:', error);

    if (error) {
      console.error('Error fetching featured review:', error);
      return { success: false, error: error.message };
    }

    if (!data || data.length === 0) {
      console.log('🚫 No featured review found for user:', userId);
      return { success: true, data: null };
    }

    const reviewData = data[0];
    console.log('📝 Raw review data:', reviewData);

    // Helper function to normalize IGDB image URLs (reuse from above)
    const normalizeIGDBImageUrl = (url: string | null | undefined): string | undefined => {
      if (!url || url === 'undefined' || url === 'null') return undefined;
      return url.replace('t_thumb', 't_cover_big');
    };

    // Transform data to match UserReview interface
    const transformedData = {
      id: reviewData.id,
      user_id: reviewData.author_id,
      game_name: reviewData.game_name,
      game_image: reviewData.main_image_url,
      igdb_cover_url: normalizeIGDBImageUrl(reviewData.igdb_cover_url),
      rating: parseFloat(reviewData.overall_score) || 0,
      review_text: typeof reviewData.review_text === 'object' ? JSON.stringify(reviewData.review_text) : reviewData.review_text,
      created_at: reviewData.created_at,
      updated_at: reviewData.updated_at,
      likes_count: reviewData.likes_count || 0,
      views_count: reviewData.views_count || 0,
      comments_count: reviewData.comments_count || 0,
      is_featured: reviewData.is_featured || false,
      is_public: true,
      platform: reviewData.platforms?.[0] || '',
      playtime_hours: undefined,
      tags: reviewData.tags || [],
      title: reviewData.title,
      slug: reviewData.slug
    };

    console.log('✨ Transformed review data:', transformedData);
    return { success: true, data: transformedData };
  } catch (error) {
    console.error('💥 Error in getFeaturedReview:', error);
    return { success: false, error: 'Failed to fetch featured review' };
  }
}

// Incrementar views de conteúdo
export async function incrementContentViews(
  contentType: 'review' | 'survey' | 'media',
  contentId: string
): Promise<ContentResponse<boolean>> {
  try {
    const supabase = await getSupabaseClient();
    
    const tableName = contentType === 'review' ? 'user_reviews' : 
                     contentType === 'survey' ? 'user_surveys' : 'user_media';
    
    const { error } = await supabase
      .from(tableName)
      .update({ 
        views_count: supabase.raw('views_count + 1') 
      })
      .eq('id', contentId);

    if (error) {
      console.error(`Error incrementing ${contentType} views:`, error);
      return { success: false, error: error.message };
    }

    return { success: true, data: true };
  } catch (error) {
    console.error('Error in incrementContentViews:', error);
    return { success: false, error: 'Failed to increment views' };
  }
}

// Toggle like em conteúdo
export async function toggleContentLike(
  contentType: 'review' | 'survey' | 'media',
  contentId: string,
  userId: string
): Promise<ContentResponse<boolean>> {
  try {
    const supabase = await getSupabaseClient();
    
    // Verificar se já curtiu
    const { data: existingLike } = await supabase
      .from('content_likes')
      .select('id')
      .eq('content_type', contentType)
      .eq('content_id', contentId)
      .eq('user_id', userId)
      .single();

    if (existingLike) {
      // Remover like
      await supabase
        .from('content_likes')
        .delete()
        .eq('id', existingLike.id);
      
      // Decrementar contador
      const tableName = contentType === 'review' ? 'user_reviews' : 
                       contentType === 'survey' ? 'user_surveys' : 'user_media';
      
      await supabase
        .from(tableName)
        .update({ 
          likes_count: supabase.raw('GREATEST(likes_count - 1, 0)') 
        })
        .eq('id', contentId);
      
      return { success: true, data: false };
    } else {
      // Adicionar like
      await supabase
        .from('content_likes')
        .insert({
          content_type: contentType,
          content_id: contentId,
          user_id: userId,
          created_at: new Date().toISOString()
        });
      
      // Incrementar contador
      const tableName = contentType === 'review' ? 'user_reviews' : 
                       contentType === 'survey' ? 'user_surveys' : 'user_media';
      
      await supabase
        .from(tableName)
        .update({ 
          likes_count: supabase.raw('likes_count + 1') 
        })
        .eq('id', contentId);
      
      return { success: true, data: true };
    }
  } catch (error) {
    console.error('Error in toggleContentLike:', error);
    return { success: false, error: 'Failed to toggle like' };
  }
}

// YouTube API helper functions
async function fetchYouTubeChannelData(channelUrl: string): Promise<UserYouTubeData | null> {
  try {
    const apiKey = process.env.YOUTUBE_API_KEY;
    if (!apiKey) {
      throw new Error('YouTube API key not configured');
    }

    // Extract channel ID or username from URL
    const channelId = extractYouTubeChannelId(channelUrl);
    if (!channelId) {
      throw new Error('Invalid YouTube channel URL');
    }

    // Fetch channel details
    const channelResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&${channelId.type}=${channelId.value}&key=${apiKey}`
    );

    if (!channelResponse.ok) {
      throw new Error('Failed to fetch channel data');
    }

    const channelData = await channelResponse.json();
    
    if (!channelData.items || channelData.items.length === 0) {
      throw new Error('Channel not found');
    }

    const channel = channelData.items[0];
    const actualChannelId = channel.id;

    // Fetch latest videos
    const videosResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/search?part=snippet&channelId=${actualChannelId}&order=date&maxResults=12&type=video&key=${apiKey}`
    );

    if (!videosResponse.ok) {
      throw new Error('Failed to fetch videos');
    }

    const videosData = await videosResponse.json();

    // Get video statistics
    const videoIds = videosData.items?.map((item: any) => item.id.videoId).join(',') || '';
    const videoStatsResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?part=statistics,contentDetails&id=${videoIds}&key=${apiKey}`
    );

    const videoStats = videoStatsResponse.ok ? await videoStatsResponse.json() : { items: [] };

    // Process videos
    const videos = videosData.items?.map((item: any, index: number) => {
      const stats = videoStats.items?.find((stat: any) => stat.id === item.id.videoId);
      return {
        id: `youtube_${item.id.videoId}`,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnail: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
        publishedAt: item.snippet.publishedAt,
        duration: stats?.contentDetails?.duration || 'PT0S',
        viewCount: parseInt(stats?.statistics?.viewCount || '0'),
        likeCount: parseInt(stats?.statistics?.likeCount || '0'),
        commentCount: parseInt(stats?.statistics?.commentCount || '0'),
        videoId: item.id.videoId,
        url: `https://www.youtube.com/watch?v=${item.id.videoId}`
      };
    }) || [];

    return {
      userId: '', // Will be set by caller
      channelUrl,
      channelId: actualChannelId,
      channel: {
        id: actualChannelId,
        title: channel.snippet.title,
        description: channel.snippet.description,
        thumbnail: channel.snippet.thumbnails.high?.url || channel.snippet.thumbnails.medium?.url || channel.snippet.thumbnails.default?.url,
        subscriberCount: parseInt(channel.statistics.subscriberCount || '0'),
        videoCount: parseInt(channel.statistics.videoCount || '0'),
        viewCount: parseInt(channel.statistics.viewCount || '0'),
        customUrl: channel.snippet.customUrl,
        publishedAt: channel.snippet.publishedAt
      },
      videos,
      lastFetched: new Date().toISOString(),
      isValid: true
    };
  } catch (error) {
    console.error('YouTube API Error:', error);
    return null;
  }
}

function extractYouTubeChannelId(url: string): { type: string; value: string } | null {
  try {
    const urlObj = new URL(url);
    
    // Handle different YouTube URL formats
    if (urlObj.hostname.includes('youtube.com')) {
      // Channel ID format: /channel/UC...
      if (urlObj.pathname.startsWith('/channel/')) {
        return { type: 'id', value: urlObj.pathname.split('/channel/')[1] };
      }
      
      // Custom URL format: /c/channelname or /user/username
      if (urlObj.pathname.startsWith('/c/')) {
        return { type: 'forUsername', value: urlObj.pathname.split('/c/')[1] };
      }
      
      if (urlObj.pathname.startsWith('/user/')) {
        return { type: 'forUsername', value: urlObj.pathname.split('/user/')[1] };
      }
      
      // Handle @username format
      if (urlObj.pathname.startsWith('/@')) {
        return { type: 'forHandle', value: urlObj.pathname.split('/@')[1] };
      }
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

// YouTube Server Actions
export async function getUserYouTubeData(userId: string, channelUrl?: string): Promise<GetUserYouTubeDataResponse> {
  try {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    const supabase = await getSupabaseClient();

    // If channelUrl provided, fetch fresh data from API
    if (channelUrl) {
      const youtubeData = await fetchYouTubeChannelData(channelUrl);
      if (youtubeData) {
        youtubeData.userId = userId;
        return { success: true, data: youtubeData };
      } else {
        return { success: false, error: 'Failed to fetch YouTube channel data' };
      }
    }

    // Try to get data from database first
    const { data: youtubeData, error: youtubeError } = await supabase
      .from('user_youtube_data')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (youtubeError && youtubeError.code !== 'PGRST116') {
      console.error('Error fetching YouTube data from database:', youtubeError);
      return { success: false, error: 'Failed to fetch YouTube data' };
    }

    if (!youtubeData) {
      // No YouTube data found for this user
      return { success: false, error: 'No YouTube channel connected' };
    }

    // Get videos from database
    const { data: videos, error: videosError } = await supabase
      .from('user_youtube_videos')
      .select('*')
      .eq('user_id', userId)
      .order('published_at', { ascending: false })
      .limit(12);

    if (videosError) {
      console.error('Error fetching YouTube videos:', videosError);
      // Continue without videos rather than failing completely
    }

    // Transform database data to expected format
    const transformedData: UserYouTubeData = {
      userId,
      channelUrl: youtubeData.channel_url,
      channelId: youtubeData.channel_id,
      channel: {
        id: youtubeData.channel_id,
        title: youtubeData.channel_title,
        description: youtubeData.channel_description || '',
        thumbnail: youtubeData.channel_thumbnail || '',
        subscriberCount: youtubeData.subscriber_count || 0,
        videoCount: youtubeData.video_count || 0,
        viewCount: youtubeData.view_count || 0,
        customUrl: youtubeData.custom_url || '',
        publishedAt: youtubeData.published_at || ''
      },
      videos: (videos || []).map(video => ({
        id: video.id,
        title: video.title,
        description: video.description || '',
        thumbnail: video.thumbnail || '',
        publishedAt: video.published_at || '',
        duration: video.duration || '',
        viewCount: video.view_count || 0,
        likeCount: video.like_count || 0,
        commentCount: video.comment_count || 0,
        videoId: video.video_id,
        url: video.url
      })),
      lastFetched: youtubeData.last_fetched || '',
      isValid: youtubeData.is_valid || false,
      error: youtubeData.error_message
    };

    return { success: true, data: transformedData, cached: true };
  } catch (error) {
    console.error('Error fetching YouTube data:', error);
    return { success: false, error: 'Failed to fetch YouTube data' };
  }
}

export async function updateYouTubeChannel(userId: string, channelUrl: string): Promise<UpdateYouTubeChannelResponse> {
  try {
    if (!userId || !channelUrl) {
      return { success: false, error: 'User ID and channel URL are required' };
    }

    const youtubeData = await fetchYouTubeChannelData(channelUrl);

    if (!youtubeData) {
      return { success: false, error: 'Invalid YouTube channel URL or channel not found' };
    }

    youtubeData.userId = userId;

    const supabase = await getSupabaseClient();

    // Save channel data to database
    const channelData = {
      user_id: userId,
      channel_url: channelUrl,
      channel_id: youtubeData.channelId,
      channel_title: youtubeData.channel.title,
      channel_description: youtubeData.channel.description,
      channel_thumbnail: youtubeData.channel.thumbnail,
      subscriber_count: youtubeData.channel.subscriberCount,
      video_count: youtubeData.channel.videoCount,
      view_count: youtubeData.channel.viewCount,
      custom_url: youtubeData.channel.customUrl,
      published_at: youtubeData.channel.publishedAt,
      last_fetched: new Date().toISOString(),
      is_valid: true,
      error_message: null,
      updated_at: new Date().toISOString()
    };

    const { error: channelError } = await supabase
      .from('user_youtube_data')
      .upsert(channelData, { onConflict: 'user_id' });

    if (channelError) {
      console.error('Error saving YouTube channel data:', channelError);
      return { success: false, error: 'Failed to save channel data' };
    }

    // Save videos to database
    if (youtubeData.videos && youtubeData.videos.length > 0) {
      const videosData = youtubeData.videos.map(video => ({
        user_id: userId,
        video_id: video.videoId,
        title: video.title,
        description: video.description,
        thumbnail: video.thumbnail,
        published_at: video.publishedAt,
        duration: video.duration,
        view_count: video.viewCount,
        like_count: video.likeCount,
        comment_count: video.commentCount,
        url: video.url,
        created_at: new Date().toISOString()
      }));

      // Clear existing videos and insert new ones
      await supabase
        .from('user_youtube_videos')
        .delete()
        .eq('user_id', userId);

      const { error: videosError } = await supabase
        .from('user_youtube_videos')
        .insert(videosData);

      if (videosError) {
        console.error('Error saving YouTube videos:', videosError);
        // Don't fail the operation for videos, channel data is more important
      }
    }

    return { success: true, data: youtubeData };
  } catch (error) {
    console.error('Error updating YouTube channel:', error);
    return { success: false, error: 'Failed to update YouTube channel' };
  }
}