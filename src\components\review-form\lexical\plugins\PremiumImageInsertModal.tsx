// src/components/review-form/lexical/plugins/PremiumImageInsertModal.tsx
'use client';

import React, { useState, useCallback, useRef } from 'react';
import { X, Upload, Image as ImageIcon, Trash2, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useB2ImageUpload, UploadResult } from '@/hooks/useB2ImageUpload';

interface ImageData {
  id: string;
  src: string;
  altText: string;
  caption?: string;
  key?: string; // B2 storage key
  isUploaded: boolean;
}

interface PremiumImageInsertModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (images: Array<{src: string; altText: string; caption?: string}>) => void;
}

export default function PremiumImageInsertModal({
  isOpen,
  onClose,
  onInsert
}: PremiumImageInsertModalProps) {
  const [images, setImages] = useState<ImageData[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');
  const [isInserting, setIsInserting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // B2 Upload hook
  const {
    uploads,
    isUploading,
    uploadFiles,
    cancelUploads,
    clearUploads,
    getUploadStats,
  } = useB2ImageUpload({
    maxFiles: 1,
    onUploadComplete: handleUploadComplete,
    onUploadError: handleUploadError,
  });

  const generateId = () => Math.random().toString(36).substr(2, 9);

  // Handle successful uploads
  function handleUploadComplete(results: UploadResult[]) {
    const newImages: ImageData[] = results.map(result => ({
      id: generateId(),
      src: result.url!,
      altText: result.metadata?.originalName?.replace(/\.[^/.]+$/, '') || 'Image',
      caption: '',
      key: result.key,
      isUploaded: true,
    }));

    setImages(prev => [...prev, ...newImages]);
    setUploadError('');
  }

  // Handle upload errors
  function handleUploadError(error: string) {
    setUploadError(error);
  }

  // File selection handlers
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    uploadFiles(fileArray);
  }, [uploadFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  // Image management
  const updateImage = useCallback((id: string, field: keyof ImageData, value: string) => {
    setImages(prev => prev.map(img =>
      img.id === id ? { ...img, [field]: value } : img
    ));
  }, []);

  const removeImage = useCallback(async (id: string) => {
    const image = images.find(img => img.id === id);

    // If image is uploaded to B2, delete it
    if (image?.isUploaded && image.key) {
      try {
        await fetch('/api/b2/delete', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ key: image.key }),
        });
      } catch (error) {
        console.error('Failed to delete image from B2:', error);
        // Continue with local removal even if B2 deletion fails
      }
    }

    setImages(prev => prev.filter(img => img.id !== id));
  }, [images]);

  // Insert images into editor
  const handleInsert = useCallback(async () => {
    if (images.length === 0) return;

    setIsInserting(true);

    try {
      // Only insert successfully uploaded images
      const uploadedImages = images.filter(img => img.isUploaded);

      if (uploadedImages.length === 0) {
        setUploadError('No successfully uploaded images to insert');
        return;
      }

      const imagesToInsert = uploadedImages.map(img => ({
        src: img.src,
        altText: img.altText || 'Image',
        caption: img.caption
      }));

      onInsert(imagesToInsert);
      handleClose();
    } catch (error) {
      setUploadError('Failed to insert images');
    } finally {
      setIsInserting(false);
    }
  }, [images, onInsert]);

  // Modal close handler
  const handleClose = useCallback(() => {
    // Cancel any ongoing uploads
    if (isUploading) {
      cancelUploads();
    }

    setImages([]);
    setUploadError('');
    clearUploads();
    onClose();
  }, [isUploading, cancelUploads, clearUploads, onClose]);

  // Get current status
  const stats = getUploadStats();
  const hasUploadedImages = images.some(img => img.isUploaded);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-900 rounded-lg border border-slate-700 w-full max-w-lg overflow-hidden shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <div className="flex items-center gap-2">
            <ImageIcon className="w-5 h-5 text-purple-400" />
            <h2 className="text-lg font-semibold text-white">Upload Image</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-slate-400 hover:text-white transition-colors"
            disabled={isUploading}
            type="button"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* Upload Error */}
          {uploadError && (
            <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm">{uploadError}</span>
            </div>
          )}

          {/* Upload Status */}
          {isUploading && (
            <div className="p-3 bg-slate-800 rounded-lg border border-slate-700 flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
              <span className="text-slate-300 text-sm">Uploading...</span>
            </div>
          )}

          {/* Drop Zone */}
          {!images.length && (
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                isDragOver
                  ? 'border-purple-400 bg-purple-400/10'
                  : 'border-slate-600 hover:border-slate-500'
              } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <Upload className="w-8 h-8 text-slate-400 mx-auto mb-3" />
              <p className="text-slate-300 mb-2">
                Drag & drop an image here, or{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-purple-400 hover:text-purple-300 underline"
                  type="button"
                >
                  browse files
                </button>
              </p>
              <p className="text-slate-500 text-sm">
                JPG, PNG, GIF, WebP • Max 10MB
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInputChange}
                className="hidden"
                disabled={isUploading}
                title="Select an image file"
                aria-label="Select an image file for upload"
              />
            </div>
          )}

          {/* Uploaded Image */}
          {images.length > 0 && (
            <div className="space-y-3">
              {images.map((image) => (
                <div key={image.id} className="bg-slate-800 rounded-lg border border-slate-700 p-4">
                  <div className="flex gap-3 items-start">
                    <img
                      src={image.src}
                      alt={image.altText}
                      className="w-16 h-16 object-cover rounded border border-slate-600"
                    />
                    <div className="flex-1 space-y-3">
                      <input
                        type="text"
                        value={image.altText}
                        onChange={(e) => updateImage(image.id, 'altText', e.target.value)}
                        className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-slate-400 focus:outline-none focus:border-purple-400"
                        placeholder="Alt text"
                      />
                      <input
                        type="text"
                        value={image.caption || ''}
                        onChange={(e) => updateImage(image.id, 'caption', e.target.value)}
                        className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-slate-400 focus:outline-none focus:border-purple-400"
                        placeholder="Caption (optional)"
                      />
                    </div>
                    <button
                      onClick={() => removeImage(image.id)}
                      className="text-slate-400 hover:text-red-400 transition-colors"
                      title="Remove"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-slate-700">
          <div className="text-sm text-slate-400">
            {images.length > 0 && hasUploadedImages && (
              <span className="text-green-400">Ready to insert</span>
            )}
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
              disabled={isInserting}
              type="button"
            >
              Cancel
            </button>
            <button
              onClick={handleInsert}
              disabled={!hasUploadedImages || isUploading || isInserting}
              className="px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:bg-slate-600 disabled:text-slate-400 text-white font-medium rounded transition-colors flex items-center gap-2"
              type="button"
            >
              {isInserting && <Loader2 className="w-4 h-4 animate-spin" />}
              {isInserting ? 'Inserting...' : 'Insert'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
