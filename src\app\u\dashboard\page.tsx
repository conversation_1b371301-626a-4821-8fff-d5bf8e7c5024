'use client';

import { useState, useMemo, useCallback, Suspense, useEffect } from 'react';
import { useAuthContext } from '@/contexts/auth-context';
import { useUserReviews } from '@/hooks/useUserReviews';
import { useUserPerformanceSurveys } from '@/hooks/useUserPerformanceSurveys';
import { useUserStats } from '@/hooks/useUserStats';
import { useSearchParams } from 'next/navigation';
// DASHBOARD REDESIGN: Phase 1 - Layout Integration  
// Date: 15/06/2025
// Task: dashboardStyleAdmin001
// 
// Updated imports to use new UserDashboardLayout instead of SimplifiedDashboard
// - Replaced SimplifiedDashboard with UserDashboardLayout matching AdminLayout structure
// - Maintained all existing functionality and components
// - Applied new layout structure with fixed header and responsive grid

import { UserDashboardLayout } from '@/components/dashboard/UserDashboardLayout';
import { ModernOverviewSection } from '@/components/dashboard/ModernOverviewSection';
import { ModernReviewsSection } from '@/components/dashboard/ModernReviewsSection';
import { ModernPerformanceSection } from '@/components/dashboard/ModernPerformanceSection';
import { SuspensionNotice } from '@/components/suspension/SuspensionNotice';
import { SuspensionGuard } from '@/components/suspension/SuspensionGuard';
import YouTubeChannelConfig from '@/components/dashboard/YouTubeChannelConfig';
import MultipleFeaturedBannersConfig from '@/components/dashboard/MultipleFeaturedBannersConfig';
import CompactBannerConfig from '@/components/dashboard/CompactBannerConfig';
import ReviewDisplaySettings from './ReviewDisplaySettings';
import PrivacySettings from '@/components/dashboard/PrivacySettings';
import GamingConnectionsSection from '@/components/dashboard/GamingConnectionsSection';
import SocialConnectionsSection from '@/components/dashboard/SocialConnectionsSection';
import { CommentModerationSection } from '@/components/dashboard/comments/CommentModerationSection';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
// PRIVACY SETTINGS COMPONENT REMOVED - component deleted as part of Firebase cleanup
// TODO: Rebuild privacy settings with Supabase when ready
import { usePerformanceMonitor } from '@/components/dashboard/DashboardOptimizations';

// Import the CSS that contains adaptive-text-title styles
import '@/components/review-form/style/NewReview.css';
import EditProfileModal from '@/components/userprofile/EditProfileModal';
import {
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { DashboardStats } from '@/types/dashboard';
import type { UnifiedUserProfile } from '@/lib/types/profile';
import {
  exportUserData,
  bulkUpdateReviewsPrivacy,
  bulkUpdateSurveysPrivacy,
  requestDataDeletion
} from '@/lib/services/privacyService';
import { useToast } from '@/hooks/use-toast';

// Component that uses useSearchParams - needs to be wrapped in Suspense
function DashboardWithParams() {
  const searchParams = useSearchParams();
  const initialTab = searchParams.get('tab') || 'settings';

  return <UserDashboardContent initialTab={initialTab} />;
}

function UserDashboardContent({ initialTab }: { initialTab: string }) {
  usePerformanceMonitor('UserDashboardPage');

  const { user, loading: authLoading, isSuspended, suspensionReason, suspendedAt } = useAuthContext();
  const [activeTab, setActiveTab] = useState(initialTab);
  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);

  // Background brightness detection for adaptive text styling
  const isDarkBackground = useBackgroundBrightness();
  const router = useRouter();
  const { toast } = useToast();

  // Fetch user data with optimized queries
  const {
    reviews,
    loading: reviewsLoading,
    error: reviewsError,
    refetch: refetchReviews,
    totalCount: reviewCount
  } = useUserReviews(user?.uid || null, { realTime: true });

  const {
    surveys,
    loading: surveysLoading,
    error: surveysError,
    totalCount: surveyCount,
    deleteSurvey
  } = useUserPerformanceSurveys(user?.uid || null);

  // Get comprehensive user stats including total views
  const { stats: userStats, loading: userStatsLoading } = useUserStats(user?.uid || null);

  // Calculate dashboard statistics with memoization
  const dashboardStats = useMemo((): DashboardStats => {
    // Ensure reviews is an array to prevent undefined errors
    const safeReviews = reviews || [];

    const publishedReviews = safeReviews.filter(review =>
      review.status === 'published' || !review.status
    ).length;

    const draftReviews = safeReviews.filter(review =>
      review.status === 'draft'
    ).length;

    const averageScore = safeReviews.length > 0
      ? safeReviews.reduce((sum, review) => sum + (review.overallScore || 0), 0) / safeReviews.length
      : 0;

    const lastActivity = safeReviews.length > 0
      ? new Date(Math.max(...safeReviews.map(r => {
          const date = r.createdAt;
          if (!date) return 0;
          if (date instanceof Date) return date.getTime();
          if (typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {
            return (date.toDate() as Date).getTime();
          }
          return new Date(date as string).getTime();
        })))
      : null;

    return {
      totalReviews: reviewCount,
      totalSurveys: surveyCount,
      averageScore: Math.round(averageScore * 10) / 10,
      joinDate: user?.createdAt || null,
      lastActivity,
      publishedReviews,
      draftReviews,
      // Add stats from userStats hook
      totalViews: userStats?.totalViews || 0,
      totalLikes: userStats?.totalLikes || 0,
      totalComments: userStats?.totalComments || 0,
      totalFollowers: 0 // Placeholder for future implementation
    };
  }, [reviews, reviewCount, surveyCount, user, userStats]);

  // Optimized handlers
  const handleCreateReview = useCallback(() => {
    router.push('/reviews/create');
  }, [router]);

  const handleViewProfile = useCallback(() => {
    if (user?.slug) {
      router.push(`/u/${user.slug}`);
    }
  }, [router, user?.slug]);

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  // Privacy handlers
  const handleDataExport = useCallback(async () => {
    if (!user?.uid) return;

    try {
      const exportData = await exportUserData(user.uid);
      if (exportData) {
        // Create and download JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `criticalpixel-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "Data Export Complete",
          description: "Your data has been downloaded successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export your data. Please try again.",
        variant: "destructive"
      });
    }
  }, [user?.uid, toast]);

  const handleBulkPrivacyUpdate = useCallback(async (type: 'reviews' | 'surveys', isPrivate: boolean) => {
    if (!user?.uid) return;

    try {
      let result;
      if (type === 'reviews') {
        result = await bulkUpdateReviewsPrivacy(user.uid, isPrivate);
      } else {
        result = await bulkUpdateSurveysPrivacy(user.uid, isPrivate);
      }

      if (result.success) {
        toast({
          title: "Privacy Updated",
          description: `${result.updatedCount || 0} ${type} updated successfully.`,
        });
        // Refresh data
        refetchReviews();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: `Failed to update ${type} privacy. Please try again.`,
        variant: "destructive"
      });
    }
  }, [user?.uid, toast, refetchReviews]);

  const handleRequestDeletion = useCallback(async () => {
    if (!user?.uid) return;

    try {
      const result = await requestDataDeletion(user.uid);
      if (result.success) {
        toast({
          title: "Deletion Request Submitted",
          description: "Your data deletion request has been submitted and will be processed within 30 days.",
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Request Failed",
        description: "Failed to submit deletion request. Please try again.",
        variant: "destructive"
      });
    }
  }, [user?.uid, toast]);

  // Handle edit profile modal
  const handleOpenEditProfile = useCallback(() => {
    setIsEditProfileModalOpen(true);
  }, []);

  const handleCloseEditProfile = useCallback(() => {
    setIsEditProfileModalOpen(false);
  }, []);

  const handleSaveProfile = useCallback(async (updatedProfile: Partial<UnifiedUserProfile>) => {
    if (!user?.uid) return;

    try {
      // Here you would typically call an API to update the user profile
      // For now, we'll just show a success message and close the modal
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
      setIsEditProfileModalOpen(false);
      
      // Optionally refresh user data here
      // await refetchUserData();
      
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    }
  }, [user?.uid, toast]);

  // Loading state
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-3 text-slate-300">
          <Loader2 className="animate-spin" size={24} />
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }

  // Authentication check
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <AlertCircle className="mx-auto text-red-400" size={48} />
          <h1 className="text-xl font-semibold text-slate-200">Authentication Required</h1>
          <p className="text-slate-400">Please log in to view your dashboard.</p>
          <Button asChild>
            <Link href="/auth/login">Sign In</Link>
          </Button>
        </div>
      </div>
    );
  }

  // SECURITY: Complete suspension check - block dashboard access for suspended users
  if (isSuspended) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <SuspensionNotice
            reason={suspensionReason}
            suspendedAt={suspendedAt}
            variant="card"
            showActions={false}
          />
          <div className="mt-6 text-center">
            <Button asChild variant="outline">
              <Link href="/">Return Home</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const isLoading = reviewsLoading || surveysLoading || userStatsLoading;
  const hasError = reviewsError || surveysError;

  return (
    <UserDashboardLayout
      activeTab={activeTab}
      onTabChange={handleTabChange}
      stats={{
        totalReviews: dashboardStats.totalReviews,
        publishedReviews: dashboardStats.publishedReviews,
        averageScore: dashboardStats.averageScore || 0,
        totalViews: dashboardStats.totalViews,
        totalLikes: dashboardStats.totalLikes,
        totalComments: dashboardStats.totalComments,
        totalFollowers: dashboardStats.totalFollowers
      }}
    >
      {/* Suspension Notice */}
      {isSuspended && (
        <div className="p-6">
          <SuspensionNotice
            reason={suspensionReason}
            suspendedAt={suspendedAt}
            variant="card"
            showActions={true}
          />
        </div>
      )}
      {/* Error Display */}
      {hasError && (
        <div className="p-6">
          <Alert className="border-red-500/50 bg-red-500/10">
            <AlertCircle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-300">
              {reviewsError || surveysError}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Tab Content */}
      {renderTabContent()}

      {/* Edit Profile Modal */}
      {user && user.uid && (
        <EditProfileModal
          profile={{
            // Core fields from UnifiedUserProfile
            id: user.uid,
            username: user.userName || 'user',
            slug: user.slug || user.userName || 'user',
            slug_lower: (user.slug || user.userName || 'user').toLowerCase(),
            display_name: user.displayName || user.userName || 'User',
            
            // Legacy compatibility fields
            uid: user.uid,
            email: user.email || '',
            displayName: user.displayName || '',
            userName: user.userName || '',
            bio: (user as any).bio || '',
            avatarUrl: (user as any).avatarUrl || '',
            bannerUrl: (user as any).bannerUrl || '',
            theme: (user as any).theme || 'muted-dark',
            preferredGenres: (user as any).preferredGenres || [],
            favoriteConsoles: (user as any).favoriteConsoles || [],
            gamingProfiles: (user as any).gamingProfiles || [],
            socialMedia: (user as any).socialMedia || [],
            customColors: (user as any).customColors || {
              primary: '#8b5cf6',
              secondary: '#7c3aed',
              accent: '#ec4899'
            },
            
            // Timestamps
            createdAt: user.createdAt || new Date(),
            updatedAt: new Date(),
            created_at: user.createdAt?.toISOString() || new Date().toISOString(),
            updated_at: new Date().toISOString()
          }}
          open={isEditProfileModalOpen}
          onClose={handleCloseEditProfile}
          onSave={handleSaveProfile}
        />
      )}
    </UserDashboardLayout>
  );

  function renderTabContent() {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-slate-300">
            <Loader2 className="animate-spin" size={24} />
            <span>Loading {activeTab}...</span>
          </div>
        </div>
      );
    }

    switch (activeTab) {
      case 'settings':
        return renderSettingsTab(isDarkBackground);
      case 'privacy':
        return renderPrivacyTab(isDarkBackground);
      case 'reviews':
        return renderReviewsTab(isDarkBackground);
      case 'comments':
        return renderCommentsTab(isDarkBackground);
      case 'performance':
        return renderPerformanceTab(isDarkBackground);
      case 'connections':
        return renderConnectionsTab(isDarkBackground);
      case 'overview':
        return renderOverviewTab();
      default:
        return renderSettingsTab(isDarkBackground);
    }
  }

  function renderOverviewTab() {
    if (!user || !user.uid) return null;
    
    // Create user object with required uid field as string
    const userWithRequiredUid = {
      ...user,
      uid: user.uid as string // Ensure uid is string, not undefined
    };
    
    return (
      <ModernOverviewSection
        user={userWithRequiredUid}
        reviews={reviews}
        surveys={surveys}
        stats={dashboardStats}
        isLoading={isLoading}
        onEditProfile={handleOpenEditProfile}
      />
    );
  }

  function renderReviewsTab(isDarkBackground: boolean) {
    return (
      <SuspensionGuard
        fallback={
          <div className="p-6 text-center">
            <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-8">
              <h3 className="text-lg font-semibold text-slate-200 mb-2">Review Creation Suspended</h3>
              <p className="text-slate-400 mb-4">
                You cannot create or edit reviews while your account is suspended.
              </p>
              <p className="text-sm text-slate-500">
                You can still view your existing reviews below.
              </p>
            </div>
          </div>
        }
      >
        <ModernReviewsSection
          reviews={reviews}
          isLoading={isLoading}
          onRefresh={refetchReviews}
          isDarkBackground={isDarkBackground}
        />
      </SuspensionGuard>
    );
  }

  function renderCommentsTab(isDarkBackground: boolean) {
    return (
      <div className="p-2 sm:p-3 lg:p-4 xl:p-6">
        <CommentModerationSection isDarkBackground={isDarkBackground} />
      </div>
    );
  }

  function renderPerformanceTab(isDarkBackground: boolean) {
    return (
      <ModernPerformanceSection
        surveys={surveys}
        loading={surveysLoading}
        onDeleteSurvey={async (surveyId: string) => {
          const success = await deleteSurvey(surveyId);
          if (!success) {
            // Error handling is already done in the hook
            console.error('Failed to delete survey');
          }
        }}
        isDarkBackground={isDarkBackground}
      />
    );
  }

  function renderSettingsTab(isDarkBackground: boolean) {
    if (!user?.uid) return null;

    return (
      <div className="p-2 sm:p-3 lg:p-4 xl:p-6 space-y-8">
        {/* Profile Customization Section */}
        <div className="space-y-4">
          <div className="border-l-4 border-purple-500 pl-4">
            <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
              <span className="text-violet-400/60">&lt;</span>
              <span className="px-2">Profile Customization</span>
              <span className="text-violet-400/60">/&gt;</span>
            </span>
          </div>
          
          <div className="space-y-6">
            {/* Multiple Featured Banners Configuration */}
            <MultipleFeaturedBannersConfig userId={user.uid} />

            {/* Review Display Settings */}
            <ReviewDisplaySettings userId={user.uid} />

            {/* YouTube Configuration */}
            <YouTubeChannelConfig userId={user.uid} />
            
            {/* Compact Banner Configuration */}
            <CompactBannerConfig userId={user.uid} />
          </div>
        </div>
      </div>
    );
  }

  function renderPrivacyTab(isDarkBackground: boolean) {
    if (!user?.uid) return null;

    return (
      <div className="p-2 sm:p-3 lg:p-4 xl:p-6">
        <PrivacySettings userId={user.uid} isDarkBackground={isDarkBackground} />
      </div>
    );
  }

  function renderConnectionsTab(isDarkBackground: boolean) {
    if (!user?.uid) return null;

    return (
      <div className="p-2 sm:p-3 lg:p-4 xl:p-6 space-y-8">
        {/* Platform Connections Section */}
        <div className="space-y-4">
          <div className="border-l-4 border-purple-500 pl-4">
            <span className={`font-mono text-3xl font-bold adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
              <span className="text-violet-400/60">&lt;</span>
              <span className="px-2">Platform Connections</span>
              <span className="text-violet-400/60">/&gt;</span>
            </span>
          </div>
          
          <div className="space-y-6">
            {/* Gaming Connections Section */}
            <GamingConnectionsSection userId={user.uid} />
            
            {/* Social Connections Section */}
            <SocialConnectionsSection userId={user.uid} />
          </div>
        </div>
      </div>
    );
  }
}

// Main component with Suspense boundary
function UserDashboardPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-3 text-slate-300">
          <Loader2 className="animate-spin" size={24} />
          <span className="text-lg font-mono">Loading dashboard...</span>
        </div>
      </div>
    }>
      <DashboardWithParams />
    </Suspense>
  );
}

export default UserDashboardPage;