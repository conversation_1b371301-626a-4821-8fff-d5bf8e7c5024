// src/app/tags/page.tsx
// Tags index page showing all tags with categories

import { Metadata } from 'next';
import TagsIndexClient from './TagsIndexClient';
import { createServerTagService } from '@/lib/services/tagService';

export const metadata: Metadata = {
  title: 'Browse Tags - CriticalPixel',
  description: 'Explore all gaming tags and discover reviews by category, genre, platform, and more.',
  keywords: 'gaming tags, game categories, review tags, browse reviews',
  openGraph: {
    title: 'Browse Tags - CriticalPixel',
    description: 'Explore all gaming tags and discover reviews by category, genre, platform, and more.',
    type: 'website',
    siteName: 'CriticalPixel',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Browse Tags - CriticalPixel',
    description: 'Explore all gaming tags and discover reviews by category, genre, platform, and more.',
  },
};

export default async function TagsIndexPage() {
  const tagService = createServerTagService();

  // Get trending tags
  const trendingResult = await tagService.getTrendingTags(20);
  
  // Get popular tags
  const popularResult = await tagService.getPopularTags(50);
  
  // Get featured tags
  const featuredResult = await tagService.getFeaturedTags(15);
  
  // Get tags by category
  const categoriesResult = await tagService.getTagsByCategory();

  return (
    <TagsIndexClient
      trendingTags={trendingResult.tags || []}
      popularTags={popularResult.tags || []}
      featuredTags={featuredResult.tags || []}
      categories={categoriesResult.categories || []}
    />
  );
}