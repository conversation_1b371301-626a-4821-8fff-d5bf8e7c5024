# Guia de Estilo para Componentes do Dashboard

## Visão Geral
Este guia fornece instruções detalhadas para customizar qualquer módulo seguindo o padrão visual estabelecido pelos componentes `FeaturedBannerConfig.tsx` e `ReviewDisplaySettings.tsx`. O objetivo é manter consistência visual em todo o dashboard.

## Estrutura Base do Componente

### 1. Card Principal
```tsx
<Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
```

**Características:**
- Border cinza escuro (`border-gray-800`)
- Gradiente de fundo (`from-gray-900/95 to-gray-800/95`)
- Efeito de blur no fundo (`backdrop-blur`)

### 2. Header <PERSON>vel (com expansão/colapso)
```tsx
<CardHeader 
  className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
  onClick={() => setIsExpanded(!isExpanded)}
>
  <div className="flex items-center justify-between">
    <div className="flex-1">
      <CardTitle className="text-lg text-white font-mono">
        <span className="text-purple-400 mr-1">//</span>
        TÍTULO DO COMPONENTE
      </CardTitle>
      <p className="font-mono text-xs text-gray-400 mt-1">
        Descrição breve do que o componente faz
      </p>
    </div>
    <div className="text-gray-400 hover:text-white ml-4">
      {isExpanded ? (
        <ChevronUp className="h-4 w-4" />
      ) : (
        <ChevronDown className="h-4 w-4" />
      )}
    </div>
  </div>
</CardHeader>
```

**Elementos obrigatórios:**
- **Título**: Sempre precedido por `<span className="text-purple-400 mr-1">//</span>`
- **Fonte**: `font-mono` para elementos de texto técnico
- **Cores**: `text-white` para títulos, `text-gray-400` para descrições
- **Ícones**: ChevronUp/ChevronDown para indicar estado de expansão
- **Hover**: `hover:bg-gray-800/30` no header

### 3. Conteúdo Expansível com Animação
```tsx
<AnimatePresence>
  {isExpanded && (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: "auto", opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ 
        duration: 0.3, 
        ease: "easeInOut",
        opacity: { duration: 0.2 }
      }}
      style={{ overflow: 'hidden' }}
    >
      <CardContent className="space-y-4">
        {/* Conteúdo aqui */}
      </CardContent>
    </motion.div>
  )}
</AnimatePresence>
```

## Elementos de Interface

### 1. Labels e Títulos de Seção
```tsx
<label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
  NOME DO CAMPO
</label>
```

**Características:**
- Sempre em maiúsculas (`uppercase`)
- Fonte mono (`font-mono`)
- Tamanho pequeno (`text-xs`)
- Espaçamento entre letras (`tracking-wide`)
- Cor clara (`text-slate-300`)

### 2. Campos de Input
```tsx
<Input
  className="bg-gray-800/50 border-gray-700/50 focus:border-purple-400 text-white placeholder:text-gray-500 font-mono text-xs"
  placeholder="Texto de exemplo..."
/>
```

**Características:**
- Fundo semi-transparente (`bg-gray-800/50`)
- Border cinza (`border-gray-700/50`)
- Focus em roxo (`focus:border-purple-400`)
- Texto branco, placeholder cinza

### 3. Selects
```tsx
<Select>
  <SelectTrigger className="bg-gray-800/50 border-gray-700/50 text-white font-mono text-xs">
    <SelectValue />
  </SelectTrigger>
  <SelectContent className="bg-gray-900 border-gray-700">
    <SelectItem value="opcao1" className="font-mono text-xs">Opção 1</SelectItem>
  </SelectContent>
</Select>
```

### 4. Botões Principais
```tsx
<Button
  className="w-full bg-purple-600 hover:bg-purple-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
>
  <Icone className="h-4 w-4 mr-2" />
  AÇÃO PRINCIPAL
</Button>
```

### 5. Botões Secundários
```tsx
<Button
  variant="outline"
  className="text-gray-300 border-gray-600 hover:bg-gray-800 font-mono text-xs uppercase tracking-wide"
>
  AÇÃO SECUNDÁRIA
</Button>
```

### 6. Badges de Status
```tsx
<Badge variant="outline" className="font-mono text-xs bg-purple-600/20 border-purple-500/50 text-purple-300">
  STATUS
</Badge>
```

## Padrões de Cores

### Hierarquia de Cores
1. **Primária**: Purple-400/500/600 para elementos principais
2. **Texto Principal**: white para títulos, slate-200 para texto importante
3. **Texto Secundário**: gray-400 para descrições e labels
4. **Fundos**: gray-800/900 com transparências
5. **Borders**: gray-700 com transparências
6. **Estados**:
   - Success: green-400
   - Error: red-400
   - Warning: orange-400
   - Info: blue-400

### Transparências Recomendadas
- Fundos de input: `/50` (50% opacity)
- Fundos de card: `/95` (95% opacity)
- Hovers: `/30` (30% opacity)
- Borders: `/50` (50% opacity)

## Estados de Loading

### Loading de Componente Completo
```tsx
if (isLoading) {
  return (
    <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-mono text-base font-semibold tracking-wide">
          <Settings className="h-5 w-5 text-emerald-500" />
          <span className="text-slate-200 uppercase">LOADING...</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="py-6">
        <div className="flex items-center justify-center h-16">
          <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
        </div>
      </CardContent>
    </Card>
  );
}
```

### Loading em Botões
```tsx
<Button disabled={isSaving}>
  {isSaving ? (
    <Loader2 className="h-4 w-4 animate-spin mr-2" />
  ) : (
    <Check className="h-4 w-4 mr-2" />
  )}
  Salvar Alterações
</Button>
```

## Animações e Transições

### Animações de Entrada
```tsx
<motion.div
  initial={{ opacity: 0, y: 10 }}
  animate={{ opacity: 1, y: 0 }}
  className="pt-4 border-t border-slate-700/50"
>
```

### Animações de Botão
```tsx
<motion.button
  whileHover={{ scale: 1.02 }}
  whileTap={{ scale: 0.98 }}
  className="..."
>
```

### Transições CSS
- Duração padrão: `duration-200` ou `duration-300`
- Easing: `ease-in-out` para expansões, `ease` para hovers
- Sempre incluir `transition-all` ou `transition-colors`

## Tipografia

### Hierarquia de Fontes
1. **Títulos principais**: `text-lg font-mono text-white`
2. **Subtítulos**: `text-base font-mono text-slate-200`
3. **Labels**: `text-xs font-mono text-slate-300 uppercase tracking-wide font-semibold`
4. **Texto corpo**: `text-sm font-mono text-gray-400`
5. **Texto pequeno**: `text-xs font-mono text-gray-500`

### Spacing e Layout
- **Container spacing**: `space-y-4` ou `space-y-6`
- **Padding interno**: `p-3` ou `p-4`
- **Margins**: `mb-2`, `mt-1` para elementos pequenos
- **Gaps**: `gap-2` para elementos próximos, `gap-4` para seções

## Ícones

### Padrões de Uso
- Tamanho padrão: `h-4 w-4` ou `h-5 w-5`
- Sempre com classes de cor apropriadas
- Posicionamento: `mr-2` quando antes do texto

### Ícones por Contexto
- **Configurações**: Settings, Gear
- **Ações**: Check, X, Edit3, Save, Trash2
- **Navegação**: ChevronUp, ChevronDown, ExternalLink
- **Status**: Loader2, Eye, Heart, MessageSquare
- **Categorias**: Gamepad2, ShoppingCart, Link2, Target

## Responsividade

### Grid Layouts
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
```

### Breakpoints Recomendados
- Mobile first approach
- `md:` para tablets e acima
- `lg:` para desktops
- `xl:` para telas grandes

## Estados Condicionais

### Mostrar Alterações Não Salvas
```tsx
{hasChanges && (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    className="pt-4 border-t border-slate-700/50"
  >
    {/* Botões de ação */}
  </motion.div>
)}
```

### Mensagens de Status
```tsx
{saveStatus === 'success' && (
  <div className="flex items-center gap-2 text-green-400">
    <Check className="h-4 w-4" />
    <span className="font-mono text-xs tracking-wide">Settings saved!</span>
  </div>
)}
```

## Checklist de Implementação

### ✅ Estrutura Básica
- [ ] Card com gradiente correto
- [ ] Header clicável com hover
- [ ] Título com "//" em purple-400
- [ ] Descrição em font-mono
- [ ] Ícones de expansão

### ✅ Conteúdo
- [ ] AnimatePresence para expansão
- [ ] CardContent com spacing adequado
- [ ] Labels em maiúsculas
- [ ] Inputs estilizados corretamente

### ✅ Interatividade
- [ ] Estados de loading
- [ ] Animações de entrada
- [ ] Feedback visual para ações
- [ ] Botões com ícones

### ✅ Acessibilidade
- [ ] Contraste adequado
- [ ] Estados disabled visíveis
- [ ] Feedback para ações

### ✅ Responsividade
- [ ] Layout responsivo
- [ ] Breakpoints apropriados
- [ ] Touch-friendly em mobile

## Exemplo de Implementação Completa

```tsx
'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Settings, Check, Loader2 } from 'lucide-react';

interface ComponentProps {
  userId: string;
  className?: string;
}

export default function CustomComponent({ userId, className = '' }: ComponentProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Custom Component
              </CardTitle>
              <p className="font-mono text-xs text-gray-400 mt-1">
                Descrição do que este componente faz
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-4">
                {/* Conteúdo personalizado aqui */}
                <div className="space-y-2">
                  <label className="font-mono text-xs text-slate-300 uppercase tracking-wide font-semibold">
                    Campo Exemplo
                  </label>
                  <input 
                    className="w-full bg-gray-800/50 border border-gray-700/50 focus:border-purple-400 text-white placeholder:text-gray-500 font-mono text-xs rounded-md px-3 py-2"
                    placeholder="Digite algo..."
                  />
                </div>
                
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="pt-4 border-t border-slate-700/50"
                >
                  <Button
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Check className="h-4 w-4 mr-2" />
                    )}
                    Ação Principal
                  </Button>
                </motion.div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
}
```

---

## Notas Importantes

1. **Consistência**: Sempre mantenha os padrões estabelecidos
2. **Performance**: Use motion components apenas quando necessário
3. **Acessibilidade**: Teste com screen readers e navegação por teclado
4. **Manutenibilidade**: Comente código complexo e mantenha estrutura clara
5. **Testing**: Sempre teste em diferentes tamanhos de tela

Este guia garante que todos os componentes do dashboard mantenham a mesma identidade visual e experiência de usuário consistente. 