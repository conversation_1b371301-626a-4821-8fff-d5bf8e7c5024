# CriticalPixel Implementation Guide

## 🚀 Getting Started

This guide provides **technical implementation details** for developers and AI assistants working on CriticalPixel. Follow these patterns to maintain consistency and quality.

### Development Environment Setup
```bash
# Clone and setup
git clone https://github.com/ZaphreBR/CriticalPixel.git
cd CriticalPixel

# Install dependencies
npm install

# Start development server
npm run dev  # Runs on localhost:9003

# Additional commands
npm run dev:turbo    # Faster builds with Turbo
npm run typecheck    # TypeScript validation
npm run lint         # Code quality checks
npm run build        # Production build
```

### Required Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# AI Integration
GOOGLE_GENAI_API_KEY=your_genai_api_key

# IGDB API (Optional)
IGDB_CLIENT_ID=your_igdb_client_id
IGDB_CLIENT_SECRET=your_igdb_client_secret
```

## 🏗️ Architecture Patterns

### File Structure
```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── (pages)/           # Page components
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ui/               # Base UI (Shadcn)
│   ├── layout/           # Layout components
│   ├── auth/             # Authentication
│   ├── review-form/      # Review creation
│   └── style/            # Component-specific CSS
├── lib/                  # Utilities and services
│   ├── supabase/         # Database client
│   ├── types.ts          # TypeScript definitions
│   └── utils.ts          # Helper functions
├── hooks/                # Custom React hooks
└── contexts/             # React contexts
```

### Component Creation Pattern
```tsx
// 1. Import dependencies
import React from 'react';
import { cn } from '@/lib/utils';

// 2. Define TypeScript interfaces
interface ComponentProps {
  title: string;
  variant?: 'default' | 'gaming';
  className?: string;
  children?: React.ReactNode;
}

// 3. Create component with forwardRef if needed
const Component = React.forwardRef<HTMLDivElement, ComponentProps>(
  ({ title, variant = 'default', className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          'rounded-lg border bg-card text-card-foreground shadow-sm',
          // Variant styles
          variant === 'gaming' && 'bg-slate-900/60 border-slate-700/50',
          // Custom className
          className
        )}
        {...props}
      >
        <h3 className="text-lg font-semibold">{title}</h3>
        {children}
      </div>
    );
  }
);

Component.displayName = 'Component';
export { Component };
```

## 🎨 Styling Implementation

### CSS Architecture
```
1. Tailwind CSS (Primary)    → Utility-first styling
2. CSS Custom Properties     → Dynamic theming
3. Component CSS Files       → Complex component styles
4. Shadcn UI Components      → Base component library
```

### Styling Best Practices
```tsx
// ✅ Good: Use Tailwind utilities with cn() helper
<div className={cn(
  'bg-slate-900/60 border-slate-700/50 rounded-lg p-6',
  'hover:border-purple-500/50 transition-colors',
  isActive && 'border-purple-500',
  className
)} />

// ✅ Good: Use CSS variables for dynamic values
<div style={{ '--theme-primary': themeColor }} />

// ❌ Avoid: Inline styles for static values
<div style={{ backgroundColor: '#1e293b', padding: '24px' }} />
```

### Theme Implementation
```tsx
// Apply theme to component
import { ThemeManager } from '@/lib/ThemeManager';

const Component = ({ themeId = 'default' }) => {
  const theme = ThemeManager.getTheme(themeId);
  
  return (
    <div 
      className={`theme-${themeId}`}
      style={{
        '--theme-primary': theme.colors.primary,
        '--theme-secondary': theme.colors.secondary,
        '--theme-accent': theme.colors.accent,
      }}
    >
      {/* Component content */}
    </div>
  );
};
```

## 🗄️ Database Integration

### Supabase Client Usage
```tsx
// Client-side operations
import { createClient } from '@/lib/supabase/client';

const supabase = createClient();

// Fetch data
const { data, error } = await supabase
  .from('reviews')
  .select('*')
  .eq('published', true)
  .order('created_at', { ascending: false });

// Insert data
const { data, error } = await supabase
  .from('reviews')
  .insert([{ title, content, user_id }]);
```

### Server-side Operations
```tsx
// Server components and API routes
import { createClient } from '@/lib/supabase/server';

export async function getReviews() {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('reviews')
    .select(`
      *,
      profiles:user_id (
        username,
        avatar_url
      )
    `)
    .eq('published', true);
    
  if (error) throw error;
  return data;
}
```

### Row Level Security (RLS)
```sql
-- Example RLS policy
CREATE POLICY "Users can view published reviews"
ON reviews FOR SELECT
USING (published = true);

CREATE POLICY "Users can edit own reviews"
ON reviews FOR UPDATE
USING (auth.uid() = user_id);
```

## 🔧 Component Patterns

### Form Components
```tsx
// Use React Hook Form with Zod validation
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const schema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(10, 'Content must be at least 10 characters'),
});

const ReviewForm = () => {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      title: '',
      content: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    // Handle form submission
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="form-group">
        <label className="form-label">Title</label>
        <input
          {...form.register('title')}
          className="form-input"
          placeholder="Enter review title..."
        />
        {form.formState.errors.title && (
          <p className="text-error-message">
            {form.formState.errors.title.message}
          </p>
        )}
      </div>
      
      <Button type="submit" variant="gradient">
        Submit Review
      </Button>
    </form>
  );
};
```

### Data Fetching with React Query
```tsx
import { useQuery } from '@tanstack/react-query';

const useReviews = () => {
  return useQuery({
    queryKey: ['reviews'],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('published', true);
      
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Usage in component
const ReviewsList = () => {
  const { data: reviews, isLoading, error } = useReviews();

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {reviews?.map(review => (
        <ReviewCard key={review.id} review={review} />
      ))}
    </div>
  );
};
```

## 🎮 Gaming-Specific Implementations

### Score Display Component
```tsx
interface ScoreDisplayProps {
  score: number;
  maxScore?: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  score,
  maxScore = 10,
  size = 'md',
  showLabel = true,
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 9) return 'text-green-500';
    if (score >= 8) return 'text-blue-500';
    if (score >= 7) return 'text-yellow-500';
    if (score >= 6) return 'text-orange-500';
    return 'text-red-500';
  };

  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl',
  };

  return (
    <div className="score-display text-center">
      <div className={cn(
        'font-bold',
        sizeClasses[size],
        getScoreColor(score)
      )}>
        {score.toFixed(1)}
      </div>
      {showLabel && (
        <div className="text-xs text-muted-foreground uppercase tracking-wide">
          Score
        </div>
      )}
    </div>
  );
};
```

### Platform Badge Component
```tsx
interface PlatformBadgeProps {
  platform: 'PC' | 'PS5' | 'Xbox' | 'Switch' | 'Steam';
  size?: 'sm' | 'md';
}

const PlatformBadge: React.FC<PlatformBadgeProps> = ({ platform, size = 'md' }) => {
  const platformColors = {
    PC: 'bg-gray-600',
    PS5: 'bg-blue-600',
    Xbox: 'bg-green-600',
    Switch: 'bg-red-600',
    Steam: 'bg-slate-800',
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
  };

  return (
    <span className={cn(
      'inline-flex items-center rounded-full font-medium text-white',
      platformColors[platform],
      sizeClasses[size]
    )}>
      {platform}
    </span>
  );
};
```

## 🔒 Authentication Implementation

### Auth Context Usage
```tsx
import { useAuthContext } from '@/hooks/use-auth-context';

const ProtectedComponent = () => {
  const { user, loading, signOut } = useAuthContext();

  if (loading) return <LoadingSpinner />;
  
  if (!user) {
    return <AuthModal />;
  }

  return (
    <div>
      <p>Welcome, {user.email}</p>
      <Button onClick={signOut}>Sign Out</Button>
    </div>
  );
};
```

### Protected Routes
```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

const DashboardPage = () => {
  return (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  );
};
```

## 📱 Responsive Implementation

### Breakpoint Usage
```tsx
// Use Tailwind responsive prefixes
<div className={cn(
  'grid grid-cols-1',           // Mobile: 1 column
  'md:grid-cols-2',             // Tablet: 2 columns
  'lg:grid-cols-3',             // Desktop: 3 columns
  'xl:grid-cols-4',             // Large: 4 columns
  'gap-4 md:gap-6 lg:gap-8'    // Responsive gaps
)} />

// Custom breakpoint (1360px)
<div className="w-full bp1360:w-4/5 mx-auto" />
```

### Mobile-First CSS
```css
/* Base styles (mobile) */
.component {
  padding: 1rem;
  font-size: 0.875rem;
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    padding: 1.5rem;
    font-size: 1rem;
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .component {
    padding: 2rem;
    font-size: 1.125rem;
  }
}
```

## 🚀 Performance Optimization

### Image Optimization
```tsx
import Image from 'next/image';

// Optimized image component
<Image
  src={imageUrl}
  alt={altText}
  width={600}
  height={400}
  className="rounded-lg object-cover"
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
  priority={isAboveFold}
/>
```

### Code Splitting
```tsx
// Lazy load components
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <LoadingSpinner />,
  ssr: false, // Client-side only if needed
});
```

### Bundle Analysis
```bash
# Analyze bundle size
npm run analyze

# Check for unused dependencies
npx depcheck

# Audit performance
npm audit
```

## 🧪 Testing Patterns

### Component Testing
```tsx
import { render, screen } from '@testing-library/react';
import { ReviewCard } from './ReviewCard';

describe('ReviewCard', () => {
  const mockReview = {
    id: '1',
    title: 'Test Review',
    score: 8.5,
    platform: 'PC',
  };

  it('renders review information correctly', () => {
    render(<ReviewCard review={mockReview} />);
    
    expect(screen.getByText('Test Review')).toBeInTheDocument();
    expect(screen.getByText('8.5')).toBeInTheDocument();
    expect(screen.getByText('PC')).toBeInTheDocument();
  });
});
```

---

*This implementation guide ensures consistent, maintainable, and performant code across all CriticalPixel features while following modern React and Next.js best practices.*
