/**
 * Admin Global Settings Service
 * Provides comprehensive site settings management functionality for admin dashboard
 * Created: 20/01/2025 - Admin System Completion
 */

import { createClient } from '@/lib/supabase/client';
import { logAdminAction, AdminAction } from '@/lib/audit/adminActions';

// Settings interfaces
export interface SiteSettings {
  general: {
    site_name: string;
    site_description: string;
    site_url: string;
    admin_email: string;
    timezone: string;
    language: string;
    maintenance_mode: boolean;
    maintenance_message: string;
  };
  seo: {
    meta_title: string;
    meta_description: string;
    meta_keywords: string;
    og_image: string;
    twitter_card: string;
    google_analytics_id: string;
    google_search_console: string;
  };
  content: {
    allow_user_registration: boolean;
    require_email_verification: boolean;
    allow_anonymous_comments: boolean;
    moderate_comments: boolean;
    max_review_length: number;
    max_comment_length: number;
    featured_reviews_count: number;
  };
  security: {
    enable_rate_limiting: boolean;
    max_login_attempts: number;
    session_timeout: number;
    require_strong_passwords: boolean;
    enable_two_factor: boolean;
    allowed_file_types: string[];
    max_file_size: number;
  };
  notifications: {
    email_notifications: boolean;
    admin_notifications: boolean;
    user_notifications: boolean;
    newsletter_enabled: boolean;
    smtp_host: string;
    smtp_port: number;
    smtp_username: string;
    smtp_password: string;
  };
  integrations: {
    igdb_api_key: string;
    discord_webhook: string;
    slack_webhook: string;
    backup_enabled: boolean;
    backup_frequency: string;
    backup_retention: number;
  };
}

export interface SettingsCategory {
  id: keyof SiteSettings;
  name: string;
  description: string;
  icon: string;
}

// Verify admin permissions using RLS
export async function verifyAdminPermissions(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // Use the is_admin() RPC function for verification
    const { data, error } = await supabase.rpc('is_admin', { user_id: userId });

    if (error) {
      console.error('Admin verification error:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Admin verification failed:', error);
    return false;
  }
}

// Get all site settings
export async function getSiteSettings(
  adminUserId: string
): Promise<SiteSettings> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      throw new Error('Unauthorized: Admin access required');
    }

    // For now, return mock settings since we don't have settings table in database
    // In a real implementation, this would query the settings table
    const mockSettings: SiteSettings = {
      general: {
        site_name: 'CriticalPixel',
        site_description: 'The ultimate gaming review platform',
        site_url: 'https://criticalpixel.com',
        admin_email: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenance_mode: false,
        maintenance_message: 'Site is under maintenance. Please check back later.'
      },
      seo: {
        meta_title: 'CriticalPixel - Gaming Reviews & Community',
        meta_description: 'Discover the best games with in-depth reviews, ratings, and community discussions.',
        meta_keywords: 'gaming, reviews, games, community, ratings',
        og_image: 'https://criticalpixel.com/og-image.jpg',
        twitter_card: 'summary_large_image',
        google_analytics_id: 'GA-XXXXXXXXX',
        google_search_console: 'GSC-XXXXXXXXX'
      },
      content: {
        allow_user_registration: true,
        require_email_verification: true,
        allow_anonymous_comments: false,
        moderate_comments: true,
        max_review_length: 10000,
        max_comment_length: 1000,
        featured_reviews_count: 6
      },
      security: {
        enable_rate_limiting: true,
        max_login_attempts: 5,
        session_timeout: 3600,
        require_strong_passwords: true,
        enable_two_factor: false,
        allowed_file_types: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        max_file_size: 5242880 // 5MB
      },
      notifications: {
        email_notifications: true,
        admin_notifications: true,
        user_notifications: true,
        newsletter_enabled: false,
        smtp_host: 'smtp.gmail.com',
        smtp_port: 587,
        smtp_username: '',
        smtp_password: ''
      },
      integrations: {
        igdb_api_key: process.env.NEXT_PUBLIC_IGDB_API_KEY || '',
        discord_webhook: '',
        slack_webhook: '',
        backup_enabled: true,
        backup_frequency: 'daily',
        backup_retention: 30
      }
    };

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.VIEW_SYSTEM_CONFIG, {
      resource: 'site_settings',
      action: 'view_all'
    });

    return mockSettings;

  } catch (error) {
    console.error('Error in getSiteSettings:', error);
    throw error;
  }
}

// Update site settings
export async function updateSiteSettings(
  adminUserId: string,
  category: keyof SiteSettings,
  settings: Partial<SiteSettings[keyof SiteSettings]>
): Promise<{ success: boolean; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    // Validate settings based on category
    const validationResult = validateSettings(category, settings);
    if (!validationResult.valid) {
      return { success: false, error: validationResult.error };
    }

    // In a real implementation, this would update the database
    // For now, we'll just simulate success

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.UPDATE_SYSTEM_CONFIG, {
      resource: 'site_settings',
      category,
      action: 'update',
      changes: Object.keys(settings)
    });

    return { success: true };

  } catch (error) {
    console.error('Error in updateSiteSettings:', error);
    return { success: false, error: 'Failed to update settings' };
  }
}

// Validate settings based on category
function validateSettings(
  category: keyof SiteSettings,
  settings: any
): { valid: boolean; error?: string } {
  switch (category) {
    case 'general':
      if (settings.site_name && settings.site_name.length < 3) {
        return { valid: false, error: 'Site name must be at least 3 characters long' };
      }
      if (settings.admin_email && !isValidEmail(settings.admin_email)) {
        return { valid: false, error: 'Invalid admin email format' };
      }
      break;

    case 'seo':
      if (settings.meta_title && settings.meta_title.length > 60) {
        return { valid: false, error: 'Meta title should be 60 characters or less' };
      }
      if (settings.meta_description && settings.meta_description.length > 160) {
        return { valid: false, error: 'Meta description should be 160 characters or less' };
      }
      break;

    case 'content':
      if (settings.max_review_length && (settings.max_review_length < 100 || settings.max_review_length > 50000)) {
        return { valid: false, error: 'Review length must be between 100 and 50,000 characters' };
      }
      if (settings.max_comment_length && (settings.max_comment_length < 10 || settings.max_comment_length > 5000)) {
        return { valid: false, error: 'Comment length must be between 10 and 5,000 characters' };
      }
      break;

    case 'security':
      if (settings.max_login_attempts && (settings.max_login_attempts < 3 || settings.max_login_attempts > 10)) {
        return { valid: false, error: 'Max login attempts must be between 3 and 10' };
      }
      if (settings.session_timeout && (settings.session_timeout < 300 || settings.session_timeout > 86400)) {
        return { valid: false, error: 'Session timeout must be between 5 minutes and 24 hours' };
      }
      break;

    case 'notifications':
      if (settings.smtp_port && (settings.smtp_port < 1 || settings.smtp_port > 65535)) {
        return { valid: false, error: 'SMTP port must be between 1 and 65535' };
      }
      break;

    case 'integrations':
      if (settings.backup_retention && (settings.backup_retention < 1 || settings.backup_retention > 365)) {
        return { valid: false, error: 'Backup retention must be between 1 and 365 days' };
      }
      break;
  }

  return { valid: true };
}

// Helper function to validate email
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Get settings categories
export function getSettingsCategories(): SettingsCategory[] {
  return [
    {
      id: 'general',
      name: 'General',
      description: 'Basic site configuration and information',
      icon: 'Settings'
    },
    {
      id: 'seo',
      name: 'SEO & Analytics',
      description: 'Search engine optimization and tracking',
      icon: 'TrendingUp'
    },
    {
      id: 'content',
      name: 'Content Management',
      description: 'User-generated content settings and moderation',
      icon: 'FileText'
    },
    {
      id: 'security',
      name: 'Security',
      description: 'Authentication and security policies',
      icon: 'Shield'
    },
    {
      id: 'notifications',
      name: 'Notifications',
      description: 'Email and notification settings',
      icon: 'Bell'
    },
    {
      id: 'integrations',
      name: 'Integrations',
      description: 'Third-party services and API configurations',
      icon: 'Plug'
    }
  ];
}

// Reset settings to defaults
export async function resetSettingsToDefaults(
  adminUserId: string,
  category: keyof SiteSettings
): Promise<{ success: boolean; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    // In a real implementation, this would reset the specific category to defaults
    // For now, we'll just simulate success

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.UPDATE_SYSTEM_CONFIG, {
      resource: 'site_settings',
      category,
      action: 'reset_to_defaults'
    });

    return { success: true };

  } catch (error) {
    console.error('Error in resetSettingsToDefaults:', error);
    return { success: false, error: 'Failed to reset settings' };
  }
}

// Export settings for backup
export async function exportSettings(
  adminUserId: string
): Promise<{ success: boolean; data?: SiteSettings; error?: string }> {
  try {
    // Verify admin permissions
    const isAdmin = await verifyAdminPermissions(adminUserId);
    if (!isAdmin) {
      return { success: false, error: 'Unauthorized: Admin access required' };
    }

    const settings = await getSiteSettings(adminUserId);

    // Log admin action
    await logAdminAction(adminUserId, AdminAction.EXPORT_ANALYTICS, {
      resource: 'site_settings',
      action: 'export'
    });

    return { success: true, data: settings };

  } catch (error) {
    console.error('Error in exportSettings:', error);
    return { success: false, error: 'Failed to export settings' };
  }
}
