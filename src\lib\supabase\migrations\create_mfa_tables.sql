-- =====================================================
-- CriticalPixel MFA (Multi-Factor Authentication) Tables
-- Date: June 16, 2025
-- Purpose: Complete MFA system for admin security
-- =====================================================

-- Drop existing tables if they exist (for development)
DROP TABLE IF EXISTS mfa_verification_sessions CASCADE;
DROP TABLE IF EXISTS user_mfa_settings CASCADE;

-- =====================================================
-- 1. USER MFA SETTINGS TABLE
-- =====================================================
CREATE TABLE user_mfa_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
  
  -- MFA Configuration
  secret_encrypted TEXT NOT NULL,
  backup_codes_encrypted TEXT NOT NULL,
  recovery_phrase_encrypted TEXT,
  
  -- Status and Metadata
  is_enabled BOOLEAN DEFAULT FALSE NOT NULL,
  admin_level VARCHAR(20) DEFAULT 'ADMIN',
  
  -- Timestamps
  setup_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  
  -- Emergency Disable
  disabled_at TIMESTAMP WITH TIME ZONE,
  disabled_reason TEXT,
  disabled_by UUID REFERENCES auth.users(id),
  
  -- Audit
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT chk_admin_level CHECK (admin_level IN ('SUPER_ADMIN', 'ADMIN', 'MODERATOR', 'EDITOR', 'VIEWER'))
);

-- =====================================================
-- 2. MFA VERIFICATION SESSIONS TABLE
-- =====================================================
CREATE TABLE mfa_verification_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Session Details
  session_token TEXT NOT NULL UNIQUE,
  operation VARCHAR(50),
  verified BOOLEAN DEFAULT FALSE NOT NULL,
  
  -- Metadata
  ip_address INET,
  user_agent TEXT,
  
  -- Timing
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- 3. INDEXES FOR PERFORMANCE
-- =====================================================

-- User MFA Settings Indexes
CREATE INDEX idx_user_mfa_settings_user_id ON user_mfa_settings(user_id);
CREATE INDEX idx_user_mfa_settings_enabled ON user_mfa_settings(is_enabled) WHERE is_enabled = TRUE;
CREATE INDEX idx_user_mfa_settings_admin_level ON user_mfa_settings(admin_level);
CREATE INDEX idx_user_mfa_settings_last_used ON user_mfa_settings(last_used_at);

-- MFA Verification Sessions Indexes
CREATE INDEX idx_mfa_sessions_token ON mfa_verification_sessions(session_token);
CREATE INDEX idx_mfa_sessions_user_id ON mfa_verification_sessions(user_id);
CREATE INDEX idx_mfa_sessions_expires ON mfa_verification_sessions(expires_at);
CREATE INDEX idx_mfa_sessions_verified ON mfa_verification_sessions(verified, expires_at);

-- =====================================================
-- 4. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE user_mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_verification_sessions ENABLE ROW LEVEL SECURITY;

-- User MFA Settings Policies
CREATE POLICY "Users can manage their own MFA settings" ON user_mfa_settings
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Super admins can view all MFA settings" ON user_mfa_settings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.admin_level = 'SUPER_ADMIN'
      AND p.is_admin = TRUE
    )
  );

-- MFA Verification Sessions Policies
CREATE POLICY "Users can manage their own MFA sessions" ON mfa_verification_sessions
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Cleanup expired sessions" ON mfa_verification_sessions
  FOR DELETE USING (expires_at < NOW());

-- =====================================================
-- 5. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_mfa_settings_updated_at 
  BEFORE UPDATE ON user_mfa_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. CLEANUP FUNCTIONS
-- =====================================================

-- Function to cleanup expired MFA sessions
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM mfa_verification_sessions 
  WHERE expires_at < NOW() - INTERVAL '1 hour';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log cleanup
  INSERT INTO security_audit_log (
    event_type,
    admin_id,
    event_data,
    severity,
    created_at
  ) VALUES (
    'MFA_SESSION_CLEANUP',
    NULL,
    jsonb_build_object('deleted_sessions', deleted_count),
    'LOW',
    NOW()
  );
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. MFA STATISTICS FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION get_mfa_statistics()
RETURNS TABLE (
  total_users_with_mfa INTEGER,
  enabled_mfa_users INTEGER,
  super_admin_mfa_enabled INTEGER,
  admin_mfa_enabled INTEGER,
  recent_mfa_usage INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE AND admin_level = 'SUPER_ADMIN'),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE is_enabled = TRUE AND admin_level = 'ADMIN'),
    (SELECT COUNT(*)::INTEGER FROM user_mfa_settings WHERE last_used_at > NOW() - INTERVAL '7 days');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. INITIAL DATA & GRANTS
-- =====================================================

-- Grant permissions for service role
GRANT ALL ON user_mfa_settings TO service_role;
GRANT ALL ON mfa_verification_sessions TO service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_expired_mfa_sessions() TO service_role;
GRANT EXECUTE ON FUNCTION get_mfa_statistics() TO service_role;

-- =====================================================
-- 9. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE user_mfa_settings IS 'Stores encrypted MFA secrets and settings for admin users';
COMMENT ON TABLE mfa_verification_sessions IS 'Temporary sessions for MFA verification challenges';

COMMENT ON COLUMN user_mfa_settings.secret_encrypted IS 'AES-256 encrypted TOTP secret';
COMMENT ON COLUMN user_mfa_settings.backup_codes_encrypted IS 'AES-256 encrypted backup codes array';
COMMENT ON COLUMN user_mfa_settings.recovery_phrase_encrypted IS 'AES-256 encrypted human-readable recovery phrase';

COMMENT ON FUNCTION cleanup_expired_mfa_sessions() IS 'Removes expired MFA verification sessions';
COMMENT ON FUNCTION get_mfa_statistics() IS 'Returns MFA adoption and usage statistics';

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Log successful migration
INSERT INTO security_audit_log (
  event_type,
  admin_id,
  event_data,
  severity,
  created_at
) VALUES (
  'MFA_TABLES_CREATED',
  NULL,
  jsonb_build_object(
    'tables_created', ARRAY['user_mfa_settings', 'mfa_verification_sessions'],
    'migration_date', NOW(),
    'version', '1.0.0'
  ),
  'LOW',
  NOW()
);

-- Success message
SELECT 'MFA tables created successfully!' as result; 