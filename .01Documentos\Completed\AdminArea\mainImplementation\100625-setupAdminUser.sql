-- Setup Admin User Script
-- Date: 15/01/2025
-- Task: adminSystemImpl001 - Setup <EMAIL> as admin
-- Purpose: Configure admin user for testing admin system implementation

-- Step 1: Verify if user exists and check current admin status
SELECT 
    id,
    email,
    username,
    display_name,
    is_admin,
    created_at
FROM profiles p
JOIN auth.users u ON p.id = u.id
WHERE u.email = '<EMAIL>';

-- Step 2: Update user to admin if exists
UPDATE profiles 
SET is_admin = true,
    updated_at = NOW()
WHERE id IN (
    SELECT p.id 
    FROM profiles p
    JOIN auth.users u ON p.id = u.id
    WHERE u.email = '<EMAIL>'
);

-- Step 3: Verify admin status was set correctly
SELECT 
    'Admin setup verification' as status,
    p.id,
    u.email,
    p.username,
    p.display_name,
    p.is_admin,
    CASE 
        WHEN p.is_admin = true THEN '✅ ADMIN CONFIGURED'
        ELSE '❌ ADMIN NOT SET'
    END as admin_status
FROM profiles p
JOIN auth.users u ON p.id = u.id
WHERE u.email = '<EMAIL>';

-- Step 4: Test admin security function
SELECT 
    'Security function test' as test_type,
    is_admin(p.id) as is_admin_function_result,
    CASE 
        WHEN is_admin(p.id) = true THEN '✅ SECURITY FUNCTION WORKING'
        ELSE '❌ SECURITY FUNCTION FAILED'
    END as function_status
FROM profiles p
JOIN auth.users u ON p.id = u.id
WHERE u.email = '<EMAIL>';

-- Step 5: Verify RLS policies allow admin access
-- This should return data if admin policies are working
SELECT 
    'RLS policy test' as test_type,
    COUNT(*) as total_profiles_visible,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ ADMIN CAN ACCESS ALL PROFILES'
        ELSE '❌ RLS BLOCKING ADMIN ACCESS'
    END as rls_status
FROM profiles
WHERE EXISTS (
    SELECT 1 FROM profiles p2
    JOIN auth.users u ON p2.id = u.id
    WHERE u.email = '<EMAIL>' AND p2.is_admin = true
);

-- Expected Results:
-- 1. User should exist in profiles table
-- 2. is_admin should be set to true
-- 3. is_admin() function should return true
-- 4. Admin should be able to see all profiles (RLS test)

-- Instructions for execution:
-- 1. Copy this SQL to Supabase SQL Editor
-- 2. Execute each section step by step
-- 3. Verify all checks return ✅ status
-- 4. If user doesn't exist, they need to sign up first
-- 5. If any step fails, check RLS policies and security functions
