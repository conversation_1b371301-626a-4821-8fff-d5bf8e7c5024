import { NextRequest, NextResponse } from 'next/server';
import { getSteamGridDBHeroes, STEAMGRIDDB_STYLES } from '@/lib/steamgriddb-api';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  const gameId = searchParams.get('gameId');
  const styles = searchParams.get('styles')?.split(',').filter(Boolean) || [];
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

  if (!gameId) {
    return NextResponse.json(
      { error: 'gameId parameter is required' },
      { status: 400 }
    );
  }

  if (isNaN(parseInt(gameId))) {
    return NextResponse.json(
      { error: 'gameId must be a valid number' },
      { status: 400 }
    );
  }

  // Validate styles
  const validStyles = styles.filter(style => STEAMGRIDDB_STYLES.includes(style as any));
  if (styles.length > 0 && validStyles.length === 0) {
    return NextResponse.json(
      { 
        error: 'Invalid styles provided',
        validStyles: STEAMGRIDDB_STYLES
      },
      { status: 400 }
    );
  }

  try {
    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      limit
    };

    const heroes = await getSteamGridDBHeroes(parseInt(gameId), options);

    return NextResponse.json({
      success: true,
      data: heroes,
      metadata: {
        count: heroes.length,
        gameId: parseInt(gameId),
        styles: validStyles
      }
    });
  } catch (error) {
    console.error('SteamGridDB heroes error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch heroes from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gameId, styles, limit } = body;

    if (!gameId) {
      return NextResponse.json(
        { error: 'gameId is required' },
        { status: 400 }
      );
    }

    // Validate styles if provided
    const validStyles = styles ? styles.filter((style: string) => STEAMGRIDDB_STYLES.includes(style as any)) : [];

    const options = {
      styles: validStyles.length > 0 ? validStyles : undefined,
      limit
    };

    const heroes = await getSteamGridDBHeroes(gameId, options);

    return NextResponse.json({
      success: true,
      data: heroes,
      metadata: {
        count: heroes.length,
        gameId,
        styles: validStyles
      }
    });
  } catch (error) {
    console.error('SteamGridDB heroes error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch heroes from SteamGridDB',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}