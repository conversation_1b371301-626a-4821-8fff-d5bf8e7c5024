'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  Link as LinkIcon,
  Image as ImageIcon,
  Loader2,
  Monitor,
  ExternalLink,
  TrendingUp,
  ChevronDown,
  ChevronUp,
  Save,
  Trash2,
  Zap
} from 'lucide-react';
import {
  getUserContentBanner,
  saveContentBanner,
  deleteContentBanner,
  type ContentBannerData
} from '@/lib/services/contentBannerService';
import {
  getUserSponsorBanner,
  saveSponsorBanner,
  deleteSponsorBanner
} from '@/lib/services/sponsorBannerService';
import UnifiedBannerAnalytics from './UnifiedBannerAnalytics';

interface CompactBannerConfigProps {
  userId: string;
  className?: string;
}

interface ContentData {
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  id?: string;
  created_at?: string;
  updated_at?: string;
}

interface SponsorData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

const CompactBannerConfig: React.FC<CompactBannerConfigProps> = ({
  userId,
  className = ''
}) => {
  const [activeAnalytics, setActiveAnalytics] = useState<'content' | 'sponsor' | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Content Banner State
  const [isContentLoading, setIsContentLoading] = useState(false);
  const [isContentSaving, setIsContentSaving] = useState(false);
  const [contentData, setContentData] = useState<ContentData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentContentBanner, setCurrentContentBanner] = useState<ContentBannerData | null>(null);

  // Sponsor Banner State
  const [isSponsorLoading, setIsSponsorLoading] = useState(false);
  const [isSponsorSaving, setIsSponsorSaving] = useState(false);
  const [sponsorData, setSponsorData] = useState<SponsorData>({
    user_id: userId,
    img_url: '',
    url: '',
    is_active: false
  });
  const [currentSponsor, setCurrentSponsor] = useState<SponsorData | null>(null);
  
  const { toast } = useToast();

  // Load data on mount
  useEffect(() => {
    loadContentData();
    loadSponsorData();
  }, [userId]);

  const loadContentData = async () => {
    try {
      setIsContentLoading(true);
      if (!userId) return;
      
      const data = await getUserContentBanner(userId);
      if (data) {
        setContentData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentContentBanner(data);
      }
    } catch (error) {
      console.error('Error loading content banner data:', error);
    } finally {
      setIsContentLoading(false);
    }
  };

  const loadSponsorData = async () => {
    try {
      setIsSponsorLoading(true);
      if (!userId) return;
      
      const data = await getUserSponsorBanner(userId);
      if (data) {
        setSponsorData({
          id: data.id,
          user_id: data.user_id,
          img_url: data.img_url,
          url: data.url,
          is_active: data.is_active,
          created_at: data.created_at,
          updated_at: data.updated_at
        });
        setCurrentSponsor(data);
      }
    } catch (error) {
      console.error('Error loading sponsor data:', error);
    } finally {
      setIsSponsorLoading(false);
    }
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const handleSaveContent = async () => {
    if (!contentData.img_url.trim() || !contentData.url.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    if (!isValidUrl(contentData.img_url) || !isValidUrl(contentData.url)) {
      toast({
        title: "Error",
        description: "Please enter valid URLs",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsContentSaving(true);

      // If there's an existing banner with different URLs, delete it first to reset analytics
      if (currentContentBanner &&
          (currentContentBanner.img_url !== contentData.img_url ||
           currentContentBanner.url !== contentData.url)) {
        await deleteContentBanner(userId);
      }

      const savedData = await saveContentBanner({
        userId,
        imgUrl: contentData.img_url,
        url: contentData.url,
        isActive: true
      });

      setCurrentContentBanner(savedData);
      setContentData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });

      toast({
        title: "Success",
        description: "Content banner saved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save content banner",
        variant: "destructive",
      });
    } finally {
      setIsContentSaving(false);
    }
  };

  const handleSaveSponsor = async () => {
    if (!sponsorData.img_url.trim() || !sponsorData.url.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    if (!isValidUrl(sponsorData.img_url) || !isValidUrl(sponsorData.url)) {
      toast({
        title: "Error",
        description: "Please enter valid URLs",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSponsorSaving(true);

      // If there's an existing banner with different URLs, delete it first to reset analytics
      if (currentSponsor &&
          (currentSponsor.img_url !== sponsorData.img_url ||
           currentSponsor.url !== sponsorData.url)) {
        await deleteSponsorBanner(userId);
      }

      const savedData = await saveSponsorBanner({
        userId,
        imgUrl: sponsorData.img_url,
        url: sponsorData.url,
        isActive: true
      });

      setCurrentSponsor(savedData);
      setSponsorData({
        id: savedData.id,
        user_id: savedData.user_id,
        img_url: savedData.img_url,
        url: savedData.url,
        is_active: savedData.is_active,
        created_at: savedData.created_at,
        updated_at: savedData.updated_at
      });

      toast({
        title: "Success",
        description: "Sponsor banner saved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save sponsor banner",
        variant: "destructive",
      });
    } finally {
      setIsSponsorSaving(false);
    }
  };

  const handleDeleteContent = async () => {
    try {
      setIsContentSaving(true);
      if (currentContentBanner) {
        await deleteContentBanner(userId);
        setCurrentContentBanner(null);
        setContentData({
          user_id: userId,
          img_url: '',
          url: '',
          is_active: false
        });
        toast({
          title: "Success",
          description: "Content banner removed completely",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove content banner",
        variant: "destructive",
      });
    } finally {
      setIsContentSaving(false);
    }
  };

  const handleDeleteSponsor = async () => {
    try {
      setIsSponsorSaving(true);
      if (currentSponsor) {
        await deleteSponsorBanner(userId);
        setCurrentSponsor(null);
        setSponsorData({
          user_id: userId,
          img_url: '',
          url: '',
          is_active: false
        });
        toast({
          title: "Success",
          description: "Sponsor banner removed completely",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove sponsor banner",
        variant: "destructive",
      });
    } finally {
      setIsSponsorSaving(false);
    }
  };

  const toggleAnalytics = (type: 'content' | 'sponsor') => {
    setActiveAnalytics(activeAnalytics === type ? null : type);
  };

  if (isContentLoading && isSponsorLoading) {
    return (
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
            <span className="ml-2 text-slate-400 font-['Lato']">Loading banner settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white font-mono">
                <span className="text-purple-400 mr-1">//</span>
                Banner Management
              </CardTitle>
              <p className="font-mono text-xs text-gray-400 mt-1">
                Manage your content and sponsor banners for profile display
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-6">
                {/* Banner Configuration Sections */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Content Banner Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4 text-blue-400" />
                        <h3 className="font-mono text-sm font-semibold text-white uppercase tracking-wide">Content Banner</h3>
                      </div>
                      {currentContentBanner && currentContentBanner.is_active && (
                        <Button
                          onClick={() => toggleAnalytics('content')}
                          variant={activeAnalytics === 'content' ? 'default' : 'outline'}
                          size="sm"
                          className={`font-mono text-xs uppercase tracking-wide transition-all duration-200 ${
                            activeAnalytics === 'content'
                              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg'
                              : 'text-blue-400 hover:text-blue-300 border-blue-400/50 hover:bg-blue-400/10'
                          }`}
                        >
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {activeAnalytics === 'content' ? 'Hide Analytics' : 'Show Analytics'}
                        </Button>
                      )}
                    </div>

                    {/* Current Content Banner Display */}
                    {currentContentBanner && currentContentBanner.is_active && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-xs text-green-400 uppercase tracking-wide font-['Lato']">Active</span>
                          </div>
                          <Button
                            onClick={handleDeleteContent}
                            disabled={isContentSaving}
                            variant="outline"
                            size="sm"
                            className="text-red-400 border-red-400/50 hover:bg-red-400/10 font-mono text-xs uppercase tracking-wide"
                          >
                            {isContentSaving ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <div className="w-full h-20 bg-gray-800/60 border border-gray-700/50 rounded overflow-hidden mb-3">
                          <img
                            src={currentContentBanner.img_url}
                            alt="Content Banner"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-400 font-mono">
                          <ExternalLink className="h-3 w-3" />
                          <span className="truncate font-['Lato']">{currentContentBanner.url.substring(0, 30)}...</span>
                        </div>
                      </motion.div>
                    )}

                    {/* Content Banner Configuration Form */}
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <label className="font-mono text-xs text-gray-300 uppercase tracking-wide font-semibold">
                          Banner Image URL
                        </label>
                        <Input
                          placeholder="https://example.com/banner-image.jpg"
                          value={contentData.img_url}
                          onChange={(e) => setContentData({ ...contentData, img_url: e.target.value })}
                          className="bg-gray-800/50 border-gray-700/50 focus:border-blue-400 text-white placeholder:text-gray-500 font-['Lato'] text-xs"
                          disabled={isContentSaving}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="font-mono text-xs text-gray-300 uppercase tracking-wide font-semibold">
                          Target Link URL
                        </label>
                        <Input
                          placeholder="https://example.com/target-page"
                          value={contentData.url}
                          onChange={(e) => setContentData({ ...contentData, url: e.target.value })}
                          className="bg-gray-800/50 border-gray-700/50 focus:border-blue-400 text-white placeholder:text-gray-500 font-['Lato'] text-xs"
                          disabled={isContentSaving}
                        />
                      </div>
                      <Button
                        onClick={handleSaveContent}
                        disabled={isContentSaving}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
                      >
                        {isContentSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        {currentContentBanner && currentContentBanner.is_active ? 'Update Content Banner' : 'Activate Content Banner'}
                      </Button>
                    </div>
                  </div>

                  {/* Sponsor Banner Section */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4 text-emerald-400" />
                        <h3 className="font-mono text-sm font-semibold text-white uppercase tracking-wide">Sponsor Banner</h3>
                      </div>
                      {currentSponsor && currentSponsor.is_active && (
                        <Button
                          onClick={() => toggleAnalytics('sponsor')}
                          variant={activeAnalytics === 'sponsor' ? 'default' : 'outline'}
                          size="sm"
                          className={`font-mono text-xs uppercase tracking-wide transition-all duration-200 ${
                            activeAnalytics === 'sponsor'
                              ? 'bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg'
                              : 'text-emerald-400 hover:text-emerald-300 border-emerald-400/50 hover:bg-emerald-400/10'
                          }`}
                        >
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {activeAnalytics === 'sponsor' ? 'Hide Analytics' : 'Show Analytics'}
                        </Button>
                      )}
                    </div>

                    {/* Current Sponsor Banner Display */}
                    {currentSponsor && currentSponsor.is_active && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                            <span className="text-xs text-emerald-400 uppercase tracking-wide font-['Lato']">Active</span>
                          </div>
                          <Button
                            onClick={handleDeleteSponsor}
                            disabled={isSponsorSaving}
                            variant="outline"
                            size="sm"
                            className="text-red-400 border-red-400/50 hover:bg-red-400/10 font-mono text-xs uppercase tracking-wide"
                          >
                            {isSponsorSaving ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <div className="w-full h-20 bg-gray-800/60 border border-gray-700/50 rounded overflow-hidden mb-3">
                          <img
                            src={currentSponsor.img_url}
                            alt="Sponsor Banner"
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-400 font-mono">
                          <ExternalLink className="h-3 w-3" />
                          <span className="truncate font-['Lato']">{currentSponsor.url.substring(0, 30)}...</span>
                        </div>
                      </motion.div>
                    )}

                    {/* Sponsor Banner Configuration Form */}
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <label className="font-mono text-xs text-gray-300 uppercase tracking-wide font-semibold">
                          Banner Image URL
                        </label>
                        <Input
                          placeholder="https://example.com/sponsor-banner.jpg"
                          value={sponsorData.img_url}
                          onChange={(e) => setSponsorData({ ...sponsorData, img_url: e.target.value })}
                          className="bg-gray-800/50 border-gray-700/50 focus:border-emerald-400 text-white placeholder:text-gray-500 font-mono text-xs"
                          disabled={isSponsorSaving}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="font-mono text-xs text-gray-300 uppercase tracking-wide font-semibold">
                          Affiliate Link URL
                        </label>
                        <Input
                          placeholder="https://example.com/affiliate-link"
                          value={sponsorData.url}
                          onChange={(e) => setSponsorData({ ...sponsorData, url: e.target.value })}
                          className="bg-gray-800/50 border-gray-700/50 focus:border-emerald-400 text-white placeholder:text-gray-500 font-mono text-xs"
                          disabled={isSponsorSaving}
                        />
                      </div>
                      <Button
                        onClick={handleSaveSponsor}
                        disabled={isSponsorSaving}
                        className="w-full bg-emerald-600 hover:bg-emerald-700 text-white font-mono text-xs uppercase tracking-wide font-semibold"
                      >
                        {isSponsorSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Save className="h-4 w-4 mr-2" />
                        )}
                        {currentSponsor && currentSponsor.is_active ? 'Update Sponsor Banner' : 'Activate Sponsor Banner'}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Unified Analytics Section */}
                <AnimatePresence>
                  {activeAnalytics === 'content' && currentContentBanner && currentContentBanner.is_active && currentContentBanner.id && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="pt-4 border-t border-gray-700/50"
                    >
                      <UnifiedBannerAnalytics
                        bannerId={currentContentBanner.id}
                        bannerType="content"
                      />
                    </motion.div>
                  )}

                  {activeAnalytics === 'sponsor' && currentSponsor && currentSponsor.is_active && currentSponsor.id && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="pt-4 border-t border-gray-700/50"
                    >
                      <UnifiedBannerAnalytics
                        bannerId={currentSponsor.id}
                        bannerType="sponsor"
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};

export default CompactBannerConfig;
