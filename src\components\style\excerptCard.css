/* Enhanced Excerpt Card Styles with Theme Integration - List Layout */

/* Base excerpt card styling - horizontal list layout */
.excerpt-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  backdrop-filter: blur(12px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(to right,
    rgba(15, 23, 42, 0.8),
    rgba(15, 23, 42, 0.6),
    rgba(30, 41, 59, 0.8)
  );
  border: 1px solid rgba(71, 85, 105, 0.3);
}

/* Theme-aware hover effects */
.excerpt-card:hover {
  border-color: rgba(var(--theme-primary-rgb), 0.5);
  background: linear-gradient(to right,
    rgba(15, 23, 42, 0.9),
    rgba(15, 23, 42, 0.7),
    rgba(30, 41, 59, 0.9)
  );
}

/* Game cover styling */
.excerpt-game-cover {
  aspect-ratio: 3/4; /* Standard game cover aspect ratio */
  object-fit: cover;
}

/* Game icon fallback with theme integration */
.excerpt-game-icon-fallback {
  position: relative;
}

.excerpt-game-icon-fallback::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.2;
  background: radial-gradient(circle at center, var(--theme-primary), transparent 70%);
}

/* Theme accent border for fallback covers */
.excerpt-game-fallback-border {
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}

/* Neural score display */
.excerpt-score-display {
  position: relative;
}

.excerpt-score-glow {
  position: absolute;
  inset: 0;
  border-radius: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 0 20px rgba(var(--theme-primary-rgb), 0.4);
}

.excerpt-card:hover .excerpt-score-glow {
  opacity: 0.3;
}

/* Banner image enhancements */
.excerpt-card-banner {
  position: relative;
  height: 12rem;
  overflow: hidden;
}

.excerpt-card-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.excerpt-card:hover .excerpt-card-banner img {
  transform: scale(1.05);
}

/* Theme-aware glow overlay */
.excerpt-card-glow {
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: radial-gradient(circle at center, var(--theme-primary), transparent 70%);
}

.excerpt-card:hover .excerpt-card-glow {
  opacity: 0.2;
}

/* Fallback game icon styling */
.excerpt-card-fallback {
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, 
    rgb(30, 41, 59), 
    rgb(51, 65, 85), 
    rgb(71, 85, 105)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.excerpt-card-fallback::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background: radial-gradient(circle at 30% 30%, var(--theme-primary), transparent 50%);
}

/* Content section background glow */
.excerpt-card-content {
  position: relative;
  padding: 1.5rem;
  z-index: 10;
}

.excerpt-card-content::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.05;
  border-radius: 0 0 0.75rem 0.75rem;
  background: radial-gradient(ellipse at bottom, var(--theme-primary), transparent 70%);
}

/* Enhanced badge styling */
.excerpt-card-badge {
  border: none;
  backdrop-filter: blur(12px);
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(var(--theme-primary-rgb), 0.3);
}

.excerpt-card-badge.platform {
  background-color: rgba(var(--theme-primary-rgb), 0.9);
  color: white;
}

.excerpt-card-badge.private {
  background-color: rgba(249, 115, 22, 0.9);
  color: white;
}

/* Rating badge enhancements */
.excerpt-card-rating {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(12px);
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(var(--theme-primary-rgb), 0.2);
  transition: transform 0.2s ease;
}

.excerpt-card-rating:hover {
  transform: scale(1.05);
}

/* Enhanced button styling */
.excerpt-card-button {
  width: 100%;
  border: 1px solid rgba(71, 85, 105, 0.5);
  color: rgb(203, 213, 225);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.excerpt-card:hover .excerpt-card-button {
  border-color: var(--theme-primary);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  color: white;
  box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.3);
}

/* Button shimmer effect */
.excerpt-card-button-shimmer {
  position: absolute;
  inset: 0;
  opacity: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(var(--theme-primary-rgb), 0.1), 
    transparent
  );
  transition: opacity 0.3s ease;
}

.excerpt-card:hover .excerpt-card-button-shimmer {
  opacity: 1;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Tag styling with theme integration */
.excerpt-card-tag {
  background: rgba(30, 41, 59, 0.6);
  color: rgb(203, 213, 225);
  border: 1px solid rgba(71, 85, 105, 0.3);
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.excerpt-card:hover .excerpt-card-tag {
  border-color: rgba(var(--theme-primary-rgb), 0.3);
  background: rgba(51, 65, 85, 0.6);
}

/* Loading state enhancements */
.excerpt-card-loading {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  height: 20rem;
  background: linear-gradient(to bottom right, 
    rgba(15, 23, 42, 0.6), 
    rgba(30, 41, 59, 0.6)
  );
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.excerpt-card-loading-shimmer {
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(71, 85, 105, 0.2), 
    transparent
  );
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Load more button styling */
.excerpt-load-more {
  border: 1px solid rgba(var(--theme-primary-rgb), 0.3);
  background-color: rgba(var(--theme-primary-rgb), 0.05);
  backdrop-filter: blur(4px);
  padding: 0.75rem 2rem;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(var(--theme-primary-rgb), 0.1);
}

/* Adaptive styling for dark backgrounds */
.excerpt-load-more.dark-background {
  color: rgb(203, 213, 225);
  border-color: rgba(var(--theme-primary-rgb), 0.3);
}

.excerpt-load-more.dark-background:hover {
  color: white;
  box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.2);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

/* Adaptive styling for light backgrounds */
.excerpt-load-more.light-background {
  color: rgb(51, 65, 85);
  border-color: rgba(71, 85, 105, 0.4);
  background-color: rgba(255, 255, 255, 0.8);
}

.excerpt-load-more.light-background:hover {
  color: rgb(30, 41, 59);
  border-color: rgba(var(--theme-primary-rgb), 0.5);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.2);
}

.excerpt-load-more:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .excerpt-card-content {
    padding: 1rem;
  }
  
  .excerpt-card-banner {
    height: 10rem;
  }
  
  .excerpt-load-more {
    padding: 0.5rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  .excerpt-card,
  .excerpt-card-banner img,
  .excerpt-card-button,
  .excerpt-card-rating {
    transition: none;
  }
  
  .excerpt-card-button-shimmer,
  .excerpt-card-loading-shimmer {
    animation: none;
  }
}

/* Focus states for accessibility */
.excerpt-card:focus-within {
  outline: none;
  box-shadow: 
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 0 0 3px rgba(var(--theme-primary-rgb), 0.5);
}

/* Dynamic Text Adaptation System */

/* Base adaptive text title */
.adaptive-text-title {
  font-weight: 700;
  letter-spacing: -0.02em;
  transition: all 0.3s ease;
  
  /* Default: white text for dark backgrounds */
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

.adaptive-text-title.light-background {
  color: #1e293b;
  text-shadow: 
    1px 1px 0px rgba(255, 255, 255, 0.8),
    2px 2px 4px rgba(255, 255, 255, 0.6);
}

.adaptive-text-title.dark-background {
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

/* Base adaptive subtitle */
.adaptive-text-subtitle {
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  
  /* Default: light gray for dark backgrounds */
  color: #94a3b8;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
}

.adaptive-text-subtitle.light-background {
  color: #475569;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.4);
}

.adaptive-text-subtitle.dark-background {
  color: #94a3b8;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
}

/* Adaptive content text */
.adaptive-text-content {
  transition: all 0.3s ease;
  color: #e2e8f0;
}

.adaptive-text-content.light-background {
  color: #334155;
}

.adaptive-text-content.dark-background {
  color: #e2e8f0;
}
