'use client';

// Forum Report Button Component
// Date: 21/06/2025
// Task: Forum Report System Implementation

import React, { useState } from 'react';
import { Flag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/contexts/auth-context';
import { useUserReport } from '@/hooks/useUserReports';
import { useQueryClient } from '@tanstack/react-query';

interface ForumReportButtonProps {
  postId: string;
  postTitle?: string;
  className?: string;
  size?: 'sm' | 'xs';
}

const FORUM_REPORT_REASONS = [
  { value: 'spam', label: 'Spam/Self-promotion' },
  { value: 'harassment', label: 'Harassment/Toxicity' },
  { value: 'off_topic', label: 'Off-topic/Irrelevant' },
  { value: 'misinformation', label: 'Misinformation' },
  { value: 'inappropriate', label: 'Inappropriate Content' },
  { value: 'other', label: 'Other' },
];

export function ForumReportButton({ 
  postId, 
  postTitle = 'Post',
  className = '',
  size = 'sm'
}: ForumReportButtonProps) {
  const { user } = useAuthContext();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Check if user has already reported this content
  const { data: hasReported = false } = useUserReport(postId, 'comment');

  const handleReport = async (reason: string, label: string) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to report content.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentId: postId,
          contentType: 'comment', // Forum posts are stored in comments table
          reporterId: user.uid,
          reason,
          description: `Forum post reported for: ${label}`,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Invalidate the user report query to update the UI immediately
        queryClient.invalidateQueries({ 
          queryKey: ['user-report', user.uid, postId, 'comment'] 
        });
        
        toast({
          title: 'Report submitted',
          description: 'Thank you for reporting. Our team will review this content.',
        });
      } else {
        toast({
          title: 'Report failed',
          description: result.error || 'Unable to submit report. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Report submission error:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while reporting.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={size}
          disabled={isSubmitting || hasReported}
          className={`
            transition-all duration-200 h-7 px-2
            ${size === 'xs' ? 'h-6 px-1.5' : ''}
            ${hasReported 
              ? 'text-red-400 cursor-not-allowed' 
              : 'text-slate-400 hover:text-red-400 hover:bg-red-400/10'
            }
            ${className}
          `}
          title={hasReported ? 'You have already reported this content' : 'Report this content'}
        >
          <Flag className={`${size === 'xs' ? 'w-3 h-3' : 'w-3.5 h-3.5'} ${hasReported ? 'fill-current' : ''}`} />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-56 bg-slate-800 border-slate-700"
      >
        <div className="px-3 py-2 border-b border-slate-700/50">
          <span className="font-mono text-slate-200 text-sm">
            <span className="text-slate-500">//</span> Report Content
          </span>
        </div>
        
        {hasReported ? (
          <div className="px-3 py-4 text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Flag className="w-4 h-4 text-red-400 fill-current" />
              <span className="font-mono text-sm text-red-400">Already Reported</span>
            </div>
            <p className="text-xs text-slate-400 font-mono">
              You have already reported this content. Our team will review it.
            </p>
          </div>
        ) : (
          FORUM_REPORT_REASONS.map((reason) => (
            <DropdownMenuItem
              key={reason.value}
              onClick={() => handleReport(reason.value, reason.label)}
              disabled={isSubmitting}
              className="text-slate-300 hover:text-white hover:bg-slate-700/50 cursor-pointer focus:bg-slate-700/50 focus:text-white"
            >
              <span className="text-xs text-[11px] font-lato font-thin">{reason.label}</span>
            </DropdownMenuItem>
          ))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
