import { createClient } from '@/lib/supabase/client';

export interface ContentBannerData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  impression_count?: number;
  click_count?: number;
  created_at?: string;
  updated_at?: string;
  last_impression_at?: string;
  last_click_at?: string;
}

// Get content banner for a user
export async function getUserContentBanner(userId: string): Promise<ContentBannerData | null> {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_content_banners')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No data found, return null
        return null;
      }
      console.error('Error fetching content banner:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getUserContentBanner:', error);
    return null;
  }
}

// Save or update a content banner
export async function saveContentBanner({
  userId,
  imgUrl,
  url,
  isActive = true,
}: {
  userId: string;
  imgUrl: string;
  url: string;
  isActive?: boolean;
}): Promise<ContentBannerData> {
  try {
    const supabase = createClient();

    // Check if user already has a content banner
    const existing = await getUserContentBanner(userId);

    let response;

    if (existing) {
      // Update existing record
      response = await supabase
        .from('user_content_banners')
        .update({
          img_url: imgUrl,
          url: url,
          is_active: isActive,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existing.id)
        .select()
        .single();
    } else {
      // Create new record
      response = await supabase
        .from('user_content_banners')
        .insert({
          user_id: userId,
          img_url: imgUrl,
          url: url,
          is_active: isActive,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();
    }

    const { data, error } = response;

    if (error) {
      console.error('Error saving content banner:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in saveContentBanner:', error);
    throw error;
  }
}

// Deactivate a content banner
export async function deactivateContentBanner(userId: string): Promise<ContentBannerData> {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_content_banners')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error deactivating content banner:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in deactivateContentBanner:', error);
    throw error;
  }
}

// Completely delete a content banner and all its analytics data
export async function deleteContentBanner(userId: string): Promise<boolean> {
  try {
    const supabase = createClient();

    // First get the banner to get its ID for analytics deletion
    const banner = await getUserContentBanner(userId);
    if (!banner) {
      return true; // Already deleted
    }

    // Delete analytics data first (foreign key constraint)
    if (banner.id) {
      const { error: analyticsError } = await supabase
        .from('content_banner_analytics')
        .delete()
        .eq('banner_id', banner.id);

      if (analyticsError) {
        console.error('Error deleting content banner analytics:', analyticsError);
        throw new Error(analyticsError.message);
      }
    }

    // Then delete the banner itself
    const { error: bannerError } = await supabase
      .from('user_content_banners')
      .delete()
      .eq('user_id', userId);

    if (bannerError) {
      console.error('Error deleting content banner:', bannerError);
      throw new Error(bannerError.message);
    }

    return true;
  } catch (error) {
    console.error('Error in deleteContentBanner:', error);
    throw error;
  }
}

// Track impressions when banner is viewed
export async function trackContentBannerImpression(
  bannerId: string,
  userAgent?: string,
  referrer?: string
): Promise<boolean> {
  try {
    const supabase = createClient();
    const { error } = await supabase.rpc('increment_content_banner_impression', {
      banner_id: bannerId,
      user_agent_param: userAgent,
      referrer_param: referrer
    });

    if (error) {
      console.error('Error tracking content banner impression:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in trackContentBannerImpression:', error);
    return false;
  }
}

// Track clicks when banner link is clicked
export async function trackContentBannerClick(
  bannerId: string,
  userAgent?: string,
  referrer?: string
): Promise<boolean> {
  try {
    const supabase = createClient();
    const { error } = await supabase.rpc('increment_content_banner_click', {
      banner_id: bannerId,
      user_agent_param: userAgent,
      referrer_param: referrer
    });

    if (error) {
      console.error('Error tracking content banner click:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in trackContentBannerClick:', error);
    return false;
  }
}
