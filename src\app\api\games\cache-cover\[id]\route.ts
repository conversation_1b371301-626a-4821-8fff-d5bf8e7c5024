// API route for manually caching game covers
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { downloadAndCacheGameCover, shouldRecacheCover } from '@/lib/services/gameCoverService';
import { extractIgdbIdServer } from '@/lib/utils/coverUtils.server';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Get Supabase client
    const supabase = await createServerClient();

    // Check authentication (optional - can be called by authenticated users or service)
    const { data: { user } } = await supabase.auth.getUser();
    
    // Get game data
    const { data: game, error: gameError } = await supabase
      .from('games')
      .select('id, igdb_id, cover_url, supabase_cover_url, cover_cache_status, igdb_cover_cached_at')
      .eq('id', id)
      .single();

    if (gameError || !game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Check if game has a cover URL to cache
    if (!game.cover_url) {
      return NextResponse.json(
        { error: 'Game has no cover URL to cache' },
        { status: 400 }
      );
    }

    // Extract IGDB ID
    const igdbId = extractIgdbIdServer(game);
    if (!igdbId) {
      return NextResponse.json(
        { error: 'Could not determine IGDB ID for cover caching' },
        { status: 400 }
      );
    }

    // Check if we should recache (unless force parameter is provided)
    const { searchParams } = new URL(request.url);
    const force = searchParams.get('force') === 'true';
    
    if (!force && !shouldRecacheCover(game)) {
      return NextResponse.json({
        success: true,
        message: 'Cover is already cached and up to date',
        cached: true,
        coverUrl: game.supabase_cover_url
      });
    }

    // Check if already processing
    if (game.cover_cache_status === 'processing') {
      return NextResponse.json({
        success: false,
        error: 'Cover is already being processed',
        processing: true
      }, { status: 409 });
    }

    // Start cover caching process
    console.log(`[Cover Cache] Starting cover caching for game ${id} (IGDB ID: ${igdbId})`);
    
    const result = await downloadAndCacheGameCover(
      game.id,
      igdbId,
      game.cover_url
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Cover cached successfully',
        coverUrl: result.supabaseCoverUrl,
        processingTimeMs: result.processingTimeMs,
        fileSizeBytes: result.fileSizeBytes
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
        processingTimeMs: result.processingTimeMs
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('[Cover Cache API] Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Get Supabase client
    const supabase = await createServerClient();

    // Get game cover status
    const { data: game, error: gameError } = await supabase
      .from('games')
      .select(`
        id, 
        igdb_id, 
        cover_url, 
        supabase_cover_url, 
        cover_cache_status, 
        igdb_cover_cached_at
      `)
      .eq('id', id)
      .single();

    if (gameError || !game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Get cover processing audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from('game_cover_audit')
      .select('*')
      .eq('game_id', id)
      .order('created_at', { ascending: false })
      .limit(10);

    const igdbId = extractIgdbIdServer(game);
    const shouldRecache = shouldRecacheCover(game);

    return NextResponse.json({
      game: {
        id: game.id,
        igdbId,
        coverUrl: game.cover_url,
        supabaseCoverUrl: game.supabase_cover_url,
        cacheStatus: game.cover_cache_status,
        cachedAt: game.igdb_cover_cached_at,
        shouldRecache
      },
      auditLogs: auditLogs || [],
      auditError: auditError?.message
    });

  } catch (error: any) {
    console.error('[Cover Cache Status API] Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Get Supabase client
    const supabase = await createServerClient();

    // Check authentication - only authenticated users can delete cached covers
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get game data
    const { data: game, error: gameError } = await supabase
      .from('games')
      .select('id, igdb_id, supabase_cover_url, cover_cache_status')
      .eq('id', id)
      .single();

    if (gameError || !game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    if (game.cover_cache_status !== 'cached' || !game.supabase_cover_url) {
      return NextResponse.json(
        { error: 'No cached cover to delete' },
        { status: 400 }
      );
    }

    const igdbId = extractIgdbIdServer(game);
    if (!igdbId) {
      return NextResponse.json(
        { error: 'Could not determine IGDB ID' },
        { status: 400 }
      );
    }

    // Delete cover files from storage
    const coverSizes = ['thumb', 'small', 'big'];
    const deletePromises = coverSizes.map(size => {
      const path = `covers/${igdbId}/${size}.webp`;
      return supabase.storage.from('game-covers').remove([path]);
    });

    await Promise.all(deletePromises);

    // Reset cover cache status in database
    const { error: updateError } = await supabase
      .from('games')
      .update({
        supabase_cover_url: null,
        cover_cache_status: 'pending',
        igdb_cover_cached_at: null
      })
      .eq('id', id);

    if (updateError) {
      throw new Error(`Failed to update game record: ${updateError.message}`);
    }

    // Log the deletion
    await supabase.rpc('log_cover_processing', {
      p_game_id: id,
      p_action: 'cache_deleted',
      p_supabase_url: game.supabase_cover_url
    });

    return NextResponse.json({
      success: true,
      message: 'Cached cover deleted successfully'
    });

  } catch (error: any) {
    console.error('[Cover Cache Delete API] Error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error.message || 'Unknown error'
      },
      { status: 500 }
    );
  }
}
