// src/components/suspension/SuspensionNotice.tsx
// SUSPENSION NOTICE COMPONENT
// Componente para exibir avisos de suspensão aos usuários

'use client';

import React from 'react';
import { AlertTriangle, Mail, Clock, User, Shield } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface SuspensionNoticeProps {
  /** Motivo da suspensão */
  reason?: string;
  /** Data/hora da suspensão */
  suspendedAt?: string;
  /** Username do admin que aplicou a suspensão */
  suspendedByUsername?: string;
  /** Estilo do componente (alert, card, inline) */
  variant?: 'alert' | 'card' | 'inline';
  /** Classe CSS adicional */
  className?: string;
  /** Se deve mostrar ações (contato, suporte) */
  showActions?: boolean;
  /** Callback quando clica em contatar suporte */
  onContactSupport?: () => void;
}

/**
 * Componente para exibir aviso de suspensão ao usuário
 */
export function SuspensionNotice({
  reason = 'Violação dos termos de uso',
  suspendedAt,
  suspendedByUsername,
  variant = 'alert',
  className = '',
  showActions = true,
  onContactSupport
}: SuspensionNoticeProps) {
  
  // Formatar data da suspensão
  const formatSuspensionDate = (dateString?: string) => {
    if (!dateString) return 'Data não disponível';
    
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { 
        addSuffix: true, 
        locale: ptBR 
      });
    } catch {
      return 'Data inválida';
    }
  };

  const suspensionContent = (
    <div className="space-y-3">
      <div className="flex items-center gap-2 text-orange-700">
        <AlertTriangle className="h-5 w-5" />
        <span className="font-semibold">Conta Suspensa</span>
      </div>
      
      <div className="space-y-2">
        <p className="text-sm text-muted-foreground">
          Sua conta foi suspensa e você não pode criar ou editar conteúdo. 
          Você ainda pode navegar e visualizar o conteúdo existente.
        </p>
        
        <div className="grid gap-2 text-xs">
          <div className="flex items-center gap-2">
            <Badge variant="destructive" className="text-xs">
              Motivo
            </Badge>
            <span className="text-muted-foreground">{reason}</span>
          </div>
          
          {suspendedAt && (
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                Suspenso {formatSuspensionDate(suspendedAt)}
              </span>
            </div>
          )}
          
          {suspendedByUsername && (
            <div className="flex items-center gap-2">
              <User className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                Por: {suspendedByUsername}
              </span>
            </div>
          )}
        </div>
      </div>
      
      {showActions && (
        <div className="flex gap-2 pt-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onContactSupport}
            className="text-xs"
          >
            <Mail className="h-3 w-3 mr-1" />
            Contatar Suporte
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => window.open('/terms', '_blank')}
            className="text-xs"
          >
            <Shield className="h-3 w-3 mr-1" />
            Ver Termos
          </Button>
        </div>
      )}
    </div>
  );

  // Renderizar baseado na variante
  switch (variant) {
    case 'card':
      return (
        <Card className={`border-orange-200 bg-orange-50/50 ${className}`}>
          <CardHeader className="pb-3">
            <CardTitle className="text-orange-800 text-base flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Conta Suspensa
            </CardTitle>
            <CardDescription className="text-orange-700">
              Acesso limitado devido à suspensão da conta
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            {suspensionContent}
          </CardContent>
        </Card>
      );

    case 'inline':
      return (
        <div className={`p-3 rounded-md bg-orange-50 border border-orange-200 ${className}`}>
          {suspensionContent}
        </div>
      );

    case 'alert':
    default:
      return (
        <Alert variant="destructive" className={`border-orange-200 bg-orange-50 ${className}`}>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle className="text-orange-800">Conta Suspensa</AlertTitle>
          <AlertDescription className="text-orange-700 space-y-3">
            {suspensionContent}
          </AlertDescription>
        </Alert>
      );
  }
}

/**
 * Componente compacto para mostrar status de suspensão em headers/navbars
 */
export function SuspensionBadge({
  reason,
  suspendedAt,
  className = ''
}: Pick<SuspensionNoticeProps, 'reason' | 'suspendedAt' | 'className'>) {
  return (
    <Badge 
      variant="destructive" 
      className={`bg-orange-500 hover:bg-orange-600 ${className}`}
    >
      <AlertTriangle className="h-3 w-3 mr-1" />
      Conta Suspensa
    </Badge>
  );
}

/**
 * Hook para detectar se um usuário está suspenso
 */
export function useSuspensionStatus(user: any) {
  const isSuspended = user?.suspended || user?.isSuspended || false;
  const suspensionReason = user?.suspension_reason || user?.suspensionReason;
  const suspendedAt = user?.suspended_at || user?.suspendedAt;
  const suspendedBy = user?.suspended_by || user?.suspendedBy;
  
  return {
    isSuspended,
    suspensionReason,
    suspendedAt,
    suspendedBy
  };
}

export default SuspensionNotice; 