-- Quick Admin Setup Verification
-- Date: 15/01/2025
-- Purpose: Quick check if admin user is properly configured

-- Single query to verify everything
SELECT 
    '🔍 ADMIN VERIFICATION REPORT' as report_title,
    u.email,
    p.username,
    p.display_name,
    p.is_admin,
    is_admin(p.id) as security_function_result,
    CASE 
        WHEN p.is_admin = true AND is_admin(p.id) = true THEN '✅ ADMIN FULLY CONFIGURED'
        WHEN p.is_admin = true AND is_admin(p.id) = false THEN '⚠️ ADMIN FLAG SET BUT FUNCTION FAILED'
        WHEN p.is_admin = false THEN '❌ USER NOT ADMIN'
        ELSE '❌ UNKNOWN STATE'
    END as admin_status,
    p.created_at,
    p.updated_at
FROM profiles p
JOIN auth.users u ON p.id = u.id
WHERE u.email = '<EMAIL>';

-- If no results, user needs to sign up first
-- If admin_status shows ❌ or ⚠️, run the setup script
