import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, reviewId, displayOrder, bannerId, storeLinks } = body;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createServerClient();

    switch (action) {
      case 'getBanners':
        try {
          const { data, error } = await supabase
            .from('featured_banners')
            .select(`
              id,
              user_id,
              review_id,
              display_order,
              is_active,
              created_at,
              updated_at,
              reviews!inner (
                id,
                title,
                game_name,
                game_id,
                main_image_url,
                igdb_cover_url,
                overall_score,
                content_lexical,
                like_count,
                view_count,
                created_at,
                played_on,
                slug,
                games:game_id (
                  cover_url,
                  igdb_id
                )
              )
            `)
            .eq('user_id', userId)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

          if (error) {
            console.error('Supabase error:', error);
            return NextResponse.json(
              { success: false, error: error.message },
              { status: 500 }
            );
          }

          // Transform data to include review information
          const banners = data?.map(banner => ({
            id: banner.id,
            user_id: banner.user_id,
            review_id: banner.review_id,
            display_order: banner.display_order,
            is_active: banner.is_active,
            created_at: banner.created_at,
            updated_at: banner.updated_at,
            review: Array.isArray(banner.reviews) ? banner.reviews[0] : banner.reviews
          })) || [];

          return NextResponse.json({ success: true, data: banners });
        } catch (error) {
          console.error('Get featured banners error:', error);
          return NextResponse.json(
            { success: false, error: 'Failed to fetch featured banners' },
            { status: 500 }
          );
        }

      case 'setBanner':
        if (!reviewId || !displayOrder) {
          return NextResponse.json(
            { success: false, error: 'Review ID and display order are required' },
            { status: 400 }
          );
        }

        try {
          // Validate display order (1-3)
          if (displayOrder < 1 || displayOrder > 3) {
            return NextResponse.json(
              { success: false, error: 'Display order must be between 1 and 3' },
              { status: 400 }
            );
          }

          // Check if review exists and belongs to user
          const { data: review, error: reviewError } = await supabase
            .from('reviews')
            .select('id, author_id, status, is_blocked')
            .eq('id', reviewId)
            .eq('author_id', userId)
            .eq('status', 'published')
            .eq('is_blocked', false)
            .single();

          if (reviewError || !review) {
            return NextResponse.json(
              { success: false, error: 'Review not found or not accessible' },
              { status: 404 }
            );
          }

          // Upsert the featured banner
          const { data: bannerData, error: bannerError } = await supabase
            .from('featured_banners')
            .upsert({
              user_id: userId,
              review_id: reviewId,
              display_order: displayOrder,
              is_active: true,
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'user_id,display_order',
              ignoreDuplicates: false
            })
            .select()
            .single();

          if (bannerError) {
            console.error('Banner upsert error:', bannerError);
            return NextResponse.json(
              { success: false, error: bannerError.message },
              { status: 500 }
            );
          }

          return NextResponse.json({ success: true, data: bannerData });
        } catch (error) {
          console.error('Set featured banner error:', error);
          return NextResponse.json(
            { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' },
            { status: 500 }
          );
        }

      case 'removeBanner':
        if (!displayOrder) {
          return NextResponse.json(
            { success: false, error: 'Display order is required' },
            { status: 400 }
          );
        }

        try {
          // Remove the featured banner
          const { error: deleteError } = await supabase
            .from('featured_banners')
            .delete()
            .eq('user_id', userId)
            .eq('display_order', displayOrder);

          if (deleteError) {
            console.error('Banner delete error:', deleteError);
            return NextResponse.json(
              { success: false, error: deleteError.message },
              { status: 500 }
            );
          }

          return NextResponse.json({ success: true });
        } catch (error) {
          console.error('Remove featured banner error:', error);
          return NextResponse.json(
            { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' },
            { status: 500 }
          );
        }

      case 'getStoreLinks':
        if (!bannerId) {
          return NextResponse.json(
            { success: false, error: 'Banner ID is required' },
            { status: 400 }
          );
        }
        
        try {
          const { data, error } = await supabase
            .from('featured_review_store_links')
            .select('*')
            .eq('banner_id', bannerId)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

          if (error) {
            console.error('Store links error:', error);
            return NextResponse.json(
              { success: false, error: error.message },
              { status: 500 }
            );
          }

          return NextResponse.json({ success: true, data: data || [] });
        } catch (error) {
          console.error('Get store links error:', error);
          return NextResponse.json(
            { success: false, error: 'Failed to fetch store links' },
            { status: 500 }
          );
        }

      case 'saveStoreLinks':
        if (!bannerId) {
          return NextResponse.json(
            { success: false, error: 'Banner ID is required' },
            { status: 400 }
          );
        }

        try {
          if (!Array.isArray(storeLinks)) {
            return NextResponse.json(
              { success: false, error: 'Store links must be an array' },
              { status: 400 }
            );
          }

          // Verify banner belongs to user
          const { data: banner, error: bannerError } = await supabase
            .from('featured_banners')
            .select('user_id')
            .eq('id', bannerId)
            .single();

          if (bannerError || !banner || banner.user_id !== userId) {
            return NextResponse.json(
              { success: false, error: 'Banner not found or access denied' },
              { status: 404 }
            );
          }

          // Delete existing store links for this banner
          const { error: deleteError } = await supabase
            .from('featured_review_store_links')
            .delete()
            .eq('banner_id', bannerId);

          if (deleteError) {
            console.error('Delete store links error:', deleteError);
            return NextResponse.json(
              { success: false, error: deleteError.message },
              { status: 500 }
            );
          }

          // Insert new store links
          if (storeLinks.length > 0) {
            const storeLinkData = storeLinks.map((link: any, index: number) => ({
              banner_id: bannerId,
              user_id: userId,
              store_name: link.store_name,
              price: link.price,
              original_price: link.original_price || null,
              store_url: link.store_url,
              display_order: index + 1,
              color_gradient: link.color_gradient || 'from-blue-500 to-purple-600',
              is_active: true
            }));

            const { error: insertError } = await supabase
              .from('featured_review_store_links')
              .insert(storeLinkData);

            if (insertError) {
              console.error('Store links insert error:', insertError);
              return NextResponse.json(
                { success: false, error: insertError.message },
                { status: 500 }
              );
            }
          }
          return NextResponse.json({ success: true });
        } catch (error) {
          console.error('Save store links error:', error);
          return NextResponse.json(
            { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' },
            { status: 500 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in featured banners API:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
