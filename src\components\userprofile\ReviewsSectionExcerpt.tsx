'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Calendar,
  ArrowRight,
  Eye,
  MessageCircle,
  Shield,
  Clock,
  Sparkles,
  Heart
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { useUserContent } from '@/hooks/useUserContent';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import type { UserProfile } from '@/lib/types';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import '@/components/style/excerptCard.css';

interface ReviewsSectionExcerptProps {
  profileData: UserProfile;
  currentUserId?: string;
  isOwnProfile: boolean;
  theme: any;
  searchTerm: string;
  filterPlatform: string;
  itemsPerPage?: number;
  defaultSort?: 'date' | 'rating' | 'title';
}

interface ExcerptCardProps {
  review: any;
  index: number;
  theme: any;
}

const ExcerptCard = React.memo(({ 
  review, 
  index, 
  theme
}: ExcerptCardProps) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [bannerLoaded, setBannerLoaded] = useState(false);
  const [bannerError, setBannerError] = useState(false);
  const [localBannerIsDark, setLocalBannerIsDark] = useState(true);

  const reviewUrl = `/reviews/view/${review.slug}`;

  // Helper function to normalize IGDB image URLs
  const normalizeIGDBImageUrl = (url: string | null | undefined): string | undefined => {
    if (!url || url === 'undefined' || url === 'null') return undefined;
    // Convert IGDB thumbnail URLs to cover_big size for better quality
    let normalizedUrl = url.startsWith('//') ? `https:${url}` : url;
    return normalizedUrl.replace('t_thumb', 't_cover_big');
  };

  // Format rating to 0-100 scale (matching other components)
  const formatRating = (rating: number) => {
    if (rating <= 1) return Math.round(rating * 100);
    if (rating <= 10) return Math.round(rating * 10);
    return Math.round(Math.max(0, Math.min(100, rating)));
  };

  const displayScore = formatRating(review.overallScore || review.rating || 0);

  // Theme colors
  const themeColors = theme?.colors || { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa' };

  // Get review banner URL
  const reviewBanner = review.main_image_url || review.game_image;

  // Function to analyze banner brightness without global dimmer interference
  const analyzeBannerBrightness = useCallback((imageUrl: string) => {
    if (!imageUrl) {
      setLocalBannerIsDark(true);
      return;
    }

    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        let totalBrightness = 0;
        let pixelCount = 0;

        // Sample every 10th pixel for performance
        for (let i = 0; i < data.length; i += 40) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          
          // Calculate luminance
          const brightness = (0.299 * r + 0.587 * g + 0.114 * b);
          totalBrightness += brightness;
          pixelCount++;
        }

        const averageBrightness = totalBrightness / pixelCount;
        // Consider dark if average brightness is below 128
        setLocalBannerIsDark(averageBrightness < 128);
      } catch (error) {
        // If analysis fails, assume dark
        setLocalBannerIsDark(true);
      }
    };

    img.onerror = () => {
      setLocalBannerIsDark(true);
    };

    img.src = imageUrl;
  }, []);

  // Analyze banner brightness when banner loads
  useEffect(() => {
    if (reviewBanner && bannerLoaded) {
      analyzeBannerBrightness(reviewBanner);
    } else {
      setLocalBannerIsDark(true);
    }
  }, [reviewBanner, bannerLoaded, analyzeBannerBrightness]);

  // Extract text from Lexical content (improved implementation)
  const extractTextFromLexical = (children: any[]): string => {
    if (!Array.isArray(children)) return '';
    
    let text = '';

    for (const child of children) {
      if (!child) continue;
      
      if (child.type === 'text') {
        text += child.text || '';
      } else if (child.type === 'paragraph' || child.type === 'heading') {
        if (child.children && Array.isArray(child.children)) {
          text += extractTextFromLexical(child.children) + ' ';
        }
      } else if (child.type === 'list') {
        if (child.children && Array.isArray(child.children)) {
          text += extractTextFromLexical(child.children) + ' ';
        }
      } else if (child.type === 'listitem') {
        if (child.children && Array.isArray(child.children)) {
          text += extractTextFromLexical(child.children) + ' ';
        }
      } else if (child.children && Array.isArray(child.children)) {
        text += extractTextFromLexical(child.children);
      }
    }

    return text.trim();
  };

  // Create excerpt from content (250 characters)
  const excerpt = useMemo(() => {
    let plainText = '';

    // Try to extract from Lexical content first
    if (review.content_lexical || review.contentLexical) {
      try {
        const lexicalContent = review.content_lexical || review.contentLexical;
        let lexicalData;

        if (typeof lexicalContent === 'string') {
          // Handle JSON string
          lexicalData = JSON.parse(lexicalContent);
        } else if (lexicalContent && typeof lexicalContent === 'object') {
          // Already an object
          lexicalData = lexicalContent;
        }

        // Extract text using the proper structure
        if (lexicalData && lexicalData.root && Array.isArray(lexicalData.root.children)) {
          plainText = extractTextFromLexical(lexicalData.root.children);
        } else if (lexicalData && Array.isArray(lexicalData.children)) {
          // Alternative structure
          plainText = extractTextFromLexical(lexicalData.children);
        } else if (lexicalData && typeof lexicalData === 'object') {
          // If it's still an object but not the expected structure, try to find text content
          const findText = (obj: any): string => {
            if (typeof obj === 'string') return obj;
            if (obj && typeof obj === 'object') {
              if (obj.text) return obj.text;
              if (obj.children && Array.isArray(obj.children)) {
                return obj.children.map(findText).join(' ');
              }
              if (Array.isArray(obj)) {
                return obj.map(findText).join(' ');
              }
              // Try to extract from any property that might contain text
              for (const key in obj) {
                if (key.includes('text') || key.includes('content')) {
                  const value = findText(obj[key]);
                  if (value) return value;
                }
              }
            }
            return '';
          };
          plainText = findText(lexicalData);
        }
      } catch (error) {
        console.warn('Error parsing Lexical content:', error);
      }
    }

    // Fallback to other content fields
    if (!plainText || plainText.length < 10) {
      plainText = review.excerpt || 
                  review.review_text || 
                  review.content_markdown || 
                  review.description || 
                  '';
    }

    // Clean and format the text
    if (plainText) {
      // Remove HTML tags, normalize whitespace, and clean up
      plainText = plainText
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/[^\w\s.,!?;:()-]/g, '') // Remove special characters but keep basic punctuation
        .trim();
    }

    // Return truncated text or fallback message
    if (!plainText || plainText.length < 5) {
      return 'Review content available - click to read more...';
    }

    return plainText.length > 250 ? plainText.substring(0, 250) + '...' : plainText;
  }, [review.content_lexical, review.contentLexical, review.excerpt, review.review_text, review.content_markdown, review.description]);

  // Format date as MM/DD/YYYY
  const formattedDate = useMemo(() => {
    return new Date(review.created_at).toLocaleDateString('en-US');
  }, [review.created_at]);

  // Title as excerpt (max 20 characters)
  const titleExcerpt = useMemo(() => {
    const title = review.title || review.game_name || 'Untitled Review';
    return title.length > 20 ? title.substring(0, 20) + '...' : title;
  }, [review.title, review.game_name]);


  // Format numbers for display
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <Link href={reviewUrl} className="block">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        transition={{
          duration: 0.4,
          delay: index * 0.1,
          ease: [0.4, 0, 0.2, 1]
        }}
        className="relative overflow-hidden rounded-xl transition-all duration-300 hover:border-gray-700 mb-[35px]"
        style={{
          backgroundImage: reviewBanner && !bannerError ? `url(${reviewBanner})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: review.main_image_position || 'center',
          backgroundColor: !reviewBanner || bannerError ? '#111827' : 'transparent',
          borderColor: isHovered ? `${themeColors.primary}30` : undefined,
        }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
      {/* Banner Image (hidden, just for loading) */}
      {reviewBanner && !bannerError && (
        <img
          src={reviewBanner}
          alt="Review banner"
          className="hidden"
          onLoad={() => setBannerLoaded(true)}
          onError={() => setBannerError(true)}
        />
      )}
      
      {/* Very Dark Blur Overlay */}
      <div 
        className="absolute inset-0 bg-black/80 rounded-xl"
        style={{
          backdropFilter: 'blur(12px) saturate(120%)',
        }}
      />
      <div className="relative z-10 p-6">
        <div className="flex items-center gap-4">
          {/* IGDB Game Cover */}
          <div className="flex-shrink-0 w-28 h-36 md:w-32 md:h-44 rounded-lg overflow-hidden relative bg-gray-800">
            {(() => {
              const coverUrl = normalizeIGDBImageUrl(review.igdb_cover_url) ||
                             normalizeIGDBImageUrl(review.games?.cover_url) ||
                             review.game_image;
              return coverUrl && !imageError;
            })() ? (
              <motion.img
                src={(() => {
                  const coverUrl = normalizeIGDBImageUrl(review.igdb_cover_url) ||
                                 normalizeIGDBImageUrl(review.games?.cover_url) ||
                                 review.game_image;
                  return coverUrl || '';
                })()}
                alt={`${review.game_name} cover`}
                className={cn(
                  "w-full h-full object-cover transition-all duration-300",
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                )}
                loading="lazy"
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-700 via-gray-600 to-gray-500 flex items-center justify-center relative">
                <div className="text-white/60 text-2xl">🎮</div>
                <div 
                  className="absolute inset-0 border-2 rounded-lg opacity-20"
                  style={{ borderColor: themeColors.primary }}
                />
              </div>
            )}
          </div>

          {/* Content Section */}
          <div className="flex-1 min-w-0 space-y-3">
            <div className="space-y-2">
              {/* Review Title */}
              <motion.h2 
                className={cn(
                  "text-base font-semibold truncate transition-all duration-300 hover:opacity-80",
                  "adaptive-text-title",
                  localBannerIsDark ? 'dark-background' : 'light-background'
                )}
                style={{ color: isHovered ? themeColors.accent : undefined }}
              >
                {review.title || 'Untitled Review'}
              </motion.h2>

              {/* Game Name */}
              <h3 className={cn(
                "text-sm font-mono truncate transition-all duration-300",
                "adaptive-text-subtitle",
                localBannerIsDark ? 'dark-background' : 'light-background'
              )}>
                {review.game_name || 'Unknown Game'}
              </h3>

              <div className="flex items-center gap-3">
                <div className={cn(
                  "px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300",
                  localBannerIsDark 
                    ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                    : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                )}
                style={{
                  backdropFilter: 'blur(8px)',
                  boxShadow: localBannerIsDark 
                    ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                    : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                }}
                >
                  {formattedDate}
                </div>
                {review.played_on && (
                  <div className={cn(
                    "px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300",
                    localBannerIsDark 
                      ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                      : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                  )}
                  style={{
                    backdropFilter: 'blur(8px)',
                    boxShadow: localBannerIsDark 
                      ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                      : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                  }}
                  >
                    {review.played_on}
                  </div>
                )}
              </div>

              <motion.p 
                className={cn(
                  "text-sm leading-relaxed line-clamp-2 transition-all duration-300",
                  "adaptive-text-content",
                  localBannerIsDark ? 'dark-background' : 'light-background'
                )}
              >
                {excerpt}
              </motion.p>
            </div>
          </div>

          {/* Modern Stats Module */}
          <div className="flex-shrink-0 min-w-[120px]">
              {/* Interactive Round Gauge - Score */}
              <div className="relative mb-3 flex justify-center">
                <div className="relative w-20 h-20">
                  {/* Background Circle */}
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 80 80">
                    <circle
                      cx="40"
                      cy="40"
                      r="32"
                      fill="none"
                      stroke="rgba(255,255,255,0.1)"
                      strokeWidth="6"
                      className="transition-all duration-300"
                    />
                    {/* Progress Circle */}
                    <circle
                      cx="40"
                      cy="40"
                      r="32"
                      fill="none"
                      strokeWidth="6"
                      strokeLinecap="round"
                      className="transition-all duration-1000 ease-out"
                      style={{
                        stroke: `url(#gradient-${review.id})`,
                        strokeDasharray: `${2 * Math.PI * 32}`,
                        strokeDashoffset: `${2 * Math.PI * 32 * (1 - displayScore / 100)}`,
                        filter: 'drop-shadow(0 0 6px rgba(139, 92, 246, 0.5))'
                      }}
                    />
                    {/* Gradient Definition */}
                    <defs>
                      <linearGradient id={`gradient-${review.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor={themeColors.primary} />
                        <stop offset="50%" stopColor={themeColors.accent} />
                        <stop offset="100%" stopColor={themeColors.secondary} />
                      </linearGradient>
                    </defs>
                  </svg>
                  
                  {/* Center Content */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <div className={cn(
                      "text-lg font-bold font-mono tabular-nums transition-all duration-300",
                      localBannerIsDark ? 'text-white' : 'text-gray-100'
                    )}>
                      {displayScore}
                    </div>
                    <div className={cn(
                      "text-[10px] font-mono uppercase tracking-wider transition-all duration-300 opacity-70",
                      localBannerIsDark ? 'text-purple-200' : 'text-purple-100'
                    )}>
                      SCORE
                    </div>
                  </div>
                  
                  {/* Outer Glow Ring */}
                  <div 
                    className="absolute inset-0 rounded-full opacity-30 blur-md"
                    style={{
                      background: `conic-gradient(from 0deg, ${themeColors.primary}40 0deg, ${themeColors.accent}40 ${displayScore * 3.6}deg, transparent ${displayScore * 3.6}deg)`
                    }}
                  />
                </div>
              </div>

              {/* Stats Pills - Matching Design */}
              <div className="flex gap-2 flex-wrap">
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300",
                  localBannerIsDark 
                    ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                    : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                )}
                style={{
                  backdropFilter: 'blur(8px)',
                  boxShadow: localBannerIsDark 
                    ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                    : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                }}
                >
                  <Eye className="w-3 h-3 text-blue-400" />
                  <span className="tabular-nums">{formatNumber(review.views_count || 0)}</span>
                </div>
                
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300",
                  localBannerIsDark 
                    ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                    : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                )}
                style={{
                  backdropFilter: 'blur(8px)',
                  boxShadow: localBannerIsDark 
                    ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                    : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                }}
                >
                  <Heart className="w-3 h-3 text-red-400" />
                  <span className="tabular-nums">{formatNumber(review.likes_count || 0)}</span>
                </div>
                
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300",
                  localBannerIsDark 
                    ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                    : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                )}
                style={{
                  backdropFilter: 'blur(8px)',
                  boxShadow: localBannerIsDark 
                    ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                    : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                }}
                >
                  <MessageCircle className="w-3 h-3 text-green-400" />
                  <span className="tabular-nums">{formatNumber(review.comments_count || 0)}</span>
                </div>
              </div>
              
              {/* Read More Button - Full Width */}
              <div className="mt-2">
                <div className={cn(
                  "flex items-center justify-center gap-1 px-2 py-1 rounded-md font-mono text-xs font-medium transition-all duration-300 w-full",
                  localBannerIsDark 
                    ? 'bg-gradient-to-r from-gray-800/80 to-gray-700/80 text-gray-200 border border-gray-600/50 shadow-lg' 
                    : 'bg-gradient-to-r from-white/90 to-gray-100/90 text-gray-700 border border-gray-300/60 shadow-md'
                )}
                style={{
                  backdropFilter: 'blur(8px)',
                  boxShadow: localBannerIsDark 
                    ? '0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)' 
                    : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
                }}
                >
                  <span>Read More</span>
                  <ArrowRight className="w-3 h-3 text-blue-400" />
                </div>
              </div>
            </div>
        </div>
      </div>
      </motion.div>
    </Link>
  );
});

ExcerptCard.displayName = 'ExcerptCard';

const ReviewsSectionExcerpt = React.memo(({
  profileData,
  currentUserId,
  isOwnProfile,
  theme,
  searchTerm,
  filterPlatform,
  itemsPerPage = 6,
  defaultSort = 'date'
}: ReviewsSectionExcerptProps) => {
  const [displayedCount, setDisplayedCount] = useState(itemsPerPage);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Background brightness detection for text adaptation
  const isDarkBackground = useBackgroundBrightness();

  const {
    data,
    isLoading,
    error
  } = useUserContent(profileData.id, currentUserId || undefined);

  const reviews = data?.reviews || [];

  // Filter and sort reviews
  const filteredReviews = useMemo(() => {
    if (!data?.reviews) return [];

    let filtered = data.reviews.filter((review: any) => {
      // Search filter - using correct field names
      const matchesSearch = !searchTerm ||
        review.game_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.review_text?.toLowerCase().includes(searchTerm.toLowerCase());

      // Platform filter - using played_on field (where user actually played)
      const matchesPlatform = filterPlatform === 'all' ||
        review.played_on?.toLowerCase() === filterPlatform.toLowerCase();

      return matchesSearch && matchesPlatform;
    });

    // Sort reviews using correct field names
    return filtered.sort((a: any, b: any) => {
      switch (defaultSort) {
        case 'rating':
          return (b.overallScore || 0) - (a.overallScore || 0);
        case 'title':
          return a.game_name.localeCompare(b.game_name);
        case 'date':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [data?.reviews, searchTerm, filterPlatform, defaultSort]);

  const displayedReviews = filteredReviews.slice(0, displayedCount);
  const hasMoreReviews = displayedCount < filteredReviews.length;

  const loadMore = async () => {
    setIsLoadingMore(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate loading
    setDisplayedCount(prev => Math.min(prev + itemsPerPage, filteredReviews.length));
    setIsLoadingMore(false);
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <motion.div
            key={i}
            className="flex items-center gap-4 p-4 rounded-lg bg-gradient-to-r from-slate-900 to-slate-800 border border-slate-700"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: i * 0.05 }}
          >
            {/* Image placeholder */}
            <div className="w-28 h-36 md:w-32 md:h-44 bg-slate-700 rounded-lg animate-pulse" />
            {/* Content placeholder */}
            <div className="flex-1 space-y-2">
              <div className="h-3 bg-slate-700 rounded w-1/4 animate-pulse" />
              <div className="h-4 bg-slate-700 rounded w-3/4 animate-pulse" />
              <div className="h-3 bg-slate-600 rounded w-full animate-pulse" />
              <div className="h-3 bg-slate-600 rounded w-2/3 animate-pulse" />
            </div>
            {/* Score placeholder */}
            <div className="w-20 h-10 bg-slate-700 rounded-lg animate-pulse" />
          </motion.div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">Error loading reviews</p>
      </div>
    );
  }

  if (filteredReviews.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
          <Star className="w-8 h-8 text-slate-600" />
        </div>
        <h3 className="text-lg font-medium text-slate-300 mb-2">
          {searchTerm || filterPlatform ? 'No reviews match your filters' : 'No reviews yet'}
        </h3>
        <p className="text-slate-500">
          {searchTerm || filterPlatform 
            ? 'Try adjusting your search criteria' 
            : 'Reviews will appear here when they are published'
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Reviews List */}
      <div className="space-y-6">
        <AnimatePresence mode="popLayout">
          {displayedReviews.map((review: any, index: number) => (
            <ExcerptCard
              key={review.id}
              review={review}
              index={index}
              theme={theme}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Enhanced Load More Button with Theme Integration */}
      {hasMoreReviews && (
        <div className="flex justify-center -mt-4">
          <Button
              onClick={loadMore}
              disabled={isLoadingMore}
              className={cn(
                "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
                isDarkBackground 
                  ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70' 
                  : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
              )}
              style={{
                backdropFilter: 'blur(8px)',
                boxShadow: isDarkBackground 
                  ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)' 
                  : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
              }}
            >
              {isLoadingMore ? (
                <motion.div
                  className="flex items-center gap-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Clock className="w-4 h-4" />
                  </motion.div>
                  Loading more reviews...
                </motion.div>
              ) : (
                <span className="flex items-center gap-2">
                  Load More Reviews
                  <Badge
                    variant="secondary"
                    className="bg-slate-700 text-slate-300 text-xs"
                  >
                    {filteredReviews.length - displayedCount} remaining
                  </Badge>
                </span>
              )}
            </Button>
        </div>
      )}
    </div>
  );
});

ReviewsSectionExcerpt.displayName = 'ReviewsSectionExcerpt';

export default ReviewsSectionExcerpt;
