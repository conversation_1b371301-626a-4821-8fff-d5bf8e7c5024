import { createServerClient } from '@/lib/supabase/server';
import { createClient } from '@supabase/supabase-js';
import { authenticator } from 'otplib';
import QRCode from 'qrcode';
import { randomBytes, createCipheriv, createDecipheriv } from 'crypto';

// MFA Configuration
const MFA_CONFIG = {
  serviceName: 'CriticalPixel Admin',
  backupCodeCount: 10,
  backupCodeLength: 8,
  tokenWindow: 2, // Allow 2 periods of drift
  sessionDuration: 60 * 60 * 1000, // 1 hour in milliseconds
};

// Types
export interface MFASetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
  recoveryPhrase: string;
}

export interface MFAVerificationResult {
  valid: boolean;
  usedBackupCode?: boolean;
  remainingBackupCodes?: number;
}

export interface MFAStatus {
  isEnabled: boolean;
  isRequired: boolean;
  hasBackupCodes: boolean;
  lastVerified?: string;
  enrolledAt?: string;
}

export class MFAService {
  // Helper to create service role client for admin operations
  private static createServiceClient() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    return createClient(supabaseUrl, supabaseServiceKey);
  }

  private static generateSecret(): string {
    return authenticator.generateSecret();
  }

  private static generateBackupCodes(): string[] {
    return Array.from({ length: MFA_CONFIG.backupCodeCount }, () => 
      randomBytes(MFA_CONFIG.backupCodeLength / 2).toString('hex').toUpperCase()
    );
  }

  private static generateRecoveryPhrase(): string {
    // Generate a human-readable recovery phrase
    const words = [
      'stellar', 'nebula', 'cosmic', 'galaxy', 'asteroid', 'photon', 'quantum', 'fusion',
      'meteor', 'eclipse', 'supernova', 'comet', 'orbit', 'plasma', 'gravity', 'velocity'
    ];
    return Array.from({ length: 6 }, () => 
      words[Math.floor(Math.random() * words.length)]
    ).join('-');
  }

  /**
   * Configura MFA para um usuário admin
   */
  static async setupMFA(userId: string): Promise<MFASetupResult> {
    // Use service client for admin verification (bypasses RLS)
    const serviceClient = this.createServiceClient();

    // Verificar se o usuário é admin
    const { data: profile, error: profileError } = await serviceClient
      .from('profiles')
      .select('is_admin, admin_level')
      .eq('id', userId)
      .single();

    console.log('MFA Setup Debug:', {
      userId,
      profile,
      profileError,
      isAdmin: profile ? profile.is_admin : 'no profile'
    });

    if (profileError || !profile || !profile.is_admin) {
      throw new Error('MFA setup only available for admin users');
    }

    // Use regular client for user operations
    const supabase = await createServerClient();

    const secret = this.generateSecret();
    const backupCodes = this.generateBackupCodes();
    const recoveryPhrase = this.generateRecoveryPhrase();
    
    // Obter informações do usuário para QR code
    const { data: user } = await supabase.auth.getUser();
    const email = user.user?.email || '<EMAIL>';
    
    // Gerar URL de autenticação TOTP
    const otpauth = authenticator.keyuri(email, MFA_CONFIG.serviceName, secret);
    const qrCodeUrl = await QRCode.toDataURL(otpauth, {
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    });

    // SEMPRE DELETAR CONFIGURAÇÃO EXISTENTE E CRIAR NOVA (FORÇA RECONFIGURAÇÃO)
    console.log('🔄 FORÇANDO RECONFIGURAÇÃO MFA - Deletando configuração existente...');
    
    // Deletar qualquer configuração existente (corrompida ou não)
    await supabase
      .from('user_mfa_settings')
      .delete()
      .eq('user_id', userId as any);
      
    // Deletar também sessões MFA existentes
    await supabase
      .from('mfa_verification_sessions')
      .delete()
      .eq('user_id', userId as any);

    console.log('✅ Configurações MFA antigas removidas');

    // Criar nova configuração SEMPRE
    const { error } = await supabase
      .from('user_mfa_settings')
      .insert({
        user_id: userId,
        secret_encrypted: this.encryptSecret(secret),
        backup_codes_encrypted: this.encryptBackupCodes(backupCodes),
        recovery_phrase_encrypted: this.encryptSecret(recoveryPhrase),
        is_enabled: false, // Usuário deve verificar primeiro token para ativar
        setup_at: new Date().toISOString(),
        admin_level: profile.admin_level || 'ADMIN',
      } as any);

    if (error) {
      throw new Error(`Failed to setup MFA: ${error.message}`);
    }

    // Log do evento de configuração
    await this.logMFAEvent('MFA_SETUP_INITIATED', userId, {
      adminLevel: profile.admin_level,
      backupCodesGenerated: backupCodes.length,
    });

    return {
      secret,
      qrCodeUrl,
      backupCodes,
      recoveryPhrase,
    };
  }

  /**
   * Verifica token MFA (TOTP ou backup code)
   */
  static async verifyMFAToken(
    userId: string, 
    token: string,
    operation?: string
  ): Promise<MFAVerificationResult> {
    const supabase = await createServerClient();
    
    const { data: mfaSettings, error } = await supabase
      .from('user_mfa_settings')
      .select('*')
      .eq('user_id', userId as any)
      .single();

    if (error || !mfaSettings) {
      throw new Error('MFA not configured for this user');
    }

    const secret = this.decryptSecret((mfaSettings as any).secret_encrypted);
    
    // Primeiro, tentar verificar como token TOTP
    const isValidTOTP = authenticator.verify({
      token: token.replace(/\s/g, ''), // Remove espaços
      secret,
    });

    if (isValidTOTP) {
      // Ativar MFA se esta for a primeira verificação bem-sucedida
      if (!(mfaSettings as any).is_enabled) {
        await supabase
          .from('user_mfa_settings')
          .update({
            is_enabled: true,
            verified_at: new Date().toISOString(),
          } as any)
          .eq('user_id', userId as any);

        await this.logMFAEvent('MFA_ENABLED', userId, { operation });
      }

      // Atualizar último uso
      await supabase
        .from('user_mfa_settings')
        .update({ last_used_at: new Date().toISOString() } as any)
        .eq('user_id', userId as any);

      await this.logMFAEvent('MFA_TOTP_VERIFIED', userId, { operation });

      return { valid: true };
    }

    // Se TOTP falhou, tentar backup codes
    const backupCodes = this.decryptBackupCodes((mfaSettings as any).backup_codes_encrypted);
    const normalizedToken = token.replace(/\s/g, '').toUpperCase();
    const backupCodeIndex = backupCodes.findIndex(code => code === normalizedToken);
    
    if (backupCodeIndex !== -1) {
      // Remover código de backup usado
      backupCodes.splice(backupCodeIndex, 1);
      
      await supabase
        .from('user_mfa_settings')
        .update({
          backup_codes_encrypted: this.encryptBackupCodes(backupCodes),
          last_used_at: new Date().toISOString(),
        } as any)
        .eq('user_id', userId as any);

      await this.logMFAEvent('MFA_BACKUP_CODE_USED', userId, { 
        operation,
        remainingCodes: backupCodes.length,
      });

      // Alertar se restam poucos códigos
      if (backupCodes.length <= 2) {
        await this.logMFAEvent('MFA_LOW_BACKUP_CODES', userId, {
          remainingCodes: backupCodes.length,
        });
      }

      return { 
        valid: true, 
        usedBackupCode: true,
        remainingBackupCodes: backupCodes.length,
      };
    }

    // Token inválido
    await this.logMFAEvent('MFA_INVALID_TOKEN', userId, { 
      operation,
      tokenLength: token.length,
    });

    return { valid: false };
  }

  /**
   * Verifica se MFA é obrigatório para usuário/operação
   */
  static async isMFARequired(userId: string, operation?: string): Promise<boolean> {
    const supabase = await createServerClient();
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('admin_level, is_admin')
      .eq('id', userId as any)
      .single();

    if (!profile || !(profile as any).is_admin) {
      return false;
    }

    // Sempre exigir MFA para SUPER_ADMIN
    if ((profile as any).admin_level === 'SUPER_ADMIN') {
      return true;
    }

    // Exigir MFA para operações sensíveis
    const mfaRequiredOperations = [
      'USER_DELETE',
      'ADMIN_PROMOTE', 
      'SECURITY_CONFIG',
      'BULK_USER_UPDATE',
      'SYSTEM_SETTINGS_MODIFY',
      'AUDIT_LOG_ACCESS',
    ];

    if (operation && mfaRequiredOperations.includes(operation)) {
      return true;
    }

    // Para outros admins, MFA é recomendado mas não obrigatório por enquanto
    // TODO: Tornar obrigatório baseado em política de segurança
    return false;
  }

  /**
   * Obtém status do MFA para um usuário
   */
  static async getMFAStatus(userId: string): Promise<MFAStatus> {
    const supabase = await createServerClient();
    
    const { data: mfaSettings } = await supabase
      .from('user_mfa_settings')
      .select('is_enabled, setup_at, verified_at, last_used_at, backup_codes_encrypted, secret_encrypted')
      .eq('user_id', userId as any)
      .single();

    if (!mfaSettings) {
      return {
        isEnabled: false,
        isRequired: await this.isMFARequired(userId),
        hasBackupCodes: false,
      };
    }

    // VERIFICAR SE O MFA ESTÁ CORROMPIDO
    try {
      // Tentar descriptografar o secret para verificar se está válido
      if ((mfaSettings as any).secret_encrypted) {
        this.decryptSecret((mfaSettings as any).secret_encrypted);
      }
      
      // Se chegou até aqui, o MFA está válido
      const hasBackupCodes = (mfaSettings as any).backup_codes_encrypted ?
        this.decryptBackupCodes((mfaSettings as any).backup_codes_encrypted).length > 0 : false;

      return {
        isEnabled: (mfaSettings as any).is_enabled,
        isRequired: await this.isMFARequired(userId),
        hasBackupCodes,
        lastVerified: (mfaSettings as any).last_used_at,
        enrolledAt: (mfaSettings as any).verified_at || (mfaSettings as any).setup_at,
      };
    } catch (error) {
      // MFA CORROMPIDO - DELETAR E PERMITIR RECONFIGURAÇÃO
      console.log('🔥 MFA CORROMPIDO DETECTADO - REMOVENDO:', error);
      
      try {
        await supabase
          .from('user_mfa_settings')
          .delete()
          .eq('user_id', userId as any);
          
        console.log('✅ MFA corrompido removido com sucesso');
      } catch (deleteError) {
        console.error('❌ Erro ao remover MFA corrompido:', deleteError);
      }
      
      // Retornar como se não tivesse MFA configurado
      return {
        isEnabled: false,
        isRequired: await this.isMFARequired(userId),
        hasBackupCodes: false,
      };
    }
  }

  /**
   * Regenera códigos de backup
   */
  static async regenerateBackupCodes(userId: string): Promise<string[]> {
    const supabase = await createServerClient();
    
    const newBackupCodes = this.generateBackupCodes();
    
    const { error } = await supabase
      .from('user_mfa_settings')
      .update({
        backup_codes_encrypted: this.encryptBackupCodes(newBackupCodes),
      } as any)
      .eq('user_id', userId as any);

    if (error) {
      throw new Error(`Failed to regenerate backup codes: ${error.message}`);
    }

    await this.logMFAEvent('MFA_BACKUP_CODES_REGENERATED', userId, {
      codesGenerated: newBackupCodes.length,
    });

    return newBackupCodes;
  }

  /**
   * Desabilita MFA para um usuário (emergência)
   */
  static async disableMFA(userId: string, reason: string, adminId: string): Promise<void> {
    const supabase = await createServerClient();
    
    const { error } = await supabase
      .from('user_mfa_settings')
      .update({
        is_enabled: false,
        disabled_at: new Date().toISOString(),
        disabled_reason: reason,
        disabled_by: adminId,
      } as any)
      .eq('user_id', userId as any);

    if (error) {
      throw new Error(`Failed to disable MFA: ${error.message}`);
    }

    await this.logMFAEvent('MFA_DISABLED', userId, {
      reason,
      disabledBy: adminId,
    });
  }

  // Métodos privados de criptografia
  private static encryptSecret(secret: string): string {
    const algorithm = 'aes-256-cbc';
    const key = Buffer.from(this.getEncryptionKey(), 'hex');
    const iv = randomBytes(16);

    const cipher = createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(secret, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    return `${iv.toString('hex')}:${encrypted}`;
  }

  private static decryptSecret(encryptedSecret: string): string {
    const [ivHex, encrypted] = encryptedSecret.split(':');
    const algorithm = 'aes-256-cbc';
    const key = Buffer.from(this.getEncryptionKey(), 'hex');
    const iv = Buffer.from(ivHex, 'hex');

    const decipher = createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  private static encryptBackupCodes(codes: string[]): string {
    return this.encryptSecret(JSON.stringify(codes));
  }

  private static decryptBackupCodes(encryptedCodes: string): string[] {
    const decrypted = this.decryptSecret(encryptedCodes);
    return JSON.parse(decrypted);
  }

  private static getEncryptionKey(): string {
    const key = process.env.MFA_ENCRYPTION_KEY;
    if (!key) {
      throw new Error('MFA_ENCRYPTION_KEY environment variable not set');
    }
    return key;
  }

  private static async logMFAEvent(
    eventType: string, 
    userId: string, 
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const supabase = await createServerClient();
      await supabase.rpc('log_security_event', {
        p_event_type: eventType,
        p_user_id: userId,
        p_admin_id: userId,
        p_ip_address: '127.0.0.1', // Server-side operation
        p_user_agent: 'MFA-Service',
        p_session_id: 'mfa-service',
        p_resource_type: 'MFA',
        p_action: eventType,
        p_event_data: metadata,
        p_severity: 'MEDIUM',
      });
    } catch (error) {
      console.error('Failed to log MFA event:', error);
    }
  }
} 