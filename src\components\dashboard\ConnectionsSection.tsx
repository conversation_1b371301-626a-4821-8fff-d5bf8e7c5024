'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { 
  Link2, 
  CheckCircle, 
  XCircle,
  Gamepad2,
  Users,
  Shield,
  Zap,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import SteamIcon from '@/components/ui/icons/gaming/SteamIcon';
import PlayStationIcon from '@/components/ui/icons/gaming/PlaystationIcon';
import XboxIcon from '@/components/ui/icons/gaming/XboxIcon';

// Import CSS for adaptive text classes
import '@/components/review-form/style/NewReview.css';

// Gaming Platform Icon Wrappers
const SteamIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-blue-500/90 to-blue-600/95 rounded-lg flex items-center justify-center">
    <SteamIcon className="w-5 h-5 text-white" />
  </div>
);

const PSNIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-blue-600/90 to-blue-700/95 rounded-lg flex items-center justify-center">
    <PlayStationIcon className="w-5 h-5 text-white" />
  </div>
);

const XboxIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-green-500/90 to-green-600/95 rounded-lg flex items-center justify-center">
    <XboxIcon className="w-5 h-5 text-white" />
  </div>
);

const EpicIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-slate-700/90 to-slate-800/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">E</span>
  </div>
);

const NintendoIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-red-500/90 to-red-600/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">N</span>
  </div>
);

// Social Platform Icons
const DiscordIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-indigo-500/90 to-indigo-600/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">D</span>
  </div>
);

const GoogleIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-red-500/90 to-yellow-500/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">G</span>
  </div>
);

const TwitterIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-blue-400/90 to-blue-500/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">X</span>
  </div>
);

const TwitchIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-purple-500/90 to-purple-600/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">T</span>
  </div>
);

const YouTubeIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-red-600/90 to-red-700/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">Y</span>
  </div>
);

const FacebookIconWrapper = () => (
  <div className="w-8 h-8 bg-gradient-to-r from-blue-600/90 to-blue-700/95 rounded-lg flex items-center justify-center">
    <span className="text-white text-sm font-bold">F</span>
  </div>
);

interface Connection {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  connected: boolean;
  apiAvailable: boolean;
  category: 'gaming' | 'social';
}

const connections: Connection[] = [
  // Gaming Connections
  {
    id: 'steam',
    name: 'Steam',
    description: 'Import game library and achievements',
    icon: <SteamIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'gaming'
  },
  {
    id: 'psn',
    name: 'PlayStation',
    description: 'Sync PlayStation trophies and game stats',
    icon: <PSNIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'gaming'
  },
  {
    id: 'xbox',
    name: 'Xbox Live',
    description: 'Track Xbox achievements and Game Pass',
    icon: <XboxIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'gaming'
  },
  {
    id: 'epic',
    name: 'Epic Games',
    description: 'Connect Epic Store library',
    icon: <EpicIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'gaming'
  },
  {
    id: 'nintendo',
    name: 'Nintendo',
    description: 'Nintendo Switch profile integration',
    icon: <NintendoIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'gaming'
  },
  // Social Connections
  {
    id: 'discord',
    name: 'Discord',
    description: 'Rich presence and server integration',
    icon: <DiscordIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'social'
  },
  {
    id: 'youtube',
    name: 'YouTube',
    description: 'Gaming content and channel sync',
    icon: <YouTubeIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'social'
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    description: 'Share reviews and gaming updates',
    icon: <TwitterIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'social'
  },
  {
    id: 'twitch',
    name: 'Twitch',
    description: 'Streaming and viewer analytics',
    icon: <TwitchIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'social'
  },
  {
    id: 'facebook',
    name: 'Facebook',
    description: 'Gaming groups and community',
    icon: <FacebookIconWrapper />,
    connected: false,
    apiAvailable: true,
    category: 'social'
  }
];

interface ConnectionCardProps {
  connection: Connection;
  onToggle: (id: string, connected: boolean) => void;
}

const ConnectionCard: React.FC<ConnectionCardProps> = ({ connection, onToggle }) => {
  const { toast } = useToast();

  const handleConnect = () => {
    if (!connection.apiAvailable) {
      toast({
        title: "API Not Available",
        description: `${connection.name} API integration is not currently available.`,
        variant: "destructive"
      });
      return;
    }

    // Mock connection logic
    toast({
      title: connection.connected ? "Disconnected" : "Connected",
      description: `${connection.name} has been ${connection.connected ? 'disconnected' : 'connected'} successfully.`,
    });
    
    onToggle(connection.id, !connection.connected);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {connection.icon}
          <div className="flex-1">
            <h4 className="font-mono text-sm font-semibold text-white">
              {connection.name}
            </h4>
            <p className="text-xs text-gray-400 font-['Lato'] mt-1">
              {connection.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {connection.connected ? (
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs font-mono uppercase tracking-wide">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="outline" className="border-gray-600 text-gray-400 text-xs font-mono uppercase tracking-wide">
              <XCircle className="w-3 h-3 mr-1" />
              Disconnected
            </Badge>
          )}
          
          <Button
            onClick={handleConnect}
            variant={connection.connected ? "outline" : "default"}
            size="sm"
            disabled={!connection.apiAvailable}
            className={`font-mono text-xs uppercase tracking-wide font-semibold ${
              connection.connected 
                ? "border-red-500/50 text-red-400 hover:bg-red-500/10" 
                : "bg-purple-600 hover:bg-purple-700 text-white"
            }`}
          >
            {connection.connected ? "Disconnect" : "Connect"}
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

interface ConnectionsSectionProps {
  userId: string;
}

export default function ConnectionsSection({ userId }: ConnectionsSectionProps) {
  const [userConnections, setUserConnections] = useState(connections);
  const [isExpanded, setIsExpanded] = useState(false);
  const isDarkBackground = useBackgroundBrightness();

  const handleToggle = (id: string, connected: boolean) => {
    setUserConnections(prev => prev.map(conn => 
      conn.id === id ? { ...conn, connected } : conn
    ));
  };

  const gamingConnections = userConnections.filter(c => c.category === 'gaming');
  const socialConnections = userConnections.filter(c => c.category === 'social');
  const totalConnected = userConnections.filter(c => c.connected).length;

  return (
    <div className="space-y-6">
      {/* Platform Connections Card */}
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardHeader 
          className="cursor-pointer hover:bg-gray-800/30 transition-colors duration-200"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <CardTitle className={`text-lg font-mono adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
                <span className="text-purple-400 mr-1">//</span>
                Platform Connections
              </CardTitle>
              <p className="text-xs text-gray-400 mt-1 font-mono">
                Connect your gaming and social platforms to enhance your experience
              </p>
            </div>
            <div className="text-gray-400 hover:text-white ml-4">
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </div>
        </CardHeader>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ 
                duration: 0.3, 
                ease: "easeInOut",
                opacity: { duration: 0.2 }
              }}
              style={{ overflow: 'hidden' }}
            >
              <CardContent className="space-y-6">
                {/* Statistics Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-500/20 rounded-lg">
                        <Link2 className="w-4 h-4 text-purple-400" />
                      </div>
                      <div>
                        <p className="text-xs text-gray-400 font-mono uppercase tracking-wide">Total Connected</p>
                        <p className="text-lg font-mono font-bold text-white">
                          {totalConnected}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-500/20 rounded-lg">
                        <Gamepad2 className="w-4 h-4 text-green-400" />
                      </div>
                      <div>
                        <p className="text-xs text-gray-400 font-mono uppercase tracking-wide">Gaming</p>
                        <p className="text-lg font-mono font-bold text-white">
                          {gamingConnections.filter(c => c.connected).length}/{gamingConnections.length}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <Users className="w-4 h-4 text-blue-400" />
                      </div>
                      <div>
                        <p className="text-xs text-gray-400 font-mono uppercase tracking-wide">Social</p>
                        <p className="text-lg font-mono font-bold text-white">
                          {socialConnections.filter(c => c.connected).length}/{socialConnections.length}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Gaming Platforms Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-500/20 rounded-lg">
                      <Gamepad2 className="w-5 h-5 text-purple-400" />
                    </div>
                    <h3 className="font-mono text-sm font-semibold text-white uppercase tracking-wide">
                      Gaming Platforms
                    </h3>
                  </div>
                  
                  <div className="space-y-3">
                    {gamingConnections.map((connection) => (
                      <ConnectionCard
                        key={connection.id}
                        connection={connection}
                        onToggle={handleToggle}
                      />
                    ))}
                  </div>
                </div>

                {/* Social Platforms Section */}
                <div className="space-y-4 pt-6 border-t border-gray-700/50">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500/20 rounded-lg">
                      <Users className="w-5 h-5 text-blue-400" />
                    </div>
                    <h3 className="font-mono text-sm font-semibold text-white uppercase tracking-wide">
                      Social Platforms
                    </h3>
                  </div>
                  
                  <div className="space-y-3">
                    {socialConnections.map((connection) => (
                      <ConnectionCard
                        key={connection.id}
                        connection={connection}
                        onToggle={handleToggle}
                      />
                    ))}
                  </div>
                </div>

                {/* Privacy & Security Info */}
                <div className="pt-6 border-t border-gray-700/50">
                  <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-700/50">
                    <div className="flex items-start gap-4">
                      <div className="p-2 bg-blue-500/20 rounded-lg">
                        <Shield className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <h4 className="font-mono text-sm font-semibold text-white mb-2 uppercase tracking-wide">
                          Privacy & Security
                        </h4>
                        <p className="text-gray-400 text-xs mb-3 font-['Lato']">
                          Your connection data is encrypted and stored securely. You can disconnect any platform at any time.
                        </p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="border-green-500/30 text-green-400 text-xs font-mono uppercase tracking-wide">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Encrypted
                          </Badge>
                          <Badge variant="outline" className="border-blue-500/30 text-blue-400 text-xs font-mono uppercase tracking-wide">
                            <Shield className="w-3 h-3 mr-1" />
                            Secure
                          </Badge>
                          <Badge variant="outline" className="border-purple-500/30 text-purple-400 text-xs font-mono uppercase tracking-wide">
                            <Zap className="w-3 h-3 mr-1" />
                            Fast API
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
}