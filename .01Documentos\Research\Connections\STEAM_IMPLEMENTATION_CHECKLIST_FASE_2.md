# Steam API Implementation Checklist - FASE 2: Backend e APIs

## ✅ INSTRUÇÕES IMPORTANTES PARA A IA

**Ao implementar cada item desta checklist:**
1. ✅ **SEMPRE comente o código** explicando o que cada parte faz
2. ✅ **Documente as decisões** técnicas tomadas
3. ✅ **Explique o propósito** de cada arquivo/função criada
4. ✅ **Mencione dependências** e configurações necessárias
5. ✅ **Indique próximos passos** após cada implementação
6. ✅ **Teste cada endpoint** após implementação

---

## 📋 CHECKLIST FASE 2: BACKEND E APIs

### 🔌 1. SERVIÇOS DE INTEGRAÇÃO COM STEAM API

#### ☐ 1.1 Serviço Principal Steam
**Arquivo a criar:** `src/lib/services/steam.service.ts`

**Tarefa para IA:**
- [ ] Criar classe `SteamService` com métodos estáticos
- [ ] **COMENTAR**: Explicar cada método e sua função na Steam API
- [ ] **DOCUMENTAR**: Limitações e rate limits da Steam API
- [ ] **Implementar métodos:**
  - `getOwnedGames()` - Buscar jogos possuídos
  - `getPlayerSummary()` - Informações do perfil
  - `checkGameOwnership()` - Verificar posse específica
  - `getGameInfo()` - Detalhes do jogo
  - `normalizeGames()` - Normalizar dados para formato interno

**Funcionalidades obrigatórias:**
- Tratamento de perfis privados
- Cache de requisições 
- Normalização de dados
- Tratamento de erros da Steam API

#### ☐ 1.2 Utilitários Steam
**Arquivo a criar:** `src/lib/utils/steamUtils.ts`

**Tarefa para IA:**
- [ ] Criar funções utilitárias para Steam
- [ ] **COMENTAR**: Explicar conversões e formatações
- [ ] **DOCUMENTAR**: Casos de uso de cada função
- [ ] **Implementar:**
  - `formatSteamId()` - Formatação do Steam ID
  - `generateImageUrl()` - URLs de imagens Steam
  - `calculatePlaytime()` - Conversões de tempo de jogo
  - `validateSteamProfile()` - Validações específicas

#### ☐ 1.3 Cache Service
**Arquivo a criar:** `src/lib/services/cache.service.ts`

**Tarefa para IA:**
- [ ] Implementar sistema de cache em memória
- [ ] **COMENTAR**: Por que cache é necessário
- [ ] **DOCUMENTAR**: Estratégias de invalidação
- [ ] **Implementar:**
  - Cache de dados Steam (TTL: 1 hora)
  - Cache de verificações de posse (TTL: 30 min)
  - Limpeza automática de cache expirado

### 🛠️ 2. API ROUTES PRINCIPAIS

#### ☐ 2.1 API de Sincronização Steam
**Arquivo a criar:** `src/app/api/steam/sync/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint POST para sincronização
- [ ] **COMENTAR**: Fluxo completo de sincronização
- [ ] **DOCUMENTAR**: Tratamento de erros e casos extremos
- [ ] **Funcionalidades:**
  - Verificar autenticação
  - Buscar jogos da Steam API
  - Salvar no banco de dados
  - Atualizar status de conexão
  - Retornar estatísticas da sincronização

**Validações obrigatórias:**
- Usuário autenticado
- Steam conectado
- Rate limiting
- Validação de dados

#### ☐ 2.2 API de Verificação de Posse
**Arquivo a criar:** `src/app/api/verification/check/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint GET para verificação
- [ ] **COMENTAR**: Lógica de verificação de posse
- [ ] **DOCUMENTAR**: Parâmetros aceitos e retornos
- [ ] **Funcionalidades:**
  - Verificar se usuário possui jogo específico
  - Retornar tempo de jogo
  - Suporte a verificação de outros usuários
  - Cache de resultados

**Parâmetros aceitos:**
- `gameId` (obrigatório)
- `userId` (opcional, padrão: usuário atual)

#### ☐ 2.3 API de Status da Conexão
**Arquivo a criar:** `src/app/api/steam/status/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint GET para status
- [ ] **COMENTAR**: Informações retornadas
- [ ] **DOCUMENTAR**: Casos de uso no frontend
- [ ] **Retornar:**
  - Status da conexão Steam
  - Última sincronização
  - Número de jogos sincronizados
  - Erros da última sincronização

#### ☐ 2.4 API de Desconexão Steam
**Arquivo a criar:** `src/app/api/steam/disconnect/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint DELETE para desconexão
- [ ] **COMENTAR**: Processo de limpeza de dados
- [ ] **DOCUMENTAR**: Dados removidos vs mantidos
- [ ] **Funcionalidades:**
  - Remover conexão Steam
  - Manter histórico de jogos verificados
  - Atualizar status de verificação

### 📊 3. API ROUTES COMPLEMENTARES

#### ☐ 3.1 API de Jogos Verificados
**Arquivo a criar:** `src/app/api/steam/games/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint GET para listar jogos
- [ ] **COMENTAR**: Filtros e paginação disponíveis
- [ ] **DOCUMENTAR**: Estrutura de resposta
- [ ] **Funcionalidades:**
  - Listar jogos verificados do usuário
  - Filtros por nome, tempo de jogo, etc.
  - Paginação
  - Ordenação por diferentes critérios

#### ☐ 3.2 API de Estatísticas
**Arquivo a criar:** `src/app/api/steam/stats/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint GET para estatísticas
- [ ] **COMENTAR**: Métricas calculadas
- [ ] **DOCUMENTAR**: Uso no dashboard
- [ ] **Retornar:**
  - Total de jogos possuídos
  - Total de horas jogadas
  - Jogos mais jogados
  - Estatísticas de verificação

#### ☐ 3.3 API de Sincronização Manual
**Arquivo a criar:** `src/app/api/steam/sync/manual/route.ts`

**Tarefa para IA:**
- [ ] Implementar endpoint POST para sync forçada
- [ ] **COMENTAR**: Diferença da sincronização normal
- [ ] **DOCUMENTAR**: Limitações e casos de uso
- [ ] **Funcionalidades:**
  - Força nova sincronização
  - Ignora cache
  - Rate limiting mais restritivo

### 🔒 4. MIDDLEWARE E SEGURANÇA

#### ☐ 4.1 Middleware de Autenticação Steam
**Arquivo a criar:** `src/lib/middleware/steamAuth.ts`

**Tarefa para IA:**
- [ ] Criar middleware para validar conexão Steam
- [ ] **COMENTAR**: Verificações realizadas
- [ ] **DOCUMENTAR**: Como usar em API routes
- [ ] **Validações:**
  - Usuário autenticado
  - Steam conectado
  - Steam ID válido
  - Conexão ativa

#### ☐ 4.2 Middleware de Rate Limiting Avançado
**Arquivo a criar:** `src/lib/middleware/rateLimitAdvanced.ts`

**Tarefa para IA:**
- [ ] Implementar rate limiting por endpoint
- [ ] **COMENTAR**: Limites específicos por operação
- [ ] **DOCUMENTAR**: Configurações recomendadas
- [ ] **Configurações:**
  - Sync: 1 por minuto
  - Verificação: 30 por minuto
  - Status: 60 por minuto

#### ☐ 4.3 Middleware de Tratamento de Erros
**Arquivo a criar:** `src/lib/middleware/errorHandler.ts`

**Tarefa para IA:**
- [ ] Criar handler global de erros
- [ ] **COMENTAR**: Tipos de erro tratados
- [ ] **DOCUMENTAR**: Estrutura de resposta de erro
- [ ] **Tratar:**
  - Erros da Steam API
  - Erros de validação
  - Erros de banco de dados
  - Erros de autenticação

### 🧪 5. TESTES DE API

#### ☐ 5.1 Testes do Steam Service
**Arquivo a criar:** `src/__tests__/services/steam.service.test.ts`

**Tarefa para IA:**
- [ ] Criar testes unitários para SteamService
- [ ] **COMENTAR**: Cenários testados
- [ ] **DOCUMENTAR**: Como executar testes
- [ ] **Testar:**
  - Busca de jogos com perfil público
  - Tratamento de perfil privado
  - Normalização de dados
  - Tratamento de erros da API

#### ☐ 5.2 Testes de API Routes
**Arquivo a criar:** `src/__tests__/api/steam.test.ts`

**Tarefa para IA:**
- [ ] Criar testes de integração para endpoints
- [ ] **COMENTAR**: Setup de mocks necessários
- [ ] **DOCUMENTAR**: Dados de teste utilizados
- [ ] **Testar:**
  - Endpoints com autenticação
  - Validação de parâmetros
  - Respostas de erro
  - Rate limiting

#### ☐ 5.3 Testes de Middleware
**Arquivo a criar:** `src/__tests__/middleware/steamAuth.test.ts`

**Tarefa para IA:**
- [ ] Testar middleware de autenticação
- [ ] **COMENTAR**: Cenários de sucesso e falha
- [ ] **DOCUMENTAR**: Como simular diferentes estados
- [ ] **Testar:**
  - Usuário autenticado com Steam
  - Usuário sem Steam conectado
  - Steam ID inválido
  - Conexão expirada

### 📝 6. DOCUMENTAÇÃO TÉCNICA

#### ☐ 6.1 Documentação das APIs
**Arquivo a criar:** `src/docs/steam-api-reference.md`

**Tarefa para IA:**
- [ ] Documentar todos os endpoints criados
- [ ] **COMENTAR**: Exemplos de uso para cada endpoint
- [ ] **DOCUMENTAR**: Códigos de erro possíveis
- [ ] **Incluir:**
  - Parâmetros de entrada
  - Estrutura de resposta
  - Códigos de status HTTP
  - Exemplos de curl/fetch

#### ☐ 6.2 Guia de Troubleshooting
**Arquivo a criar:** `src/docs/steam-troubleshooting.md`

**Tarefa para IA:**
- [ ] Criar guia de solução de problemas
- [ ] **COMENTAR**: Problemas comuns e soluções
- [ ] **DOCUMENTAR**: Como diagnosticar issues
- [ ] **Incluir:**
  - Perfis privados
  - Erros da Steam API
  - Problemas de conectividade
  - Rate limiting

### 🔍 7. MONITORAMENTO E LOGS

#### ☐ 7.1 Sistema de Logs Estruturados
**Arquivo a criar:** `src/lib/monitoring/logger.ts`

**Tarefa para IA:**
- [ ] Implementar logger estruturado
- [ ] **COMENTAR**: Níveis de log utilizados
- [ ] **DOCUMENTAR**: Como adicionar logs em novos endpoints
- [ ] **Implementar:**
  - Logs de requisições Steam API
  - Logs de sincronização
  - Logs de erros
  - Métricas de performance

#### ☐ 7.2 Health Check Endpoint
**Arquivo a criar:** `src/app/api/health/steam/route.ts`

**Tarefa para IA:**
- [ ] Criar endpoint de health check
- [ ] **COMENTAR**: Verificações realizadas
- [ ] **DOCUMENTAR**: Como interpretar resultados
- [ ] **Verificar:**
  - Conectividade com Steam API
  - Status do banco de dados
  - Cache funcionando
  - Rate limits não atingidos

---

## ✅ CRITÉRIOS DE CONCLUSÃO DA FASE 2

### Verificações Obrigatórias:

- [ ] ✅ SteamService implementado e testado
- [ ] ✅ Todos os endpoints principais funcionando
- [ ] ✅ Middleware de segurança implementado
- [ ] ✅ Sistema de cache operacional
- [ ] ✅ Tratamento de erros funcionando
- [ ] ✅ Testes de API passando
- [ ] ✅ Documentação técnica completa
- [ ] ✅ Sistema de logs implementado
- [ ] ✅ Health checks funcionando

### Testes de Validação:

1. **Teste de Sincronização:**
   - [ ] POST `/api/steam/sync` funciona
   - [ ] Dados são salvos no banco
   - [ ] Perfis privados são tratados
   - [ ] Rate limiting funciona

2. **Teste de Verificação:**
   - [ ] GET `/api/verification/check` funciona
   - [ ] Retorna dados corretos
   - [ ] Cache funciona adequadamente
   - [ ] Suporta verificação de outros usuários

3. **Teste de Segurança:**
   - [ ] Middleware de auth funciona
   - [ ] Rate limiting está ativo
   - [ ] Erros são tratados adequadamente
   - [ ] Logs são gerados

4. **Teste de Performance:**
   - [ ] Endpoints respondem em < 2s
   - [ ] Cache reduz tempo de resposta
   - [ ] Rate limits são respeitados
   - [ ] Não há vazamentos de memória

---

## 🚀 PRÓXIMOS PASSOS

Após completar a Fase 2, prossiga para:
**STEAM_IMPLEMENTATION_CHECKLIST_FASE_3.md** - Frontend e Interface

### O que você terá ao final da Fase 2:
- ✅ Backend completo para Steam API
- ✅ Endpoints seguros e documentados
- ✅ Sistema de cache e monitoramento
- ✅ Testes de integração funcionando
- ✅ Logs e troubleshooting

### O que vem na Fase 3:
- 🔄 Componentes React para Steam
- 🔄 Hooks personalizados
- 🔄 Interface de conexões
- 🔄 Badge de verificação
- 🔄 Dashboard de estatísticas 