# Complete Banner Implementation Guide

**Purpose**: Step-by-step guide for implementing a new banner system with dashboard control, user page display, and analytics tracking.

**Based on**: Sponsor Banner implementation (160625-SponsorBannerImplementation001-004.md)

**Target**: AI assistant implementation reference

---

## Overview

This guide provides a complete blueprint for implementing a banner system that includes:
- Dashboard configuration interface
- User profile page display
- Analytics tracking with detailed metrics
- Database schema with RLS security
- TypeScript type definitions

## Step 1: Database Schema Implementation

### 1.1 Create Main Banner Table

Create migration file: `src/lib/supabase/migrations/YYYYMMDD_[banner_name]_banners.sql`

```sql
-- Main banner table
CREATE TABLE IF NOT EXISTS [banner_name]_banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  img_url TEXT NOT NULL,
  url TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  impression_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  last_impression_at TIMESTAMP WITH TIME ZONE,
  last_click_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_[banner_name]_banners_user_id ON [banner_name]_banners(user_id);

-- Enable RLS
ALTER TABLE [banner_name]_banners ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY [banner_name]_banners_select ON [banner_name]_banners
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY [banner_name]_banners_insert ON [banner_name]_banners
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY [banner_name]_banners_update ON [banner_name]_banners
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY [banner_name]_banners_delete ON [banner_name]_banners
  FOR DELETE USING (auth.uid() = user_id);
```

### 1.2 Create Analytics Table

```sql
-- Detailed analytics tracking table
CREATE TABLE IF NOT EXISTS [banner_name]_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  banner_id UUID NOT NULL REFERENCES [banner_name]_banners(id) ON DELETE CASCADE,
  event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('impression', 'click')),
  user_agent TEXT,
  ip_address INET,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_[banner_name]_analytics_banner_id ON [banner_name]_analytics(banner_id);
CREATE INDEX IF NOT EXISTS idx_[banner_name]_analytics_event_type ON [banner_name]_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_[banner_name]_analytics_created_at ON [banner_name]_analytics(created_at);

-- Enable RLS
ALTER TABLE [banner_name]_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY [banner_name]_analytics_select ON [banner_name]_analytics
  FOR SELECT USING (
    banner_id IN (
      SELECT id FROM [banner_name]_banners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY [banner_name]_analytics_insert ON [banner_name]_analytics
  FOR INSERT WITH CHECK (true);
```

### 1.3 Create Tracking Functions

```sql
-- Impression tracking function
CREATE OR REPLACE FUNCTION increment_[banner_name]_impression(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  UPDATE [banner_name]_banners
  SET 
    impression_count = impression_count + 1,
    last_impression_at = NOW()
  WHERE id = banner_id;
  
  INSERT INTO [banner_name]_analytics (
    banner_id, event_type, user_agent, ip_address, referrer
  ) VALUES (
    banner_id, 'impression', user_agent_param, ip_address_param, referrer_param
  );
END;
$$ LANGUAGE plpgsql;

-- Click tracking function
CREATE OR REPLACE FUNCTION increment_[banner_name]_click(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  UPDATE [banner_name]_banners
  SET 
    click_count = click_count + 1,
    last_click_at = NOW()
  WHERE id = banner_id;
  
  INSERT INTO [banner_name]_analytics (
    banner_id, event_type, user_agent, ip_address, referrer
  ) VALUES (
    banner_id, 'click', user_agent_param, ip_address_param, referrer_param
  );
END;
$$ LANGUAGE plpgsql;
```

### 1.4 Create Analytics Functions

```sql
-- Summary analytics function
CREATE OR REPLACE FUNCTION get_[banner_name]_summary(banner_id UUID)
RETURNS TABLE(
  total_impressions BIGINT,
  total_clicks BIGINT,
  ctr NUMERIC,
  last_impression TIMESTAMP WITH TIME ZONE,
  last_click TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  days_active INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.impression_count::BIGINT,
    b.click_count::BIGINT,
    CASE 
      WHEN b.impression_count > 0 
      THEN ROUND((b.click_count::NUMERIC / b.impression_count::NUMERIC) * 100, 2)
      ELSE 0
    END,
    b.last_impression_at,
    b.last_click_at,
    b.created_at,
    EXTRACT(DAY FROM NOW() - b.created_at)::INTEGER
  FROM [banner_name]_banners b
  WHERE b.id = get_[banner_name]_summary.banner_id;
END;
$$ LANGUAGE plpgsql;

-- Daily stats function
CREATE OR REPLACE FUNCTION get_[banner_name]_daily_stats(
  banner_id UUID,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE(
  date DATE,
  impressions BIGINT,
  clicks BIGINT,
  ctr NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(a.created_at),
    COUNT(CASE WHEN a.event_type = 'impression' THEN 1 END),
    COUNT(CASE WHEN a.event_type = 'click' THEN 1 END),
    CASE 
      WHEN COUNT(CASE WHEN a.event_type = 'impression' THEN 1 END) > 0 
      THEN ROUND(
        (COUNT(CASE WHEN a.event_type = 'click' THEN 1 END)::NUMERIC / 
         COUNT(CASE WHEN a.event_type = 'impression' THEN 1 END)::NUMERIC) * 100, 
        2
      )
      ELSE 0
    END
  FROM [banner_name]_analytics a
  WHERE a.banner_id = get_[banner_name]_daily_stats.banner_id
    AND a.created_at >= NOW() - INTERVAL '1 day' * days_back
  GROUP BY DATE(a.created_at)
  ORDER BY date DESC;
END;
$$ LANGUAGE plpgsql;
```

## Step 2: TypeScript Type Definitions

### 2.1 Update Supabase Types

Add to `src/lib/supabase/types.ts` in the Tables section:

```typescript
[banner_name]_banners: {
  Row: {
    id: string
    user_id: string
    img_url: string
    url: string
    is_active: boolean
    impression_count: number
    click_count: number
    last_impression_at: string | null
    last_click_at: string | null
    created_at: string
    updated_at: string
  }
  Insert: {
    id?: string
    user_id: string
    img_url: string
    url: string
    is_active?: boolean
    impression_count?: number
    click_count?: number
    last_impression_at?: string | null
    last_click_at?: string | null
    created_at?: string
    updated_at?: string
  }
  Update: {
    id?: string
    user_id?: string
    img_url?: string
    url?: string
    is_active?: boolean
    impression_count?: number
    click_count?: number
    last_impression_at?: string | null
    last_click_at?: string | null
    created_at?: string
    updated_at?: string
  }
  Relationships: [
    {
      foreignKeyName: "[banner_name]_banners_user_id_fkey"
      columns: ["user_id"]
      referencedRelation: "users"
      referencedColumns: ["id"]
    }
  ]
},
[banner_name]_analytics: {
  Row: {
    id: string
    banner_id: string
    event_type: string
    user_agent: string | null
    ip_address: string | null
    referrer: string | null
    created_at: string
  }
  Insert: {
    id?: string
    banner_id: string
    event_type: string
    user_agent?: string | null
    ip_address?: string | null
    referrer?: string | null
    created_at?: string
  }
  Update: {
    id?: string
    banner_id?: string
    event_type?: string
    user_agent?: string | null
    ip_address?: string | null
    referrer?: string | null
    created_at?: string
  }
  Relationships: [
    {
      foreignKeyName: "[banner_name]_analytics_banner_id_fkey"
      columns: ["banner_id"]
      referencedRelation: "[banner_name]_banners"
      referencedColumns: ["id"]
    }
  ]
},
```

## Step 3: Service Layer Implementation

### 3.1 Create Banner Service

Create `src/lib/services/[bannerName]Service.ts`:

```typescript
import { createClient } from '@/lib/supabase/client';

interface BannerData {
  id?: string;
  user_id: string;
  img_url: string;
  url: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export async function getUser[BannerName](userId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('[banner_name]_banners')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    console.error('Error fetching [banner_name]:', error);
    return null;
  }

  return data;
}

export async function save[BannerName]({
  userId,
  imgUrl,
  url,
  isActive = true,
}: {
  userId: string;
  imgUrl: string;
  url: string;
  isActive?: boolean;
}) {
  const supabase = createClient();
  const existing = await getUser[BannerName](userId);

  let response;

  if (existing) {
    response = await supabase
      .from('[banner_name]_banners')
      .update({
        img_url: imgUrl,
        url: url,
        is_active: isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', existing.id)
      .select();
  } else {
    response = await supabase
      .from('[banner_name]_banners')
      .insert({
        user_id: userId,
        img_url: imgUrl,
        url: url,
        is_active: isActive,
      })
      .select();
  }

  const { data, error } = response;

  if (error) {
    console.error('Error saving [banner_name]:', error);
    throw new Error(error.message);
  }

  return data[0];
}

export async function deactivate[BannerName](userId: string) {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('[banner_name]_banners')
    .update({ 
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)
    .select();

  if (error) {
    console.error('Error deactivating [banner_name]:', error);
    throw new Error(error.message);
  }

  return data[0];
}

export async function track[BannerName]Impression(
  bannerId: string, 
  userAgent?: string, 
  referrer?: string
) {
  const supabase = createClient();
  
  const { data, error } = await supabase.rpc('increment_[banner_name]_impression', {
    banner_id: bannerId,
    user_agent_param: userAgent || null,
    ip_address_param: null,
    referrer_param: referrer || null
  });
  
  if (error) {
    console.error('Error tracking impression:', error);
    return false;
  }
  
  return true;
}

export async function track[BannerName]Click(
  bannerId: string, 
  userAgent?: string, 
  referrer?: string
) {
  const supabase = createClient();
  
  const { data, error } = await supabase.rpc('increment_[banner_name]_click', {
    banner_id: bannerId,
    user_agent_param: userAgent || null,
    ip_address_param: null,
    referrer_param: referrer || null
  });
  
  if (error) {
    console.error('Error tracking click:', error);
    return false;
  }
  
  return true;
}
```
