/* src/components/style/lexicaltoolbar.css - Responsive Toolbar */

/* Only target elements inside .lexical-editor-container */
.lexical-editor-container .lexical-toolbar {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    gap: 6px;
    flex-wrap: wrap;
    min-height: 44px;
    position: relative;
    z-index: 10;
    flex-shrink: 0;
    flex-grow: 0;
    order: -1;
  }
  
  /* Loading state styling */
  .lexical-editor-container .lexical-toolbar div.toolbar-button {
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    background: rgba(30, 41, 59, 0.3);
    font-size: 12px;
    font-weight: 500;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.4);
    user-select: none;
    backdrop-filter: blur(8px);
    opacity: 0.6;
  }
  
  .lexical-editor-container .toolbar-category {
    display: flex;
    align-items: center;
  }
  
  .lexical-editor-container .toolbar-group {
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 2px;
    background: transparent;
    border-radius: 8px;
    border: none;
  }

  .lexical-editor-container .toolbar-button {
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    background: rgba(30, 41, 59, 0.3);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    user-select: none;
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, monospace;
    white-space: nowrap;
  }
  
  .lexical-editor-container .toolbar-button::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .lexical-editor-container .toolbar-button:hover:not(.disabled) {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(6, 182, 212, 0.15));
    border-color: rgba(139, 92, 246, 0.3);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
  }
  
  .lexical-editor-container .toolbar-button:hover:not(.disabled)::before {
    opacity: 1;
  }
  
  .lexical-editor-container .toolbar-button.active {
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    color: white;
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }
  
  .lexical-editor-container .toolbar-button.active::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 1;
  }
  
  .lexical-editor-container .toolbar-button.disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: rgba(15, 23, 42, 0.5);
    color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.02);
  }
  
  .lexical-editor-container .toolbar-button.disabled:hover {
    transform: none;
    box-shadow: none;
  }
  
  .lexical-editor-container .toolbar-separator {
    width: 1px;
    height: 24px;
    background: rgba(139, 92, 246, 0.15);
    margin: 0 8px;
    flex-shrink: 0;
    position: relative;
  }

  /* Toolbar Icon Styling */
  .lexical-editor-container .toolbar-icon {
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
  }
  
  /* Special styling for specific buttons */
  .lexical-editor-container .toolbar-button strong {
    font-weight: 700;
  }
  
  .lexical-editor-container .toolbar-button em {
    font-style: italic;
  }
  
  .lexical-editor-container .toolbar-button u {
    text-decoration: underline;
  }
  
  .lexical-editor-container .toolbar-button s {
    text-decoration: line-through;
  }
  
  /* Hover animations */
  @keyframes toolbar-pulse {
    0%, 100% { 
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); 
    }
    50% { 
      box-shadow: 0 0 25px rgba(139, 92, 246, 0.5); 
    }
  }
  
  .lexical-editor-container .toolbar-button.active {
    animation: toolbar-pulse 2s ease-in-out infinite;
  }

  /* Premium Toolbar Styling - Using Site Colors */
  .lexical-editor-container .lexical-toolbar.premium-toolbar {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.03), rgba(6, 182, 212, 0.02));
    border: 1px solid rgba(139, 92, 246, 0.08);
    position: relative;
  }

  /* Premium Feature Buttons */
  .lexical-editor-container .toolbar-button.premium-feature {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(6, 182, 212, 0.04));
    border-color: rgba(139, 92, 246, 0.15);
    color: #a78bfa;
    position: relative;
  }

  .lexical-editor-container .toolbar-button.premium-feature::after {
    content: '';
    position: absolute;
    top: -1px;
    right: -1px;
    width: 4px;
    height: 4px;
    background: #8b5cf6;
    border-radius: 50%;
    box-shadow: 0 0 4px #8b5cf6;
    animation: premium-glow 3s ease-in-out infinite;
  }

  @keyframes premium-glow {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }

  .lexical-editor-container .toolbar-button.premium-feature:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(6, 182, 212, 0.08));
    border-color: rgba(139, 92, 246, 0.3);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
  }

  .lexical-editor-container .toolbar-button.premium-feature.premium-active {
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    color: #ffffff;
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Premium Group Styling */
  .lexical-editor-container .toolbar-group.premium-group {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.03), rgba(6, 182, 212, 0.02));
    border: 1px solid rgba(139, 92, 246, 0.08);
    border-radius: 6px;
    padding: 3px;
  }

  /* Enhanced H3 for Premium */
  .lexical-editor-container .toolbar-button[title*="Heading 3"] {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.06), rgba(6, 182, 212, 0.03));
    border-color: rgba(139, 92, 246, 0.12);
    color: #a78bfa;
  }

  .lexical-editor-container .toolbar-button[title*="Heading 3"]:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.12), rgba(6, 182, 212, 0.06));
    border-color: rgba(139, 92, 246, 0.25);
    color: #ffffff;
  }

  /* Responsive Design for Different Container Widths */

  /* 75% width containers - compact mode */
  @container (max-width: 800px) {
    .lexical-editor-container .lexical-toolbar {
      gap: 4px;
      padding: 6px 10px;
    }

    .lexical-editor-container .toolbar-button {
      padding: 5px 8px;
      min-width: 28px;
      min-height: 28px;
      font-size: 11px;
    }

    .lexical-editor-container .toolbar-icon {
      font-size: 10px;
    }

    .lexical-editor-container .toolbar-separator {
      margin: 0 6px;
      height: 20px;
    }

    .lexical-editor-container .toolbar-group {
      gap: 2px;
    }
  }

  /* 50% width containers - ultra compact mode */
  @container (max-width: 600px) {
    .lexical-editor-container .lexical-toolbar {
      gap: 3px;
      padding: 4px 8px;
      flex-wrap: wrap;
    }

    .lexical-editor-container .toolbar-button {
      padding: 4px 6px;
      min-width: 24px;
      min-height: 24px;
      font-size: 10px;
    }

    .lexical-editor-container .toolbar-icon {
      font-size: 9px;
    }

    .lexical-editor-container .toolbar-separator {
      margin: 0 4px;
      height: 16px;
    }

    .lexical-editor-container .toolbar-group {
      gap: 1px;
    }

    /* Hide some separators in ultra compact mode */
    .lexical-editor-container .toolbar-separator:nth-of-type(n+4) {
      display: none;
    }
  }

  /* Mobile fallback */
  @media (max-width: 480px) {
    .lexical-editor-container .lexical-toolbar {
      gap: 2px;
      padding: 4px 6px;
    }

    .lexical-editor-container .toolbar-button {
      padding: 3px 5px;
      min-width: 22px;
      min-height: 22px;
      font-size: 9px;
    }

    .lexical-editor-container .toolbar-icon {
      font-size: 8px;
    }
  }