# Sistema de Módulos de Conteúdo - CriticalPixel

Um sistema abrangente para exibir conteúdo gerado pelo usuário nas páginas de perfil, servindo como "cartão de visita e portfólio" para criadores na plataforma CriticalPixel.

## 📋 Visão Geral

Este sistema permite que usuários exibam seus reviews, surveys de performance, achievements, galeria de mídia e atividades recentes de forma modular e personalizável. É projetado para futuras integrações com o dashboard, onde usuários poderão ter controle granular sobre seus módulos.

## 🏗️ Arquitetura

### Componentes Principais

#### 1. **UserContentModules** (`/src/components/userprofile/UserContentModules.tsx`)
- Componente principal que renderiza os módulos de conteúdo
- Sistema de tabs para navegação entre Reviews, Surveys e Atividades
- Estatísticas gamificadas em tempo real
- Suporte a filtros e diferentes modos de visualização

**Features:**
- ✅ Navegação por tabs interativa
- ✅ Estatísticas gamificadas (Reviews, Surveys, Views, Likes)
- ✅ Grid responsivo para reviews e surveys
- ✅ Lista de atividades recentes
- ✅ Estados de loading e empty states
- ✅ Integração com sistema de temas

#### 2. **EnhancedContentDisplay** (`/src/components/userprofile/EnhancedContentDisplay.tsx`)
- Seção premium para conteúdo destacado
- Efeitos visuais avançados usando partículas animadas
- Review em destaque com border animada
- Showcase de achievements com raridades
- Galeria de mídia interativa com modal

**Features:**
- ✅ Review destacada com efeitos especiais
- ✅ Sistema de achievements com raridades (common, rare, epic, legendary)
- ✅ Galeria de mídia com preview e modal fullscreen
- ✅ Partículas animadas para conteúdo premium
- ✅ Bordas gradient animadas
- ✅ Controles de mídia (mute/unmute, fullscreen)

#### 3. **ContentModulesConfig** (`/src/components/userprofile/ContentModulesConfig.tsx`)
- Interface de configuração para personalização dos módulos
- Controle de visibilidade e ordenação
- Configurações específicas por módulo
- Preparado para integração futura com dashboard

**Features:**
- ✅ Toggle de ativação/desativação por módulo
- ✅ Controle de visibilidade (público, amigos, privado)
- ✅ Reordenação com setas up/down
- ✅ Configurações específicas (máximo de itens, interações)
- ✅ Detecção de mudanças não salvas
- ✅ Restauração de configurações padrão

#### 4. **MagicUIIntegration** (`/src/components/userprofile/MagicUIIntegration.tsx`)
- Biblioteca de efeitos visuais avançados
- Componentes reutilizáveis para toda a aplicação
- Baseado em Framer Motion para animações suaves

**Componentes inclusos:**
- `FloatingParticles` - Partículas flutuantes para backgrounds
- `AnimatedBorder` - Bordas gradient animadas
- `MagicCursor` - Efeito de brilho que segue o cursor
- `ShimmerText` - Texto com efeito shimmer no hover
- `AnimatedGridPattern` - Padrão de grid animado
- `AnimatedCounter` - Números animados com spring
- `OrbitingIcon` - Ícones em órbita
- `AnimatedProgress` - Progresso circular animado
- `ConfettiEffect` - Confetti para celebrar achievements
- `MagicContainer` - Wrapper que combina múltiplos efeitos

### Tipos TypeScript

#### **UserContent Types** (`/src/types/user-content.ts`)
Definições completas para todos os dados de conteúdo:

```typescript
interface UserReview {
  id: string;
  user_id: string;
  game_name: string;
  rating: number;
  review_text: string;
  likes_count: number;
  views_count: number;
  is_featured: boolean;
  // ... outros campos
}

interface UserSurvey {
  id: string;
  user_id: string;
  game_name: string;
  performance_score: number;
  fps_average: number;
  resolution: string;
  // ... outros campos
}

// + UserActivity, UserAchievement, UserMedia, UserStats, etc.
```

### Server Actions

#### **Content Actions** (`/src/app/u/actions-content.ts`)
Server actions para buscar e gerenciar dados:

```typescript
// Buscar dados do usuário
getUserReviews(userId: string, filters?: ContentFilters)
getUserSurveys(userId: string, filters?: ContentFilters)
getUserActivities(userId: string, limit?: number)
getUserAchievements(userId: string)
getUserMedia(userId: string, filters?: ContentFilters)
getUserStats(userId: string)

// Interações
incrementContentViews(contentType, contentId)
toggleContentLike(contentType, contentId, userId)

// Utilitários
getUserContentData(userId: string) // Busca todos os dados de uma vez
getFeaturedReview(userId: string)
```

### Hooks Personalizados

#### **useUserContent** (`/src/hooks/useUserContent.ts`)
Hook principal para gerenciamento de estado e dados:

```typescript
const {
  data,                    // Todos os dados do usuário
  featuredReview,          // Review em destaque
  isLoading,               // Estado de carregamento
  error,                   // Erros
  refetch,                 // Recarregar dados
  incrementViews,          // Incrementar visualizações
  toggleLike,              // Toggle like/unlike
  stats                    // Estatísticas computadas
} = useUserContent(userId, currentUserId);
```

**Features:**
- ✅ Cache inteligente (5 minutos)
- ✅ Atualizações otimistas
- ✅ Gerenciamento de estados de loading/error
- ✅ Estatísticas computadas em tempo real
- ✅ Debounce para chamadas de API

## 🎨 Design System

### Temas
- **6 temas pré-definidos**: Cosmic, Ocean, Forest, Crimson, Silver, Amber
- **Cores dinâmicas**: Primary, Secondary, Accent extraídas do ThemeManager
- **Consistência**: Todos os componentes respeitam o tema ativo

### Estilo Visual
- **Dark Theme**: Fundo escuro com elementos em glassmorphism
- **Typography**: Font mono para títulos com brackets `</>` temáticos
- **Cards**: Borders sutis, backdrop blur, hover effects
- **Animações**: Framer Motion para transições suaves
- **Iconografia**: Lucide React com cores temáticas

## 🚀 Integração

### Na Página de Perfil
```tsx
// ProfilePageClient.tsx
import UserContentModules from '@/components/userprofile/UserContentModules';
import EnhancedContentDisplay from '@/components/userprofile/EnhancedContentDisplay';

// Dados de exemplo (substituir por dados reais)
const featuredReview = { /* dados da review destacada */ };
const achievements = [ /* achievements do usuário */ ];
const media = [ /* galeria de mídia */ ];
const theme = ThemeManager.getTheme(profileData.theme);

return (
  <div>
    {/* Conteúdo Destacado - Seção Premium */}
    <EnhancedContentDisplay
      featuredReview={featuredReview}
      achievements={achievements}
      media={media}
      theme={theme}
    />

    {/* Módulos de Conteúdo Principal */}
    <UserContentModules
      userId={profileData.id}
      currentUserId={currentUserId}
      isOwnProfile={isOwnProfile}
      theme={theme}
    />
  </div>
);
```

### Com Hook Personalizado
```tsx
import { useUserContent } from '@/hooks/useUserContent';

function MyComponent({ userId, currentUserId }) {
  const { 
    data, 
    isLoading, 
    error, 
    incrementViews, 
    toggleLike 
  } = useUserContent(userId, currentUserId);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div>
      {data.reviews.map(review => (
        <ReviewCard 
          key={review.id}
          review={review}
          onView={() => incrementViews('review', review.id)}
          onLike={() => toggleLike('review', review.id)}
        />
      ))}
    </div>
  );
}
```

## 📊 Estrutura de Dados

### Database Schema (Sugerido)

```sql
-- Tabela de reviews
CREATE TABLE user_reviews (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  game_name VARCHAR(255) NOT NULL,
  game_image VARCHAR(255),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  likes_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_public BOOLEAN DEFAULT TRUE,
  platform VARCHAR(50),
  tags TEXT[], -- Array de tags
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de surveys
CREATE TABLE user_surveys (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  game_name VARCHAR(255) NOT NULL,
  performance_score INTEGER,
  fps_average INTEGER,
  resolution VARCHAR(50),
  graphics_settings VARCHAR(100),
  hardware_used TEXT,
  is_public BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de achievements
CREATE TABLE user_achievements (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  rarity VARCHAR(20) CHECK (rarity IN ('common', 'rare', 'epic', 'legendary', 'mythic')),
  category VARCHAR(50),
  points_value INTEGER DEFAULT 0,
  is_hidden BOOLEAN DEFAULT FALSE,
  unlocked_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de mídia
CREATE TABLE user_media (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  type VARCHAR(20) CHECK (type IN ('image', 'video', 'gif')),
  url VARCHAR(500) NOT NULL,
  title VARCHAR(255),
  game_name VARCHAR(255),
  likes_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de atividades
CREATE TABLE user_activities (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  related_id UUID, -- ID do conteúdo relacionado
  is_public BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabela de likes
CREATE TABLE content_likes (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  content_type VARCHAR(20) NOT NULL,
  content_id UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, content_type, content_id)
);
```

## 🔮 Roadmap Futuro

### Dashboard Integration
- [ ] **Configuração Avançada**: Interface completa no dashboard para configurar módulos
- [ ] **Analytics**: Estatísticas detalhadas de visualizações e engajamento
- [ ] **Content Management**: Upload e edição de conteúdo direto no dashboard
- [ ] **Moderação**: Ferramentas para moderação de conteúdo

### Features Avançadas
- [ ] **Real-time Updates**: WebSocket para atualizações em tempo real
- [ ] **Social Features**: Sistema de follows, notificações, feed
- [ ] **Gamificação**: Sistema de pontos, rankings, badges dinâmicos
- [ ] **API Pública**: Endpoints para desenvolvedores externos

### Performance
- [ ] **Infinite Scroll**: Para listas grandes de conteúdo
- [ ] **Image Optimization**: CDN e otimização automática de imagens
- [ ] **Caching**: Redis para cache de dados frequentes
- [ ] **PWA**: Progressive Web App features

### UX/UI
- [ ] **Drag & Drop**: Reordenação visual dos módulos
- [ ] **Themes Customizados**: Editor de temas personalizado
- [ ] **Accessibility**: Melhorias de acessibilidade (WCAG 2.1)
- [ ] **Mobile First**: Otimizações específicas para mobile

## 🧪 Testing

### Testes Recomendados
```typescript
// Exemplo de teste para UserContentModules
describe('UserContentModules', () => {
  it('should render stats correctly', () => {
    render(<UserContentModules userId="123" theme={mockTheme} />);
    expect(screen.getByText('Reviews')).toBeInTheDocument();
  });

  it('should handle tab switching', () => {
    render(<UserContentModules userId="123" theme={mockTheme} />);
    fireEvent.click(screen.getByText('Surveys'));
    expect(screen.getByText('Nenhum survey encontrado')).toBeInTheDocument();
  });
});
```

## 📝 Conclusão

O sistema de módulos de conteúdo da CriticalPixel oferece uma base sólida e extensível para exibição de conteúdo gerado pelo usuário. Com componentes modulares, tipos TypeScript robustos, e uma arquitetura preparada para crescimento, o sistema está pronto para evoluir conforme as necessidades da plataforma.

### Pontos Fortes
- ✅ **Modular**: Componentes independentes e reutilizáveis
- ✅ **Type-Safe**: TypeScript em toda a aplicação
- ✅ **Performance**: Cache inteligente e atualizações otimistas
- ✅ **UX**: Animações suaves e feedback visual
- ✅ **Extensível**: Preparado para futuras funcionalidades
- ✅ **Consistente**: Design system bem definido

### Próximos Passos
1. **Implementar APIs reais** substituindo dados mockados
2. **Adicionar testes unitários** para todos os componentes
3. **Integrar com dashboard** para configuração avançada
4. **Otimizar performance** com lazy loading e code splitting
5. **Melhorar acessibilidade** seguindo WCAG guidelines

---

**Desenvolvido para CriticalPixel** - Sistema de módulos de conteúdo para perfis de usuário  
**Versão**: 1.0.0  
**Data**: Janeiro 2025 