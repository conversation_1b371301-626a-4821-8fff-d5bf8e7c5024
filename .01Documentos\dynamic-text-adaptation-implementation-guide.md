# Dynamic Text Color Adaptation Implementation Guide

## Overview

This guide provides a comprehensive implementation pattern for dynamic text color adaptation based on background brightness changes. The system automatically adjusts text colors to maintain optimal readability when background brightness is modified via sliders, theme changes, or overlays.

## Architecture Components

### 1. Background Dimmer System
**Purpose**: Controls page-wide background brightness via CSS custom properties

**Core Hook**: `useBackgroundDimmer.ts`
```typescript
const STORAGE_KEY = 'background-dimmer-value';
const DEFAULT_DIMMER_VALUE = 50; // 50% = original, 0% = darkest, 100% = brightest

export const useBackgroundDimmer = () => {
  const [dimmerValue, setDimmerValue] = useState<number>(DEFAULT_DIMMER_VALUE);
  const [isLoaded, setIsLoaded] = useState(false);

  // Apply dimmer effect to document root
  useEffect(() => {
    if (!isLoaded) return;

    let darkOpacity = 0;
    let lightOpacity = 0;
    
    if (dimmerValue <= 50) {
      // 0-50%: Add dark overlay (0.8 max darkness at 0%)
      darkOpacity = (50 - dimmerValue) / 50 * 0.8;
    } else {
      // 50-100%: Add light overlay (0.9 max brightness at 100%)
      lightOpacity = (dimmerValue - 50) / 50 * 0.9;
    }
    
    // Set CSS custom properties
    document.documentElement.style.setProperty('--bg-dimmer-dark-opacity', darkOpacity.toString());
    document.documentElement.style.setProperty('--bg-dimmer-light-opacity', lightOpacity.toString());
  }, [dimmerValue, isLoaded]);

  return { dimmerValue, updateDimmerValue, isLoaded };
};
```

### 2. CSS Overlay System
**Purpose**: Applies visual overlays using CSS custom properties

**CSS Implementation** (`globals.css`):
```css
:root {
  --bg-dimmer-dark-opacity: 0;
  --bg-dimmer-light-opacity: 0;
}

body::before {
  content: '';
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 0, 0, var(--bg-dimmer-dark-opacity, 0));
  pointer-events: none;
  z-index: 1;
}

body::after {
  content: '';
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(255, 255, 255, var(--bg-dimmer-light-opacity, 0));
  pointer-events: none;
  z-index: 1;
}

/* Ensure content is above overlays */
main, nav, footer {
  position: relative;
  z-index: 2;
}
```

### 3. Background Brightness Detection
**Purpose**: Monitors background changes and determines optimal text color

**Core Hook**: `useBackgroundBrightness.ts`
```typescript
const useBackgroundBrightness = () => {
  const [isDarkBackground, setIsDarkBackground] = useState(true);

  useEffect(() => {
    const detectBackgroundBrightness = () => {
      try {
        // Priority 1: Check dimmer overlay values
        const rootStyles = getComputedStyle(document.documentElement);
        const darkOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-dark-opacity') || '0');
        const lightOpacity = parseFloat(rootStyles.getPropertyValue('--bg-dimmer-light-opacity') || '0');

        if (darkOpacity > 0) {
          setIsDarkBackground(true);
          return;
        } else if (lightOpacity > 0.3) {
          setIsDarkBackground(false);
          return;
        }

        // Priority 2: Fallback to computed background color detection
        const elements = [
          document.body,
          document.documentElement,
          document.querySelector('.page-main-container'),
          document.querySelector('main')
        ].filter(Boolean);

        for (const element of elements) {
          const computedStyle = window.getComputedStyle(element as Element);
          const backgroundColor = computedStyle.backgroundColor;

          if (backgroundColor === 'transparent' || backgroundColor === 'rgba(0, 0, 0, 0)') {
            continue;
          }

          const rgbMatch = backgroundColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
          if (rgbMatch) {
            const [, r, g, b] = rgbMatch.map(Number);
            // Standard luminance formula
            const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
            setIsDarkBackground(luminance < 0.5);
            return;
          }
        }

        setIsDarkBackground(true);
      } catch (error) {
        console.warn('Background brightness detection failed:', error);
        setIsDarkBackground(true);
      }
    };

    // Initial detection
    detectBackgroundBrightness();

    // Monitor CSS custom property changes
    const observer = new MutationObserver(detectBackgroundBrightness);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['style'],
      subtree: false
    });

    // Monitor body changes
    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['style', 'class'],
      subtree: true
    });

    // Theme change detection
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', detectBackgroundBrightness);

    // Polling backup for CSS variable changes
    const interval = setInterval(detectBackgroundBrightness, 100);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', detectBackgroundBrightness);
      clearInterval(interval);
    };
  }, []);

  return isDarkBackground;
};
```

### 4. Adaptive CSS Classes
**Purpose**: Defines visual styles for different background states

**CSS Classes**:
```css
/* Base adaptive text title */
.adaptive-text-title {
  font-weight: 700;
  letter-spacing: -0.02em;
  transition: all 0.3s ease;
  
  /* Default: white text for dark backgrounds */
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

.adaptive-text-title.light-background {
  color: #1e293b;
  text-shadow: 
    1px 1px 0px rgba(255, 255, 255, 0.8),
    2px 2px 4px rgba(255, 255, 255, 0.6);
}

.adaptive-text-title.dark-background {
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

/* Base adaptive subtitle */
.adaptive-text-subtitle {
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  
  /* Default: light gray for dark backgrounds */
  color: #94a3b8;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
}

.adaptive-text-subtitle.light-background {
  color: #475569;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.4);
}

.adaptive-text-subtitle.dark-background {
  color: #94a3b8;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
}

/* Adaptive content text */
.adaptive-text-content {
  transition: all 0.3s ease;
  color: #e2e8f0;
}

.adaptive-text-content.light-background {
  color: #334155;
}

.adaptive-text-content.dark-background {
  color: #e2e8f0;
}
```

### 5. UI Slider Component
**Purpose**: Provides user interface for brightness control

**Component**: `BackgroundDimmerSlider.tsx`
```typescript
const BackgroundDimmerSlider = ({ className = '' }: { className?: string }) => {
  const { dimmerValue, updateDimmerValue, isLoaded } = useBackgroundDimmer();

  if (!isLoaded) return null;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Moon className="w-4 h-4 text-slate-400" />
      
      <div className="relative w-20 h-2">
        <input
          type="range"
          min="0"
          max="100"
          value={dimmerValue}
          onChange={(e) => updateDimmerValue(parseInt(e.target.value, 10))}
          className="dimmer-slider w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>
      
      <Sun className="w-4 h-4 text-slate-300" />
    </div>
  );
};
```

## Implementation Steps

### Step 1: Setup Core Hooks
1. Create `useBackgroundDimmer.ts` hook
2. Create `useBackgroundBrightness.ts` hook
3. Ensure localStorage persistence for user preferences

### Step 2: Implement CSS Foundation
1. Add CSS custom properties to `:root`
2. Implement overlay system with `::before` and `::after` pseudo-elements
3. Set proper z-index hierarchy

### Step 3: Create Adaptive CSS Classes
1. Define base adaptive classes (`.adaptive-text-title`, `.adaptive-text-subtitle`, etc.)
2. Create state-specific variants (`.light-background`, `.dark-background`)
3. Add smooth transitions for color changes

### Step 4: Build UI Components
1. Create slider component
2. Integrate with existing navigation/UI
3. Add accessibility features (aria-labels, keyboard support)

### Step 5: Apply to Text Elements
1. Add adaptive classes to target elements
2. Use brightness detection hook
3. Apply conditional CSS classes based on state

## Usage Pattern

### Component Implementation
```typescript
const MyComponent = () => {
  const isDarkBackground = useBackgroundBrightness();
  
  return (
    <div>
      <h1 className={`adaptive-text-title ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
        Title Text
      </h1>
      <p className={`adaptive-text-subtitle ${isDarkBackground ? 'dark-background' : 'light-background'}`}>
        Subtitle Text
      </p>
    </div>
  );
};
```

### Integration with Existing Components
```typescript
// In navigation or layout component
<BackgroundDimmerSlider className="hidden md:flex" />
```

## Key Features

### 1. Real-time Adaptation
- Monitors CSS custom property changes
- Uses MutationObserver for DOM changes
- Polling backup ensures reliability
- Smooth transitions prevent jarring changes

### 2. Accessibility Compliance
- Maintains proper contrast ratios
- Uses WCAG-compliant color combinations
- Preserves text readability across all brightness levels
- Keyboard-accessible controls

### 3. Performance Optimization
- Efficient polling (100ms intervals)
- Minimal DOM queries
- CSS-based animations
- Proper cleanup of observers and intervals

### 4. Fallback Systems
- Default color schemes for failures
- Multiple detection methods
- Graceful degradation
- Cross-browser compatibility

## Configuration Options

### Customizable Parameters
```typescript
// Brightness detection sensitivity
const LIGHT_THRESHOLD = 0.3; // Minimum light opacity to trigger light mode

// Polling frequency
const DETECTION_INTERVAL = 100; // ms

// Color definitions
const COLORS = {
  DARK_TEXT: '#1e293b',
  LIGHT_TEXT: '#ffffff',
  DARK_SUBTITLE: '#475569',
  LIGHT_SUBTITLE: '#94a3b8'
};
```

### Theme Integration
```css
/* Custom theme variables */
:root {
  --adaptive-dark-text: #1e293b;
  --adaptive-light-text: #ffffff;
  --adaptive-transition: all 0.3s ease;
}
```

## Testing Scenarios

### 1. Functionality Tests
- Slider movement updates text colors
- Page refresh preserves dimmer value
- Multiple components adapt simultaneously
- CSS variable changes are detected

### 2. Edge Cases
- External iframe content
- Rapid slider movements
- Component mounting/unmounting
- Browser tab switching

### 3. Performance Tests
- Memory leak detection
- CPU usage monitoring
- Animation smoothness
- Large page rendering

## Troubleshooting

### Common Issues

**1. Text not updating**
- Check CSS custom property detection
- Verify observer setup
- Confirm polling interval
- Test MutationObserver browser support

**2. Performance issues**
- Reduce polling frequency
- Optimize CSS selectors
- Limit observer scope
- Debounce rapid changes

**3. Styling conflicts**
- Check CSS specificity
- Verify class application order
- Test with existing styles
- Ensure proper cascade

### Debug Tools
```typescript
// Add to useBackgroundBrightness hook for debugging
console.log('Dimmer values:', { darkOpacity, lightOpacity });
console.log('Background state:', isDarkBackground);
console.log('Applied classes:', element.className);
```

## Browser Support

### Supported Features
- CSS Custom Properties (IE 11+)
- MutationObserver (IE 11+)
- getComputedStyle (All browsers)
- localStorage (IE 8+)

### Fallbacks
- Polling for older browsers
- Default color schemes
- Graceful degradation
- Progressive enhancement

## Extension Possibilities

### 1. Advanced Detection
- Image background analysis
- Gradient color extraction
- Video background adaptation
- Dynamic theme switching

### 2. User Preferences
- Per-component settings
- Saved color schemes
- Accessibility overrides
- Animation preferences

### 3. Integration Features
- Theme system integration
- CMS content adaptation
- Multi-language support
- RTL text support

## Security Considerations

### 1. Input Validation
- Sanitize slider values
- Validate CSS property values
- Prevent injection attacks
- Limit DOM access scope

### 2. Performance Limits
- Throttle observer callbacks
- Limit polling frequency
- Prevent memory leaks
- Monitor resource usage

This implementation provides a robust, accessible, and performant solution for dynamic text color adaptation that can be applied to any web application requiring background-aware text styling.