

You need to use every MCP tool available to you to complete this task. You need Sequential Thinking, Context7 if needed, Web Browsing if needed, and Supabase integrations if needed.

Every time you finish a task you are supposed to store every relevant information on aument memory and you need to create a proper log file on the .01Documentos/ folder. The format you must use to save is DDMMYY-taskNameSmall###.md Every change you do on a function, always create a file with the changes made followed by the sequential version of that change. We need to document our files with better title ordering and pattern

This file also needs to include a complete log of files that you edited on the given task, aswell as a range of lines you edited in it.