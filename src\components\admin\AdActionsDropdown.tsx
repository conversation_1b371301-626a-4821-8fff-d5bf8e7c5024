'use client';

import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { But<PERSON> } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Copy,
  BarChart3,
  ExternalLink,
  AlertTriangle,
  TrendingUp,
} from 'lucide-react';
import type { AdConfiguration } from '@/lib/admin/adService';

interface AdActionsDropdownProps {
  ad: AdConfiguration;
  onEdit: (ad: AdConfiguration) => void;
  onDelete: (adId: string) => Promise<void>;
  onToggleStatus?: (adId: string, newStatus: boolean) => Promise<void>;
  onViewStats?: (adId: string) => void;
  compact?: boolean;
  disabled?: boolean;
}

export function AdActionsDropdown({
  ad,
  onEdit,
  onDelete,
  onToggleStatus,
  onViewStats,
  compact = false,
  disabled = false,
}: AdActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    type: 'delete' | 'toggle' | null;
    title: string;
    description: string;
    variant: 'default' | 'destructive';
  }>({
    isOpen: false,
    type: null,
    title: '',
    description: '',
    variant: 'default',
  });

  const handleAction = async (action: string) => {
    setIsOpen(false);
    
    switch (action) {
      case 'edit':
        onEdit(ad);
        break;
        
      case 'delete':
        setConfirmDialog({
          isOpen: true,
          type: 'delete',
          title: 'Delete Ad Configuration',
          description: `This will permanently delete the ad configuration "${ad.name}". This action cannot be undone.`,
          variant: 'destructive',
        });
        break;
        
      case 'toggle':
        if (onToggleStatus) {
          setConfirmDialog({
            isOpen: true,
            type: 'toggle',
            title: ad.is_active ? 'Deactivate Ad' : 'Activate Ad',
            description: ad.is_active 
              ? `This will deactivate "${ad.name}" and stop showing it to users.`
              : `This will activate "${ad.name}" and start showing it to users.`,
            variant: 'default',
          });
        }
        break;
        
      case 'stats':
        if (onViewStats) {
          onViewStats(ad.id);
        }
        break;
        
      case 'copy':
        await navigator.clipboard.writeText(ad.link_url || '');
        break;
        
      case 'preview':
        if (ad.link_url) {
          window.open(ad.link_url, '_blank');
        }
        break;
    }
  };

  const handleConfirmAction = async () => {
    switch (confirmDialog.type) {
      case 'delete':
        await onDelete(ad.id);
        break;
      case 'toggle':
        if (onToggleStatus) {
          await onToggleStatus(ad.id, !ad.is_active);
        }
        break;
    }
    
    setConfirmDialog({
      isOpen: false,
      type: null,
      title: '',
      description: '',
      variant: 'default',
    });
  };

  return (
    <TooltipProvider>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size={compact ? "sm" : "icon"}
                className="hover:bg-muted/50 transition-colors"
                disabled={disabled}
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open ad actions menu</span>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Ad Actions</p>
          </TooltipContent>
        </Tooltip>
        
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="font-semibold text-primary">
            Ad Management
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Edit Action */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction('edit')}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Configuration
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Edit ad settings and configuration</p>
            </TooltipContent>
          </Tooltip>
          
          {/* Status Toggle */}
          {onToggleStatus && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction('toggle')}
                  className={`${ad.is_active 
                    ? 'text-orange-600 hover:text-orange-700 hover:bg-orange-50' 
                    : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                  } cursor-pointer`}
                >
                  {ad.is_active ? (
                    <>
                      <EyeOff className="mr-2 h-4 w-4" />
                      Deactivate Ad
                    </>
                  ) : (
                    <>
                      <Eye className="mr-2 h-4 w-4" />
                      Activate Ad
                    </>
                  )}
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>
                  {ad.is_active 
                    ? 'Hide this ad from users' 
                    : 'Show this ad to users'}
                </p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Analytics and Performance */}
          {onViewStats && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction('stats')}
                  className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 cursor-pointer"
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Statistics
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>View detailed performance analytics</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          {/* Preview/External Link */}
          {ad.link_url && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction('preview')}
                  className="text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 cursor-pointer"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Preview Link
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Open ad link in new tab</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          {/* Copy Link */}
          {ad.link_url && (
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuItem
                  onClick={() => handleAction('copy')}
                  className="text-gray-600 hover:text-gray-700 hover:bg-gray-50 cursor-pointer"
                >
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Link
                </DropdownMenuItem>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Copy ad link to clipboard</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Destructive Actions */}
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuItem
                onClick={() => handleAction('delete')}
                className="text-destructive hover:text-destructive/80 hover:bg-destructive/10 cursor-pointer"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Ad
              </DropdownMenuItem>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Permanently delete this ad configuration</p>
            </TooltipContent>
          </Tooltip>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => !open && setConfirmDialog(prev => ({ ...prev, isOpen: false }))}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              {confirmDialog.title}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAction}
              className={confirmDialog.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </TooltipProvider>
  );
} 