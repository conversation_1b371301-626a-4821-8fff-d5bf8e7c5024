// src/components/suspension/SuspensionGuard.tsx
// SUSPENSION GUARD COMPONENT
// Componente que protege funcionalidades contra usuários suspensos

'use client';

import React from 'react';
import { SuspensionNotice, useSuspensionStatus } from './SuspensionNotice';
import { useAuthContext } from '@/contexts/auth-context';

interface SuspensionGuardProps {
  /** Componentes filhos a serem protegidos */
  children: React.ReactNode;
  /** Componente customizado para mostrar quando suspenso */
  fallback?: React.ReactNode;
  /** Se deve mostrar aviso inline ao invés de bloquear completamente */
  showInlineNotice?: boolean;
  /** Ação que está sendo protegida (para logging) */
  action?: string;
  /** Classe CSS adicional */
  className?: string;
  /** Se deve redirecionar para página específica quando suspenso */
  redirectTo?: string;
}

/**
 * Componente que protege funcionalidades contra usuários suspensos
 * Mostra aviso de suspensão e bloqueia acesso se o usuário estiver suspenso
 */
export function SuspensionGuard({
  children,
  fallback,
  showInlineNotice = false,
  action = 'access_protected_content',
  className = '',
  redirectTo
}: SuspensionGuardProps) {
  const { user } = useAuthContext();
  const { isSuspended, suspensionReason, suspendedAt } = useSuspensionStatus(user);

  // TODOS OS HOOKS DEVEM ESTAR NO TOPO - REGRA DOS HOOKS DO REACT
  // Log da tentativa de acesso (em produção, isso seria enviado para analytics)
  React.useEffect(() => {
    if (isSuspended) {
      console.warn('Suspended user attempted action:', {
        userId: user?.uid || user?.id,
        action,
        timestamp: new Date().toISOString()
      });
    }
  }, [isSuspended, user?.uid, user?.id, action]);

  // Redirecionar se especificado
  React.useEffect(() => {
    if (isSuspended && redirectTo && typeof window !== 'undefined') {
      window.location.href = redirectTo;
    }
  }, [isSuspended, redirectTo]);

  // AGORA SIM PODEMOS TER RETURNS CONDICIONAIS APÓS TODOS OS HOOKS
  // Se o usuário não está suspenso, renderiza normalmente
  if (!isSuspended) {
    return <>{children}</>;
  }

  // Se tem fallback customizado, usa ele
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  // Se deve mostrar aviso inline junto com o conteúdo
  if (showInlineNotice) {
    return (
      <div className={className}>
        <SuspensionNotice
          reason={suspensionReason}
          suspendedAt={suspendedAt}
          variant="inline"
          className="mb-4"
        />
        <div className="opacity-50 pointer-events-none">
          {children}
        </div>
      </div>
    );
  }

  // Bloqueia completamente e mostra aviso
  return (
    <div className={className}>
      <SuspensionNotice
        reason={suspensionReason}
        suspendedAt={suspendedAt}
        variant="card"
        onContactSupport={() => {
          // Em produção, abriria modal de contato ou redirecionaria
          window.open('/contact', '_blank');
        }}
      />
    </div>
  );
}

/**
 * Hook que retorna função para verificar se uma ação é permitida
 */
export function useSuspensionCheck() {
  const { user } = useAuthContext();
  const { isSuspended, suspensionReason } = useSuspensionStatus(user);

  const checkAction = React.useCallback((action: string): { allowed: boolean; reason?: string } => {
    if (isSuspended) {
      return {
        allowed: false,
        reason: suspensionReason || 'Conta suspensa'
      };
    }
    
    return { allowed: true };
  }, [isSuspended, suspensionReason]);

  return {
    isSuspended,
    suspensionReason,
    checkAction
  };
}

/**
 * Componente para proteger formulários contra usuários suspensos
 */
export function SuspensionFormGuard({
  children,
  formName = 'form',
  onSuspendedSubmit
}: {
  children: React.ReactNode;
  formName?: string;
  onSuspendedSubmit?: () => void;
}) {
  const { checkAction } = useSuspensionCheck();

  const handleSubmit = React.useCallback((e: React.FormEvent) => {
    const result = checkAction(`submit_${formName}`);
    
    if (!result.allowed) {
      e.preventDefault();
      e.stopPropagation();
      
      if (onSuspendedSubmit) {
        onSuspendedSubmit();
      } else {
        alert(`Não é possível enviar formulário: ${result.reason}`);
      }
    }
  }, [checkAction, formName, onSuspendedSubmit]);

  return (
    <div onSubmit={handleSubmit}>
      {children}
    </div>
  );
}

/**
 * Componente para proteger botões contra usuários suspensos
 */
export function SuspensionButtonGuard({
  children,
  action = 'button_click',
  onSuspendedClick,
  className = ''
}: {
  children: React.ReactNode;
  action?: string;
  onSuspendedClick?: () => void;
  className?: string;
}) {
  const { checkAction } = useSuspensionCheck();

  const handleClick = React.useCallback((e: React.MouseEvent) => {
    const result = checkAction(action);
    
    if (!result.allowed) {
      e.preventDefault();
      e.stopPropagation();
      
      if (onSuspendedClick) {
        onSuspendedClick();
      } else {
        alert(`Ação não permitida: ${result.reason}`);
      }
    }
  }, [checkAction, action, onSuspendedClick]);

  return (
    <div onClick={handleClick} className={className}>
      {children}
    </div>
  );
}

/**
 * Componente para mostrar status de suspensão em páginas de perfil
 */
export function ProfileSuspensionStatus({ 
  userProfile,
  isOwnProfile = false 
}: { 
  userProfile: any;
  isOwnProfile?: boolean;
}) {
  const { isSuspended, suspensionReason, suspendedAt } = useSuspensionStatus(userProfile);

  if (!isSuspended) {
    return null;
  }

  return (
    <SuspensionNotice
      reason={suspensionReason}
      suspendedAt={suspendedAt}
      variant={isOwnProfile ? "card" : "inline"}
      showActions={isOwnProfile}
      className="mb-4"
    />
  );
}

export default SuspensionGuard; 