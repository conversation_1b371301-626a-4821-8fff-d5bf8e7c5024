-- Content Banner Module - Database Migration
-- Date: June 16, 2025
-- Purpose: Create database structure for content banners (horizontal banners between reviews and YouTube)

-- Create the main content banners table
CREATE TABLE user_content_banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  img_url TEXT NOT NULL,
  url TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  impression_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  last_impression_at TIMESTAMP WITH TIME ZONE,
  last_click_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups by user_id
CREATE INDEX idx_user_content_banners_user_id ON user_content_banners(user_id);

-- Create index for active banners
CREATE INDEX idx_user_content_banners_active ON user_content_banners(user_id, is_active);

-- Enable RLS for security
ALTER TABLE user_content_banners ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_content_banners
-- Allow users to read their own content banner data
CREATE POLICY user_content_banners_select ON user_content_banners
  FOR SELECT USING (auth.uid() = user_id);

-- Allow users to insert their own content banner data
CREATE POLICY user_content_banners_insert ON user_content_banners
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to update only their own content banner data
CREATE POLICY user_content_banners_update ON user_content_banners
  FOR UPDATE USING (auth.uid() = user_id);

-- Allow users to delete only their own content banner data
CREATE POLICY user_content_banners_delete ON user_content_banners
  FOR DELETE USING (auth.uid() = user_id);

-- Create detailed analytics table for content banners
CREATE TABLE content_banner_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  banner_id UUID NOT NULL REFERENCES user_content_banners(id) ON DELETE CASCADE,
  event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('impression', 'click')),
  user_agent TEXT,
  referrer TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for analytics table
CREATE INDEX idx_content_banner_analytics_banner_id ON content_banner_analytics(banner_id);
CREATE INDEX idx_content_banner_analytics_event_type ON content_banner_analytics(banner_id, event_type);
CREATE INDEX idx_content_banner_analytics_created_at ON content_banner_analytics(created_at);

-- Enable RLS for analytics table
ALTER TABLE content_banner_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policy for analytics - users can only see analytics for their own banners
CREATE POLICY content_banner_analytics_select ON content_banner_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_content_banners 
      WHERE user_content_banners.id = content_banner_analytics.banner_id 
      AND user_content_banners.user_id = auth.uid()
    )
  );

-- Allow inserting analytics data (for tracking)
CREATE POLICY content_banner_analytics_insert ON content_banner_analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_content_banners 
      WHERE user_content_banners.id = content_banner_analytics.banner_id 
      AND user_content_banners.user_id = auth.uid()
    )
  );

-- Stored procedure to increment impression count with metadata
CREATE OR REPLACE FUNCTION increment_content_banner_impression(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Update the main banner table
  UPDATE user_content_banners
  SET 
    impression_count = impression_count + 1,
    last_impression_at = NOW(),
    updated_at = NOW()
  WHERE id = banner_id;
  
  -- Insert detailed analytics record
  INSERT INTO content_banner_analytics (
    banner_id, 
    event_type, 
    user_agent, 
    referrer, 
    ip_address
  ) VALUES (
    banner_id, 
    'impression', 
    user_agent_param, 
    referrer_param, 
    ip_address_param
  );
END;
$$ LANGUAGE plpgsql;

-- Stored procedure to increment click count with metadata
CREATE OR REPLACE FUNCTION increment_content_banner_click(
  banner_id UUID,
  user_agent_param TEXT DEFAULT NULL,
  referrer_param TEXT DEFAULT NULL,
  ip_address_param INET DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Update the main banner table
  UPDATE user_content_banners
  SET 
    click_count = click_count + 1,
    last_click_at = NOW(),
    updated_at = NOW()
  WHERE id = banner_id;
  
  -- Insert detailed analytics record
  INSERT INTO content_banner_analytics (
    banner_id, 
    event_type, 
    user_agent, 
    referrer, 
    ip_address
  ) VALUES (
    banner_id, 
    'click', 
    user_agent_param, 
    referrer_param, 
    ip_address_param
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get content banner summary analytics
CREATE OR REPLACE FUNCTION get_content_banner_summary(banner_id UUID)
RETURNS TABLE (
  total_impressions BIGINT,
  total_clicks BIGINT,
  ctr NUMERIC(5,2),
  last_impression TIMESTAMP WITH TIME ZONE,
  last_click TIMESTAMP WITH TIME ZONE,
  days_active INTEGER,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(ucb.impression_count::BIGINT, 0) as total_impressions,
    COALESCE(ucb.click_count::BIGINT, 0) as total_clicks,
    CASE 
      WHEN ucb.impression_count > 0 THEN 
        ROUND((ucb.click_count::NUMERIC / ucb.impression_count::NUMERIC) * 100, 2)
      ELSE 0
    END as ctr,
    ucb.last_impression_at as last_impression,
    ucb.last_click_at as last_click,
    COALESCE(EXTRACT(DAY FROM NOW() - ucb.created_at)::INTEGER, 0) as days_active,
    ucb.created_at
  FROM user_content_banners ucb
  WHERE ucb.id = banner_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get daily stats for content banners
CREATE OR REPLACE FUNCTION get_content_banner_daily_stats(
  banner_id UUID, 
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
  date DATE,
  impressions BIGINT,
  clicks BIGINT,
  ctr NUMERIC(5,2)
) AS $$
BEGIN
  RETURN QUERY
  WITH date_series AS (
    SELECT generate_series(
      CURRENT_DATE - INTERVAL '1 day' * days_back,
      CURRENT_DATE,
      INTERVAL '1 day'
    )::DATE as date
  ),
  daily_analytics AS (
    SELECT 
      cba.created_at::DATE as date,
      COUNT(CASE WHEN cba.event_type = 'impression' THEN 1 END) as impressions,
      COUNT(CASE WHEN cba.event_type = 'click' THEN 1 END) as clicks
    FROM content_banner_analytics cba
    WHERE cba.banner_id = get_content_banner_daily_stats.banner_id
      AND cba.created_at >= CURRENT_DATE - INTERVAL '1 day' * days_back
    GROUP BY cba.created_at::DATE
  )
  SELECT 
    ds.date,
    COALESCE(da.impressions, 0) as impressions,
    COALESCE(da.clicks, 0) as clicks,
    CASE 
      WHEN COALESCE(da.impressions, 0) > 0 THEN 
        ROUND((COALESCE(da.clicks, 0)::NUMERIC / da.impressions::NUMERIC) * 100, 2)
      ELSE 0
    END as ctr
  FROM date_series ds
  LEFT JOIN daily_analytics da ON ds.date = da.date
  ORDER BY ds.date;
END;
$$ LANGUAGE plpgsql;
