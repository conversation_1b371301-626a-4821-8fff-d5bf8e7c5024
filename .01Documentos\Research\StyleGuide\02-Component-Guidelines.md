# CriticalPixel Component Guidelines

## 🧩 Component Architecture

CriticalPixel uses a **layered component architecture** combining Shadcn UI base components with custom gaming-themed styling.

### Component Hierarchy
```
1. Base UI Components (Shadcn)     → Core functionality
2. Custom Style Layers             → Gaming aesthetic
3. Composite Components            → Feature-specific combinations
4. Page-level Components           → Full layouts
```

## 🎯 Button Components

### Primary Button Variants
```tsx
// Standard button variants
<Button variant="default">Default Action</Button>
<Button variant="gradient">Featured Action</Button>
<Button variant="outline">Secondary Action</Button>
<Button variant="ghost">Subtle Action</Button>
<Button variant="destructive">Delete Action</Button>
```

### Button Styling Patterns
```css
/* Gradient button (featured actions) */
.btn-gradient {
  background: linear-gradient(to right, #8b5cf6, #06b6d4);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(to right, #7c3aed, #0891b2);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}
```

### Button Sizes
- **sm**: `h-9 px-3` - Compact actions
- **default**: `h-10 px-4` - Standard actions  
- **lg**: `h-11 px-8` - Primary CTAs
- **icon**: `h-10 w-10` - Icon-only buttons

## 🃏 Card Components

### Base Card Structure
```tsx
<Card className="bg-slate-900/60 border-slate-700/50 rounded-lg">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Supporting text</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content */}
  </CardContent>
  <CardFooter>
    {/* Actions */}
  </CardFooter>
</Card>
```

### Gaming Card Variants

#### Review Card
```css
.review-card {
  background: linear-gradient(to bottom right, rgba(15, 23, 42, 0.5), rgba(30, 41, 59, 0.3));
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.review-card:hover {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5), 0 0 20px rgba(139, 92, 246, 0.2);
}
```

#### Game Card
```css
.game-card {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: rgba(30, 33, 45, 0.85);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(139, 92, 246, 0.1), transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.game-card:hover::before {
  opacity: 1;
}
```

## 📝 Form Components

### Input Field Pattern
```tsx
<div className="form-group mb-6">
  <label className="form-label">
    Field Label
  </label>
  <input 
    className="form-input"
    type="text"
    placeholder="Enter value..."
  />
</div>
```

### Form Styling Classes
```css
.form-label {
  font-family: 'Fira Code', monospace;
  font-size: 0.85rem;
  text-transform: uppercase;
  color: var(--text-secondary);
  letter-spacing: 0.05em;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.form-input {
  background-color: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 10px;
  padding: 14px 16px;
  color: var(--text-primary);
  font-size: 0.95rem;
  width: 100%;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) inset;
}

.form-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  outline: none;
}
```

## 🏷️ Badge Components

### Badge Variants
```tsx
<Badge variant="default">Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="destructive">Error</Badge>
<Badge variant="outline">Outline</Badge>
```

### Gaming-Specific Badges
```css
/* Platform badges */
.badge-platform {
  background: var(--platform-color);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

/* Score badges */
.badge-score {
  background: linear-gradient(45deg, var(--score-color), var(--score-color-light));
  color: white;
  font-weight: 700;
  min-width: 40px;
  justify-content: center;
}
```

## 🎛️ Navigation Components

### Navbar Structure
```tsx
<nav className="fixed top-0 w-full z-50 navbar-glass">
  <div className="container mx-auto px-4">
    <div className="flex items-center justify-between h-16">
      <Logo />
      <NavigationMenu />
      <UserMenu />
    </div>
  </div>
</nav>
```

### Navigation Styling
```css
.navbar-glass {
  background: rgba(3, 7, 18, 0.8);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: var(--text-primary);
  background: rgba(139, 92, 246, 0.1);
}

.nav-link.active {
  color: var(--text-accent);
  background: rgba(139, 92, 246, 0.2);
}
```

## 📊 Data Display Components

### Score Display
```tsx
<div className="score-display">
  <div className="score-value">{score}</div>
  <div className="score-label">Overall</div>
</div>
```

```css
.score-display {
  text-align: center;
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.1));
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.score-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.score-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 4px;
}
```

### Progress Indicators
```css
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(71, 85, 105, 0.3);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--theme-primary), var(--theme-accent));
  border-radius: 4px;
  transition: width 0.5s ease;
}
```

## 🎨 Modal Components

### Modal Structure
```tsx
<Dialog>
  <DialogTrigger asChild>
    <Button>Open Modal</Button>
  </DialogTrigger>
  <DialogContent className="modal-content">
    <DialogHeader>
      <DialogTitle>Modal Title</DialogTitle>
      <DialogDescription>Modal description</DialogDescription>
    </DialogHeader>
    {/* Modal body */}
    <DialogFooter>
      {/* Actions */}
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Modal Styling
```css
.modal-content {
  background: linear-gradient(to bottom right, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.6);
}

.modal-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}
```

## 🎯 Interactive States

### Hover Effects
```css
/* Standard hover lift */
.hover-lift:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Glow effect */
.hover-glow:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
  transition: box-shadow 0.3s ease;
}
```

### Focus States
```css
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
  border-color: var(--border-focus);
}
```

### Loading States
```css
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgba(71, 85, 105, 0.1) 25%, 
    rgba(139, 92, 246, 0.1) 50%, 
    rgba(71, 85, 105, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

## 📱 Responsive Patterns

### Mobile-First Approach
```css
/* Base styles (mobile) */
.component {
  padding: 16px;
  font-size: 14px;
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    padding: 24px;
    font-size: 16px;
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .component {
    padding: 32px;
    font-size: 18px;
  }
}
```

---

*These component guidelines ensure consistent implementation across all CriticalPixel features while maintaining the gaming-focused aesthetic and professional quality.*
