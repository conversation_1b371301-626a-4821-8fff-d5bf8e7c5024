-- SISTEMA DE SUSPENSÃO DE USUÁRIOS - FUNÇÃO ADMINISTRATIVA
-- Data: 11 de Janeiro de 2025
-- Tarefa: UserSuspensionSystemImplementation - Função admin_toggle_user_suspension

-- 1. Adicionar colunas de suspensão se não existirem
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS suspended BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS suspension_reason TEXT,
ADD COLUMN IF NOT EXISTS suspended_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS suspended_by UUID REFERENCES profiles(id);

-- 2. <PERSON>riar função para verificar se usuário está suspenso
CREATE OR REPLACE FUNCTION is_user_suspended(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_id AND suspended = TRUE
  );
END;
$$;

-- 3. Função administrativa principal para alternar suspensão
CREATE OR REPLACE FUNCTION admin_toggle_user_suspension(
  p_target_user_id UUID,
  p_suspended BOOLEAN,
  p_reason TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_admin_id UUID;
  v_target_user_exists BOOLEAN;
  v_result JSON;
BEGIN
  -- Obter ID do admin atual da sessão
  v_admin_id := auth.uid();
  
  -- Verificar se admin está autenticado
  IF v_admin_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required';
  END IF;
  
  -- Verificar se admin tem permissões
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = v_admin_id AND is_admin = TRUE
  ) THEN
    RAISE EXCEPTION 'Admin privileges required';
  END IF;
  
  -- Verificar se usuário alvo existe
  SELECT EXISTS (
    SELECT 1 FROM profiles WHERE id = p_target_user_id
  ) INTO v_target_user_exists;
  
  IF NOT v_target_user_exists THEN
    RAISE EXCEPTION 'Target user not found';
  END IF;
  
  -- Prevenir auto-suspensão
  IF v_admin_id = p_target_user_id THEN
    RAISE EXCEPTION 'Cannot suspend your own account';
  END IF;
  
  -- Executar a suspensão/reativação
  UPDATE profiles 
  SET 
    suspended = p_suspended,
    suspension_reason = CASE 
      WHEN p_suspended THEN COALESCE(p_reason, 'Administrative action')
      ELSE NULL 
    END,
    suspended_at = CASE 
      WHEN p_suspended THEN NOW() 
      ELSE NULL 
    END,
    suspended_by = CASE 
      WHEN p_suspended THEN v_admin_id 
      ELSE NULL 
    END,
    updated_at = NOW()
  WHERE id = p_target_user_id;
  
  -- Verificar se update foi bem-sucedido
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Failed to update user suspension status';
  END IF;
  
  -- Log da ação administrativa
  INSERT INTO admin_audit_log (
    admin_id,
    action,
    target_user_id,
    details,
    created_at
  ) VALUES (
    v_admin_id,
    CASE WHEN p_suspended THEN 'user_suspended' ELSE 'user_reactivated' END,
    p_target_user_id,
    json_build_object(
      'reason', p_reason,
      'suspended', p_suspended,
      'timestamp', NOW()
    ),
    NOW()
  );
  
  -- Retornar resultado
  v_result := json_build_object(
    'success', TRUE,
    'user_id', p_target_user_id,
    'suspended', p_suspended,
    'reason', p_reason,
    'admin_id', v_admin_id,
    'timestamp', NOW()
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    -- Log do erro
    INSERT INTO admin_audit_log (
      admin_id,
      action,
      target_user_id,
      details,
      created_at
    ) VALUES (
      COALESCE(v_admin_id, '00000000-0000-0000-0000-000000000000'::UUID),
      'suspension_error',
      p_target_user_id,
      json_build_object(
        'error', SQLERRM,
        'sqlstate', SQLSTATE,
        'timestamp', NOW()
      ),
      NOW()
    );
    
    RAISE;
END;
$$;

-- 4. Criar tabela de log administrativo se não existir
CREATE TABLE IF NOT EXISTS admin_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_id UUID REFERENCES profiles(id),
  action TEXT NOT NULL,
  target_user_id UUID REFERENCES profiles(id),
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Adicionar índices para performance
CREATE INDEX IF NOT EXISTS idx_profiles_suspended ON profiles(suspended) WHERE suspended = TRUE;
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_admin_id ON admin_audit_log(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_target_user ON admin_audit_log(target_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action ON admin_audit_log(action);

-- 6. RLS (Row Level Security) para proteção
ALTER TABLE admin_audit_log ENABLE ROW LEVEL SECURITY;

-- Política para admins visualizarem logs
CREATE POLICY admin_audit_log_admin_read ON admin_audit_log
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );

-- 7. Comentários para documentação
COMMENT ON FUNCTION admin_toggle_user_suspension IS 'Função administrativa para suspender/reativar usuários. Requer privilégios de admin.';
COMMENT ON FUNCTION is_user_suspended IS 'Verificar se um usuário está suspenso';
COMMENT ON TABLE admin_audit_log IS 'Log de auditoria para ações administrativas';

-- 8. Testar a função (comentado para não executar automaticamente)
-- SELECT admin_toggle_user_suspension(
--   'user-id-here'::UUID, 
--   TRUE, 
--   'Test suspension'
-- ); 