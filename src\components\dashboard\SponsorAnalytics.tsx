'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  MousePointer,
  Calendar,
  Download,
  Activity,
  Target,
  Clock,
  BarChart3
} from 'lucide-react';
import {
  getSponsorBannerSummary,
  getSponsorBannerDailyStats,
  getPerformanceMetrics,
  exportAnalyticsToCSV,
  type SponsorBannerSummary,
  type DailyStats
} from '@/lib/services/sponsorAnalyticsService';

interface SponsorAnalyticsProps {
  bannerId: string;
  className?: string;
}

interface PerformanceMetrics extends SponsorBannerSummary {
  impressionTrend: number;
  clickTrend: number;
  dailyAvgImpressions: number;
  dailyAvgClicks: number;
  isPerforming: boolean;
  lastActivity: string | null;
}

const SponsorAnalytics: React.FC<SponsorAnalyticsProps> = ({
  bannerId,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([]);
  const [timeRange, setTimeRange] = useState<7 | 30 | 90>(30);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (bannerId) {
      loadAnalyticsData();
    }
  }, [bannerId, timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const [metricsData, statsData] = await Promise.all([
        getPerformanceMetrics(bannerId),
        getSponsorBannerDailyStats(bannerId, timeRange)
      ]);
      
      setMetrics(metricsData);
      setDailyStats(statsData.reverse()); // Reverse to show chronological order
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportCSV = async () => {
    try {
      setIsExporting(true);
      const csvData = await exportAnalyticsToCSV(bannerId, timeRange);
      
      if (csvData) {
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `sponsor-analytics-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-slate-400" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-500';
    if (trend < 0) return 'text-red-500';
    return 'text-slate-400';
  };

  if (isLoading) {
    return (
      <Card className={`border-slate-700/50 bg-slate-900/60 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-purple-500" />
            Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card className={`border-slate-700/50 bg-slate-900/60 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-purple-500" />
            Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-slate-400">No analytics data available yet.</p>
            <p className="text-sm text-slate-500 mt-2">
              Data will appear once your banner starts receiving impressions.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Time Range Selector */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-purple-500" />
          Sponsor Banner Analytics
        </h3>
        <div className="flex items-center gap-2">
          <div className="flex bg-slate-800/50 rounded-lg p-1">
            {[7, 30, 90].map((days) => (
              <Button
                key={days}
                variant={timeRange === days ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setTimeRange(days as 7 | 30 | 90)}
                className={`text-xs ${
                  timeRange === days
                    ? 'bg-purple-600 text-white'
                    : 'text-slate-400 hover:text-white'
                }`}
              >
                {days}d
              </Button>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportCSV}
            disabled={isExporting}
            className="border-slate-600 text-slate-300 hover:bg-slate-800"
          >
            <Download className="h-4 w-4 mr-1" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Impressions */}
        <Card className="border-slate-700/50 bg-slate-900/60">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Impressions</p>
                <p className="text-2xl font-bold text-white">
                  {formatNumber(metrics.total_impressions)}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(metrics.impressionTrend)}
                  <span className={`text-xs ${getTrendColor(metrics.impressionTrend)}`}>
                    {metrics.impressionTrend > 0 ? '+' : ''}{metrics.impressionTrend.toFixed(1)}%
                  </span>
                </div>
              </div>
              <Eye className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        {/* Total Clicks */}
        <Card className="border-slate-700/50 bg-slate-900/60">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Clicks</p>
                <p className="text-2xl font-bold text-white">
                  {formatNumber(metrics.total_clicks)}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(metrics.clickTrend)}
                  <span className={`text-xs ${getTrendColor(metrics.clickTrend)}`}>
                    {metrics.clickTrend > 0 ? '+' : ''}{metrics.clickTrend.toFixed(1)}%
                  </span>
                </div>
              </div>
              <MousePointer className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        {/* Click-Through Rate */}
        <Card className="border-slate-700/50 bg-slate-900/60">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Click-Through Rate</p>
                <p className="text-2xl font-bold text-white">{metrics.ctr.toFixed(2)}%</p>
                <div className="flex items-center gap-1 mt-1">
                  <Badge 
                    variant={metrics.isPerforming ? 'default' : 'secondary'}
                    className={`text-xs ${
                      metrics.isPerforming 
                        ? 'bg-green-600 text-white' 
                        : 'bg-slate-600 text-slate-300'
                    }`}
                  >
                    {metrics.isPerforming ? 'Good' : 'Average'}
                  </Badge>
                </div>
              </div>
              <Target className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        {/* Days Active */}
        <Card className="border-slate-700/50 bg-slate-900/60">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Days Active</p>
                <p className="text-2xl font-bold text-white">{metrics.days_active}</p>
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-xs text-slate-400">
                    Since {new Date(metrics.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <Calendar className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Chart */}
      <Card className="border-slate-700/50 bg-slate-900/60">
        <CardHeader>
          <CardTitle className="text-white">Performance Over Time</CardTitle>
          <p className="text-sm text-slate-400">
            Daily impressions and clicks for the last {timeRange} days
          </p>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="date"
                  stroke="#9CA3AF"
                  fontSize={12}
                  tickFormatter={formatDate}
                />
                <YAxis stroke="#9CA3AF" fontSize={12} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    color: '#F9FAFB'
                  }}
                  labelFormatter={(value) => `Date: ${formatDate(value as string)}`}
                />
                <Line
                  type="monotone"
                  dataKey="impressions"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                  name="Impressions"
                />
                <Line
                  type="monotone"
                  dataKey="clicks"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  name="Clicks"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>


    </div>
  );
};

export default SponsorAnalytics;
