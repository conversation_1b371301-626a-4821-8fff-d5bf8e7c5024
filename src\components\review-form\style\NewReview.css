/* ═══════════════════════════════════════════════════════════════════════════
   📄 PAGE.TSX SPECIFIC STYLES - REVIEW CREATION FORM
   ═══════════════════════════════════════════════════════════════════════════ */

/* Core Variables (Essential for page) */
:root {
  /* Transitions */
  --review-transition-fast: 0.15s ease-out;
  --review-transition-normal: 0.3s ease-out;
  --review-transition-slow: 0.5s ease-out;
  --review-easing-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);
  --review-easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Colors used in page */
  --review-text-primary: #ffffff;
  --review-text-secondary: rgba(255, 255, 255, 0.8);
  --review-text-muted: rgba(255, 255, 255, 0.6);
  --review-text-accent: #8b5cf6;
  --review-text-cyan: #06b6d4;
  
  /* Spacing */
  --review-spacing-xs: 0.25rem;
  --review-spacing-sm: 0.5rem;
  --review-spacing-md: 1rem;
  --review-spacing-lg: 1.5rem;
  --review-spacing-xl: 2rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
   📝 PAGE TITLE STYLING - READABLE ON ALL BACKGROUNDS
   ═══════════════════════════════════════════════════════════════════════════ */

.adaptive-text-title {
  /* Base styling */
  font-weight: 700;
  letter-spacing: -0.02em;
  transition: all 0.3s ease;

  /* Default white text with clean drop shadow - for dark backgrounds */
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

/* Adaptive styling for title based on background brightness */
.adaptive-text-title.light-background {
  color: #1e293b;
  text-shadow: 
    1px 1px 0px rgba(255, 255, 255, 0.8),
    2px 2px 4px rgba(255, 255, 255, 0.6);
}

.adaptive-text-title.dark-background {
  color: #ffffff;
  text-shadow: 2px 2px 0px rgba(0,0,0,1);
}

/* Subtitle styling for gray text under headers */
.adaptive-text-subtitle {
  /* Base styling */
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;

  /* Gray text with clean drop shadow - no outline */
  color: #94a3b8;
  text-shadow: 1px 1px 0px rgba(0,0,0,1);
}

/* ═══════════════════════════════════════════════════════════════════════════
   📝 ADAPTIVE SUBTITLE STYLING - READABLE ON ALL BACKGROUNDS
   ═══════════════════════════════════════════════════════════════════════════ */

.adaptive-text-subtitle {
  /* Base styling */
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;

  /* Default to light gray text with dark shadow (for dark backgrounds) */
  color: #94a3b8;
  text-shadow:
    0 1px 1px rgba(0, 0, 0, 0.6),
    0 1px 2px rgba(0, 0, 0, 0.4);
}

/* Adaptive styling for subtitles based on background brightness */
.adaptive-text-subtitle.light-background {
  color: #475569;
  text-shadow:
    0 1px 1px rgba(255, 255, 255, 0.4),
    0.3px 0.3px 0 rgba(255, 255, 255, 0.3),
    -0.3px -0.3px 0 rgba(255, 255, 255, 0.3),
    0.3px -0.3px 0 rgba(255, 255, 255, 0.3),
    -0.3px 0.3px 0 rgba(255, 255, 255, 0.3);
}

.adaptive-text-subtitle.dark-background {
  color: #94a3b8;
  text-shadow:
    0 1px 1px rgba(0, 0, 0, 0.6),
    0.3px 0.3px 0 rgba(0, 0, 0, 0.4),
    -0.3px -0.3px 0 rgba(0, 0, 0, 0.4),
    0.3px -0.3px 0 rgba(0, 0, 0, 0.4),
    -0.3px 0.3px 0 rgba(0, 0, 0, 0.4);
}

/* ═══════════════════════════════════════════════════════════════════════════
   📄 ADAPTIVE CONTENT STYLING - FOR SMALL TEXT AND LABELS
   ═══════════════════════════════════════════════════════════════════════════ */

.adaptive-text-content {
  /* Base styling */
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;

  /* Default to white text with dark shadow (for dark backgrounds) */
  color: #e2e8f0;
  text-shadow:
    0 1px 1px rgba(0, 0, 0, 0.6),
    0 1px 2px rgba(0, 0, 0, 0.4);
}

/* Adaptive styling for content text based on background brightness */
.adaptive-text-content.light-background {
  color: #334155;
  text-shadow:
    0 1px 1px rgba(255, 255, 255, 0.6),
    0.5px 0.5px 0 rgba(255, 255, 255, 0.4),
    -0.5px -0.5px 0 rgba(255, 255, 255, 0.4),
    0.5px -0.5px 0 rgba(255, 255, 255, 0.4),
    -0.5px 0.5px 0 rgba(255, 255, 255, 0.4);
}

.adaptive-text-content.dark-background {
  color: #e2e8f0;
  text-shadow:
    0 1px 1px rgba(0, 0, 0, 0.6),
    0.5px 0.5px 0 rgba(0, 0, 0, 0.4),
    -0.5px -0.5px 0 rgba(0, 0, 0, 0.4),
    0.5px -0.5px 0 rgba(0, 0, 0, 0.4),
    -0.5px 0.5px 0 rgba(0, 0, 0, 0.4);
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 FORM SECTION STYLING (Used in SectionWrapper)
   ═══════════════════════════════════════════════════════════════════════════ */

.review-form-section {
  scroll-margin-top: 1rem; /* MINIMAL - just enough to clear navbar */
  transition: all var(--review-transition-slow) ease-out;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🔘 BUTTON STYLES (Used in page navigation)
   ═══════════════════════════════════════════════════════════════════════════ */

.review-continue-button {
  position: relative;
  overflow: hidden;
  transition: all var(--review-transition-normal);
  border-radius: 0.5rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  border: none;
  cursor: pointer;
}

.review-continue-ready {
  background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
  color: var(--review-text-primary);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.review-continue-ready:hover {
  background: linear-gradient(135deg, #a78bfa 0%, #22d3ee 100%);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.3);
  transform: translateY(-2px);
}

.review-continue-disabled {
  background: #374151;
  color: var(--review-text-muted);
  cursor: not-allowed;
}

.review-continue-secondary {
  background: #374151;
  color: #94a3b8;
  cursor: pointer;
}

.review-continue-secondary:hover {
  background: #4b5563;
  color: #cbd5e1;
}

.review-button-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  font-weight: 600;
}

.review-button-arrow {
  width: 1rem;
  height: 1rem;
  margin-left: var(--review-spacing-sm);
}

.review-code-brackets {
  color: var(--review-text-accent);
  opacity: 0.7;
  transition: all var(--review-transition-normal);
  font-weight: 400;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎨 ANIMATIONS (Used in page.tsx)
   ═══════════════════════════════════════════════════════════════════════════ */

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(200%); }
}

@keyframes spin-reverse {
  from { transform: rotate(360deg); }
  to { transform: rotate(0deg); }
}

@keyframes console-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-spin-reverse {
  animation: spin-reverse 1s linear infinite;
}

.animate-console-blink {
  animation: console-blink 1.2s infinite;
}

/* ═══════════════════════════════════════════════════════════════════════════
   📱 RESPONSIVE DESIGN (Container width overrides)
   ═══════════════════════════════════════════════════════════════════════════ */

/* Responsive width classes used in page.tsx - SCOPED TO REVIEW FORM */
@media (max-width: 1360px) {
  .page-main-container .container {
    max-width: 100% !important;
  }
}

/* Section scroll margins for navbar offset - MINIMAL */
section {
  scroll-margin-top: 1rem; /* MINIMAL - just enough to clear navbar */
}

/* ═══════════════════════════════════════════════════════════════════════════
   📱 RESPONSIVE NAVIGATION SECTIONS
   ═══════════════════════════════════════════════════════════════════════════ */

/* Ensure status text breaks properly on small screens */
@media (max-width: 640px) {
  /* Stack navigation elements vertically on mobile - SCOPED TO REVIEW FORM */
  .page-main-container .flex.flex-col.sm\\:flex-row.sm\\:items-center.sm\\:justify-between {
    gap: 1rem;
  }

  /* Ensure status text doesn't overflow */
  .text-sm.text-slate-400.opacity-80.font-mono.break-words {
    word-break: break-word;
    hyphens: auto;
    line-height: 1.4;
  }

  /* Make buttons full width on very small screens */
  .review-continue-button {
    min-width: 120px;
  }
}

/* Medium screens - ensure proper spacing - SCOPED TO REVIEW FORM */
@media (max-width: 768px) {
  .page-main-container .flex.items-center.space-x-3.flex-shrink-0 {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

.review-form-section {
  scroll-margin-top: 1rem; /* MINIMAL - just enough to clear navbar */
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎮 LOADING STATE STYLES (Used in page.tsx loading component)
   ═══════════════════════════════════════════════════════════════════════════ */

/* Custom scrollbar styling SCOPED to review form content only */
.page-main-container ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.page-main-container ::-webkit-scrollbar-track {
  background: transparent;
}

.page-main-container ::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 4px;
}

.page-main-container ::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* ═══════════════════════════════════════════════════════════════════════════
 📦 SECTION WRAPPER STYLES
 ═══════════════════════════════════════════════════════════════════════════ */

.section-wrapper-active {
  border-color: rgba(255, 255, 255, 0.08);
  background: linear-gradient(135deg, #1e293b 0%, #181c2a 100%);
}

.section-wrapper-inactive {
  border-color: rgba(255, 255, 255, 0.05);
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

.section-wrapper-inactive:hover {
  border-color: rgba(255, 255, 255, 0.1);
}

.section-active-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, #64748b 0%, #6b7280 50%, #64748b 100%);
  opacity: 0.05;
}

.section-header {
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  padding: 0.75rem;
  transition: colors var(--review-transition-normal);
  background: linear-gradient(to right, #1e293b 0%, #181c2a 100%);
}

/* ═══════════════════════════════════════════════════════════════════════════
   🔄 LOADING STATE STYLES
   ═══════════════════════════════════════════════════════════════════════════ */

.loading-container {
  min-height: 100vh;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loading-spinner {
  position: relative;
}

.loading-spinner-primary {
  width: 4rem;
  height: 4rem;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.loading-spinner-secondary {
  position: absolute;
  inset: 0;
  width: 4rem;
  height: 4rem;
  border: 2px solid rgba(6, 182, 212, 0.2);
  border-bottom: 2px solid #06b6d4;
  border-radius: 50%;
  animation: spin-reverse 1s linear infinite;
  margin: 0 auto;
}

.loading-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.loading-title {
  color: white;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  font-size: 1.125rem;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.loading-dot-1 {
  width: 0.5rem;
  height: 0.5rem;
  background: #8b5cf6;
  border-radius: 50%;
  animation: bounce 1s infinite;
}

.loading-dot-2 {
  width: 0.5rem;
  height: 0.5rem;
  background: #a855f7;
  border-radius: 50%;
  animation: bounce 1s infinite;
  animation-delay: 0.1s;
}

.loading-dot-3 {
  width: 0.5rem;
  height: 0.5rem;
  background: #06b6d4;
  border-radius: 50%;
  animation: bounce 1s infinite;
  animation-delay: 0.2s;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🔐 AUTHENTICATION STATE STYLES
   ═══════════════════════════════════════════════════════════════════════════ */

.auth-container {
  min-height: 100vh;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.auth-card-wrapper {
  position: relative;
  max-width: 28rem;
  width: 100%;
}

.auth-card-glow {
  position: absolute;
  inset: -0.25rem;
  background: linear-gradient(to right, rgba(239, 68, 68, 0.3) 0%, rgba(251, 146, 60, 0.3) 100%);
  border-radius: 1rem;
  filter: blur(0.25rem);
  opacity: 0.6;
}

.auth-card {
  position: relative;
  border: 1px solid rgba(239, 68, 68, 0.2);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  background: linear-gradient(135deg, rgba(24, 28, 42, 0.92) 0%, rgba(20, 23, 38, 0.88) 100%);
}

.auth-icon-container {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 1.5rem auto;
  border-radius: 1rem;
  background: linear-gradient(to bottom right, rgba(239, 68, 68, 0.2) 0%, rgba(251, 146, 60, 0.2) 100%);
  border: 1px solid rgba(239, 68, 68, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎨 MAIN PAGE LAYOUT - FIXED: REMOVED EXCESSIVE TOP PADDING
   ═══════════════════════════════════════════════════════════════════════════ */

.page-main-container {
  min-height: 100vh;
  background: transparent;
}

.page-background {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.background-gradient-1 {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(139, 92, 246, 0.01) 0%, transparent 100%);
  border-radius: 50%;
  filter: blur(80px);
  animation: pulse 8s ease-in-out infinite;
}

.background-gradient-2 {
  position: absolute;
  bottom: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top left, rgba(6, 182, 212, 0.01) 0%, transparent 100%);
  border-radius: 50%;
  filter: blur(80px);
  animation: pulse 8s ease-in-out infinite;
  animation-delay: 2s;
}

/* Simplified pulse animation for subtle background effect */
@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.page-content-container {
  position: relative;
  margin: 0 auto;
  padding: 0rem;
  padding-top: 0.5rem; /* MINIMAL padding - just enough for breathing room */
  padding-bottom: 8rem; /* Bottom padding for floating navbar */
  width: 100%;
  /* Width will be controlled by the slider */
}

/* ═══════════════════════════════════════════════════════════════════════════
   📱 RESPONSIVE BREAKPOINTS - UPDATED TO MATCH ORIGINAL TAILWIND CLASSES
   ═══════════════════════════════════════════════════════════════════════════ */

/* Width controlled by slider component */

/* lg:max-w-full md:max-w-full sm:max-w-full */
@media (max-width: 1024px) {
  .page-content-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .page-content-container {
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .page-content-container {
    max-width: 100%;
  }
}

/* Container width override controlled by slider in page component */

/* Container width override for smaller screens - SCOPED TO REVIEW FORM */
@media (max-width: 1360px) {
  .page-content-container {
    max-width: 100%;
  }

  .page-main-container .container,
  .page-main-container > div {
    max-width: 100% !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 FOCUS MODE ANIMATIONS
   ═══════════════════════════════════════════════════════════════════════════ */

/* Disable body scroll when focus mode is active */
body:has(.focus-mode-overlay) {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* Focus Mode Overlay */
.focus-mode-overlay {
  position: fixed;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(50px);
  -webkit-backdrop-filter: blur(50px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 0 !important;
  max-width: none !important;
  transform: none !important;

  /* Entrance Animation */
  opacity: 0;
  animation: focusModeEnter 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.focus-mode-overlay.exiting {
  animation: focusModeExit 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Focus Mode Container */
.focus-mode-container {
  width: 90%;
  height: 90%;
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow:
    0 8px 16px -4px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(139, 92, 246, 0.1);

  /* Entrance Animation */
  transform: scale(0.95) translateY(20px);
  opacity: 0;
  animation: focusContainerEnter 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s forwards;
}

.focus-mode-overlay.exiting .focus-mode-container {
  animation: focusContainerExit 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Focus Mode Header */
.focus-mode-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(16px);
}

/* Focus Mode Editor Area */
.focus-mode-editor-area {
  height: calc(100% - 80px);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.focus-mode-editor-wrapper {
  height: 100%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  overflow: hidden;
  background: rgba(15, 23, 42, 0.5);
  display: flex;
  flex-direction: column;
}

/* Keyframe Animations */
@keyframes focusModeEnter {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes focusModeExit {
  from {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
  to {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
}

@keyframes focusContainerEnter {
  from {
    transform: scale(0.95) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes focusContainerExit {
  from {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  to {
    transform: scale(0.95) translateY(20px);
    opacity: 0;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 ADDITIONAL ANIMATIONS (Previously in JSX style block)
   ═══════════════════════════════════════════════════════════════════════════ */

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25%);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎯 REVIEW PAGE CONTAINER - DYNAMIC WIDTH SUPPORT
   ═══════════════════════════════════════════════════════════════════════════ */

/* Base container styles - no transitions, instant changes */
.page-main-container .review-page-container {
  margin-left: auto;
  margin-right: auto;
  transition: none !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Force 100% width on screens 1360px and below */
@media (max-width: 1360px) {
  .page-main-container .review-page-container {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
   🎛️ WIDTH CONTROL BUTTON ENHANCEMENTS - NO TRANSITIONS
   ═══════════════════════════════════════════════════════════════════════════ */

/* No transitions - instant feedback */
.width-control-button {
  transition: none !important;
  transform: none !important;
}

.width-control-button:hover:not(:disabled) {
  transform: none !important;
}

.width-control-button:active:not(:disabled) {
  transform: none !important;
}

/* No transitions for width indicator dots */
.width-indicator-dot {
  transition: none !important;
  cursor: pointer;
  transform: none !important;
}

.width-indicator-dot:hover {
  transform: none !important;
}