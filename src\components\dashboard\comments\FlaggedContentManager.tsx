'use client';

// Flagged Content Manager Component
// Date: 22/06/2025
// Task: Simplified Flagged Content Display - Slim container with context menu navigation

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Flag,
  Check,
  X,
  ExternalLink,
  User,
  Calendar,
  MoreVertical,
  Trash2,
  Loader2,
  ArrowUpRight,
  ChevronDown
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import { toast } from '@/hooks/use-toast';
import { useBackgroundBrightness } from '@/hooks/useBackgroundBrightness';
import { cn } from '@/lib/utils';

interface FlaggedContent {
  id: string;
  content_id: string;
  content_type: 'review' | 'comment';
  reporter_id: string;
  reason: string;
  description?: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
  resolved_by?: string;
  resolved_at?: string;
  content?: {
    id: string;
    title?: string;
    content: string;
    author_id: string;
    author_name?: string;
    author_username?: string;
    author_display_name?: string;
    review_id?: string;
    review_title?: string;
    review_slug?: string;
    forum_post_title?: string;
    is_forum_reply?: boolean;
    parent_post_id?: string;
  };
  reporter?: {
    username: string;
    display_name: string;
  };
}

interface FlaggedContentManagerProps {
  userId: string;
}

export function FlaggedContentManager({ userId }: FlaggedContentManagerProps) {
  const queryClient = useQueryClient();
  const supabase = createClient();
  const [selectedFlag, setSelectedFlag] = useState<string | null>(null);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [removeDialogOpen, setRemoveDialogOpen] = useState(false);
  const [selectedContentForRemoval, setSelectedContentForRemoval] = useState<FlaggedContent | null>(null);
  const [shouldBanUser, setShouldBanUser] = useState(false);

  // Background brightness detection for text adaptation
  const isDarkBackground = useBackgroundBrightness();

  // Filter states
  const [filterReason, setFilterReason] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('latest');
  const [displayCount, setDisplayCount] = useState(5);

  // Fetch flagged content for user's reviews and forum posts
  const { data: flaggedContent, isLoading } = useQuery({
    queryKey: ['flagged-content', userId],
    queryFn: async (): Promise<FlaggedContent[]> => {
      console.log('🔍 FlaggedContentManager: Starting query for userId:', userId);
      
      // Get user's review IDs
      const { data: userReviews } = await supabase
        .from('reviews')
        .select('id')
        .eq('author_id', userId);

      const reviewIds = userReviews?.map(r => r.id) || [];
      console.log('📚 Found user reviews:', reviewIds.length, reviewIds);

      // Get comment IDs for user's reviews (comments on their reviews)
      const { data: userReviewComments } = await supabase
        .from('comments')
        .select('id')
        .in('review_id', reviewIds);

      const userReviewCommentIds = userReviewComments?.map(c => c.id) || [];

      // Get comment IDs authored by the user (on any review)
      const { data: userAuthoredComments } = await supabase
        .from('comments')
        .select('id')
        .eq('author_id', userId);

      const userAuthoredCommentIds = userAuthoredComments?.map(c => c.id) || [];
      console.log('✍️ Found user authored comments:', userAuthoredCommentIds.length, userAuthoredCommentIds);

      // Combine all content IDs (reviews + comments on user's reviews + user's comments on any review)
      const allContentIds = [
        ...reviewIds,
        ...userReviewCommentIds,
        ...userAuthoredCommentIds
      ];

      // Remove duplicates
      const uniqueContentIds = [...new Set(allContentIds)];
      console.log('🎯 Total unique content IDs to check:', uniqueContentIds.length, uniqueContentIds);

      if (uniqueContentIds.length === 0) {
        console.log('❌ No content IDs found, returning empty array');
        return [];
      }

      // Get flags for user's content (without the problematic join)
      const { data: flags, error } = await supabase
        .from('content_flags')
        .select('*')
        .in('content_id', uniqueContentIds)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      console.log('🚩 Raw flags query result:', flags?.length || 0, flags);

      if (error) {
        console.error('❌ Error fetching flags:', error);
        throw error;
      }

      // Get reporter data separately for all flags
      let reporterData: Record<string, any> = {};
      if (flags && flags.length > 0) {
        const reporterIds = [...new Set(flags.map(f => f.reporter_id).filter(Boolean))];
        if (reporterIds.length > 0) {
          const { data: reporters } = await supabase
            .from('profiles')
            .select('id, username, display_name')
            .in('id', reporterIds);
          
          if (reporters) {
            reporters.forEach(reporter => {
              reporterData[reporter.id] = {
                username: reporter.username,
                display_name: reporter.display_name
              };
            });
          }
        }
      }

      // Get additional data for each flag
      console.log(`🔍 Processing ${flags?.length || 0} flags for user ${userId}`);
      
      const enrichedFlags = await Promise.all(
        (flags || []).map(async (flag) => {
          console.log('🔍 Processing flag:', flag.id, 'for content:', flag.content_type, flag.content_id);
          let content = null;

          if (flag.content_type === 'review') {
            const { data: review } = await supabase
              .from('reviews')
              .select('id, title, content, author_id, slug')
              .eq('id', flag.content_id)
              .single();

            if (review) {
              console.log('✅ Found review:', review.id, 'by author:', review.author_id);
              content = {
                id: review.id,
                title: review.title,
                content: review.content || '',
                author_id: review.author_id,
                review_slug: review.slug,
              };
            } else {
              console.log('❌ Review not found for flag:', flag.content_id);
            }
          } else if (flag.content_type === 'comment') {
            const { data: comment } = await supabase
              .from('comments')
              .select('id, content, author_id, author_name, review_id, parent_id, title')
              .eq('id', flag.content_id)
              .single();

            if (comment) {
              console.log('✅ Found comment:', comment.id, 'by author:', comment.author_id);
              
              // Get review info for this comment
              const { data: reviewInfo } = await supabase
                .from('reviews')
                .select('title, slug')
                .eq('id', comment.review_id)
                .single();

              let forumPostTitle = null;
              let isForumReply = false;

              // Check if this is a forum reply (has parent_id)
              if (comment.parent_id) {
                isForumReply = true;
                // Get the parent forum post title
                const { data: parentPost } = await supabase
                  .from('comments')
                  .select('title, author_name')
                  .eq('id', comment.parent_id)
                  .single();
                
                forumPostTitle = parentPost?.title || `Post by ${parentPost?.author_name || 'Unknown'}`;
              } else if (comment.title) {
                // This is a forum post itself
                forumPostTitle = comment.title;
              }

              content = {
                id: comment.id,
                content: comment.content || '',
                author_id: comment.author_id,
                author_name: comment.author_name,
                review_id: comment.review_id,
                review_title: reviewInfo?.title || 'Unknown Review',
                review_slug: reviewInfo?.slug || '',
                forum_post_title: forumPostTitle,
                is_forum_reply: isForumReply,
                parent_post_id: comment.parent_id,
              };
            } else {
              console.log('❌ Comment not found for flag:', flag.content_id);
            }
          }

          return {
            ...flag,
            content,
            reporter: reporterData[flag.reporter_id] || null,
          };
        })
      );

      console.log('✅ Returning', enrichedFlags.length, 'enriched flagged content items');
      return enrichedFlags;
    },
    enabled: !!userId,
  });

  // Filter and sort flagged content
  const filteredAndSortedContent = useMemo(() => {
    if (!flaggedContent) return [];
    
    let filtered = flaggedContent;
    
    // Filter by reason
    if (filterReason !== 'all') {
      filtered = filtered.filter(flag => flag.reason === filterReason);
    }
    
    // Filter by type
    if (filterType !== 'all') {
      if (filterType === 'posts') {
        filtered = filtered.filter(flag => 
          flag.content_type === 'comment' && 
          flag.content?.forum_post_title && 
          !flag.content?.is_forum_reply
        );
      } else if (filterType === 'replies') {
        filtered = filtered.filter(flag => 
          flag.content_type === 'comment' && 
          flag.content?.is_forum_reply
        );
      }
    }
    
    // Sort by date
    if (sortBy === 'latest') {
      filtered = filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    } else if (sortBy === 'oldest') {
      filtered = filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    }
    
    return filtered;
  }, [flaggedContent, filterReason, filterType, sortBy]);

  // Reset display count when filters change
  React.useEffect(() => {
    setDisplayCount(5);
  }, [filterReason, filterType, sortBy]);

  // Paginated flagged content for display
  const displayedContent = useMemo(() => {
    return filteredAndSortedContent.slice(0, displayCount);
  }, [filteredAndSortedContent, displayCount]);

  // Get unique reasons for filter dropdown
  const uniqueReasons = useMemo(() => {
    if (!flaggedContent) return [];
    return [...new Set(flaggedContent.map(flag => flag.reason))];
  }, [flaggedContent]);

  // Handle load more
  const handleLoadMore = () => {
    setDisplayCount(prev => prev + 5);
  };

  // Resolve flag mutation
  const resolveFlag = useMutation({
    mutationFn: async ({ flagId, action, notes }: { flagId: string; action: 'resolve' | 'dismiss'; notes: string }) => {
      const { error } = await supabase
        .from('content_flags')
        .update({
          status: action === 'resolve' ? 'resolved' : 'dismissed',
          resolved_by: userId,
          resolved_at: new Date().toISOString(),
        })
        .eq('id', flagId);
        
      if (error) throw error;
    },
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: ['flagged-content', userId] });
      
      // Invalidate user comments query to update the flagged count in the header
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      
      // Invalidate forum queries to refresh any changes
      queryClient.invalidateQueries({ queryKey: ['forum-posts'] });
      queryClient.invalidateQueries({ queryKey: ['forum-thread'] });
      queryClient.invalidateQueries({ queryKey: ['forum-stats'] });
      
      toast({
        title: `Flag ${action}d`,
        description: `The flagged content has been ${action}d successfully.`,
      });
      setSelectedFlag(null);
      setResolutionNotes('');
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Remove content and optionally ban user
  const removeContentMutation = useMutation({
    mutationFn: async ({ flag, banUser }: { flag: FlaggedContent; banUser: boolean }) => {
      // Delete the flagged content
      if (flag.content_type === 'review') {
        const { error } = await supabase
          .from('reviews')
          .delete()
          .eq('id', flag.content_id);
        if (error) throw error;
      } else if (flag.content_type === 'comment') {
        const { error } = await supabase
          .from('comments')
          .update({ is_deleted: true })
          .eq('id', flag.content_id);
        if (error) throw error;
      }

      // Ban user if requested
      if (banUser && flag.content?.author_id) {
        const { error: banError } = await supabase
          .from('user_blocks')
          .insert({
            blocker_id: userId,
            blocked_id: flag.content.author_id,
            reason: `Banned due to flagged content: ${flag.reason}`,
            created_at: new Date().toISOString(),
          });
        if (banError) throw banError;
      }

      // Mark flag as resolved
      const { error: flagError } = await supabase
        .from('content_flags')
        .update({
          status: 'resolved',
          resolved_by: userId,
          resolved_at: new Date().toISOString(),
        })
        .eq('id', flag.id);
      
      if (flagError) throw flagError;
    },
    onSuccess: (_, { flag, banUser }) => {
      queryClient.invalidateQueries({ queryKey: ['flagged-content', userId] });
      
      // Invalidate user comments query to update the flagged count in the header
      queryClient.invalidateQueries({ queryKey: ['user-comments', userId] });
      
      // Invalidate forum queries to refresh the display with moderation message
      if (flag.content_type === 'comment') {
        queryClient.invalidateQueries({ queryKey: ['forum-posts'] });
        queryClient.invalidateQueries({ queryKey: ['forum-thread'] });
        queryClient.invalidateQueries({ queryKey: ['forum-stats'] });
      }
      
      toast({
        title: "Content removed",
        description: `The flagged content has been removed${banUser ? ' and the user has been banned' : ''}.`,
      });
      setRemoveDialogOpen(false);
      setSelectedContentForRemoval(null);
      setShouldBanUser(false);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleResolveFlag = (flagId: string, action: 'resolve' | 'dismiss') => {
    resolveFlag.mutate({ flagId, action, notes: resolutionNotes });
  };

  const handleRemoveContent = (flag: FlaggedContent) => {
    setSelectedContentForRemoval(flag);
    setRemoveDialogOpen(true);
  };

  const confirmRemoveContent = () => {
    if (selectedContentForRemoval) {
      removeContentMutation.mutate({
        flag: selectedContentForRemoval,
        banUser: shouldBanUser,
      });
    }
  };

  if (isLoading) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-16">
            <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
            <span className="ml-3 font-mono text-sm text-slate-400">Loading flagged content...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!flaggedContent || flaggedContent.length === 0) {
    return (
      <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
        <CardContent className="p-12 text-center">
          <Flag className="mx-auto h-12 w-12 text-slate-400 mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2 font-mono">
            No flagged content
          </h3>
          <p className="text-slate-400 font-mono text-sm">
            No content has been flagged for review. Great job maintaining quality!
          </p>
        </CardContent>
      </Card>
    );
  }

  // Show filter controls and content
  const showFilters = flaggedContent.length > 0;

  const getContentUrl = (flag: FlaggedContent) => {
    if (flag.content_type === 'review' && flag.content?.review_slug) {
      return `/reviews/view/${flag.content.review_slug}`;
    } else if (flag.content_type === 'comment' && flag.content?.review_slug) {
      // Check if this is a forum reply
      if (flag.content.is_forum_reply && flag.content.parent_post_id) {
        // For forum replies, navigate to the parent forum post
        return `/reviews/view/${flag.content.review_slug}?scrollTo=comment-${flag.content.parent_post_id}&highlight=true#comment-${flag.content.parent_post_id}`;
      } else {
        // For regular comments or forum posts, navigate directly to the comment
        return `/reviews/view/${flag.content.review_slug}?scrollTo=comment-${flag.content_id}&highlight=true#comment-${flag.content_id}`;
      }
    }
    return null;
  };

  const handleGoToComment = (flag: FlaggedContent) => {
    const url = getContentUrl(flag);
    if (url) {
      window.open(url, '_blank');
    }
  };

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row gap-3 p-4 bg-slate-800/30 border border-slate-700/40 rounded-lg">
          <div className="flex-1">
            <label className="text-xs font-mono text-slate-400 mb-1 block">
              <span className="text-slate-500">//</span> Reason
            </label>
            <Select value={filterReason} onValueChange={setFilterReason}>
              <SelectTrigger className="bg-slate-900/60 border-slate-600/50 text-slate-200 h-8 text-xs">
                <SelectValue placeholder="All reasons" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900 border-slate-700">
                <SelectItem value="all">All reasons</SelectItem>
                {uniqueReasons.map((reason) => (
                  <SelectItem key={reason} value={reason}>{reason}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex-1">
            <label className="text-xs font-mono text-slate-400 mb-1 block">
              <span className="text-slate-500">//</span> Type
            </label>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="bg-slate-900/60 border-slate-600/50 text-slate-200 h-8 text-xs">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900 border-slate-700">
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="posts">Posts</SelectItem>
                <SelectItem value="replies">Replies</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex-1">
            <label className="text-xs font-mono text-slate-400 mb-1 block">
              <span className="text-slate-500">//</span> Sort by
            </label>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="bg-slate-900/60 border-slate-600/50 text-slate-200 h-8 text-xs">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-slate-900 border-slate-700">
                <SelectItem value="latest">Latest reports</SelectItem>
                <SelectItem value="oldest">Oldest reports</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-end">
            <div className="px-3 py-1 bg-slate-700/50 border border-slate-600/50 rounded text-xs font-mono text-slate-300">
              {filteredAndSortedContent.length} flagged
            </div>
          </div>
        </div>
      )}

      {/* Show message if no items match filters */}
      {filteredAndSortedContent.length === 0 && flaggedContent.length > 0 && (
        <Card className="border-gray-800 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur">
          <CardContent className="p-8 text-center">
            <Flag className="mx-auto h-8 w-8 text-slate-500 mb-3" />
            <p className="text-slate-400 font-mono text-sm">
              No flagged content matches the current filters
            </p>
          </CardContent>
        </Card>
      )}

      {/* Flagged Content List */}
      {displayedContent.map((flag) => (
        <motion.div
          key={flag.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="group"
        >
          <Card 
            className="border-slate-700/50 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur hover:border-slate-600/70 transition-all duration-200 cursor-pointer"
            onClick={() => handleGoToComment(flag)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 flex-1">
                  {/* Flag Icon */}
                  <div className="p-2 bg-orange-600/20 rounded border border-orange-600/30 flex-shrink-0">
                    <Flag className="h-4 w-4 text-orange-400" />
                  </div>
                  
                  {/* Report Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-xs text-slate-500 font-mono">
                        {formatDistanceToNow(new Date(flag.created_at))} ago
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-1 text-slate-400 text-xs font-mono">
                      <User className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">
                        {flag.content?.author_name || flag.content?.author_username || 'Unknown'}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Badges */}
                <div className="flex items-center gap-2 mr-3 flex-shrink-0">
                  <Badge variant="outline" className="text-xs font-mono bg-orange-500/10 text-orange-300 border-orange-500/30">
                    {flag.reason}
                  </Badge>
                  {flag.content?.is_forum_reply && (
                    <Badge variant="outline" className="text-xs font-mono bg-blue-500/10 text-blue-300 border-blue-500/30">
                      Reply
                    </Badge>
                  )}
                  {flag.content?.forum_post_title && !flag.content?.is_forum_reply && (
                    <Badge variant="outline" className="text-xs font-mono bg-purple-500/10 text-purple-300 border-purple-500/30">
                      Post
                    </Badge>
                  )}
                </div>
                
                {/* Context Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-8 w-8 p-0 opacity-100 flex-shrink-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-slate-900/95 border-slate-700 backdrop-blur-sm min-w-44">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleGoToComment(flag);
                      }}
                      className="font-mono text-xs text-blue-400 hover:bg-blue-500/20 hover:text-blue-300 cursor-pointer"
                    >
                      <ArrowUpRight className="h-3 w-3 mr-2" />
                      Go to Comment
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleResolveFlag(flag.id, 'resolve');
                      }}
                      className="font-mono text-xs text-green-400 hover:bg-green-500/20 hover:text-green-300 cursor-pointer"
                    >
                      <Check className="h-3 w-3 mr-2" />
                      Resolve
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleResolveFlag(flag.id, 'dismiss');
                      }}
                      className="font-mono text-xs text-yellow-400 hover:bg-yellow-500/20 hover:text-yellow-300 cursor-pointer"
                    >
                      <X className="h-3 w-3 mr-2" />
                      Dismiss
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveContent(flag);
                      }}
                      className="font-mono text-xs text-red-400 hover:bg-red-500/20 hover:text-red-300 cursor-pointer"
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      Remove Content
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}

      {/* Enhanced Load More Button with Theme Integration */}
      {displayedContent.length < filteredAndSortedContent.length && (
        <div className="flex justify-center pt-4">
          <Button
            onClick={handleLoadMore}
            className={cn(
              "px-6 py-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:scale-105 border-2",
              isDarkBackground
                ? 'bg-gradient-to-r from-gray-800/90 to-gray-700/90 text-gray-200 border-gray-600/60 shadow-lg hover:from-gray-700/95 hover:to-gray-600/95 hover:border-gray-500/70'
                : 'bg-gradient-to-r from-blue-50/90 to-purple-50/90 text-blue-700 border-blue-300/60 shadow-md hover:from-blue-100/90 hover:to-purple-100/90 hover:border-blue-400/70'
            )}
            style={{
              backdropFilter: 'blur(8px)',
              boxShadow: isDarkBackground
                ? '0 4px 12px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.1)'
                : '0 4px 12px rgba(59,130,246,0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
            }}
          >
            <span className="flex items-center gap-2">
              <ChevronDown className="h-4 w-4" />
              Load More ({filteredAndSortedContent.length - displayedContent.length} remaining)
            </span>
          </Button>
        </div>
      )}

      {/* Remove Content Dialog */}
      <AlertDialog open={removeDialogOpen} onOpenChange={setRemoveDialogOpen}>
        <AlertDialogContent className="bg-slate-900/95 border-slate-700 backdrop-blur-sm">
          <AlertDialogHeader>
            <AlertDialogTitle className="font-mono text-slate-200">
              Remove Flagged Content
            </AlertDialogTitle>
            <AlertDialogDescription className="font-mono text-sm text-slate-400">
              This will permanently remove the flagged content. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="ban-user"
                checked={shouldBanUser}
                onCheckedChange={(checked) => setShouldBanUser(checked as boolean)}
              />
              <label htmlFor="ban-user" className="text-sm font-mono text-slate-300">
                Also ban the user from commenting on your content
              </label>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel className="font-mono text-xs">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveContent}
              className="bg-red-600 hover:bg-red-700 font-mono text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Remove Content
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
