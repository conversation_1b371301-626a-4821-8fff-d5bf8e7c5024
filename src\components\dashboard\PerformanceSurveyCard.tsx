'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Monitor,
  Gamepad2,
  Laptop,
  Gauge,
  Trash2,
  Calendar,
  Eye,
  EyeOff,
  MoreVertical,
  Shield,
  Cpu,
  HardDrive,
  Zap,
  Activity,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
import type { PerformanceSurveyRecord } from '@/lib/services/performanceSurveyService';

interface PerformanceSurveyCardProps {
  survey: PerformanceSurveyRecord;
  onDelete?: (surveyId: string) => Promise<void>;
  onPrivacyToggle?: (surveyId: string, isPrivate: boolean) => Promise<void>;
  className?: string;
  fullWidth?: boolean;
}

export function PerformanceSurveyCard({
  survey,
  onDelete,
  onPrivacyToggle,
  className,
  fullWidth = false
}: PerformanceSurveyCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdatingPrivacy, setIsUpdatingPrivacy] = useState(false);

  const handleDelete = async () => {
    if (!onDelete || !survey.id) return;
    
    setIsDeleting(true);
    try {
      await onDelete(survey.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting survey:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePrivacyToggle = async () => {
    if (!onPrivacyToggle || !survey.id) return;

    setIsUpdatingPrivacy(true);
    try {
      await onPrivacyToggle(survey.id, !(survey as any).is_private);
    } catch (error) {
      console.error('Error updating privacy:', error);
    } finally {
      setIsUpdatingPrivacy(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDeviceIcon = () => {
    switch (survey.device_type) {
      case 'desktop':
        return <Monitor className="text-blue-400" size={16} />;
      case 'laptop':
        return <Laptop className="text-green-400" size={16} />;
      case 'handheld':
        return <Gamepad2 className="text-purple-400" size={16} />;
      default:
        return <Monitor className="text-gray-400" size={16} />;
    }
  };

  const getPerformanceColor = (fps?: number | null) => {
    if (!fps) return 'text-gray-400';
    if (fps >= 60) return 'text-green-400';
    if (fps >= 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  // Compact view for old layout
  if (!fullWidth) {
    return (
      <>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          whileHover={{ y: -2 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "bg-slate-900/60 border border-slate-700/50 rounded-lg p-4 hover:border-slate-600/50 transition-all duration-200 backdrop-blur-sm focus-within:ring-2 focus-within:ring-purple-500/50",
            className
          )}
          role="article"
          aria-label={`Performance survey for ${survey.game_title || 'Unknown Game'}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getDeviceIcon()}
              <div>
                <h3 className="font-semibold text-slate-200">
                  {survey.game_title || 'Unknown Game'}
                </h3>
                <div className="flex items-center gap-2 text-xs text-slate-400">
                  <Calendar size={12} />
                  <span>{formatDate(survey.created_at)}</span>
                  <span className="text-slate-600">•</span>
                  <span className="capitalize">{survey.platform || 'Unknown'}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="bg-slate-800/50 rounded px-2 py-1">
                <div className={cn("font-bold", getPerformanceColor(survey.fps_average))}>
                  {survey.fps_average || 'N/A'} FPS
                </div>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    aria-label="Survey actions menu"
                  >
                    <MoreVertical size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem
                    onClick={handlePrivacyToggle}
                    disabled={isUpdatingPrivacy}
                  >
                    {(survey as any).is_private ? (
                      <>
                        <Eye size={16} className="mr-2" />
                        Make Public
                      </>
                    ) : (
                      <>
                        <EyeOff size={16} className="mr-2" />
                        Make Private
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {(survey as any).is_private && (
            <div className="mt-2">
              <span className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs flex items-center gap-1 w-fit">
                <Shield size={12} />
                Private
              </span>
            </div>
          )}
        </motion.div>
      </>
    );
  }

  // Redesigned compact full-width layout
  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.002, boxShadow: "0 8px 32px rgba(139, 92, 246, 0.15)" }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className={cn(
          "relative bg-gradient-to-r from-slate-900/70 via-slate-900/60 to-slate-800/70 border border-slate-700/50 rounded-xl p-3 hover:border-violet-400/60 transition-all duration-300 backdrop-blur-sm overflow-hidden group cursor-pointer",
          className
        )}
        role="article"
        aria-label={`Performance survey for ${survey.game_title || 'Unknown Game'}`}
      >
        {/* Subtle animated background */}
        <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-transparent to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-violet-500/10 via-transparent to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        
        {/* Main Content Grid */}
        <div className="relative z-10 grid grid-cols-12 gap-3 items-center">
          
          {/* Left Section: Game Info (3 columns) */}
          <div className="col-span-12 md:col-span-3 flex items-center gap-2">
            <motion.div 
              whileHover={{ scale: 1.1, rotate: 5 }}
              className="p-1.5 rounded-lg bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-slate-600/40 shadow-lg"
            >
              {getDeviceIcon()}
            </motion.div>
            <div className="min-w-0 flex-1">
              <h3 className="text-sm font-bold text-white truncate bg-gradient-to-r from-slate-100 to-slate-200 bg-clip-text text-transparent">
                {survey.game_title || 'Unknown Game'}
              </h3>
              <div className="flex items-center gap-1.5 text-[10px] text-slate-400 font-mono">
                <Calendar size={8} className="text-violet-400 flex-shrink-0" />
                <span className="truncate">{formatDate(survey.created_at)}</span>
              </div>
            </div>
          </div>

          {/* Center Section: Performance Metrics (6 columns) */}
          <div className="col-span-12 md:col-span-6 grid grid-cols-4 gap-2">
            
            {/* FPS - Primary metric */}
            <motion.div 
              whileHover={{ scale: 1.05, y: -2 }}
              className="group/metric relative bg-slate-800/30 rounded-lg p-2 text-center border border-slate-700/30 hover:border-green-500/50 transition-all duration-200"
            >
              <div className="text-[8px] text-slate-400 mb-0.5 uppercase tracking-wider font-mono font-semibold">FPS</div>
              <div className={cn("text-base font-bold font-mono tabular-nums", getPerformanceColor(survey.fps_average))}>
                {survey.fps_average || '—'}
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-green-500/10 to-transparent rounded-lg opacity-0 group-hover/metric:opacity-100 transition-opacity duration-200" />
            </motion.div>

            {/* Resolution */}
            <motion.div 
              whileHover={{ scale: 1.05, y: -2 }}
              className="group/metric relative bg-slate-800/30 rounded-lg p-2 text-center border border-slate-700/30 hover:border-cyan-500/50 transition-all duration-200"
            >
              <div className="text-[8px] text-slate-400 mb-0.5 uppercase tracking-wider font-mono font-semibold">RES</div>
              <div className="text-[10px] font-semibold text-cyan-400 font-mono leading-tight">
                {survey.resolution ? survey.resolution.replace('x', '×') : '—'}
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-cyan-500/10 to-transparent rounded-lg opacity-0 group-hover/metric:opacity-100 transition-opacity duration-200" />
            </motion.div>

            {/* Frame Gen & Upscaling Combined */}
            <motion.div 
              whileHover={{ scale: 1.05, y: -2 }}
              className="group/metric relative bg-slate-800/30 rounded-lg p-2 text-center border border-slate-700/30 hover:border-emerald-500/50 transition-all duration-200"
            >
              <div className="text-[8px] text-slate-400 mb-0.5 uppercase tracking-wider font-mono font-semibold">TECH</div>
              <div className="flex justify-center gap-1">
                <div className={cn("text-[9px] font-bold font-mono", survey.frame_gen ? "text-emerald-400" : "text-slate-500")}>
                  FG
                </div>
                <div className="text-slate-600">•</div>
                <div className={cn("text-[9px] font-bold font-mono", survey.upscale ? "text-emerald-400" : "text-slate-500")}>
                  UP
                </div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-emerald-500/10 to-transparent rounded-lg opacity-0 group-hover/metric:opacity-100 transition-opacity duration-200" />
            </motion.div>

            {/* Platform & Device Type */}
            <motion.div 
              whileHover={{ scale: 1.05, y: -2 }}
              className="group/metric relative bg-slate-800/30 rounded-lg p-2 text-center border border-slate-700/30 hover:border-purple-500/50 transition-all duration-200"
            >
              <div className="text-[8px] text-slate-400 mb-0.5 uppercase tracking-wider font-mono font-semibold">SYS</div>
              <div className="text-[9px] font-semibold text-purple-400 font-mono capitalize leading-tight">
                {survey.platform || 'Unknown'}
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-purple-500/10 to-transparent rounded-lg opacity-0 group-hover/metric:opacity-100 transition-opacity duration-200" />
            </motion.div>
          </div>

          {/* Right Section: Hardware & Actions (3 columns) */}
          <div className="col-span-12 md:col-span-3 flex items-center justify-between gap-2">
            
            {/* Hardware Quick Info */}
            <div className="flex-1 min-w-0">
              {/* Memory */}
              {survey.total_memory && (
                <div className="text-[10px] text-blue-400 font-mono font-semibold">
                  {survey.total_memory}GB RAM
                </div>
              )}
              
              {/* CPU/GPU Info (abbreviated) */}
              <div className="text-[9px] text-slate-400 font-mono truncate">
                {survey.cpu && (
                  <span className="text-red-300">
                    {survey.cpu.split(' ').slice(0, 2).join(' ')}
                  </span>
                )}
                {survey.cpu && survey.gpu && <span className="text-slate-600 mx-1">•</span>}
                {survey.gpu && (
                  <span className="text-green-300">
                    {survey.gpu.split(' ').slice(0, 2).join(' ')}
                  </span>
                )}
              </div>
            </div>

            {/* Status Indicators & Actions */}
            <div className="flex items-center gap-1.5">
              
              {/* Ultrawide Badge */}
              {survey.ultrawide && (
                <motion.span 
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.1 }}
                  className="bg-gradient-to-r from-purple-500/20 to-indigo-500/20 text-purple-300 px-1.5 py-0.5 rounded text-[8px] font-mono font-bold border border-purple-500/30"
                >
                  UW
                </motion.span>
              )}

              {/* Private Badge */}
              {(survey as any).is_private && (
                <motion.span 
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.1 }}
                  className="bg-gradient-to-r from-orange-500/20 to-amber-500/20 text-orange-400 px-1.5 py-0.5 rounded text-[8px] flex items-center gap-1 font-mono font-bold border border-orange-500/30"
                >
                  <Shield size={8} />
                  PVT
                </motion.span>
              )}
              
              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-violet-500/20 hover:border-violet-400/50 border border-transparent rounded-md transition-all duration-200"
                      aria-label="Survey actions menu"
                    >
                      <MoreVertical size={12} className="text-slate-400 hover:text-violet-300" />
                    </Button>
                  </motion.div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem
                    onClick={handlePrivacyToggle}
                    disabled={isUpdatingPrivacy}
                  >
                    {(survey as any).is_private ? (
                      <>
                        <Eye size={16} className="mr-2" />
                        Make Public
                      </>
                    ) : (
                      <>
                        <EyeOff size={16} className="mr-2" />
                        Make Private
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Hover Info Overlay - Shows additional details on hover */}
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          whileHover={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.2 }}
          className="absolute bottom-1 left-3 right-3 bg-slate-900/90 backdrop-blur-sm rounded-lg px-2 py-1 border border-slate-700/50 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none"
        >
          <div className="grid grid-cols-3 gap-2 text-[9px] font-mono text-slate-300">
            <div>
              <span className="text-slate-400">Smooth:</span> <span className="text-yellow-400">{survey.smoothness ? `${survey.smoothness}/3` : '—'}</span>
            </div>
            {survey.frame_gen_type && (
              <div>
                <span className="text-slate-400">FG:</span> <span className="text-emerald-400">{survey.frame_gen_type}</span>
              </div>
            )}
            {survey.upscale_type && (
              <div>
                <span className="text-slate-400">UP:</span> <span className="text-emerald-400">{survey.upscale_type}</span>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>

      {/* Delete Confirmation Dialog - Hidden but kept for future reference */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hide Performance Survey</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to hide this performance survey for "{survey.game_title}"?
              You can restore it from your privacy settings.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? 'Hiding...' : 'Hide'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}