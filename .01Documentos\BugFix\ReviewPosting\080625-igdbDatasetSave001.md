# Bug Fix: IGDB Dataset Not Creating Proper Dataset

**Date:** 06/01/2025  
**Issue ID:** igdbDatasetSave001  
**Severity:** High  
**Status:** Fixed  

## Problem Description

O sistema não estava a criar um dataset apropriado na base de dados quando o utilizador selecionava um jogo da pesquisa IGDB. Os dados obtidos da IGDB API eram exibidos corretamente no componente `TitleYourQuest.tsx`, mas não estavam a ser guardados adequadamente na base de dados nem mostrados corretamente nas páginas de review `/reviews/view/[slug]`.

## Root Cause Analysis

### 1. **Estruturação Incorreta de Dados no Formulário**
No ficheiro `src/app/reviews/new/page.tsx` (linhas 779-792), os dados do IGDB estavam a ser incluídos como campos individuais no `reviewData`:

```typescript
// ANTES (Problemático)
...(typeof igdbId === 'number' && { igdbId }),
...(igdbSummary && { summary: igdbSummary }),
...(typeof igdbAggregatedRating === 'number' && { aggregatedRating: igdbAggregatedRating }),
...(igdbDevelopers.length > 0 && { developers: igdbDevelopers }),
```

### 2. **Interface ReviewFormData Não Correspondente**
A interface `ReviewFormData` em `src/lib/review-service.ts` esperava:
- `igdbId?: number`
- `igdbData?: any` (objeto completo)

Mas estava a receber campos individuais dispersos.

### 3. **Função ensureGameExists Não Executada**
A função `createReview` só criava/encontrava jogos na base de dados se ambos `formData.igdbId` E `formData.igdbData` estivessem presentes. Como `igdbData` não estava a ser passado corretamente, a função nunca executava.

### 4. **Dados do IGDB Incorretos no getReviewBySlug**
O select na função `getReviewBySlug` não incluía todos os campos necessários da tabela `games` e alguns mappings estavam incorretos.

## Solution Applied

### 1. **Reestruturação dos Dados do IGDB no Formulário**
Modificado `src/app/reviews/new/page.tsx` para estruturar corretamente os dados:

```typescript
// DEPOIS (Correto)
...(typeof igdbId === 'number' && { 
  igdbId,
  igdbData: {
    id: igdbId,
    name: gameName,
    summary: igdbSummary || undefined,
    aggregated_rating: typeof igdbAggregatedRating === 'number' ? igdbAggregatedRating : undefined,
    aggregated_rating_count: typeof igdbAggregatedRatingCount === 'number' ? igdbAggregatedRatingCount : undefined,
    first_release_date: releaseDate ? Math.floor(releaseDate.getTime() / 1000) : undefined,
    developers: igdbDevelopers.length > 0 ? igdbDevelopers.map(dev => ({ name: dev })) : undefined,
    publishers: igdbPublishers.length > 0 ? igdbPublishers.map(pub => ({ name: pub })) : undefined,
    game_engines: igdbGameEngines.length > 0 ? igdbGameEngines : undefined,
    player_perspectives: igdbPlayerPerspectives.length > 0 ? igdbPlayerPerspectives : undefined,
    time_to_beat: (typeof igdbTimeToBeatNormally === 'number' || typeof igdbTimeToBeatCompletely === 'number') ? {
      ...(typeof igdbTimeToBeatNormally === 'number' && { normally: igdbTimeToBeatNormally }),
      ...(typeof igdbTimeToBeatCompletely === 'number' && { completely: igdbTimeToBeatCompletely })
    } : undefined,
    cover: igdbCoverUrl ? { url: igdbCoverUrl } : undefined,
    platforms: Array.from(selectedPlatforms).length > 0 ? Array.from(selectedPlatforms).map(platform => ({ name: platform })) : undefined,
    genres: Array.from(selectedGenres).length > 0 ? Array.from(selectedGenres).map(genre => ({ name: genre })) : undefined
  }
}),
```

### 2. **Correção do Select na getReviewBySlug**
Modificado `src/lib/review-service.ts` para incluir todos os campos necessários da tabela `games`:

```typescript
games:game_id (
  name,
  cover_url,
  igdb_id,
  summary,
  genres,
  platforms,
  developers,
  publishers,
  game_engines,
  player_perspectives,
  aggregated_rating,
  aggregated_rating_count,
  time_to_beat_normally,
  time_to_beat_completely,
  release_date
)
```

### 3. **Mapeamento Completo dos Dados do IGDB**
Adicionado o mapeamento completo dos dados do IGDB na função `getReviewBySlug`:

```typescript
aggregatedRating: review.games?.aggregated_rating,
aggregatedRatingCount: review.games?.aggregated_rating_count,
timeToBeatNormally: review.games?.time_to_beat_normally,
timeToBeatCompletely: review.games?.time_to_beat_completely,
releaseDate: review.games?.release_date ? new Date(review.games.release_date) : undefined
```

## Testing

### 1. **Fluxo de Criação de Review**
- ✅ Selecionar jogo da pesquisa IGDB
- ✅ Verificar que os dados são exibidos corretamente no TitleYourQuest
- ✅ Criar review e verificar que o jogo é criado/encontrado na tabela `games`
- ✅ Verificar que todos os metadados do IGDB são salvos

### 2. **Fluxo de Visualização de Review**
- ✅ Aceder à página `/reviews/view/[slug]`
- ✅ Verificar que todos os dados do IGDB são carregados e exibidos
- ✅ Verificar metadados como desenvolvedores, publishers, ratings, etc.

## Dependencies

- ✅ IGDB API integration: `src/lib/igdb-api.ts`
- ✅ Database schema: Tabela `games` com todos os campos necessários
- ✅ Review service: `src/lib/review-service.ts`
- ✅ Review form: `src/app/reviews/new/page.tsx`

## Impact Assessment

**Before Fix:**
- Dados do IGDB não eram salvos na base de dados
- Reviews não tinham informações de jogo associadas
- Metadados do IGDB não apareciam nas páginas de review

**After Fix:**
- ✅ Dados do IGDB são corretamente estruturados e salvos
- ✅ Jogos são criados/encontrados na tabela `games` com todos os metadados
- ✅ Reviews exibem informações completas do IGDB
- ✅ Metadata como developers, publishers, ratings são preservados

## Related Files Modified

1. `src/app/reviews/new/page.tsx` - Correção da estruturação dos dados IGDB
2. `src/lib/review-service.ts` - Correção do select e mapeamento na getReviewBySlug

## Future Improvements

1. Adicionar validação mais rigorosa para os dados do IGDB
2. Implementar cache para evitar chamadas desnecessárias à IGDB API
3. Adicionar logs de debug para tracking de criação de jogos
4. Considerar sync periódico para atualizar dados do IGDB

---

**Developer:** Claude Sonnet 4 (Microsoft Senior Bug Fixer)  
**Review Status:** ✅ Fixed and Tested  
**Documentation:** Complete 