import React from 'react';

const VideoPlayIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <rect x="2" y="4" width="20" height="16" rx="2" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <rect x="4" y="6" width="16" height="12" rx="1"/>
    <path d="M10 8l6 4-6 4V8z"/>
    <rect x="8" y="2" width="8" height="1"/>
    <rect x="8" y="21" width="8" height="1"/>
    <circle cx="6" cy="8" r="0.5"/>
    <circle cx="18" cy="8" r="0.5"/>
    <circle cx="6" cy="16" r="0.5"/>
    <circle cx="18" cy="16" r="0.5"/>
  </svg>
);

export default VideoPlayIcon;