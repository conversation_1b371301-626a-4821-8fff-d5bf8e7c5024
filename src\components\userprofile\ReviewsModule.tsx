'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Star, 
  Trophy, 
  Eye, 
  MessageSquare, 
  Heart, 
  Gamepad2,
  Calendar,
  Grid,
  List,
  Search,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUserContent } from '@/hooks/useUserContent';

// Tipos para os dados de reviews
interface UserReview {
  id: string;
  game_name: string;
  game_image?: string;
  igdb_cover_url?: string;
  rating: number;
  review_text: string;
  created_at: string;
  updated_at?: string;
  likes_count: number;
  views_count: number;
  comments_count: number;
  is_featured: boolean;
  platform?: string;
  playtime_hours?: number;
  tags?: string[];
}

interface ReviewsModuleProps {
  userId: string;
  currentUserId?: string;
  isOwnProfile?: boolean;
  theme?: any;
}

// Componente para exibir reviews em grid moderno
const ReviewsGrid = ({ reviews, theme }: { reviews: UserReview[]; theme: any }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    {reviews.map((review) => (
      <motion.div
        key={review.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5 }}
        className="group"
      >
        <Card className="bg-gray-900/50 border-gray-800/50 backdrop-blur-sm overflow-hidden h-full">
          <CardContent className="p-0">
            {/* Header com imagem do jogo */}
            <div className="relative">
              {review.game_image ? (
                <img 
                  src={review.game_image} 
                  alt={review.game_name}
                  className="w-full h-32 object-cover"
                />
              ) : (
                <div 
                  className="w-full h-32 flex items-center justify-center"
                  style={{ backgroundColor: `${theme?.colors?.primary}30` }}
                >
                  <Gamepad2 className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="absolute top-2 right-2 flex gap-2">
                {review.is_featured && (
                  <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                    <Trophy className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                <Badge variant="secondary" className="bg-gray-900/80">
                  {review.rating}/5 ⭐
                </Badge>
              </div>
            </div>

            {/* Conteúdo */}
            <div className="p-4 space-y-3">
              <div>
                <h3 className="font-semibold text-white truncate">{review.game_name}</h3>
                {review.platform && (
                  <Badge variant="outline" className="text-xs mt-1">
                    {review.platform}
                  </Badge>
                )}
              </div>

              <p className="text-sm text-gray-300 line-clamp-3">
                {review.review_text}
              </p>

              {/* Tags */}
              {review.tags && review.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {review.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Estatísticas */}
              <div className="flex items-center justify-between text-xs text-gray-400">
                <div className="flex items-center gap-3">
                  <span className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    {review.views_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <Heart className="h-3 w-3" />
                    {review.likes_count}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    {review.comments_count}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{new Date(review.created_at).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Playtime se disponível */}
              {review.playtime_hours && (
                <div className="text-xs text-gray-500">
                  Jogado por {review.playtime_hours}h
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    ))}
  </div>
);

// Componente para exibir estatísticas das reviews
const ReviewsStats = ({ stats, theme }: { stats: any; theme: any }) => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
    {[
      { label: 'Total Reviews', value: stats.reviewsCount, icon: Star, color: 'text-yellow-400' },
      { label: 'Average Rating', value: `${stats.averageRating}/5`, icon: Trophy, color: 'text-amber-400' },
      { label: 'Total Views', value: stats.totalViews, icon: Eye, color: 'text-green-400' },
      { label: 'Total Likes', value: stats.totalLikes, icon: Heart, color: 'text-red-400' },
    ].map((stat) => (
      <motion.div
        key={stat.label}
        whileHover={{ scale: 1.02 }}
        className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50 backdrop-blur-sm"
      >
        <div className="flex items-center gap-3">
          <div 
            className="p-2 rounded-lg"
            style={{ backgroundColor: `${theme?.colors?.primary}20` }}
          >
            <stat.icon className={cn("h-4 w-4", stat.color)} />
          </div>
          <div>
            <p className="text-2xl font-bold text-white">{stat.value}</p>
            <p className="text-xs text-gray-400">{stat.label}</p>
          </div>
        </div>
      </motion.div>
    ))}
  </div>
);

// Componente principal do módulo de reviews
export default function ReviewsModule({
  userId,
  currentUserId,
  isOwnProfile = false,
  theme
}: ReviewsModuleProps) {
  // ALL HOOKS MUST BE AT THE TOP
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'rating' | 'views'>('date');

  // Use the real useUserContent hook
  const {
    data,
    isLoading,
    error,
    stats
  } = useUserContent(userId, currentUserId);

  // Filter and sort reviews based on search and sort criteria
  const filteredReviews = useMemo(() => {
    if (!data?.reviews) return [];
    
    let filtered = data.reviews.filter(review => 
      !searchTerm || 
      review.game_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.review_text.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'views':
          return b.views_count - a.views_count;
        case 'date':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [data?.reviews, searchTerm, sortBy]);

  // Prepare stats for display
  const reviewsStats = useMemo(() => {
    const reviews = filteredReviews;
    const averageRating = reviews.length > 0 
      ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1)
      : '0.0';
    
    return {
      reviewsCount: reviews.length,
      averageRating,
      totalViews: reviews.reduce((sum, review) => sum + review.views_count, 0),
      totalLikes: reviews.reduce((sum, review) => sum + review.likes_count, 0),
    };
  }, [filteredReviews]);

  // Handle loading and error states AFTER all hooks
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12 text-red-400">
          <p>Error loading reviews: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Título da Seção */}
      <div className="flex items-center gap-3">
        <div 
          className="p-2 rounded-lg"
          style={{ backgroundColor: `${theme?.colors?.primary}20` }}
        >
          <Star className="h-5 w-5" style={{ color: theme?.colors?.primary }} />
        </div>
        <div>
          <h2 className="text-xl font-bold text-white font-mono">
            <span style={{ color: theme?.colors?.accent }}>&lt;</span>
            {isOwnProfile ? 'Minhas Reviews' : 'Reviews do Usuário'}
            <span style={{ color: theme?.colors?.accent }}>/&gt;</span>
          </h2>
          <p className="text-sm text-gray-400">
            Análises e avaliações de jogos
          </p>
        </div>
      </div>

      {/* Estatísticas das Reviews */}
      <ReviewsStats stats={reviewsStats} theme={theme} />

      {/* Search and Filter Controls */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Buscar reviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
            />
          </div>
          <Select value={sortBy} onValueChange={(value: 'date' | 'rating' | 'views') => setSortBy(value)}>
            <SelectTrigger className="w-full sm:w-48 bg-gray-800/50 border-gray-700 text-white">
              <SelectValue placeholder="Ordenar por" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Mais Recentes</SelectItem>
              <SelectItem value="rating">Melhor Avaliação</SelectItem>
              <SelectItem value="views">Mais Visualizadas</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Controles de Visualização */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            {filteredReviews.length} {filteredReviews.length === 1 ? 'review encontrada' : 'reviews encontradas'}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 bg-gray-900/50 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                aria-label="Visualizar em grade"
                className={cn(
                  "p-2 rounded transition-colors",
                  viewMode === 'grid' ? "bg-gray-800 text-white" : "text-gray-400 hover:text-white"
                )}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                aria-label="Visualizar em lista"
                className={cn(
                  "p-2 rounded transition-colors",
                  viewMode === 'list' ? "bg-gray-800 text-white" : "text-gray-400 hover:text-white"
                )}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo das Reviews */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`reviews-${searchTerm}-${sortBy}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {filteredReviews.length > 0 ? (
            <ReviewsGrid reviews={filteredReviews} theme={theme} />
          ) : (
            <div className="text-center py-12 text-gray-400">
              <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">
                {searchTerm ? 'Nenhuma review encontrada' : 'Nenhuma review publicada'}
              </p>
              <p className="text-sm">
                {searchTerm 
                  ? 'Tente ajustar os filtros de busca' 
                  : isOwnProfile 
                    ? 'Comece escrevendo sua primeira review!' 
                    : 'Este usuário ainda não publicou reviews'
                }
              </p>
              {searchTerm && (
                <button 
                  onClick={() => setSearchTerm('')}
                  className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Limpar busca
                </button>
              )}
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </motion.div>
  );
} 