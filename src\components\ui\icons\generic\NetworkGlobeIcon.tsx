import React from 'react';

const NetworkGlobeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M12 2c2.5 0 4.5 4.5 4.5 10s-2 10-4.5 10-4.5-4.5-4.5-10S9.5 2 12 2z" stroke="currentColor" strokeWidth="1.5" fill="none"/>
    <path d="M2 12h20" stroke="currentColor" strokeWidth="1.5"/>
    <path d="M4.5 7h15" stroke="currentColor" strokeWidth="1"/>
    <path d="M4.5 17h15" stroke="currentColor" strokeWidth="1"/>
    <circle cx="6" cy="6" r="0.5"/>
    <circle cx="18" cy="6" r="0.5"/>
    <circle cx="6" cy="18" r="0.5"/>
    <circle cx="18" cy="18" r="0.5"/>
    <circle cx="3" cy="12" r="0.5"/>
    <circle cx="21" cy="12" r="0.5"/>
  </svg>
);

export default NetworkGlobeIcon;