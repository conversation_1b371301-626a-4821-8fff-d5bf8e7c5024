// 🧠 COMPONENTES NEUROCOGNITIVOS - CRITICALPIXEL
// Dr. <PERSON><PERSON> | Implementação Prática

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// ===== SISTEMA DE CORES NEUROCIENTÍFICO =====
export const neuralColors = {
  primary: '#00D4FF',
  secondary: '#7C3AED',
  bg: {
    primary: '#0A0A0B',
    secondary: '#1A1A1C',
    neural: 'linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(124, 58, 237, 0.05) 100%)'
  },
  text: {
    primary: '#F5F5F7',
    secondary: '#A1A1AA'
  },
  feedback: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6'
  }
};

// ===== BOTÃO NEURAL COM FEEDBACK HÁPTICO =====
interface NeuralButtonProps {
  variant?: 'primary' | 'ghost' | 'achievement';
  size?: 'sm' | 'md' | 'lg';
  cognitiveLoad?: 'low' | 'medium' | 'high';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
}

export const NeuralButton: React.FC<NeuralButtonProps> = ({
  variant = 'primary',
  size = 'md',
  cognitiveLoad = 'low',
  children,
  onClick,
  disabled = false
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [achievementTriggered, setAchievementTriggered] = useState(false);

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  const variantClasses = {
    primary: 'bg-[#00D4FF] text-black hover:bg-[#00B8E6]',
    ghost: 'bg-transparent border border-[#00D4FF]/30 text-[#00D4FF] hover:bg-[#00D4FF]/10',
    achievement: 'bg-gradient-to-r from-[#00D4FF] to-[#7C3AED] text-white'
  };

  const cognitiveLoadEffects = {
    low: { scale: 1.02, duration: 0.2 },
    medium: { scale: 1.05, duration: 0.3 },
    high: { scale: 1.08, duration: 0.4 }
  };

  const handleClick = () => {
    if (disabled) return;
    
    setIsPressed(true);
    setTimeout(() => setIsPressed(false), 150);
    
    if (variant === 'achievement') {
      setAchievementTriggered(true);
      setTimeout(() => setAchievementTriggered(false), 600);
    }
    
    onClick?.();
  };

  return (
    <motion.button
      className={cn(
        "relative font-medium rounded-xl transition-all duration-200",
        "focus:outline-none focus:ring-2 focus:ring-[#00D4FF]/50",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        sizeClasses[size],
        variantClasses[variant]
      )}
      whileHover={!disabled ? {
        scale: cognitiveLoadEffects[cognitiveLoad].scale,
        boxShadow: "0 0 20px rgba(0, 212, 255, 0.3)"
      } : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      animate={achievementTriggered ? {
        scale: [1, 1.2, 1],
        rotate: [0, -5, 5, 0]
      } : {}}
      transition={{ 
        duration: cognitiveLoadEffects[cognitiveLoad].duration,
        ease: "easeInOut"
      }}
      onClick={handleClick}
      disabled={disabled}
    >
      {/* Gradiente neural de fundo */}
      <motion.div
        className="absolute inset-0 rounded-xl opacity-0"
        style={{ 
          background: neuralColors.bg.neural 
        }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
      
      {/* Indicador de carga cognitiva */}
      {cognitiveLoad !== 'low' && (
        <motion.div
          className="absolute top-1 right-1 w-2 h-2 rounded-full"
          style={{
            backgroundColor: cognitiveLoad === 'medium' ? '#F59E0B' : '#EF4444'
          }}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      )}
      
      <span className="relative z-10">{children}</span>
    </motion.button>
  );
};

// ===== CARD COGNITIVO COM HIERARQUIA VISUAL =====
interface CognitiveCardProps {
  priority?: 'low' | 'medium' | 'high';
  cognitiveWeight?: number; // 1-10
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const CognitiveCard: React.FC<CognitiveCardProps> = ({
  priority = 'medium',
  cognitiveWeight = 5,
  children,
  className,
  onClick
}) => {
  const priorityStyles = {
    high: 'ring-2 ring-[#00D4FF]/50 bg-gradient-to-br from-[#00D4FF]/10 to-[#7C3AED]/5',
    medium: 'ring-1 ring-[#A1A1AA]/20 bg-[#1A1A1C]/50',
    low: 'border border-[#A1A1AA]/10 bg-[#1A1A1C]/30'
  };

  // Calcula opacidade baseada no peso cognitivo
  const opacity = Math.max(0.3, Math.min(1, cognitiveWeight / 10));

  return (
    <motion.div
      className={cn(
        "rounded-2xl p-6 backdrop-blur-sm cursor-pointer",
        "transition-all duration-300",
        priorityStyles[priority],
        className
      )}
      style={{ opacity }}
      whileHover={{ 
        y: -2,
        scale: 1.01,
        boxShadow: priority === 'high' ? "0 10px 30px rgba(0, 212, 255, 0.2)" : "0 5px 15px rgba(0, 0, 0, 0.1)"
      }}
      whileTap={{ scale: 0.99 }}
      layout
      onClick={onClick}
    >
      {/* Indicador de peso cognitivo */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex space-x-1">
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="w-1 h-3 rounded-full"
              style={{
                backgroundColor: i < cognitiveWeight ? neuralColors.primary : '#A1A1AA',
                opacity: i < cognitiveWeight ? 1 : 0.3
              }}
              initial={{ scaleY: 0 }}
              animate={{ scaleY: 1 }}
              transition={{ delay: i * 0.05 }}
            />
          ))}
        </div>
        
        {priority === 'high' && (
          <motion.div
            className="text-xs text-[#00D4FF] bg-[#00D4FF]/20 px-2 py-1 rounded-full"
            animate={{ opacity: [0.6, 1, 0.6] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            PRIORITY
          </motion.div>
        )}
      </div>
      
      {children}
    </motion.div>
  );
};

// ===== QUICK REVIEW FLOW =====
interface QuickReviewFlowProps {
  onComplete?: (reviewData: any) => void;
  onCancel?: () => void;
}

export const QuickReviewFlow: React.FC<QuickReviewFlowProps> = ({
  onComplete,
  onCancel
}) => {
  const [step, setStep] = useState(1);
  const [selectedGame, setSelectedGame] = useState<string>('');
  const [rating, setRating] = useState<number>(0);
  const [quickThoughts, setQuickThoughts] = useState<string>('');

  const totalSteps = 3;
  const progressPercentage = (step / totalSteps) * 100;

  const handleGameSelect = (game: string) => {
    setSelectedGame(game);
    setStep(2);
  };

  const handleRatingSelect = (selectedRating: number) => {
    setRating(selectedRating);
    setStep(3);
  };

  const handleSubmit = () => {
    const reviewData = {
      game: selectedGame,
      rating,
      thoughts: quickThoughts,
      timestamp: new Date().toISOString()
    };
    onComplete?.(reviewData);
  };

  return (
    <motion.div
      className="max-w-2xl mx-auto bg-[#1A1A1C] rounded-3xl p-8 relative overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      {/* Barra de progresso neural */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-[#F5F5F7] font-medium">Quick Review</span>
          <span className="text-[#A1A1AA] text-sm">{step}/{totalSteps}</span>
        </div>
        <div className="w-full bg-[#A1A1AA]/20 rounded-full h-2">
          <motion.div
            className="h-2 rounded-full bg-gradient-to-r from-[#00D4FF] to-[#7C3AED]"
            initial={{ width: '0%' }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      <AnimatePresence mode="wait">
        {step === 1 && (
          <GameSelectionStep
            key="game-selection"
            onSelect={handleGameSelect}
          />
        )}
        
        {step === 2 && (
          <RatingStep
            key="rating"
            game={selectedGame}
            onSelect={handleRatingSelect}
          />
        )}
        
        {step === 3 && (
          <ThoughtsStep
            key="thoughts"
            game={selectedGame}
            rating={rating}
            value={quickThoughts}
            onChange={setQuickThoughts}
            onSubmit={handleSubmit}
            onCancel={onCancel}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Steps individuais do Quick Review Flow
const GameSelectionStep: React.FC<{ onSelect: (game: string) => void }> = ({ onSelect }) => {
  const popularGames = [
    'Cyberpunk 2077', 'The Witcher 3', 'Elden Ring', 
    'God of War', 'Spider-Man 2', 'Baldur\'s Gate 3'
  ];

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
    >
      <h3 className="text-2xl font-bold text-[#F5F5F7] mb-6">
        Qual jogo você quer avaliar?
      </h3>
      
      <div className="grid grid-cols-2 gap-4 mb-6">
        {popularGames.map((game) => (
          <motion.button
            key={game}
            className="p-4 text-left rounded-xl bg-[#0A0A0B] border border-[#A1A1AA]/20 hover:border-[#00D4FF]/50"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onSelect(game)}
          >
            <span className="text-[#F5F5F7] font-medium">{game}</span>
          </motion.button>
        ))}
      </div>
      
      <input
        type="text"
        placeholder="Ou digite o nome do jogo..."
        className="w-full p-4 bg-[#0A0A0B] border border-[#A1A1AA]/20 rounded-xl text-[#F5F5F7] placeholder-[#A1A1AA] focus:border-[#00D4FF]/50 focus:outline-none"
        onKeyPress={(e) => {
          if (e.key === 'Enter' && e.currentTarget.value.trim()) {
            onSelect(e.currentTarget.value.trim());
          }
        }}
      />
    </motion.div>
  );
};

const RatingStep: React.FC<{ 
  game: string; 
  onSelect: (rating: number) => void; 
}> = ({ game, onSelect }) => {
  const [hoveredRating, setHoveredRating] = useState(0);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="text-center"
    >
      <h3 className="text-2xl font-bold text-[#F5F5F7] mb-2">
        Como você avalia
      </h3>
      <p className="text-[#00D4FF] text-xl mb-8">{game}?</p>
      
      <div className="flex justify-center space-x-4 mb-8">
        {[1, 2, 3, 4, 5].map((rating) => (
          <motion.button
            key={rating}
            className="relative"
            onMouseEnter={() => setHoveredRating(rating)}
            onMouseLeave={() => setHoveredRating(0)}
            onClick={() => onSelect(rating)}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              className="w-16 h-16 rounded-full border-2 flex items-center justify-center font-bold text-xl"
              style={{
                borderColor: (hoveredRating >= rating) ? neuralColors.primary : '#A1A1AA',
                backgroundColor: (hoveredRating >= rating) ? `${neuralColors.primary}20` : 'transparent',
                color: (hoveredRating >= rating) ? neuralColors.primary : '#A1A1AA'
              }}
              animate={{
                boxShadow: (hoveredRating >= rating) ? `0 0 20px ${neuralColors.primary}40` : 'none'
              }}
            >
              {rating}
            </motion.div>
          </motion.button>
        ))}
      </div>
      
      <div className="text-[#A1A1AA] text-sm">
        {hoveredRating > 0 && (
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {hoveredRating === 1 && "Terrível"}
            {hoveredRating === 2 && "Ruim"}
            {hoveredRating === 3 && "Ok"}
            {hoveredRating === 4 && "Bom"}
            {hoveredRating === 5 && "Excelente"}
          </motion.span>
        )}
      </div>
    </motion.div>
  );
};

const ThoughtsStep: React.FC<{
  game: string;
  rating: number;
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onCancel?: () => void;
}> = ({ game, rating, value, onChange, onSubmit, onCancel }) => {
  const [charCount, setCharCount] = useState(0);
  const maxChars = 280;

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (newValue.length <= maxChars) {
      onChange(newValue);
      setCharCount(newValue.length);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
    >
      <div className="mb-6">
        <h3 className="text-xl font-bold text-[#F5F5F7] mb-2">
          Conte-nos mais sobre {game}
        </h3>
        <div className="flex items-center space-x-2 text-[#A1A1AA]">
          <span>Sua nota:</span>
          <div className="flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={`w-3 h-3 rounded-full ${
                  i < rating ? 'bg-[#00D4FF]' : 'bg-[#A1A1AA]/30'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <textarea
          value={value}
          onChange={handleInputChange}
          placeholder="O que você achou do jogo? (opcional)"
          className="w-full h-32 p-4 bg-[#0A0A0B] border border-[#A1A1AA]/20 rounded-xl text-[#F5F5F7] placeholder-[#A1A1AA] focus:border-[#00D4FF]/50 focus:outline-none resize-none"
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-[#A1A1AA] text-sm">
            {charCount}/{maxChars} caracteres
          </span>
          <motion.div
            className="w-8 h-1 bg-[#A1A1AA]/20 rounded-full overflow-hidden"
            initial={{ width: 0 }}
            animate={{ width: 32 }}
          >
            <motion.div
              className="h-full bg-[#00D4FF] rounded-full"
              style={{ width: `${(charCount / maxChars) * 100}%` }}
            />
          </motion.div>
        </div>
      </div>
      
      <div className="flex space-x-4">
        <NeuralButton
          variant="primary"
          size="lg"
          onClick={onSubmit}
          cognitiveLoad="low"
        >
          Publicar Review
        </NeuralButton>
        
        <NeuralButton
          variant="ghost"
          size="lg"
          onClick={onCancel}
          cognitiveLoad="low"
        >
          Cancelar
        </NeuralButton>
      </div>
    </motion.div>
  );
};

// ===== NAVEGAÇÃO PREDITIVA =====
export const PredictiveNav: React.FC = () => {
  const [suggestions, setSuggestions] = useState<Array<{
    id: string;
    icon: string;
    label: string;
    probability: number;
    action: () => void;
  }>>([]);

  // Simula análise comportamental
  useEffect(() => {
    const mockSuggestions = [
      {
        id: 'create-review',
        icon: '✍️',
        label: 'Criar Review',
        probability: 0.8,
        action: () => console.log('Create review')
      },
      {
        id: 'browse-games',
        icon: '🎮',
        label: 'Explorar Jogos',
        probability: 0.6,
        action: () => console.log('Browse games')
      },
      {
        id: 'check-notifications',
        icon: '🔔',
        label: 'Notificações',
        probability: 0.3,
        action: () => console.log('Check notifications')
      }
    ];

    setSuggestions(mockSuggestions.sort((a, b) => b.probability - a.probability));
  }, []);

  return (
    <motion.nav
      className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.5 }}
    >
      <motion.div
        className="flex space-x-3 bg-[#1A1A1C]/90 backdrop-blur-lg rounded-full p-3 border border-[#A1A1AA]/20"
        layout
      >
        {suggestions.map((suggestion, index) => (
          <motion.button
            key={suggestion.id}
            className="relative p-3 rounded-full bg-[#00D4FF]/10 hover:bg-[#00D4FF]/20 transition-colors"
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.9 }}
            onClick={suggestion.action}
            style={{
              order: suggestion.probability > 0.7 ? -1 : index
            }}
            title={suggestion.label}
          >
            <span className="text-xl">{suggestion.icon}</span>
            
            {/* Indicador de probabilidade */}
            <motion.div
              className="absolute -top-1 -right-1 w-3 h-3 rounded-full"
              style={{
                backgroundColor: suggestion.probability > 0.7 ? '#10B981' : 
                                suggestion.probability > 0.5 ? '#F59E0B' : '#A1A1AA'
              }}
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </motion.button>
        ))}
      </motion.div>
    </motion.nav>
  );
};

// ===== EASTER EGG SYSTEM =====
export const EasterEggSystem: React.FC = () => {
  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);
  const [developerMode, setDeveloperMode] = useState(false);
  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      const newSequence = [...konamiSequence, e.code].slice(-10);
      setKonamiSequence(newSequence);

      if (JSON.stringify(newSequence) === JSON.stringify(konamiCode)) {
        setDeveloperMode(true);
        // Trigger developer mode activation
        console.log('🎮 Developer mode activated!');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [konamiSequence]);

  if (!developerMode) return null;

  return (
    <motion.div
      className="fixed top-4 right-4 bg-[#7C3AED] text-white p-3 rounded-lg z-50"
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      transition={{ type: "spring", stiffness: 200 }}
    >
      <div className="flex items-center space-x-2">
        <span className="text-lg">🧠</span>
        <span className="font-mono text-sm">DEV_MODE_ACTIVE</span>
      </div>
    </motion.div>
  );
};

export default {
  NeuralButton,
  CognitiveCard,
  QuickReviewFlow,
  PredictiveNav,
  EasterEggSystem,
  neuralColors
}; 