"use client";

import React, { useState, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { AnimatedBackground } from "./ModernDashboardLayout";
import {
  FileText,
  Gauge,
  Settings,
  Bell,
  Search,
  Plus,
  Star,
  LayoutDashboard
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";

interface SimplifiedDashboardProps {
  children: React.ReactNode;
  user?: {
    displayName?: string;
    userName?: string;
    photoURL?: string | null;
    slug?: string;
  };
  activeTab: string;
  onTabChange: (tab: string) => void;
  stats?: {
    totalReviews: number;
    totalSurveys: number;
    averageScore: number;
    publishedReviews: number;
    draftReviews: number;
  };
  onCreateReview?: () => void;
  onViewProfile?: () => void;
  notifications?: number;
}

export function SimplifiedDashboard({
  children,
  user,
  activeTab,
  onTabChange,
  stats,
  onCreateReview,
  onViewProfile,
  notifications = 0
}: SimplifiedDashboardProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");

  // Handle URL tab parameter
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && tabFromUrl !== activeTab) {
      onTabChange(tabFromUrl);
    }
  }, [searchParams, activeTab, onTabChange]);

  // Tab configuration
  const tabs = [
    {
      id: "overview",
      label: "Overview",
      icon: <LayoutDashboard className="w-4 h-4" />
    },
    {
      id: "reviews",
      label: "Reviews",
      icon: <FileText className="w-4 h-4" />
    },
    {
      id: "performance",
      label: "Performance",
      icon: <Gauge className="w-4 h-4" />
    },
    {
      id: "settings",
      label: "Settings",
      icon: <Settings className="w-4 h-4" />
    }
  ];

  const handleTabChange = useCallback((tab: string) => {
    onTabChange(tab);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tab);
    window.history.pushState({}, '', url.toString());
  }, [onTabChange]);

  return (
    <div className="min-h-screen">
      {/* Main Container */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-2">
                Welcome back, {user?.displayName || user?.userName}
              </h1>
              <p className="text-slate-600 dark:text-slate-400 text-sm">
                Here's what's happening with your gaming content.
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative hidden sm:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <input
                  type="text"
                  placeholder="Search your content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-10 w-64 rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800/50 pl-10 pr-4 text-sm text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                />
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={onCreateReview}
                  className="bg-purple-600 hover:bg-purple-700 text-white border-0"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">New Review</span>
                </Button>

                {/* Notifications */}
                <button 
                  type="button" 
                  className="relative h-10 w-10 rounded-lg flex items-center justify-center hover:bg-slate-200 dark:hover:bg-slate-700/50 transition-colors"
                  aria-label="Notifications"
                >
                  <Bell className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                  {notifications > 0 && (
                    <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-xs font-medium text-white flex items-center justify-center">
                      {notifications > 9 ? "9+" : notifications}
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          {stats && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 hover:border-purple-300 dark:hover:border-purple-500/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Total Reviews</p>
                    <p className="mt-1 text-2xl font-semibold text-slate-800 dark:text-slate-200">
                      {stats.totalReviews}
                    </p>
                  </div>
                  <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30">
                    <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <div className="mt-4 text-xs text-slate-500 dark:text-slate-400">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                    +12% from last month
                  </span>
                </div>
              </div>
              
              <div className="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 hover:border-cyan-300 dark:hover:border-cyan-500/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Performance Surveys</p>
                    <p className="mt-1 text-2xl font-semibold text-slate-800 dark:text-slate-200">
                      {stats.totalSurveys}
                    </p>
                  </div>
                  <div className="p-2 rounded-lg bg-cyan-100 dark:bg-cyan-900/30">
                    <Gauge className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
                  </div>
                </div>
                <div className="mt-4 text-xs text-slate-500 dark:text-slate-400">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                    +5 new this week
                  </span>
                </div>
              </div>
              
              <div className="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 hover:border-yellow-300 dark:hover:border-yellow-500/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Average Rating</p>
                    <div className="flex items-center mt-1">
                      <p className="text-2xl font-semibold text-slate-800 dark:text-slate-200">
                        {stats.averageScore > 0 ? stats.averageScore.toFixed(1) : "N/A"}
                      </p>
                      <span className="ml-2 text-sm text-yellow-500">
                        <Star className="h-4 w-4 fill-current inline" />
                      </span>
                    </div>
                  </div>
                  <div className="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/30">
                    <Star className="h-5 w-5 text-yellow-600 dark:text-yellow-400 fill-current" />
                  </div>
                </div>
                <div className="mt-4">
                  <div className="h-1.5 w-full bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-yellow-400 dark:bg-yellow-500" 
                      style={{ width: `${(stats.averageScore / 5) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700/50 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 hover:border-green-300 dark:hover:border-green-500/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Published Reviews</p>
                    <p className="mt-1 text-2xl font-semibold text-slate-800 dark:text-slate-200">
                      {stats.publishedReviews}
                    </p>
                  </div>
                  <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/30">
                    <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="mt-4">
                  <div className="h-1.5 w-full bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-green-400 dark:bg-green-500" 
                      style={{ width: `${(stats.publishedReviews / Math.max(1, stats.totalReviews)) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="bg-white dark:bg-slate-800/50 border border-slate-200 dark:border-slate-700/50 rounded-2xl overflow-hidden shadow-sm">
          {/* Tabs */}
          <div className="border-b border-slate-200 dark:border-slate-700/50">
            <nav className="flex -mb-px overflow-x-auto hide-scrollbar" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={cn(
                    "relative px-4 py-3 text-sm font-medium transition-colors duration-200 focus:outline-none",
                    activeTab === tab.id
                      ? "text-purple-600 dark:text-purple-400"
                      : "text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
                  )}
                  aria-current={activeTab === tab.id ? "page" : undefined}
                >
                  <div className="flex items-center gap-2">
                    <span className={cn(
                      "p-1.5 rounded-md",
                      activeTab === tab.id 
                        ? "bg-purple-100 text-purple-600 dark:bg-purple-900/50 dark:text-purple-400"
                        : "text-slate-500 group-hover:text-purple-500 dark:text-slate-400 dark:group-hover:text-purple-400"
                    )}>
                      {tab.icon}
                    </span>
                    <span>{tab.label}</span>
                  </div>
                  {activeTab === tab.id && (
                    <motion.span 
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-purple-500"
                      layoutId="activeTabIndicator"
                      transition={{
                        type: 'spring',
                        bounce: 0.2,
                        duration: 0.6
                      }}
                    />
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6 bg-white dark:bg-slate-800/50 rounded-b-2xl">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

export default SimplifiedDashboard;
