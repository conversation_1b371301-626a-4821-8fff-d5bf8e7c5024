import { NextRequest, NextResponse } from 'next/server';
import { setFeaturedReview, removeFeaturedReview } from '@/app/u/actions';
import { createServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const contentType = request.headers.get('content-type');
    let action: string, userId: string, reviewId: string;

    if (contentType?.includes('application/json')) {
      // Handle JSON requests from the new component
      const body = await request.json();
      action = body.action;
      userId = body.userId;
      reviewId = body.reviewId;
    } else {
      // Handle FormData requests from the dashboard
      const formData = await request.formData();
      action = formData.get('action') as string;
      userId = formData.get('userId') as string;
      reviewId = formData.get('reviewId') as string;
    }

    console.log('Featured review API called:', { action, userId, reviewId });

    if (!action || !userId) {
      console.log('Missing required parameters:', { action, userId });
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'setFeatured':
        if (!reviewId) {
          console.log('Review ID is required for setFeatured action');
          return NextResponse.json(
            { success: false, error: 'Review ID is required' },
            { status: 400 }
          );
        }
        console.log('Calling setFeaturedReview with:', { userId, reviewId });
        result = await setFeaturedReview(userId, reviewId);
        console.log('setFeaturedReview result:', result);
        break;

      case 'removeFeatured':
        console.log('Calling removeFeaturedReview with:', { userId, reviewId });
        result = await removeFeaturedReview(userId, reviewId);
        console.log('removeFeaturedReview result:', result);
        break;

      case 'getFeatured':
        console.log('Getting featured review for user:', userId);
        try {
          const supabase = await createServerClient();

          const { data, error } = await supabase
            .from('reviews')
            .select(`
              id,
              title,
              game_name,
              main_image_url,
              igdb_cover_url,
              overall_score,
              content_lexical,
              like_count,
              view_count,
              created_at,
              played_on,
              date_played,
              slug,
              games (
                cover_url,
                igdb_id
              )
            `)
            .eq('author_id', userId)
            .eq('is_featured', true)
            .eq('status', 'published')
            .eq('is_blocked', false)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
            console.error('Supabase error:', error);
            return NextResponse.json(
              { success: false, error: error.message },
              { status: 500 }
            );
          }

          console.log('Featured review found:', data);
          return NextResponse.json({ success: true, data });
        } catch (error) {
          console.error('Get featured review error:', error);
          return NextResponse.json(
            { success: false, error: 'Failed to fetch featured review' },
            { status: 500 }
          );
        }

      case 'getStoreLinks':
        console.log('Getting store links for user:', userId);
        try {
          const supabase = await createServerClient();

          const { data, error } = await supabase
            .from('featured_review_store_links')
            .select('*')
            .eq('user_id', userId)
            .eq('is_active', true)
            .order('display_order', { ascending: true });

          if (error) {
            console.error('Store links error:', error);
            return NextResponse.json(
              { success: false, error: error.message },
              { status: 500 }
            );
          }

          console.log('Store links found:', data);
          return NextResponse.json({ success: true, data: data || [] });
        } catch (error) {
          console.error('Get store links error:', error);
          return NextResponse.json(
            { success: false, error: 'Failed to fetch store links' },
            { status: 500 }
          );
        }

      default:
        console.log('Invalid action:', action);
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    console.log('Returning result:', result);
    
    // Ensure we always return a valid response structure
    if (!result || typeof result !== 'object') {
      console.error('Invalid result from action function:', result);
      return NextResponse.json(
        { success: false, error: 'Invalid response from server' },
        { status: 500 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in featured review API:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 