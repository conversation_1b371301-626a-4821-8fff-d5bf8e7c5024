/* ═══════════════════════════════════════════════════════════════════════════
   💰 MONETIZATION CONFIGURATOR - STREAMLINED STYLES
   ═══════════════════════════════════════════════════════════════════════════ */

   :root {
    /* Core monetization colors - simplified */
    --monetization-bg-primary: rgba(15, 23, 42, 0.6);
    --monetization-bg-secondary: rgba(30, 41, 59, 0.4);
    --monetization-border: rgba(100, 116, 139, 0.4);
    --monetization-border-light: rgba(100, 116, 139, 0.3);
    
    /* Status colors */
    --monetization-success: #10b981;
    --monetization-warning: #f59e0b;
    --monetization-error: #ef4444;
    --monetization-info: #06b6d4;
    
    /* Interactive states - subtle */
    --monetization-hover: rgba(100, 116, 139, 0.1);
    --monetization-active: rgba(139, 92, 246, 0.1);
}

/* ═══════════════════════════════════════════════════════════════════════════
   PLACEMENT CARDS
   ═══════════════════════════════════════════════════════════════════════════ */

/* Clean placement card styling */
.monetization-placement-card {
    transition: border-color 0.2s ease;
}

.monetization-placement-card:hover {
    border-color: var(--monetization-border);
}

/* Configuration form styling */
.monetization-config-form {
    background: var(--monetization-bg-secondary);
    border-top: 1px solid var(--monetization-border-light);
}

/* ═══════════════════════════════════════════════════════════════════════════
   BANNER DISPLAY
   ═══════════════════════════════════════════════════════════════════════════ */

/* Active banner container */
.monetization-banner-item {
    background: var(--monetization-bg-primary);
    border: 1px solid var(--monetization-border-light);
    transition: all 0.2s ease;
}

.monetization-banner-item:hover {
    border-color: var(--monetization-border);
    background: var(--monetization-hover);
}

/* Banner thumbnail styling */
.monetization-banner-thumbnail {
    background: var(--monetization-bg-secondary);
    border: 1px solid var(--monetization-border-light);
    overflow: hidden;
}

.monetization-banner-thumbnail img {
    transition: opacity 0.2s ease;
}

.monetization-banner-thumbnail:hover img {
    opacity: 0.9;
}

/* ═══════════════════════════════════════════════════════════════════════════
   STATUS INDICATORS
   ═══════════════════════════════════════════════════════════════════════════ */

.monetization-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    border: 1px solid;
}

.monetization-status-full {
    background: rgba(239, 68, 68, 0.1);
    color: var(--monetization-error);
    border-color: rgba(239, 68, 68, 0.2);
}

.monetization-status-available {
    background: rgba(16, 185, 129, 0.1);
    color: var(--monetization-success);
    border-color: rgba(16, 185, 129, 0.2);
}

.monetization-status-soon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--monetization-warning);
    border-color: rgba(245, 158, 11, 0.2);
}

/* ═══════════════════════════════════════════════════════════════════════════
   FORM ELEMENTS
   ═══════════════════════════════════════════════════════════════════════════ */

/* Preview container */
.monetization-preview {
    background: var(--monetization-bg-primary);
    border: 1px solid var(--monetization-border-light);
    border-radius: 0.375rem;
    overflow: hidden;
}

.monetization-preview img {
    display: block;
    width: 100%;
    height: auto;
}

.monetization-preview-error {
    padding: 2rem;
    text-align: center;
    color: #94a3b8;
}

/* Input field customizations */
.monetization-input {
    background: var(--monetization-bg-primary);
    border: 1px solid var(--monetization-border-light);
    color: #e2e8f0;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    font-size: 0.875rem;
}

.monetization-input:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.2);
    outline: none;
}

.monetization-input::placeholder {
    color: #64748b;
}

/* ═══════════════════════════════════════════════════════════════════════════
   BUTTONS
   ═══════════════════════════════════════════════════════════════════════════ */

/* Primary action button */
.monetization-btn-primary {
    background: #8b5cf6;
    color: white;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    transition: background-color 0.2s ease;
}

.monetization-btn-primary:hover {
    background: #7c3aed;
}

.monetization-btn-primary:disabled {
    background: #4c1d95;
    opacity: 0.5;
    cursor: not-allowed;
}

/* Secondary action button */
.monetization-btn-secondary {
    background: var(--monetization-bg-primary);
    border: 1px solid var(--monetization-border);
    color: #cbd5e1;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    transition: all 0.2s ease;
}

.monetization-btn-secondary:hover {
    background: var(--monetization-hover);
    border-color: var(--monetization-border);
    color: #e2e8f0;
}

/* Danger/remove button */
.monetization-btn-danger {
    color: #94a3b8;
    transition: all 0.2s ease;
}

.monetization-btn-danger:hover {
    color: var(--monetization-error);
    background: rgba(239, 68, 68, 0.1);
}

/* ═══════════════════════════════════════════════════════════════════════════
   INFO PANELS
   ═══════════════════════════════════════════════════════════════════════════ */

.monetization-info-panel {
    background: var(--monetization-bg-primary);
    border: 1px solid var(--monetization-border-light);
    border-radius: 0.5rem;
    padding: 1rem;
}

.monetization-info-icon {
    color: var(--monetization-info);
    flex-shrink: 0;
}

.monetization-info-text {
    color: #cbd5e1;
    font-size: 0.875rem;
    line-height: 1.5;
}

.monetization-info-highlight {
    color: var(--monetization-info);
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

/* ═══════════════════════════════════════════════════════════════════════════
   RESPONSIVE DESIGN
   ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 768px) {
    .monetization-placement-card {
        padding: 1rem;
    }
    
    .monetization-banner-item {
        padding: 0.75rem;
    }
    
    .monetization-config-form {
        padding: 1rem;
    }
}

@media (max-width: 640px) {
    .monetization-status {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }
    
    .monetization-banner-thumbnail {
        width: 3rem;
        height: 2rem;
    }
}